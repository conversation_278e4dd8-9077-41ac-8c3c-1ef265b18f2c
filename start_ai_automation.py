#!/usr/bin/env python3
"""
AI Automation System Startup Script

Properly starts the AI automation system with Discord integration in an async context.
"""

import asyncio
import sys
import os
import signal
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Discord webhook URL
DISCORD_WEBHOOK_URL = "https://discord.com/api/webhooks/1409753017137369088/2wUoyZFHFMs5DbAiBfqd6IvzRauFWc4cjC5AJLPD61D71qh6IXrIaVULTu_HvejxFyyZ"

class AIAutomationSystem:
    """Main AI automation system controller."""
    
    def __init__(self):
        self.scheduler = None
        self.running = False
        self.shutdown_event = asyncio.Event()
        
    async def initialize(self):
        """Initialize the AI automation system."""
        try:
            print("🚀 Initializing AI Automation System...")
            
            from src.core.automation.report_scheduler import initialize_ai_report_scheduler
            
            # Initialize scheduler with Discord integration
            self.scheduler = initialize_ai_report_scheduler(DISCORD_WEBHOOK_URL)
            
            if self.scheduler:
                print("✅ AI Report Scheduler initialized with Discord integration")
                print(f"📡 Discord webhook: {DISCORD_WEBHOOK_URL[:50]}...")
                
                # Check scheduled jobs
                jobs = self.scheduler.get_all_jobs_status()
                print(f"📅 Found {len(jobs)} scheduled jobs:")
                
                for job in jobs:
                    print(f"   - {job['job_id']}: {job['report_type']} ({job['schedule']})")
                    print(f"     Status: {'🟢 Enabled' if job['enabled'] else '🔴 Disabled'}")
                
                return True
            else:
                print("❌ Failed to initialize AI Report Scheduler")
                return False
                
        except Exception as e:
            print(f"❌ Error initializing AI automation system: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def start(self):
        """Start the AI automation system."""
        try:
            if not self.scheduler:
                print("❌ Scheduler not initialized")
                return False
            
            print("\n🚀 Starting AI Automation System...")
            
            # Start the scheduler
            self.scheduler.start()
            self.running = True
            
            print("✅ AI Automation System is now LIVE!")
            print("🤖 Captain Hook is now your AI-powered market analyst")
            print("📊 Reports will be automatically delivered to Discord")
            print("⏰ Scheduled jobs are now running")
            
            # Send startup notification to Discord
            await self._send_startup_notification()
            
            return True
            
        except Exception as e:
            print(f"❌ Error starting AI automation system: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def _send_startup_notification(self):
        """Send startup notification to Discord."""
        try:
            from src.core.automation.discord_handler import DiscordWebhookHandler
            
            startup_message = {
                "content": "🚀 **AI Automation System Online!**",
                "embeds": [{
                    "title": "🤖 Captain Hook - AI Market Analyst",
                    "description": "The AI automation system is now live and ready to deliver market insights!",
                    "color": 0x00ff00,
                    "fields": [
                        {
                            "name": "📊 Daily Market Summary",
                            "value": "9:00 PM UTC (4:00 PM ET) Mon-Fri",
                            "inline": True
                        },
                        {
                            "name": "🏥 Market Health Dashboard", 
                            "value": "2:00 PM UTC (9:00 AM ET) Mon-Fri",
                            "inline": True
                        },
                        {
                            "name": "🌅 Pre-Market Analysis",
                            "value": "12:00 PM UTC (7:00 AM ET) Mon-Fri",
                            "inline": True
                        },
                        {
                            "name": "🏖️ Weekend Summary",
                            "value": "6:00 PM UTC (1:00 PM ET) Saturday",
                            "inline": True
                        }
                    ],
                    "footer": {
                        "text": "🤖 AI-Powered Market Analysis"
                    },
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }],
                "username": "Captain Hook - AI Market Analyst"
            }
            
            async with DiscordWebhookHandler(DISCORD_WEBHOOK_URL) as handler:
                success = await handler.send_report(type('FormattedReport', (), {
                    'content': startup_message['embeds'][0]['description'],
                    'metadata': {'report_type': 'startup_notification'},
                    'timestamp': datetime.now(timezone.utc)
                })())
                
                if success:
                    print("✅ Startup notification sent to Discord")
                else:
                    print("⚠️ Failed to send startup notification to Discord")
                    
        except Exception as e:
            print(f"⚠️ Could not send startup notification: {e}")
    
    async def run(self):
        """Main run loop for the AI automation system."""
        try:
            # Initialize system
            if not await self.initialize():
                return False
            
            # Start system
            if not await self.start():
                return False
            
            print("\n" + "="*60)
            print("🎯 AI Automation System Status: RUNNING")
            print("="*60)
            print("📊 Reports will be automatically generated and sent to Discord")
            print("⏰ Press Ctrl+C to stop the system")
            print("="*60)
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            return True
            
        except Exception as e:
            print(f"❌ Error in AI automation system: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def shutdown(self):
        """Shutdown the AI automation system."""
        try:
            print("\n🛑 Shutting down AI Automation System...")
            
            if self.scheduler and self.running:
                self.scheduler.stop()
                self.running = False
                print("✅ Scheduler stopped")
            
            # Send shutdown notification to Discord
            await self._send_shutdown_notification()
            
            print("✅ AI Automation System shutdown complete")
            
        except Exception as e:
            print(f"⚠️ Error during shutdown: {e}")
    
    async def _send_shutdown_notification(self):
        """Send shutdown notification to Discord."""
        try:
            from src.core.automation.discord_handler import DiscordWebhookHandler
            
            shutdown_message = {
                "content": "🛑 **AI Automation System Offline**",
                "embeds": [{
                    "title": "🤖 Captain Hook - System Status",
                    "description": "The AI automation system has been stopped.",
                    "color": 0xff0000,
                    "footer": {
                        "text": "🤖 AI-Powered Market Analysis"
                    },
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }],
                "username": "Captain Hook - AI Market Analyst"
            }
            
            async with DiscordWebhookHandler(DISCORD_WEBHOOK_URL) as handler:
                await handler.send_report(type('FormattedReport', (), {
                    'content': shutdown_message['embeds'][0]['description'],
                    'metadata': {'report_type': 'shutdown_notification'},
                    'timestamp': datetime.now(timezone.utc)
                })())
                
        except Exception as e:
            print(f"⚠️ Could not send shutdown notification: {e}")


async def main():
    """Main entry point."""
    # Create system instance
    system = AIAutomationSystem()
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        print(f"\n📡 Received signal {signum}, initiating shutdown...")
        asyncio.create_task(system.shutdown())
        system.shutdown_event.set()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Run the system
        success = await system.run()
        
        if success:
            print("🎉 AI Automation System completed successfully")
        else:
            print("❌ AI Automation System failed")
            return 1
            
    except KeyboardInterrupt:
        print("\n📡 Keyboard interrupt received")
        await system.shutdown()
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        await system.shutdown()
        return 1
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n📡 Startup interrupted")
        sys.exit(1) 