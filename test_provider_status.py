#!/usr/bin/env python3
"""
Quick test to check the status of all data providers.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_all_providers():
    """Test all data providers to see what's working."""
    try:
        print("🔌 Testing All Data Providers")
        print("=" * 50)
        
        # Import all providers - Using consolidated implementations
        from src.api.data.providers.polygon import PolygonProvider
        from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
        from src.api.data.providers.finnhub import FinnhubProvider
        from src.shared.data_providers.yfinance_provider import YFinanceProvider
        
        providers = [
            ("Polygon", PolygonProvider()),
            ("Alpha Vantage", AlphaVantageProvider()),
            ("Finnhub", FinnhubProvider()),
            ("YFinance", YFinanceProvider())
        ]
        
        for name, provider in providers:
            print(f"\n📊 Testing {name} Provider:")
            print("-" * 30)
            
            # Check configuration
            is_configured = provider.is_configured()
            print(f"✅ Configured: {is_configured}")
            
            if is_configured:
                # Test basic ticker fetch
                try:
                    ticker_data = await provider.get_ticker("AAPL")
                    if ticker_data and not ticker_data.get('error'):
                        print(f"✅ AAPL data: ${ticker_data['current_price']:.2f}")
                    else:
                        print(f"⚠️ Error: {ticker_data.get('error', 'Unknown error')}")
                except Exception as e:
                    print(f"❌ Exception: {e}")
            else:
                print("❌ Not configured")
        
        print("\n" + "=" * 50)
        print("🎯 Now testing the report engine...")
        
        # Test the report engine
        from src.core.automation.report_engine import AIReportEngine
        
        engine = AIReportEngine()
        print("✅ Report engine created")
        
        # Test market data collection
        print("📊 Collecting market data...")
        market_data = await engine._collect_market_data()
        
        if market_data:
            print(f"✅ Successfully collected data for {len(market_data)} symbols")
            for stock in market_data[:3]:  # Show first 3
                print(f"  {stock.symbol}: ${stock.current_price:.2f} ({stock.price_change_pct:+.2f}%)")
        else:
            print("❌ No market data collected")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    await test_all_providers()
    print("\n✅ Provider status test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 