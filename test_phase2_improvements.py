#!/usr/bin/env python3
"""
Test script to verify Phase 2 improvements:
- Enhanced technical indicator calculations
- Supply/demand zone detection
- Improved data quality scoring
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_phase2_improvements():
    """Test the Phase 2 improvements to the ask pipeline."""
    try:
        print("🔍 Testing Phase 2 improvements...")
        
        # Test importing the enhanced components
        from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor
        print("✅ Enhanced AI Chat Processor imported successfully")
        
        # Test the enhanced technical indicators calculation
        print("\n🧪 Testing enhanced technical indicators calculation...")
        
        # Create a test config
        test_config = {
            'technical': {
                'rsi_period': 14,
                'sma_short': 20,
                'sma_long': 50,
                'ema_short': 12,
                'ema_long': 26,
                'decimal_places': 2
            }
        }
        
        # Create processor instance
        processor = AIChatProcessor(test_config)
        print("✅ Processor instance created successfully")
        
        # Test technical indicators quality calculation
        test_indicators = {
            'rsi': 65.5,
            'macd': 0.25,
            'macd_signal': 0.20,
            'sma_20': 150.25,
            'sma_50': 148.75,
            'ema_12': 151.00,
            'ema_26': 149.50,
            'bollinger_upper': 155.00,
            'bollinger_middle': 150.25,
            'bollinger_lower': 145.50,
            'volume_sma': 1000000,
            'volume_ratio': 1.2
        }
        
        quality_score = processor._calculate_technical_indicators_quality(test_indicators)
        print(f"✅ Technical indicators quality score: {quality_score}/100")
        
        # Test data quality calculation
        test_data = {
            'current_price': 150.25,
            'change_percent': 1.5,
            'volume': 1200000,
            'high': 151.00,
            'low': 149.50,
            'open': 149.75,
            'close': 150.25,
            'timestamp': '2024-01-15T10:30:00Z',
            'status': 'success',
            'technical_indicators': test_indicators
        }
        
        data_quality = processor._calculate_data_quality(test_data)
        print(f"✅ Data quality score: {data_quality}/100")
        
        # Test zone detection methods exist
        print("\n🧪 Testing zone detection methods...")
        if hasattr(processor, '_detect_supply_demand_zones'):
            print("✅ Zone detection method exists")
        else:
            print("❌ Zone detection method missing")
            
        if hasattr(processor, '_generate_zone_recommendations'):
            print("✅ Zone recommendations method exists")
        else:
            print("❌ Zone recommendations method missing")
            
        if hasattr(processor, '_assess_market_structure'):
            print("✅ Market structure assessment method exists")
        else:
            print("❌ Market structure assessment method missing")
        
        print("\n🎉 Phase 2 improvements test completed successfully!")
        print(f"📊 Technical indicators quality: {quality_score}/100")
        print(f"📊 Overall data quality: {data_quality}/100")
        
        return True
        
    except Exception as e:
        print(f"❌ Phase 2 improvements test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_phase2_improvements())
    sys.exit(0 if success else 1) 