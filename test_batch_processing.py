#!/usr/bin/env python3
"""
Test script for batch market data processing.
This tests the new batch processing capabilities to avoid rate limits.
"""

import asyncio
import logging
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_batch_processing():
    """Test the batch processing functionality."""
    try:
        print("🚀 Testing Batch Market Data Processing")
        print("=" * 50)
        
        # Import the report engine
        from src.core.automation.report_engine import AIReportEngine
        
        # Create report engine
        engine = AIReportEngine()
        print("✅ Report engine created successfully")
        
        # Test batch data collection
        print("\n📊 Testing Batch Market Data Collection...")
        market_data = await engine._collect_market_data()
        
        if market_data:
            print(f"✅ Successfully collected data for {len(market_data)} symbols")
            
            # Show some sample data
            print("\n📈 Sample Market Data:")
            for i, stock in enumerate(market_data[:5]):  # Show first 5
                print(f"  {stock.symbol}: ${stock.current_price:.2f} ({stock.price_change_pct:+.2f}%) - {stock.sector}")
            
            # Test sector analysis
            print("\n🏭 Testing Sector Performance Analysis...")
            sector_performance = await engine._analyze_sector_performance()
            
            if sector_performance:
                print(f"✅ Analyzed {len(sector_performance)} sectors")
                for sector in sector_performance[:3]:  # Show top 3 sectors
                    print(f"  {sector.sector}: {sector.performance_pct:+.2f}% (Top: {sector.top_performer} +{sector.top_performer_pct:.2f}%)")
            
            # Test market health assessment
            print("\n🏥 Testing Market Health Assessment...")
            market_health = await engine._assess_market_health()
            
            if market_health:
                print(f"✅ Market Health: {market_health.avg_data_quality:.1f}/100")
                print(f"  Sentiment: {market_health.market_sentiment}")
                print(f"  Risk Level: {market_health.risk_level}")
                print(f"  Data Coverage: {market_health.total_symbols - market_health.stale_symbols_count}/{market_health.total_symbols}")
            
        else:
            print("❌ No market data collected")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def test_provider_batch_methods():
    """Test individual provider batch methods."""
    try:
        print("\n🔌 Testing Provider Batch Methods")
        print("=" * 40)
        
        # Test YFinance provider
        from src.shared.data_providers.yfinance_provider import YFinanceProvider
        yf_provider = YFinanceProvider()
        
        symbols = ["AAPL", "MSFT", "GOOGL"]
        print(f"Testing YFinance batch fetch for {symbols}...")
        
        batch_data = await yf_provider.get_multiple_tickers(symbols)
        if batch_data:
            print(f"✅ YFinance batch: {len(batch_data)} results")
            for item in batch_data:
                if not item.get('error'):
                    print(f"  {item['symbol']}: ${item['current_price']:.2f}")
                else:
                    print(f"  {item['symbol']}: Error - {item['error']}")
        
        # Test Alpha Vantage provider
        from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
        av_provider = AlphaVantageProvider()
        
        if av_provider.is_configured():
            print(f"\nTesting Alpha Vantage batch fetch for {symbols}...")
            batch_data = await av_provider.get_multiple_tickers(symbols)
            if batch_data:
                print(f"✅ Alpha Vantage batch: {len(batch_data)} results")
                for item in batch_data:
                    if not item.get('error'):
                        print(f"  {item['symbol']}: ${item['current_price']:.2f}")
                    else:
                        print(f"  {item['symbol']}: Error - {item['error']}")
        else:
            print("⚠️ Alpha Vantage not configured (missing API key)")
            
    except Exception as e:
        print(f"❌ Error testing provider batch methods: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    print("🧪 Batch Processing Test Suite")
    print("=" * 50)
    
    # Test provider batch methods first
    await test_provider_batch_methods()
    
    # Test full batch processing
    await test_batch_processing()
    
    print("\n✅ Batch processing tests completed!")

if __name__ == "__main__":
    asyncio.run(main()) 