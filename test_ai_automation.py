#!/usr/bin/env python3
"""
Test AI Automation System

Simple test to verify the AI-powered report generation and automation system.
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_ai_report_generation():
    """Test AI report generation functionality."""
    print("🧪 Testing AI Report Generation...")
    
    try:
        from src.core.automation.report_engine import AIReportEngine, ReportType
        
        # Create report engine
        engine = AIReportEngine()
        print("✅ Report engine created successfully")
        
        # Generate daily market report
        print("📊 Generating daily market report...")
        daily_report = await engine.generate_daily_market_report()
        
        if daily_report:
            print(f"✅ Daily market report generated successfully")
            print(f"   - Title: {daily_report.title}")
            print(f"   - Type: {daily_report.report_type.value}")
            print(f"   - AI Insights: {len(daily_report.ai_insights)}")
            print(f"   - Market Data: {len(daily_report.market_data)} symbols")
            print(f"   - Sector Performance: {len(daily_report.sector_performance)} sectors")
            print(f"   - Data Quality: {daily_report.market_health.avg_data_quality:.1f}/100")
        else:
            print("❌ Failed to generate daily market report")
            return False
        
        # Generate market health report
        print("\n🏥 Generating market health report...")
        health_report = await engine.generate_market_health_report()
        
        if health_report:
            print(f"✅ Market health report generated successfully")
            print(f"   - Title: {health_report.title}")
            print(f"   - Type: {health_report.report_type.value}")
            print(f"   - AI Insights: {len(health_report.ai_insights)}")
            print(f"   - Data Quality: {health_report.market_health.avg_data_quality:.1f}/100")
            print(f"   - Risk Level: {health_report.market_health.risk_level}")
        else:
            print("❌ Failed to generate market health report")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ AI report generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_report_formatting():
    """Test report formatting functionality."""
    print("\n🧪 Testing Report Formatting...")
    
    try:
        from src.core.automation.report_engine import AIReportEngine, ReportType
        from src.core.automation.report_formatter import ReportFormatter, OutputFormat
        
        # Create report engine and formatter
        engine = AIReportEngine()
        formatter = ReportFormatter()
        print("✅ Report engine and formatter created successfully")
        
        # Generate a report
        print("📊 Generating report for formatting test...")
        report = await engine.generate_daily_market_report()
        
        if not report:
            print("❌ Failed to generate report for formatting test")
            return False
        
        # Test Discord formatting
        print("📱 Testing Discord formatting...")
        discord_report = formatter.format_for_discord(report)
        if discord_report and discord_report.content:
            print(f"✅ Discord formatting successful ({len(discord_report.content)} characters)")
            print(f"   - Format: {discord_report.format.value}")
            print(f"   - Metadata: {discord_report.metadata}")
        else:
            print("❌ Discord formatting failed")
            return False
        
        # Test Markdown formatting
        print("📝 Testing Markdown formatting...")
        markdown_report = formatter.format_for_markdown(report)
        if markdown_report and markdown_report.content:
            print(f"✅ Markdown formatting successful ({len(markdown_report.content)} characters)")
            print(f"   - Format: {markdown_report.format.value}")
            print(f"   - Metadata: {markdown_report.metadata}")
        else:
            print("❌ Markdown formatting failed")
            return False
        
        # Test JSON formatting
        print("🔧 Testing JSON formatting...")
        json_report = formatter.format_for_json(report)
        if json_report and json_report.content:
            print(f"✅ JSON formatting successful ({len(json_report.content)} characters)")
            print(f"   - Format: {json_report.format.value}")
            print(f"   - Metadata: {json_report.metadata}")
        else:
            print("❌ JSON formatting failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Report formatting test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_automation_scheduler():
    """Test automation scheduler functionality."""
    print("\n🧪 Testing Automation Scheduler...")
    
    try:
        from src.core.automation.report_scheduler import AIReportScheduler
        
        # Create scheduler
        scheduler = AIReportScheduler()
        print("✅ AI Report Scheduler created successfully")
        
        # Check default jobs
        print("📅 Checking default scheduled jobs...")
        all_jobs = scheduler.get_all_jobs_status()
        
        if all_jobs:
            print(f"✅ Found {len(all_jobs)} default jobs:")
            for job in all_jobs:
                print(f"   - {job['job_id']}: {job['report_type']} ({job['schedule']})")
                print(f"     Enabled: {job['enabled']}, Status: {job['scheduler_status']}")
        else:
            print("❌ No default jobs found")
            return False
        
        # Test job status
        print("\n📊 Testing job status retrieval...")
        daily_job_status = scheduler.get_job_status("daily_market_summary")
        if daily_job_status:
            print(f"✅ Daily market summary job status retrieved:")
            print(f"   - Job ID: {daily_job_status['job_id']}")
            print(f"   - Report Type: {daily_job_status['report_type']}")
            print(f"   - Schedule: {daily_job_status['schedule']}")
            print(f"   - Enabled: {daily_job_status['enabled']}")
            print(f"   - Run Count: {daily_job_status['run_count']}")
        else:
            print("❌ Failed to get job status")
            return False
        
        # Test immediate job execution
        print("\n🚀 Testing immediate job execution...")
        success = await scheduler.run_job_now("daily_market_summary")
        if success:
            print("✅ Immediate job execution successful")
        else:
            print("❌ Immediate job execution failed")
            return False
        
        # Test job testing
        print("\n🧪 Testing job testing functionality...")
        test_success = await scheduler.test_job("daily_market_summary")
        if test_success:
            print("✅ Job testing successful")
        else:
            print("❌ Job testing failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Automation scheduler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all automation tests."""
    print("🚀 AI Automation System Test Suite")
    print("=" * 50)
    
    tests = [
        ("AI Report Generation", test_ai_report_generation),
        ("Report Formatting", test_report_formatting),
        ("Automation Scheduler", test_automation_scheduler)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} test passed!")
            else:
                print(f"❌ {test_name} test failed!")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All automation tests passed! The AI automation system is working correctly.")
        print("\n🚀 Ready to deploy automated AI reports!")
        print("   - Daily market summaries")
        print("   - Market health dashboards")
        print("   - Pre-market analysis")
        print("   - Weekend summaries")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)