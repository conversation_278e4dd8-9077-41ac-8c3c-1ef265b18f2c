#!/usr/bin/env python3
"""
Simple Redis connection test script for Docker container
"""

import redis
import os
import sys

def test_redis_connection():
    """Test Redis connection using direct parameters"""
    try:
        # Get Redis password from environment
        redis_password = os.environ.get("REDIS_PASSWORD", "123SECURE_REDIS_PASSWORD!@#")
        
        # Connect to Redis
        print(f"Connecting to Red<PERSON> at redis:6379 with password")
        r = redis.Redis(
            host='redis',
            port=6379,
            password=redis_password,
            decode_responses=True
        )
        
        # Test connection
        print("Testing connection with PING...")
        response = r.ping()
        print(f"PING response: {response}")
        
        # Test basic operations
        test_key = "test:redis:connection"
        test_value = "Connection test successful"
        
        print(f"Setting test key: {test_key}")
        r.set(test_key, test_value)
        
        print("Getting test key...")
        result = r.get(test_key)
        print(f"Retrieved value: {result}")
        
        print("Deleting test key...")
        r.delete(test_key)
        
        print("Redis connection test successful!")
        return True
        
    except Exception as e:
        print(f"Redis connection test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_redis_connection()
    sys.exit(0 if success else 1)
