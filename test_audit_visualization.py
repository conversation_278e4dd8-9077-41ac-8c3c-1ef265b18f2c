"""
Test Audit Visualization System

This script tests the automatic request visualization system by simulating
Discord interactions and verifying that they are properly visualized and
sent to the developer channel.
"""

import asyncio
import logging
import time
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional

# Create mock discord module
class MockDiscord:
    class Embed:
        def __init__(self, title=None, description=None, color=None, timestamp=None):
            self.title = title
            self.description = description
            self.color = color
            self.timestamp = timestamp
            self.fields = []
            self.footer = None
        
        def add_field(self, name, value, inline=False):
            self.fields.append({"name": name, "value": value, "inline": inline})
            return self
                
        def set_footer(self, text=None, icon_url=None):
            self.footer = {"text": text, "icon_url": icon_url}
            return self
    
    class Color:
        @staticmethod
        def blue(): return "blue"
        @staticmethod
        def green(): return "green"
        @staticmethod
        def red(): return "red"
        @staticmethod
        def light_grey(): return "light_grey"
        @staticmethod
        def gold(): return "gold"
        @staticmethod
        def dark_red(): return "dark_red"
            
    class InteractionType:
        application_command = 2
        
    class utils:
        @staticmethod
        def utcnow():
            return datetime.utcnow()

# Add mock discord module to sys.modules
sys.modules["discord"] = MockDiscord
import discord  # This will now import our mock module

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("audit-test")

# Discord module is already mocked above

# Import after discord module is available
from src.bot.audit.request_visualizer import request_visualizer, AuditLevel
from src.core.logger import get_logger, generate_correlation_id

class MockUser:
    """Mock Discord user for testing"""
    def __init__(self, id="123456789", name="Test User", display_name="TestUser"):
        self.id = id
        self.name = name
        self.display_name = display_name
        self.display_avatar = MockAvatar()
        self.guild_permissions = MockPermissions()
        self.bot = False

class MockAvatar:
    """Mock Discord avatar for testing"""
    def __init__(self, url="https://example.com/avatar.png"):
        self.url = url

class MockPermissions:
    """Mock Discord permissions for testing"""
    def __init__(self, administrator=False):
        self.administrator = administrator

class MockChannel:
    """Mock Discord channel for testing"""
    def __init__(self, id="987654321", name="test-channel"):
        self.id = id
        self.name = name
        
    async def send(self, content=None, embed=None, ephemeral=False):
        """Mock send method"""
        if content:
            logger.info(f"[CHANNEL] Message sent: {content}")
        if embed:
            logger.info(f"[CHANNEL] Embed sent: {embed.title}")
        return MockMessage()

class MockGuild:
    """Mock Discord guild for testing"""
    def __init__(self, id="111222333", name="Test Guild", member_count=100):
        self.id = id
        self.name = name
        self.member_count = member_count

class MockMessage:
    """Mock Discord message for testing"""
    def __init__(self, content="Test message", author=None):
        self.content = content
        self.author = author or MockUser()
        
    async def edit(self, content=None, embed=None):
        """Mock edit method"""
        if content:
            logger.info(f"[MESSAGE] Message edited: {content}")
        if embed:
            logger.info(f"[MESSAGE] Embed edited: {embed.title}")
        return self

class MockInteraction:
    """Mock Discord interaction for testing"""
    
    class MockResponse:
        """Mock interaction response for testing"""
        def __init__(self):
            self.is_done_flag = False
            
        def is_done(self):
            """Check if response is done"""
            return self.is_done_flag
            
        async def send_message(self, content=None, embed=None, ephemeral=False):
            """Mock send_message method"""
            if content:
                logger.info(f"[RESPONSE] Message sent: {content}")
            if embed:
                logger.info(f"[RESPONSE] Embed sent: {embed.title}")
            self.is_done_flag = True
            
        async def defer(self, thinking=False):
            """Mock defer method"""
            logger.info(f"[RESPONSE] Deferred (thinking: {thinking})")
            self.is_done_flag = True
    
    class MockFollowup:
        """Mock interaction followup for testing"""
        async def send(self, content=None, embed=None, ephemeral=False):
            """Mock send method"""
            if content:
                logger.info(f"[FOLLOWUP] Message sent: {content}")
            if embed:
                logger.info(f"[FOLLOWUP] Embed sent: {embed.title}")
            return MockMessage()
    
    def __init__(self, command_name="test", options=None):
        self.user = MockUser()
        self.response = self.MockResponse()
        self.followup = self.MockFollowup()
        self.channel_id = "987654321"
        self.guild_id = "111222333"
        self.guild = MockGuild()
        self.channel = MockChannel()
        self.type = discord.InteractionType.application_command
        self.data = {
            "name": command_name,
            "options": options or []
        }
        
    async def original_response(self):
        """Mock original_response method"""
        return MockMessage()

class MockBot:
    """Mock Discord bot for testing"""
    def __init__(self):
        self.user = MockUser(id="999888777", name="Bot User", display_name="TestBot")
        self.latency = 0.05  # 50ms latency
        self.guilds = [MockGuild()]
        
    def get_channel(self, channel_id):
        """Mock get_channel method"""
        return MockChannel(id=channel_id)

async def setup_visualizer():
    """Set up the request visualizer for testing"""
    # Create mock bot
    bot = MockBot()
    
    # Configure request visualizer
    request_visualizer.bot = bot
    request_visualizer.configure(
        dev_channel_id=123456789,  # Mock developer channel ID
        enabled=True,
        log_to_console=True,
        log_to_channel=True
    )
    
    # Register commands for visualization
    request_visualizer.register_commands(["analyze", "ask", "price", "watchlist"])
    
    return request_visualizer

async def test_request_visualization():
    """Test request visualization"""
    logger.info("=== Testing Request Visualization ===")
    
    # Set up visualizer
    visualizer = await setup_visualizer()
    
    # Create mock interaction for /analyze command
    interaction = MockInteraction(
        command_name="analyze",
        options=[
            {"name": "symbol", "value": "AAPL"},
            {"name": "indicators", "value": "RSI,MACD"},
            {"name": "target", "value": "$180"},
            {"name": "debug", "value": True}
        ]
    )
    
    # Generate correlation ID
    correlation_id = generate_correlation_id()
    
    # Visualize request
    logger.info("Visualizing /analyze request...")
    await visualizer.visualize_request(
        interaction=interaction,
        command_name="analyze",
        args={
            "symbol": "AAPL",
            "indicators": "RSI,MACD",
            "target": "$180",
            "debug": True
        },
        correlation_id=correlation_id
    )
    
    # Simulate processing time
    await asyncio.sleep(1)
    
    # Visualize successful response
    logger.info("Visualizing successful response...")
    await visualizer.visualize_response(
        interaction=interaction,
        command_name="analyze",
        response_data="AAPL analysis shows bullish trend with RSI at 65.4 and positive MACD.",
        execution_time=3.5,
        correlation_id=correlation_id,
        success=True
    )
    
    # Simulate pipeline visualization
    logger.info("Visualizing pipeline execution...")
    pipeline_data = {
        "status": "completed",
        "execution_time": 3.5,
        "stages": [
            {
                "name": "initialization",
                "status": "completed",
                "duration": 0.2
            },
            {
                "name": "symbol_validation",
                "status": "completed",
                "duration": 0.5
            },
            {
                "name": "market_data_retrieval",
                "status": "completed",
                "duration": 1.0
            },
            {
                "name": "technical_analysis",
                "status": "completed",
                "duration": 1.5
            },
            {
                "name": "response_formatting",
                "status": "completed",
                "duration": 0.3
            }
        ],
        "errors": []
    }
    
    await visualizer.visualize_pipeline(
        interaction=interaction,
        command_name="analyze",
        pipeline_data=pipeline_data,
        correlation_id=correlation_id
    )
    
    logger.info("=== Request Visualization Test Completed ===")

async def test_error_visualization():
    """Test error visualization"""
    logger.info("=== Testing Error Visualization ===")
    
    # Set up visualizer
    visualizer = await setup_visualizer()
    
    # Create mock interaction for /ask command
    interaction = MockInteraction(
        command_name="ask",
        options=[
            {"name": "query", "value": "What is the price of INVALID_SYMBOL?"}
        ]
    )
    
    # Generate correlation ID
    correlation_id = generate_correlation_id()
    
    # Visualize request
    logger.info("Visualizing /ask request...")
    await visualizer.visualize_request(
        interaction=interaction,
        command_name="ask",
        args={"query": "What is the price of INVALID_SYMBOL?"},
        correlation_id=correlation_id
    )
    
    # Simulate processing time
    await asyncio.sleep(1)
    
    # Visualize error response
    logger.info("Visualizing error response...")
    await visualizer.visualize_response(
        interaction=interaction,
        command_name="ask",
        response_data="Failed to retrieve data for INVALID_SYMBOL",
        execution_time=2.5,
        correlation_id=correlation_id,
        success=False,
        error=Exception("Symbol not found: INVALID_SYMBOL")
    )
    
    # Simulate pipeline visualization with error
    logger.info("Visualizing pipeline execution with error...")
    pipeline_data = {
        "status": "failed",
        "execution_time": 2.5,
        "stages": [
            {
                "name": "initialization",
                "status": "completed",
                "duration": 0.2
            },
            {
                "name": "symbol_validation",
                "status": "failed",
                "duration": 0.5
            },
            {
                "name": "market_data_retrieval",
                "status": "skipped",
                "duration": None
            },
            {
                "name": "technical_analysis",
                "status": "skipped",
                "duration": None
            },
            {
                "name": "response_formatting",
                "status": "skipped",
                "duration": None
            }
        ],
        "errors": [
            {
                "stage": "symbol_validation",
                "error_type": "SymbolNotFoundError",
                "error_message": "Symbol not found: INVALID_SYMBOL"
            }
        ]
    }
    
    await visualizer.visualize_pipeline(
        interaction=interaction,
        command_name="ask",
        pipeline_data=pipeline_data,
        correlation_id=correlation_id
    )
    
    logger.info("=== Error Visualization Test Completed ===")

async def test_audit_logging():
    """Test audit logging"""
    logger.info("=== Testing Audit Logging ===")
    
    # Set up visualizer
    visualizer = await setup_visualizer()
    
    # Log audit events at different levels
    for level in AuditLevel:
        logger.info(f"Logging audit event at {level.value} level...")
        await visualizer.log_audit_event(
            level=level,
            message=f"Test audit event at {level.value} level",
            command_name="test",
            correlation_id=generate_correlation_id(),
            data={
                "test": True,
                "level": level.value,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    logger.info("=== Audit Logging Test Completed ===")

async def main():
    """Run all tests"""
    logger.info("Starting Audit Visualization Tests")
    
    # Test request visualization
    await test_request_visualization()
    
    # Test error visualization
    await test_error_visualization()
    
    # Test audit logging
    await test_audit_logging()
    
    logger.info("All Audit Visualization Tests Completed")

if __name__ == "__main__":
    asyncio.run(main())
