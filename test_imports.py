#!/usr/bin/env python3
"""
Simple test script to validate all imports work correctly
Handles Discord dependency gracefully
"""

import sys
import os
import traceback

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_import(module_name, class_name=None, skip_if_discord_missing=False):
    """Test importing a module and optionally a class"""
    try:
        module = __import__(module_name, fromlist=[class_name] if class_name else [])
        if class_name:
            getattr(module, class_name)
        print(f"✓ {module_name}" + (f".{class_name}" if class_name else ""))
        return True
    except ImportError as e:
        # Handle Discord dependency gracefully
        if skip_if_discord_missing and "discord" in str(e).lower():
            print(f"⚠ {module_name}" + (f".{class_name}" if class_name else "") + f": Skipped (Discord dependency not available)")
            return True
        print(f"✗ {module_name}" + (f".{class_name}" if class_name else "") + f": {e}")
        return False
    except Exception as e:
        print(f"✗ {module_name}" + (f".{class_name}" if class_name else "") + f": {e}")
        traceback.print_exc()
        return False

def test_pipeline_optimizer():
    """Test pipeline optimizer functionality"""
    try:
        from src.bot.pipeline.performance_optimizer import initialize_pipeline_optimizer
        optimizer = initialize_pipeline_optimizer()
        print("✓ Pipeline optimizer initialized successfully")
        return True
    except Exception as e:
        print(f"✗ Pipeline optimizer failed: {e}")
        traceback.print_exc()
        return False

def main():
    print("Testing all bot component imports...")
    print("=" * 50)
    
    # Test basic imports
    tests = [
        ('src.bot.utils.rate_limiter', 'ThreadSafeRateLimiter', False),
        ('src.bot.utils.error_handler', 'ErrorHandler', True),
        ('src.bot.utils.error_handler', 'ErrorCategory', True),
        ('src.bot.utils.component_checker', 'ComponentChecker', True),
        ('src.bot.watchlist_manager', 'WatchlistManager', False),
        ('src.bot.pipeline.performance_optimizer', 'IntelligentCache', False),
        ('src.bot.enhancements.discord_ux', 'ProgressTracker', True),
        ('src.bot.enhancements.discord_ux', 'InteractiveEmbeds', True),
        ('src.bot.monitoring.health_monitor', 'HealthChecker', False),
        ('src.bot.monitoring.health_monitor', 'HealthStatus', False),
        ('src.bot.security.advanced_security', 'AdvancedSecurityManager', False),
        ('src.bot.security.advanced_security', 'InputValidator', False),
    ]
    
    passed = 0
    total = len(tests)
    
    for module_name, class_name, skip_discord in tests:
        if test_import(module_name, class_name, skip_discord):
            passed += 1
    
    # Test pipeline optimizer initialization
    print("\nTesting pipeline optimizer...")
    if test_pipeline_optimizer():
        passed += 1
    total += 1
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests successful")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed - check errors above")
        return 1

if __name__ == "__main__":
    sys.exit(main())
