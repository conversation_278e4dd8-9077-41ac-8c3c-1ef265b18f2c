#!/bin/bash
# Script to set environment variables for Docker Compose
# Usage: source docker-env.sh

# Core variables from .env.secure
export REDIS_PASSWORD="123SECURE_REDIS_PASSWORD!@#"
export REDIS_URL="redis://:123SECURE_REDIS_PASSWORD!@#@redis:6379/0"
export SUPABASE_DB_URL="postgresql+asyncpg://postgres:<EMAIL>:5432/postgres"
export WEBHOOK_SECRET="webhook_secret_123"
export DISCORD_WEBHOOK_URL="https://discord.com/api/webhooks/example"
export SLACK_WEBHOOK_URL="https://hooks.slack.com/services/example"
export NGROK_AUTHTOKEN="example_token"

# Other variables from .env.secure
export SUPABASE_URL="https://sgxjackuhalscowqrulv.supabase.co"
export SUPABASE_KEY="********************************************"
export DISCORD_BOT_TOKEN="MTQwNDUwNjk2MTc3NjQ4MDMxNg.GQRo7g.w1qaV6PpYEPq8CEwYph7aA_xN8W3SYNG5_aij0"
export OPENROUTER_API_KEY="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"
export POLYGON_API_KEY="********************************"
export FINNHUB_API_KEY="d2mjok1r01qog4441lsgd2mjok1r01qog4441lt0"
export ALPACA_API_KEY="your_alpaca_api_key_here"
export JWT_SECRET="vLzXgYq8bNfP6hJ2kM5sR9uV0wZ4yA1cE7fG3hK5jP3sR6uV0xY4zC1bE7fG2hK5jP3sR6uV0wZ4yA1c"

echo "Environment variables set for Docker Compose"
