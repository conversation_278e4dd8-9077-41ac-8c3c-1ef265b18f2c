import asyncio
import os
import sys
import logging

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/data_providers_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Import data source manager
from src.api.data.providers.data_source_manager import DataSourceManager

async def test_data_providers():
    """
    Comprehensive test of data providers
    
    Verifies:
    - Successful data retrieval
    - Data structure
    - Error handling
    """
    # Initialize data source manager
    manager = DataSourceManager()
    
    # Test symbols
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'GME']
    
    for symbol in symbols:
        print(f'\n🔍 Testing symbol: {symbol}')
        try:
            data = await manager.fetch_stock_data(symbol)
            print('✅ Data retrieved successfully:')
            for key, value in data.items():
                print(f'{key}: {value}')
            
            # Validate data structure
            assert 'symbol' in data, f"Missing symbol for {symbol}"
            assert 'current_price' in data, f"Missing current price for {symbol}"
            assert data['current_price'] > 0, f"Invalid price for {symbol}"
            assert 'timestamp' in data, f"Missing timestamp for {symbol}"
            assert 'source' in data, f"Missing source for {symbol}"
        
        except Exception as e:
            print(f'❌ Error fetching data for {symbol}: {e}')
            logger.error(f"Failed to fetch data for {symbol}: {e}", exc_info=True)
            raise

# Run the async function
if __name__ == '__main__':
    try:
        asyncio.run(test_data_providers())
        print("\n✅ All data provider tests passed successfully!")
    except Exception as e:
        print(f"\n❌ Data provider tests failed: {e}")
        sys.exit(1) 