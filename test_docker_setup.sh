#!/bin/bash
# Test script for Docker setup
# This script tests the Docker build and configuration

set -e  # Exit on any error

echo "🧪 Testing Docker Setup"
echo "======================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed"
    exit 1
fi

echo "✅ Docker and Docker Compose are installed"

# Check if .env.secure exists
if [ ! -f .env.secure ]; then
    echo "⚠️ Warning: .env.secure file not found"
    echo "Creating a sample .env.secure file for testing..."
    
    cat > .env.secure.sample << EOL
# Sample environment variables for testing
REDIS_PASSWORD=test_password
SUPABASE_URL=https://example.supabase.co
SUPABASE_KEY=sample_key
SUPABASE_DB_URL=postgresql://postgres:<EMAIL>:5432/postgres
DISCORD_BOT_TOKEN=sample_token
WEBHOOK_SECRET=sample_webhook_secret
EOL
    
    echo "Created .env.secure.sample file. Please rename to .env.secure and update with real values."
fi

# Test building the optimized Dockerfile
echo -e "\n🔨 Testing Dockerfile build..."
docker build -t tradingview-test -f Dockerfile.optimized . || {
    echo "❌ Dockerfile build failed"
    exit 1
}
echo "✅ Dockerfile build successful"

# Test Docker Compose configuration
echo -e "\n🔨 Testing Docker Compose configuration..."
docker-compose -f docker-compose.dev.optimized.yml config || {
    echo "❌ Docker Compose configuration test failed"
    exit 1
}
echo "✅ Docker Compose configuration is valid"

echo -e "\n✅ All Docker setup tests passed!"
echo "You can now run the system with:"
echo "docker-compose -f docker-compose.dev.optimized.yml up --build"
