#!/usr/bin/env python3
"""
Comprehensive test for symbol extraction fixes across the codebase.
Tests all symbol extraction functions to ensure they don't extract false positives.
"""

import sys
import os
import re
from typing import List, Optional

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_validators_module():
    """Test the fixed validators module."""
    print("Testing src/bot/pipeline/commands/ask/modules/utils/validators.py...")
    
    # Mock the exceptions for testing
    class InvalidSymbolFormatError(Exception):
        pass

    def validate_symbol(symbol: str) -> str:
        """Mock validate_symbol function"""
        if not symbol or not isinstance(symbol, str):
            raise InvalidSymbolFormatError(str(symbol))
        
        symbol = symbol.strip().upper()
        symbol = re.sub(r'\.[A-Z]+$', '', symbol)
        symbol = re.sub(r'^[A-Z]+:', '', symbol)
        
        if not re.match(r'^[A-Z0-9]{1,5}$', symbol):
            raise InvalidSymbolFormatError(symbol)
        
        invalid_patterns = [r'^TEST$', r'^DEMO$', r'^FAKE$', r'^\d+$']
        for pattern in invalid_patterns:
            if re.match(pattern, symbol, re.IGNORECASE):
                raise InvalidSymbolFormatError(symbol)
        
        return symbol

    def extract_symbols_from_text(text: str) -> List[str]:
        """Fixed version from validators.py"""
        if not text:
            return []
        
        patterns = [
            r'\$[A-Z]{1,5}\b',  # $AAPL format
            r'\b[A-Z]{1,5}\.[A-Z]{1,4}\b',  # AAPL.NASDAQ format
        ]
        
        symbols = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                symbol = match.lstrip('$')
                try:
                    validated = validate_symbol(symbol)
                    if validated not in symbols:
                        symbols.append(validated)
                except InvalidSymbolFormatError:
                    continue
        
        return symbols
    
    # Test cases
    test_cases = [
        ("What is the price of $AAPL?", ["AAPL"]),
        ("Tell me about AAPL and MSFT", []),  # No $ prefix
        ("I want $TSLA and $NVDA info", ["TSLA", "NVDA"]),
        ("What are the top TECH stocks?", []),  # No $ prefix
        ("Check $MSFT.NASDAQ performance", ["MSFT"]),
        ("The PRICE of STOCK is high", []),  # Common words
    ]
    
    all_passed = True
    for query, expected in test_cases:
        result = extract_symbols_from_text(query)
        if result == expected:
            print(f"  ✅ '{query}' -> {result}")
        else:
            print(f"  ❌ '{query}' -> {result} (expected {expected})")
            all_passed = False
    
    return all_passed

def test_batch_processor_module():
    """Test the batch processor module."""
    print("\nTesting src/bot/pipeline/commands/ask/batch_processor.py...")
    
    def extract_symbols_from_query(query: str) -> List[str]:
        """Function from batch_processor.py"""
        symbol_pattern = r'\$([A-Z]{1,5})\b'
        matches = re.findall(symbol_pattern, query)
        
        symbols = []
        excluded_words = {
            "AND", "OR", "FOR", "THE", "A", "AN", "IN", "ON", "AT", "BY",
            "IS", "ARE", "WAS", "WERE", "BE", "BEEN", "HAVE", "HAS", "HAD",
            "DO", "DOES", "DID", "WILL", "WOULD", "COULD", "SHOULD", "MAY",
            "MIGHT", "CAN", "WHAT", "WHERE", "WHEN", "WHY", "HOW", "WHO",
            "WHICH", "THAT", "THIS", "THESE", "THOSE", "OF", "TO", "FROM",
            "WITH", "WITHOUT", "ABOUT", "ABOVE", "BELOW", "UNDER", "OVER",
            "PRICE", "STOCK", "TRADE", "BUY", "SELL", "HOLD"
        }

        for symbol in matches:
            if symbol and symbol not in excluded_words:
                symbols.append(symbol)

        unique_symbols = []
        for symbol in symbols:
            if symbol not in unique_symbols:
                unique_symbols.append(symbol)
        
        return unique_symbols
    
    test_cases = [
        ("What is the price of $AAPL?", ["AAPL"]),
        ("Tell me about $TSLA and $NVDA", ["TSLA", "NVDA"]),
        ("I want to $BUY some stocks", []),  # Excluded word
        ("Check $MSFT performance", ["MSFT"]),
        ("What $THE price of $AAPL?", ["AAPL"]),  # THE excluded
    ]
    
    all_passed = True
    for query, expected in test_cases:
        result = extract_symbols_from_query(query)
        if result == expected:
            print(f"  ✅ '{query}' -> {result}")
        else:
            print(f"  ❌ '{query}' -> {result} (expected {expected})")
            all_passed = False
    
    return all_passed

def test_response_validator_module():
    """Test the response validator module."""
    print("\nTesting src/bot/pipeline/commands/ask/stages/response_validator.py...")
    
    def extract_symbols_from_response(response: str) -> List[str]:
        """Fixed version from response_validator.py"""
        symbol_patterns = [
            r'([A-Z]{1,5})\s*\([^)]+\)',  # NVDA (NVIDIA) - company name context
            r'\$([A-Z]{1,5})',            # $AAPL - explicit ticker format
            r'\*\*([A-Z]{1,5})\*\*',      # **TSLA** - emphasized ticker
        ]
        
        symbols = set()
        for pattern in symbol_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            symbols.update(matches)
        
        return list(symbols)
    
    test_cases = [
        ("NVDA (NVIDIA) is performing well", ["NVDA"]),
        ("Check $AAPL and $TSLA prices", ["AAPL", "TSLA"]),
        ("**MSFT** is a strong buy", ["MSFT"]),
        ("The TECH sector is bullish", []),  # No context patterns
        ("AAPL (Apple Inc.) and MSFT (Microsoft) are good", ["AAPL", "MSFT"]),
    ]
    
    all_passed = True
    for response, expected in test_cases:
        result = extract_symbols_from_response(response)
        # Sort both lists for comparison since order doesn't matter
        result_sorted = sorted(result)
        expected_sorted = sorted(expected)
        if result_sorted == expected_sorted:
            print(f"  ✅ '{response}' -> {result}")
        else:
            print(f"  ❌ '{response}' -> {result} (expected {expected})")
            all_passed = False
    
    return all_passed

def test_prompt_manager_module():
    """Test the prompt manager module."""
    print("\nTesting src/core/prompts/prompt_manager.py...")
    
    def extract_symbols(text: str) -> List[str]:
        """Function from prompt_manager.py"""
        if text is None:
            return []
        
        pattern = r'\$([A-Z]{1,10})\b'
        matches = re.findall(pattern, str(text).upper())
        
        valid_symbols = []
        for symbol in matches:
            if len(symbol) >= 1 and symbol.isalpha():
                if symbol not in valid_symbols:
                    valid_symbols.append(symbol)
        
        return valid_symbols[:10]  # Max 10 symbols
    
    test_cases = [
        ("What is $AAPL price?", ["AAPL"]),
        ("Check $TSLA and $NVDA", ["TSLA", "NVDA"]),
        ("I want $123 dollars", []),  # Not alphabetic
        ("$MICROSOFT is too long", ["MICROSOFT"]),  # Up to 10 chars allowed
        ("Tell me about AAPL", []),  # No $ prefix
    ]
    
    all_passed = True
    for text, expected in test_cases:
        result = extract_symbols(text)
        if result == expected:
            print(f"  ✅ '{text}' -> {result}")
        else:
            print(f"  ❌ '{text}' -> {result} (expected {expected})")
            all_passed = False
    
    return all_passed

def main():
    """Run all tests."""
    print("🧪 Comprehensive Symbol Extraction Test Suite")
    print("=" * 50)
    
    results = []
    
    # Test each module
    results.append(("Validators Module", test_validators_module()))
    results.append(("Batch Processor Module", test_batch_processor_module()))
    results.append(("Response Validator Module", test_response_validator_module()))
    results.append(("Prompt Manager Module", test_prompt_manager_module()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    all_passed = True
    for module_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  {module_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Symbol extraction fixes are working correctly.")
        return 0
    else:
        print("💥 SOME TESTS FAILED! Please review the failing cases above.")
        return 1

if __name__ == "__main__":
    exit(main())
