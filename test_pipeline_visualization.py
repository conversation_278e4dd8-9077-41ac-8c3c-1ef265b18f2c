"""
Pipeline Visualization Test Script

This script tests the pipeline visualization functionality for debugging
the /ask and /analyze command flows.

Usage:
    python test_pipeline_visualization.py

This will simulate pipeline execution with visualization enabled.
"""

import asyncio
import time
import uuid
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional

# Add mock discord module to avoid dependency issues
class MockDiscord:
    class Embed:
        def __init__(self, title=None, description=None, color=None, timestamp=None):
            self.title = title
            self.description = description
            self.color = color
            self.timestamp = timestamp
            self.fields = []
            self.footer = None
        
        def add_field(self, name, value, inline=False):
            self.fields.append({"name": name, "value": value, "inline": inline})
            return self
            
        def set_footer(self, text=None, icon_url=None):
            self.footer = {"text": text, "icon_url": icon_url}
            return self
    
    class Color:
        blue = lambda: "blue"
        green = lambda: "green"
        red = lambda: "red"
        light_grey = lambda: "light_grey"

# Create a mock discord module
sys.modules["discord"] = MockDiscord

# Now import our modules
from src.bot.enhancements.pipeline_visualizer import create_visualizer, PipelineVisualizer
from src.core.logger import get_logger, generate_correlation_id

# Configure logging
logger = get_logger(__name__)

class MockInteraction:
    """Mock Discord interaction for testing"""
    
    class MockResponse:
        def __init__(self):
            self.is_done_flag = False
            
        def is_done(self):
            return self.is_done_flag
            
        async def send_message(self, content=None, embed=None, ephemeral=False):
            print(f"[MOCK] Sending message: {content or 'Embed message'}")
            self.is_done_flag = True
            
    class MockFollowup:
        async def send(self, content=None, embed=None, ephemeral=False):
            print(f"[MOCK] Followup message: {content or 'Embed message'}")
            return MockMessage()
            
    class MockUser:
        def __init__(self):
            self.id = "123456789"
            self.name = "Test User"
            
    def __init__(self):
        self.response = self.MockResponse()
        self.followup = self.MockFollowup()
        self.user = self.MockUser()
        
    async def original_response(self):
        return MockMessage()

class MockMessage:
    """Mock Discord message for testing"""
    
    async def edit(self, content=None, embed=None):
        print(f"[MOCK] Editing message: {content or 'Embed updated'}")

async def simulate_pipeline_stage(visualizer: PipelineVisualizer, stage_name: str, 
                                 duration: float = 1.0, fail: bool = False):
    """Simulate a pipeline stage execution"""
    print(f"Starting stage: {stage_name}")
    
    # Start the stage
    input_data = {
        "timestamp": datetime.now().isoformat(),
        "stage": stage_name,
        "parameters": {
            "param1": "value1",
            "param2": 42
        }
    }
    await visualizer.start_stage(stage_name, input_data)
    
    # Simulate processing time
    await asyncio.sleep(duration)
    
    if fail:
        # Simulate stage failure
        error = Exception(f"Simulated error in {stage_name}")
        await visualizer.fail_stage(stage_name, error)
        print(f"Failed stage: {stage_name}")
        return False
    else:
        # Complete the stage
        output_data = {
            "result": f"{stage_name} completed",
            "processing_time": duration,
            "timestamp": datetime.now().isoformat()
        }
        await visualizer.complete_stage(stage_name, output_data)
        print(f"Completed stage: {stage_name}")
        return True

async def simulate_ask_pipeline(debug_mode: bool = True):
    """Simulate the /ask pipeline execution with visualization"""
    print("\n=== Starting Ask Pipeline Simulation ===\n")
    
    # Create mock interaction
    interaction = MockInteraction() if debug_mode else None
    
    # Create visualizer
    correlation_id = generate_correlation_id()
    visualizer = create_visualizer(
        interaction=interaction,
        command_name="ask",
        correlation_id=correlation_id,
        log_to_console=True,
        log_to_discord=interaction is not None
    )
    
    # Start visualization
    await visualizer.start()
    
    try:
        # Register pipeline stages
        await visualizer.register_stage("initialization", "Pipeline initialization and context setup")
        await visualizer.register_stage("query_processing", "Processing user query")
        await visualizer.register_stage("ai_processing", "AI model processing")
        await visualizer.register_stage("response_formatting", "Formatting and validating response")
        await visualizer.register_stage("completion", "Pipeline completion and cleanup")
        
        # Simulate pipeline execution
        stages = [
            ("initialization", 0.5, False),
            ("query_processing", 1.0, False),
            ("ai_processing", 2.0, False),
            ("response_formatting", 0.8, False),
            ("completion", 0.3, False)
        ]
        
        for stage_name, duration, should_fail in stages:
            success = await simulate_pipeline_stage(
                visualizer, stage_name, duration, should_fail
            )
            if not success:
                break
            
            # Log data flow between stages
            await visualizer.log_data_flow(f"{stage_name}_metrics", {
                "duration": duration,
                "memory_usage": 1024 * (stages.index((stage_name, duration, should_fail)) + 1),
                "timestamp": time.time()
            })
        
        # Wait for visualization to complete
        await asyncio.sleep(2)
    finally:
        # Stop visualization
        await visualizer.stop()
    
    print("\n=== Ask Pipeline Simulation Completed ===\n")

async def simulate_analyze_pipeline(debug_mode: bool = True, should_fail: bool = False):
    """Simulate the /analyze pipeline execution with visualization"""
    print("\n=== Starting Analyze Pipeline Simulation ===\n")
    
    # Create mock interaction
    interaction = MockInteraction() if debug_mode else None
    
    # Create visualizer
    correlation_id = generate_correlation_id()
    visualizer = create_visualizer(
        interaction=interaction,
        command_name="analyze",
        correlation_id=correlation_id,
        log_to_console=True,
        log_to_discord=interaction is not None
    )
    
    # Start visualization
    await visualizer.start()
    
    try:
        # Register pipeline stages
        await visualizer.register_stage("initialization", "Pipeline initialization and context setup")
        await visualizer.register_stage("symbol_validation", "Validating stock symbol")
        await visualizer.register_stage("market_data_retrieval", "Retrieving market data")
        await visualizer.register_stage("technical_analysis", "Performing technical analysis")
        await visualizer.register_stage("sentiment_analysis", "Analyzing market sentiment")
        await visualizer.register_stage("response_generation", "Generating analysis response")
        await visualizer.register_stage("completion", "Pipeline completion and cleanup")
        
        # Simulate pipeline execution
        stages = [
            ("initialization", 0.5, False),
            ("symbol_validation", 0.7, False),
            ("market_data_retrieval", 1.5, False),
            ("technical_analysis", 2.0, False),
            ("sentiment_analysis", 1.2, should_fail),  # Optional failure point
            ("response_generation", 0.8, False),
            ("completion", 0.3, False)
        ]
        
        for stage_name, duration, fail in stages:
            success = await simulate_pipeline_stage(
                visualizer, stage_name, duration, fail
            )
            if not success:
                break
            
            # Log data flow between stages
            await visualizer.log_data_flow(f"{stage_name}_metrics", {
                "duration": duration,
                "data_points": 100 * (stages.index((stage_name, duration, fail)) + 1),
                "timestamp": time.time()
            })
        
        # Wait for visualization to complete
        await asyncio.sleep(2)
    finally:
        # Stop visualization
        await visualizer.stop()
    
    print("\n=== Analyze Pipeline Simulation Completed ===\n")

async def main():
    """Run the pipeline visualization tests"""
    print("Starting Pipeline Visualization Tests")
    
    # Test Ask Pipeline
    await simulate_ask_pipeline(debug_mode=True)
    
    # Test Analyze Pipeline (success case)
    await simulate_analyze_pipeline(debug_mode=True, should_fail=False)
    
    # Test Analyze Pipeline (failure case)
    await simulate_analyze_pipeline(debug_mode=True, should_fail=True)
    
    print("All Pipeline Visualization Tests Completed")

if __name__ == "__main__":
    asyncio.run(main())
