"""
Test Webhook Integration

This script tests the webhook integration for the audit system.
It sends test messages to the configured webhook URL to verify functionality.
"""

import asyncio
import logging
import sys
import os
import json
import aiohttp
from datetime import datetime
from enum import Enum

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("webhook-test")

# Define AuditLevel enum since we can't import it
class AuditLevel(Enum):
    """Audit logging levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

# Webhook URL
WEBHOOK_URL = "https://discord.com/api/webhooks/1414317125371428874/q9zbJZJee_14EBav22k0JJ3z-ANTfpRt3tDCeAScg3EOy22trtVR8yy2iS9AnMu1nX8p"

class MockInteraction:
    """Mock Discord interaction for testing"""
    
    class MockUser:
        def __init__(self, id="123456789", name="Test User"):
            self.id = id
            self.name = name
            self.display_name = name
    
    def __init__(self, user_id="123456789", user_name="Test User", guild_id="987654321"):
        self.user = self.MockUser(id=user_id, name=user_name)
        self.guild_id = guild_id

class MockEmbed:
    """Mock Discord embed for testing"""
    
    class MockFooter:
        def __init__(self, text=None):
            self.text = text
            self.icon_url = None
    
    class MockField:
        def __init__(self, name, value, inline=False):
            self.name = name
            self.value = value
            self.inline = inline
    
    def __init__(self, title=None, description=None):
        self.title = title
        self.description = description
        self.color = 0x00ff00  # Green
        self.timestamp = datetime.utcnow()
        self.fields = []
        self.footer = self.MockFooter()
    
    def add_field(self, name, value, inline=False):
        self.fields.append(self.MockField(name, value, inline))
        return self
    
    def set_footer(self, text=None, icon_url=None):
        self.footer = self.MockFooter(text)
        self.footer.icon_url = icon_url
        return self

async def send_to_webhook(embed):
    """Send an embed to the webhook"""
    try:
        # Convert embed to webhook payload
        payload = {
            "embeds": [{
                "title": embed.title,
                "description": embed.description,
                "color": embed.color,
                "timestamp": embed.timestamp.isoformat() if embed.timestamp else datetime.utcnow().isoformat(),
                "fields": [{
                    "name": field.name,
                    "value": field.value,
                    "inline": field.inline
                } for field in embed.fields]
            }]
        }
        
        # Add footer if present
        if embed.footer and embed.footer.text:
            payload["embeds"][0]["footer"] = {
                "text": embed.footer.text,
                "icon_url": embed.footer.icon_url
            }
        
        # Send to webhook
        async with aiohttp.ClientSession() as session:
            async with session.post(WEBHOOK_URL, json=payload) as response:
                if response.status >= 400:
                    logger.error(f"Webhook request failed with status {response.status}: {await response.text()}")
                    return False
                return True
                
    except Exception as e:
        logger.error(f"Failed to send to webhook: {e}")
        return False

async def test_webhook_direct():
    """Test sending a message directly to the webhook"""
    logger.info("Testing direct webhook message...")
    
    # Create a mock embed
    embed = MockEmbed(
        title="📊 Webhook Test",
        description="This is a test message sent directly to the webhook."
    )
    embed.add_field(
        name="Test Field",
        value="This is a test field value",
        inline=False
    )
    embed.set_footer(text="Test Footer")
    
    # Send to webhook
    success = await send_to_webhook(embed)
    
    if success:
        logger.info("✅ Direct webhook test successful!")
    else:
        logger.error("❌ Direct webhook test failed!")
    
    return success

async def test_request_visualization():
    """Test the request visualization functionality"""
    logger.info("Testing request visualization...")
    
    # Create mock interaction
    interaction = MockInteraction(
        user_id="123456789",
        user_name="Test User",
        guild_id="987654321"
    )
    
    # Create embed for request visualization
    embed = MockEmbed(
        title="📥 Command Request: /test_command",
        description="Command request received"
    )
    embed.add_field(
        name="Command",
        value="/test_command",
        inline=True
    )
    embed.add_field(
        name="Correlation ID",
        value="test-correlation-id",
        inline=True
    )
    embed.add_field(
        name="User",
        value=f"{interaction.user.name} ({interaction.user.id})",
        inline=True
    )
    embed.add_field(
        name="Arguments",
        value="**param1**: `value1`\n**param2**: `42`",
        inline=False
    )
    embed.set_footer(text=f"Request ID: test-correlation-id")
    
    # Send to webhook
    success = await send_to_webhook(embed)
    
    logger.info("✅ Request visualization test completed!")
    
    return success

async def test_response_visualization():
    """Test the response visualization functionality"""
    logger.info("Testing response visualization...")
    
    # Create mock interaction
    interaction = MockInteraction(
        user_id="123456789",
        user_name="Test User",
        guild_id="987654321"
    )
    
    # Create embed for successful response
    success_embed = MockEmbed(
        title="📤 Command Response: /test_command",
        description="Command completed in 1.50s"
    )
    success_embed.add_field(
        name="Command",
        value="/test_command",
        inline=True
    )
    success_embed.add_field(
        name="Correlation ID",
        value="test-correlation-id",
        inline=True
    )
    success_embed.add_field(
        name="Execution Time",
        value="1.50s",
        inline=True
    )
    success_embed.add_field(
        name="Status",
        value="✅ Success",
        inline=True
    )
    success_embed.add_field(
        name="User",
        value=f"{interaction.user.name} ({interaction.user.id})",
        inline=True
    )
    success_embed.add_field(
        name="Response",
        value="```\nThis is a test response\n```",
        inline=False
    )
    success_embed.set_footer(text="Response ID: test-correlation-id")
    success_embed.color = 0x00ff00  # Green for success
    
    # Send success response to webhook
    await send_to_webhook(success_embed)
    
    # Create embed for error response
    error_embed = MockEmbed(
        title="📤 Command Response: /test_command",
        description="Command failed in 0.50s"
    )
    error_embed.add_field(
        name="Command",
        value="/test_command",
        inline=True
    )
    error_embed.add_field(
        name="Correlation ID",
        value="test-error-id",
        inline=True
    )
    error_embed.add_field(
        name="Execution Time",
        value="0.50s",
        inline=True
    )
    error_embed.add_field(
        name="Status",
        value="❌ Failed",
        inline=True
    )
    error_embed.add_field(
        name="User",
        value=f"{interaction.user.name} ({interaction.user.id})",
        inline=True
    )
    error_embed.add_field(
        name="Response",
        value="```\nError occurred\n```",
        inline=False
    )
    error_embed.add_field(
        name="Error",
        value="**Type**: Exception\n**Message**: Test error message",
        inline=False
    )
    error_embed.set_footer(text="Response ID: test-error-id")
    error_embed.color = 0xff0000  # Red for error
    
    # Send error response to webhook
    await send_to_webhook(error_embed)
    
    logger.info("✅ Response visualization test completed!")
    
    return True

async def test_pipeline_visualization():
    """Test the pipeline visualization functionality"""
    logger.info("Testing pipeline visualization...")
    
    # Create mock interaction
    interaction = MockInteraction(
        user_id="123456789",
        user_name="Test User",
        guild_id="987654321"
    )
    
    # Create pipeline data
    pipeline_data = {
        "status": "completed",
        "execution_time": 3.5,
        "stages": [
            {
                "name": "initialization",
                "status": "completed",
                "duration": 0.2
            },
            {
                "name": "processing",
                "status": "completed",
                "duration": 2.5
            },
            {
                "name": "completion",
                "status": "completed",
                "duration": 0.8
            }
        ],
        "errors": []
    }
    
    # Create embed for pipeline visualization
    embed = MockEmbed(
        title="🔄 Pipeline Visualization: /test_command",
        description="Pipeline status: **COMPLETED**"
    )
    embed.add_field(
        name="Command",
        value="/test_command",
        inline=True
    )
    embed.add_field(
        name="Correlation ID",
        value="test-pipeline-id",
        inline=True
    )
    embed.add_field(
        name="Execution Time",
        value="3.50s",
        inline=True
    )
    
    # Add stages
    stages_text = "✅ **initialization** (0.20s)\n"
    stages_text += "✅ **processing** (2.50s)\n"
    stages_text += "✅ **completion** (0.80s)\n"
    
    embed.add_field(
        name="Pipeline Stages",
        value=stages_text,
        inline=False
    )
    
    embed.set_footer(text="Pipeline ID: test-pipeline-id")
    embed.color = 0x00ff00  # Green for completed
    
    # Send to webhook
    success = await send_to_webhook(embed)
    
    logger.info("✅ Pipeline visualization test completed!")
    
    return success

async def test_audit_events():
    """Test the audit event logging functionality"""
    logger.info("Testing audit event logging...")
    
    # Test all audit levels
    for level in AuditLevel:
        # Create embed for audit event
        emoji_map = {
            AuditLevel.DEBUG: "🔍",
            AuditLevel.INFO: "ℹ️",
            AuditLevel.WARNING: "⚠️",
            AuditLevel.ERROR: "❌",
            AuditLevel.CRITICAL: "🚨"
        }
        emoji = emoji_map.get(level, "ℹ️")
        
        color_map = {
            AuditLevel.DEBUG: 0xcccccc,  # Light grey
            AuditLevel.INFO: 0x3498db,   # Blue
            AuditLevel.WARNING: 0xf1c40f, # Gold
            AuditLevel.ERROR: 0xe74c3c,  # Red
            AuditLevel.CRITICAL: 0x992d22 # Dark red
        }
        
        embed = MockEmbed(
            title=f"{emoji} Audit Event: {level.value.upper()}",
            description=f"Test audit event at {level.value} level"
        )
        embed.add_field(
            name="Command",
            value="/test_command",
            inline=True
        )
        embed.add_field(
            name="Correlation ID",
            value="test-audit-id",
            inline=True
        )
        embed.add_field(
            name="Additional Data",
            value="**test**: `True`\n**level**: `{level.value}`\n**timestamp**: `{datetime.utcnow().isoformat()}`",
            inline=False
        )
        embed.set_footer(text=f"Audit Event | {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}")
        embed.color = color_map.get(level, 0x3498db)
        
        # Send to webhook
        await send_to_webhook(embed)
    
    logger.info("✅ Audit event logging test completed!")
    
    return True

async def main():
    """Run all webhook tests"""
    logger.info("Starting webhook integration tests...")
    
    try:
        # Test direct webhook
        await test_webhook_direct()
        
        # Test request visualization
        await test_request_visualization()
        
        # Test response visualization
        await test_response_visualization()
        
        # Test pipeline visualization
        await test_pipeline_visualization()
        
        # Test audit events
        await test_audit_events()
        
        logger.info("All webhook tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during webhook tests: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
