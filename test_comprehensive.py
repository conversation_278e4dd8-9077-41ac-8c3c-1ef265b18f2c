#!/usr/bin/env python3
"""
Comprehensive test script for Trading Automation System
"""

import sys
import asyncio
import os
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.append('/app')

def test_imports():
    """Test that all major modules can be imported"""
    print("🧪 Testing Module Imports")
    print("=" * 50)
    
    modules_to_test = [
        ("Options Greeks Calculator", "src.shared.technical_analysis.options_greeks_calculator"),
        ("Finnhub Provider", "src.api.data.providers.finnhub"),
        ("Alpaca Provider", "src.shared.data_providers.alpaca_provider"),
        ("Technical Analysis Calculator", "src.shared.technical_analysis.calculator"),
        ("Market Data Service", "src.api.data.market_data_service"),
        ("Data Aggregator", "src.shared.data_providers.aggregator")
    ]
    
    results = {}
    
    for name, module_path in modules_to_test:
        try:
            __import__(module_path)
            print(f"✅ {name}: Import successful")
            results[name] = True
        except Exception as e:
            print(f"❌ {name}: Import failed - {e}")
            results[name] = False
    
    return results

def test_options_greeks():
    """Test options Greeks calculator"""
    print("\n🧮 Testing Options Greeks Calculator")
    print("=" * 50)
    
    try:
        from src.shared.technical_analysis.options_greeks_calculator import (
            OptionsGreeksCalculator, OptionType, GreeksSnapshot
        )
        
        calculator = OptionsGreeksCalculator()
        
        # Create synthetic Greeks data
        synthetic_greeks = GreeksSnapshot(
            delta=0.52,
            gamma=0.008,
            theta=-0.45,
            vega=0.12,
            rho=0.03,
            price=12.50,
            underlying_price=573.50,
            strike=573.0,
            time_to_expiry=7/365,
            implied_volatility=0.25,
            risk_free_rate=0.05
        )
        
        # Test future Greeks estimation
        future_greeks = calculator.estimate_future_greeks(
            synthetic_greeks, 5.0, 1/365, 0.01
        )
        
        print(f"✅ Future Greeks calculation: {future_greeks['price']:.2f}")
        
        # Test breakeven calculation
        breakeven = calculator.calculate_breakeven_points(
            synthetic_greeks, OptionType.CALL
        )
        
        print(f"✅ Breakeven calculation: ${breakeven['breakeven_price']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Options Greeks test failed: {e}")
        return False

async def test_finnhub_provider():
    """Test Finnhub provider with the datetime fix"""
    print("\n📊 Testing Finnhub Provider (Datetime Fix)")
    print("=" * 50)
    
    try:
        from src.api.data.providers.finnhub import FinnhubProvider
        
        provider = FinnhubProvider()
        
        # Test the datetime parameter fix
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        print(f"🕐 Testing datetime parameters:")
        print(f"   Start: {start_time}")
        print(f"   End: {end_time}")
        
        # This should work without the previous datetime type error
        try:
            # Note: This will likely fail due to API rate limits, but shouldn't crash
            # due to datetime type errors
            data = await provider.get_historical_data('QQQ', start_time, end_time, "15")
            print("✅ No datetime type error occurred - fix is working!")
            return True
        except Exception as e:
            if "unsupported type for timedelta" in str(e):
                print("❌ Datetime type error still exists")
                return False
            else:
                print(f"✅ No datetime type error (other error: {type(e).__name__})")
                return True
                
    except Exception as e:
        print(f"❌ Finnhub provider test failed: {e}")
        return False

async def test_market_data_service():
    """Test market data service"""
    print("\n📈 Testing Market Data Service")
    print("=" * 50)
    
    try:
        from src.api.data.market_data_service import MarketDataService
        
        service = MarketDataService()
        
        # Test basic functionality
        print("✅ Market data service initialized")
        
        # Test that the service has the expected methods
        expected_methods = ['get_current_price', 'get_historical_data']
        for method in expected_methods:
            if hasattr(service, method):
                print(f"✅ Method {method} available")
            else:
                print(f"❌ Method {method} missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Market data service test failed: {e}")
        return False

async def test_technical_analysis():
    """Test technical analysis calculator"""
    print("\n📊 Testing Technical Analysis Calculator")
    print("=" * 50)
    
    try:
        from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator
        from src.shared.technical_analysis.indicators import calculate_rsi, calculate_sma
        
        calculator = TechnicalAnalysisCalculator()
        
        # Test with synthetic data
        import pandas as pd
        prices = pd.Series([100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110])
        volumes = [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000]
        
        # Test RSI calculation using the imported function
        rsi = calculate_rsi(prices, period=14)
        if rsi is not None:
            print(f"✅ RSI calculation: {rsi:.2f}")
        else:
            print("⚠️  RSI calculation returned None (may need more data points)")
        
        # Test SMA calculation using the imported function
        sma = calculate_sma(prices, window=5)
        if sma is not None:
            print(f"✅ SMA calculation: {sma:.2f}")
        else:
            print("⚠️  SMA calculation returned None (may need more data points)")
        
        # Test the calculator's built-in method
        df = pd.DataFrame({
            'close': prices,
            'high': prices + 1,
            'low': prices - 1,
            'open': prices - 0.5,
            'volume': volumes
        })
        
        all_indicators = calculator.calculate_all_indicators(df, "TEST")
        print(f"✅ All indicators calculation: {len(all_indicators)} indicators")
        
        # Test individual indicator methods if available
        if hasattr(calculator, '_calculate_trend_indicators'):
            trend_indicators = calculator._calculate_trend_indicators(prices)
            print(f"✅ Trend indicators: {len(trend_indicators)} calculated")
        
        return True
        
    except Exception as e:
        print(f"❌ Technical analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Comprehensive Trading Automation Tests")
    print("=" * 70)
    
    results = {}
    
    # Test 1: Module imports
    results['Module Imports'] = test_imports()
    
    # Test 2: Options Greeks calculator
    results['Options Greeks'] = test_options_greeks()
    
    # Test 3: Finnhub provider (datetime fix)
    results['Finnhub Provider'] = await test_finnhub_provider()
    
    # Test 4: Market data service
    results['Market Data Service'] = await test_market_data_service()
    
    # Test 5: Technical analysis
    results['Technical Analysis'] = await test_technical_analysis()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 Comprehensive Test Summary")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {test_name:<25}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The system is working correctly.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    asyncio.run(main()) 