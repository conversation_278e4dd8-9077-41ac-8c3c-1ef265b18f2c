"""
Test script for the Market Data API.
Run this script to test the API endpoints.
"""
import requests
import sys

def test_market_api(base_url="http://localhost:8000"):
    """Test the market data API endpoints."""
    # Test current price
    print("Testing /api/market/price/AAPL")
    response = requests.get(f"{base_url}/api/market/price/AAPL")
    print(f"Status Code: {response.status_code}")
    print("Response:", response.json())
    print("-" * 50)
    
    # Test historical data
    print("\nTesting /api/market/historical/AAPL")
    params = {'start_date': '2025-01-01', 'interval': '1d'}
    response = requests.get(f"{base_url}/api/market/historical/AAPL", params=params)
    print(f"Status Code: {response.status_code}")
    data = response.json()
    print(f"Got {len(data.get('data', []))} data points")
    if data.get('data'):
        print(f"First data point: {data['data'][0]}")
    print("-" * 50)
    
    # Test intraday data
    print("\nTesting /api/market/intraday/AAPL")
    response = requests.get(f"{base_url}/api/market/intraday/AAPL?interval=5m&days=1")
    print(f"Status Code: {response.status_code}")
    data = response.json()
    print(f"Got {len(data.get('data', []))} data points")
    if data.get('data'):
        print(f"First data point: {data['data'][0]}")

if __name__ == "__main__":
    # Allow overriding the base URL with a command line argument
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    test_market_api(base_url)
