#!/usr/bin/env python3
"""
Test script to verify Phase 3 improvements:
- Concurrent data fetching with asyncio.gather()
- Request timeouts at each stage
- Progressive response generation
- Circuit breaker pattern for failing providers
"""

import asyncio
import sys
import os
import time

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_phase3_improvements():
    """Test the Phase 3 improvements to the ask pipeline."""
    try:
        print("🔍 Testing Phase 3 improvements...")
        
        # Test importing the enhanced components
        from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor
        print("✅ Enhanced AI Chat Processor imported successfully")
        
        # Test the enhanced technical indicators calculation
        print("\n🧪 Testing Phase 3 performance improvements...")
        
        # Create a test config
        test_config = {
            'technical': {
                'rsi_period': 14,
                'sma_short': 20,
                'sma_long': 50,
                'ema_short': 12,
                'ema_long': 26,
                'decimal_places': 2
            }
        }
        
        # Create processor instance
        processor = AIChatProcessor(test_config)
        print("✅ Processor instance created successfully")
        
        # Test circuit breaker functionality
        print("\n🧪 Testing circuit breaker pattern...")
        
        # Test initial state
        initial_state = processor.circuit_breaker['state']
        print(f"✅ Initial circuit breaker state: {initial_state}")
        
        # Test circuit breaker methods
        if hasattr(processor, '_check_circuit_breaker'):
            print("✅ Circuit breaker check method exists")
        else:
            print("❌ Circuit breaker check method missing")
            
        if hasattr(processor, '_record_success'):
            print("✅ Circuit breaker success recording method exists")
        else:
            print("❌ Circuit breaker success recording method missing")
            
        if hasattr(processor, '_record_failure'):
            print("✅ Circuit breaker failure recording method exists")
        else:
            print("❌ Circuit breaker failure recording method missing")
        
        # Test progressive response generation
        print("\n🧪 Testing progressive response generation...")
        
        if hasattr(processor, '_generate_progressive_response'):
            print("✅ Progressive response generation method exists")
            
            # Test progressive response with sample data
            base_response = "Here's your analysis:"
            available_data = {
                'AAPL': {
                    'current_price': 150.25,
                    'change_percent': 1.5,
                    'data_available': True
                },
                'MSFT': {
                    'current_price': 320.50,
                    'change_percent': -0.8,
                    'data_available': True
                }
            }
            all_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA']
            
            progressive_response = processor._generate_progressive_response(base_response, available_data, all_symbols)
            print(f"✅ Progressive response generated: {len(progressive_response)} characters")
            print(f"📊 Response preview: {progressive_response[:100]}...")
        else:
            print("❌ Progressive response generation method missing")
        
        # Test concurrent data fetching structure
        print("\n🧪 Testing concurrent data fetching structure...")
        
        # Check if the concurrent fetching logic exists in the code
        with open('src/bot/pipeline/commands/ask/stages/ai_chat_processor.py', 'r') as f:
            content = f.read()
            if 'asyncio.gather' in content:
                print("✅ Concurrent data fetching with asyncio.gather implemented")
            else:
                print("❌ Concurrent data fetching not implemented")
                
            if 'semaphore' in content:
                print("✅ Semaphore-based concurrency control implemented")
            else:
                print("❌ Semaphore-based concurrency control not implemented")
                
            if 'timeout=' in content:
                print("✅ Request timeouts implemented")
            else:
                print("❌ Request timeouts not implemented")
        
        print("\n🎉 Phase 3 improvements test completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Phase 3 improvements test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_phase3_improvements())
    sys.exit(0 if success else 1) 