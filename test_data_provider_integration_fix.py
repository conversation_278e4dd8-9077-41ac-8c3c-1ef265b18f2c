#!/usr/bin/env python3
"""
Test script to verify data provider configuration and yfinance integration fixes.

This test validates:
1. Enhanced provider configuration
2. Dependency checking
3. Fallback mechanisms
4. Error handling improvements
5. yfinance integration
"""

import sys
import os
import traceback
import asyncio

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_enhanced_provider_config():
    """Test the enhanced provider configuration system"""
    print("🧪 Testing Enhanced Provider Configuration...")
    
    try:
        from src.data.providers.enhanced_config import (
            get_enhanced_provider_config,
            get_available_providers,
            get_fallback_chain,
            is_yfinance_available,
            get_provider_status_summary
        )
        
        config = get_enhanced_provider_config()
        
        # Test 1: Provider status checking
        print("  ✅ Test 1: Provider status checking")
        status_report = config.get_provider_status_report()
        print(f"     Total providers: {status_report['total_providers']}")
        print(f"     Available providers: {status_report['available_providers']}")
        
        # Test 2: Available providers
        print("  ✅ Test 2: Available providers")
        available = get_available_providers()
        print(f"     Available: {available}")
        
        # Test 3: Fallback chain
        print("  ✅ Test 3: Fallback chain")
        chain = get_fallback_chain()
        print(f"     Fallback chain: {' → '.join(chain) if chain else 'None'}")
        
        # Test 4: yfinance availability
        print("  ✅ Test 4: yfinance availability")
        yf_available = is_yfinance_available()
        print(f"     yfinance available: {yf_available}")
        
        # Test 5: Status summary
        print("  ✅ Test 5: Status summary")
        summary = get_provider_status_summary()
        print(f"     Summary: {summary}")
        
        # Test 6: Provider details
        print("  ✅ Test 6: Provider details")
        for name, details in status_report["provider_details"].items():
            status = details["status"]
            error = details.get("error_message", "None")
            print(f"     {name}: {status} | Error: {error}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def test_fallback_provider():
    """Test the fallback data provider"""
    print("🧪 Testing Fallback Data Provider...")
    
    try:
        from src.data.providers.fallback_provider import (
            get_fallback_provider,
            get_fallback_data_for_symbol,
            cache_successful_data
        )
        
        provider = get_fallback_provider()
        
        # Test 1: Mock data generation
        print("  ✅ Test 1: Mock data generation")
        fallback_data = provider.get_fallback_data("AAPL")
        print(f"     AAPL mock data: ${fallback_data.price} ({fallback_data.change_percent:+.2f}%)")
        print(f"     Source: {fallback_data.source}")
        
        # Test 2: Dictionary conversion
        print("  ✅ Test 2: Dictionary conversion")
        data_dict = provider.to_dict(fallback_data)
        print(f"     Dict keys: {list(data_dict.keys())}")
        print(f"     Suspicious: {data_dict.get('data_suspicious', False)}")
        
        # Test 3: Cache functionality
        print("  ✅ Test 3: Cache functionality")
        test_data = {
            'current_price': 180.50,
            'change': 2.30,
            'change_percent': 1.29,
            'volume': 45000000
        }
        cache_successful_data("AAPL", test_data)
        cached = provider.get_cached_data("AAPL")
        if cached:
            print(f"     Cached AAPL: ${cached.price} (age: {cached.age_minutes}m)")
        
        # Test 4: Market indices fallback
        print("  ✅ Test 4: Market indices fallback")
        market_data = provider.get_market_indices_fallback()
        indices = market_data.get('market_indices', {})
        print(f"     Market indices: {len(indices)} indices")
        for symbol, data in indices.items():
            print(f"       {symbol}: ${data['current_price']} ({data['change_percent']:+.1f}%)")
        
        # Test 5: Cache stats
        print("  ✅ Test 5: Cache statistics")
        stats = provider.get_cache_stats()
        print(f"     Cache stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def test_yfinance_integration():
    """Test yfinance integration and error handling"""
    print("🧪 Testing yfinance Integration...")
    
    try:
        # Test 1: Direct yfinance import
        print("  ✅ Test 1: Direct yfinance import")
        import yfinance as yf
        print("     yfinance imported successfully")
        
        # Test 2: Basic yfinance functionality
        print("  ✅ Test 2: Basic yfinance functionality")
        ticker = yf.Ticker("AAPL")
        hist = ticker.history(period="1d")
        if not hist.empty:
            latest_price = hist['Close'].iloc[-1]
            print(f"     AAPL latest price: ${latest_price:.2f}")
        else:
            print("     No historical data available")
        
        # Test 3: Enhanced yfinance provider
        print("  ✅ Test 3: Enhanced yfinance provider")
        try:
            from src.shared.data_providers.yfinance_provider import YFinanceProvider
            provider = YFinanceProvider()
            print(f"     YFinanceProvider created: {provider.provider_name}")
            print(f"     Configured: {provider.is_configured()}")
        except Exception as e:
            print(f"     YFinanceProvider error: {e}")
        
        # Test 4: Error handling improvements
        print("  ✅ Test 4: Error handling improvements")
        try:
            # This should not raise "yfinance package not available" since it's installed
            from src.api.data.providers.data_source_manager import RealYahooFinanceProvider
            yahoo_provider = RealYahooFinanceProvider()
            print("     RealYahooFinanceProvider created successfully")
        except Exception as e:
            print(f"     Expected error (provider may need config): {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

async def test_async_data_fetching():
    """Test async data fetching with fallback"""
    print("🧪 Testing Async Data Fetching with Fallback...")
    
    try:
        from src.data.providers.fallback_provider import get_fallback_data_for_symbol
        
        # Test 1: Fallback data fetching
        print("  ✅ Test 1: Fallback data fetching")
        symbols = ["AAPL", "MSFT", "GOOGL", "UNKNOWN_SYMBOL"]
        
        for symbol in symbols:
            try:
                data = get_fallback_data_for_symbol(symbol)
                price = data.get('current_price', 0)
                source = data.get('source', 'unknown')
                suspicious = data.get('data_suspicious', False)
                print(f"     {symbol}: ${price:.2f} from {source} (suspicious: {suspicious})")
            except Exception as e:
                print(f"     {symbol}: Error - {e}")
        
        # Test 2: Async compatibility
        print("  ✅ Test 2: Async compatibility")
        await asyncio.sleep(0.1)  # Simulate async operation
        print("     Async operations working correctly")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def test_requirements_version():
    """Test requirements.txt version update"""
    print("🧪 Testing Requirements Version Update...")
    
    try:
        # Test 1: Check requirements.txt
        print("  ✅ Test 1: Check requirements.txt")
        with open('requirements.txt', 'r') as f:
            content = f.read()
        
        if 'yfinance==0.2.65' in content:
            print("     yfinance version updated to 0.2.65 ✅")
        elif 'yfinance==0.2.18' in content:
            print("     yfinance still at old version 0.2.18 ⚠️")
        else:
            print("     yfinance version not found in requirements.txt")
        
        # Test 2: Check installed version
        print("  ✅ Test 2: Check installed version")
        import yfinance
        if hasattr(yfinance, '__version__'):
            print(f"     Installed yfinance version: {yfinance.__version__}")
        else:
            print("     yfinance version not available")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Run all tests"""
    print("🔧 Testing Data Provider Configuration and yfinance Integration Fixes\n")
    
    tests = [
        test_enhanced_provider_config,
        test_fallback_provider,
        test_yfinance_integration,
        test_async_data_fetching,
        test_requirements_version
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if asyncio.iscoroutinefunction(test):
                result = await test()
            else:
                result = test()
            
            if result:
                passed += 1
                print("✅ PASSED\n")
            else:
                print("❌ FAILED\n")
        except Exception as e:
            print(f"❌ FAILED with exception: {e}\n")
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Data provider configuration and yfinance integration fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
