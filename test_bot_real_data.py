#!/usr/bin/env python3
"""
Test script for bot's real data fetching capabilities.
This tests the actual working data pipeline that the bot uses.
"""

import asyncio
import logging
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_bot_real_data_fetching():
    """Test the bot's real data fetching capabilities"""
    try:
        from src.api.data.market_data_service import MarketDataService
        
        logger.info("🧪 Testing Bot's Real Data Fetching Capabilities")
        logger.info("=" * 60)
        
        # Initialize the same service the bot uses
        market_data_service = MarketDataService()
        
        # Test symbols
        test_symbols = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
        
        for i, symbol in enumerate(test_symbols):
            logger.info(f"\n🔍 Testing {symbol}:")
            
            # Add delay between requests to respect rate limits
            if i > 0:
                logger.info(f"      ⏳ Waiting 2 seconds before next request...")
                await asyncio.sleep(2)
            
            try:
                # Test comprehensive data fetch (what the bot actually uses)
                logger.info(f"      📊 Fetching comprehensive data...")
                comprehensive_data = await market_data_service.get_comprehensive_stock_data(symbol)
                
                if comprehensive_data and comprehensive_data.get('data_available'):
                    data = comprehensive_data
                    logger.info(f"      ✅ Successfully fetched real data for {symbol}")
                    logger.info(f"      💰 Current price: ${data.get('current_price', 'N/A')}")
                    logger.info(f"      📈 Change: {data.get('change_percent', 'N/A')}%")
                    logger.info(f"      📊 Volume: {data.get('volume', 'N/A'):,}")
                    
                    # Technical indicators
                    if data.get('technical_indicators_available'):
                        logger.info(f"      📊 RSI: {data.get('rsi', 'N/A')}")
                        logger.info(f"      📊 MACD: {data.get('macd', 'N/A')}")
                        logger.info(f"      📊 SMA 20: ${data.get('sma_20', 'N/A')}")
                        logger.info(f"      📊 Support: ${data.get('support_levels', ['N/A'])[0] if data.get('support_levels') else 'N/A'}")
                        logger.info(f"      📊 Resistance: ${data.get('resistance_levels', ['N/A'])[0] if data.get('resistance_levels') else 'N/A'}")
                    
                    # Historical data
                    historical = data.get('historical', [])
                    if historical:
                        logger.info(f"      📅 Historical data: {len(historical)} days")
                        logger.info(f"      📅 Date range: {historical[0].get('date', 'N/A')} to {historical[-1].get('date', 'N/A')}")
                    
                else:
                    logger.warning(f"      ⚠️ No data available for {symbol}")
                    
            except Exception as e:
                logger.error(f"      ❌ Failed to fetch data for {symbol}: {e}")
                continue
        
        logger.info(f"\n🎉 Bot real data fetching tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

async def main():
    """Main test execution"""
    try:
        await test_bot_real_data_fetching()
        logger.info("✅ All tests passed!")
    except Exception as e:
        logger.error(f"❌ Tests failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 