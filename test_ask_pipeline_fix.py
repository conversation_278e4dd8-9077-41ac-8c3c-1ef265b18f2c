#!/usr/bin/env python3
"""
Test script to verify the compatibility fix between ai_chat_processor.processor and AskPipeline._run_stage.
This script tests that the context parameter is passed correctly and that the processor returns a dict.
"""

import asyncio
import sys
import os
from unittest.mock import MagicMock, patch

# Mock the external dependencies to avoid import issues
mock_modules = {
    'openai': MagicMock(),
    'src.api.data.market_data_service': MagicMock(),
    'src.bot.pipeline.core.pipeline_engine': MagicMock(),
    'src.bot.pipeline.core.context_manager': MagicMock(),
}

# Mock the AskPipelineConfig class
class MockAskPipelineConfig:
    def __init__(self):
        self.ai_chat_processor = {}
        
    def get(self, key, default=None):
        return getattr(self, key, default)

# Create a mock for the config module
mock_config_module = MagicMock()
mock_config_module.AskPipelineConfig = MockAskPipelineConfig

# Add to mock modules
mock_modules['src.bot.pipeline.commands.ask.config'] = mock_config_module

# Apply all mocks
for mod_name, mock_obj in mock_modules.items():
    sys.modules[mod_name] = mock_obj

# Now import the modules we need to test
from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline

async def test_pipeline():
    """Test the ask pipeline with a sample query."""
    print("Testing ask pipeline compatibility fix...")
    
    # Sample query and expected response
    query = "What is the current price of AAPL?"
    expected_response = {
        'response': 'This is a test response',
        'data': {},
        'intent': 'test',
        'symbols': [],
        'needs_data': False
    }
    
    try:
        # Create a mock PipelineContext
        mock_context = MagicMock()
        mock_context.processing_results = {}
        mock_context.status.value = "completed"
        
        # Mock the AskPipeline class
        with patch('src.bot.pipeline.commands.ask.pipeline.AskPipeline') as mock_pipeline_class:
            # Set up the mock pipeline instance
            mock_pipeline = MagicMock()
            mock_pipeline.run.return_value = {'ai_chat_processor': expected_response}
            mock_pipeline_class.return_value = mock_pipeline
            
            # Mock the PipelineContext class
            with patch('src.bot.pipeline.commands.ask.pipeline.PipelineContext') as mock_context_class:
                mock_context_class.return_value = mock_context
                
                # Execute the pipeline
                context = await execute_ask_pipeline(query, user_id="test_user", guild_id="test_guild")
                
                # Verify the pipeline was instantiated with correct arguments
                mock_pipeline_class.assert_called_once()
                
                # Verify the pipeline was run with the query
                mock_pipeline.run.assert_called_once_with(query)
        
        # Check if we got the expected response
        if hasattr(context, 'processing_results'):
            print("\n📋 Processing results:")
            result = context.processing_results.get('ai_chat_processor', {})
            
            # Check for expected keys
            all_present = True
            for key in expected_response:
                if key in result:
                    print(f"✅ {key} key found in processing results")
                else:
                    print(f"❌ {key} key missing from processing results")
                    all_present = False
            
            if all_present:
                print("\n✅ All expected keys present in processing results")
                print(f"Response: {result['response']}")
            else:
                print("\n❌ Some expected keys are missing from processing results")
                
        else:
            print("❌ No processing_results found in context")
            if hasattr(context, 'status'):
                try:
                    status = getattr(context.status, 'value', 'unknown')
                    print(f"Status: {status}")
                except Exception as e:
                    print(f"Could not determine status: {e}")
            
            # Try to get error information in a safe way
            if hasattr(context, 'errors'):
                try:
                    errors = getattr(context, 'errors', [])
                    if errors:
                        print("\nErrors:")
                        for error in errors:
                            print(f"- {error}")
                except Exception as e:
                    print(f"\n⚠️  Could not access errors: {e}")
            else:
                print("\n⚠️  No error information available")
                    
    except Exception as e:
        print(f"❌ Exception during pipeline execution: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_pipeline())