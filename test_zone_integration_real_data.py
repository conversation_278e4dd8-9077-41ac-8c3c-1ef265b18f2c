#!/usr/bin/env python3
"""
Comprehensive test for supply and demand zone integration using real market data
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ZoneIntegrationRealDataTest:
    """Test suite for supply and demand zone integration with real market data"""
    
    def __init__(self):
        self.market_data_service = None
        self.calculator = None
        self.formatter = None
        self.detector = None
        
    async def setup_services(self):
        """Initialize all required services"""
        logger.info("🔧 Setting up services...")
        
        try:
            # Import and initialize market data service
            from src.shared.data_providers.aggregator import DataProviderAggregator
            self.market_data_service = DataProviderAggregator()
            logger.info("✅ DataProviderAggregator initialized")
            
            # Import technical analysis services
            from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator
            from src.shared.technical_analysis.zones import SupplyDemandDetector
            from src.core.formatting.technical_analysis import TechnicalAnalysisFormatter
            
            self.calculator = TechnicalAnalysisCalculator()
            self.detector = SupplyDemandDetector()
            self.formatter = TechnicalAnalysisFormatter()
            
            logger.info("✅ Technical analysis services initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup services: {e}")
            return False
    
    async def fetch_real_data(self, symbols, days=365):
        """Fetch real market data for testing"""
        logger.info(f"📊 Fetching real data for {symbols}...")
        
        all_data = {}
        
        for symbol in symbols:
            try:
                logger.info(f"🔍 Fetching {symbol}...")
                
                # Get historical data
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)
                
                data = await self.market_data_service.get_historical_data(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    interval='1d'
                )
                
                if data and 'error' not in str(data).lower():
                    # Convert to DataFrame. Providers may return different shapes:
                    # - list of dicts (records)
                    # - dict with key 'data' containing list of records
                    # - dict-of-lists / dict-of-series
                    if isinstance(data, dict):
                        # Handle AlphaVantage-style response
                        if 'data' in data and isinstance(data['data'], (list, tuple)):
                            df = pd.DataFrame(data['data'])
                        elif 'Time Series (Daily)' in data:
                            # Handle raw AlphaVantage format
                            raw_data = data['Time Series (Daily)']
                            df = pd.DataFrame.from_dict(raw_data, orient='index')
                            df = df.reset_index().rename(columns={'index': 'date'})
                        elif 'results' in data:
                            # Handle Polygon.io format
                            df = pd.DataFrame(data['results'])
                        else:
                            # Try to detect dict-of-records vs dict-of-series
                            sample_vals = [v for v in data.values() if isinstance(v, (list, tuple))]
                            if sample_vals:
                                df = pd.DataFrame(data)
                            else:
                                df = pd.DataFrame(data.get('data', []))
                    elif isinstance(data, list):
                        # Direct list of records
                        df = pd.DataFrame(data)
                    else:
                        # Fallback to DataFrame constructor
                        df = pd.DataFrame(data)
                    
                    # Ensure proper column names and data types
                    if 'timestamp' in df.columns:
                        df['date'] = pd.to_datetime(df['timestamp'])
                        df.set_index('date', inplace=True)
                    elif 'Date' in df.columns:
                        df['date'] = pd.to_datetime(df['Date'])
                        df.set_index('date', inplace=True)
                    elif 'date' in df.columns:
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index('date', inplace=True)
                    
                    # Comprehensive column mapping for different providers
                    column_mapping = {
                        'open': ['open', 'Open', '1. open', 'o'],
                        'high': ['high', 'High', '2. high', 'h'],
                        'low': ['low', 'Low', '3. low', 'l'],
                        'close': ['close', 'Close', '4. close', 'c', 'adjclose', 'Adj Close'],
                        'volume': ['volume', 'Volume', '5. volume', 'v', 'adjvolume']
                    }
                    
                    # Map columns with priority order
                    for std_name, alt_names in column_mapping.items():
                        for alt_name in alt_names:
                            if alt_name in df.columns:
                                df[std_name] = pd.to_numeric(df[alt_name], errors='coerce')
                                break
                    
                    # Validate required columns
                    required_cols = ['open', 'high', 'low', 'close', 'volume']
                    missing_cols = [col for col in required_cols if col not in df.columns or df[col].isna().all()]
                    
                    if missing_cols:
                        logger.warning(f"⚠️  Missing columns for {symbol}: {missing_cols}")
                        continue
                    
                    # Clean data
                    df = df[required_cols].dropna()
                    
                    # Ensure we have numeric data and remove duplicates
                    df = df.apply(pd.to_numeric, errors='coerce').dropna()
                    df = df[~df.index.duplicated(keep='first')]
                    
                    # Sort by date
                    if not df.index.is_monotonic_increasing:
                        df = df.sort_index()
                    
                    # Basic validation checks
                    if len(df) < 50:
                        logger.warning(f"⚠️  {symbol}: Insufficient data ({len(df)} points)")
                        continue
                    
                    # Check for suspicious data (e.g., all zeros, negative prices)
                    if (df[['open', 'high', 'low', 'close']] <= 0).any().any():
                        logger.warning(f"⚠️  {symbol}: Invalid price data (negative or zero values)")
                        continue
                    
                    # Check volume data
                    if (df['volume'] < 0).any():
                        logger.warning(f"⚠️  {symbol}: Invalid volume data (negative values)")
                        continue
                    
                    all_data[symbol] = df
                    logger.info(f"✅ {symbol}: {len(df)} data points loaded successfully")
                        
                else:
                    logger.warning(f"⚠️  No data for {symbol}")
                    
            except Exception as e:
                logger.error(f"❌ Error fetching {symbol}: {e}")
                import traceback
                logger.error(traceback.format_exc())
                continue
        
        logger.info(f"📈 Successfully processed {len(all_data)}/{len(symbols)} symbols")
        return all_data
    
    def test_zone_detection_with_real_data(self, symbol, df):
        """Test zone detection with real market data"""
        logger.info(f"🎯 Testing zone detection for {symbol}...")
        
        try:
            # Calculate all indicators including zones
            results = self.calculator.calculate_all_indicators(df, symbol=symbol)
            
            if not results:
                logger.warning(f"⚠️  No results from calculator for {symbol}")
                return False
            
            # Extract zone information
            zones = results.get('supply_demand_zones', [])
            metrics = results.get('zone_metrics', {})
            
            logger.info(f"📈 {symbol} Results:")
            logger.info(f"   - Total zones: {len(zones)}")
            logger.info(f"   - Supply zones: {metrics.get('supply_zone_count', 0)}")
            logger.info(f"   - Demand zones: {metrics.get('demand_zone_count', 0)}")
            logger.info(f"   - Recent zones: {metrics.get('recent_zone_count', 0)}")
            
            # Test zone formatting
            zone_analysis = self.formatter.format_zone_analysis(results)
            logger.info(f"   - Zone formatting: {'✅' if zone_analysis else '❌'}")
            
            # Test indicator summary
            indicator_summary = self.formatter.format_indicators_summary(results)
            logger.info(f"   - Indicator summary: {'✅' if indicator_summary else '❌'}")
            
            # Validate zone data structure
            if zones:
                sample_zone = zones[0]
                required_fields = ['type', 'price', 'strength', 'last_touched', 'touch_count']
                has_all_fields = all(field in sample_zone for field in required_fields)
                logger.info(f"   - Zone data structure: {'✅' if has_all_fields else '❌'}")
                
                # Log sample zones
                supply_zones = [z for z in zones if z['type'] == 'supply']
                demand_zones = [z for z in zones if z['type'] == 'demand']
                
                if supply_zones:
                    logger.info(f"   - Supply zone example: ${supply_zones[0]['price']:.2f} "
                              f"(strength: {supply_zones[0]['strength']})")
                
                if demand_zones:
                    logger.info(f"   - Demand zone example: ${demand_zones[0]['price']:.2f} "
                              f"(strength: {demand_zones[0]['strength']})")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error testing {symbol}: {e}")
            return False
    
    def test_zone_edge_cases(self, symbol, df):
        """Test edge cases with real data"""
        logger.info(f"🔍 Testing edge cases for {symbol}...")
        
        try:
            # Test with different time periods
            test_periods = [30, 90, 180, len(df)]
            
            for period in test_periods:
                if period <= len(df):
                    subset_df = df.tail(period)
                    results = self.calculator.calculate_all_indicators(subset_df, symbol=f"{symbol}_{period}d")
                    
                    if results:
                        zones = results.get('supply_demand_zones', [])
                        logger.info(f"   - {period}d period: {len(zones)} zones")
            
            # Test with very small datasets
            if len(df) > 10:
                small_df = df.tail(10)
                results = self.calculator.calculate_all_indicators(small_df, symbol=f"{symbol}_10d")
                zones = results.get('supply_demand_zones', []) if results else []
                logger.info(f"   - 10d small dataset: {len(zones)} zones")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error testing edge cases for {symbol}: {e}")
            return False
    
    async def run_comprehensive_test(self):
        """Run comprehensive zone integration test with real data"""
        logger.info("🚀 Starting comprehensive zone integration test...")
        logger.info("=" * 70)
        
        # Setup services
        if not await self.setup_services():
            return False
        
        # Test symbols (popular stocks with good data)
        test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMD']
        
        # Fetch real data
        real_data = await self.fetch_real_data(test_symbols, days=365)
        
        if not real_data:
            logger.error("❌ No real data available for testing")
            return False
        
        logger.info(f"✅ Successfully fetched data for {len(real_data)} symbols")
        
        # Test each symbol
        results_summary = {}
        
        for symbol, df in real_data.items():
            logger.info(f"\n📊 Processing {symbol} ({len(df)} data points)...")
            
            # Test zone detection
            zone_success = self.test_zone_detection_with_real_data(symbol, df)
            
            # Test edge cases
            edge_success = self.test_zone_edge_cases(symbol, df)
            
            results_summary[symbol] = {
                'data_points': len(df),
                'zone_success': zone_success,
                'edge_success': edge_success,
                'zones_detected': 0
            }
            
            # Get actual zone count
            try:
                results = self.calculator.calculate_all_indicators(df, symbol=symbol)
                zones = results.get('supply_demand_zones', [])
                results_summary[symbol]['zones_detected'] = len(zones)
            except:
                pass
        
        # Generate summary report
        self.generate_summary_report(results_summary)
        
        return True
    
    def generate_summary_report(self, results_summary):
        """Generate a comprehensive test summary"""
        logger.info("\n📋 TEST SUMMARY REPORT")
        logger.info("=" * 50)
        
        total_symbols = len(results_summary)
        successful_symbols = sum(1 for r in results_summary.values() if r['zone_success'])
        total_zones = sum(r['zones_detected'] for r in results_summary.values())
        
        logger.info(f"📊 Total symbols tested: {total_symbols}")
        logger.info(f"✅ Successful zone detection: {successful_symbols}")
        logger.info(f"📈 Total zones detected: {total_zones}")
        logger.info(f"📊 Average zones per symbol: {total_zones/max(total_symbols,1):.1f}")
        
        # Detailed breakdown
        logger.info("\n🔍 Detailed Results:")
        for symbol, results in results_summary.items():
            status = "✅" if results['zone_success'] else "❌"
            logger.info(f"   {status} {symbol}: {results['data_points']} data points, "
                       f"{results['zones_detected']} zones")
        
        # Performance metrics
        logger.info("\n⚡ Performance Metrics:")
        logger.info(f"   - Total data points processed: {sum(r['data_points'] for r in results_summary.values())}")
        logger.info(f"   - Average data points per symbol: {sum(r['data_points'] for r in results_summary.values())/max(total_symbols,1):.0f}")
        
        return {
            'total_symbols': total_symbols,
            'successful_symbols': successful_symbols,
            'total_zones': total_zones,
            'results': results_summary
        }


async def main():
    """Main test runner"""
    logger.info("🧪 Zone Integration Test with Real Market Data")
    logger.info("=" * 70)
    
    try:
        test_runner = ZoneIntegrationRealDataTest()
        success = await test_runner.run_comprehensive_test()
        
        if success:
            logger.info("\n🎉 Test completed successfully!")
            return 0
        else:
            logger.error("\n❌ Test failed!")
            return 1
            
    except KeyboardInterrupt:
        logger.info("\n⚠️  Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"\n💥 Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)