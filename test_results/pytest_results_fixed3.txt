 Container tradingview-automation-redis-1  Running
============================= test session starts ==============================
platform linux -- Python 3.10.18, pytest-7.4.3, pluggy-1.6.0 -- /usr/local/bin/python
cachedir: .pytest_cache
rootdir: /app
configfile: pytest.ini
plugins: cov-4.1.0, asyncio-0.23.2, anyio-3.7.1
asyncio: mode=strict
collecting ... {"timestamp": "2025-08-24T17:39:30.700310", "level": "DEBUG", "logger": "passlib.registry", "message": "registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>", "module": "registry", "function": "register_crypt_handler", "line": 296, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:30.704915", "level": "DEBUG", "logger": "passlib.utils.compat", "message": "loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>", "module": "__init__", "function": "__getattr__", "line": 449, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:30.705588", "level": "DEBUG", "logger": "passlib.utils.compat", "message": "loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>", "module": "__init__", "function": "__getattr__", "line": 449, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:30.705932", "level": "DEBUG", "logger": "passlib.utils.compat", "message": "loaded lazy attr 'BytesIO': <class '_io.BytesIO'>", "module": "__init__", "function": "__getattr__", "line": 449, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:31.840186", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:31.840822", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:31.841039", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:31.841283", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:31.918513", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.config", "message": "\ud83d\udd27 Configuration loaded from environment", "module": "config", "function": "__init__", "line": 80, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:31.918953", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.config", "message": "\u2705 Configuration validation passed", "module": "config", "function": "__init__", "line": 84, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:31.919502", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\ud83d\udd27 Attempting Redis connection to redis:6379", "module": "ai_cache", "function": "__init__", "line": 52, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:31.924710", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\u2705 Redis cache connected successfully", "module": "ai_cache", "function": "__init__", "line": 64, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:33.386631", "level": "INFO", "logger": "src.shared.data_providers.aggregator", "message": "Initialized data provider: alpha_vantage", "module": "aggregator", "function": "_initialize_providers", "line": 66, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:33.387139", "level": "INFO", "logger": "src.shared.data_providers.aggregator", "message": "Initialized data provider: yfinance", "module": "aggregator", "function": "_initialize_providers", "line": 66, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:33.395158", "level": "INFO", "logger": "src.data.cache.manager", "message": "\ud83d\uddc4\ufe0f Cache manager initialized with memory backend", "module": "manager", "function": "__init__", "line": 215, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.191898", "level": "ERROR", "logger": "src.database.supabase_client", "message": "Failed to initialize Supabase client: Invalid API key", "module": "supabase_client", "function": "_create_supabase_client", "line": 85, "process_id": 1, "thread_id": 135570410797952}
collected 69 items

tests/test_advanced_security.py::TestSecurityFeatures::test_input_sanitization PASSED [  1%]
tests/test_advanced_security.py::TestSecurityFeatures::test_role_based_access_control PASSED [  2%]
tests/test_advanced_security.py::TestSecurityFeatures::test_multi_factor_authentication PASSED [  4%]
tests/test_advanced_security.py::TestSecurityFeatures::test_adaptive_rate_limiter PASSED [  5%]
tests/test_advanced_security.py::TestSecurityFeatures::test_role_required_decorator PASSED [  7%]
tests/test_advanced_security.py::test_security_suite PASSED              [  8%]
tests/test_ai_chat_processor.py::test_ai_chat_processor_successful_flow FAILED [ 10%]
tests/test_ai_chat_processor.py::test_ai_chat_processor_data_fetch_failure PASSED [ 11%]
tests/test_ai_chat_processor.py::test_ai_chat_processor_no_ai_client FAILED [ 13%]
tests/test_ai_chat_processor.py::test_ai_chat_processor_json_parsing_error FAILED [ 14%]
tests/test_ai_chat_processor.py::test_processor_function_with_context PASSED [ 15%]
tests/test_ai_chat_processor.py::test_processor_function_no_query FAILED [ 17%]
tests/test_ai_chat_processor.py::test_ai_response_validation FAILED      [ 18%]
tests/test_ai_chat_processor.py::test_contains_placeholders PASSED       [ 20%]
tests/test_ai_chat_processor.py::test_sanitize_json_string PASSED        [ 21%]
tests/test_database_connection.py::test_database_connection FAILED       [ 23%]
tests/test_database_connection.py::test_supabase_connection PASSED       [ 24%]
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_success PASSED [ 26%]
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_empty_query FAILED [ 27%]
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_error FAILED [ 28%]
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_timeout FAILED [ 30%]
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_no_ai_config PASSED [ 31%]
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_multiple_symbols PASSED [ 33%]
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_no_data_needed PASSED [ 34%]
tests/test_multi_symbol_integration.py::test_multi_symbol_market_overview FAILED [ 36%]
tests/test_multi_symbol_integration.py::test_tech_sector_analysis FAILED [ 37%]
tests/test_multi_symbol_integration.py::test_response_contains_required_elements FAILED [ 39%]
tests/test_multi_symbol_integration.py::test_discord_compatible_response_length FAILED [ 40%]
tests/test_recommendation_engine.py::test_recommendation_engine_initialization PASSED [ 42%]
tests/test_recommendation_engine.py::test_recommendation_engine_with_custom_weights PASSED [ 43%]
tests/test_recommendation_engine.py::test_generate_recommendation_buy_signal PASSED [ 44%]
tests/test_recommendation_engine.py::test_generate_recommendation_sell_signal PASSED [ 46%]
tests/test_recommendation_engine.py::test_generate_recommendation_hold_signal PASSED [ 47%]
tests/test_recommendation_engine.py::test_dynamic_weighting PASSED       [ 49%]
tests/test_recommendation_engine.py::test_sentiment_scoring PASSED       [ 50%]
tests/test_recommendation_engine.py::test_ml_prediction_adjustment PASSED [ 52%]
tests/test_recommendation_engine.py::test_news_sentiment_integration PASSED [ 53%]
tests/test_recommendation_engine.py::test_error_handling PASSED          [ 55%]
tests/test_supabase_connection.py::test_supabase_connection <- src/database/supabase_client.py PASSED [ 56%]
tests/test_supabase_connection.py::test_supabase_client_initialization FAILED [ 57%]
tests/test_supabase_connection.py::test_supabase_connection_status FAILED [ 59%]
tests/test_supabase_connection.py::test_supabase_insert_and_query FAILED [ 60%]
tests/test_supabase_connection.py::test_supabase_update FAILED           [ 62%]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_get_current_price ERROR [ 63%]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_get_historical_data ERROR [ 65%]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_invalid_symbol ERROR [ 66%]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[1] ERROR [ 68%]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[7] ERROR [ 69%]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[30] ERROR [ 71%]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[90] ERROR [ 72%]
tests/integration/test_market_data_service.py::TestMarketDataService::test_get_current_price PASSED [ 73%]
tests/integration/test_market_data_service.py::TestMarketDataService::test_get_historical_data FAILED [ 75%]
tests/integration/test_market_data_service.py::TestMarketDataService::test_invalid_symbol FAILED [ 76%]
tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[1] FAILED [ 78%]
tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[7] FAILED [ 79%]
tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[30] FAILED [ 81%]
tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[90] FAILED [ 82%]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_get_current_price FAILED [ 84%]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_get_historical_data FAILED [ 85%]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_invalid_symbol FAILED [ 86%]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[1] FAILED [ 88%]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[7] FAILED [ 89%]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[30] FAILED [ 91%]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[90] FAILED [ 92%]
tests/integration/test_supabase_integration.py::test_supabase_connection <- src/database/supabase_client.py PASSED [ 94%]
tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_client_initialization FAILED [ 95%]
tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_connection_status FAILED [ 97%]
tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_crud_operations FAILED [ 98%]
tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_error_handling FAILED [100%]/usr/local/lib/python3.10/site-packages/coverage/parser.py:432: RuntimeWarning: coroutine 'PolygonProvider._get_historical_data_async' was never awaited
  self.code = compile(text, filename, "exec", dont_inherit=True)
RuntimeWarning: Enable tracemalloc to get the object allocation traceback


==================================== ERRORS ====================================
______ ERROR at setup of TestAlphaVantageProvider.test_get_current_price _______
tests/integration/test_alpha_vantage_provider.py:20: in alpha_vantage_provider
    return AlphaVantageProvider(api_key=api_key)
E   TypeError: AlphaVantageProvider.__init__() got an unexpected keyword argument 'api_key'
_____ ERROR at setup of TestAlphaVantageProvider.test_get_historical_data ______
tests/integration/test_alpha_vantage_provider.py:20: in alpha_vantage_provider
    return AlphaVantageProvider(api_key=api_key)
E   TypeError: AlphaVantageProvider.__init__() got an unexpected keyword argument 'api_key'
________ ERROR at setup of TestAlphaVantageProvider.test_invalid_symbol ________
tests/integration/test_alpha_vantage_provider.py:20: in alpha_vantage_provider
    return AlphaVantageProvider(api_key=api_key)
E   TypeError: AlphaVantageProvider.__init__() got an unexpected keyword argument 'api_key'
_ ERROR at setup of TestAlphaVantageProvider.test_historical_data_with_different_periods[1] _
tests/integration/test_alpha_vantage_provider.py:20: in alpha_vantage_provider
    return AlphaVantageProvider(api_key=api_key)
E   TypeError: AlphaVantageProvider.__init__() got an unexpected keyword argument 'api_key'
_ ERROR at setup of TestAlphaVantageProvider.test_historical_data_with_different_periods[7] _
tests/integration/test_alpha_vantage_provider.py:20: in alpha_vantage_provider
    return AlphaVantageProvider(api_key=api_key)
E   TypeError: AlphaVantageProvider.__init__() got an unexpected keyword argument 'api_key'
_ ERROR at setup of TestAlphaVantageProvider.test_historical_data_with_different_periods[30] _
tests/integration/test_alpha_vantage_provider.py:20: in alpha_vantage_provider
    return AlphaVantageProvider(api_key=api_key)
E   TypeError: AlphaVantageProvider.__init__() got an unexpected keyword argument 'api_key'
_ ERROR at setup of TestAlphaVantageProvider.test_historical_data_with_different_periods[90] _
tests/integration/test_alpha_vantage_provider.py:20: in alpha_vantage_provider
    return AlphaVantageProvider(api_key=api_key)
E   TypeError: AlphaVantageProvider.__init__() got an unexpected keyword argument 'api_key'
=================================== FAILURES ===================================
____________________ test_ai_chat_processor_successful_flow ____________________
tests/test_ai_chat_processor.py:95: in test_ai_chat_processor_successful_flow
    assert 'needs_data' in result
E   AssertionError: assert 'needs_data' in {'data': {}, 'intent': 'price_check', 'response': 'Analyzing AAPL stock data...', 'symbols': ['AAPL'], ...}
---------------------------- Captured stderr setup -----------------------------
{"timestamp": "2025-08-24T17:39:34.348625", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] AIChatProcessor.__init__ starting for pipeline: test-pipeline-123", "module": "ai_chat_processor", "function": "__init__", "line": 370, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.349349", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 379, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.349712", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 OpenRouter API Key: *****", "module": "ai_chat_processor", "function": "__init__", "line": 385, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.349915", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83c\udf10 OpenRouter Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 386, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.350124", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "OpenRouter configuration loaded", "module": "ai_chat_processor", "function": "__init__", "line": 389, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.350309", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 Using API key (format check disabled)", "module": "ai_chat_processor", "function": "__init__", "line": 398, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.350510", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd27 Configuring OpenAI client with:", "module": "ai_chat_processor", "function": "__init__", "line": 401, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.350699", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   API Key: *****8bdf3", "module": "ai_chat_processor", "function": "__init__", "line": 402, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.350886", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 403, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.351072", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 404, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.351351", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 OpenRouter client configured successfully", "module": "ai_chat_processor", "function": "__init__", "line": 430, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.351559", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [test-pipeline-123] OpenRouter client configured with model: moonshotai/kimi-k2:free", "module": "ai_chat_processor", "function": "__init__", "line": 432, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.351900", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.352129", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.352391", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.352584", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log setup ------------------------------
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:370 🔍 [DIAG] AIChatProcessor.__init__ starting for pipeline: test-pipeline-123
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:379 🔍 [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:385 🔑 OpenRouter API Key: *****
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:386 🌐 OpenRouter Base URL: https://openrouter.ai/api/v1
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:389 OpenRouter configuration loaded
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:398 🔑 Using API key (format check disabled)
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:401 🔧 Configuring OpenAI client with:
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:402    API Key: *****8bdf3
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:403    Base URL: https://openrouter.ai/api/v1
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:404    Timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:430 ✅ OpenRouter client configured successfully
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:432 ✅ [test-pipeline-123] OpenRouter client configured with model: moonshotai/kimi-k2:free
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:630 ✅ Yahoo Finance provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:636 ✅ Polygon provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:644 ✅ Finnhub provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:650 ✅ Initialized 3 data providers
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:39:34.383951", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83e\udd16 [test-pipeline-123] Calling AI model moonshotai/kimi-k2:free with query: What is the price of AAPL?...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 851, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.387146", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udcca [test-pipeline-123] Token usage: {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 890, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.388678", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [test-pipeline-123] Raw AI response content: {\"intent\": \"price_check\", \"symbols\": [\"AAPL\"], \"needs_data\": true, \"response\": \"Analyzing AAPL stock data...\"}...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 903, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.389088", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [test-pipeline-123] Full raw AI response: {\"intent\": \"price_check\", \"symbols\": [\"AAPL\"], \"needs_data\": true, \"response\": \"Analyzing AAPL stock data...\"}", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 904, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.394852", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [test-pipeline-123] AI call successful. Intent: price_check, Symbols: ['AAPL']", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1025, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.396716", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\ud83d\udcbe Cached response for query: What is the price of AAPL?... (TTL: 300s)", "module": "ai_cache", "function": "cache_response", "line": 168, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:851 🤖 [test-pipeline-123] Calling AI model moonshotai/kimi-k2:free with query: What is the price of AAPL?...
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:890 📊 [test-pipeline-123] Token usage: {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:903 🔍 [test-pipeline-123] Raw AI response content: {"intent": "price_check", "symbols": ["AAPL"], "needs_data": true, "response": "Analyzing AAPL stock data..."}...
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:904 🔍 [test-pipeline-123] Full raw AI response: {"intent": "price_check", "symbols": ["AAPL"], "needs_data": true, "response": "Analyzing AAPL stock data..."}
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1025 ✅ [test-pipeline-123] AI call successful. Intent: price_check, Symbols: ['AAPL']
INFO     src.bot.pipeline.commands.ask.stages.ai_cache:ai_cache.py:168 💾 Cached response for query: What is the price of AAPL?... (TTL: 300s)
_____________________ test_ai_chat_processor_no_ai_client ______________________
tests/test_ai_chat_processor.py:126: in test_ai_chat_processor_no_ai_client
    result = await ai_chat_processor.process("Test query")
src/bot/pipeline/commands/ask/stages/ai_chat_processor.py:513: in process
    result = await self._call_ai_model(query)
src/bot/pipeline/commands/ask/stages/ai_chat_processor.py:1048: in _call_ai_model
    return AIAskResult(
E   pydantic_core._pydantic_core.ValidationError: 1 validation error for AIAskResult
E   response
E     Input should be a valid string [type=string_type, input_value={'intent': 'educational',...ou can afford to lose.'}, input_type=dict]
E       For further information visit https://errors.pydantic.dev/2.5/v/string_type
---------------------------- Captured stderr setup -----------------------------
{"timestamp": "2025-08-24T17:39:34.436467", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] AIChatProcessor.__init__ starting for pipeline: test-pipeline-123", "module": "ai_chat_processor", "function": "__init__", "line": 370, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.437245", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 379, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.437703", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 OpenRouter API Key: *****", "module": "ai_chat_processor", "function": "__init__", "line": 385, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.437945", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83c\udf10 OpenRouter Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 386, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.438163", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "OpenRouter configuration loaded", "module": "ai_chat_processor", "function": "__init__", "line": 389, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.438319", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 Using API key (format check disabled)", "module": "ai_chat_processor", "function": "__init__", "line": 398, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.438505", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd27 Configuring OpenAI client with:", "module": "ai_chat_processor", "function": "__init__", "line": 401, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.438658", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   API Key: *****8bdf3", "module": "ai_chat_processor", "function": "__init__", "line": 402, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.438838", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 403, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.439009", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 404, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.439268", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 OpenRouter client configured successfully", "module": "ai_chat_processor", "function": "__init__", "line": 430, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.439437", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [test-pipeline-123] OpenRouter client configured with model: moonshotai/kimi-k2:free", "module": "ai_chat_processor", "function": "__init__", "line": 432, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.439765", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.439949", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.440130", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.440287", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log setup ------------------------------
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:370 🔍 [DIAG] AIChatProcessor.__init__ starting for pipeline: test-pipeline-123
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:379 🔍 [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:385 🔑 OpenRouter API Key: *****
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:386 🌐 OpenRouter Base URL: https://openrouter.ai/api/v1
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:389 OpenRouter configuration loaded
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:398 🔑 Using API key (format check disabled)
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:401 🔧 Configuring OpenAI client with:
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:402    API Key: *****8bdf3
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:403    Base URL: https://openrouter.ai/api/v1
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:404    Timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:430 ✅ OpenRouter client configured successfully
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:432 ✅ [test-pipeline-123] OpenRouter client configured with model: moonshotai/kimi-k2:free
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:630 ✅ Yahoo Finance provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:636 ✅ Polygon provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:644 ✅ Finnhub provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:650 ✅ Initialized 3 data providers
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:39:34.444736", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "[test-pipeline-123] AI client is not configured (missing API key). Returning fallback response.", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 838, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:34.445674", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "[test-pipeline-123] AI call attempt 1 failed: 1 validation error for AIAskResult\nresponse\n  Input should be a valid string [type=string_type, input_value={'intent': 'educational',... not financial advice.'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/string_type", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1030, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:36.447852", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "[test-pipeline-123] AI client is not configured (missing API key). Returning fallback response.", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 838, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:36.448670", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "[test-pipeline-123] AI call attempt 2 failed: 1 validation error for AIAskResult\nresponse\n  Input should be a valid string [type=string_type, input_value={'intent': 'educational',... not financial advice.'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/string_type", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1030, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:40.452765", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "[test-pipeline-123] AI client is not configured (missing API key). Returning fallback response.", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 838, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:40.454083", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "[test-pipeline-123] AI call attempt 3 failed: 1 validation error for AIAskResult\nresponse\n  Input should be a valid string [type=string_type, input_value={'intent': 'educational',... not financial advice.'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/string_type", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1030, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.462974", "level": "ERROR", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u274c [test-pipeline-123] AI call failed after 3 attempts. Last error: 1 validation error for AIAskResult\nresponse\n  Input should be a valid string [type=string_type, input_value={'intent': 'educational',... not financial advice.'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.5/v/string_type", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1037, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:838 [test-pipeline-123] AI client is not configured (missing API key). Returning fallback response.
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1030 [test-pipeline-123] AI call attempt 1 failed: 1 validation error for AIAskResult
response
  Input should be a valid string [type=string_type, input_value={'intent': 'educational',... not financial advice.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:838 [test-pipeline-123] AI client is not configured (missing API key). Returning fallback response.
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1030 [test-pipeline-123] AI call attempt 2 failed: 1 validation error for AIAskResult
response
  Input should be a valid string [type=string_type, input_value={'intent': 'educational',... not financial advice.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:838 [test-pipeline-123] AI client is not configured (missing API key). Returning fallback response.
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1030 [test-pipeline-123] AI call attempt 3 failed: 1 validation error for AIAskResult
response
  Input should be a valid string [type=string_type, input_value={'intent': 'educational',... not financial advice.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
ERROR    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1037 ❌ [test-pipeline-123] AI call failed after 3 attempts. Last error: 1 validation error for AIAskResult
response
  Input should be a valid string [type=string_type, input_value={'intent': 'educational',... not financial advice.'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
__________________ test_ai_chat_processor_json_parsing_error ___________________
tests/test_ai_chat_processor.py:144: in test_ai_chat_processor_json_parsing_error
    result = await ai_chat_processor.process("Test query")
src/bot/pipeline/commands/ask/stages/ai_chat_processor.py:513: in process
    result = await self._call_ai_model(query)
src/bot/pipeline/commands/ask/stages/ai_chat_processor.py:1048: in _call_ai_model
    return AIAskResult(
E   pydantic_core._pydantic_core.ValidationError: 1 validation error for AIAskResult
E   response
E     Input should be a valid string [type=string_type, input_value={'intent': 'educational',...ou can afford to lose.'}, input_type=dict]
E       For further information visit https://errors.pydantic.dev/2.5/v/string_type
---------------------------- Captured stderr setup -----------------------------
{"timestamp": "2025-08-24T17:39:48.770170", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] AIChatProcessor.__init__ starting for pipeline: test-pipeline-123", "module": "ai_chat_processor", "function": "__init__", "line": 370, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.771199", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 379, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.771489", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 OpenRouter API Key: *****", "module": "ai_chat_processor", "function": "__init__", "line": 385, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.771722", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83c\udf10 OpenRouter Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 386, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.771959", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "OpenRouter configuration loaded", "module": "ai_chat_processor", "function": "__init__", "line": 389, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.772187", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 Using API key (format check disabled)", "module": "ai_chat_processor", "function": "__init__", "line": 398, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.772426", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd27 Configuring OpenAI client with:", "module": "ai_chat_processor", "function": "__init__", "line": 401, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.772672", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   API Key: *****8bdf3", "module": "ai_chat_processor", "function": "__init__", "line": 402, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.772899", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 403, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.773087", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 404, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.773428", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 OpenRouter client configured successfully", "module": "ai_chat_processor", "function": "__init__", "line": 430, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.773685", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [test-pipeline-123] OpenRouter client configured with model: moonshotai/kimi-k2:free", "module": "ai_chat_processor", "function": "__init__", "line": 432, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.774203", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.774477", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.774698", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.774886", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log setup ------------------------------
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:370 🔍 [DIAG] AIChatProcessor.__init__ starting for pipeline: test-pipeline-123
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:379 🔍 [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:385 🔑 OpenRouter API Key: *****
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:386 🌐 OpenRouter Base URL: https://openrouter.ai/api/v1
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:389 OpenRouter configuration loaded
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:398 🔑 Using API key (format check disabled)
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:401 🔧 Configuring OpenAI client with:
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:402    API Key: *****8bdf3
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:403    Base URL: https://openrouter.ai/api/v1
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:404    Timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:430 ✅ OpenRouter client configured successfully
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:432 ✅ [test-pipeline-123] OpenRouter client configured with model: moonshotai/kimi-k2:free
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:630 ✅ Yahoo Finance provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:636 ✅ Polygon provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:644 ✅ Finnhub provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:650 ✅ Initialized 3 data providers
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:39:48.781218", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83e\udd16 [test-pipeline-123] Calling AI model moonshotai/kimi-k2:free with query: Test query...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 851, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.785734", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udcca [test-pipeline-123] Token usage: {'prompt_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.prompt_tokens' id='135569932356816'>, 'completion_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.completion_tokens' id='135569932364256'>, 'total_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.total_tokens' id='135569932297424'>}", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 890, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.786996", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [test-pipeline-123] Raw AI response content: Invalid JSON response...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 903, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.787346", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [test-pipeline-123] Full raw AI response: Invalid JSON response", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 904, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.788236", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u26a0\ufe0f [test-pipeline-123] Received empty response from AI model", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1009, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:48.788492", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "[test-pipeline-123] AI call attempt 1 failed: Empty response from AI model", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1030, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:50.791697", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83e\udd16 [test-pipeline-123] Calling AI model moonshotai/kimi-k2:free with query: Test query...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 851, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:50.794685", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udcca [test-pipeline-123] Token usage: {'prompt_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.prompt_tokens' id='135569932356816'>, 'completion_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.completion_tokens' id='135569932364256'>, 'total_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.total_tokens' id='135569932297424'>}", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 890, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:50.795584", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [test-pipeline-123] Raw AI response content: Invalid JSON response...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 903, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:50.795951", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [test-pipeline-123] Full raw AI response: Invalid JSON response", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 904, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:50.796573", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u26a0\ufe0f [test-pipeline-123] Received empty response from AI model", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1009, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:50.796894", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "[test-pipeline-123] AI call attempt 2 failed: Empty response from AI model", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1030, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:54.801306", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83e\udd16 [test-pipeline-123] Calling AI model moonshotai/kimi-k2:free with query: Test query...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 851, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:54.803510", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udcca [test-pipeline-123] Token usage: {'prompt_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.prompt_tokens' id='135569932356816'>, 'completion_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.completion_tokens' id='135569932364256'>, 'total_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.total_tokens' id='135569932297424'>}", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 890, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:54.804023", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [test-pipeline-123] Raw AI response content: Invalid JSON response...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 903, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:54.804240", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [test-pipeline-123] Full raw AI response: Invalid JSON response", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 904, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:54.804687", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u26a0\ufe0f [test-pipeline-123] Received empty response from AI model", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1009, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:39:54.805111", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "[test-pipeline-123] AI call attempt 3 failed: Empty response from AI model", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1030, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:02.813861", "level": "ERROR", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u274c [test-pipeline-123] AI call failed after 3 attempts. Last error: Empty response from AI model", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1037, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:851 🤖 [test-pipeline-123] Calling AI model moonshotai/kimi-k2:free with query: Test query...
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:890 📊 [test-pipeline-123] Token usage: {'prompt_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.prompt_tokens' id='135569932356816'>, 'completion_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.completion_tokens' id='135569932364256'>, 'total_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.total_tokens' id='135569932297424'>}
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:903 🔍 [test-pipeline-123] Raw AI response content: Invalid JSON response...
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:904 🔍 [test-pipeline-123] Full raw AI response: Invalid JSON response
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1009 ⚠️ [test-pipeline-123] Received empty response from AI model
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1030 [test-pipeline-123] AI call attempt 1 failed: Empty response from AI model
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:851 🤖 [test-pipeline-123] Calling AI model moonshotai/kimi-k2:free with query: Test query...
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:890 📊 [test-pipeline-123] Token usage: {'prompt_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.prompt_tokens' id='135569932356816'>, 'completion_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.completion_tokens' id='135569932364256'>, 'total_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.total_tokens' id='135569932297424'>}
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:903 🔍 [test-pipeline-123] Raw AI response content: Invalid JSON response...
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:904 🔍 [test-pipeline-123] Full raw AI response: Invalid JSON response
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1009 ⚠️ [test-pipeline-123] Received empty response from AI model
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1030 [test-pipeline-123] AI call attempt 2 failed: Empty response from AI model
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:851 🤖 [test-pipeline-123] Calling AI model moonshotai/kimi-k2:free with query: Test query...
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:890 📊 [test-pipeline-123] Token usage: {'prompt_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.prompt_tokens' id='135569932356816'>, 'completion_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.completion_tokens' id='135569932364256'>, 'total_tokens': <MagicMock name='OpenAI().chat.completions.create().usage.total_tokens' id='135569932297424'>}
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:903 🔍 [test-pipeline-123] Raw AI response content: Invalid JSON response...
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:904 🔍 [test-pipeline-123] Full raw AI response: Invalid JSON response
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1009 ⚠️ [test-pipeline-123] Received empty response from AI model
WARNING  src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1030 [test-pipeline-123] AI call attempt 3 failed: Empty response from AI model
ERROR    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:1037 ❌ [test-pipeline-123] AI call failed after 3 attempts. Last error: Empty response from AI model
_______________________ test_processor_function_no_query _______________________
src/core/response_generator.py:100: in generate_response
    if processing_time == 0.0:
E   UnboundLocalError: local variable 'processing_time' referenced before assignment

During handling of the above exception, another exception occurred:
tests/test_ai_chat_processor.py:179: in test_processor_function_no_query
    result = await processor(mock_context, results)
src/bot/pipeline/commands/ask/stages/ai_chat_processor.py:1712: in processor
    fallback_response = response_gen.generate_response(
src/core/response_generator.py:142: in generate_response
    self.logger.error(
/usr/local/lib/python3.10/logging/__init__.py:1506: in error
    self._log(ERROR, msg, args, **kwargs)
/usr/local/lib/python3.10/logging/__init__.py:1624: in _log
    self.handle(record)
/usr/local/lib/python3.10/logging/__init__.py:1634: in handle
    self.callHandlers(record)
/usr/local/lib/python3.10/logging/__init__.py:1696: in callHandlers
    hdlr.handle(record)
/usr/local/lib/python3.10/logging/__init__.py:968: in handle
    self.emit(record)
/usr/local/lib/python3.10/site-packages/_pytest/logging.py:372: in emit
    super().emit(record)
/usr/local/lib/python3.10/logging/__init__.py:1108: in emit
    self.handleError(record)
/usr/local/lib/python3.10/logging/__init__.py:1100: in emit
    msg = self.format(record)
/usr/local/lib/python3.10/logging/__init__.py:943: in format
    return fmt.format(record)
/usr/local/lib/python3.10/site-packages/_pytest/logging.py:136: in format
    return super().format(record)
/usr/local/lib/python3.10/logging/__init__.py:678: in format
    record.message = record.getMessage()
/usr/local/lib/python3.10/logging/__init__.py:368: in getMessage
    msg = msg % self.args
E   TypeError: not all arguments converted during string formatting
----------------------------- Captured stderr call -----------------------------
--- Logging error ---
Traceback (most recent call last):
  File "/app/src/core/response_generator.py", line 100, in generate_response
    if processing_time == 0.0:
UnboundLocalError: local variable 'processing_time' referenced before assignment

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/logging/handlers.py", line 73, in emit
    if self.shouldRollover(record):
  File "/usr/local/lib/python3.10/logging/handlers.py", line 196, in shouldRollover
    msg = "%s\n" % self.format(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/app/src/shared/error_handling/logging.py", line 39, in format
    'message': record.getMessage(),
  File "/usr/local/lib/python3.10/logging/__init__.py", line 368, in getMessage
    msg = msg % self.args
TypeError: not all arguments converted during string formatting
Call stack:
  File "/usr/local/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.10/site-packages/pytest/__main__.py", line 5, in <module>
    raise SystemExit(pytest.console_main())
  File "/usr/local/lib/python3.10/site-packages/_pytest/config/__init__.py", line 192, in console_main
    code = main()
  File "/usr/local/lib/python3.10/site-packages/_pytest/config/__init__.py", line 169, in main
    ret: Union[ExitCode, int] = config.hook.pytest_cmdline_main(
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 318, in pytest_cmdline_main
    return wrap_session(config, _main)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 271, in wrap_session
    session.exitstatus = doit(config, session) or 0
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 325, in _main
    config.hook.pytest_runtestloop(session=session)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 350, in pytest_runtestloop
    item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 114, in pytest_runtest_protocol
    runtestprotocol(item, nextitem=nextitem)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 133, in runtestprotocol
    reports.append(call_and_report(item, "call", log))
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 222, in call_and_report
    call = call_runtest_hook(item, when, **kwds)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 261, in call_runtest_hook
    return CallInfo.from_call(
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 341, in from_call
    result: Optional[TResult] = func()
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 262, in <lambda>
    lambda: ihook(item=item, **kwds), when=when, reraise=reraise
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 169, in pytest_runtest_call
    item.runtest()
  File "/usr/local/lib/python3.10/site-packages/pytest_asyncio/plugin.py", line 431, in runtest
    super().runtest()
  File "/usr/local/lib/python3.10/site-packages/_pytest/python.py", line 1792, in runtest
    self.ihook.pytest_pyfunc_call(pyfuncitem=self)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/python.py", line 194, in pytest_pyfunc_call
    result = testfunction(**testargs)
  File "/usr/local/lib/python3.10/site-packages/pytest_asyncio/plugin.py", line 879, in inner
    _loop.run_until_complete(task)
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/local/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/app/tests/test_ai_chat_processor.py", line 179, in test_processor_function_no_query
    result = await processor(mock_context, results)
  File "/app/src/bot/pipeline/commands/ask/stages/ai_chat_processor.py", line 1712, in processor
    fallback_response = response_gen.generate_response(
  File "/app/src/core/response_generator.py", line 142, in generate_response
    self.logger.error(
Message: 'Error during response generation'
Arguments: ({'intent': 'no_query', 'symbols': [], 'response_type': 'fallback'}, UnboundLocalError("local variable 'processing_time' referenced before assignment"))
--- Logging error ---
Traceback (most recent call last):
  File "/app/src/core/response_generator.py", line 100, in generate_response
    if processing_time == 0.0:
UnboundLocalError: local variable 'processing_time' referenced before assignment

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/logging/__init__.py", line 1100, in emit
    msg = self.format(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/app/src/shared/error_handling/logging.py", line 39, in format
    'message': record.getMessage(),
  File "/usr/local/lib/python3.10/logging/__init__.py", line 368, in getMessage
    msg = msg % self.args
TypeError: not all arguments converted during string formatting
Call stack:
  File "/usr/local/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.10/site-packages/pytest/__main__.py", line 5, in <module>
    raise SystemExit(pytest.console_main())
  File "/usr/local/lib/python3.10/site-packages/_pytest/config/__init__.py", line 192, in console_main
    code = main()
  File "/usr/local/lib/python3.10/site-packages/_pytest/config/__init__.py", line 169, in main
    ret: Union[ExitCode, int] = config.hook.pytest_cmdline_main(
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 318, in pytest_cmdline_main
    return wrap_session(config, _main)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 271, in wrap_session
    session.exitstatus = doit(config, session) or 0
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 325, in _main
    config.hook.pytest_runtestloop(session=session)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 350, in pytest_runtestloop
    item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 114, in pytest_runtest_protocol
    runtestprotocol(item, nextitem=nextitem)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 133, in runtestprotocol
    reports.append(call_and_report(item, "call", log))
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 222, in call_and_report
    call = call_runtest_hook(item, when, **kwds)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 261, in call_runtest_hook
    return CallInfo.from_call(
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 341, in from_call
    result: Optional[TResult] = func()
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 262, in <lambda>
    lambda: ihook(item=item, **kwds), when=when, reraise=reraise
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 169, in pytest_runtest_call
    item.runtest()
  File "/usr/local/lib/python3.10/site-packages/pytest_asyncio/plugin.py", line 431, in runtest
    super().runtest()
  File "/usr/local/lib/python3.10/site-packages/_pytest/python.py", line 1792, in runtest
    self.ihook.pytest_pyfunc_call(pyfuncitem=self)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/python.py", line 194, in pytest_pyfunc_call
    result = testfunction(**testargs)
  File "/usr/local/lib/python3.10/site-packages/pytest_asyncio/plugin.py", line 879, in inner
    _loop.run_until_complete(task)
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/local/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/app/tests/test_ai_chat_processor.py", line 179, in test_processor_function_no_query
    result = await processor(mock_context, results)
  File "/app/src/bot/pipeline/commands/ask/stages/ai_chat_processor.py", line 1712, in processor
    fallback_response = response_gen.generate_response(
  File "/app/src/core/response_generator.py", line 142, in generate_response
    self.logger.error(
Message: 'Error during response generation'
Arguments: ({'intent': 'no_query', 'symbols': [], 'response_type': 'fallback'}, UnboundLocalError("local variable 'processing_time' referenced before assignment"))
_________________________ test_ai_response_validation __________________________
tests/test_ai_chat_processor.py:199: in test_ai_response_validation
    assert result.needs_data == True
/usr/local/lib/python3.10/site-packages/pydantic/main.py:761: in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
E   AttributeError: 'AIAskResult' object has no attribute 'needs_data'
___________________________ test_database_connection ___________________________
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py:145: in __init__
    self._dbapi_connection = engine.raw_connection()
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py:3292: in raw_connection
    return self.pool.connect()
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:452: in connect
    return _ConnectionFairy._checkout(self)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:1269: in _checkout
    fairy = _ConnectionRecord.checkout(pool)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:716: in checkout
    rec = pool._do_get()
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/impl.py:169: in _do_get
    with util.safe_reraise():
/usr/local/lib/python3.10/site-packages/sqlalchemy/util/langhelpers.py:146: in __exit__
    raise exc_value.with_traceback(exc_tb)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/impl.py:167: in _do_get
    return self._create_connection()
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:393: in _create_connection
    return _ConnectionRecord(self)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:678: in __init__
    self.__connect()
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:902: in __connect
    with util.safe_reraise():
/usr/local/lib/python3.10/site-packages/sqlalchemy/util/langhelpers.py:146: in __exit__
    raise exc_value.with_traceback(exc_tb)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:898: in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/create.py:637: in connect
    return dialect.connect(*cargs, **cparams)
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/default.py:616: in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
/usr/local/lib/python3.10/site-packages/psycopg2/__init__.py:122: in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E   psycopg2.OperationalError: connection to server at "db.sgxjackuhalscowqrulv.supabase.co" (2600:1f16:1cd0:330c:76b1:9049:9d7f:2768), port 5432 failed: Network is unreachable
E   	Is the server running on that host and accepting TCP/IP connections?

The above exception was the direct cause of the following exception:
tests/test_database_connection.py:11: in test_database_connection
    result = database_connection.execute(text("SELECT 1")).scalar()
/usr/local/lib/python3.10/site-packages/sqlalchemy/orm/session.py:2308: in execute
    return self._execute_internal(
/usr/local/lib/python3.10/site-packages/sqlalchemy/orm/session.py:2180: in _execute_internal
    conn = self._connection_for_bind(bind)
/usr/local/lib/python3.10/site-packages/sqlalchemy/orm/session.py:2047: in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
<string>:2: in _connection_for_bind
    ???
/usr/local/lib/python3.10/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
/usr/local/lib/python3.10/site-packages/sqlalchemy/orm/session.py:1143: in _connection_for_bind
    conn = bind.connect()
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py:3268: in connect
    return self._connection_cls(self)
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py:147: in __init__
    Connection._handle_dbapi_exception_noconnection(
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py:2430: in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py:145: in __init__
    self._dbapi_connection = engine.raw_connection()
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py:3292: in raw_connection
    return self.pool.connect()
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:452: in connect
    return _ConnectionFairy._checkout(self)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:1269: in _checkout
    fairy = _ConnectionRecord.checkout(pool)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:716: in checkout
    rec = pool._do_get()
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/impl.py:169: in _do_get
    with util.safe_reraise():
/usr/local/lib/python3.10/site-packages/sqlalchemy/util/langhelpers.py:146: in __exit__
    raise exc_value.with_traceback(exc_tb)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/impl.py:167: in _do_get
    return self._create_connection()
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:393: in _create_connection
    return _ConnectionRecord(self)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:678: in __init__
    self.__connect()
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:902: in __connect
    with util.safe_reraise():
/usr/local/lib/python3.10/site-packages/sqlalchemy/util/langhelpers.py:146: in __exit__
    raise exc_value.with_traceback(exc_tb)
/usr/local/lib/python3.10/site-packages/sqlalchemy/pool/base.py:898: in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/create.py:637: in connect
    return dialect.connect(*cargs, **cparams)
/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/default.py:616: in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
/usr/local/lib/python3.10/site-packages/psycopg2/__init__.py:122: in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E   sqlalchemy.exc.OperationalError: (psycopg2.OperationalError) connection to server at "db.sgxjackuhalscowqrulv.supabase.co" (2600:1f16:1cd0:330c:76b1:9049:9d7f:2768), port 5432 failed: Network is unreachable
E   	Is the server running on that host and accepting TCP/IP connections?
E   
E   (Background on this error at: https://sqlalche.me/e/20/e3q8)
____________________ test_execute_ask_pipeline_empty_query _____________________
tests/test_execute_ask_pipeline.py:81: in test_execute_ask_pipeline_empty_query
    assert context.status == PipelineStatus.COMPLETED
E   AssertionError: assert <PipelineStatus.FAILED: 'failed'> == <PipelineStatus.COMPLETED: 'completed'>
E    +  where <PipelineStatus.FAILED: 'failed'> = PipelineContext(pipeline_id='43ddda80-549b-4eec-8863-065dd1061cc6', command_name='ask', user_id='test_user', guild_id='test_guild', original_query='', query_metadata={}, query_complexity=1, query_type='', collected_data={}, data_sources=[], data_timestamps={}, validated_data={}, quality_scores={}, validation_errors=[], processing_results={}, analysis_results={}, ai_responses={}, ai_confidence=0.0, current_stage='', stage_history=[], status=<PipelineStatus.FAILED: 'failed'>, start_time=datetime.datetime(2025, 8, 24, 17, 40, 5, 373145), end_time=None, stage_timings={}, total_execution_time=0.0, resource_usage={}, audit_trail=[], error_log=[{'error_message': 'not all arguments converted during string formatting', 'error_type': 'TypeError', 'timestamp': '2025-08-24T17:40:05.373200'}], config={}, max_execution_time=30.0, should_continue=True).status
E    +  and   <PipelineStatus.COMPLETED: 'completed'> = PipelineStatus.COMPLETED
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:05.361703", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting Enhanced Ask Pipeline", "module": "pipeline", "function": "execute_ask_pipeline", "line": 190, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.362184", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udcdd Query: ", "module": "pipeline", "function": "execute_ask_pipeline", "line": 191, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.362449", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udc64 User: test_user", "module": "pipeline", "function": "execute_ask_pipeline", "line": 192, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.362680", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfe0 Guild: test_guild", "module": "pipeline", "function": "execute_ask_pipeline", "line": 193, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.362886", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udd94 Pipeline ID: f715315b-b8d5-4197-acb7-9b786c0d968f", "module": "pipeline", "function": "execute_ask_pipeline", "line": 194, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.363147", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd12 Strict Mode: False", "module": "pipeline", "function": "execute_ask_pipeline", "line": 195, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.363602", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing pipeline with 1 sections...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 212, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.363916", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting pipeline execution with 1 sections", "module": "pipeline", "function": "run", "line": 139, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.364169", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing stage: ai_chat_processor", "module": "pipeline", "function": "run", "line": 142, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.364435", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Running stage ai_chat_processor with query: ...", "module": "pipeline", "function": "_run_stage", "line": 154, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.364748", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor input results: {'query': ''}", "module": "pipeline", "function": "_run_stage", "line": 159, "process_id": 1, "thread_id": 135570410797952}
--- Logging error ---
Traceback (most recent call last):
  File "/app/src/core/response_generator.py", line 100, in generate_response
    if processing_time == 0.0:
UnboundLocalError: local variable 'processing_time' referenced before assignment

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/logging/handlers.py", line 73, in emit
    if self.shouldRollover(record):
  File "/usr/local/lib/python3.10/logging/handlers.py", line 196, in shouldRollover
    msg = "%s\n" % self.format(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/app/src/shared/error_handling/logging.py", line 39, in format
    'message': record.getMessage(),
  File "/usr/local/lib/python3.10/logging/__init__.py", line 368, in getMessage
    msg = msg % self.args
TypeError: not all arguments converted during string formatting
Call stack:
  File "/usr/local/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.10/site-packages/pytest/__main__.py", line 5, in <module>
    raise SystemExit(pytest.console_main())
  File "/usr/local/lib/python3.10/site-packages/_pytest/config/__init__.py", line 192, in console_main
    code = main()
  File "/usr/local/lib/python3.10/site-packages/_pytest/config/__init__.py", line 169, in main
    ret: Union[ExitCode, int] = config.hook.pytest_cmdline_main(
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 318, in pytest_cmdline_main
    return wrap_session(config, _main)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 271, in wrap_session
    session.exitstatus = doit(config, session) or 0
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 325, in _main
    config.hook.pytest_runtestloop(session=session)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 350, in pytest_runtestloop
    item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 114, in pytest_runtest_protocol
    runtestprotocol(item, nextitem=nextitem)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 133, in runtestprotocol
    reports.append(call_and_report(item, "call", log))
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 222, in call_and_report
    call = call_runtest_hook(item, when, **kwds)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 261, in call_runtest_hook
    return CallInfo.from_call(
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 341, in from_call
    result: Optional[TResult] = func()
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 262, in <lambda>
    lambda: ihook(item=item, **kwds), when=when, reraise=reraise
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 169, in pytest_runtest_call
    item.runtest()
  File "/usr/local/lib/python3.10/site-packages/pytest_asyncio/plugin.py", line 431, in runtest
    super().runtest()
  File "/usr/local/lib/python3.10/site-packages/_pytest/python.py", line 1792, in runtest
    self.ihook.pytest_pyfunc_call(pyfuncitem=self)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/python.py", line 194, in pytest_pyfunc_call
    result = testfunction(**testargs)
  File "/usr/local/lib/python3.10/site-packages/pytest_asyncio/plugin.py", line 879, in inner
    _loop.run_until_complete(task)
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/local/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/app/tests/test_execute_ask_pipeline.py", line 74, in test_execute_ask_pipeline_empty_query
    context = await execute_ask_pipeline(
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 215, in execute_ask_pipeline
    results = await pipeline.run(query)
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 144, in run
    result = await self._run_stage(stage_name, stage_def, query)
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 161, in _run_stage
    result = await stage_def["processor"](self.context, results)
  File "/app/src/bot/pipeline/commands/ask/stages/ai_chat_processor.py", line 1712, in processor
    fallback_response = response_gen.generate_response(
  File "/app/src/core/response_generator.py", line 142, in generate_response
    self.logger.error(
Message: 'Error during response generation'
Arguments: ({'intent': 'no_query', 'symbols': [], 'response_type': 'fallback'}, UnboundLocalError("local variable 'processing_time' referenced before assignment"))
--- Logging error ---
Traceback (most recent call last):
  File "/app/src/core/response_generator.py", line 100, in generate_response
    if processing_time == 0.0:
UnboundLocalError: local variable 'processing_time' referenced before assignment

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/logging/__init__.py", line 1100, in emit
    msg = self.format(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/app/src/shared/error_handling/logging.py", line 39, in format
    'message': record.getMessage(),
  File "/usr/local/lib/python3.10/logging/__init__.py", line 368, in getMessage
    msg = msg % self.args
TypeError: not all arguments converted during string formatting
Call stack:
  File "/usr/local/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.10/site-packages/pytest/__main__.py", line 5, in <module>
    raise SystemExit(pytest.console_main())
  File "/usr/local/lib/python3.10/site-packages/_pytest/config/__init__.py", line 192, in console_main
    code = main()
  File "/usr/local/lib/python3.10/site-packages/_pytest/config/__init__.py", line 169, in main
    ret: Union[ExitCode, int] = config.hook.pytest_cmdline_main(
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 318, in pytest_cmdline_main
    return wrap_session(config, _main)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 271, in wrap_session
    session.exitstatus = doit(config, session) or 0
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 325, in _main
    config.hook.pytest_runtestloop(session=session)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/main.py", line 350, in pytest_runtestloop
    item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 114, in pytest_runtest_protocol
    runtestprotocol(item, nextitem=nextitem)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 133, in runtestprotocol
    reports.append(call_and_report(item, "call", log))
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 222, in call_and_report
    call = call_runtest_hook(item, when, **kwds)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 261, in call_runtest_hook
    return CallInfo.from_call(
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 341, in from_call
    result: Optional[TResult] = func()
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 262, in <lambda>
    lambda: ihook(item=item, **kwds), when=when, reraise=reraise
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/runner.py", line 169, in pytest_runtest_call
    item.runtest()
  File "/usr/local/lib/python3.10/site-packages/pytest_asyncio/plugin.py", line 431, in runtest
    super().runtest()
  File "/usr/local/lib/python3.10/site-packages/_pytest/python.py", line 1792, in runtest
    self.ihook.pytest_pyfunc_call(pyfuncitem=self)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "/usr/local/lib/python3.10/site-packages/pluggy/_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "/usr/local/lib/python3.10/site-packages/_pytest/python.py", line 194, in pytest_pyfunc_call
    result = testfunction(**testargs)
  File "/usr/local/lib/python3.10/site-packages/pytest_asyncio/plugin.py", line 879, in inner
    _loop.run_until_complete(task)
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/local/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/app/tests/test_execute_ask_pipeline.py", line 74, in test_execute_ask_pipeline_empty_query
    context = await execute_ask_pipeline(
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 215, in execute_ask_pipeline
    results = await pipeline.run(query)
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 144, in run
    result = await self._run_stage(stage_name, stage_def, query)
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 161, in _run_stage
    result = await stage_def["processor"](self.context, results)
  File "/app/src/bot/pipeline/commands/ask/stages/ai_chat_processor.py", line 1712, in processor
    fallback_response = response_gen.generate_response(
  File "/app/src/core/response_generator.py", line 142, in generate_response
    self.logger.error(
Message: 'Error during response generation'
Arguments: ({'intent': 'no_query', 'symbols': [], 'response_type': 'fallback'}, UnboundLocalError("local variable 'processing_time' referenced before assignment"))
{"timestamp": "2025-08-24T17:40:05.371014", "level": "ERROR", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u274c Pipeline execution failed: not all arguments converted during string formatting", "module": "pipeline", "function": "execute_ask_pipeline", "line": 305, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:190 🚀 Starting Enhanced Ask Pipeline
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:191 📝 Query: 
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:192 👤 User: test_user
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:193 🏠 Guild: test_guild
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:194 🆔 Pipeline ID: f715315b-b8d5-4197-acb7-9b786c0d968f
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:195 🔒 Strict Mode: False
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:212 ⚡ Executing pipeline with 1 sections...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:139 🚀 Starting pipeline execution with 1 sections
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:142 ⚡ Executing stage: ai_chat_processor
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:154 🔧 Running stage ai_chat_processor with query: ...
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:159 🔧 Stage ai_chat_processor input results: {'query': ''}
ERROR    src.bot.pipeline.commands.ask.pipeline:pipeline.py:305 ❌ Pipeline execution failed: not all arguments converted during string formatting
Traceback (most recent call last):
  File "/app/src/core/response_generator.py", line 100, in generate_response
    if processing_time == 0.0:
UnboundLocalError: local variable 'processing_time' referenced before assignment

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 215, in execute_ask_pipeline
    results = await pipeline.run(query)
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 144, in run
    result = await self._run_stage(stage_name, stage_def, query)
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 161, in _run_stage
    result = await stage_def["processor"](self.context, results)
  File "/app/src/bot/pipeline/commands/ask/stages/ai_chat_processor.py", line 1712, in processor
    fallback_response = response_gen.generate_response(
  File "/app/src/core/response_generator.py", line 142, in generate_response
    self.logger.error(
  File "/usr/local/lib/python3.10/logging/__init__.py", line 1506, in error
    self._log(ERROR, msg, args, **kwargs)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/usr/local/lib/python3.10/site-packages/_pytest/logging.py", line 372, in emit
    super().emit(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 1100, in emit
    msg = self.format(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/usr/local/lib/python3.10/site-packages/_pytest/logging.py", line 136, in format
    return super().format(record)
  File "/usr/local/lib/python3.10/logging/__init__.py", line 678, in format
    record.message = record.getMessage()
  File "/usr/local/lib/python3.10/logging/__init__.py", line 368, in getMessage
    msg = msg % self.args
TypeError: not all arguments converted during string formatting
_______________________ test_execute_ask_pipeline_error ________________________
tests/test_execute_ask_pipeline.py:101: in test_execute_ask_pipeline_error
    assert 'error' in context.processing_results
E   AssertionError: assert 'error' in {}
E    +  where {} = PipelineContext(pipeline_id='175ec988-2e85-4b96-a4b9-6f433e8411b9', command_name='ask', user_id='test_user', guild_id='test_guild', original_query='What is the price of AAPL?', query_metadata={}, query_complexity=1, query_type='', collected_data={}, data_sources=[], data_timestamps={}, validated_data={}, quality_scores={}, validation_errors=[], processing_results={}, analysis_results={}, ai_responses={}, ai_confidence=0.0, current_stage='', stage_history=[], status=<PipelineStatus.FAILED: 'failed'>, start_time=datetime.datetime(2025, 8, 24, 17, 40, 5, 408327), end_time=None, stage_timings={}, total_execution_time=0.0, resource_usage={}, audit_trail=[], error_log=[{'error_message': 'Pipeline failed', 'error_type': 'Exception', 'timestamp': '2025-08-24T17:40:05.408365'}], config={}, max_execution_time=30.0, should_continue=True).processing_results
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:05.399946", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting Enhanced Ask Pipeline", "module": "pipeline", "function": "execute_ask_pipeline", "line": 190, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.400457", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udcdd Query: What is the price of AAPL?", "module": "pipeline", "function": "execute_ask_pipeline", "line": 191, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.400822", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udc64 User: test_user", "module": "pipeline", "function": "execute_ask_pipeline", "line": 192, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.401131", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfe0 Guild: test_guild", "module": "pipeline", "function": "execute_ask_pipeline", "line": 193, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.401456", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udd94 Pipeline ID: 0dd70427-10b2-4dd9-ba51-4b8a3189c248", "module": "pipeline", "function": "execute_ask_pipeline", "line": 194, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.401801", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd12 Strict Mode: False", "module": "pipeline", "function": "execute_ask_pipeline", "line": 195, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.405140", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing pipeline with 0 sections...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 212, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.406298", "level": "ERROR", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u274c Pipeline execution failed: Pipeline failed", "module": "pipeline", "function": "execute_ask_pipeline", "line": 305, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:190 🚀 Starting Enhanced Ask Pipeline
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:191 📝 Query: What is the price of AAPL?
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:192 👤 User: test_user
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:193 🏠 Guild: test_guild
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:194 🆔 Pipeline ID: 0dd70427-10b2-4dd9-ba51-4b8a3189c248
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:195 🔒 Strict Mode: False
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:212 ⚡ Executing pipeline with 0 sections...
ERROR    src.bot.pipeline.commands.ask.pipeline:pipeline.py:305 ❌ Pipeline execution failed: Pipeline failed
Traceback (most recent call last):
  File "/app/src/bot/pipeline/commands/ask/pipeline.py", line 215, in execute_ask_pipeline
    results = await pipeline.run(query)
  File "/usr/local/lib/python3.10/unittest/mock.py", line 2234, in _execute_mock_call
    raise effect
Exception: Pipeline failed
______________________ test_execute_ask_pipeline_timeout _______________________
tests/test_execute_ask_pipeline.py:125: in test_execute_ask_pipeline_timeout
    assert context.status == PipelineStatus.FAILED
E   assert <PipelineStatus.COMPLETED: 'completed'> == <PipelineStatus.FAILED: 'failed'>
E    +  where <PipelineStatus.COMPLETED: 'completed'> = PipelineContext(pipeline_id='dd9e8a2c-5785-445b-b83e-158ccec8e5d7', command_name='ask', user_id='test_user', guild_id='test_guild', original_query='What is the price of AAPL?', query_metadata={}, query_complexity=1, query_type='', collected_data={}, data_sources=[], data_timestamps={}, validated_data={}, quality_scores={}, validation_errors=[], processing_results={'response': "I couldn't generate a response for your query. The detected intent was: general_question. Please try asking about stocks, trading, or market-related topics."}, analysis_results={}, ai_responses={}, ai_confidence=0.0, current_stage='', stage_history=[], status=<PipelineStatus.COMPLETED: 'completed'>, start_time=datetime.datetime(2025, 8, 24, 17, 40, 5, 433151), end_time=None, stage_timings={}, total_execution_time=0.0, resource_usage={}, audit_trail=[], error_log=[], config={}, max_execution_time=30.0, should_continue=True).status
E    +  and   <PipelineStatus.FAILED: 'failed'> = PipelineStatus.FAILED
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:05.431458", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting Enhanced Ask Pipeline", "module": "pipeline", "function": "execute_ask_pipeline", "line": 190, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.432208", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udcdd Query: What is the price of AAPL?", "module": "pipeline", "function": "execute_ask_pipeline", "line": 191, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.432437", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udc64 User: test_user", "module": "pipeline", "function": "execute_ask_pipeline", "line": 192, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.432620", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfe0 Guild: test_guild", "module": "pipeline", "function": "execute_ask_pipeline", "line": 193, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.432786", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udd94 Pipeline ID: dd9e8a2c-5785-445b-b83e-158ccec8e5d7", "module": "pipeline", "function": "execute_ask_pipeline", "line": 194, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.432955", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd12 Strict Mode: False", "module": "pipeline", "function": "execute_ask_pipeline", "line": 195, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:05.434306", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing pipeline with 0 sections...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 212, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.464834", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Pipeline execution completed, got 0 results", "module": "pipeline", "function": "execute_ask_pipeline", "line": 219, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.465459", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Raw pipeline results: {}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 220, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.465738", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Analyzing 0 section results...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 230, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.465914", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted keys: []", "module": "pipeline", "function": "execute_ask_pipeline", "line": 262, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.466067", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted data: {}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 263, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.466234", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a0\ufe0f No 'response' key found. Available keys: []", "module": "pipeline", "function": "execute_ask_pipeline", "line": 267, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.466449", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Created fallback response", "module": "pipeline", "function": "execute_ask_pipeline", "line": 286, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.466620", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final response length: 156 characters", "module": "pipeline", "function": "execute_ask_pipeline", "line": 290, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.466833", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Pipeline completed successfully in 30.04s", "module": "pipeline", "function": "execute_ask_pipeline", "line": 300, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:190 🚀 Starting Enhanced Ask Pipeline
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:191 📝 Query: What is the price of AAPL?
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:192 👤 User: test_user
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:193 🏠 Guild: test_guild
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:194 🆔 Pipeline ID: dd9e8a2c-5785-445b-b83e-158ccec8e5d7
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:195 🔒 Strict Mode: False
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:212 ⚡ Executing pipeline with 0 sections...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:219 🔍 Pipeline execution completed, got 0 results
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:220 🔍 Raw pipeline results: {}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:230 🔍 Analyzing 0 section results...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:262 🎯 Final extracted keys: []
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:263 🎯 Final extracted data: {}
WARNING  src.bot.pipeline.commands.ask.pipeline:pipeline.py:267 ⚠️ No 'response' key found. Available keys: []
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:286 ✅ Created fallback response
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:290 🎯 Final response length: 156 characters
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:300 ✅ Pipeline completed successfully in 30.04s
______________________ test_multi_symbol_market_overview _______________________
tests/test_multi_symbol_integration.py:134: in test_multi_symbol_market_overview
    assert 'AAPL' in response
E   AssertionError: assert 'AAPL' in 'Comparing Apple and Google performance'
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:35.690981", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting Enhanced Ask Pipeline", "module": "pipeline", "function": "execute_ask_pipeline", "line": 190, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.691738", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udcdd Query: Compare Apple and Google stock performance", "module": "pipeline", "function": "execute_ask_pipeline", "line": 191, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.691967", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udc64 User: test_user", "module": "pipeline", "function": "execute_ask_pipeline", "line": 192, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.692128", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfe0 Guild: test_guild", "module": "pipeline", "function": "execute_ask_pipeline", "line": 193, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.692278", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udd94 Pipeline ID: 82270bd4-8857-4cc6-95ec-bdbd31aabc8a", "module": "pipeline", "function": "execute_ask_pipeline", "line": 194, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.692453", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd12 Strict Mode: False", "module": "pipeline", "function": "execute_ask_pipeline", "line": 195, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.692720", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing pipeline with 1 sections...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 212, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.692895", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting pipeline execution with 1 sections", "module": "pipeline", "function": "run", "line": 139, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.693058", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing stage: ai_chat_processor", "module": "pipeline", "function": "run", "line": 142, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.693238", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Running stage ai_chat_processor with query: Compare Apple and Google stock performance...", "module": "pipeline", "function": "_run_stage", "line": 154, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.693457", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor input results: {'query': 'Compare Apple and Google stock performance'}", "module": "pipeline", "function": "_run_stage", "line": 159, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.693676", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] AIChatProcessor.__init__ starting for pipeline: 82270bd4-8857-4cc6-95ec-bdbd31aabc8a", "module": "ai_chat_processor", "function": "__init__", "line": 370, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.693928", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 379, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.694190", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 OpenRouter API Key: *****", "module": "ai_chat_processor", "function": "__init__", "line": 385, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.694422", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83c\udf10 OpenRouter Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 386, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.694670", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "OpenRouter configuration loaded", "module": "ai_chat_processor", "function": "__init__", "line": 389, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.694875", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 Using API key (format check disabled)", "module": "ai_chat_processor", "function": "__init__", "line": 398, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.695068", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd27 Configuring OpenAI client with:", "module": "ai_chat_processor", "function": "__init__", "line": 401, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.695271", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   API Key: *****8bdf3", "module": "ai_chat_processor", "function": "__init__", "line": 402, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.695517", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 403, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.695697", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 404, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.712665", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 OpenRouter client configured successfully", "module": "ai_chat_processor", "function": "__init__", "line": 430, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.713316", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [82270bd4-8857-4cc6-95ec-bdbd31aabc8a] OpenRouter client configured with model: moonshotai/kimi-k2:free", "module": "ai_chat_processor", "function": "__init__", "line": 432, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.714959", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\ud83d\udcbe Cached response for query: Compare Apple and Google stock performance... (TTL: 300s)", "module": "ai_cache", "function": "cache_response", "line": 168, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.715483", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor returned: <class 'dict'>", "module": "pipeline", "function": "_run_stage", "line": 162, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.715743", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor completed, result type: <class 'dict'>", "module": "pipeline", "function": "run", "line": 145, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.715945", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor raw result: {'response': 'Comparing Apple and Google performance', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL'], 'tools_required': []}", "module": "pipeline", "function": "run", "line": 146, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.716137", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Pipeline execution completed, returning 1 results", "module": "pipeline", "function": "run", "line": 149, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.716327", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Pipeline execution completed, got 1 results", "module": "pipeline", "function": "execute_ask_pipeline", "line": 219, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.716522", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Raw pipeline results: {'ai_chat_processor': {'response': 'Comparing Apple and Google performance', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL'], 'tools_required': []}}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 220, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.716718", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Analyzing 1 section results...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 230, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.716886", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor returned: <class 'dict'>", "module": "pipeline", "function": "execute_ask_pipeline", "line": 233, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.717076", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor raw result: {'response': 'Comparing Apple and Google performance', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL'], 'tools_required': []}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 234, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.717304", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor returned dict with keys: ['response', 'data', 'intent', 'symbols', 'tools_required']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 240, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.717569", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted response from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.717818", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for response: Comparing Apple and Google performance", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.718050", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted data from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.718301", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for data: {}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.718585", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted intent from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.718858", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for intent: market_overview", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.719079", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted symbols from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.719316", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for symbols: ['AAPL', 'GOOGL']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.719581", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted tools_required from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.719743", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for tools_required: []", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.719923", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted keys: ['response', 'data', 'intent', 'symbols', 'tools_required']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 262, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.720133", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted data: {'response': 'Comparing Apple and Google performance', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL'], 'tools_required': []}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 263, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.720305", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final response length: 38 characters", "module": "pipeline", "function": "execute_ask_pipeline", "line": 290, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.720535", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Pipeline completed successfully in 0.03s", "module": "pipeline", "function": "execute_ask_pipeline", "line": 300, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:190 🚀 Starting Enhanced Ask Pipeline
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:191 📝 Query: Compare Apple and Google stock performance
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:192 👤 User: test_user
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:193 🏠 Guild: test_guild
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:194 🆔 Pipeline ID: 82270bd4-8857-4cc6-95ec-bdbd31aabc8a
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:195 🔒 Strict Mode: False
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:212 ⚡ Executing pipeline with 1 sections...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:139 🚀 Starting pipeline execution with 1 sections
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:142 ⚡ Executing stage: ai_chat_processor
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:154 🔧 Running stage ai_chat_processor with query: Compare Apple and Google stock performance...
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:159 🔧 Stage ai_chat_processor input results: {'query': 'Compare Apple and Google stock performance'}
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:370 🔍 [DIAG] AIChatProcessor.__init__ starting for pipeline: 82270bd4-8857-4cc6-95ec-bdbd31aabc8a
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:379 🔍 [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:385 🔑 OpenRouter API Key: *****
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:386 🌐 OpenRouter Base URL: https://openrouter.ai/api/v1
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:389 OpenRouter configuration loaded
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:398 🔑 Using API key (format check disabled)
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:401 🔧 Configuring OpenAI client with:
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:402    API Key: *****8bdf3
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:403    Base URL: https://openrouter.ai/api/v1
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:404    Timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:430 ✅ OpenRouter client configured successfully
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:432 ✅ [82270bd4-8857-4cc6-95ec-bdbd31aabc8a] OpenRouter client configured with model: moonshotai/kimi-k2:free
INFO     src.bot.pipeline.commands.ask.stages.ai_cache:ai_cache.py:168 💾 Cached response for query: Compare Apple and Google stock performance... (TTL: 300s)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:162 🔧 Stage ai_chat_processor returned: <class 'dict'>
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:145 ✅ Stage ai_chat_processor completed, result type: <class 'dict'>
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:146 ✅ Stage ai_chat_processor raw result: {'response': 'Comparing Apple and Google performance', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:149 🎯 Pipeline execution completed, returning 1 results
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:219 🔍 Pipeline execution completed, got 1 results
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:220 🔍 Raw pipeline results: {'ai_chat_processor': {'response': 'Comparing Apple and Google performance', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL'], 'tools_required': []}}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:230 🔍 Analyzing 1 section results...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:233 📋 Section ai_chat_processor returned: <class 'dict'>
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:234 📋 Section ai_chat_processor raw result: {'response': 'Comparing Apple and Google performance', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:240 📋 Section ai_chat_processor returned dict with keys: ['response', 'data', 'intent', 'symbols', 'tools_required']
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted response from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for response: Comparing Apple and Google performance
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted data from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for data: {}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted intent from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for intent: market_overview
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted symbols from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for symbols: ['AAPL', 'GOOGL']
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted tools_required from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for tools_required: []
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:262 🎯 Final extracted keys: ['response', 'data', 'intent', 'symbols', 'tools_required']
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:263 🎯 Final extracted data: {'response': 'Comparing Apple and Google performance', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:290 🎯 Final response length: 38 characters
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:300 ✅ Pipeline completed successfully in 0.03s
__________________________ test_tech_sector_analysis ___________________________
tests/test_multi_symbol_integration.py:181: in test_tech_sector_analysis
    assert 'Analysis' in response
E   AssertionError: assert 'Analysis' in 'Tech sector analysis'
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:35.737127", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting Enhanced Ask Pipeline", "module": "pipeline", "function": "execute_ask_pipeline", "line": 190, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.737841", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udcdd Query: Analyze tech sector stocks", "module": "pipeline", "function": "execute_ask_pipeline", "line": 191, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.738220", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udc64 User: test_user", "module": "pipeline", "function": "execute_ask_pipeline", "line": 192, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.738523", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfe0 Guild: test_guild", "module": "pipeline", "function": "execute_ask_pipeline", "line": 193, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.738785", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udd94 Pipeline ID: f32c9a0b-c297-4e85-a8ae-40e58737dc0c", "module": "pipeline", "function": "execute_ask_pipeline", "line": 194, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.739028", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd12 Strict Mode: False", "module": "pipeline", "function": "execute_ask_pipeline", "line": 195, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.739358", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing pipeline with 1 sections...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 212, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.739625", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting pipeline execution with 1 sections", "module": "pipeline", "function": "run", "line": 139, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.739848", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing stage: ai_chat_processor", "module": "pipeline", "function": "run", "line": 142, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.740014", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Running stage ai_chat_processor with query: Analyze tech sector stocks...", "module": "pipeline", "function": "_run_stage", "line": 154, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.740185", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor input results: {'query': 'Analyze tech sector stocks'}", "module": "pipeline", "function": "_run_stage", "line": 159, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.740376", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] AIChatProcessor.__init__ starting for pipeline: f32c9a0b-c297-4e85-a8ae-40e58737dc0c", "module": "ai_chat_processor", "function": "__init__", "line": 370, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.740597", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 379, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.740788", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 OpenRouter API Key: *****", "module": "ai_chat_processor", "function": "__init__", "line": 385, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.740952", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83c\udf10 OpenRouter Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 386, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.741101", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "OpenRouter configuration loaded", "module": "ai_chat_processor", "function": "__init__", "line": 389, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.741246", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 Using API key (format check disabled)", "module": "ai_chat_processor", "function": "__init__", "line": 398, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.741391", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd27 Configuring OpenAI client with:", "module": "ai_chat_processor", "function": "__init__", "line": 401, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.741555", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   API Key: *****8bdf3", "module": "ai_chat_processor", "function": "__init__", "line": 402, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.741698", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 403, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.741844", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 404, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.754855", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 OpenRouter client configured successfully", "module": "ai_chat_processor", "function": "__init__", "line": 430, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.755558", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [f32c9a0b-c297-4e85-a8ae-40e58737dc0c] OpenRouter client configured with model: moonshotai/kimi-k2:free", "module": "ai_chat_processor", "function": "__init__", "line": 432, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.758260", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\ud83d\udcbe Cached response for query: Analyze tech sector stocks... (TTL: 300s)", "module": "ai_cache", "function": "cache_response", "line": 168, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.758969", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor returned: <class 'dict'>", "module": "pipeline", "function": "_run_stage", "line": 162, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.759245", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor completed, result type: <class 'dict'>", "module": "pipeline", "function": "run", "line": 145, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.759456", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor raw result: {'response': 'Tech sector analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}", "module": "pipeline", "function": "run", "line": 146, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.759638", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Pipeline execution completed, returning 1 results", "module": "pipeline", "function": "run", "line": 149, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.759804", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Pipeline execution completed, got 1 results", "module": "pipeline", "function": "execute_ask_pipeline", "line": 219, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.759960", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Raw pipeline results: {'ai_chat_processor': {'response': 'Tech sector analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 220, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.760144", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Analyzing 1 section results...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 230, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.760296", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor returned: <class 'dict'>", "module": "pipeline", "function": "execute_ask_pipeline", "line": 233, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.760461", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor raw result: {'response': 'Tech sector analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 234, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.760625", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor returned dict with keys: ['response', 'data', 'intent', 'symbols', 'tools_required']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 240, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.760774", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted response from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.760919", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for response: Tech sector analysis", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.761062", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted data from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.761202", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for data: {}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.761345", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted intent from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.761504", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for intent: market_overview", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.761654", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted symbols from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.761798", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for symbols: ['AAPL', 'GOOGL', 'TSLA']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.761938", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted tools_required from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.762078", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for tools_required: []", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.762221", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted keys: ['response', 'data', 'intent', 'symbols', 'tools_required']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 262, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.762370", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted data: {'response': 'Tech sector analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 263, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.762527", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final response length: 20 characters", "module": "pipeline", "function": "execute_ask_pipeline", "line": 290, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.762699", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Pipeline completed successfully in 0.03s", "module": "pipeline", "function": "execute_ask_pipeline", "line": 300, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:190 🚀 Starting Enhanced Ask Pipeline
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:191 📝 Query: Analyze tech sector stocks
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:192 👤 User: test_user
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:193 🏠 Guild: test_guild
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:194 🆔 Pipeline ID: f32c9a0b-c297-4e85-a8ae-40e58737dc0c
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:195 🔒 Strict Mode: False
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:212 ⚡ Executing pipeline with 1 sections...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:139 🚀 Starting pipeline execution with 1 sections
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:142 ⚡ Executing stage: ai_chat_processor
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:154 🔧 Running stage ai_chat_processor with query: Analyze tech sector stocks...
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:159 🔧 Stage ai_chat_processor input results: {'query': 'Analyze tech sector stocks'}
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:370 🔍 [DIAG] AIChatProcessor.__init__ starting for pipeline: f32c9a0b-c297-4e85-a8ae-40e58737dc0c
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:379 🔍 [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:385 🔑 OpenRouter API Key: *****
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:386 🌐 OpenRouter Base URL: https://openrouter.ai/api/v1
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:389 OpenRouter configuration loaded
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:398 🔑 Using API key (format check disabled)
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:401 🔧 Configuring OpenAI client with:
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:402    API Key: *****8bdf3
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:403    Base URL: https://openrouter.ai/api/v1
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:404    Timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:430 ✅ OpenRouter client configured successfully
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:432 ✅ [f32c9a0b-c297-4e85-a8ae-40e58737dc0c] OpenRouter client configured with model: moonshotai/kimi-k2:free
INFO     src.bot.pipeline.commands.ask.stages.ai_cache:ai_cache.py:168 💾 Cached response for query: Analyze tech sector stocks... (TTL: 300s)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:162 🔧 Stage ai_chat_processor returned: <class 'dict'>
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:145 ✅ Stage ai_chat_processor completed, result type: <class 'dict'>
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:146 ✅ Stage ai_chat_processor raw result: {'response': 'Tech sector analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:149 🎯 Pipeline execution completed, returning 1 results
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:219 🔍 Pipeline execution completed, got 1 results
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:220 🔍 Raw pipeline results: {'ai_chat_processor': {'response': 'Tech sector analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:230 🔍 Analyzing 1 section results...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:233 📋 Section ai_chat_processor returned: <class 'dict'>
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:234 📋 Section ai_chat_processor raw result: {'response': 'Tech sector analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:240 📋 Section ai_chat_processor returned dict with keys: ['response', 'data', 'intent', 'symbols', 'tools_required']
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted response from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for response: Tech sector analysis
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted data from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for data: {}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted intent from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for intent: market_overview
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted symbols from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for symbols: ['AAPL', 'GOOGL', 'TSLA']
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted tools_required from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for tools_required: []
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:262 🎯 Final extracted keys: ['response', 'data', 'intent', 'symbols', 'tools_required']
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:263 🎯 Final extracted data: {'response': 'Tech sector analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:290 🎯 Final response length: 20 characters
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:300 ✅ Pipeline completed successfully in 0.03s
___________________ test_response_contains_required_elements ___________________
tests/test_multi_symbol_integration.py:219: in test_response_contains_required_elements
    assert present_elements >= 5, f"Missing required elements in response: {response}"
E   AssertionError: Missing required elements in response: Apple stock analysis
E   assert 1 >= 5
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:35.786579", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting Enhanced Ask Pipeline", "module": "pipeline", "function": "execute_ask_pipeline", "line": 190, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.787057", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udcdd Query: What about AAPL stock?", "module": "pipeline", "function": "execute_ask_pipeline", "line": 191, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.787233", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udc64 User: test_user", "module": "pipeline", "function": "execute_ask_pipeline", "line": 192, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.787387", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfe0 Guild: test_guild", "module": "pipeline", "function": "execute_ask_pipeline", "line": 193, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.787557", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udd94 Pipeline ID: 9d66439d-db66-445e-a419-73bf27af95db", "module": "pipeline", "function": "execute_ask_pipeline", "line": 194, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.787707", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd12 Strict Mode: False", "module": "pipeline", "function": "execute_ask_pipeline", "line": 195, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.787937", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing pipeline with 1 sections...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 212, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.788186", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting pipeline execution with 1 sections", "module": "pipeline", "function": "run", "line": 139, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.788371", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing stage: ai_chat_processor", "module": "pipeline", "function": "run", "line": 142, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.788567", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Running stage ai_chat_processor with query: What about AAPL stock?...", "module": "pipeline", "function": "_run_stage", "line": 154, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.788733", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor input results: {'query': 'What about AAPL stock?'}", "module": "pipeline", "function": "_run_stage", "line": 159, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.788913", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] AIChatProcessor.__init__ starting for pipeline: 9d66439d-db66-445e-a419-73bf27af95db", "module": "ai_chat_processor", "function": "__init__", "line": 370, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.789117", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 379, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.789318", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 OpenRouter API Key: *****", "module": "ai_chat_processor", "function": "__init__", "line": 385, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.789559", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83c\udf10 OpenRouter Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 386, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.789725", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "OpenRouter configuration loaded", "module": "ai_chat_processor", "function": "__init__", "line": 389, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.789871", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 Using API key (format check disabled)", "module": "ai_chat_processor", "function": "__init__", "line": 398, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.790030", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd27 Configuring OpenAI client with:", "module": "ai_chat_processor", "function": "__init__", "line": 401, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.790180", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   API Key: *****8bdf3", "module": "ai_chat_processor", "function": "__init__", "line": 402, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.790324", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 403, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.790497", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 404, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.804675", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 OpenRouter client configured successfully", "module": "ai_chat_processor", "function": "__init__", "line": 430, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.805152", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [9d66439d-db66-445e-a419-73bf27af95db] OpenRouter client configured with model: moonshotai/kimi-k2:free", "module": "ai_chat_processor", "function": "__init__", "line": 432, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.806683", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.models", "message": "Invalid intent 'stock_analysis', defaulting to 'general_question'", "module": "models", "function": "validate_intent", "line": 44, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.807417", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\ud83d\udcbe Cached response for query: What about AAPL stock?... (TTL: 300s)", "module": "ai_cache", "function": "cache_response", "line": 168, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.807808", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor returned: <class 'dict'>", "module": "pipeline", "function": "_run_stage", "line": 162, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.808087", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor completed, result type: <class 'dict'>", "module": "pipeline", "function": "run", "line": 145, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.808389", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor raw result: {'response': 'Apple stock analysis', 'data': {}, 'intent': 'general_question', 'symbols': ['AAPL'], 'tools_required': []}", "module": "pipeline", "function": "run", "line": 146, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.808802", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Pipeline execution completed, returning 1 results", "module": "pipeline", "function": "run", "line": 149, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.809017", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Pipeline execution completed, got 1 results", "module": "pipeline", "function": "execute_ask_pipeline", "line": 219, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.809230", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Raw pipeline results: {'ai_chat_processor': {'response': 'Apple stock analysis', 'data': {}, 'intent': 'general_question', 'symbols': ['AAPL'], 'tools_required': []}}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 220, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.809502", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Analyzing 1 section results...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 230, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.809766", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor returned: <class 'dict'>", "module": "pipeline", "function": "execute_ask_pipeline", "line": 233, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.810014", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor raw result: {'response': 'Apple stock analysis', 'data': {}, 'intent': 'general_question', 'symbols': ['AAPL'], 'tools_required': []}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 234, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.810266", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor returned dict with keys: ['response', 'data', 'intent', 'symbols', 'tools_required']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 240, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.810550", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted response from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.810796", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for response: Apple stock analysis", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.811013", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted data from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.811192", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for data: {}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.811351", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted intent from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.811571", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for intent: general_question", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.811728", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted symbols from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.811881", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for symbols: ['AAPL']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.812040", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted tools_required from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.812187", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for tools_required: []", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.812340", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted keys: ['response', 'data', 'intent', 'symbols', 'tools_required']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 262, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.812511", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted data: {'response': 'Apple stock analysis', 'data': {}, 'intent': 'general_question', 'symbols': ['AAPL'], 'tools_required': []}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 263, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.812675", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final response length: 20 characters", "module": "pipeline", "function": "execute_ask_pipeline", "line": 290, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.812859", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Pipeline completed successfully in 0.03s", "module": "pipeline", "function": "execute_ask_pipeline", "line": 300, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:190 🚀 Starting Enhanced Ask Pipeline
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:191 📝 Query: What about AAPL stock?
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:192 👤 User: test_user
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:193 🏠 Guild: test_guild
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:194 🆔 Pipeline ID: 9d66439d-db66-445e-a419-73bf27af95db
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:195 🔒 Strict Mode: False
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:212 ⚡ Executing pipeline with 1 sections...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:139 🚀 Starting pipeline execution with 1 sections
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:142 ⚡ Executing stage: ai_chat_processor
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:154 🔧 Running stage ai_chat_processor with query: What about AAPL stock?...
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:159 🔧 Stage ai_chat_processor input results: {'query': 'What about AAPL stock?'}
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:370 🔍 [DIAG] AIChatProcessor.__init__ starting for pipeline: 9d66439d-db66-445e-a419-73bf27af95db
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:379 🔍 [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:385 🔑 OpenRouter API Key: *****
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:386 🌐 OpenRouter Base URL: https://openrouter.ai/api/v1
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:389 OpenRouter configuration loaded
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:398 🔑 Using API key (format check disabled)
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:401 🔧 Configuring OpenAI client with:
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:402    API Key: *****8bdf3
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:403    Base URL: https://openrouter.ai/api/v1
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:404    Timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:430 ✅ OpenRouter client configured successfully
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:432 ✅ [9d66439d-db66-445e-a419-73bf27af95db] OpenRouter client configured with model: moonshotai/kimi-k2:free
WARNING  src.bot.pipeline.commands.ask.stages.models:models.py:44 Invalid intent 'stock_analysis', defaulting to 'general_question'
INFO     src.bot.pipeline.commands.ask.stages.ai_cache:ai_cache.py:168 💾 Cached response for query: What about AAPL stock?... (TTL: 300s)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:162 🔧 Stage ai_chat_processor returned: <class 'dict'>
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:145 ✅ Stage ai_chat_processor completed, result type: <class 'dict'>
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:146 ✅ Stage ai_chat_processor raw result: {'response': 'Apple stock analysis', 'data': {}, 'intent': 'general_question', 'symbols': ['AAPL'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:149 🎯 Pipeline execution completed, returning 1 results
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:219 🔍 Pipeline execution completed, got 1 results
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:220 🔍 Raw pipeline results: {'ai_chat_processor': {'response': 'Apple stock analysis', 'data': {}, 'intent': 'general_question', 'symbols': ['AAPL'], 'tools_required': []}}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:230 🔍 Analyzing 1 section results...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:233 📋 Section ai_chat_processor returned: <class 'dict'>
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:234 📋 Section ai_chat_processor raw result: {'response': 'Apple stock analysis', 'data': {}, 'intent': 'general_question', 'symbols': ['AAPL'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:240 📋 Section ai_chat_processor returned dict with keys: ['response', 'data', 'intent', 'symbols', 'tools_required']
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted response from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for response: Apple stock analysis
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted data from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for data: {}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted intent from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for intent: general_question
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted symbols from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for symbols: ['AAPL']
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted tools_required from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for tools_required: []
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:262 🎯 Final extracted keys: ['response', 'data', 'intent', 'symbols', 'tools_required']
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:263 🎯 Final extracted data: {'response': 'Apple stock analysis', 'data': {}, 'intent': 'general_question', 'symbols': ['AAPL'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:290 🎯 Final response length: 20 characters
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:300 ✅ Pipeline completed successfully in 0.03s
___________________ test_discord_compatible_response_length ____________________
tests/test_multi_symbol_integration.py:246: in test_discord_compatible_response_length
    assert len(response) > 100, "Response too short"
E   AssertionError: Response too short
E   assert 24 > 100
E    +  where 24 = len('Market overview analysis')
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:35.831972", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting Enhanced Ask Pipeline", "module": "pipeline", "function": "execute_ask_pipeline", "line": 190, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.832824", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udcdd Query: Market overview please", "module": "pipeline", "function": "execute_ask_pipeline", "line": 191, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.833048", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udc64 User: test_user", "module": "pipeline", "function": "execute_ask_pipeline", "line": 192, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.833225", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfe0 Guild: test_guild", "module": "pipeline", "function": "execute_ask_pipeline", "line": 193, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.833464", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udd94 Pipeline ID: eeb56529-cef9-40da-b0dc-aedb36abb694", "module": "pipeline", "function": "execute_ask_pipeline", "line": 194, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.833743", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd12 Strict Mode: False", "module": "pipeline", "function": "execute_ask_pipeline", "line": 195, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.834219", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing pipeline with 1 sections...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 212, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.834583", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\ude80 Starting pipeline execution with 1 sections", "module": "pipeline", "function": "run", "line": 139, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.834896", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing stage: ai_chat_processor", "module": "pipeline", "function": "run", "line": 142, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.835175", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Running stage ai_chat_processor with query: Market overview please...", "module": "pipeline", "function": "_run_stage", "line": 154, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.835459", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor input results: {'query': 'Market overview please'}", "module": "pipeline", "function": "_run_stage", "line": 159, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.835791", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] AIChatProcessor.__init__ starting for pipeline: eeb56529-cef9-40da-b0dc-aedb36abb694", "module": "ai_chat_processor", "function": "__init__", "line": 370, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.836088", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 379, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.836309", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 OpenRouter API Key: *****", "module": "ai_chat_processor", "function": "__init__", "line": 385, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.836517", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83c\udf10 OpenRouter Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 386, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.836684", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "OpenRouter configuration loaded", "module": "ai_chat_processor", "function": "__init__", "line": 389, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.836836", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd11 Using API key (format check disabled)", "module": "ai_chat_processor", "function": "__init__", "line": 398, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.836989", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd27 Configuring OpenAI client with:", "module": "ai_chat_processor", "function": "__init__", "line": 401, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.837180", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   API Key: *****8bdf3", "module": "ai_chat_processor", "function": "__init__", "line": 402, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.837445", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Base URL: https://openrouter.ai/api/v1", "module": "ai_chat_processor", "function": "__init__", "line": 403, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.837690", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "   Timeout: 30.0", "module": "ai_chat_processor", "function": "__init__", "line": 404, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.853109", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 OpenRouter client configured successfully", "module": "ai_chat_processor", "function": "__init__", "line": 430, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.853722", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [eeb56529-cef9-40da-b0dc-aedb36abb694] OpenRouter client configured with model: moonshotai/kimi-k2:free", "module": "ai_chat_processor", "function": "__init__", "line": 432, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.855577", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\ud83d\udcbe Cached response for query: Market overview please... (TTL: 300s)", "module": "ai_cache", "function": "cache_response", "line": 168, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.855915", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor returned: <class 'dict'>", "module": "pipeline", "function": "_run_stage", "line": 162, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.856118", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor completed, result type: <class 'dict'>", "module": "pipeline", "function": "run", "line": 145, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.856295", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor raw result: {'response': 'Market overview analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}", "module": "pipeline", "function": "run", "line": 146, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.856536", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Pipeline execution completed, returning 1 results", "module": "pipeline", "function": "run", "line": 149, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.856712", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Pipeline execution completed, got 1 results", "module": "pipeline", "function": "execute_ask_pipeline", "line": 219, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.856873", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Raw pipeline results: {'ai_chat_processor': {'response': 'Market overview analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 220, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.857075", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Analyzing 1 section results...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 230, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.857253", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor returned: <class 'dict'>", "module": "pipeline", "function": "execute_ask_pipeline", "line": 233, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.857441", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor raw result: {'response': 'Market overview analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 234, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.857656", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb Section ai_chat_processor returned dict with keys: ['response', 'data', 'intent', 'symbols', 'tools_required']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 240, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.857845", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted response from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.858012", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for response: Market overview analysis", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.858172", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted data from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.858328", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for data: {}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.858505", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted intent from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.858672", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for intent: market_overview", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.858870", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted symbols from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.859039", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for symbols: ['AAPL', 'GOOGL', 'TSLA']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.859199", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted tools_required from ai_chat_processor section (dict)", "module": "pipeline", "function": "execute_ask_pipeline", "line": 243, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.859350", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for tools_required: []", "module": "pipeline", "function": "execute_ask_pipeline", "line": 244, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.859522", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted keys: ['response', 'data', 'intent', 'symbols', 'tools_required']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 262, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.859693", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted data: {'response': 'Market overview analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 263, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.859863", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final response length: 24 characters", "module": "pipeline", "function": "execute_ask_pipeline", "line": 290, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:35.860069", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Pipeline completed successfully in 0.03s", "module": "pipeline", "function": "execute_ask_pipeline", "line": 300, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:190 🚀 Starting Enhanced Ask Pipeline
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:191 📝 Query: Market overview please
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:192 👤 User: test_user
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:193 🏠 Guild: test_guild
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:194 🆔 Pipeline ID: eeb56529-cef9-40da-b0dc-aedb36abb694
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:195 🔒 Strict Mode: False
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:212 ⚡ Executing pipeline with 1 sections...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:139 🚀 Starting pipeline execution with 1 sections
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:142 ⚡ Executing stage: ai_chat_processor
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:154 🔧 Running stage ai_chat_processor with query: Market overview please...
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:159 🔧 Stage ai_chat_processor input results: {'query': 'Market overview please'}
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:370 🔍 [DIAG] AIChatProcessor.__init__ starting for pipeline: eeb56529-cef9-40da-b0dc-aedb36abb694
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:379 🔍 [DIAG] Configuration - model: moonshotai/kimi-k2:free, temp: 0.7, max_tokens: 2000, timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:385 🔑 OpenRouter API Key: *****
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:386 🌐 OpenRouter Base URL: https://openrouter.ai/api/v1
DEBUG    src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:389 OpenRouter configuration loaded
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:398 🔑 Using API key (format check disabled)
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:401 🔧 Configuring OpenAI client with:
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:402    API Key: *****8bdf3
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:403    Base URL: https://openrouter.ai/api/v1
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:404    Timeout: 30.0
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:430 ✅ OpenRouter client configured successfully
INFO     src.bot.pipeline.commands.ask.stages.ai_chat_processor:ai_chat_processor.py:432 ✅ [eeb56529-cef9-40da-b0dc-aedb36abb694] OpenRouter client configured with model: moonshotai/kimi-k2:free
INFO     src.bot.pipeline.commands.ask.stages.ai_cache:ai_cache.py:168 💾 Cached response for query: Market overview please... (TTL: 300s)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:162 🔧 Stage ai_chat_processor returned: <class 'dict'>
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:145 ✅ Stage ai_chat_processor completed, result type: <class 'dict'>
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:146 ✅ Stage ai_chat_processor raw result: {'response': 'Market overview analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:149 🎯 Pipeline execution completed, returning 1 results
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:219 🔍 Pipeline execution completed, got 1 results
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:220 🔍 Raw pipeline results: {'ai_chat_processor': {'response': 'Market overview analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:230 🔍 Analyzing 1 section results...
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:233 📋 Section ai_chat_processor returned: <class 'dict'>
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:234 📋 Section ai_chat_processor raw result: {'response': 'Market overview analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:240 📋 Section ai_chat_processor returned dict with keys: ['response', 'data', 'intent', 'symbols', 'tools_required']
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted response from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for response: Market overview analysis
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted data from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for data: {}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted intent from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for intent: market_overview
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted symbols from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for symbols: ['AAPL', 'GOOGL', 'TSLA']
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:243 Extracted tools_required from ai_chat_processor section (dict)
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:244 Value for tools_required: []
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:262 🎯 Final extracted keys: ['response', 'data', 'intent', 'symbols', 'tools_required']
DEBUG    src.bot.pipeline.commands.ask.pipeline:pipeline.py:263 🎯 Final extracted data: {'response': 'Market overview analysis', 'data': {}, 'intent': 'market_overview', 'symbols': ['AAPL', 'GOOGL', 'TSLA'], 'tools_required': []}
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:290 🎯 Final response length: 24 characters
INFO     src.bot.pipeline.commands.ask.pipeline:pipeline.py:300 ✅ Pipeline completed successfully in 0.03s
_____________________ test_supabase_client_initialization ______________________
tests/test_supabase_connection.py:9: in test_supabase_client_initialization
    assert client is not None, "Supabase client should be initialized"
E   AssertionError: Supabase client should be initialized
E   assert None is not None
_______________________ test_supabase_connection_status ________________________
tests/test_supabase_connection.py:16: in test_supabase_connection_status
    assert connection_status is True, "Supabase connection test should pass"
E   AssertionError: Supabase connection test should pass
E   assert False is True
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:35.911611", "level": "ERROR", "logger": "src.database.supabase_client", "message": "Supabase connection test failed: No client available", "module": "supabase_client", "function": "test_supabase_connection", "line": 223, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.database.supabase_client:supabase_client.py:223 Supabase connection test failed: No client available
________________________ test_supabase_insert_and_query ________________________
tests/test_supabase_connection.py:23: in test_supabase_insert_and_query
    assert client is not None, "Supabase client should be initialized"
E   AssertionError: Supabase client should be initialized
E   assert None is not None
_____________________________ test_supabase_update _____________________________
tests/test_supabase_connection.py:48: in test_supabase_update
    assert client is not None, "Supabase client should be initialized"
E   AssertionError: Supabase client should be initialized
E   assert None is not None
________________ TestMarketDataService.test_get_historical_data ________________
src/api/data/market_data_service.py:104: in get_historical_data
    historical_item = MarketDataResponse(
E   TypeError: MarketDataResponse.__init__() got an unexpected keyword argument 'change'

During handling of the above exception, another exception occurred:
tests/integration/test_market_data_service.py:46: in test_get_historical_data
    historical_data = await market_data_service.get_historical_data(
src/api/data/market_data_service.py:123: in get_historical_data
    raise MarketDataError(f"Failed to get historical data for {symbol}")
src/core/exceptions.py:275: in __init__
    super().__init__(
src/core/exceptions.py:214: in __init__
    super().__init__(
E   KeyError: 'details'
---------------------------- Captured stderr setup -----------------------------
{"timestamp": "2025-08-24T17:40:36.778522", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:36.779311", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:36.779605", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:36.779816", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log setup ------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:630 ✅ Yahoo Finance provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:636 ✅ Polygon provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:644 ✅ Finnhub provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:650 ✅ Initialized 3 data providers
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:36.782084", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udd0d Provider priority for MSFT: ['polygon', 'finnhub', 'yahoo']", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1023, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.401855", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 polygon provided usable data for MSFT", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1063, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.402223", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\ude80 High-priority provider polygon succeeded - returning data immediately", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1067, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.402582", "level": "ERROR", "logger": "src.api.data.market_data_service", "message": "Error retrieving historical data for MSFT: MarketDataResponse.__init__() got an unexpected keyword argument 'change'", "module": "market_data_service", "function": "get_historical_data", "line": 122, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1023 🔍 Provider priority for MSFT: ['polygon', 'finnhub', 'yahoo']
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1063 ✅ polygon provided usable data for MSFT
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1067 🚀 High-priority provider polygon succeeded - returning data immediately
ERROR    src.api.data.market_data_service:market_data_service.py:122 Error retrieving historical data for MSFT: MarketDataResponse.__init__() got an unexpected keyword argument 'change'
__________________ TestMarketDataService.test_invalid_symbol ___________________
src/api/data/market_data_service.py:42: in get_current_price
    raise ValueError(f"Invalid symbol: {symbol}")
E   ValueError: Invalid symbol:

During handling of the above exception, another exception occurred:
tests/integration/test_market_data_service.py:76: in test_invalid_symbol
    await market_data_service.get_current_price(symbol)
src/api/data/market_data_service.py:65: in get_current_price
    raise MarketDataError(f"Failed to get current price for {symbol}")
src/core/exceptions.py:275: in __init__
    super().__init__(
src/core/exceptions.py:214: in __init__
    super().__init__(
E   KeyError: 'details'
---------------------------- Captured stderr setup -----------------------------
{"timestamp": "2025-08-24T17:40:37.445904", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.446451", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.446684", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.446846", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log setup ------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:630 ✅ Yahoo Finance provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:636 ✅ Polygon provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:644 ✅ Finnhub provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:650 ✅ Initialized 3 data providers
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:37.449574", "level": "ERROR", "logger": "src.api.data.market_data_service", "message": "Error retrieving current price for : Invalid symbol: ", "module": "market_data_service", "function": "get_current_price", "line": 64, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.api.data.market_data_service:market_data_service.py:64 Error retrieving current price for : Invalid symbol:
_____ TestMarketDataService.test_historical_data_with_different_periods[1] _____
src/api/data/market_data_service.py:104: in get_historical_data
    historical_item = MarketDataResponse(
E   TypeError: MarketDataResponse.__init__() got an unexpected keyword argument 'change'

During handling of the above exception, another exception occurred:
tests/integration/test_market_data_service.py:90: in test_historical_data_with_different_periods
    historical_data = await market_data_service.get_historical_data(
src/api/data/market_data_service.py:123: in get_historical_data
    raise MarketDataError(f"Failed to get historical data for {symbol}")
src/core/exceptions.py:275: in __init__
    super().__init__(
src/core/exceptions.py:214: in __init__
    super().__init__(
E   KeyError: 'details'
---------------------------- Captured stderr setup -----------------------------
{"timestamp": "2025-08-24T17:40:37.496692", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.497248", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.497459", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.497640", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log setup ------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:630 ✅ Yahoo Finance provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:636 ✅ Polygon provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:644 ✅ Finnhub provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:650 ✅ Initialized 3 data providers
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:37.500141", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udd0d Provider priority for GOOGL: ['polygon', 'finnhub', 'yahoo']", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1023, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.850797", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 polygon provided usable data for GOOGL", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1063, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.851134", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\ude80 High-priority provider polygon succeeded - returning data immediately", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1067, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.851426", "level": "ERROR", "logger": "src.api.data.market_data_service", "message": "Error retrieving historical data for GOOGL: MarketDataResponse.__init__() got an unexpected keyword argument 'change'", "module": "market_data_service", "function": "get_historical_data", "line": 122, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1023 🔍 Provider priority for GOOGL: ['polygon', 'finnhub', 'yahoo']
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1063 ✅ polygon provided usable data for GOOGL
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1067 🚀 High-priority provider polygon succeeded - returning data immediately
ERROR    src.api.data.market_data_service:market_data_service.py:122 Error retrieving historical data for GOOGL: MarketDataResponse.__init__() got an unexpected keyword argument 'change'
_____ TestMarketDataService.test_historical_data_with_different_periods[7] _____
src/api/data/market_data_service.py:104: in get_historical_data
    historical_item = MarketDataResponse(
E   TypeError: MarketDataResponse.__init__() got an unexpected keyword argument 'change'

During handling of the above exception, another exception occurred:
tests/integration/test_market_data_service.py:90: in test_historical_data_with_different_periods
    historical_data = await market_data_service.get_historical_data(
src/api/data/market_data_service.py:123: in get_historical_data
    raise MarketDataError(f"Failed to get historical data for {symbol}")
src/core/exceptions.py:275: in __init__
    super().__init__(
src/core/exceptions.py:214: in __init__
    super().__init__(
E   KeyError: 'details'
---------------------------- Captured stderr setup -----------------------------
{"timestamp": "2025-08-24T17:40:37.888633", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.888970", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.889170", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:37.889330", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log setup ------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:630 ✅ Yahoo Finance provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:636 ✅ Polygon provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:644 ✅ Finnhub provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:650 ✅ Initialized 3 data providers
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:37.890768", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udd0d Provider priority for GOOGL: ['polygon', 'finnhub', 'yahoo']", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1023, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.207586", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 polygon provided usable data for GOOGL", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1063, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.208224", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\ude80 High-priority provider polygon succeeded - returning data immediately", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1067, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.208651", "level": "ERROR", "logger": "src.api.data.market_data_service", "message": "Error retrieving historical data for GOOGL: MarketDataResponse.__init__() got an unexpected keyword argument 'change'", "module": "market_data_service", "function": "get_historical_data", "line": 122, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1023 🔍 Provider priority for GOOGL: ['polygon', 'finnhub', 'yahoo']
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1063 ✅ polygon provided usable data for GOOGL
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1067 🚀 High-priority provider polygon succeeded - returning data immediately
ERROR    src.api.data.market_data_service:market_data_service.py:122 Error retrieving historical data for GOOGL: MarketDataResponse.__init__() got an unexpected keyword argument 'change'
____ TestMarketDataService.test_historical_data_with_different_periods[30] _____
src/api/data/market_data_service.py:104: in get_historical_data
    historical_item = MarketDataResponse(
E   TypeError: MarketDataResponse.__init__() got an unexpected keyword argument 'change'

During handling of the above exception, another exception occurred:
tests/integration/test_market_data_service.py:90: in test_historical_data_with_different_periods
    historical_data = await market_data_service.get_historical_data(
src/api/data/market_data_service.py:123: in get_historical_data
    raise MarketDataError(f"Failed to get historical data for {symbol}")
src/core/exceptions.py:275: in __init__
    super().__init__(
src/core/exceptions.py:214: in __init__
    super().__init__(
E   KeyError: 'details'
---------------------------- Captured stderr setup -----------------------------
{"timestamp": "2025-08-24T17:40:38.258662", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.259391", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.259641", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.259795", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log setup ------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:630 ✅ Yahoo Finance provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:636 ✅ Polygon provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:644 ✅ Finnhub provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:650 ✅ Initialized 3 data providers
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:38.261652", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udd0d Provider priority for GOOGL: ['polygon', 'finnhub', 'yahoo']", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1023, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.800268", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 polygon provided usable data for GOOGL", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1063, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.800741", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\ude80 High-priority provider polygon succeeded - returning data immediately", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1067, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.801032", "level": "ERROR", "logger": "src.api.data.market_data_service", "message": "Error retrieving historical data for GOOGL: MarketDataResponse.__init__() got an unexpected keyword argument 'change'", "module": "market_data_service", "function": "get_historical_data", "line": 122, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1023 🔍 Provider priority for GOOGL: ['polygon', 'finnhub', 'yahoo']
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1063 ✅ polygon provided usable data for GOOGL
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1067 🚀 High-priority provider polygon succeeded - returning data immediately
ERROR    src.api.data.market_data_service:market_data_service.py:122 Error retrieving historical data for GOOGL: MarketDataResponse.__init__() got an unexpected keyword argument 'change'
____ TestMarketDataService.test_historical_data_with_different_periods[90] _____
src/api/data/market_data_service.py:104: in get_historical_data
    historical_item = MarketDataResponse(
E   TypeError: MarketDataResponse.__init__() got an unexpected keyword argument 'change'

During handling of the above exception, another exception occurred:
tests/integration/test_market_data_service.py:90: in test_historical_data_with_different_periods
    historical_data = await market_data_service.get_historical_data(
src/api/data/market_data_service.py:123: in get_historical_data
    raise MarketDataError(f"Failed to get historical data for {symbol}")
src/core/exceptions.py:275: in __init__
    super().__init__(
src/core/exceptions.py:214: in __init__
    super().__init__(
E   KeyError: 'details'
---------------------------- Captured stderr setup -----------------------------
{"timestamp": "2025-08-24T17:40:38.845306", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.846230", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.846612", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:38.846821", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log setup ------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:630 ✅ Yahoo Finance provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:636 ✅ Polygon provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:644 ✅ Finnhub provider initialized
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:650 ✅ Initialized 3 data providers
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:38.849819", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udd0d Provider priority for GOOGL: ['polygon', 'finnhub', 'yahoo']", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1023, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:39.347959", "level": "ERROR", "logger": "src.api.data.providers.data_source_manager", "message": "\u274c Polygon error for GOOGL: Client error '429 Too Many Requests' for url 'https://api.polygon.io/v2/aggs/ticker/GOOGL/prev?apiKey=********************************&adjusted=true'\nFor more information check: https://httpstatuses.com/429", "module": "data_source_manager", "function": "get_stock_data", "line": 486, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:39.348240", "level": "WARNING", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udeab polygon rate limited for GOOGL: Client error '429 Too Many Requests' for url 'https://api.polygon.io/v2/aggs/ticker/GOOGL/prev?apiKey=********************************&adjusted=true'\nFor more information check: https://httpstatuses.com/429", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1079, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:40.055226", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 finnhub provided usable data for GOOGL", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1063, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:40.055807", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\ude80 High-priority provider finnhub succeeded - returning data immediately", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1067, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:40.056264", "level": "ERROR", "logger": "src.api.data.market_data_service", "message": "Error retrieving historical data for GOOGL: MarketDataResponse.__init__() got an unexpected keyword argument 'change'", "module": "market_data_service", "function": "get_historical_data", "line": 122, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1023 🔍 Provider priority for GOOGL: ['polygon', 'finnhub', 'yahoo']
ERROR    src.api.data.providers.data_source_manager:data_source_manager.py:486 ❌ Polygon error for GOOGL: Client error '429 Too Many Requests' for url 'https://api.polygon.io/v2/aggs/ticker/GOOGL/prev?apiKey=********************************&adjusted=true'
For more information check: https://httpstatuses.com/429
WARNING  src.api.data.providers.data_source_manager:data_source_manager.py:1079 🚫 polygon rate limited for GOOGL: Client error '429 Too Many Requests' for url 'https://api.polygon.io/v2/aggs/ticker/GOOGL/prev?apiKey=********************************&adjusted=true'
For more information check: https://httpstatuses.com/429
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1063 ✅ finnhub provided usable data for GOOGL
INFO     src.api.data.providers.data_source_manager:data_source_manager.py:1067 🚀 High-priority provider finnhub succeeded - returning data immediately
ERROR    src.api.data.market_data_service:market_data_service.py:122 Error retrieving historical data for GOOGL: MarketDataResponse.__init__() got an unexpected keyword argument 'change'
__________________ TestPolygonProvider.test_get_current_price __________________
src/shared/data_providers/polygon_provider.py:167: in get_current_price
    return asyncio.run(self._get_current_price_async(symbol))
/usr/local/lib/python3.10/asyncio/runners.py:33: in run
    raise RuntimeError(
E   RuntimeError: asyncio.run() cannot be called from a running event loop

During handling of the above exception, another exception occurred:
tests/integration/test_polygon_provider.py:27: in test_get_current_price
    price_data = await polygon_provider.get_current_price(symbol)
src/shared/data_providers/polygon_provider.py:170: in get_current_price
    raise ProviderError(str(e), "polygon")
E   src.shared.data_providers.base_provider.ProviderError: polygon error: asyncio.run() cannot be called from a running event loop
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:40.138359", "level": "ERROR", "logger": "src.shared.data_providers.polygon_provider", "message": "Polygon failed for AAPL: asyncio.run() cannot be called from a running event loop", "module": "polygon_provider", "function": "get_current_price", "line": 169, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.shared.data_providers.polygon_provider:polygon_provider.py:169 Polygon failed for AAPL: asyncio.run() cannot be called from a running event loop
_________________ TestPolygonProvider.test_get_historical_data _________________
tests/integration/test_polygon_provider.py:48: in test_get_historical_data
    historical_data = await polygon_provider.get_historical_data(
E   TypeError: PolygonProvider.get_historical_data() takes from 2 to 3 positional arguments but 4 were given
___________________ TestPolygonProvider.test_invalid_symbol ____________________
src/shared/data_providers/polygon_provider.py:167: in get_current_price
    return asyncio.run(self._get_current_price_async(symbol))
/usr/local/lib/python3.10/asyncio/runners.py:33: in run
    raise RuntimeError(
E   RuntimeError: asyncio.run() cannot be called from a running event loop

During handling of the above exception, another exception occurred:
tests/integration/test_polygon_provider.py:78: in test_invalid_symbol
    await polygon_provider.get_current_price(symbol)
src/shared/data_providers/polygon_provider.py:170: in get_current_price
    raise ProviderError(str(e), "polygon")
E   src.shared.data_providers.base_provider.ProviderError: polygon error: asyncio.run() cannot be called from a running event loop
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:40.202065", "level": "ERROR", "logger": "src.shared.data_providers.polygon_provider", "message": "Polygon failed for : asyncio.run() cannot be called from a running event loop", "module": "polygon_provider", "function": "get_current_price", "line": 169, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.shared.data_providers.polygon_provider:polygon_provider.py:169 Polygon failed for : asyncio.run() cannot be called from a running event loop
______ TestPolygonProvider.test_historical_data_with_different_periods[1] ______
src/shared/data_providers/polygon_provider.py:102: in get_historical_data
    return asyncio.run(self._get_historical_data_async(symbol, days))
/usr/local/lib/python3.10/asyncio/runners.py:33: in run
    raise RuntimeError(
E   RuntimeError: asyncio.run() cannot be called from a running event loop

During handling of the above exception, another exception occurred:
tests/integration/test_polygon_provider.py:92: in test_historical_data_with_different_periods
    historical_data = await polygon_provider.get_historical_data(
src/shared/data_providers/polygon_provider.py:105: in get_historical_data
    raise ProviderError(str(e), "polygon")
E   src.shared.data_providers.base_provider.ProviderError: polygon error: asyncio.run() cannot be called from a running event loop
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:40.246452", "level": "ERROR", "logger": "src.shared.data_providers.polygon_provider", "message": "Polygon failed for GOOGL: asyncio.run() cannot be called from a running event loop", "module": "polygon_provider", "function": "get_historical_data", "line": 104, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.shared.data_providers.polygon_provider:polygon_provider.py:104 Polygon failed for GOOGL: asyncio.run() cannot be called from a running event loop
______ TestPolygonProvider.test_historical_data_with_different_periods[7] ______
src/shared/data_providers/polygon_provider.py:102: in get_historical_data
    return asyncio.run(self._get_historical_data_async(symbol, days))
/usr/local/lib/python3.10/asyncio/runners.py:33: in run
    raise RuntimeError(
E   RuntimeError: asyncio.run() cannot be called from a running event loop

During handling of the above exception, another exception occurred:
tests/integration/test_polygon_provider.py:92: in test_historical_data_with_different_periods
    historical_data = await polygon_provider.get_historical_data(
src/shared/data_providers/polygon_provider.py:105: in get_historical_data
    raise ProviderError(str(e), "polygon")
E   src.shared.data_providers.base_provider.ProviderError: polygon error: asyncio.run() cannot be called from a running event loop
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:40.277893", "level": "ERROR", "logger": "src.shared.data_providers.polygon_provider", "message": "Polygon failed for GOOGL: asyncio.run() cannot be called from a running event loop", "module": "polygon_provider", "function": "get_historical_data", "line": 104, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.shared.data_providers.polygon_provider:polygon_provider.py:104 Polygon failed for GOOGL: asyncio.run() cannot be called from a running event loop
_____ TestPolygonProvider.test_historical_data_with_different_periods[30] ______
src/shared/data_providers/polygon_provider.py:102: in get_historical_data
    return asyncio.run(self._get_historical_data_async(symbol, days))
/usr/local/lib/python3.10/asyncio/runners.py:33: in run
    raise RuntimeError(
E   RuntimeError: asyncio.run() cannot be called from a running event loop

During handling of the above exception, another exception occurred:
tests/integration/test_polygon_provider.py:92: in test_historical_data_with_different_periods
    historical_data = await polygon_provider.get_historical_data(
src/shared/data_providers/polygon_provider.py:105: in get_historical_data
    raise ProviderError(str(e), "polygon")
E   src.shared.data_providers.base_provider.ProviderError: polygon error: asyncio.run() cannot be called from a running event loop
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:40.311383", "level": "ERROR", "logger": "src.shared.data_providers.polygon_provider", "message": "Polygon failed for GOOGL: asyncio.run() cannot be called from a running event loop", "module": "polygon_provider", "function": "get_historical_data", "line": 104, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.shared.data_providers.polygon_provider:polygon_provider.py:104 Polygon failed for GOOGL: asyncio.run() cannot be called from a running event loop
_____ TestPolygonProvider.test_historical_data_with_different_periods[90] ______
src/shared/data_providers/polygon_provider.py:102: in get_historical_data
    return asyncio.run(self._get_historical_data_async(symbol, days))
/usr/local/lib/python3.10/asyncio/runners.py:33: in run
    raise RuntimeError(
E   RuntimeError: asyncio.run() cannot be called from a running event loop

During handling of the above exception, another exception occurred:
tests/integration/test_polygon_provider.py:92: in test_historical_data_with_different_periods
    historical_data = await polygon_provider.get_historical_data(
src/shared/data_providers/polygon_provider.py:105: in get_historical_data
    raise ProviderError(str(e), "polygon")
E   src.shared.data_providers.base_provider.ProviderError: polygon error: asyncio.run() cannot be called from a running event loop
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:40.340327", "level": "ERROR", "logger": "src.shared.data_providers.polygon_provider", "message": "Polygon failed for GOOGL: asyncio.run() cannot be called from a running event loop", "module": "polygon_provider", "function": "get_historical_data", "line": 104, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.shared.data_providers.polygon_provider:polygon_provider.py:104 Polygon failed for GOOGL: asyncio.run() cannot be called from a running event loop
_________ TestSupabaseIntegration.test_supabase_client_initialization __________
tests/integration/test_supabase_integration.py:13: in test_supabase_client_initialization
    assert client is not None, "Supabase client should be initialized"
E   AssertionError: Supabase client should be initialized
E   assert None is not None
___________ TestSupabaseIntegration.test_supabase_connection_status ____________
tests/integration/test_supabase_integration.py:20: in test_supabase_connection_status
    assert connection_status is True, "Supabase connection test should pass"
E   AssertionError: Supabase connection test should pass
E   assert False is True
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:40.381280", "level": "ERROR", "logger": "src.database.supabase_client", "message": "Supabase connection test failed: No client available", "module": "supabase_client", "function": "test_supabase_connection", "line": 223, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.database.supabase_client:supabase_client.py:223 Supabase connection test failed: No client available
____________ TestSupabaseIntegration.test_supabase_crud_operations _____________
tests/integration/test_supabase_integration.py:28: in test_supabase_crud_operations
    assert insert_result is not None, "Data insertion should succeed"
E   AssertionError: Data insertion should succeed
E   assert None is not None
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:40.390125", "level": "ERROR", "logger": "src.database.supabase_client", "message": "Supabase client not initialized", "module": "supabase_client", "function": "insert_data", "line": 146, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.database.supabase_client:supabase_client.py:146 Supabase client not initialized
_____________ TestSupabaseIntegration.test_supabase_error_handling _____________
tests/integration/test_supabase_integration.py:60: in test_supabase_error_handling
    assert invalid_insert_result is not None, "Invalid data insertion should not raise unhandled exception"
E   AssertionError: Invalid data insertion should not raise unhandled exception
E   assert None is not None
----------------------------- Captured stderr call -----------------------------
{"timestamp": "2025-08-24T17:40:40.396194", "level": "ERROR", "logger": "src.database.supabase_client", "message": "Supabase client not initialized", "module": "supabase_client", "function": "execute_query", "line": 109, "process_id": 1, "thread_id": 135570410797952}
{"timestamp": "2025-08-24T17:40:40.396609", "level": "ERROR", "logger": "src.database.supabase_client", "message": "Supabase client not initialized", "module": "supabase_client", "function": "insert_data", "line": 146, "process_id": 1, "thread_id": 135570410797952}
------------------------------ Captured log call -------------------------------
ERROR    src.database.supabase_client:supabase_client.py:109 Supabase client not initialized
ERROR    src.database.supabase_client:supabase_client.py:146 Supabase client not initialized
=============================== warnings summary ===============================
src/core/security.py:151
  /app/src/core/security.py:151: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('*', pre=True)

src/core/security.py:183
  /app/src/core/security.py:183: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('password')

src/bot/pipeline/commands/ask/stages/models.py:36
  /app/src/bot/pipeline/commands/ask/stages/models.py:36: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('intent')

src/bot/pipeline/commands/ask/stages/models.py:48
  /app/src/bot/pipeline/commands/ask/stages/models.py:48: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('symbols', each_item=True)

src/bot/pipeline/commands/ask/stages/models.py:59
  /app/src/bot/pipeline/commands/ask/stages/models.py:59: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('symbols')

../usr/local/lib/python3.10/site-packages/gotrue/types.py:679: 19 warnings
  /usr/local/lib/python3.10/site-packages/gotrue/types.py:679: PydanticDeprecatedSince20: The `update_forward_refs` method is deprecated; use `model_rebuild` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    model.update_forward_refs()

../usr/local/lib/python3.10/site-packages/pydantic/main.py:1262: 19 warnings
  /usr/local/lib/python3.10/site-packages/pydantic/main.py:1262: PydanticDeprecatedSince20: The `update_forward_refs` method is deprecated; use `model_rebuild` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    warnings.warn(

src/database/supabase_client.py:17
  /app/src/database/supabase_client.py:17: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('url')

tests/test_ai_chat_processor.py::test_ai_chat_processor_successful_flow
  /app/src/bot/pipeline/commands/ask/stages/ai_chat_processor.py:1026: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    return validated_result.dict()

tests/test_ai_chat_processor.py::test_ai_chat_processor_successful_flow
  /usr/local/lib/python3.10/site-packages/pydantic/main.py:979: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    warnings.warn('The `dict` method is deprecated; use `model_dump` instead.', DeprecationWarning)

tests/test_supabase_connection.py::test_supabase_connection
  /usr/local/lib/python3.10/site-packages/_pytest/python.py:198: PytestReturnNotNoneWarning: Expected None, but tests/test_supabase_connection.py::test_supabase_connection returned False, which will be an error in a future version of pytest.  Did you mean to use `assert` instead of `return`?
    warnings.warn(

tests/integration/test_supabase_integration.py::test_supabase_connection
  /usr/local/lib/python3.10/site-packages/_pytest/python.py:198: PytestReturnNotNoneWarning: Expected None, but tests/integration/test_supabase_integration.py::test_supabase_connection returned False, which will be an error in a future version of pytest.  Did you mean to use `assert` instead of `return`?
    warnings.warn(

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html

🤖 AI Trading Bot Test Summary 🤖
----------------------------------------
Python Version: 3.10.18
Supabase Configured: Yes
Discord Bot Token: Configured
----------------------------------------

---------- coverage: platform linux, python 3.10.18-final-0 ----------
Name                                                           Stmts   Miss  Cover   Missing
--------------------------------------------------------------------------------------------
src/__init__.py                                                    0      0   100%
src/analysis/__init__.py                                           2      0   100%
src/analysis/ai/__init__.py                                        0      0   100%
src/analysis/ai/calculators/__init__.py                            0      0   100%
src/analysis/ai/calculators/sentiment_calculator.py               38     38     0%   1-102
src/analysis/ai/enhancement_strategy.py                           45     18    60%   38, 105-130, 156, 208-237, 246
src/analysis/ai/recommendation_engine.py                         266     41    85%   72, 99-101, 141, 148, 150, 171, 182, 192-195, 202, 204, 224, 226, 232, 234, 252, 254, 261-266, 272, 274, 310, 328, 376, 381, 409, 419, 421, 433-436, 443
src/analysis/fundamental/__init__.py                               0      0   100%
src/analysis/fundamental/calculators/__init__.py                   0      0   100%
src/analysis/fundamental/calculators/growth_calculator.py        114    114     0%   1-283
src/analysis/fundamental/calculators/pe_calculator.py             62     62     0%   1-175
src/analysis/fundamental/metrics.py                              105     63    40%   15-43, 49, 51, 53, 61, 65, 75, 78-83, 89, 91, 94-97, 102-111, 116-123, 128-135, 140-149
src/analysis/orchestration/__init__.py                             0      0   100%
src/analysis/orchestration/analysis_orchestrator.py              186    186     0%   1-282
src/analysis/orchestration/enhancement_strategy.py                48     48     0%   1-199
src/analysis/risk/__init__.py                                      0      0   100%
src/analysis/risk/assessment.py                                   93     93     0%   1-166
src/analysis/risk/calculators/__init__.py                          0      0   100%
src/analysis/risk/calculators/beta_calculator.py                  95     95     0%   1-216
src/analysis/risk/calculators/volatility_calculator.py            80     80     0%   1-178
src/analysis/technical/__init__.py                                 0      0   100%
src/analysis/technical/calculators/__init__.py                     0      0   100%
src/analysis/technical/calculators/macd_calculator.py            100    100     0%   1-235
src/analysis/technical/calculators/rsi_calculator.py              79     79     0%   1-194
src/analysis/technical/indicators.py                             106     85    20%   16-40, 50-77, 85-100, 109-133, 142-161, 166-180, 185-215
src/analysis/utils/__init__.py                                     0      0   100%
src/analysis/utils/data_validators.py                            124    124     0%   1-236
src/api/__init__.py                                                0      0   100%
src/api/analytics/__init__.py                                      0      0   100%
src/api/config.py                                                 29     29     0%   8-68
src/api/data/__init__.py                                           0      0   100%
src/api/data/cache.py                                             85     85     0%   1-263
src/api/data/market_data_service.py                               62     23    63%   89, 117-119, 137-174, 196-208
src/api/data/providers/__init__.py                                 0      0   100%
src/api/data/providers/base.py                                    91     54    41%   53-57, 70, 90, 103-111, 124-127, 133-135, 139-146, 167-169, 181-194, 213-225
src/api/data/providers/data_source_manager.py                    619    325    47%   80, 86-88, 114-117, 131-133, 164-165, 174-181, 185, 212, 216, 262, 266-270, 287-346, 350-361, 371-419, 436-438, 460, 504-506, 548-551, 563-585, 590-600, 637-638, 647-648, 652-663, 691-692, 705-716, 736-739, 777-779, 783-861, 870-895, 899-921, 929-931, 935-944, 948-963, 970-984, 994-995, 1020-1021, 1028-1029, 1039-1040, 1070-1072, 1082-1096, 1103-1105, 1116-1143, 1166, 1183, 1187, 1198-1199, 1203-1204, 1208-1221, 1229-1230, 1234-1235
src/api/data/providers/finnhub.py                                 54     41    24%   34-38, 54-80, 99-149, 162-184
src/api/data/providers/polygon.py                                 83     83     0%   1-235
src/api/main.py                                                   41     41     0%   1-117
src/api/middleware/__init__.py                                     0      0   100%
src/api/middleware/security.py                                    75     75     0%   1-172
src/api/routes/__init__.py                                         0      0   100%
src/api/routes/analytics.py                                      104    104     0%   1-237
src/api/routes/auth.py                                            73     73     0%   1-238
src/api/routes/feedback.py                                        26     26     0%   1-68
src/api/routes/health.py                                          31     31     0%   1-71
src/api/routes/market_data.py                                     92     92     0%   1-274
src/api/routes/metrics.py                                         20     20     0%   1-43
src/api/webhooks/__init__.py                                       0      0   100%
src/bot/__init__.py                                                0      0   100%
src/bot/client.py                                                315    315     0%   6-647
src/bot/commands/__init__.py                                       1      1     0%   6
src/bot/commands/analyze.py                                      120    120     0%   6-256
src/bot/events/__init__.py                                         0      0   100%
src/bot/pipeline/__init__.py                                       0      0   100%
src/bot/pipeline/commands/__init__.py                              0      0   100%
src/bot/pipeline/commands/analyze/__init__.py                      0      0   100%
src/bot/pipeline/commands/analyze/pipeline.py                    128    128     0%   7-303
src/bot/pipeline/commands/analyze/stages/__init__.py               0      0   100%
src/bot/pipeline/commands/analyze/stages/report_template.py      104    104     0%   9-255
src/bot/pipeline/commands/ask/__init__.py                          0      0   100%
src/bot/pipeline/commands/ask/config.py                          285    132    54%   50-51, 55, 59-62, 82, 94-100, 183-185, 189-211, 216-271, 279, 282, 285, 288, 293, 303, 313, 317, 320, 323, 326, 331, 333, 335, 343-344, 353-354, 356-358, 366-367, 369-371, 381-382, 386-409, 413-436, 459-460, 464-465, 469, 492-506, 521, 526
src/bot/pipeline/commands/ask/pipeline.py                        158     14    91%   97, 106, 246-259, 274, 326, 331
src/bot/pipeline/commands/ask/stages/__init__.py                   5      0   100%
src/bot/pipeline/commands/ask/stages/ai_cache.py                 135     57    58%   17-19, 46, 48, 50, 65-68, 94, 121-136, 143, 171-173, 177-198, 202-229, 249, 254
src/bot/pipeline/commands/ask/stages/ai_chat_processor.py        845    648    23%   16-276, 285-287, 297-348, 354-356, 409-412, 421-426, 433-441, 458-474, 494-495, 519-522, 537, 547-722, 738, 742, 768, 799, 854-855, 876-880, 897-900, 921-923, 937-982, 989-990, 999-1000, 1015-1023, 1041, 1060-1131, 1135-1219, 1223-1248, 1252-1276, 1280-1309, 1313-1369, 1373-1425, 1429-1523, 1533-1541, 1546-1580, 1584-1618, 1622-1655, 1659-1698, 1716, 1720, 1730-1739, 1743-1755
src/bot/pipeline/commands/ask/stages/ask_sections.py             484    484     0%   30-964
src/bot/pipeline/commands/ask/stages/depth_style_analyzer.py     267    267     0%   8-567
src/bot/pipeline/commands/ask/stages/discord_formatter.py        148    148     0%   8-373
src/bot/pipeline/commands/ask/stages/models.py                    34      5    85%   52, 56, 69-71
src/bot/pipeline/commands/ask/stages/pipeline_sections.py        192    137    29%   64-67, 71-72, 76-77, 82-107, 112-130, 133-210, 215-242, 246-248, 253-260, 278-284, 290-305
src/bot/pipeline/commands/ask/stages/prompts.py                    7      0   100%
src/bot/pipeline/commands/ask/stages/query_analyzer.py           231    173    25%   62-63, 67, 102-134, 150-168, 177-210, 216-227, 233-246, 251-304, 310-326, 331-348, 353-381, 386-395, 400-407, 412-431, 437-444, 450-459
src/bot/pipeline/commands/ask/stages/quick_commands.py           177    177     0%   8-398
src/bot/pipeline/commands/ask/stages/response_audit.py           126    126     0%   1-273
src/bot/pipeline/commands/ask/stages/response_templates.py       297    203    32%   52-55, 108-110, 113, 118, 124-126, 130, 279-330, 334-351, 355-378, 382-409, 414-424, 438-555, 570, 595-671, 678-711, 718-749
src/bot/pipeline/commands/ask/stages/response_validator.py       139    139     0%   7-281
src/bot/pipeline/commands/ask/stages/symbol_validator.py          88     67    24%   31-32, 36-50, 62-92, 96-116, 120-135, 147-151, 155-159, 165-176
src/bot/pipeline/commands/ask/test_modular_system.py              28     28     0%   7-59
src/bot/pipeline/commands/watchlist/__init__.py                    0      0   100%
src/bot/pipeline/commands/watchlist/stages/__init__.py             0      0   100%
src/bot/pipeline/core/__init__.py                                  0      0   100%
src/bot/pipeline/core/context_manager.py                         120     39    68%   45-54, 135, 141-152, 156-163, 167-169, 173, 177-188, 192-206, 210, 224-231
src/bot/pipeline/core/pipeline_engine.py                         170    130    24%   40-44, 48-91, 101-114, 119, 123-160, 164-175, 187-191, 195, 199, 203-228, 232-249, 257-272, 276, 292, 296-297, 301-302, 306-307, 311-312, 316-317, 321
src/bot/pipeline/monitoring/__init__.py                            0      0   100%
src/bot/pipeline/shared/__init__.py                                0      0   100%
src/bot/pipeline/shared/data_collectors/__init__.py                0      0   100%
src/bot/pipeline/shared/formatters/__init__.py                     0      0   100%
src/bot/pipeline/shared/validators/__init__.py                     0      0   100%
src/bot/pipeline/test_pipeline.py                                 58     58     0%   8-128
src/bot/pipeline_framework.py                                     63     63     0%   1-204
src/bot/utils/__init__.py                                          0      0   100%
src/core/__init__.py                                               9      1    89%   43
src/core/advanced_security.py                                     75      4    95%   67, 137, 231, 238
src/core/config.py                                                69     24    65%   24, 28, 32, 36, 42-43, 47, 52, 56, 60, 64, 69, 73, 78-81, 85, 89, 94, 98, 102, 106, 118
src/core/config_manager.py                                        57      7    88%   128-129, 155, 159, 163, 166, 198
src/core/enums/__init__.py                                         0      0   100%
src/core/enums/stock_analysis.py                                  30     30     0%   1-69
src/core/exceptions.py                                           189     95    50%   55-66, 70-82, 86-105, 109, 126-130, 144-150, 164-170, 182, 194, 210, 212, 226, 238-242, 255-259, 273, 288-292, 304, 316, 328-332, 345, 357, 370-374, 388, 395, 402, 415, 427-431, 464-487, 496, 510, 518, 526
src/core/feedback_mechanism.py                                   233    233     0%   1-526
src/core/formatting/__init__.py                                    4      0   100%
src/core/formatting/analysis_template.py                         172    132    23%   34-37, 43-111, 115-120, 124-137, 141-148, 152-166, 181-227, 242-282, 286-295
src/core/formatting/response_templates.py                         43     17    60%   28-30, 67-75, 98-105, 128-135, 153-177, 190-199, 209-230
src/core/formatting/technical_analysis.py                         82     59    28%   26-38, 54, 67, 80-126, 144-153, 177-189, 193
src/core/formatting/text_formatting.py                            41     41     0%   1-121
src/core/logger.py                                               125     52    58%   24, 52, 56, 71-72, 81-85, 97, 114, 131, 154-162, 188, 201-214, 259-261, 268, 275-329, 341, 345
src/core/metrics_tracker.py                                       77     50    35%   30-34, 59-62, 83-139, 160-172, 181, 204-209, 217-236
src/core/monitoring.py                                           189    120    37%   55-80, 88-97, 102-112, 117-127, 132-142, 147-157, 170-191, 207, 231-247, 251-270, 274, 320-328, 352-394, 398-408, 417, 431-439
src/core/pipeline_engine.py                                       80     45    44%   44, 48, 76-79, 92-133, 156-160, 172, 185-222, 237, 258-260, 269
src/core/response_generator.py                                    80     26    68%   82-89, 93, 101-132, 148-156, 165, 180-186, 191, 211, 217, 223
src/core/risk_management/__init__.py                               2      2     0%   8-16
src/core/risk_management/compliance_framework.py                 129    129     0%   1-459
src/core/secrets.py                                               49     19    61%   62, 74, 87-88, 102-103, 118-127, 140, 147-161, 168-170
src/core/security.py                                             107     56    48%   19, 23, 28, 35-43, 47, 68, 93, 106, 120, 130, 143-144, 156-158, 188-196, 212-216, 229-236, 249-262, 274-280, 292-296
src/core/utils.py                                                 87     36    59%   9, 23-29, 47, 65-68, 98-103, 108, 111, 116, 143-146, 152-159, 172-180, 222-223, 229-230
src/core/validation/__init__.py                                    2      0   100%
src/core/validation/financial_validator.py                       251    210    16%   36-45, 73-167, 185-233, 247-318, 331-419, 438-473
src/data/__init__.py                                               5      0   100%
src/data/cache/__init__.py                                         2      0   100%
src/data/cache/manager.py                                        261    183    30%   55, 60, 64-65, 78-88, 92-101, 105-109, 113-114, 118-123, 127-139, 143-146, 159-161, 166, 171, 176, 181, 185, 206, 219, 223-245, 249-264, 268-282, 286-297, 301-302, 306-308, 322-335, 339-349, 360-385, 389-418, 424-454, 458-472
src/data/models/__init__.py                                        6      5    17%   8-13
src/data/models/indicators.py                                     26     26     0%   5-45
src/data/models/stock_data.py                                     84      0   100%
src/data/providers/__init__.py                                     7      0   100%
src/data/providers/base.py                                       145     80    45%   72, 79-81, 85-98, 110-120, 124, 142-182, 186-206, 210-212, 225, 229, 245-248, 253-265, 268, 271
src/data/providers/config.py                                      56     56     0%   8-149
src/data/providers/manager.py                                    138    138     0%   9-331
src/database/__init__.py                                           0      0   100%
src/database/config.py                                            28     28     0%   8-60
src/database/connection.py                                        98     98     0%   1-176
src/database/migrations/__init__.py                                0      0   100%
src/database/migrations/env.py                                    33     33     0%   1-98
src/database/models/__init__.py                                    5      5     0%   1-6
src/database/models/alerts.py                                     37     37     0%   1-86
src/database/models/analysis.py                                   35     35     0%   1-83
src/database/models/interactions.py                               29     29     0%   1-73
src/database/models/market_data.py                                30     30     0%   1-63
src/database/models/users.py                                      28     28     0%   1-67
src/database/supabase_client.py                                   89     36    60%   23, 73-74, 81-82, 112-132, 149-165, 184-211, 226-233
src/modules/__init__.py                                            0      0   100%
src/modules/crypto/__init__.py                                     0      0   100%
src/modules/forex/__init__.py                                      0      0   100%
src/modules/options_flow/__init__.py                               0      0   100%
src/modules/trading_signals/__init__.py                            2      2     0%   9-19
src/modules/trading_signals/signals.py                           119    119     0%   1-255
src/modules/watchlist/__init__.py                                  2      2     0%   7-9
src/modules/watchlist/watchlist_manager.py                       118    118     0%   1-299
src/shared/data_providers/__init__.py                              6      0   100%
src/shared/data_providers/aggregator.py                           84     53    37%   60-61, 68-72, 81, 94-120, 136-162, 174-180
src/shared/data_providers/alpha_vantage.py                       133    103    23%   55-62, 66, 70, 74, 90-158, 172-268, 272-277
src/shared/data_providers/base_provider.py                        20      2    90%   35, 39
src/shared/data_providers/polygon_provider.py                    115     79    31%   26, 37, 41, 45-74, 78-96, 109-161, 174-202, 206, 215-226
src/shared/data_providers/yfinance_provider.py                    98     73    26%   25-32, 36, 40, 44, 52-53, 57-120, 124-153, 157-172
src/shared/error_handling/__init__.py                              4      0   100%
src/shared/error_handling/fallback.py                            120     83    31%   19-26, 56-78, 85-100, 104-110, 134-187, 193, 207-235
src/shared/error_handling/logging.py                             134     70    48%   20-26, 49, 51, 53, 57, 59, 61, 65-66, 101, 148, 171-183, 187, 195-199, 202-212, 215-245, 254-290, 300-315
src/shared/error_handling/retry.py                               165    130    21%   20-28, 52-64, 68-73, 78-93, 97-115, 137-195, 217-272, 276, 299-338, 356-417
src/templates/__init__.py                                          0      0   100%
src/templates/analysis_response.py                               110    110     0%   1-247
src/templates/ask.py                                             291    291     0%   9-751
src/templates/core.py                                             30     30     0%   5-137
--------------------------------------------------------------------------------------------
TOTAL                                                          12628   9591    24%

=========================== short test summary info ============================
FAILED tests/test_ai_chat_processor.py::test_ai_chat_processor_successful_flow
FAILED tests/test_ai_chat_processor.py::test_ai_chat_processor_no_ai_client
FAILED tests/test_ai_chat_processor.py::test_ai_chat_processor_json_parsing_error
FAILED tests/test_ai_chat_processor.py::test_processor_function_no_query - Ty...
FAILED tests/test_ai_chat_processor.py::test_ai_response_validation - Attribu...
FAILED tests/test_database_connection.py::test_database_connection - sqlalche...
FAILED tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_empty_query
FAILED tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_error - ...
FAILED tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_timeout
FAILED tests/test_multi_symbol_integration.py::test_multi_symbol_market_overview
FAILED tests/test_multi_symbol_integration.py::test_tech_sector_analysis - As...
FAILED tests/test_multi_symbol_integration.py::test_response_contains_required_elements
FAILED tests/test_multi_symbol_integration.py::test_discord_compatible_response_length
FAILED tests/test_supabase_connection.py::test_supabase_client_initialization
FAILED tests/test_supabase_connection.py::test_supabase_connection_status - A...
FAILED tests/test_supabase_connection.py::test_supabase_insert_and_query - As...
FAILED tests/test_supabase_connection.py::test_supabase_update - AssertionErr...
FAILED tests/integration/test_market_data_service.py::TestMarketDataService::test_get_historical_data
FAILED tests/integration/test_market_data_service.py::TestMarketDataService::test_invalid_symbol
FAILED tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[1]
FAILED tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[7]
FAILED tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[30]
FAILED tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[90]
FAILED tests/integration/test_polygon_provider.py::TestPolygonProvider::test_get_current_price
FAILED tests/integration/test_polygon_provider.py::TestPolygonProvider::test_get_historical_data
FAILED tests/integration/test_polygon_provider.py::TestPolygonProvider::test_invalid_symbol
FAILED tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[1]
FAILED tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[7]
FAILED tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[30]
FAILED tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[90]
FAILED tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_client_initialization
FAILED tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_connection_status
FAILED tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_crud_operations
FAILED tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_error_handling
ERROR tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_get_current_price
ERROR tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_get_historical_data
ERROR tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_invalid_symbol
ERROR tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[1]
ERROR tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[7]
ERROR tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[30]
ERROR tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[90]
======= 34 failed, 28 passed, 48 warnings, 7 errors in 73.43s (0:01:13) ========

