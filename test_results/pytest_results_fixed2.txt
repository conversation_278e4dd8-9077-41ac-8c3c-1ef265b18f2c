 Container tradingview-automation-redis-1  Running
============================= test session starts ==============================
platform linux -- Python 3.10.18, pytest-7.4.3, pluggy-1.6.0 -- /usr/local/bin/python
cachedir: .pytest_cache
rootdir: /app
configfile: pytest.ini
plugins: cov-4.1.0, asyncio-0.23.2, anyio-3.7.1
asyncio: mode=strict
collecting ... {"timestamp": "2025-08-24T17:39:09.683222", "level": "DEBUG", "logger": "passlib.registry", "message": "registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>", "module": "registry", "function": "register_crypt_handler", "line": 296, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:09.687057", "level": "DEBUG", "logger": "passlib.utils.compat", "message": "loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>", "module": "__init__", "function": "__getattr__", "line": 449, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:09.687371", "level": "DEBUG", "logger": "passlib.utils.compat", "message": "loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>", "module": "__init__", "function": "__getattr__", "line": 449, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:09.687580", "level": "DEBUG", "logger": "passlib.utils.compat", "message": "loaded lazy attr 'BytesIO': <class '_io.BytesIO'>", "module": "__init__", "function": "__getattr__", "line": 449, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:10.613761", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:10.614250", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:10.614537", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:10.614704", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:10.684227", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.config", "message": "\ud83d\udd27 Configuration loaded from environment", "module": "config", "function": "__init__", "line": 80, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:10.684762", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.config", "message": "\u2705 Configuration validation passed", "module": "config", "function": "__init__", "line": 84, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:10.685479", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\ud83d\udd27 Attempting Redis connection to redis:6379", "module": "ai_cache", "function": "__init__", "line": 52, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:10.690214", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\u2705 Redis cache connected successfully", "module": "ai_cache", "function": "__init__", "line": 64, "correlation_id": null}
{"timestamp": "2025-08-24T17:39:12.159042", "level": "INFO", "logger": "src.shared.data_providers.aggregator", "message": "Initialized data provider: alpha_vantage", "module": "aggregator", "function": "_initialize_providers", "line": 66, "process_id": 1, "thread_id": 126246583757696}
{"timestamp": "2025-08-24T17:39:12.159638", "level": "INFO", "logger": "src.shared.data_providers.aggregator", "message": "Initialized data provider: yfinance", "module": "aggregator", "function": "_initialize_providers", "line": 66, "process_id": 1, "thread_id": 126246583757696}
{"timestamp": "2025-08-24T17:39:13.828943", "level": "ERROR", "logger": "src.database.supabase_client", "message": "Failed to initialize Supabase client: Invalid API key", "module": "supabase_client", "function": "_create_supabase_client", "line": 85, "process_id": 1, "thread_id": 126246583757696}
collected 59 items / 2 errors

==================================== ERRORS ====================================
_____________ ERROR collecting tests/test_recommendation_engine.py _____________
/usr/local/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
/usr/local/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
/usr/local/lib/python3.10/site-packages/_pytest/doctest.py:567: in collect
    module = import_path(
/usr/local/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/local/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
/usr/local/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/test_recommendation_engine.py:4: in <module>
    from src.analysis.ai.recommendation_engine import AIRecommendationEngine, Recommendation
src/analysis/ai/recommendation_engine.py:5: in <module>
    from src.data.models.stock_data import AnalysisResult
src/data/__init__.py:9: in <module>
    from .providers import (
src/data/providers/__init__.py:17: in <module>
    from src.shared.data_providers.yfinance import YFinanceProvider
E   ModuleNotFoundError: No module named 'src.shared.data_providers.yfinance'
_____________ ERROR collecting tests/test_recommendation_engine.py _____________
ImportError while importing test module '/app/tests/test_recommendation_engine.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/local/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
/usr/local/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/local/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
/usr/local/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/test_recommendation_engine.py:4: in <module>
    from src.analysis.ai.recommendation_engine import AIRecommendationEngine, Recommendation
src/analysis/ai/recommendation_engine.py:5: in <module>
    from src.data.models.stock_data import AnalysisResult
src/data/__init__.py:9: in <module>
    from .providers import (
src/data/providers/__init__.py:17: in <module>
    from src.shared.data_providers.yfinance import YFinanceProvider
E   ModuleNotFoundError: No module named 'src.shared.data_providers.yfinance'
=============================== warnings summary ===============================
src/core/security.py:151
  /app/src/core/security.py:151: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('*', pre=True)

src/core/security.py:183
  /app/src/core/security.py:183: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('password')

src/bot/pipeline/commands/ask/stages/models.py:36
  /app/src/bot/pipeline/commands/ask/stages/models.py:36: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('intent')

src/bot/pipeline/commands/ask/stages/models.py:48
  /app/src/bot/pipeline/commands/ask/stages/models.py:48: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('symbols', each_item=True)

src/bot/pipeline/commands/ask/stages/models.py:59
  /app/src/bot/pipeline/commands/ask/stages/models.py:59: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('symbols')

../usr/local/lib/python3.10/site-packages/gotrue/types.py:679: 19 warnings
  /usr/local/lib/python3.10/site-packages/gotrue/types.py:679: PydanticDeprecatedSince20: The `update_forward_refs` method is deprecated; use `model_rebuild` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    model.update_forward_refs()

../usr/local/lib/python3.10/site-packages/pydantic/main.py:1262: 19 warnings
  /usr/local/lib/python3.10/site-packages/pydantic/main.py:1262: PydanticDeprecatedSince20: The `update_forward_refs` method is deprecated; use `model_rebuild` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    warnings.warn(

src/database/supabase_client.py:17
  /app/src/database/supabase_client.py:17: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
    @validator('url')

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html

🤖 AI Trading Bot Test Summary 🤖
----------------------------------------
Python Version: 3.10.18
Supabase Configured: Yes
Discord Bot Token: Configured
----------------------------------------

---------- coverage: platform linux, python 3.10.18-final-0 ----------
Name                                                           Stmts   Miss  Cover   Missing
--------------------------------------------------------------------------------------------
src/__init__.py                                                    0      0   100%
src/analysis/__init__.py                                           2      0   100%
src/analysis/ai/__init__.py                                        0      0   100%
src/analysis/ai/calculators/__init__.py                            0      0   100%
src/analysis/ai/calculators/sentiment_calculator.py               38     38     0%   1-102
src/analysis/ai/enhancement_strategy.py                           45     18    60%   38, 105-130, 156, 208-237, 246
src/analysis/ai/recommendation_engine.py                         266    262     2%   6-445
src/analysis/fundamental/__init__.py                               0      0   100%
src/analysis/fundamental/calculators/__init__.py                   0      0   100%
src/analysis/fundamental/calculators/growth_calculator.py        114    114     0%   1-283
src/analysis/fundamental/calculators/pe_calculator.py             62     62     0%   1-175
src/analysis/fundamental/metrics.py                              105    105     0%   1-149
src/analysis/orchestration/__init__.py                             0      0   100%
src/analysis/orchestration/analysis_orchestrator.py              186    186     0%   1-282
src/analysis/orchestration/enhancement_strategy.py                48     48     0%   1-199
src/analysis/risk/__init__.py                                      0      0   100%
src/analysis/risk/assessment.py                                   93     93     0%   1-166
src/analysis/risk/calculators/__init__.py                          0      0   100%
src/analysis/risk/calculators/beta_calculator.py                  95     95     0%   1-216
src/analysis/risk/calculators/volatility_calculator.py            80     80     0%   1-178
src/analysis/technical/__init__.py                                 0      0   100%
src/analysis/technical/calculators/__init__.py                     0      0   100%
src/analysis/technical/calculators/macd_calculator.py            100    100     0%   1-235
src/analysis/technical/calculators/rsi_calculator.py              79     79     0%   1-194
src/analysis/technical/indicators.py                             106    106     0%   1-215
src/analysis/utils/__init__.py                                     0      0   100%
src/analysis/utils/data_validators.py                            124    124     0%   1-236
src/api/__init__.py                                                0      0   100%
src/api/analytics/__init__.py                                      0      0   100%
src/api/config.py                                                 29     29     0%   8-68
src/api/data/__init__.py                                           0      0   100%
src/api/data/cache.py                                             85     85     0%   1-263
src/api/data/market_data_service.py                               62     44    29%   39-65, 86-123, 137-174, 196-208
src/api/data/providers/__init__.py                                 0      0   100%
src/api/data/providers/base.py                                    91     54    41%   53-57, 70, 90, 103-111, 124-127, 133-135, 139-146, 167-169, 181-194, 213-225
src/api/data/providers/data_source_manager.py                    619    434    30%   80, 86-88, 114-117, 131-133, 164-165, 169-170, 174-181, 185, 212, 216, 220-235, 253-262, 266-270, 287-346, 350-361, 371-419, 432-487, 500-551, 563-585, 590-600, 637-638, 647-648, 652-663, 684-716, 720-739, 758-779, 783-861, 870-895, 899-921, 929-931, 935-944, 948-963, 970-984, 992-996, 1000-1096, 1100-1107, 1111-1112, 1116-1143, 1147, 1166, 1183, 1187, 1198-1199, 1203-1204, 1208-1221, 1229-1230, 1234-1235
src/api/data/providers/finnhub.py                                 54     41    24%   34-38, 54-80, 99-149, 162-184
src/api/data/providers/polygon.py                                 83     83     0%   1-235
src/api/main.py                                                   41     41     0%   1-117
src/api/middleware/__init__.py                                     0      0   100%
src/api/middleware/security.py                                    75     75     0%   1-172
src/api/routes/__init__.py                                         0      0   100%
src/api/routes/analytics.py                                      104    104     0%   1-237
src/api/routes/auth.py                                            73     73     0%   1-238
src/api/routes/feedback.py                                        26     26     0%   1-68
src/api/routes/health.py                                          31     31     0%   1-71
src/api/routes/market_data.py                                     92     92     0%   1-274
src/api/routes/metrics.py                                         20     20     0%   1-43
src/api/webhooks/__init__.py                                       0      0   100%
src/bot/__init__.py                                                0      0   100%
src/bot/client.py                                                315    315     0%   6-647
src/bot/commands/__init__.py                                       1      1     0%   6
src/bot/commands/analyze.py                                      120    120     0%   6-256
src/bot/events/__init__.py                                         0      0   100%
src/bot/pipeline/__init__.py                                       0      0   100%
src/bot/pipeline/commands/__init__.py                              0      0   100%
src/bot/pipeline/commands/analyze/__init__.py                      0      0   100%
src/bot/pipeline/commands/analyze/pipeline.py                    128    128     0%   7-303
src/bot/pipeline/commands/analyze/stages/__init__.py               0      0   100%
src/bot/pipeline/commands/analyze/stages/report_template.py      104    104     0%   9-255
src/bot/pipeline/commands/ask/__init__.py                          0      0   100%
src/bot/pipeline/commands/ask/config.py                          285    133    53%   50-51, 55, 59-62, 82, 94-100, 183-185, 189-211, 216-271, 279, 282, 285, 288, 293, 303, 313, 317, 320, 323, 326, 331, 333, 335, 343-344, 353-354, 356-358, 366-367, 369-371, 381-382, 386-409, 413-436, 440, 459-460, 464-465, 469, 492-506, 521, 526
src/bot/pipeline/commands/ask/pipeline.py                        158     94    41%   93-102, 106, 125-127, 138-150, 154-164, 187-321, 326, 331
src/bot/pipeline/commands/ask/stages/__init__.py                   5      0   100%
src/bot/pipeline/commands/ask/stages/ai_cache.py                 135     95    30%   17-19, 46, 48, 50, 65-68, 73-75, 79-89, 93-138, 142-173, 177-198, 202-229, 239, 244, 249, 254
src/bot/pipeline/commands/ask/stages/ai_chat_processor.py        845    798     6%   16-276, 285-287, 297-348, 354-356, 366-444, 449-756, 768, 772-775, 779-800, 804-823, 827-1048, 1056-1131, 1135-1219, 1223-1248, 1252-1276, 1280-1309, 1313-1369, 1373-1425, 1429-1523, 1533-1541, 1546-1580, 1584-1618, 1622-1655, 1659-1698, 1703-1755
src/bot/pipeline/commands/ask/stages/ask_sections.py             484    484     0%   30-964
src/bot/pipeline/commands/ask/stages/depth_style_analyzer.py     267    267     0%   8-567
src/bot/pipeline/commands/ask/stages/discord_formatter.py        148    148     0%   8-373
src/bot/pipeline/commands/ask/stages/models.py                    34     17    50%   39-46, 51-57, 62, 67-71
src/bot/pipeline/commands/ask/stages/pipeline_sections.py        192    137    29%   64-67, 71-72, 76-77, 82-107, 112-130, 133-210, 215-242, 246-248, 253-260, 278-284, 290-305
src/bot/pipeline/commands/ask/stages/prompts.py                    7      0   100%
src/bot/pipeline/commands/ask/stages/query_analyzer.py           231    173    25%   62-63, 67, 102-134, 150-168, 177-210, 216-227, 233-246, 251-304, 310-326, 331-348, 353-381, 386-395, 400-407, 412-431, 437-444, 450-459
src/bot/pipeline/commands/ask/stages/quick_commands.py           177    177     0%   8-398
src/bot/pipeline/commands/ask/stages/response_audit.py           126    126     0%   1-273
src/bot/pipeline/commands/ask/stages/response_templates.py       297    203    32%   52-55, 108-110, 113, 118, 124-126, 130, 279-330, 334-351, 355-378, 382-409, 414-424, 438-555, 570, 595-671, 678-711, 718-749
src/bot/pipeline/commands/ask/stages/response_validator.py       139    139     0%   7-281
src/bot/pipeline/commands/ask/stages/symbol_validator.py          88     67    24%   31-32, 36-50, 62-92, 96-116, 120-135, 147-151, 155-159, 165-176
src/bot/pipeline/commands/ask/test_modular_system.py              28     28     0%   7-59
src/bot/pipeline/commands/watchlist/__init__.py                    0      0   100%
src/bot/pipeline/commands/watchlist/stages/__init__.py             0      0   100%
src/bot/pipeline/core/__init__.py                                  0      0   100%
src/bot/pipeline/core/context_manager.py                         120     40    67%   45-54, 134-135, 141-152, 156-163, 167-169, 173, 177-188, 192-206, 210, 224-231
src/bot/pipeline/core/pipeline_engine.py                         170    130    24%   40-44, 48-91, 101-114, 119, 123-160, 164-175, 187-191, 195, 199, 203-228, 232-249, 257-272, 276, 292, 296-297, 301-302, 306-307, 311-312, 316-317, 321
src/bot/pipeline/monitoring/__init__.py                            0      0   100%
src/bot/pipeline/shared/__init__.py                                0      0   100%
src/bot/pipeline/shared/data_collectors/__init__.py                0      0   100%
src/bot/pipeline/shared/formatters/__init__.py                     0      0   100%
src/bot/pipeline/shared/validators/__init__.py                     0      0   100%
src/bot/pipeline/test_pipeline.py                                 58     58     0%   8-128
src/bot/pipeline_framework.py                                     63     63     0%   1-204
src/bot/utils/__init__.py                                          0      0   100%
src/core/__init__.py                                               9      1    89%   43
src/core/advanced_security.py                                     75     47    37%   37, 49-50, 66-69, 79-83, 99-101, 114-116, 132-137, 154-156, 169-192, 206-212, 224-250
src/core/config.py                                                69     24    65%   24, 28, 32, 36, 42-43, 47, 52, 56, 60, 64, 69, 73, 78-81, 85, 89, 94, 98, 102, 106, 118
src/core/config_manager.py                                        57      9    84%   128-129, 155, 159, 163, 166, 185-186, 198
src/core/enums/__init__.py                                         0      0   100%
src/core/enums/stock_analysis.py                                  30     30     0%   1-69
src/core/exceptions.py                                           189    102    46%   55-66, 70-82, 86-105, 109, 126-130, 144-150, 164-170, 182, 194, 208-214, 226, 238-242, 255-259, 271-275, 288-292, 304, 316, 328-332, 345, 357, 370-374, 388, 395, 402, 415, 427-431, 464-487, 496, 510, 518, 526
src/core/feedback_mechanism.py                                   233    233     0%   1-526
src/core/formatting/__init__.py                                    4      0   100%
src/core/formatting/analysis_template.py                         172    132    23%   34-37, 43-111, 115-120, 124-137, 141-148, 152-166, 181-227, 242-282, 286-295
src/core/formatting/response_templates.py                         43     17    60%   28-30, 67-75, 98-105, 128-135, 153-177, 190-199, 209-230
src/core/formatting/technical_analysis.py                         82     68    17%   26-38, 54, 67, 80-126, 139-155, 173-195
src/core/formatting/text_formatting.py                            41     41     0%   1-121
src/core/logger.py                                               125     52    58%   24, 52, 56, 71-72, 81-85, 97, 114, 131, 154-162, 188, 201-214, 259-261, 268, 275-329, 341, 345
src/core/metrics_tracker.py                                       77     50    35%   30-34, 59-62, 83-139, 160-172, 181, 204-209, 217-236
src/core/monitoring.py                                           189    120    37%   55-80, 88-97, 102-112, 117-127, 132-142, 147-157, 170-191, 207, 231-247, 251-270, 274, 320-328, 352-394, 398-408, 417, 431-439
src/core/pipeline_engine.py                                       80     45    44%   44, 48, 76-79, 92-133, 156-160, 172, 185-222, 237, 258-260, 269
src/core/response_generator.py                                    80     43    46%   36-37, 62-156, 161-171, 180-186, 191, 211, 217, 223
src/core/risk_management/__init__.py                               2      2     0%   8-16
src/core/risk_management/compliance_framework.py                 129    129     0%   1-459
src/core/secrets.py                                               49     19    61%   62, 74, 87-88, 102-103, 118-127, 140, 147-161, 168-170
src/core/security.py                                             107     61    43%   19, 23, 28, 35-43, 47, 67-80, 93, 106, 120, 130, 143-144, 156-158, 188-196, 212-216, 229-236, 249-262, 274-280, 292-296
src/core/utils.py                                                 87     74    15%   9, 23-29, 47, 65-68, 78-122, 140-159, 172-180, 192-236
src/core/validation/__init__.py                                    2      0   100%
src/core/validation/financial_validator.py                       251    210    16%   36-45, 73-167, 185-233, 247-318, 331-419, 438-473
src/data/__init__.py                                               5      4    20%   15-33
src/data/cache/__init__.py                                         2      2     0%   8-10
src/data/cache/manager.py                                        261    261     0%   8-476
src/data/models/__init__.py                                        6      6     0%   7-13
src/data/models/indicators.py                                     26     26     0%   5-45
src/data/models/stock_data.py                                     84     84     0%   1-106
src/data/providers/__init__.py                                     7      1    86%   19
src/data/providers/base.py                                       145     80    45%   72, 79-81, 85-98, 110-120, 124, 142-182, 186-206, 210-212, 225, 229, 245-248, 253-265, 268, 271
src/data/providers/config.py                                      56     56     0%   8-149
src/data/providers/manager.py                                    138    138     0%   9-331
src/database/__init__.py                                           0      0   100%
src/database/config.py                                            28     28     0%   8-60
src/database/connection.py                                        98     98     0%   1-176
src/database/migrations/__init__.py                                0      0   100%
src/database/migrations/env.py                                    33     33     0%   1-98
src/database/models/__init__.py                                    5      5     0%   1-6
src/database/models/alerts.py                                     37     37     0%   1-86
src/database/models/analysis.py                                   35     35     0%   1-83
src/database/models/interactions.py                               29     29     0%   1-73
src/database/models/market_data.py                                30     30     0%   1-63
src/database/models/users.py                                      28     28     0%   1-67
src/database/supabase_client.py                                   89     47    47%   23, 73-74, 81-82, 95, 108-132, 145-165, 184-211, 220-233
src/modules/__init__.py                                            0      0   100%
src/modules/crypto/__init__.py                                     0      0   100%
src/modules/forex/__init__.py                                      0      0   100%
src/modules/options_flow/__init__.py                               0      0   100%
src/modules/trading_signals/__init__.py                            2      2     0%   9-19
src/modules/trading_signals/signals.py                           119    119     0%   1-255
src/modules/watchlist/__init__.py                                  2      2     0%   7-9
src/modules/watchlist/watchlist_manager.py                       118    118     0%   1-299
src/shared/data_providers/__init__.py                              6      0   100%
src/shared/data_providers/aggregator.py                           84     53    37%   60-61, 68-72, 81, 94-120, 136-162, 174-180
src/shared/data_providers/alpha_vantage.py                       133    103    23%   55-62, 66, 70, 74, 90-158, 172-268, 272-277
src/shared/data_providers/base_provider.py                        20      3    85%   35, 39, 46
src/shared/data_providers/polygon_provider.py                    115     95    17%   24-32, 36-37, 41, 45-74, 78-96, 100-105, 109-161, 165-170, 174-202, 206, 215-226
src/shared/data_providers/yfinance_provider.py                    98     73    26%   25-32, 36, 40, 44, 52-53, 57-120, 124-153, 157-172
src/shared/error_handling/__init__.py                              4      0   100%
src/shared/error_handling/fallback.py                            120     83    31%   19-26, 56-78, 85-100, 104-110, 134-187, 193, 207-235
src/shared/error_handling/logging.py                             134     70    48%   20-26, 49, 51, 53, 57, 59, 61, 65-66, 101, 148, 171-183, 187, 195-199, 202-212, 215-245, 254-290, 300-315
src/shared/error_handling/retry.py                               165    130    21%   20-28, 52-64, 68-73, 78-93, 97-115, 137-195, 217-272, 276, 299-338, 356-417
src/templates/__init__.py                                          0      0   100%
src/templates/analysis_response.py                               110    110     0%   1-247
src/templates/ask.py                                             291    291     0%   9-751
src/templates/core.py                                             30     30     0%   5-137
--------------------------------------------------------------------------------------------
TOTAL                                                          12628  10606    16%

=========================== short test summary info ============================
ERROR tests/test_recommendation_engine.py - ModuleNotFoundError: No module na...
ERROR tests/test_recommendation_engine.py
!!!!!!!!!!!!!!!!!!! Interrupted: 2 errors during collection !!!!!!!!!!!!!!!!!!!!
======================== 44 warnings, 2 errors in 8.20s ========================

