#!/usr/bin/env python3
"""
Test YFinance as the fallback provider.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_yfinance_fallback():
    """Test YFinance as the working fallback provider."""
    try:
        print("📊 Testing YFinance Fallback Provider")
        print("=" * 50)
        
        from src.shared.data_providers.yfinance_provider import YFinanceProvider
        
        provider = YFinanceProvider()
        print("✅ YFinance provider created")
        
        # Test basic functionality
        symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "NVDA"]
        print(f"\n🔍 Testing {len(symbols)} symbols...")
        
        results = []
        for symbol in symbols:
            try:
                data = await provider.get_ticker(symbol)
                if data and not data.get('error'):
                    results.append(data)
                    print(f"✅ {symbol}: ${data['current_price']:.2f} ({data['change_percent']:+.2f}%)")
                else:
                    print(f"❌ {symbol}: {data.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"❌ {symbol}: Exception - {e}")
        
        print(f"\n📈 Successfully fetched {len(results)} symbols")
        
        if results:
            print("\n🎯 Testing batch method...")
            try:
                batch_data = await provider.get_multiple_tickers(symbols)
                if batch_data:
                    print(f"✅ Batch method working: {len(batch_data)} results")
                else:
                    print("❌ Batch method failed")
            except Exception as e:
                print(f"❌ Batch method error: {e}")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    await test_yfinance_fallback()
    print("\n✅ YFinance fallback test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 