version: '3.8'

services:
  # Discord bot service
  discord-bot:
    build: .
    container_name: tradingview-discord-bot
    command: python -m src.bot.client
    env_file:
      - .env.secure
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./src:/app/src
      - ./config:/app/config:ro
      - ./logs:/app/logs
    depends_on:
      - redis

  # API service
  api:
    build: .
    container_name: tradingview-api
    command: uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload
    volumes:
      - ./src:/app/src
      - ./config:/app/config:ro
      - ./logs:/app/logs
    env_file:
      - .env.secure
    environment:
      - PYTHONPATH=/app
    ports:
      - "8000:8000"
    depends_on:
      - redis

  # Webhook ingest service
  webhook-ingest:
    build: ./tradingview-ingest
    container_name: tradingview-webhook-ingest
    volumes:
      - ./tradingview-ingest/src:/app/src
      - ./tradingview-ingest/config:/app/config:ro
      - ./tradingview-ingest/logs:/app/logs
    ports:
      - "8001:8001"
    env_file:
      - .env.secure
    environment:
      - PYTHONPATH=/app
    depends_on:
      - redis

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: tradingview-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

volumes:
  redis_data: