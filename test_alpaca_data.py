#!/usr/bin/env python3
"""
Test script for Alpaca market data provider and options Greeks calculator
"""

import os
import sys
import asyncio
from datetime import datetime, timedel<PERSON>

# Add the src directory to the path
sys.path.append('/app')

from src.shared.data_providers.alpaca_provider import AlpacaProvider
from src.shared.technical_analysis.options_greeks_calculator import OptionsGreeksCalculator, OptionType, GreeksSnapshot

async def test_alpaca_provider():
    """Test Alpaca provider functionality"""
    print("🧪 Testing Alpaca Provider")
    print("=" * 50)
    
    # Check if credentials are set
    api_key = os.getenv('ALPACA_API_KEY')
    api_secret = os.getenv('ALPACA_API_SECRET')
    
    if not api_key or not api_secret:
        print("❌ Alpaca API credentials not fully configured")
        print("   Please set both ALPACA_API_KEY and ALPACA_API_SECRET")
        return False
    
    print(f"✅ API Key: {api_key[:5]}...{api_key[-5:]}")
    print(f"✅ API Secret: {'*' * len(api_secret)}")
    
    # Initialize provider
    provider = AlpacaProvider()
    
    if not provider.is_configured:
        print("❌ Alpaca provider not properly configured")
        return False
    
    print("✅ Alpaca provider initialized")
    
    # Test 1: Get current price for QQQ
    print("\n📊 Testing QQQ current price...")
    try:
        ticker_data = await provider.get_ticker("QQQ")
        if "error" in ticker_data:
            print(f"❌ Error: {ticker_data['error']}")
        else:
            print(f"✅ QQQ Price: ${ticker_data['current_price']:.2f}")
            print(f"   Ask: ${ticker_data.get('ask', 'N/A')}")
            print(f"   Bid: ${ticker_data.get('bid', 'N/A')}")
            print(f"   Volume: {ticker_data.get('volume', 'N/A')}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Get multiple tickers
    print("\n📊 Testing multiple tickers...")
    try:
        symbols = ["AAPL", "MSFT", "GOOGL"]
        results = await provider.get_multiple_tickers(symbols)
        for result in results:
            if "error" not in result:
                print(f"✅ {result['symbol']}: ${result['current_price']:.2f}")
            else:
                print(f"❌ {result.get('symbol', 'Unknown')}: {result['error']}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 3: Provider info
    print("\nℹ️  Provider Information:")
    info = provider.get_provider_info()
    for key, value in info.items():
        print(f"   {key}: {value}")
    
    return True

def test_options_greeks_calculator():
    """Test options Greeks calculator with synthetic data"""
    print("\n🧮 Testing Options Greeks Calculator")
    print("=" * 50)
    
    try:
        calculator = OptionsGreeksCalculator()
        
        # Create synthetic Greeks data for QQQ $573 call option
        current_price = 573.50
        strike_price = 573.0
        days_to_expiry = 7
        risk_free_rate = 0.05
        volatility = 0.25  # 25% annual volatility
        
        # Create a GreeksSnapshot with synthetic data
        synthetic_greeks = GreeksSnapshot(
            delta=0.52,           # 52% chance of being in-the-money
            gamma=0.008,          # Rate of change of delta
            theta=-0.45,          # Time decay (negative = loses value over time)
            vega=0.12,            # Sensitivity to volatility changes
            rho=0.03,             # Sensitivity to interest rate changes
            price=12.50,          # Option premium
            underlying_price=current_price,
            strike=strike_price,
            time_to_expiry=days_to_expiry/365,
            implied_volatility=volatility,
            risk_free_rate=risk_free_rate
        )
        
        print(f"📊 QQQ ${strike_price:.0f} Call Option Analysis")
        print(f"📈 Current QQQ Price: ${current_price:.2f}")
        print(f"🎯 Strike Price: ${strike_price:.0f}")
        print(f"⏰ Days to Expiry: {days_to_expiry}")
        print(f"📊 Volatility: {volatility*100:.1f}%")
        print(f"💰 Current Option Price: ${synthetic_greeks.price:.2f}")
        print()
        
        print("🎢 Current Option Greeks:")
        print(f"   Delta: {synthetic_greeks.delta:.4f}")
        print(f"   Gamma: {synthetic_greeks.gamma:.4f}")
        print(f"   Theta: {synthetic_greeks.theta:.4f}")
        print(f"   Vega: {synthetic_greeks.vega:.4f}")
        print(f"   Rho: {synthetic_greeks.rho:.4f}")
        print()
        
        # Test future Greeks estimation
        print("🔮 Testing Future Greeks Estimation")
        print("-" * 40)
        
        price_change = 5.0
        time_elapsed = 1/365  # 1 day
        volatility_change = 0.01  # 1% increase in volatility
        
        future_greeks = calculator.estimate_future_greeks(
            synthetic_greeks,
            price_change,
            time_elapsed,
            volatility_change
        )
        
        print(f"📈 After QQQ moves +${price_change:.2f} in 1 day:")
        print(f"   New Delta: {future_greeks['delta']:.4f}")
        print(f"   New Gamma: {future_greeks['gamma']:.4f}")
        print(f"   New Theta: {future_greeks['theta']:.4f}")
        print(f"   New Vega: {future_greeks['vega']:.4f}")
        print(f"   New Price: ${future_greeks['price']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Options Greeks calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Trading Automation Tests")
    print("=" * 60)
    
    # Test options Greeks calculator first (doesn't require API calls)
    greeks_success = test_options_greeks_calculator()
    
    # Test Alpaca provider (requires API credentials)
    alpaca_success = await test_alpaca_provider()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   Options Greeks Calculator: {'✅ PASS' if greeks_success else '❌ FAIL'}")
    print(f"   Alpaca Provider: {'✅ PASS' if alpaca_success else '❌ FAIL'}")
    
    if greeks_success and alpaca_success:
        print("\n🎉 All tests passed!")
        return True
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    asyncio.run(main())