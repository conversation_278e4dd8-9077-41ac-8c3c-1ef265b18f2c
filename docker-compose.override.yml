version: '3.8'

services:
  # Discord bot service with hot reloading for development
  discord-bot:
    command: >
      watchmedo auto-restart 
      --directory=/app/src 
      --pattern="*.py" 
      --recursive -- 
      python -m src.bot.client
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}

  # API service with reload for development
  api:
    command: uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}