# Docker Setup

## Overview
This project uses Docker for containerization to ensure consistent environments across development and production.

## Files
- `Dockerfile`: Main Docker image definition for all services
- `docker-compose.yml`: Base configuration for all services
- `docker-compose.override.yml`: Development-specific overrides (auto-loaded in development)

## Usage

### Development
```bash
# Start all services
docker-compose up --build

# Start specific service
docker-compose up --build discord-bot

# View logs
docker-compose logs discord-bot

# Stop services
docker-compose down
```

### Production
For production deployment, the base `docker-compose.yml` can be used with a production-specific override file.

## Services
1. **discord-bot**: Main Discord bot service
2. **api**: REST API service
3. **webhook-ingest**: TradingView webhook receiver
4. **redis**: Cache and message queue

The development setup includes hot-reloading for Python files, so code changes are automatically reflected without restarting containers.