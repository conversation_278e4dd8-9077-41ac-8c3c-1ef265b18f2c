#!/usr/bin/env python3
"""
Direct test of Finnhub API to see what's wrong.
"""

import asyncio
import sys
import os
import httpx

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_finnhub_direct():
    """Test Finnhub API directly."""
    try:
        print("🔌 Direct Finnhub API Test")
        print("=" * 40)
        
        # Get API key from environment
        api_key = os.getenv('FINNHUB_API_KEY')
        print(f"🔑 API Key: {api_key[:8]}...{api_key[-4:] if api_key else 'None'}")
        
        if not api_key:
            print("❌ No FINNHUB_API_KEY found in environment")
            return
        
        # Test basic quote endpoint
        base_url = "https://finnhub.io/api/v1"
        
        print("\n📊 Testing quote endpoint...")
        url = f"{base_url}/quote"
        params = {
            'symbol': 'AAPL',
            'token': api_key
        }
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(url, params=params)
            print(f"📡 Response status: {response.status_code}")
            print(f"📄 Response headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success! Data: {data}")
                
                if 'c' in data:  # Current price
                    print(f"💰 Current price: ${data['c']}")
                if 'pc' in data:  # Previous close
                    print(f"📈 Previous close: ${data['pc']}")
                if 'v' in data:  # Volume
                    print(f"📊 Volume: {data['v']:,}")
                    
            elif response.status_code == 401:
                print("❌ 401 Unauthorized - API key is invalid or expired")
                print(f"📄 Response: {response.text}")
            elif response.status_code == 429:
                print("⚠️ 429 Rate Limited - too many requests")
            else:
                print(f"❌ Unexpected status: {response.status_code}")
                print(f"📄 Response: {response.text}")
        
        # Test company profile endpoint
        print("\n🏢 Testing company profile endpoint...")
        url = f"{base_url}/stock/profile2"
        params = {
            'symbol': 'AAPL',
            'token': api_key
        }
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(url, params=params)
            print(f"📡 Response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Company profile: {data}")
            else:
                print(f"❌ Company profile failed: {response.status_code}")
                print(f"📄 Response: {response.text}")
        
        # Test with different symbols
        print("\n🔍 Testing different symbols...")
        symbols = ["AAPL", "MSFT", "GOOGL", "SPY"]
        
        for symbol in symbols:
            try:
                url = f"{base_url}/quote"
                params = {
                    'symbol': symbol,
                    'token': api_key
                }
                
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(url, params=params)
                    
                    if response.status_code == 200:
                        data = response.json()
                        if 'c' in data and data['c']:
                            print(f"✅ {symbol}: ${data['c']}")
                        else:
                            print(f"⚠️ {symbol}: No price data")
                    else:
                        print(f"❌ {symbol}: {response.status_code}")
                        
            except Exception as e:
                print(f"❌ {symbol}: Error - {e}")
            
            # Small delay between requests
            await asyncio.sleep(0.5)
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    await test_finnhub_direct()
    print("\n✅ Direct Finnhub test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 