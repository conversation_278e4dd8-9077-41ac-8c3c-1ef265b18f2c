#!/usr/bin/env python3
"""
Test enhanced analysis with specific symbol
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_tsla_analysis():
    """Test analysis with TSLA symbol"""
    
    print("🚗 TESTING TSLA ANALYSIS WITH ENHANCED ENGINES")
    print("=" * 60)
    
    try:
        from src.analysis.technical.price_targets import PriceTargetEngine
        from src.analysis.probability.probability_engine import ProbabilityEngine
        from src.analysis.technical.timeframe_confirmation import TimeframeConfirmationAnalyzer
        import pandas as pd
        import numpy as np
        
        # Generate TSLA-like data (more volatile, trending)
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(123)  # Different seed for TSLA
        
        # TSLA-like characteristics: higher volatility, stronger trend
        base_price = 200
        trend = np.linspace(0, 80, 100)  # Stronger upward trend
        noise = np.random.normal(0, 8, 100)  # Higher volatility
        prices = base_price + trend + noise
        
        price_data = pd.DataFrame({
            'open': prices * 0.98,
            'high': prices * 1.03,
            'low': prices * 0.97,
            'close': prices,
            'volume': np.random.randint(500000, 5000000, 100)
        }, index=dates)
        
        volume_data = pd.DataFrame({
            'volume': np.random.randint(500000, 5000000, 100)
        }, index=dates)
        
        # Test Price Target Engine
        print("🎯 Testing Price Target Engine...")
        price_engine = PriceTargetEngine()
        targets = price_engine.calculate_targets('TSLA', '1d', price_data, volume_data)
        
        print(f"✅ TSLA Price Targets:")
        print(f"   Current Price: ${targets.current_price:.2f}")
        print(f"   Conservative Target: ${targets.conservative_target:.2f}")
        print(f"   Moderate Target: ${targets.moderate_target:.2f}")
        print(f"   Aggressive Target: ${targets.aggressive_target:.2f}")
        print(f"   Stop Loss: ${targets.stop_loss:.2f}")
        print(f"   Trend: {targets.trend_direction.value} ({targets.trend_strength:.1%})")
        print(f"   Volatility: {targets.volatility:.1%}")
        
        # Test Probability Engine
        print("\n🎲 Testing Probability Engine...")
        prob_engine = ProbabilityEngine()
        assessment = prob_engine.assess_probabilities('TSLA', '1d', price_data, volume_data, 0.4)
        
        print(f"✅ TSLA Probability Assessment:")
        print(f"   Bullish: {assessment.bullish_probability:.1%}")
        print(f"   Bearish: {assessment.bearish_probability:.1%}")
        print(f"   Sideways: {assessment.sideways_probability:.1%}")
        print(f"   Confidence: {assessment.confidence_level:.1%}")
        print(f"   Market Regime: {assessment.market_regime.value}")
        print(f"   Risk-Adjusted Return: {assessment.risk_adjusted_return:.4f}")
        
        # Test Timeframe Confirmation
        print("\n⏰ Testing Timeframe Confirmation...")
        timeframe_analyzer = TimeframeConfirmationAnalyzer()
        
        # Create multi-timeframe data
        multi_price_data = {
            '1d': price_data,
            '4h': price_data.iloc[::6],  # Sample every 6th point for 4h
            '1h': price_data.iloc[::24]  # Sample every 24th point for 1h
        }
        
        multi_volume_data = {
            '1d': volume_data,
            '4h': volume_data.iloc[::6],
            '1h': volume_data.iloc[::24]
        }
        
        confirmation = timeframe_analyzer.get_timeframe_agreement('TSLA', multi_price_data, multi_volume_data)
        
        print(f"✅ TSLA Timeframe Analysis:")
        print(f"   Short-term: {confirmation.short_term_bias.value}")
        print(f"   Medium-term: {confirmation.medium_term_bias.value}")
        print(f"   Long-term: {confirmation.long_term_bias.value}")
        print(f"   Agreement Score: {confirmation.agreement_score:.1%}")
        print(f"   Overall Confidence: {confirmation.overall_confidence:.1%}")
        print(f"   Recommendation: {confirmation.recommendation}")
        
        # Show key insights
        print(f"\n🔍 Key Insights for TSLA:")
        print(f"   • Price targets range: ${targets.conservative_target:.2f} - ${targets.aggressive_target:.2f}")
        print(f"   • Risk level: {'HIGH' if targets.volatility > 0.05 else 'MEDIUM' if targets.volatility > 0.02 else 'LOW'}")
        print(f"   • Trend strength: {'Strong' if targets.trend_strength > 0.5 else 'Moderate' if targets.trend_strength > 0.2 else 'Weak'}")
        print(f"   • Market regime: {assessment.market_regime.value}")
        print(f"   • Timeframe alignment: {'Excellent' if confirmation.agreement_score > 0.8 else 'Good' if confirmation.agreement_score > 0.6 else 'Poor'}")
        
        print(f"\n🎯 TSLA Analysis Complete!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing TSLA analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_tsla_analysis()