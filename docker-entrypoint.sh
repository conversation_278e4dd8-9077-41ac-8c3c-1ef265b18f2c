#!/bin/bash
set -e

echo "Starting trading bot in development mode..."

# Check if required environment variables are set
required_vars=(
    "SUPABASE_KEY"
    "DISCORD_BOT_TOKEN" 
    "OPENROUTER_API_KEY"
    "POLYGON_API_KEY"
    "FINNHUB_API_KEY"
    "ALPACA_API_KEY"
    "JWT_SECRET"
    "REDIS_PASSWORD"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo "❌ Missing required environment variables:"
    printf '  - %s\n' "${missing_vars[@]}"
    echo "💡 Make sure these are set in your .env file"
    exit 1
fi

echo "✅ All required environment variables are set"
echo "🚀 Starting application..."

# Execute the main command
exec "$@" 