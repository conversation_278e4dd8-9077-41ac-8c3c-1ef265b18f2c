#!/usr/bin/env python3
"""
Show Full Enhanced Analysis Report

This test shows the complete enhanced analysis output that users will see.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def show_full_enhanced_report():
    """Show the complete enhanced analysis report"""
    
    print("🧪 SHOWING COMPLETE ENHANCED ANALYSIS REPORT")
    print("=" * 60)
    
    try:
        from src.bot.pipeline.commands.analyze.stages.enhanced_analysis import EnhancedAnalysisStage
        from src.bot.pipeline.commands.analyze.pipeline import PipelineContext
        from src.bot.pipeline.commands.analyze.stages.report_generator import _format_report
        
        # Create mock pipeline context
        ticker = "TSLA"
        print(f"📊 Generating enhanced analysis report for: {ticker}")
        
        # Generate realistic mock market data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        base_price = 179.30
        prices = base_price + np.linspace(0, 30, 100) + np.random.normal(0, 3, 100)
        
        mock_market_data = {
            'current_price': prices[-1],
            'price': prices[-1],
            'volume': np.random.randint(50000000, 200000000, 100).mean(),
            'historical_data': {
                'daily': [
                    {
                        'date': date.strftime('%Y-%m-%d'),
                        'open': price * 0.99,
                        'high': price * 1.02,
                        'low': price * 0.98,
                        'close': price,
                        'volume': np.random.randint(50000000, 200000000)
                    }
                    for date, price in zip(dates, prices)
                ]
            }
        }
        
        # Create pipeline context
        context = PipelineContext(
            ticker=ticker.upper(),
            user_id="test_user_123",
            guild_id="test_guild_456",
            correlation_id="test_corr_789",
            strict_mode=True
        )
        
        # Add mock market data
        context.processing_results["market_data"] = mock_market_data
        
        # Run enhanced analysis stage
        print("🚀 Running enhanced analysis...")
        enhanced_stage = EnhancedAnalysisStage()
        updated_context = await enhanced_stage.run(context)
        
        # Get enhanced analysis data
        enhanced_data = updated_context.processing_results.get('enhanced_analysis', {})
        
        # Add mock technical analysis and price targets
        context.processing_results['technical_analysis'] = {
            'rsi': 65.5,
            'macd': 'bullish',
            'trend': 'uptrend'
        }
        
        context.processing_results['price_targets'] = {
            'tp1': enhanced_data.get('price_targets', {}).get('conservative_target', 188.89),
            'tp2': enhanced_data.get('price_targets', {}).get('moderate_target', 198.48),
            'tp3': enhanced_data.get('price_targets', {}).get('aggressive_target', 217.66),
            'sl': enhanced_data.get('price_targets', {}).get('stop_loss', 173.54)
        }
        
        # Generate the complete report
        print("📝 Generating complete report...")
        report = _format_report(
            ticker, 
            mock_market_data, 
            context.processing_results['technical_analysis'], 
            context.processing_results['price_targets'], 
            enhanced_data
        )
        
        # Show the complete enhanced analysis report
        print("\n" + "="*60)
        print("🚀 COMPLETE ENHANCED ANALYSIS REPORT")
        print("="*60)
        print(report)
        print("="*60)
        
        # Show enhanced analysis details
        print("\n🔍 ENHANCED ANALYSIS DETAILS:")
        print(f"   Overall Recommendation: {enhanced_data.get('overall_recommendation', 'N/A')}")
        print(f"   Confidence Score: {enhanced_data.get('confidence_score', 0) * 100:.1f}%")
        print(f"   Risk Level: {enhanced_data.get('risk_level', 'N/A')}")
        
        # Show price targets
        price_targets = enhanced_data.get('price_targets', {})
        print(f"\n🎯 ENHANCED PRICE TARGETS:")
        print(f"   Conservative: ${price_targets.get('conservative_target', 0):.2f}")
        print(f"   Moderate: ${price_targets.get('moderate_target', 0):.2f}")
        print(f"   Aggressive: ${price_targets.get('aggressive_target', 0):.2f}")
        print(f"   Stop Loss: ${price_targets.get('stop_loss', 0):.2f}")
        
        # Show probability assessment
        prob_assessment = enhanced_data.get('probability_assessment', {})
        print(f"\n🎲 PROBABILITY ASSESSMENT:")
        print(f"   Bullish: {prob_assessment.get('bullish_probability', 0) * 100:.1f}%")
        print(f"   Bearish: {prob_assessment.get('bearish_probability', 0) * 100:.1f}%")
        print(f"   Sideways: {prob_assessment.get('sideways_probability', 0) * 100:.1f}%")
        print(f"   Confidence: {prob_assessment.get('confidence_level', 0) * 100:.1f}%")
        
        # Show timeframe confirmation
        timeframe_conf = enhanced_data.get('timeframe_confirmation', {})
        print(f"\n⏰ TIMEFRAME CONFIRMATION:")
        print(f"   Agreement Score: {timeframe_conf.get('agreement_score', 0) * 100:.1f}%")
        print(f"   Overall Confidence: {timeframe_conf.get('overall_confidence', 0) * 100:.1f}%")
        print(f"   Recommendation: {timeframe_conf.get('recommendation', 'N/A')}")
        
        # Show key insights
        key_insights = enhanced_data.get('key_insights', [])
        if key_insights:
            print(f"\n🔍 KEY INSIGHTS:")
            for insight in key_insights[:5]:
                print(f"   • {insight}")
        
        print(f"\n🎉 This is exactly what your Discord bot users will see!")
        print(f"   Professional-grade analysis with institutional quality!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error showing enhanced analysis report: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🎯 Showing Complete Enhanced Analysis Report")
    print("=" * 60)
    
    success = await show_full_enhanced_report()
    
    if success:
        print("\n🎉 ENHANCED ANALYSIS REPORT DISPLAYED SUCCESSFULLY!")
        print("This shows exactly what your users will get from the enhanced analysis system!")
    else:
        print("\n⚠️ Failed to display enhanced analysis report. Check the error messages above.")
    
    return success

if __name__ == "__main__":
    import asyncio
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 