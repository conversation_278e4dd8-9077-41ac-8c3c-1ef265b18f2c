#!/usr/bin/env python3
"""
Test script to verify Finnhub API key fix
"""

import sys
import asyncio
import os

# Add the src directory to the path
sys.path.append('/app')

async def test_finnhub_fix():
    """Test if Finnhub is now working with the new API key"""
    print("🔧 Testing Finnhub API Key Fix")
    print("=" * 50)
    
    try:
        from src.api.data.providers.finnhub import FinnhubProvider
        
        # Set the new API key
        os.environ['FINNHUB_API_KEY'] = 'd2mjok1r01qog4441lsgd2mjok1r01qog4441lt0'
        
        provider = FinnhubProvider()
        
        if not provider.is_configured:
            print("❌ Finnhub provider not properly configured")
            return False
        
        print("✅ Finnhub provider initialized")
        print(f"🔑 API Key: {os.environ.get('FINNHUB_API_KEY', 'Not set')[:10]}...")
        
        # Test 1: Get current price for QQQ
        print("\n📊 Testing QQQ current price...")
        try:
            ticker_data = await provider.get_ticker("QQQ")
            if "error" in ticker_data:
                print(f"❌ Error: {ticker_data['error']}")
                return False
            else:
                print(f"✅ QQQ Price: ${ticker_data['current_price']:.2f}")
                print(f"   Change: ${ticker_data.get('change', 'N/A')}")
                print(f"   Change %: {ticker_data.get('change_percent', 'N/A')}%")
                print(f"   Volume: {ticker_data.get('volume', 'N/A')}")
                return True
        except Exception as e:
            print(f"❌ Exception: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Import/initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Testing Finnhub API Key Fix")
    print("=" * 60)
    
    success = await test_finnhub_fix()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   Finnhub API Key Fix: {'✅ PASS' if success else '❌ FAIL'}")
    
    if success:
        print("\n🎉 Finnhub is now working! Real data should be available.")
    else:
        print("\n⚠️  Finnhub still has issues. Check the output above.")

if __name__ == "__main__":
    asyncio.run(main()) 