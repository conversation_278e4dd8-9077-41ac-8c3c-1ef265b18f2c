# TradingView Automation Environment Variables
# Copy this file to .env and fill in the values

# Database Configuration (Supabase Only)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
SUPABASE_DB_URL=postgresql://postgres:<EMAIL>:5432/postgres
SUPABASE_URL=https://your-supabase-project.supabase.co
SUPABASE_KEY=your-supabase-key
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
USE_SUPABASE=true
SUPABASE_CLIENT_TYPE=sdk
SUPABASE_FALLBACK_IP=127.0.0.1

# Redis Configuration
REDIS_URL=redis://your-redis-host:6379/0
REDIS_ENABLED=true
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-secure-redis-password
REDIS_POOL_SIZE=10
REDIS_MAX_CONNECTIONS=20
REDIS_DEFAULT_TTL=600
REDIS_SSL=false

# Security Configuration
JWT_SECRET=your-jwt-secret
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
WEBHOOK_SECRET=your-webhook-secret
REQUIRE_WEBHOOK_SIGNATURE=true

# Discord Configuration
DISCORD_BOT_TOKEN=your-discord-bot-token
DISCORD_ENABLED=true
DISCORD_USE_SLASH_COMMANDS=true
DISCORD_MAX_RETRIES=3
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your-webhook-id/your-webhook-token
PAID_ROLE_IDS=1408844110642544836
ADMIN_ROLE_IDS=1304548446090301448

# Market Data Providers
POLYGON_API_KEY=your-polygon-api-key
FINNHUB_API_KEY=your-finnhub-api-key
ALPACA_API_KEY=your-alpaca-api-key
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-api-key
OPENROUTER_API_KEY=your-openrouter-api-key

# ============================================================================
# AI MODEL CONFIGURATION
# ============================================================================
# AI Model defaults
AI_DEFAULT_TEMPERATURE=0.7
AI_DEFAULT_MAX_TOKENS=2000
AI_DEFAULT_TIMEOUT_MS=30000
AI_DEFAULT_COST=0.001
AI_DEFAULT_ACCURACY=0.8
AI_DEFAULT_RESPONSE_TIME=2000

# GPT-4o Mini Configuration
GPT_4O_MINI_MODEL_ID=gpt-4o-mini
GPT_4O_MINI_MAX_TOKENS=16384
GPT_4O_MINI_COST=0.00015
GPT_4O_MINI_RESPONSE_TIME=2000
GPT_4O_MINI_ACCURACY=0.85
GPT_4O_MINI_ENABLED=true

# GPT-4o Configuration
GPT_4O_MODEL_ID=gpt-4o
GPT_4O_MAX_TOKENS=128000
GPT_4O_COST=0.005
GPT_4O_RESPONSE_TIME=3000
GPT_4O_ACCURACY=0.92
GPT_4O_ENABLED=true

# Claude 3.5 Sonnet Configuration
CLAUDE_3_5_SONNET_MODEL_ID=claude-3-5-sonnet-20241022
CLAUDE_3_5_SONNET_MAX_TOKENS=200000
CLAUDE_3_5_SONNET_COST=0.003
CLAUDE_3_5_SONNET_RESPONSE_TIME=2500
CLAUDE_3_5_SONNET_ACCURACY=0.90
CLAUDE_3_5_SONNET_ENABLED=true

# Mixtral 8x7B Configuration
MIXTRAL_8X7B_MODEL_ID=mixtral-8x7b-instruct
MIXTRAL_8X7B_MAX_TOKENS=32768
MIXTRAL_8X7B_COST=0.00014
MIXTRAL_8X7B_RESPONSE_TIME=1500
MIXTRAL_8X7B_ACCURACY=0.78
MIXTRAL_8X7B_ENABLED=true

# Llama 3 8B Configuration
LLAMA_3_8B_MODEL_ID=llama-3-8b-instruct
LLAMA_3_8B_MAX_TOKENS=8192
LLAMA_3_8B_COST=0.00010
LLAMA_3_8B_RESPONSE_TIME=1000
LLAMA_3_8B_ACCURACY=0.75
LLAMA_3_8B_ENABLED=true

# AI Scoring Weights (for model selection)
AI_SCORING_ACCURACY_WEIGHT=0.4
AI_SCORING_COST_WEIGHT=0.25
AI_SCORING_TIME_WEIGHT=0.2
AI_SCORING_CAPACITY_WEIGHT=0.1
AI_SCORING_PREFERENCE_WEIGHT=0.05

# Complexity Multipliers (for token estimation)
AI_COMPLEXITY_SIMPLE=1.0
AI_COMPLEXITY_MODERATE=1.5
AI_COMPLEXITY_COMPLEX=2.0
AI_COMPLEXITY_EXPERT=2.5
AI_COMPLEXITY_REAL_TIME=3.0

# Performance Tracking
AI_PERFORMANCE_TRACKING=true
AI_METRICS_RETENTION=30
AI_CIRCUIT_BREAKER_THRESHOLD=5
AI_CIRCUIT_BREAKER_TIMEOUT=300

# Data Provider Configuration
DATA_CACHE_TTL=300
DATA_REQUEST_TIMEOUT=10.0
DATA_MAX_RETRIES=3
YAHOO_RATE_LIMIT=100
POLYGON_RATE_LIMIT=60
FINNHUB_RATE_LIMIT=60
ALPHA_VANTAGE_RATE_LIMIT=5
MIN_DATA_QUALITY=0.7
FALLBACK_QUALITY_THRESHOLD=0.6
ENABLE_DETAILED_LOGGING=false
ENABLE_PERFORMANCE_TRACKING=true
ENABLE_AUDIT_TRAIL=true

# Technical Analysis Configuration
TECH_ANALYSIS_SMA_WINDOW=20
TECH_ANALYSIS_EMA_SPAN=12
TECH_ANALYSIS_RSI_PERIOD=14
TECH_ANALYSIS_MACD_FAST=12
TECH_ANALYSIS_MACD_SLOW=26
TECH_ANALYSIS_MACD_SIGNAL=9
TECH_ANALYSIS_BB_WINDOW=20
TECH_ANALYSIS_BB_STD=2.0
TECH_ANALYSIS_ATR_PERIOD=14
TECH_ANALYSIS_VOLUME_WINDOW=20

# TradingView Ingest Configuration
BATCH_SIZE=100
PROCESSING_DELAY=0.1
RATE_LIMIT_PER_MINUTE=100
ALERT_WEBHOOK_URL=
REQUIRE_WEBHOOK_SIGNATURE=true
WEBHOOK_SECRET=your-webhook-secret
WEBHOOK_MAX_WORKERS=4
WEBHOOK_RATE_LIMIT=60
WEBHOOK_TIMEOUT=30
ENABLE_METRICS=true
ENABLE_QUEUE=true

# Bot Database Configuration (Supabase Only)
USE_SUPABASE=true
SUPABASE_CLIENT_TYPE=sdk

# Market Intelligence Configuration
HTTP_TIMEOUT=10.0
DEFAULT_SCAN_SYMBOLS=AAPL,MSFT,GOOGL,AMZN,TSLA,NVDA,SPY,QQQ
DEFAULT_CACHE_SYMBOLS=SPY,QQQ,AAPL,MSFT,GOOGL,AMZN,TSLA,NVDA
ALERT_THRESHOLD=75.0
YFINANCE_PERIOD=60d
SMA_SHORT_WINDOW=20
SMA_LONG_WINDOW=50
RSI_WINDOW=14
BB_WINDOW=20
VOLUME_WINDOW=20
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9
MIN_DATA_DAYS=50
PRICE_CHANGE_DAYS=5
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0
VOLUME_HIGH_THRESHOLD=1.5
VOLUME_LOW_THRESHOLD=0.5
PRICE_MOMENTUM_THRESHOLD=5.0
HIGH_CONFIDENCE_THRESHOLD=70.0
EXTREME_MOMENTUM_THRESHOLD=8.0

# AI Pipeline Cache Configuration
ASK_PIPELINE_CACHE_TTL=3600

# Technical Analysis Configuration
TECH_ANALYSIS_SMA_WINDOW=20
TECH_ANALYSIS_EMA_SPAN=12
TECH_ANALYSIS_RSI_PERIOD=14
TECH_ANALYSIS_MACD_FAST=12
TECH_ANALYSIS_MACD_SLOW=26
TECH_ANALYSIS_MACD_SIGNAL=9
TECH_ANALYSIS_BB_WINDOW=20
TECH_ANALYSIS_BB_STD=2.0
TECH_ANALYSIS_ATR_PERIOD=14
TECH_ANALYSIS_VOLUME_WINDOW=20

# TradingView Ingest Configuration
BATCH_SIZE=100
PROCESSING_DELAY=0.1
RATE_LIMIT_PER_MINUTE=100
ALERT_WEBHOOK_URL=
REQUIRE_WEBHOOK_SIGNATURE=true
WEBHOOK_SECRET=your-webhook-secret
WEBHOOK_MAX_WORKERS=4
WEBHOOK_RATE_LIMIT=60
WEBHOOK_TIMEOUT=30
ENABLE_METRICS=true
ENABLE_QUEUE=true

# Bot Database Configuration (Supabase Only)
USE_SUPABASE=true
SUPABASE_CLIENT_TYPE=sdk

# Market Intelligence Configuration
HTTP_TIMEOUT=10.0
DEFAULT_SCAN_SYMBOLS=AAPL,MSFT,GOOGL,AMZN,TSLA,NVDA,SPY,QQQ
DEFAULT_CACHE_SYMBOLS=SPY,QQQ,AAPL,MSFT,GOOGL,AMZN,TSLA,NVDA
ALERT_THRESHOLD=75.0
YFINANCE_PERIOD=60d
SMA_SHORT_WINDOW=20
SMA_LONG_WINDOW=50
RSI_WINDOW=14
BB_WINDOW=20
VOLUME_WINDOW=20
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9
MIN_DATA_DAYS=50
PRICE_CHANGE_DAYS=5
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0
VOLUME_HIGH_THRESHOLD=1.5
VOLUME_LOW_THRESHOLD=0.5
PRICE_MOMENTUM_THRESHOLD=5.0
HIGH_CONFIDENCE_THRESHOLD=70.0
EXTREME_MOMENTUM_THRESHOLD=8.0

# Data Provider Configuration
DATA_CACHE_TTL=300
DATA_REQUEST_TIMEOUT=10.0
DATA_MAX_RETRIES=3
YAHOO_RATE_LIMIT=100
POLYGON_RATE_LIMIT=60
FINNHUB_RATE_LIMIT=60
ALPHA_VANTAGE_RATE_LIMIT=5
MIN_DATA_QUALITY=0.7
FALLBACK_QUALITY_THRESHOLD=0.6
ENABLE_DETAILED_LOGGING=false
ENABLE_PERFORMANCE_TRACKING=true
ENABLE_AUDIT_TRAIL=true

# Discord Configuration
DISCORD_BOT_TOKEN=your-discord-bot-token
DISCORD_ENABLED=true
DISCORD_USE_SLASH_COMMANDS=true
DISCORD_MAX_RETRIES=3
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your-webhook-id/your-webhook-token
PAID_ROLE_IDS=1408844110642544836
ADMIN_ROLE_IDS=1304548446090301448

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_DEFAULT_TTL=600
REDIS_POOL_SIZE=10
REDIS_SSL=false

# Security
JWT_SECRET=your-jwt-secret
WEBHOOK_SECRET=your-webhook-secret
REQUIRE_WEBHOOK_SIGNATURE=true

# Webhook Configuration
WEBHOOK_MAX_WORKERS=4
WEBHOOK_RATE_LIMIT=60
WEBHOOK_TIMEOUT=30

# Feature Flags
ENABLE_METRICS=true
ENABLE_QUEUE=true

# TradingView Ingest Configuration
BATCH_SIZE=100
PROCESSING_DELAY=0.1
RATE_LIMIT_PER_MINUTE=100
ALERT_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your-webhook-id/your-webhook-token

# Logging and Environment
LOG_LEVEL=INFO
ENVIRONMENT=production

# Market Intelligence Configuration
HTTP_TIMEOUT=10.0
DEFAULT_SCAN_SYMBOLS=AAPL,MSFT,GOOGL,AMZN,TSLA,NVDA,SPY,QQQ
DEFAULT_CACHE_SYMBOLS=SPY,QQQ,AAPL,MSFT,GOOGL,AMZN,TSLA,NVDA
ALERT_THRESHOLD=75.0
YFINANCE_PERIOD=60d
SMA_SHORT_WINDOW=20
SMA_LONG_WINDOW=50
RSI_WINDOW=14
BB_WINDOW=20
VOLUME_WINDOW=20
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9
MIN_DATA_DAYS=50
PRICE_CHANGE_DAYS=5
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0
VOLUME_HIGH_THRESHOLD=1.5
VOLUME_LOW_THRESHOLD=0.5
PRICE_MOMENTUM_THRESHOLD=5.0
HIGH_CONFIDENCE_THRESHOLD=70.0
EXTREME_MOMENTUM_THRESHOLD=8.0

# Technical Analysis Configuration
TECH_ANALYSIS_SMA_WINDOW=20
TECH_ANALYSIS_EMA_SPAN=12
TECH_ANALYSIS_RSI_PERIOD=14
TECH_ANALYSIS_MACD_FAST=12
TECH_ANALYSIS_MACD_SLOW=26
TECH_ANALYSIS_MACD_SIGNAL=9
TECH_ANALYSIS_BB_WINDOW=20
TECH_ANALYSIS_BB_STD=2.0
TECH_ANALYSIS_ATR_PERIOD=14
TECH_ANALYSIS_VOLUME_WINDOW=20

# AI Pipeline Cache Configuration
ASK_PIPELINE_CACHE_TTL=3600
