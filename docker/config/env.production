# TRADING AUTOMATION - ENV CONFIG
# Last Updated: 2025-09-03

# Core App Config
ENVIRONMENT=production
APP_NAME=TradingView Automation Bot
APP_VERSION=2.0.0
DEBUG=false
LOG_LEVEL=INFO
TRADINGBOT_ENV=production

# Frontend / API Settings
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGIN=http://localhost:3000

API_HOST=0.0.0.0
API_PORT=8000
API_RATE_LIMIT=100
API_RATE_WINDOW=60

# Database Configuration (Supabase Only)
# Primary Database (Supabase)
USE_SUPABASE=true
# Database connection URL (PostgreSQL)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
# Direct Supabase database URL
SUPABASE_DB_URL=postgresql://postgres:<EMAIL>:5432/postgres
# Fallback IP address for Supabase database (used when DNS resolution fails)
SUPABASE_FALLBACK_IP=***********
# Supabase REST API endpoint (different from database URL)
SUPABASE_URL=https://sgxjackuhalscowqrulv.supabase.co
SUPABASE_KEY=********************************************
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8sE
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTk2MTE0MywiZXhwIjoyMDY1NTM3MTQzfQ.4Nz4q6HN3XGgd23l_xCSkZWD1qDh3U0UWY4m-aDbqrA
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_ECHO=false

# Redis Configuration
REDIS_ENABLED=true
REDIS_URL=redis://:123SECURE_REDIS_PASSWORD!@#@redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=123SECURE_REDIS_PASSWORD!@#
REDIS_POOL_SIZE=10
REDIS_MAX_CONNECTIONS=20

# Security Settings
JWT_SECRET=vLzXgYq8bNfP6hJ2kM5sR9uV0wZ4yA1cE7fG3hK5jP3sR6uV0xY4zC1bE7fG2hK5jP3sR6uV0wZ4yA1c
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=600

# Discord Bot Configuration
DISCORD_BOT_TOKEN=MTQwNDUwNjk2MTc3NjQ4MDMxNg.GQRo7g.w1qaV6PpYEPq8CEwYph7aA_xN8W3SYNG5_aij0
DISCORD_GUILD_ID=1304548446090301440
DISCORD_AI_CHANNEL_ID=1403195431730810932
DISCORD_ENABLED=true

# AI / LLM Config
OPENROUTER_ENABLED=true
OPENROUTER_API_KEY=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
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Default models
OPENROUTER_GENERAL_MODEL=openrouter/sonoma-sky-alpha
OPENROUTER_GENERAL_MODEL_MAX_TOKENS=2000
OPENROUTER_GENERAL_MODEL_TEMPERATURE=0.7

MODEL_GLOBAL_FALLBACK=moonshotai/kimi-k2:free
FREE_MODELS=z-ai/glm-4.5-air:free,deepseek/deepseek-r1:free,moonshotai/kimi-k2:free,qwen/qwen2.5-7b-instruct:free

# Market Data Providers
POLYGON_API_KEY=********************************	
FINNHUB_API_KEY=d2mjok1r01qog4441lsgd2mjok1r01qog4441lt0
ALPHA_VANTAGE_API_KEY=<ALPHA_KEY>
ALPACA_API_KEY=<ALPACA_KEY>
ALPACA_API_SECRET=<ALPACA_SECRET>
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Enable/disable
YAHOO_FINANCE_ENABLED=true
POLYGON_ENABLED=true
FINNHUB_ENABLED=true
ALPHA_VANTAGE_ENABLED=false
ALPACA_ENABLED=false

# Trading Parameters
RISK_PER_TRADE=0.02
MAX_POSITION_SIZE=0.1
STOP_LOSS_MULTIPLIER=2.0
TAKE_PROFIT_MULTIPLIER=3.0
MAX_OPEN_POSITIONS=5
MINIMUM_VOLUME_THRESHOLD=100000.0
PRICE_CHANGE_THRESHOLD=0.05
SUPPORT_RESISTANCE_PERCENTAGE=0.05

# Monitoring & Logging
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

LOG_FORMAT=json
LOG_FILE_PATH=/app/logs
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# Ngrok Configuration
NGROK_AUTHTOKEN=30I3DmIoKUoLSo1S6s2VR9hJbGT_51zxMZtp5pGKqR5sdGKHT

# Dev/Test Overrides (disable in production)
ENABLE_MOCK_DATA=false
ENABLE_DEBUG_LOGGING=true
ENABLE_PERFORMANCE_PROFILING=true
ENABLE_API_RESPONSE_LOGGING=true