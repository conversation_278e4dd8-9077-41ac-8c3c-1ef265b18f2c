# ============================================================================
# TRADINGVIEW AUTOMATION - ENHANCED UNIFIED SYSTEM ARCHITECTURE
# ============================================================================
# Enhanced version with additional security, monitoring, and reliability features
# ============================================================================

services:
  # ============================================================================
  # REVERSE PROXY (ENHANCED SECURITY LAYER)
  # ============================================================================
  nginx:
    image: nginx:alpine
    container_name: tradingview-nginx
    ports:
      - "80:80"    # HTTP redirect to HTTPS
      - "443:443"  # HTTPS only
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      api:
        condition: service_healthy
      webhook-proxy:
        condition: service_healthy
    networks:
      - external-network
      - internal-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "nginx -t || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.25'

  # ============================================================================
  # WEBHOOK PROXY (RATE LIMITED ACCESS)
  # ============================================================================
  webhook-proxy:
    image: nginx:alpine
    container_name: tradingview-webhook-proxy
    ports:
      - "8001:8001"  # ONLY webhook endpoint exposed
    volumes:
      - ./nginx/webhook.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      webhook-ingest:
        condition: service_healthy
    networks:
      - external-network
      - webhook-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "nginx -t || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 64M
          cpus: '0.25'

  # ============================================================================
  # NGROK TUNNEL (WEBHOOK EXPOSURE WITH MONITORING)
  # ============================================================================
  ngrok:
    image: ngrok/ngrok:latest
    container_name: tradingview-ngrok
    command:
      - http
      - webhook-proxy:8001
      - --log=stdout
      - --config=/etc/ngrok/ngrok.yml
    volumes:
      - ./ngrok.yml:/etc/ngrok/ngrok.yml:ro
    depends_on:
      webhook-proxy:
        condition: service_healthy
    networks:
      - webhook-network
    ports:
      - "4040:4040"  # Ngrok dashboard
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "nc -z localhost 4040 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'

  # ============================================================================
  # MAIN TRADING BOT API (ENHANCED INTERNAL SERVICE)
  # ============================================================================
  api:

    build:
      context: ../..
      dockerfile: docker/dockerfiles/app.Dockerfile
    container_name: tradingview-api
    command: uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --workers 2
    volumes:
      - ./src:/app/src:ro
      - ./config:/app/config:ro
      - ./secrets:/run/secrets:ro
      - ./logs:/app/logs
      - /etc/hosts:/etc/hosts:ro
    env_file:
      - ../../.env.secure
    environment:
      - PYTHONPATH=/app
      - REDIS_HOST=redis
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - internal-network
      - external-network
    dns:
      - *******
      - *******
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
      interval: 30s
      timeout: 30s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

  # ============================================================================
  # WEBHOOK INGEST SERVICE (ENHANCED PROCESSING)
  # ============================================================================
  webhook-ingest:
    build:
      context: ../../tradingview-ingest
      dockerfile: docker/dockerfiles/services/webhook.Dockerfile
    container_name: tradingview-webhook-ingest
    env_file:
      - ../../.env.secure
    environment:
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - ../../tradingview-ingest/logs:/app/logs
      - ../../tradingview-ingest/config:/app/config:ro
      - ../../tradingview-ingest/src:/app/src:ro
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - webhook-network
      - internal-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8001/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # ============================================================================
  # DISCORD BOT SERVICE (ENHANCED RELIABILITY)
  # ============================================================================
  discord-bot:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/bot.Dockerfile
    container_name: tradingview-discord-bot
    command: python -m src.bot.client
    env_file:
      - ../../.env.secure
    environment:
      - PYTHONPATH=/app
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SKIP_DB_CONNECTION=true
    volumes:
      - ../..//src:/app/src:ro
      - ../..//logs:/app/logs
      - ../..//config:/app/config:ro
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - external-network  # Allow external DNS access for Discord
      - internal-network  # Allow access to Redis
    dns:
      - *******  # Google DNS
      - *******  # Cloudflare DNS
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import discord; print('Discord bot healthy')"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # ============================================================================
  # REDIS CACHE (ENHANCED CONFIGURATION)
  # ============================================================================
  redis:
    image: redis:7-alpine
    container_name: tradingview-redis
    command: redis-server --requirepass "${REDIS_PASSWORD}" --maxmemory 256mb --maxmemory-policy allkeys-lru --save 900 1 --save 300 10 --save 60 10000 --appendonly yes --appendfsync everysec --tcp-keepalive 300 --timeout 0
    volumes:
      - redis_data:/data
      - ./redis/logs:/var/log/redis
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    networks:
      - internal-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 384M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # ============================================================================
  # MONITORING AND LOGGING (OPTIONAL)
  # ============================================================================
  # Watchtower service has been removed as it was not being used by any other services
  # and posed a potential security risk by having access to the Docker socket.

volumes:
  redis_data:

networks:
  # EXTERNAL NETWORK - Accessible from outside
  external-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
  
  # WEBHOOK NETWORK - Isolated webhook processing
  webhook-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
  
  # INTERNAL NETWORK - Core services only
  internal-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
