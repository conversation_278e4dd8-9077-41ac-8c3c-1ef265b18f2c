version: '3.8'

services:
  # Webhook Receiver - Faces Internet (Minimal Attack Surface)
  webhook-receiver:
    build:
      context: ../../..
      dockerfile: docker/dockerfiles/services/webhook.secure.Dockerfile
    container_name: tradingview-webhook-receiver
    ports:
      - "8001:8001"  # Only this port exposed
    environment:
      - REDIS_URL=redis://tradingview-redis:6380/0
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
      - PROCESSING_QUEUE=webhook_queue
      - MAX_CONCURRENT_WEBHOOKS=50
      - WEBHOOK_TIMEOUT=30
      - LOG_LEVEL=INFO
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_DB_URL=${SUPABASE_DB_URL}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    networks:
      - webhook_network
      - internal_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/tmp

  # Processing Engine - No Internet Access
  processing-engine:
    build:
      context: ../../..
      dockerfile: docker/dockerfiles/services/processor.Dockerfile
    container_name: tradingview-processor
    environment:
      - REDIS_URL=redis://tradingview-redis:6380/0
      - PROCESSING_QUEUE=webhook_queue
      - RESULT_QUEUE=result_queue
      - MAX_WORKERS=10
      - BATCH_SIZE=100
      - LOG_LEVEL=INFO
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_DB_URL=${SUPABASE_DB_URL}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    networks:
      - internal_network
    restart: unless-stopped
    depends_on:
      - tradingview-redis
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/tmp

  # Redis - Internal Network Only
  tradingview-redis:
    image: redis:7-alpine
    container_name: tradingview-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - internal_network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    read_only: false  # Redis needs write access
    tmpfs:
      - /var/tmp

  # Monitoring - Internal Network Only
  monitoring:
    build:
      context: ../../..
      dockerfile: docker/dockerfiles/services/monitor.Dockerfile
    container_name: tradingview-monitor
    environment:
      - REDIS_URL=redis://tradingview-redis:6380/0
      - METRICS_PORT=9090
      - LOG_LEVEL=INFO
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_DB_URL=${SUPABASE_DB_URL}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    networks:
      - internal_network
    restart: unless-stopped
    depends_on:
      - tradingview-redis
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local

networks:
  # External network for webhook receiver only
  webhook_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.bridge.enable_ip_masquerade: "false"
  
  # Internal network for secure communication
  internal_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    internal: true  # No internet access
    driver_opts:
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "false" 