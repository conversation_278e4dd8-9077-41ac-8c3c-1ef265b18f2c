# ============================================================================
# OPTIMIZED DEVELOPMENT CONFIGURATION FOR DISCORD BOT
# ============================================================================
# Enhanced setup with improved health checks and dependency management
#
# To run:
#   docker-compose -f development.optimized.yml up --build
#
# To stop:
#   docker-compose -f development.optimized.yml down
# ============================================================================

version: '3.8'

services:
  # ============================================================================
  # DISCORD BOT SERVICE
  # ============================================================================
  discord-bot:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/bot.Dockerfile
    container_name: tradingview-discord-bot-dev
    # This command uses 'watchmedo' to auto-restart the bot on file changes
    command: >
      watchmedo auto-restart 
      --directory=/app/src 
      --pattern="*.py" 
      --recursive -- 
      python -m src.bot.client
    env_file:
      - ../../.env.secure
    environment:
      - PYTHONPATH=/app
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}  # Must be set in .env file
      - SUPABASE_DB_URL=${SUPABASE_DB_URL}
      - DISCORD_BOT_TOKEN=${DISCORD_BOT_TOKEN}
      - DISCORD_ENABLED=${DISCORD_ENABLED:-true}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-tradingview_pass}@redis:6379/0
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - ALPACA_API_KEY=${ALPACA_API_KEY}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - DISCORD_COMMAND_PREFIX=${DISCORD_COMMAND_PREFIX:-/}
    volumes:
      # Mount source code for hot reloading
      - ../../src:/app/src
      - ../../config:/app/config:ro
      - ../../logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import discord; print('Discord bot healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    # Resource limits to prevent container from consuming too much memory/CPU
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

  # ============================================================================
  # API SERVICE
  # ============================================================================
  api:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/app.Dockerfile
    container_name: tradingview-api-dev
    command: uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --workers 2 --reload
    volumes:
      - ../..:/app
      - ../../logs:/app/logs
    env_file:
      - ../../.env.secure
    environment:
      - PYTHONPATH=/app
      - SUPABASE_URL=${SUPABASE_URL:-https://example.supabase.co}
      - SUPABASE_KEY=${SUPABASE_KEY}  # Must be set in .env file
      - USE_SUPABASE=${USE_SUPABASE:-true}
      - SUPABASE_DB_URL=${SUPABASE_DB_URL:-postgresql://postgres:postgres@localhost:5432/postgres}
      - DISCORD_BOT_TOKEN=${DISCORD_BOT_TOKEN}
      - REDIS_URL=redis://redis:6379/0
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    ports:
      - "8000:8000"

  # ============================================================================
  # REDIS CACHE WITH IMPROVED CONFIGURATION
  # ============================================================================
  redis:
    image: redis:7-alpine
    container_name: tradingview-redis-dev
    command: >
      redis-server
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --appendonly yes
      --appendfsync everysec
    volumes:
      - redis_data_dev:/data
      - ../../logs/redis:/var/log/redis
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s
    restart: unless-stopped
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 384M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # ============================================================================
  # WEBHOOK INGEST SERVICE FOR TESTING
  # ============================================================================
  webhook-ingest:
    build:
      context: ../../tradingview-ingest
      dockerfile: docker/dockerfiles/services/webhook.Dockerfile
    container_name: tradingview-webhook-ingest-dev
    env_file:
      - ../../.env.secure
    environment:
      - USE_SUPABASE=${USE_SUPABASE:-true}
      - SUPABASE_URL=${SUPABASE_URL:-https://example.supabase.co}
      - SUPABASE_KEY=${SUPABASE_KEY}  # Must be set in .env file
      - SUPABASE_DB_URL=${SUPABASE_DB_URL:-postgresql://postgres:postgres@localhost:5432/postgres}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-tradingview_pass}@redis:6379/0
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET:-development_webhook_secret}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-tradingview_pass}
      - REQUIRE_WEBHOOK_SIGNATURE=${REQUIRE_WEBHOOK_SIGNATURE:-false}
      - MAX_WORKERS=${WEBHOOK_MAX_WORKERS:-2}
      - RATE_LIMIT_PER_MINUTE=${WEBHOOK_RATE_LIMIT:-60}
    volumes:
      - ../../tradingview-ingest/logs:/app/logs
      - ../../tradingview-ingest/config:/app/config:ro
      - ../../tradingview-ingest/src:/app/src:ro
    ports:
      - "18001:8001"  # Use alternate host port in dev to avoid conflict with webhook-proxy
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

volumes:
  redis_data_dev:
