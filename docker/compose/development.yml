# ============================================================================
# DEVELOPMENT CONFIGURATION FOR DISCORD BOT
# ============================================================================
# A simplified setup to focus only on the discord-bot service.
# Uses Supabase for database in development to match production environment.
#
# To run:
#   docker-compose -f development.yml up --build
#
# To stop:
#   docker-compose -f development.yml down
# ============================================================================

version: '3.8'

services:
  # ============================================================================
  # DISCORD BOT SERVICE
  # ============================================================================
  discord-bot:
    build:
      context: ../..
      dockerfile: docker/dockerfiles/bot.Dockerfile
    container_name: tradingview-discord-bot-dev
    # This command uses 'watchmedo' to auto-restart the bot on file changes.
    # It watches all .py files in the /app/src directory.
    command: >
      watchmedo auto-restart 
      --directory=/app/src 
      --pattern="*.py" 
      --recursive -- 
      python -m src.bot.client
    env_file:
      - ../../.env.secure # Make sure this file has all the required variables
    environment:
      - PYTHONPATH=/app
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_DB_URL=${SUPABASE_DB_URL}
      - DISCORD_BOT_TOKEN=${DISCORD_BOT_TOKEN}
      - DISCORD_ENABLED=${DISCORD_ENABLED:-true}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG} # Set to DEBUG for better development logs
      # Add other API keys as needed from your .env.secure
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - ALPACA_API_KEY=${ALPACA_API_KEY}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - DISCORD_COMMAND_PREFIX=${DISCORD_COMMAND_PREFIX:-!}
    volumes:
      # Mount your source code to see changes without rebuilding the image
      - ../../src:/app/src
      - ../../config:/app/config:ro
    depends_on:
      redis:
        condition: service_healthy

  # ============================================================================
  # REDIS CACHE
  # ============================================================================
  redis:
    image: redis:7-alpine
    container_name: tradingview-redis-dev
    command: redis-server --requirepass ${REDIS_PASSWORD} # Simplified: from .env.secure
    volumes:
      - redis_data_dev:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped

volumes:
  redis_data_dev: