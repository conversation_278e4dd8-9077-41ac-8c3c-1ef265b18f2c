# Docker Configuration

This directory contains all Docker configurations for the TradingView Automation project, organized in a standardized structure.

## Directory Structure

```
docker/
├── compose/                 # Docker Compose files
│   ├── production.yml       # Full production deployment
│   ├── development.yml      # Basic development setup
│   ├── development.optimized.yml  # Enhanced development setup
│   └── services/            # Service-specific configurations
│       ├── tradingview-ingest.yml  # Webhook processing system
│       └── pipeline.yml     # AI pipeline system
├── dockerfiles/             # Docker build files
│   ├── app.Dockerfile       # Main application
│   ├── bot.Dockerfile       # Discord bot service
│   ├── app.optimized.Dockerfile  # Optimized application build
│   ├── test.Dockerfile      # Testing environment
│   └── services/            # Service Dockerfiles
│       ├── webhook.Dockerfile     # Webhook receiver
│       ├── webhook.secure.Dockerfile  # Secure webhook receiver
│       ├── processor.Dockerfile   # Data processor
│       ├── monitor.Dockerfile     # Monitoring service
│       └── pipeline.Dockerfile    # Pipeline system
└── config/                  # Docker configuration files
    ├── env.template         # Template for environment variables
    ├── env.development      # Development environment variables
    └── env.production       # Production environment variables
```

## Usage

### Production
```bash
# From the project root directory
docker-compose -f docker/compose/production.yml up --build
```

### Development
```bash
# Basic development setup
docker-compose -f docker/compose/development.yml up --build

# Enhanced development setup
docker-compose -f docker/compose/development.optimized.yml up --build
```

### Services
```bash
# Webhook processing system
docker-compose -f docker/compose/services/tradingview-ingest.yml up --build

# AI pipeline system
docker-compose -f docker/compose/services/pipeline.yml up --build
```

## Environment Files

- `env.template`: Complete list of all possible environment variables
- `env.development`: Variables for development environment
- `env.production`: Variables for production environment

Note: Sensitive information should be stored in `env.secrets` which is not committed to version control.