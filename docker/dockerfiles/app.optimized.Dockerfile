# Multi-stage optimized build for security and performance
FROM python:3.11-slim-bullseye AS builder

# Set environment variables for better Python behavior
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install build dependencies with proper cleanup
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    pkg-config \
    libpq-dev \
    default-libmysqlclient-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better layer caching
COPY requirements.txt .

# Install Python dependencies with optimizations
RUN pip install --upgrade pip && \
    pip install --user -r requirements.txt

# Production stage with minimal image
FROM python:3.11-slim-bullseye AS production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# Install only runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    libpq5 \
    && rm -rf /var/lib/apt/lists/* && \
    apt-get clean

# Create non-root user with proper permissions
RUN groupadd -r appuser && useradd -r -g appuser -d /home/<USER>
    mkdir -p /app /app/logs /app/secrets && \
    chown -R appuser:appuser /app /app/logs /app/secrets && \
    chmod 700 /app/secrets

# Set working directory
WORKDIR /app

# Copy Python packages from builder
COPY --from=builder --chown=appuser:appuser /root/.local /home/<USER>/.local

# Copy application code with proper ownership
COPY --chown=appuser:appuser . .

# Add entrypoint script
COPY --chown=appuser:appuser docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Switch to non-root user
USER appuser

# Set PATH for user-installed packages
ENV PATH="/home/<USER>/.local/bin:$PATH"

# Improved health check with longer start period
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command with worker configuration
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "2"]
