FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src/ ./src/

# Copy test files
COPY test_enhanced_engines_only.py .
COPY test_integrated_analysis.py .

# Set Python path
ENV PYTHONPATH=/app

# Default command
CMD ["python", "test_enhanced_engines_only.py"] 