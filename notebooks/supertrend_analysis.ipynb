{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Supertrend Analysis for QQQ\n", "\n", "Analyzing QQQ using our custom Supertrend indicator\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "# Add project root to path\n", "project_root = os.path.abspath(os.path.join(os.getcwd(), '..'))\n", "sys.path.insert(0, project_root)\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import yfinance as yf\n", "import matplotlib.pyplot as plt\n", "\n", "from src.shared.technical_analysis.indicators import calculate_supertrend\n", "from src.shared.technical_analysis.strategy_calculator import PositionStrategyCalculator\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fetch QQQ data\n", "ticker = 'QQQ'\n", "start_date = '2023-01-01'\n", "end_date = pd.Timestamp.now().strftime('%Y-%m-%d')\n", "\n", "# Download daily data\n", "df = yf.download(ticker, start=start_date, end=end_date)\n", "\n", "# Display first few rows\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate Supertrend with different parameters\n", "def analyze_supertrend(df, period=10, multiplier=3.0):\n", "    # Calculate Supertrend\n", "    supertrend = calculate_supertrend(\n", "        high=df['High'], \n", "        low=df['Low'], \n", "        close=df['Close'],\n", "        period=period,\n", "        multiplier=multiplier\n", "    )\n", "    \n", "    return supertrend\n", "\n", "# Run analysis\n", "result = analyze_supertrend(df)\n", "print(\"Supertrend Analysis Result:\")\n", "for key, value in result.items():\n", "    print(f\"{key}: {value}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize Supertrend\n", "plt.figure(figsize=(15,8))\n", "plt.plot(df.index, df['Close'], label='QQQ Close Price', alpha=0.7)\n", "\n", "# Plot Supertrend bands\n", "supertrend_full = df.apply(\n", "    lambda row: calculate_supertrend(\n", "        high=df['High'][:row.name+1], \n", "        low=df['Low'][:row.name+1], \n", "        close=df['Close'][:row.name+1]\n", "    ), \n", "    axis=1\n", ")\n", "\n", "# Extract upper and lower bands\n", "upper_bands = supertrend_full.apply(lambda x: x['upper_band'])\n", "lower_bands = supertrend_full.apply(lambda x: x['lower_band'])\n", "\n", "plt.plot(df.index, upper_bands, label='Upper Supertrend Band', color='green', alpha=0.5)\n", "plt.plot(df.index, lower_bands, label='Lower Supertrend Band', color='red', alpha=0.5)\n", "\n", "plt.title(f'QQQ Supertrend Analysis (Period: 10, Multiplier: 3.0)')\n", "plt.xlabel('Date')\n", "plt.ylabel('Price')\n", "plt.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Trend Flip Analysis\n", "def analyze_trend_flips(df):\n", "    trend_flips = []\n", "    prev_trend = None\n", "    \n", "    for index, row in df.iterrows():\n", "        supertrend = calculate_supertrend(\n", "            high=df['High'][:index+1], \n", "            low=df['Low'][:index+1], \n", "            close=df['Close'][:index+1],\n", "            prev_trend=prev_trend\n", "        )\n", "        \n", "        if supertrend['flip_price'] is not None:\n", "            trend_flips.append({\n", "                'date': index,\n", "                'price': supertrend['flip_price'],\n", "                'from_trend': prev_trend,\n", "                'to_trend': supertrend['trend']\n", "            })\n", "        \n", "        prev_trend = supertrend['trend']\n", "    \n", "    return pd.DataFrame(trend_flips)\n", "\n", "# Get trend flips\n", "trend_flips = analyze_trend_flips(df)\n", "print(\"Trend Flips:\")\n", "print(trend_flips)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Performance of Trend Flips\n", "def calculate_flip_performance(df, trend_flips):\n", "    performance = []\n", "    \n", "    for _, flip in trend_flips.iterrows():\n", "        # Find the next significant price point (e.g., 5 days later)\n", "        future_index = df.index.get_loc(flip['date']) + 5\n", "        if future_index < len(df):\n", "            future_price = df.iloc[future_index]['Close']\n", "            entry_price = flip['price']\n", "            \n", "            # Calculate percentage change\n", "            pct_change = (future_price - entry_price) / entry_price * 100\n", "            \n", "            performance.append({\n", "                'date': flip['date'],\n", "                'from_trend': flip['from_trend'],\n", "                'to_trend': flip['to_trend'],\n", "                'entry_price': entry_price,\n", "                'future_price': future_price,\n", "                'pct_change': pct_change\n", "            })\n", "    \n", "    return pd.<PERSON><PERSON><PERSON><PERSON>(performance)\n", "\n", "# Analyze performance\n", "flip_performance = calculate_flip_performance(df, trend_flips)\n", "print(\"Trend Flip Performance:\")\n", "print(flip_performance)\n", "\n", "# Summary statistics\n", "print(\"\\nPerformance Summary:\")\n", "print(f\"Total Trend Flips: {len(flip_performance)}\")\n", "print(f\"Average % Change: {flip_performance['pct_change'].mean():.2f}%\")\n", "print(f\"Median % Change: {flip_performance['pct_change'].median():.2f}%\")\n", "print(f\"Win Rate: {(flip_performance['pct_change'] > 0).mean() * 100:.2f}%\")\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}