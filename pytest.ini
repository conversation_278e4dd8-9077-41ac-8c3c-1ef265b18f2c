[pytest]
addopts = -v --doctest-modules --cov=src --cov-report=term-missing
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto

[coverage:run]
source = src
omit = 
    src/*/__init__.py
    src/core/config.py
    src/core/logger.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    raise NotImplementedError
    if __name__ == .__main__.:
