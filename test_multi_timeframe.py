#!/usr/bin/env python3
"""
Test Script for Multi-Timeframe Analysis Engine

Tests the new multi-timeframe technical analysis capabilities.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_multi_timeframe_analyzer():
    """Test the multi-timeframe analyzer functionality."""
    print("🔍 Testing Multi-Timeframe Analysis Engine...")
    
    try:
        # Import the analyzer
        from shared.technical_analysis.multi_timeframe_analyzer import (
            MultiTimeframeAnalyzer, Timeframe, TimeframeData
        )
        
        print("✅ Multi-timeframe analyzer imported successfully")
        
        # Create analyzer instance
        analyzer = MultiTimeframeAnalyzer()
        print("✅ Analyzer instance created")
        
        # Test timeframe selection
        quick_timeframes = Timeframe.get_quick_timeframes()
        standard_timeframes = Timeframe.get_standard_timeframes()
        deep_timeframes = Timeframe.get_deep_timeframes()
        
        print(f"✅ Timeframe selection working:")
        print(f"   Quick: {[tf.value for tf in quick_timeframes]}")
        print(f"   Standard: {[tf.value for tf in standard_timeframes]}")
        print(f"   Deep: {[tf.value for tf in deep_timeframes]}")
        
        # Test TimeframeData creation
        sample_data = TimeframeData(
            timeframe=Timeframe.ONE_DAY,
            opens=[100.0, 101.0, 102.0, 103.0, 104.0],
            highs=[101.5, 102.5, 103.5, 104.5, 105.5],
            lows=[99.5, 100.5, 101.5, 102.5, 103.5],
            closes=[101.0, 102.0, 103.0, 104.0, 105.0],
            volumes=[1000000, 1100000, 1200000, 1300000, 1400000],
            timestamps=[datetime.now() - timedelta(days=i) for i in range(5, 0, -1)]
        )
        
        print("✅ TimeframeData creation working")
        print(f"   Latest price: ${sample_data.get_latest_price():.2f}")
        print(f"   Price change: ${sample_data.get_price_change()[0]:.2f} ({sample_data.get_price_change()[1]:.1f}%)")
        
        # Test basic calculations
        if sample_data.get_latest_price() == 105.0:
            print("✅ Price calculations working")
        else:
            print("❌ Price calculations not working")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing multi-timeframe analyzer: {e}")
        return False

def test_technical_indicators():
    """Test technical indicator calculations."""
    print("\n🔍 Testing Technical Indicators...")
    
    try:
        from shared.technical_analysis.multi_timeframe_analyzer import MultiTimeframeAnalyzer
        
        analyzer = MultiTimeframeAnalyzer()
        
        # Test SMA calculation
        prices = [100.0, 101.0, 102.0, 103.0, 104.0]
        sma_5 = analyzer._calculate_sma(prices, 5)
        if abs(sma_5 - 102.0) < 0.01:
            print("✅ SMA calculation working")
        else:
            print(f"❌ SMA calculation failed: expected 102.0, got {sma_5}")
            return False
        
        # Test RSI calculation
        rsi_prices = [100, 101, 102, 101, 100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90]
        rsi = analyzer._calculate_rsi(rsi_prices, 14)
        if 0 <= rsi <= 100:
            print("✅ RSI calculation working")
        else:
            print(f"❌ RSI calculation failed: got {rsi}")
            return False
        
        # Test Bollinger Bands
        bb_prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119]
        bb = analyzer._calculate_bollinger_bands(bb_prices, 20)
        if bb["middle"] > bb["lower"] and bb["upper"] > bb["middle"]:
            print("✅ Bollinger Bands calculation working")
        else:
            print(f"❌ Bollinger Bands calculation failed: {bb}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing technical indicators: {e}")
        return False

def test_pattern_detection():
    """Test chart pattern detection."""
    print("\n🔍 Testing Pattern Detection...")
    
    try:
        from shared.technical_analysis.multi_timeframe_analyzer import MultiTimeframeAnalyzer
        
        analyzer = MultiTimeframeAnalyzer()
        
        # Test uptrend detection
        uptrend_prices = [100, 101, 102, 103, 104, 105]
        is_uptrend = analyzer._is_uptrend(uptrend_prices)
        if is_uptrend:
            print("✅ Uptrend detection working")
        else:
            print("❌ Uptrend detection failed")
            return False
        
        # Test downtrend detection
        downtrend_prices = [105, 104, 103, 102, 101, 100]
        is_downtrend = analyzer._is_downtrend(downtrend_prices)
        if is_downtrend:
            print("✅ Downtrend detection working")
        else:
            print("❌ Downtrend detection failed")
            return False
        
        # Test price clustering
        clustered_prices = [100, 100.1, 100.2, 105, 105.1, 105.2, 110, 110.1, 110.2]
        clusters = analyzer._find_price_clusters(clustered_prices, tolerance=0.02)
        if len(clusters) >= 2:
            print("✅ Price clustering working")
        else:
            print(f"❌ Price clustering failed: expected >=2 clusters, got {len(clusters)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing pattern detection: {e}")
        return False

def run_all_tests():
    """Run all multi-timeframe analysis tests."""
    print("🚀 Running Multi-Timeframe Analysis Tests...")
    print("=" * 60)
    
    tests = [
        ("Multi-Timeframe Analyzer", test_multi_timeframe_analyzer),
        ("Technical Indicators", test_technical_indicators),
        ("Pattern Detection", test_pattern_detection)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Multi-timeframe analysis engine is working properly!")
        print("🚀 Ready to integrate with the main pipeline")
    else:
        print("⚠️  Some multi-timeframe features need attention")
        print("🔧 Please fix the failing tests before continuing")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 