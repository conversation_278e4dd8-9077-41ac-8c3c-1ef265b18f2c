#!/usr/bin/env python3
"""
Comprehensive test for the response template format fix.
Tests the actual ResponseTemplateEngine with problematic data.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_response_template_engine():
    """Test the ResponseTemplateEngine with problematic data that would cause format errors."""
    print("Testing ResponseTemplateEngine with format error scenarios...")
    
    try:
        from src.bot.pipeline.commands.ask.stages.response_templates import ResponseTemplateEngine, ResponseStyle
        
        # Initialize template engine
        engine = ResponseTemplateEngine()
        print("✅ ResponseTemplateEngine initialized successfully")
        
        # Test data that would previously cause format errors
        problematic_data = {
            'symbol': 'AAPL',
            'price': 'N/A',  # String instead of number
            'current_price': '150.50',  # String number
            'change': 'unknown',  # Non-numeric string
            'change_percent': 2.5,  # Proper number
            'volume': 'high',  # Descriptive string
            'market_cap': None,  # None value
            'support': '',  # Empty string
            'resistance': 'unavailable',  # Non-numeric string
            'rsi': '65.5',  # String number
            'macd': 'bullish',  # Descriptive string
        }
        
        test_analysis = {
            'complexity': 'moderate',
            'symbols': ['AAPL']
        }
        
        # Test with different styles
        styles_to_test = [
            ResponseStyle.SIMPLE,
            ResponseStyle.DETAILED,
            ResponseStyle.TECHNICAL,
            ResponseStyle.PROFESSIONAL
        ]
        
        results = []
        for style in styles_to_test:
            try:
                response = engine.generate_response(
                    template_type="stock_analysis",
                    style=style,
                    data=problematic_data,
                    query_analysis=test_analysis
                )
                
                print(f"✅ {style.name} style: Generated {len(response)} character response")
                results.append((style.name, True, len(response)))
                
                # Check that the response doesn't contain unresolved placeholders
                if '{' in response and '}' in response:
                    unresolved = []
                    import re
                    placeholders = re.findall(r'\{[^}]+\}', response)
                    for placeholder in placeholders:
                        if placeholder not in ['{symbol}', '{price}', '{change}']:  # Allow some expected placeholders
                            unresolved.append(placeholder)
                    
                    if unresolved:
                        print(f"⚠️  {style.name} style: Found unresolved placeholders: {unresolved}")
                
            except Exception as e:
                print(f"❌ {style.name} style: Error - {e}")
                results.append((style.name, False, str(e)))
        
        return results
        
    except Exception as e:
        print(f"❌ Error testing ResponseTemplateEngine: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_safe_dict_directly():
    """Test the SafeDict and SafeValue classes directly."""
    print("\nTesting SafeDict and SafeValue directly...")
    
    try:
        from src.bot.pipeline.commands.ask.stages.response_templates import SafeDict
        
        # Test data with various problematic values
        test_data = {
            'price': 'N/A',
            'support': '150.50',
            'change': 'unknown',
            'volume': 'high',
            'market_cap': None,
            'rsi': 65.5,
            'empty_field': '',
        }
        
        safe_data = SafeDict(test_data)
        
        # Test templates that would previously cause errors
        test_templates = [
            ("Price: ${price:.2f}", "Price: $0.00"),
            ("Support: ${support:.2f}", "Price: $150.50"),
            ("Change: {change:.2f}%", "Change: unknown%"),
            ("Volume: {volume:,}", "Volume: high"),
            ("Market cap: ${market_cap:.2f}B", "Market cap: $0.00B"),
            ("RSI: {rsi:.1f}", "RSI: 65.5"),
            ("Empty: {empty_field:.2f}", "Empty: 0.00"),
        ]
        
        all_passed = True
        for template, expected_pattern in test_templates:
            try:
                result = template.format(**safe_data)
                print(f"✅ Template: {template}")
                print(f"   Result: {result}")
                
                # Basic validation - no format errors occurred
                if "unknown" in template and "unknown" in result:
                    print(f"   ✓ Descriptive string preserved")
                elif "N/A" in str(test_data.get(template.split('{')[1].split(':')[0], '')):
                    if "0.00" in result:
                        print(f"   ✓ N/A converted to default numeric value")
                
            except Exception as e:
                print(f"❌ Template: {template}")
                print(f"   Error: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing SafeDict: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_templates_ask_module():
    """Test the templates/ask.py module as well."""
    print("\nTesting templates/ask.py module...")
    
    try:
        from src.templates.ask import ResponseTemplateEngine as AskTemplateEngine, SafeDict as AskSafeDict
        
        # Test SafeDict from ask module
        test_data = {
            'price': 'N/A',
            'change': 'unknown',
            'volume': 'high',
        }
        
        safe_data = AskSafeDict(test_data)
        
        test_templates = [
            "Price: ${price:.2f}",
            "Change: {change:.2f}%",
            "Volume: {volume}",
        ]
        
        all_passed = True
        for template in test_templates:
            try:
                result = template.format(**safe_data)
                print(f"✅ Ask module template: {template} -> {result}")
            except Exception as e:
                print(f"❌ Ask module template: {template} -> Error: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing ask module: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Response Template Format Fix - Comprehensive Test Suite")
    print("=" * 70)
    
    # Test 1: ResponseTemplateEngine
    engine_results = test_response_template_engine()
    
    # Test 2: SafeDict directly
    safe_dict_passed = test_safe_dict_directly()
    
    # Test 3: Templates ask module
    ask_module_passed = test_templates_ask_module()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 Test Results Summary:")
    
    if engine_results:
        engine_passed = all(result[1] for result in engine_results)
        print(f"  ResponseTemplateEngine: {'✅ PASSED' if engine_passed else '❌ FAILED'}")
        for style, passed, info in engine_results:
            status = "✅" if passed else "❌"
            print(f"    {status} {style}: {info}")
    else:
        print(f"  ResponseTemplateEngine: ❌ FAILED (could not test)")
        engine_passed = False
    
    print(f"  SafeDict direct test: {'✅ PASSED' if safe_dict_passed else '❌ FAILED'}")
    print(f"  Templates ask module: {'✅ PASSED' if ask_module_passed else '❌ FAILED'}")
    
    overall_passed = engine_passed and safe_dict_passed and ask_module_passed
    
    print("\n" + "=" * 70)
    if overall_passed:
        print("🎉 ALL TESTS PASSED! Format error fix is working correctly.")
        print("✅ The 'Unknown format code 'f' for object of type 'str'' error has been resolved.")
        return 0
    else:
        print("💥 SOME TESTS FAILED! Please review the failing cases above.")
        return 1

if __name__ == "__main__":
    exit(main())
