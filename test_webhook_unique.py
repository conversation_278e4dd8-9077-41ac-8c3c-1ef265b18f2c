#!/usr/bin/env python3
"""
Test script for TradingView webhook endpoint with unique identifier
"""

import json
import hmac
import hashlib
import requests
import os
import uuid
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Get webhook secret from environment
webhook_secret = os.getenv("WEBHOOK_SECRET")
if not webhook_secret:
    print("Error: WEBHOOK_SECRET environment variable not set.")
    exit(1)

# Create unique identifier
unique_id = str(uuid.uuid4())[:8]
timestamp = datetime.now().isoformat()

# Test payload with unique identifier
payload = {
    "symbol": "AAPL",
    "price": 150.25,
    "action": "BUY",
    "test": True,
    "unique_id": unique_id,
    "timestamp": timestamp
}

# Create signature
payload_str = json.dumps(payload, separators=(',', ':'))
signature = hmac.new(
    webhook_secret.encode('utf-8'),
    payload_str.encode('utf-8'),
    hashlib.sha256
).hexdigest()

# Send request
url = "http://localhost:8001/webhook/tradingview"
headers = {
    "Content-Type": "application/json",
    "X-TradingView-Signature": signature
}

print(f"Sending webhook with unique_id: {unique_id}")
response = requests.post(url, json=payload, headers=headers)

# Print response
print(f"Status Code: {response.status_code}")
print(f"Response: {response.text}")
print(f"Remember this unique_id: {unique_id} for checking the database")
