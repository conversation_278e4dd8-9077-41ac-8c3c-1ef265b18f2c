#!/usr/bin/env python3
"""
Test script to validate the centralized configuration system.
Run this to ensure the configuration loads correctly and provides expected values.
"""

import os
import sys
from pathlib import Path

# Add src to path to import modules
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.shared.configuration import Config, get_config

def test_config_loading():
    """Test that the configuration loads without errors"""
    print("🧪 Testing configuration loading...")
    
    try:
        config = get_config()
        print("✅ Configuration loaded successfully")
        
        # Test basic properties
        print(f"Environment: {config._config.get('app', {}).get('environment', 'N/A')}")
        print(f"Debug mode: {config._config.get('app', {}).get('debug', False)}")
        print(f"Database URL: {config._config.get('database', {}).get('url', 'N/A')}")
        print(f"Redis URL: {config._config.get('redis', {}).get('url', 'N/A')}")
        
        # Test provider configuration
        alpha_vantage_config = config._config.get('data_providers', {}).get('alpha_vantage', {})
        print(f"Alpha Vantage config: {alpha_vantage_config}")
        
        yfinance_config = config._config.get('data_providers', {}).get('yahoo_finance', {})
        print(f"Yahoo Finance config: {yfinance_config}")
        
        # Test pipeline config
        pipeline_config = config._config.get('pipeline', {})
        print(f"Pipeline config: {pipeline_config}")
        
        # Test API config
        api_config = config._config.get('api', {})
        print(f"API config: {api_config}")
        
        # Test database config
        db_config = config._config.get('database', {})
        print(f"Database config: {db_config}")
        
        # Test config structure
        print(f"Available config sections: {list(config._config.keys())}")
        
        print("✅ All configuration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_env_var_validation():
    """Test environment variable validation"""
    print("\n🧪 Testing environment variable validation...")
    
    from src.shared.configuration.validators import validate_env_var, ValidationResult
    
    # Test string validation
    result = validate_env_var("TEST_STRING", "hello", str)
    print(f"String validation: {result}")
    
    # Test integer validation
    result = validate_env_var("TEST_INT", "42", int)
    print(f"Integer validation: {result}")
    
    # Test float validation
    result = validate_env_var("TEST_FLOAT", "3.14", float)
    print(f"Float validation: {result}")
    
    # Test boolean validation
    result = validate_env_var("TEST_BOOL", "true", bool)
    print(f"Boolean validation: {result}")
    
    # Test validation with min value
    result = validate_env_var("TEST_MIN", "5", int, min_value=10)
    print(f"Min value validation: {result}")
    
    print("✅ Environment variable validation tests completed!")

if __name__ == "__main__":
    print("🚀 Starting configuration system tests...")
    print("=" * 50)
    
    success = test_config_loading()
    test_env_var_validation()
    
    print("=" * 50)
    if success:
        print("🎉 All tests passed! Configuration system is working correctly.")
        sys.exit(0)
    else:
        print("💥 Some tests failed. Please check the configuration.")
        sys.exit(1)