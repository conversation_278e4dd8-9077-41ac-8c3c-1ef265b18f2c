#!/usr/bin/env python3
"""
Test script for the fixed pipeline.
Tests the ask pipeline with various query types.
"""

import asyncio
import logging
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_fixed_pipeline():
    """Test the fixed ask pipeline"""
    logger.info('🧪 Testing Stock Query with Fixed Pipeline...')
    
    try:
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        
        # Test query
        query = "What is the current price of AAPL and show me technical indicators?"
        
        # Execute pipeline
        result = await execute_ask_pipeline(query)
        
        if result and result.processing_results:
            logger.info(f'📋 Available keys: {list(result.processing_results.keys())}')
            
            # Check data collection
            if 'data_collection' in result.processing_results:
                data = result.processing_results['data_collection']
                logger.info(f'📈 Collected data keys: {list(data.keys()) if isinstance(data, dict) else "Not a dict"}')
                
                # Check if we have AAPL data
                if 'AAPL' in data:
                    aapl_data = data['AAPL']
                    logger.info(f'✅ AAPL data available: {type(aapl_data)}')
                    
                    # Check for price data
                    if hasattr(aapl_data, 'current_price') or hasattr(aapl_data, 'price'):
                        price = getattr(aapl_data, 'current_price', None) or getattr(aapl_data, 'price', None)
                        logger.info(f'💰 AAPL Price: ${price}')
                    else:
                        logger.warning('⚠️ No price data found for AAPL')
                    
                    # Check for technical indicators
                    if hasattr(aapl_data, 'technical_indicators'):
                        indicators = aapl_data.technical_indicators
                        logger.info(f'📊 Technical indicators: {type(indicators)}')
                    else:
                        logger.info('ℹ️ No technical indicators found')
                else:
                    logger.warning('⚠️ No AAPL data found in results')
            else:
                logger.warning('⚠️ No data collection results found')
            
            # Check response generation
            if 'response_generation' in result.processing_results:
                response = result.processing_results['response_generation']
                logger.info(f'💬 Response generated: {type(response)}')
                if hasattr(response, 'content'):
                    logger.info(f'📝 Response content: {response.content[:100]}...')
            else:
                logger.warning('⚠️ No response generation results found')
                
        else:
            logger.error('❌ No result or processing results available')
            return False
            
        # Test fallback data generation
        try:
            from src.bot.pipeline.commands.ask.stages.fallback_data_generator import FallbackDataGenerator
            
            fallback_gen = FallbackDataGenerator()
            fallback_data = await fallback_gen.generate_fallback_data(['AAPL', 'MSFT'])
            
            if fallback_data:
                logger.info(f'✅ Fallback data generation working: {list(fallback_data.keys())}')
                
                # Check AAPL fallback data
                if 'AAPL' in fallback_data:
                    aapl_fallback = fallback_data['AAPL']
                    logger.info(f'📊 AAPL fallback data: {type(aapl_fallback)}')
                    
                    # Check for required fields
                    required_fields = ['symbol', 'status', 'message', 'timestamp']
                    missing_fields = [field for field in required_fields if not hasattr(aapl_fallback, field)]
                    
                    if missing_fields:
                        logger.warning(f'⚠️ Missing fields in fallback data: {missing_fields}')
                    else:
                        logger.info('✅ All required fields present in fallback data')
                else:
                    logger.warning('⚠️ No AAPL data in fallback results')
            else:
                logger.warning('⚠️ No fallback data generated')
                
        except Exception as e:
            logger.error(f'❌ Error testing fallback data: {e}')
        
        return True
        
    except Exception as e:
        logger.error(f'❌ Pipeline test failed: {e}')
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(test_fixed_pipeline())
        if result:
            logger.info("✅ All tests passed!")
        else:
            logger.error("❌ Some tests failed!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1) 