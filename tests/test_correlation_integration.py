"""
Test Correlation ID Integration

Demonstrates how correlation IDs flow through the entire system from pipeline to database/AI operations.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
from src.database.supabase_client import test_supabase_connection


class TestCorrelationIDIntegration:
    """Test end-to-end correlation ID flow"""
    
    @pytest.mark.asyncio
    async def test_correlation_id_flows_through_pipeline(self):
        """Test that correlation IDs flow from pipeline through all operations"""
        query = "What is the price of AAPL?"
        user_id = "test_user_123"
        guild_id = "test_guild_456"
        
        # Mock the AI processor to avoid actual API calls
        with patch('src.bot.pipeline.commands.ask.stages.ai_chat_processor.AIChatProcessor') as mock_ai_class:
            mock_ai_processor = AsyncMock()
            mock_response = {
                "response": "AAPL is trading at $150",
                "intent": "price_check",
                "symbols": ["AAPL"],
                "needs_data": True,
                "tools_required": ["price_fetch"]
            }
            mock_ai_processor.process.return_value = mock_response
            mock_ai_class.return_value = mock_ai_processor
            
            # Mock market data service
            with patch('src.api.data.market_data_service.MarketDataService') as mock_market_data:
                mock_service = AsyncMock()
                mock_service.get_comprehensive_stock_data.return_value = {
                    "symbol": "AAPL",
                    "current_price": 150.0,
                    "change": 2.5,
                    "volume": 1000000
                }
                mock_market_data.return_value = mock_service
                
                # Execute the pipeline
                result = await execute_ask_pipeline(
                    query=query,
                    user_id=user_id,
                    guild_id=guild_id
                )
                
                # Verify the result contains correlation ID
                assert result.correlation_id is not None
                correlation_id = result.correlation_id
                assert correlation_id.startswith('cmd-')
                
                # Verify the AI processor was called with the same context
                mock_ai_processor.process.assert_called_once()
                call_args = mock_ai_processor.process.call_args
                assert len(call_args) > 0
                
                # Verify the result structure
                assert 'response' in result.processing_results
                assert 'data' in result.processing_results
                assert 'intent' in result.processing_results
                
                print(f"✅ Correlation ID generated: {correlation_id}")
                print(f"✅ Pipeline result: {result}")
    
    @pytest.mark.asyncio
    async def test_database_operations_with_correlation_ids(self):
        """Test that database operations include correlation IDs"""
        correlation_id = "test-corr-db-123"
        
        # Mock the singleton supabase_client instance
        with patch('src.database.supabase_client.supabase_client') as mock_supabase:
            mock_client = MagicMock()
            mock_supabase.get_client.return_value = mock_client
            
            # Set up the mock chain for table operations
            mock_table = MagicMock()
            mock_select = MagicMock()
            mock_limit = MagicMock()
            mock_execute = MagicMock()
            
            # Set up the mock chain
            mock_client.table.return_value = mock_table
            mock_table.select.return_value = mock_select
            mock_select.limit.return_value = mock_limit
            mock_limit.execute.return_value = MagicMock(data=[])
            
            # Test database connection with correlation ID
            result = test_supabase_connection(correlation_id)
            
            # Verify the mock was called
            mock_client.table.assert_called_once_with('test_table')
            mock_select.limit.assert_called_once_with(1)
            
            # Verify the test returns True
            assert result is True
            
            print(f"✅ Database test with correlation ID: {correlation_id}")
            print(f"✅ Database operations traced successfully")
    
    @pytest.mark.asyncio
    async def test_correlation_id_format_and_uniqueness(self):
        """Test that correlation IDs are properly formatted and unique"""
        query1 = "What is the price of AAPL?"
        query2 = "What is the price of MSFT?"
        user_id = "test_user_123"
        guild_id = "test_guild_456"
        
        # Mock the AI processor
        with patch('src.bot.pipeline.commands.ask.stages.ai_chat_processor.AIChatProcessor') as mock_ai_class:
            mock_ai_processor = AsyncMock()
            mock_response = {
                "response": "Stock analysis",
                "intent": "stock_analysis",
                "symbols": ["AAPL"],
                "needs_data": True,
                "tools_required": ["price_fetch"]
            }
            mock_ai_processor.process.return_value = mock_response
            mock_ai_class.return_value = mock_ai_processor
            
            # Mock market data service
            with patch('src.api.data.market_data_service.MarketDataService') as mock_market_data:
                mock_service = AsyncMock()
                mock_service.get_comprehensive_stock_data.return_value = {
                    "symbol": "AAPL",
                    "current_price": 150.0
                }
                mock_market_data.return_value = mock_service
                
                # Execute two separate pipelines
                result1 = await execute_ask_pipeline(query1, user_id, guild_id)
                result2 = await execute_ask_pipeline(query2, user_id, guild_id)
                
                # Verify both have correlation IDs
                assert result1.correlation_id is not None
                assert result2.correlation_id is not None
                
                correlation_id1 = result1.correlation_id
                correlation_id2 = result2.correlation_id
                
                # Verify format: cmd-{uuid}-{timestamp}
                assert correlation_id1.startswith('cmd-')
                assert correlation_id2.startswith('cmd-')
                
                # Verify uniqueness
                assert correlation_id1 != correlation_id2
                
                # Verify structure (should have 3 parts separated by hyphens)
                parts1 = correlation_id1.split('-')
                parts2 = correlation_id2.split('-')
                assert len(parts1) == 3
                assert len(parts2) == 3
                assert parts1[0] == 'cmd'
                assert parts2[0] == 'cmd'
                
                print(f"✅ Correlation ID 1: {correlation_id1}")
                print(f"✅ Correlation ID 2: {correlation_id2}")
                print(f"✅ IDs are unique and properly formatted")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"]) 