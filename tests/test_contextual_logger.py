import pytest
import logging
import uuid
from src.core.logger import get_logger, BaseLogger

class TestUnifiedLogger:
    def test_info_log_event(self, caplog):
        """Test info-level log event"""
        logger = get_logger("test_logger")
        logger.set_correlation_id("test-123")
        
        logger.info("Test info message", intent='test_logging', symbols=['AAPL'])
        
        # Check log record
        assert len(caplog.records) > 0
        record = caplog.records[0]
        
        assert record.levelno == logging.INFO
        assert "Test info message" in record.getMessage()
        
        # Check extra attributes
        assert record.intent == 'test_logging'
        assert record.symbols == ['AAPL']
        assert record.correlation_id == 'test-123'
    
    def test_error_log_event(self, caplog):
        """Test error-level log event with exception"""
        class TestError(Exception):
            pass
        
        error = TestError("Test error")
        logger = get_logger("test_logger")
        logger.set_correlation_id("test-456")
        
        logger.error("Test error message", intent='error_test', symbols=['GOOGL'], error=str(error))
        
        # Check log records
        error_records = [r for r in caplog.records if r.levelno == logging.ERROR]
        assert len(error_records) > 0
        
        error_record = error_records[0]
        assert "Test error message" in error_record.getMessage()
        
        # Check extra attributes
        assert error_record.intent == 'error_test'
        assert error_record.symbols == ['GOOGL']
        assert error_record.correlation_id == 'test-456'
    
    def test_warning_log_event(self, caplog):
        """Test warning-level log event"""
        logger = get_logger("test_logger")
        logger.set_correlation_id("test-789")
        
        logger.warning("Test warning message", intent='warning_test')
        
        # Check log record
        assert len(caplog.records) > 0
        record = caplog.records[0]
        
        assert record.levelno == logging.WARNING
        assert "Test warning message" in record.getMessage()
        
        # Check extra attributes
        assert record.intent == 'warning_test'
        assert record.correlation_id == 'test-789'
    
    def test_log_event_without_context(self, caplog):
        """Test log event without providing context"""
        logger = get_logger("test_logger")
        logger.set_correlation_id("test-abc")
        
        logger.info("Test message without context")
        
        # Check log record
        assert len(caplog.records) > 0
        record = caplog.records[0]
        
        assert record.levelno == logging.INFO
        assert "Test message without context" in record.getMessage()
        assert record.correlation_id == 'test-abc'
    
    def test_correlation_id_generation(self):
        """Test correlation ID generation"""
        logger = get_logger("test_logger")
        
        # Test setting custom correlation ID
        custom_id = "custom-123"
        logger.set_correlation_id(custom_id)
        assert logger.correlation_id == custom_id
        
        # Test auto-generation
        logger.set_correlation_id()
        assert logger.correlation_id is not None
        assert len(logger.correlation_id) > 0
    
    def test_logger_types(self):
        """Test different logger types"""
        # Test base logger
        base_logger = get_logger("base")
        assert isinstance(base_logger, BaseLogger)
        
        # Test AI logger
        from src.core.logger import get_ai_logger
        ai_logger = get_ai_logger()
        assert ai_logger.name == "ai_interactions"
        
        # Test pipeline logger
        from src.core.logger import get_pipeline_logger
        pipeline_logger = get_pipeline_logger("test_pipeline")
        assert "pipeline.test_pipeline" in pipeline_logger.name
        
        # Test trading logger
        from src.core.logger import get_trading_logger
        trading_logger = get_trading_logger("test_trading")
        assert "trading_bot.test_trading" in trading_logger.name 