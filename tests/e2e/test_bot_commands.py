"""
End-to-End Tests for Bot Commands

This module provides comprehensive tests for bot commands,
simulating real user interactions and verifying correct behavior.
"""

import pytest
import asyncio
import discord
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from src.bot.client import TradingBot
from src.bot.permissions import PermissionLevel
from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
from src.bot.pipeline.commands.analyze.parallel_pipeline import execute_parallel_analyze_pipeline
from src.core.logger import get_logger

logger = get_logger(__name__)

# Test fixtures
@pytest.fixture
def mock_interaction():
    """Create a mock Discord interaction"""
    interaction = AsyncMock()
    interaction.user = MagicMock()
    interaction.user.id = "123456789"
    interaction.user.display_name = "TestUser"
    interaction.user.display_avatar = MagicMock()
    interaction.user.display_avatar.url = "https://example.com/avatar.png"
    interaction.guild_id = "987654321"
    interaction.response = AsyncMock()
    interaction.followup = AsyncMock()
    return interaction

@pytest.fixture
def mock_bot():
    """Create a mock bot instance"""
    bot = MagicMock()
    bot.user = MagicMock()
    bot.user.id = "111222333"
    return bot

@pytest.fixture
def mock_trading_bot(mock_bot):
    """Create a mock TradingBot instance"""
    trading_bot = MagicMock(spec=TradingBot)
    trading_bot.bot = mock_bot
    
    # Mock permission checker
    trading_bot.permission_checker = MagicMock()
    trading_bot.permission_checker.has_permission.return_value = (True, "")
    
    # Mock rate limiter
    trading_bot.rate_limiter = MagicMock()
    trading_bot.rate_limiter.can_make_request.return_value = True
    trading_bot.rate_limiter.get_cooldown_time.return_value = 0
    
    # Mock component status
    trading_bot.component_status = {
        'database': True,
        'watchlist_manager': True,
        'watchlist_alert_manager': True,
        'watchlist_realtime': True
    }
    
    # Mock initialization status
    trading_bot.initialization_complete = True
    
    return trading_bot

@pytest.fixture
def mock_pipeline_context():
    """Create a mock pipeline context with results"""
    context = MagicMock()
    context.processing_results = {
        'market_data': {
            'symbol': 'AAPL',
            'current_price': 150.0,
            'change_percent': 1.5,
            'volume': 1000000
        },
        'technical_analysis': {
            'trend': 'uptrend',
            'signal': 'buy',
            'rsi': 65,
            'macd': 0.5,
            'support_levels': [145.0, 140.0, 135.0],
            'resistance_levels': [155.0, 160.0, 165.0]
        },
        'price_targets': {
            'short_term': 155.0,
            'medium_term': 165.0,
            'long_term': 180.0
        },
        'enhanced_analysis': {
            'sentiment': 'bullish',
            'strength': 'moderate',
            'key_levels': [145.0, 155.0]
        },
        'response': 'This is a test response for AAPL analysis.'
    }
    return context

# Test cases for /ask command
@pytest.mark.asyncio
async def test_ask_command_single_symbol(mock_trading_bot, mock_interaction):
    """Test the /ask command with a single symbol query"""
    # Mock the execute_ask_pipeline function
    with patch('src.bot.client.execute_ask_pipeline') as mock_execute:
        # Setup mock return value
        mock_context = MagicMock()
        mock_context.processing_results = {'response': 'This is a test response for AAPL.'}
        mock_execute.return_value = mock_context
        
        # Call the handle_ask_command method
        await mock_trading_bot.handle_ask_command(mock_interaction, "What is the outlook for AAPL?")
        
        # Verify interaction with Discord
        mock_interaction.response.defer.assert_called_once()
        mock_interaction.followup.send.assert_called_once()
        
        # Verify pipeline execution
        mock_execute.assert_called_once()
        args, kwargs = mock_execute.call_args
        assert kwargs['query'] == "What is the outlook for AAPL?"
        assert kwargs['user_id'] == "123456789"
        assert kwargs['guild_id'] == "987654321"

@pytest.mark.asyncio
async def test_ask_command_batch_query(mock_trading_bot, mock_interaction):
    """Test the /ask command with a batch query containing multiple symbols"""
    # Mock the execute_batch_ask_pipeline function
    with patch('src.bot.client.execute_batch_ask_pipeline') as mock_execute_batch:
        # Setup mock return value
        mock_context = MagicMock()
        mock_context.processing_results = {'response': 'This is a batch response for AAPL, MSFT, GOOGL.'}
        mock_execute_batch.return_value = mock_context
        
        # Mock the extract_symbols_from_query function
        with patch('src.bot.client.extract_symbols_from_query') as mock_extract:
            mock_extract.return_value = ['AAPL', 'MSFT', 'GOOGL']
            
            # Call the handle_ask_command method
            await mock_trading_bot.handle_ask_command(mock_interaction, "Compare AAPL, MSFT, and GOOGL")
            
            # Verify interaction with Discord
            mock_interaction.response.defer.assert_called_once()
            assert mock_interaction.followup.send.call_count >= 2  # Initial message + results
            
            # Verify batch pipeline execution
            mock_execute_batch.assert_called_once()
            args, kwargs = mock_execute_batch.call_args
            assert kwargs['query'] == "Compare AAPL, MSFT, and GOOGL"
            assert kwargs['user_id'] == "123456789"
            assert kwargs['guild_id'] == "987654321"
            assert kwargs['symbols'] == ['AAPL', 'MSFT', 'GOOGL']

@pytest.mark.asyncio
async def test_ask_command_rate_limited(mock_trading_bot, mock_interaction):
    """Test the /ask command when user is rate limited"""
    # Mock rate limiter to indicate rate limiting
    mock_trading_bot.rate_limiter.can_make_request.return_value = False
    mock_trading_bot.rate_limiter.get_cooldown_time.return_value = 10.5
    
    # Call the handle_ask_command method
    await mock_trading_bot.handle_ask_command(mock_interaction, "What is the outlook for AAPL?")
    
    # Verify rate limit message
    mock_interaction.response.send_message.assert_called_once()
    args, kwargs = mock_interaction.response.send_message.call_args
    assert "rate limit" in args[0].lower()
    assert "10.5" in args[0]  # Cooldown time should be in the message

# Test cases for /analyze command
@pytest.mark.asyncio
async def test_analyze_command(mock_trading_bot, mock_interaction, mock_pipeline_context):
    """Test the enhanced /analyze command"""
    # Create a mock cog instance
    from src.bot.commands.analyze_async import AsyncAnalyzeCommands
    cog = AsyncAnalyzeCommands(mock_trading_bot)
    
    # Mock the execute_parallel_analyze_pipeline function
    with patch('src.bot.commands.analyze_async.execute_parallel_analyze_pipeline') as mock_execute:
        mock_execute.return_value = mock_pipeline_context
        
        # Call the analyze_command method
        await cog.analyze_command(mock_interaction, "AAPL", "1d")
        
        # Verify interaction with Discord
        mock_interaction.response.defer.assert_called_once()
        assert mock_interaction.followup.send.call_count >= 2  # Initial message + results
        
        # Verify pipeline execution
        mock_execute.assert_called_once()
        args, kwargs = mock_execute.call_args
        assert kwargs['ticker'] == "AAPL"
        assert kwargs['user_id'] == "123456789"
        assert kwargs['guild_id'] == "987654321"

@pytest.mark.asyncio
async def test_compare_command(mock_trading_bot, mock_interaction, mock_pipeline_context):
    """Test the compare command for multiple symbols"""
    # Create a mock cog instance
    from src.bot.commands.analyze_async import MultiSymbolAnalyzeCommands
    cog = MultiSymbolAnalyzeCommands(mock_trading_bot)
    
    # Mock the _analyze_symbol method
    with patch.object(cog, '_analyze_symbol') as mock_analyze:
        mock_analyze.return_value = mock_pipeline_context
        
        # Call the compare_command method
        await cog.compare_command(mock_interaction, "AAPL,MSFT,GOOGL", "1d")
        
        # Verify interaction with Discord
        mock_interaction.response.defer.assert_called_once()
        assert mock_interaction.followup.send.call_count >= 2  # Initial message + results
        
        # Verify analysis calls
        assert mock_analyze.call_count == 3  # One for each symbol

# Test cases for /zones command
@pytest.mark.asyncio
async def test_zones_command(mock_trading_bot, mock_interaction, mock_pipeline_context):
    """Test the enhanced /zones command"""
    # Create a mock cog instance
    from src.bot.commands.zones_enhanced import EnhancedZonesCommands
    cog = EnhancedZonesCommands(mock_trading_bot)
    
    # Mock the execute_parallel_analyze_pipeline function
    with patch('src.bot.commands.zones_enhanced.execute_parallel_analyze_pipeline') as mock_execute:
        mock_execute.return_value = mock_pipeline_context
        
        # Call the zones_command method
        await cog.zones_command(mock_interaction, "AAPL", "1d", True)
        
        # Verify interaction with Discord
        mock_interaction.response.defer.assert_called_once()
        assert mock_interaction.followup.send.call_count >= 2  # Initial message + results
        
        # Verify pipeline execution
        mock_execute.assert_called_once()
        args, kwargs = mock_execute.call_args
        assert kwargs['ticker'] == "AAPL"
        assert kwargs['user_id'] == "123456789"
        assert kwargs['guild_id'] == "987654321"

@pytest.mark.asyncio
async def test_multizones_command(mock_trading_bot, mock_interaction, mock_pipeline_context):
    """Test the multizones command for multiple timeframes"""
    # Create a mock cog instance
    from src.bot.commands.zones_enhanced import EnhancedZonesCommands
    cog = EnhancedZonesCommands(mock_trading_bot)
    
    # Mock the execute_parallel_analyze_pipeline function
    with patch('src.bot.commands.zones_enhanced.execute_parallel_analyze_pipeline') as mock_execute:
        mock_execute.return_value = mock_pipeline_context
        
        # Call the multizones_command method
        await cog.multizones_command(mock_interaction, "AAPL", True)
        
        # Verify interaction with Discord
        mock_interaction.response.defer.assert_called_once()
        assert mock_interaction.followup.send.call_count >= 2  # Initial message + results
        
        # Verify pipeline execution (should be called 3 times for different timeframes)
        assert mock_execute.call_count == 3
        for call in mock_execute.call_args_list:
            args, kwargs = call
            assert kwargs['ticker'] == "AAPL"
            assert kwargs['user_id'] == "123456789"
            assert kwargs['guild_id'] == "987654321"

# Test cases for /recommendations command
@pytest.mark.asyncio
async def test_recommendations_command(mock_trading_bot, mock_interaction, mock_pipeline_context):
    """Test the enhanced /recommendations command"""
    # Create a mock cog instance
    from src.bot.commands.recommendations_command import EnhancedRecommendationsCommands
    cog = EnhancedRecommendationsCommands(mock_trading_bot)
    
    # Mock the risk profile manager
    cog.risk_profile_manager = MagicMock()
    cog.risk_profile_manager.get_user_profile.return_value = MagicMock(
        user_id="123456789",
        risk_level="moderate",
        max_position_size=5.0,
        stop_loss=10.0,
        take_profit=20.0,
        time_horizon="medium-term",
        preferred_assets=["growth", "value"]
    )
    
    # Mock the execute_parallel_analyze_pipeline function
    with patch('src.bot.commands.recommendations_command.execute_parallel_analyze_pipeline') as mock_execute:
        mock_execute.return_value = mock_pipeline_context
        
        # Call the recommendations_command method
        await cog.recommendations_command(mock_interaction, "AAPL", True)
        
        # Verify interaction with Discord
        mock_interaction.response.defer.assert_called_once()
        assert mock_interaction.followup.send.call_count >= 2  # Initial message + results
        
        # Verify pipeline execution
        mock_execute.assert_called_once()
        args, kwargs = mock_execute.call_args
        assert kwargs['ticker'] == "AAPL"
        assert kwargs['user_id'] == "123456789"
        assert kwargs['guild_id'] == "987654321"

@pytest.mark.asyncio
async def test_risk_profile_command(mock_trading_bot, mock_interaction):
    """Test the risk profile command"""
    # Create a mock cog instance
    from src.bot.commands.recommendations_command import EnhancedRecommendationsCommands
    cog = EnhancedRecommendationsCommands(mock_trading_bot)
    
    # Mock the risk profile manager
    cog.risk_profile_manager = MagicMock()
    cog.risk_profile_manager.get_user_profile.return_value = MagicMock(
        user_id="123456789",
        risk_level="moderate",
        max_position_size=5.0,
        stop_loss=10.0,
        take_profit=20.0,
        time_horizon="medium-term",
        preferred_assets=["growth", "value"]
    )
    cog.risk_profile_manager.update_user_profile.return_value = True
    
    # Call the risk_profile_command method with no parameters (view only)
    await cog.risk_profile_command(mock_interaction)
    
    # Verify interaction with Discord
    mock_interaction.response.defer.assert_called_once()
    mock_interaction.followup.send.assert_called_once()
    
    # Reset mocks
    mock_interaction.reset_mock()
    
    # Call the risk_profile_command method with parameters (update)
    await cog.risk_profile_command(
        mock_interaction,
        risk_level="aggressive",
        max_position_size=10.0,
        stop_loss=15.0,
        take_profit=30.0
    )
    
    # Verify interaction with Discord
    mock_interaction.response.defer.assert_called_once()
    mock_interaction.followup.send.assert_called_once()
    
    # Verify profile update
    cog.risk_profile_manager.update_user_profile.assert_called_once()
    args, kwargs = cog.risk_profile_manager.update_user_profile.call_args
    assert kwargs['user_id'] == "123456789"
    assert kwargs['risk_level'] == "aggressive"
    assert kwargs['max_position_size'] == 10.0
    assert kwargs['stop_loss'] == 15.0
    assert kwargs['take_profit'] == 30.0

# Test cases for /watchlist command
@pytest.mark.asyncio
async def test_watchlist_command(mock_trading_bot, mock_interaction):
    """Test the enhanced /watchlist command"""
    # Create a mock cog instance
    from src.bot.commands.watchlist_enhanced import EnhancedWatchlistCommands
    cog = EnhancedWatchlistCommands(mock_trading_bot)
    
    # Mock the watchlist manager
    cog.watchlist_manager = MagicMock()
    cog.watchlist_manager.get_user_watchlists.return_value = [
        MagicMock(
            id=1,
            watchlist_name="Default",
            symbols=[
                MagicMock(symbol="AAPL", notes="Apple Inc.", alert_threshold=5.0, alert_type="price_change"),
                MagicMock(symbol="MSFT", notes="Microsoft Corp.", alert_threshold=5.0, alert_type="price_change")
            ]
        )
    ]
    
    # Mock the watchlist realtime manager
    cog.watchlist_realtime = MagicMock()
    cog.watchlist_realtime.get_real_time_data.return_value = {
        "current_price": 150.0,
        "change_percent": 1.5,
        "volume": 1000000
    }
    
    # Call the watchlist_command method with "view" action
    await cog.watchlist_command(mock_interaction, "view")
    
    # Verify interaction with Discord
    mock_interaction.response.defer.assert_called_once()
    mock_interaction.followup.send.assert_called_once()
    
    # Reset mocks
    mock_interaction.reset_mock()
    cog.watchlist_manager.reset_mock()
    
    # Mock add_symbol_to_watchlist
    cog.watchlist_manager.add_symbol_to_watchlist.return_value = True
    
    # Call the watchlist_command method with "add" action
    await cog.watchlist_command(
        mock_interaction,
        "add",
        symbol="GOOGL",
        notes="Alphabet Inc.",
        alert_threshold=5.0,
        alert_type="price_change"
    )
    
    # Verify interaction with Discord
    mock_interaction.response.defer.assert_called_once()
    mock_interaction.followup.send.assert_called_once()
    
    # Verify add_symbol_to_watchlist call
    cog.watchlist_manager.add_symbol_to_watchlist.assert_called_once()
    args, kwargs = cog.watchlist_manager.add_symbol_to_watchlist.call_args
    assert args[1] == "GOOGL"  # symbol
    assert kwargs['notes'] == "Alphabet Inc."
    assert kwargs['alert_threshold'] == 5.0
    assert kwargs['alert_type'] == "price_change"

# Integration tests for pipeline execution
@pytest.mark.asyncio
async def test_ask_pipeline_execution():
    """Integration test for ask pipeline execution"""
    # This test requires actual pipeline execution, so we'll mock the dependencies
    with patch('src.bot.pipeline.commands.ask.pipeline.AIQueryProcessor') as mock_processor:
        # Mock the process method
        mock_processor.return_value.process.return_value = "This is a test response for AAPL."
        
        # Execute the ask pipeline
        context = await execute_ask_pipeline(
            query="What is the outlook for AAPL?",
            user_id="123456789",
            guild_id="987654321",
            correlation_id="test_correlation_id"
        )
        
        # Verify context
        assert context is not None
        assert hasattr(context, 'processing_results')
        assert 'response' in context.processing_results
        assert context.processing_results['response'] == "This is a test response for AAPL."

@pytest.mark.asyncio
async def test_analyze_pipeline_execution():
    """Integration test for analyze pipeline execution"""
    # This test requires actual pipeline execution, so we'll mock the dependencies
    with patch('src.bot.pipeline.commands.analyze.parallel_pipeline.ParallelPipelineEngine') as mock_engine:
        # Mock the execute method
        mock_context = MagicMock()
        mock_context.processing_results = {
            'market_data': {'symbol': 'AAPL', 'current_price': 150.0},
            'technical_analysis': {'trend': 'uptrend', 'signal': 'buy'},
            'response': 'This is a test response for AAPL analysis.'
        }
        mock_engine.return_value.execute.return_value = mock_context
        
        # Execute the analyze pipeline
        context = await execute_parallel_analyze_pipeline(
            ticker="AAPL",
            user_id="123456789",
            guild_id="987654321",
            correlation_id="test_correlation_id"
        )
        
        # Verify context
        assert context is not None
        assert hasattr(context, 'processing_results')
        assert 'market_data' in context.processing_results
        assert 'technical_analysis' in context.processing_results
        assert context.processing_results['market_data']['symbol'] == "AAPL"

# Load test for concurrent command execution
@pytest.mark.asyncio
async def test_concurrent_command_execution(mock_trading_bot, mock_interaction):
    """Test concurrent execution of multiple commands"""
    # Mock the execute_ask_pipeline function
    with patch('src.bot.client.execute_ask_pipeline') as mock_execute:
        # Setup mock return value
        mock_context = MagicMock()
        mock_context.processing_results = {'response': 'This is a test response.'}
        mock_execute.return_value = mock_context
        
        # Create multiple tasks
        tasks = []
        for i in range(5):
            # Create a new mock interaction for each task
            interaction = AsyncMock()
            interaction.user = MagicMock()
            interaction.user.id = f"user_{i}"
            interaction.guild_id = "987654321"
            interaction.response = AsyncMock()
            interaction.followup = AsyncMock()
            
            # Create task
            task = asyncio.create_task(
                mock_trading_bot.handle_ask_command(interaction, f"Query {i}")
            )
            tasks.append(task)
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks)
        
        # Verify pipeline execution count
        assert mock_execute.call_count == 5

# Error handling tests
@pytest.mark.asyncio
async def test_error_handling_invalid_symbol(mock_trading_bot, mock_interaction):
    """Test error handling for invalid symbol"""
    # Mock the sanitize_symbol function to return invalid
    with patch('src.bot.client.InputSanitizer.sanitize_symbol') as mock_sanitize:
        mock_sanitize.return_value = ("", False, "Invalid symbol format")
        
        # Call the handle_ask_command method
        await mock_trading_bot.handle_ask_command(mock_interaction, "What is the outlook for @#$%?")
        
        # Verify error message
        mock_interaction.response.send_message.assert_called_once()
        args, kwargs = mock_interaction.response.send_message.call_args
        assert "invalid symbol" in args[0].lower()

@pytest.mark.asyncio
async def test_error_handling_timeout(mock_trading_bot, mock_interaction):
    """Test error handling for timeout"""
    # Mock the execute_ask_pipeline function to raise TimeoutError
    with patch('src.bot.client.execute_ask_pipeline') as mock_execute:
        mock_execute.side_effect = asyncio.TimeoutError()
        
        # Call the handle_ask_command method
        await mock_trading_bot.handle_ask_command(mock_interaction, "What is the outlook for AAPL?")
        
        # Verify timeout message
        mock_interaction.response.defer.assert_called_once()
        mock_interaction.followup.send.assert_called_once()
        args, kwargs = mock_interaction.followup.send.call_args
        assert "took longer than expected" in args[0].lower()

@pytest.mark.asyncio
async def test_error_handling_general_exception(mock_trading_bot, mock_interaction):
    """Test error handling for general exception"""
    # Mock the execute_ask_pipeline function to raise Exception
    with patch('src.bot.client.execute_ask_pipeline') as mock_execute:
        mock_execute.side_effect = Exception("Test error")
        
        # Call the handle_ask_command method
        await mock_trading_bot.handle_ask_command(mock_interaction, "What is the outlook for AAPL?")
        
        # Verify error message
        mock_interaction.response.defer.assert_called_once()
        mock_interaction.followup.send.assert_called_once()
        args, kwargs = mock_interaction.followup.send.call_args
        assert "error" in args[0].lower()

# Permission tests
@pytest.mark.asyncio
async def test_permission_checks(mock_trading_bot, mock_interaction):
    """Test permission checks for commands"""
    # Mock permission checker to deny access
    mock_trading_bot.permission_checker.has_permission.return_value = (False, "Insufficient permissions")
    
    # Create a mock cog instance that requires paid access
    from src.bot.commands.recommendations_command import EnhancedRecommendationsCommands
    cog = EnhancedRecommendationsCommands(mock_trading_bot)
    
    # Call the recommendations_command method
    await cog.recommendations_command(mock_interaction, "AAPL")
    
    # Verify permission denied message
    mock_interaction.response.send_message.assert_called_once()
    args, kwargs = mock_interaction.response.send_message.call_args
    assert "requires paid tier access" in args[0].lower()

# Component status tests
@pytest.mark.asyncio
async def test_component_status_checks(mock_trading_bot, mock_interaction):
    """Test component status checks"""
    # Mock component status to indicate database is down
    mock_trading_bot.component_status['database'] = False
    
    # Call the handle_ask_command method
    await mock_trading_bot.handle_ask_command(mock_interaction, "What is the outlook for AAPL?")
    
    # Verify component status error message
    mock_interaction.response.send_message.assert_called_once()
    args, kwargs = mock_interaction.response.send_message.call_args
    assert "database" in args[0].lower()
    assert "not available" in args[0].lower()

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
