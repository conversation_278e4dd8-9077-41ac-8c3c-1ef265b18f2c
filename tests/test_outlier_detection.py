"""
Tests for outlier detection system.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch

from src.core.outlier_detector import (
    OutlierDetector,
    Outlier,
    OutlierType,
    OutlierSeverity,
    detect_outliers,
    adjust_confidence_for_outliers
)


class TestOutlierDetector:
    """Test the OutlierDetector class."""
    
    @pytest.fixture
    def detector(self):
        """Create a fresh OutlierDetector instance."""
        return OutlierDetector()
    
    @pytest.fixture
    def sample_data(self):
        """Create sample market data for testing."""
        timestamps = [
            datetime(2024, 1, 1, 9, 30) + timedelta(minutes=i)
            for i in range(100)  # 100 minutes of data
        ]
        
        # Create normal price data with some outliers
        base_price = 100.0
        prices = []
        volumes = []
        
        for i in range(100):
            if i == 50:  # Price spike at minute 50
                price = base_price + 25.0  # 25% increase
            elif i == 75:  # Price drop at minute 75
                price = base_price - 20.0  # 20% decrease
            else:
                # Normal price movement
                price = base_price + np.random.normal(0, 1.0)
            
            prices.append(max(0.01, price))  # Ensure positive prices
            
            # Create volume data with some spikes
            if i == 60:  # Volume spike at minute 60
                volume = 5000000  # 5x normal volume
            else:
                volume = 1000000 + np.random.normal(0, 200000)
            
            volumes.append(max(1000, volume))
        
        data = pd.DataFrame({
            'open': prices,
            'high': [p + np.random.uniform(0, 2) for p in prices],
            'low': [max(0.01, p - np.random.uniform(0, 2)) for p in prices],
            'close': prices,
            'volume': volumes
        }, index=timestamps)
        
        return data
    
    def test_detect_outliers_comprehensive(self, detector, sample_data):
        """Test comprehensive outlier detection."""
        outliers = detector.detect_outliers(sample_data, "AAPL", lookback_periods=20)
        
        # Should detect multiple types of outliers
        assert len(outliers) > 0
        
        # Check for price outliers
        price_outliers = [o for o in outliers if o.outlier_type in [OutlierType.PRICE_SPIKE, OutlierType.PRICE_DROP]]
        assert len(price_outliers) > 0
        
        # Check for volume outliers
        volume_outliers = [o for o in outliers if o.outlier_type == OutlierType.VOLUME_SPIKE]
        assert len(volume_outliers) > 0
    
    def test_detect_price_outliers(self, detector):
        """Test price outlier detection."""
        # Create data with known price outliers
        timestamps = [datetime(2024, 1, 1, 9, 30) + timedelta(minutes=i) for i in range(50)]
        
        # Normal price movement
        prices = [100.0 + i * 0.1 for i in range(50)]
        prices[25] = 150.0  # 50% spike at minute 25
        prices[35] = 50.0   # 50% drop at minute 35
        
        data = pd.DataFrame({
            'open': prices,
            'high': [p + 1 for p in prices],
            'low': [max(0.01, p - 1) for p in prices],
            'close': prices,
            'volume': [1000000] * 50
        }, index=timestamps)
        
        outliers = detector._detect_price_outliers(data, "TEST", 20)
        
        assert len(outliers) > 0
        
        # Check for price spike
        spike_outliers = [o for o in outliers if o.outlier_type == OutlierType.PRICE_SPIKE]
        assert len(spike_outliers) > 0
        
        # Check for price drop
        drop_outliers = [o for o in outliers if o.outlier_type == OutlierType.PRICE_DROP]
        assert len(drop_outliers) > 0
    
    def test_detect_volume_outliers(self, detector):
        """Test volume outlier detection."""
        # Create data with known volume outliers
        timestamps = [datetime(2024, 1, 1, 9, 30) + timedelta(minutes=i) for i in range(50)]
        
        # Normal volume
        volumes = [1000000] * 50
        volumes[20] = 5000000  # 5x spike at minute 20
        volumes[30] = 200000   # 5x drop at minute 30
        
        data = pd.DataFrame({
            'open': [100.0] * 50,
            'high': [101.0] * 50,
            'low': [99.0] * 50,
            'close': [100.5] * 50,
            'volume': volumes
        }, index=timestamps)
        
        outliers = detector._detect_volume_outliers(data, "TEST", 20)
        
        assert len(outliers) > 0
        
        # Check for volume spike
        spike_outliers = [o for o in outliers if o.outlier_type == OutlierType.VOLUME_SPIKE]
        assert len(spike_outliers) > 0
        
        # Check for volume drop
        drop_outliers = [o for o in outliers if o.outlier_type == OutlierType.VOLUME_DROP]
        assert len(drop_outliers) > 0
    
    def test_detect_price_gaps(self, detector):
        """Test price gap detection."""
        # Create data with known price gaps
        timestamps = [datetime(2024, 1, 1, 9, 30) + timedelta(minutes=i) for i in range(50)]
        
        # Normal prices
        prices = [100.0] * 50
        opens = [100.0] * 50
        
        # Create gaps
        opens[25] = 120.0  # 20% gap up at minute 25
        opens[35] = 80.0   # 20% gap down at minute 35
        
        data = pd.DataFrame({
            'open': opens,
            'high': [p + 1 for p in prices],
            'low': [max(0.01, p - 1) for p in prices],
            'close': prices,
            'volume': [1000000] * 50
        }, index=timestamps)
        
        outliers = detector._detect_price_gaps(data, "TEST", 20)
        
        assert len(outliers) > 0
        
        # Check for gap outliers
        gap_outliers = [o for o in outliers if o.outlier_type == OutlierType.PRICE_GAP]
        assert len(gap_outliers) > 0
    
    def test_detect_volatility_spikes(self, detector):
        """Test volatility spike detection."""
        # Create data with known volatility spikes
        timestamps = [datetime(2024, 1, 1, 9, 30) + timedelta(minutes=i) for i in range(100)]
        
        # Normal returns
        returns = [0.001] * 100  # 0.1% daily returns
        
        # Add volatility spikes
        for i in range(20, 30):  # High volatility period
            returns[i] = 0.05 if i % 2 == 0 else -0.05  # 5% alternating returns
        
        # Convert returns to prices
        prices = [100.0]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = pd.DataFrame({
            'open': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'close': prices,
            'volume': [1000000] * 100
        }, index=timestamps)
        
        outliers = detector._detect_volatility_spikes(data, "TEST", 20)
        
        assert len(outliers) > 0
        
        # Check for volatility outliers
        vol_outliers = [o for o in outliers if o.outlier_type == OutlierType.VOLATILITY_SPIKE]
        assert len(vol_outliers) > 0
    
    def test_severity_determination(self, detector):
        """Test severity determination logic."""
        # Test price severity
        assert detector._determine_price_severity(6.0, 0.30) == OutlierSeverity.CRITICAL
        assert detector._determine_price_severity(4.5, 0.22) == OutlierSeverity.MAJOR
        assert detector._determine_price_severity(3.8, 0.16) == OutlierSeverity.MODERATE
        assert detector._determine_price_severity(3.2, 0.12) == OutlierSeverity.MINOR
        
        # Test volume severity
        assert detector._determine_volume_severity(12.0) == OutlierSeverity.CRITICAL
        assert detector._determine_volume_severity(6.0) == OutlierSeverity.MAJOR
        assert detector._determine_volume_severity(4.0) == OutlierSeverity.MODERATE
        assert detector._determine_volume_severity(2.5) == OutlierSeverity.MINOR
        
        # Test gap severity
        assert detector._determine_gap_severity(0.25, 6.0) == OutlierSeverity.CRITICAL
        assert detector._determine_gap_severity(0.18, 4.5) == OutlierSeverity.MAJOR
        assert detector._determine_gap_severity(0.12, 3.5) == OutlierSeverity.MODERATE
        assert detector._determine_gap_severity(0.08, 2.5) == OutlierSeverity.MINOR
        
        # Test volatility severity
        assert detector._determine_volatility_severity(6.0) == OutlierSeverity.CRITICAL
        assert detector._determine_volatility_severity(4.0) == OutlierSeverity.MAJOR
        assert detector._determine_volatility_severity(3.0) == OutlierSeverity.MODERATE
        assert detector._determine_volatility_severity(2.0) == OutlierSeverity.MINOR
    
    def test_confidence_adjustment(self, detector):
        """Test confidence adjustment for outliers."""
        # Create mock outliers
        minor_outlier = Mock()
        minor_outlier.confidence_impact = 2.0
        
        moderate_outlier = Mock()
        moderate_outlier.confidence_impact = 8.0
        
        major_outlier = Mock()
        major_outlier.confidence_impact = 20.0
        
        # Test single outlier
        base_confidence = 85.0
        adjusted = detector.adjust_confidence_for_outliers(base_confidence, [minor_outlier])
        assert adjusted == 83.0  # 85 - 2
        
        # Test multiple outliers
        adjusted = detector.adjust_confidence_for_outliers(base_confidence, [minor_outlier, moderate_outlier])
        assert adjusted == 75.0  # 85 - 2 - 8
        
        # Test bounds
        adjusted = detector.adjust_confidence_for_outliers(base_confidence, [major_outlier, major_outlier])
        assert adjusted == 0.0  # Should not go below 0
        
        # Test high confidence
        high_confidence = 100.0
        adjusted = detector.adjust_confidence_for_outliers(high_confidence, [major_outlier])
        assert adjusted == 80.0  # 100 - 20
    
    def test_empty_data_handling(self, detector):
        """Test handling of empty or insufficient data."""
        # Empty DataFrame
        empty_data = pd.DataFrame()
        outliers = detector.detect_outliers(empty_data, "TEST", 20)
        assert len(outliers) == 0
        
        # Insufficient data
        insufficient_data = pd.DataFrame({
            'open': [100.0, 101.0],
            'high': [101.0, 102.0],
            'low': [99.0, 100.0],
            'close': [100.5, 101.5],
            'volume': [1000000, 1000000]
        })
        outliers = detector.detect_outliers(insufficient_data, "TEST", 20)
        assert len(outliers) == 0
    
    def test_missing_columns_handling(self, detector):
        """Test handling of missing required columns."""
        # Missing columns
        incomplete_data = pd.DataFrame({
            'open': [100.0] * 30,
            'close': [100.5] * 30
            # Missing high, low, volume
        })
        outliers = detector.detect_outliers(incomplete_data, "TEST", 20)
        assert len(outliers) == 0


class TestOutlier:
    """Test the Outlier dataclass."""
    
    def test_outlier_creation(self):
        """Test creating Outlier objects."""
        now = datetime.now(timezone.utc)
        
        outlier = Outlier(
            symbol="AAPL",
            outlier_type=OutlierType.PRICE_SPIKE,
            severity=OutlierSeverity.MAJOR,
            timestamp=now,
            value=0.25,
            expected_range=(0.01, 0.05),
            deviation_score=4.5,
            confidence_impact=20.0,
            description="Large price spike detected",
            recommendations=["Verify data accuracy", "Check for news events"]
        )
        
        assert outlier.symbol == "AAPL"
        assert outlier.outlier_type == OutlierType.PRICE_SPIKE
        assert outlier.severity == OutlierSeverity.MAJOR
        assert outlier.confidence_impact == 20.0
        assert len(outlier.recommendations) == 2
    
    def test_outlier_timezone_handling(self):
        """Test timezone handling in Outlier."""
        # Test with naive datetime
        naive_time = datetime(2024, 1, 1, 12, 0)
        
        outlier = Outlier(
            symbol="TEST",
            outlier_type=OutlierType.VOLUME_SPIKE,
            severity=OutlierSeverity.MODERATE,
            timestamp=naive_time,
            value=5.0,
            expected_range=(1.0, 3.0),
            deviation_score=3.0,
            confidence_impact=8.0,
            description="Volume spike",
            recommendations=[]
        )
        
        # Should automatically add UTC timezone
        assert outlier.timestamp.tzinfo == timezone.utc
    
    def test_outlier_to_dict(self):
        """Test converting Outlier to dictionary."""
        now = datetime.now(timezone.utc)
        
        outlier = Outlier(
            symbol="MSFT",
            outlier_type=OutlierType.PRICE_GAP,
            severity=OutlierSeverity.CRITICAL,
            timestamp=now,
            value=0.20,
            expected_range=(0.01, 0.05),
            deviation_score=6.0,
            confidence_impact=40.0,
            description="Critical price gap",
            recommendations=["Verify data source", "Check for errors"]
        )
        
        outlier_dict = outlier.to_dict()
        
        assert outlier_dict['symbol'] == "MSFT"
        assert outlier_dict['outlier_type'] == "price_gap"
        assert outlier_dict['severity'] == "critical"
        assert outlier_dict['value'] == 0.2
        assert outlier_dict['confidence_impact'] == 40.0
        assert 'timestamp' in outlier_dict


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    def test_detect_outliers_convenience(self):
        """Test the detect_outliers convenience function."""
        # Create sample data
        timestamps = [datetime(2024, 1, 1, 9, 30) + timedelta(minutes=i) for i in range(30)]
        data = pd.DataFrame({
            'open': [100.0] * 30,
            'high': [101.0] * 30,
            'low': [99.0] * 30,
            'close': [100.5] * 30,
            'volume': [1000000] * 30
        }, index=timestamps)
        
        # Add an outlier
        data.loc[data.index[15], 'close'] = 150.0  # 50% spike
        
        outliers = detect_outliers(data, "TEST", 20)
        
        assert len(outliers) > 0
        assert any(o.outlier_type == OutlierType.PRICE_SPIKE for o in outliers)
    
    def test_adjust_confidence_for_outliers_convenience(self):
        """Test the adjust_confidence_for_outliers convenience function."""
        # Create mock outliers
        outliers = [
            Mock(confidence_impact=5.0),
            Mock(confidence_impact=10.0)
        ]
        
        base_confidence = 80.0
        adjusted_confidence = adjust_confidence_for_outliers(base_confidence, outliers)
        
        assert adjusted_confidence == 65.0  # 80 - 5 - 10


if __name__ == "__main__":
    pytest.main([__file__]) 