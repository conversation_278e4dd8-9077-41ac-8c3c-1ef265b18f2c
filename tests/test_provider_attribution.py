"""
Tests for provider attribution and transparency functionality.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import Mock, patch

from src.api.data.providers.base import (
    ProviderMetadata, 
    ProviderType, 
    MarketDataResponse,
    BaseMarketDataProvider
)
from src.api.data.metrics import cache_metrics


class TestProviderMetadata:
    """Test the ProviderMetadata class."""
    
    def test_provider_metadata_creation(self):
        """Test creating ProviderMetadata objects."""
        now = datetime.now(timezone.utc)
        
        metadata = ProviderMetadata(
            provider_name="Polygon.io",
            provider_type=ProviderType.POLYGON,
            fetched_at=now,
            data_window_start=now,
            data_window_end=now,
            is_fallback=False,
            response_time_ms=150.5
        )
        
        assert metadata.provider_name == "Polygon.io"
        assert metadata.provider_type == ProviderType.POLYGON
        assert metadata.fetched_at == now
        assert metadata.is_fallback is False
        assert metadata.response_time_ms == 150.5
    
    def test_provider_metadata_timezone_handling(self):
        """Test timezone handling in metadata."""
        # Test with naive datetime
        naive_time = datetime(2024, 1, 1, 12, 0)
        metadata = ProviderMetadata(
            provider_name="Test",
            provider_type=ProviderType.POLYGON,
            fetched_at=naive_time
        )
        
        # Should automatically add UTC timezone
        assert metadata.fetched_at.tzinfo == timezone.utc
        assert metadata.fetched_at.hour == 12
    
    def test_provider_metadata_to_dict(self):
        """Test converting metadata to dictionary."""
        now = datetime.now(timezone.utc)
        metadata = ProviderMetadata(
            provider_name="Alpha Vantage",
            provider_type=ProviderType.ALPHA_VANTAGE,
            fetched_at=now,
            is_fallback=True,
            fallback_reason="Primary provider timeout"
        )
        
        metadata_dict = metadata.to_dict()
        
        assert metadata_dict['provider_name'] == "Alpha Vantage"
        assert metadata_dict['provider_type'] == "alpha_vantage"
        assert metadata_dict['is_fallback'] is True
        assert metadata_dict['fallback_reason'] == "Primary provider timeout"
        assert 'fetched_at' in metadata_dict
    
    def test_freshness_calculation(self):
        """Test data freshness calculation."""
        # Test with recent data
        recent_time = datetime.now(timezone.utc)
        metadata = ProviderMetadata(
            provider_name="Test",
            provider_type=ProviderType.POLYGON,
            fetched_at=recent_time
        )
        
        freshness_minutes = metadata.get_freshness_minutes()
        assert freshness_minutes is not None
        assert freshness_minutes < 1  # Should be very recent
        
        # Test with older data
        old_time = datetime.now(timezone.utc).replace(hour=10, minute=0, second=0, microsecond=0)
        current_time = datetime.now(timezone.utc).replace(hour=11, minute=0, second=0, microsecond=0)
        
        with patch('src.api.data.providers.base.datetime') as mock_datetime:
            mock_datetime.now.return_value = current_time
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            
            metadata = ProviderMetadata(
                provider_name="Test",
                provider_type=ProviderType.POLYGON,
                fetched_at=old_time
            )
            
            freshness_minutes = metadata.get_freshness_minutes()
            assert freshness_minutes == 60  # 1 hour old
    
    def test_freshness_status(self):
        """Test freshness status classification."""
        now = datetime.now(timezone.utc)
        
        # Test fresh data
        fresh_metadata = ProviderMetadata(
            provider_name="Test",
            provider_type=ProviderType.POLYGON,
            fetched_at=now
        )
        assert fresh_metadata.get_freshness_status() == "fresh"
        
        # Test with older data
        old_time = datetime.now(timezone.utc).replace(hour=10, minute=0, second=0, microsecond=0)
        current_time = datetime.now(timezone.utc).replace(hour=11, minute=0, second=0, microsecond=0)
        
        with patch('src.api.data.providers.base.datetime') as mock_datetime:
            mock_datetime.now.return_value = current_time
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            
            old_metadata = ProviderMetadata(
                provider_name="Test",
                provider_type=ProviderType.POLYGON,
                fetched_at=old_time
            )
            assert old_metadata.get_freshness_status() == "moderate"


class TestMarketDataResponse:
    """Test the MarketDataResponse class."""
    
    def test_market_data_response_with_metadata(self):
        """Test creating MarketDataResponse with metadata."""
        now = datetime.now(timezone.utc)
        metadata = ProviderMetadata(
            provider_name="Polygon.io",
            provider_type=ProviderType.POLYGON,
            fetched_at=now
        )
        
        response = MarketDataResponse(
            symbol="AAPL",
            price=150.0,
            timestamp=now,
            volume=1000000,
            metadata=metadata
        )
        
        assert response.symbol == "AAPL"
        assert response.price == 150.0
        assert response.metadata == metadata
        assert response.metadata.provider_name == "Polygon.io"
    
    def test_market_data_response_to_dict(self):
        """Test converting response to dictionary."""
        now = datetime.now(timezone.utc)
        metadata = ProviderMetadata(
            provider_name="Alpha Vantage",
            provider_type=ProviderType.ALPHA_VANTAGE,
            fetched_at=now
        )
        
        response = MarketDataResponse(
            symbol="MSFT",
            price=300.0,
            timestamp=now,
            metadata=metadata
        )
        
        response_dict = response.to_dict()
        
        assert response_dict['symbol'] == "MSFT"
        assert response_dict['price'] == 300.0
        assert 'metadata' in response_dict
        assert response_dict['metadata']['provider_name'] == "Alpha Vantage"
    
    def test_attribution_text_generation(self):
        """Test generating attribution text."""
        now = datetime.now(timezone.utc)
        metadata = ProviderMetadata(
            provider_name="Polygon.io",
            provider_type=ProviderType.POLYGON,
            fetched_at=now,
            is_fallback=False
        )
        
        response = MarketDataResponse(
            symbol="TSLA",
            price=250.0,
            timestamp=now,
            metadata=metadata
        )
        
        attribution = response.get_attribution_text()
        
        assert "Data from Polygon.io" in attribution
        assert "just updated" in attribution  # Should be very recent
    
    def test_attribution_text_with_fallback(self):
        """Test attribution text with fallback information."""
        now = datetime.now(timezone.utc)
        metadata = ProviderMetadata(
            provider_name="Yahoo Finance",
            provider_type=ProviderType.YAHOO,
            fetched_at=now,
            is_fallback=True,
            fallback_reason="Primary provider timeout"
        )
        
        response = MarketDataResponse(
            symbol="NVDA",
            price=500.0,
            timestamp=now,
            metadata=metadata
        )
        
        attribution = response.get_attribution_text()
        
        assert "Data from Yahoo Finance" in attribution
        assert "fallback: Primary provider timeout" in attribution
    
    def test_attribution_text_with_cache(self):
        """Test attribution text with cache information."""
        now = datetime.now(timezone.utc)
        metadata = ProviderMetadata(
            provider_name="Polygon.io",
            provider_type=ProviderType.POLYGON,
            fetched_at=now,
            cache_hit=True,
            cache_ttl_remaining=300
        )
        
        response = MarketDataResponse(
            symbol="GOOGL",
            price=140.0,
            timestamp=now,
            metadata=metadata
        )
        
        attribution = response.get_attribution_text()
        
        assert "Cached data from Polygon.io" in attribution
        assert "TTL: 300s remaining" in attribution


class TestBaseMarketDataProvider:
    """Test the BaseMarketDataProvider class."""
    
    def test_base_provider_initialization(self):
        """Test base provider initialization."""
        provider = Mock(spec=BaseMarketDataProvider)
        provider.provider_name = "Test Provider"
        provider.provider_type = ProviderType.POLYGON
        provider.is_available = True
        provider.error_count = 0
        provider.success_count = 0
        provider.avg_response_time = 0.0
        
        assert provider.provider_name == "Test Provider"
        assert provider.provider_type == ProviderType.POLYGON
        assert provider.is_available is True
    
    def test_provider_stats(self):
        """Test provider statistics collection."""
        provider = Mock(spec=BaseMarketDataProvider)
        provider.provider_name = "Test Provider"
        provider.provider_type = ProviderType.ALPHA_VANTAGE
        provider.is_available = True
        provider.error_count = 2
        provider.success_count = 8
        provider.avg_response_time = 150.0
        provider.last_error = "Rate limit exceeded"
        
        # Mock the get_provider_stats method
        def mock_get_stats():
            return {
                'provider_name': provider.provider_name,
                'provider_type': provider.provider_type.value,
                'is_available': provider.is_available,
                'success_count': provider.success_count,
                'error_count': provider.error_count,
                'avg_response_time_ms': provider.avg_response_time,
                'last_error': provider.last_error,
                'success_rate': (
                    provider.success_count / (provider.success_count + provider.error_count) * 100
                    if (provider.success_count + provider.error_count) > 0 else 0
                )
            }
        
        provider.get_provider_stats = mock_get_stats
        
        stats = provider.get_provider_stats()
        
        assert stats['provider_name'] == "Test Provider"
        assert stats['success_count'] == 8
        assert stats['error_count'] == 2
        assert stats['success_rate'] == 80.0  # 8/10 * 100
        assert stats['avg_response_time_ms'] == 150.0


class TestProviderAttributionIntegration:
    """Test integration of provider attribution with metrics."""
    
    @patch.object(cache_metrics, 'record_data_freshness')
    def test_freshness_metrics_recording(self, mock_record_freshness):
        """Test that freshness metrics are recorded."""
        now = datetime.now(timezone.utc)
        metadata = ProviderMetadata(
            provider_name="Polygon.io",
            provider_type=ProviderType.POLYGON,
            fetched_at=now
        )
        
        response = MarketDataResponse(
            symbol="AAPL",
            price=150.0,
            timestamp=now,
            metadata=metadata
        )
        
        # Simulate calling attribution text (which should record metrics)
        attribution = response.get_attribution_text()
        
        # Verify metrics were recorded
        assert "Data from Polygon.io" in attribution
        # Note: The actual metrics recording happens in the AI processor,
        # so we test the integration there
    
    def test_fallback_attribution_tracking(self):
        """Test that fallback usage is properly tracked."""
        now = datetime.now(timezone.utc)
        metadata = ProviderMetadata(
            provider_name="Yahoo Finance",
            provider_type=ProviderType.YAHOO,
            fetched_at=now,
            is_fallback=True,
            fallback_reason="Primary provider timeout"
        )
        
        response = MarketDataResponse(
            symbol="MSFT",
            price=300.0,
            timestamp=now,
            metadata=metadata
        )
        
        attribution = response.get_attribution_text()
        
        assert "fallback: Primary provider timeout" in attribution
        assert "Yahoo Finance" in attribution


if __name__ == "__main__":
    pytest.main([__file__]) 