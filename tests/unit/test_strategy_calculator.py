import pytest
import pandas as pd
import numpy as np
import logging

from src.shared.technical_analysis.strategy_calculator import PositionStrategyCalculator
from src.core.risk_management.atr_calculator import TradeDirection, RiskLevel
from src.shared.technical_analysis.indicators import calculate_supertrend

@pytest.fixture
def strategy_calculator():
    return PositionStrategyCalculator()

@pytest.fixture
def sample_price_data():
    # Create a sample price dataset
    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', periods=100)
    close = np.cumsum(np.random.normal(0, 1, 100)) + 100
    high = close + np.abs(np.random.normal(0, 0.5, 100))
    low = close - np.abs(np.random.normal(0, 0.5, 100))
    
    return pd.DataFrame({
        'date': dates,
        'close': close,
        'high': high,
        'low': low
    }).set_index('date')

def test_calculate_entry_strategy(strategy_calculator, sample_price_data):
    result = strategy_calculator.calculate_entry_strategy(
        sample_price_data['high'], 
        sample_price_data['low'], 
        sample_price_data['close']
    )
    
    assert 'direction' in result
    assert result['direction'] in [TradeDirection.LONG.value, TradeDirection.SHORT.value]
    assert 'entry_price' in result
    assert 'stop_loss' in result
    assert 'take_profit' in result
    assert 'risk_level' in result
    assert 'atr' in result

def test_estimate_options_strategy(strategy_calculator, sample_price_data):
    entry_strategy = strategy_calculator.calculate_entry_strategy(
        sample_price_data['high'], 
        sample_price_data['low'], 
        sample_price_data['close']
    )
    
    options_strategy = strategy_calculator.estimate_options_strategy(entry_strategy)
    
    assert 'recommended_option' in options_strategy
    assert options_strategy['recommended_option'] in ['Call', 'Put', None]
    assert 'rationale' in options_strategy

def test_risk_classification(strategy_calculator):
    # Simulate different price scenarios
    scenarios = [
        (0.5, 100),   # Low volatility
        (1.5, 100),   # Medium volatility
        (2.5, 100),   # High volatility
        (4.0, 100)    # Extreme volatility
    ]
    
    expected_risks = [
        RiskLevel.LOW,
        RiskLevel.MEDIUM,
        RiskLevel.HIGH,
        RiskLevel.EXTREME
    ]
    
    for (atr, price), expected_risk in zip(scenarios, expected_risks):
        risk_level = strategy_calculator._classify_risk(atr, price)
        assert risk_level == expected_risk

def test_stop_loss_calculation(strategy_calculator):
    # Test long position stop loss
    long_stop_loss = strategy_calculator._calculate_stop_loss(
        current_price=100, 
        atr=2, 
        direction=TradeDirection.LONG
    )
    assert long_stop_loss == 96  # 100 - (2 * 2)
    
    # Test short position stop loss
    short_stop_loss = strategy_calculator._calculate_stop_loss(
        current_price=100, 
        atr=2, 
        direction=TradeDirection.SHORT
    )
    assert short_stop_loss == 104  # 100 + (2 * 2)

def test_take_profit_calculation(strategy_calculator):
    # Test long position take profit
    long_take_profit = strategy_calculator._calculate_take_profit(
        current_price=100, 
        atr=2, 
        direction=TradeDirection.LONG
    )
    assert long_take_profit == 108  # 100 + (2 * 2 * 2)
    
    # Test short position take profit
    short_take_profit = strategy_calculator._calculate_take_profit(
        current_price=100, 
        atr=2, 
        direction=TradeDirection.SHORT
    )
    assert short_take_profit == 92  # 100 - (2 * 2 * 2) 

def test_supertrend_integration(strategy_calculator, sample_price_data):
    """
    Test that Supertrend is integrated into trade direction determination
    """
    # Calculate Supertrend separately
    supertrend = calculate_supertrend(
        sample_price_data['high'], 
        sample_price_data['low'], 
        sample_price_data['close']
    )
    
    # Verify Supertrend calculation
    assert 'trend' in supertrend
    assert 'value' in supertrend
    assert 'direction' in supertrend
    assert supertrend['trend'] in ['up', 'down', None]
    
    # Verify trade direction calculation
    entry_strategy = strategy_calculator.calculate_entry_strategy(
        sample_price_data['high'], 
        sample_price_data['low'], 
        sample_price_data['close']
    )
    
    assert entry_strategy['direction'] in [TradeDirection.LONG.value, TradeDirection.SHORT.value] 

def test_supertrend_flip_tracking(strategy_calculator, sample_price_data):
    """
    Test Supertrend trend flip tracking
    """
    # Run multiple iterations to simulate trend changes
    first_entry = strategy_calculator.calculate_entry_strategy(
        sample_price_data['high'], 
        sample_price_data['low'], 
        sample_price_data['close']
    )
    
    # Simulate a price series that might cause a trend change
    modified_close = sample_price_data['close'] * 1.5  # Amplify prices to force trend change
    
    second_entry = strategy_calculator.calculate_entry_strategy(
        sample_price_data['high'], 
        sample_price_data['low'], 
        modified_close
    )
    
    # Verify Supertrend tracking
    assert 'supertrend' in first_entry
    assert 'supertrend' in second_entry
    
    # Check flip tracking details
    first_supertrend = first_entry['supertrend']
    second_supertrend = second_entry['supertrend']
    
    # Validate Supertrend components
    assert 'trend' in first_supertrend
    assert 'value' in first_supertrend
    assert 'direction' in first_supertrend
    
    # Optional: Check for potential trend flip
    if second_supertrend['flip_price'] is not None:
        assert isinstance(second_supertrend['flip_price'], float)
        assert second_supertrend['flip_price'] > 0
        assert second_supertrend['flip_timestamp'] is not None

def test_supertrend_logging(strategy_calculator, sample_price_data, caplog):
    """
    Test logging of Supertrend trend flips
    """
    # Capture log messages
    caplog.set_level(logging.INFO)
    
    # Run multiple iterations to potentially trigger trend flip logging
    strategy_calculator.calculate_entry_strategy(
        sample_price_data['high'], 
        sample_price_data['low'], 
        sample_price_data['close']
    )
    
    modified_close = sample_price_data['close'] * 1.5
    strategy_calculator.calculate_entry_strategy(
        sample_price_data['high'], 
        sample_price_data['low'], 
        modified_close
    )
    
    # Check log messages
    supertrend_logs = [record for record in caplog.records if 'Supertrend Trend Flip' in record.message]
    
    # Optional logging verification
    if len(supertrend_logs) > 0:
        for log in supertrend_logs:
            assert 'From' in log.message
            assert 'to' in log.message
            assert 'at price' in log.message
            assert 'on' in log.message 