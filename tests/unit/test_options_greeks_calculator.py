import pytest
import numpy as np

from src.shared.technical_analysis.options_greeks_calculator import (
    OptionsGreeksCalculator,
    GreeksSnapshot,
    OptionType
)

@pytest.fixture
def greeks_calculator():
    return OptionsGreeksCalculator()

@pytest.fixture
def sample_greeks():
    """Sample Greeks data for testing"""
    return GreeksSnapshot(
        delta=0.6,
        gamma=0.05,
        theta=-0.02,
        vega=0.15,
        rho=0.01,
        price=3.50,
        underlying_price=100.0,
        strike=95.0,
        time_to_expiry=0.25,  # 3 months
        implied_volatility=0.30,
        risk_free_rate=0.05
    )

def test_estimate_new_delta(greeks_calculator, sample_greeks):
    """Test delta estimation"""
    # Small price increase
    new_greeks = greeks_calculator.estimate_future_greeks(
        sample_greeks,
        underlying_price_change=2.0,  # $2 increase
        time_elapsed=0.01  # 1 day
    )
    
    assert 'delta' in new_greeks
    # Delta should increase: 0.6 + (0.05 * 2) = 0.7
    expected_delta = 0.6 + (0.05 * 2)
    assert abs(new_greeks['delta'] - expected_delta) < 0.001

def test_estimate_new_price(greeks_calculator, sample_greeks):
    """Test price estimation"""
    new_greeks = greeks_calculator.estimate_future_greeks(
        sample_greeks,
        underlying_price_change=1.0,  # $1 increase
        time_elapsed=0.01  # 1 day
    )
    
    assert 'price' in new_greeks
    assert 'price_change' in new_greeks
    assert new_greeks['price'] > 0
    
    # Price should increase due to positive delta
    assert new_greeks['price_change'] > 0

def test_breakeven_calculation(greeks_calculator, sample_greeks):
    """Test breakeven point calculations"""
    # Test call option breakeven
    call_breakeven = greeks_calculator.calculate_breakeven_points(
        sample_greeks,
        OptionType.CALL
    )
    
    assert 'breakeven_price' in call_breakeven
    # For calls: breakeven = strike + premium = 95 + 3.50 = 98.50
    expected_breakeven = 95.0 + 3.50
    assert abs(call_breakeven['breakeven_price'] - expected_breakeven) < 0.01
    
    # Test put option breakeven
    put_breakeven = greeks_calculator.calculate_breakeven_points(
        sample_greeks,
        OptionType.PUT
    )
    
    assert 'breakeven_price' in put_breakeven
    # For puts: breakeven = strike - premium = 95 - 3.50 = 91.50
    expected_breakeven = 95.0 - 3.50
    assert abs(put_breakeven['breakeven_price'] - expected_breakeven) < 0.01

def test_probability_of_profit(greeks_calculator, sample_greeks):
    """Test probability of profit estimation"""
    # Test call option probability
    call_prob = greeks_calculator.estimate_probability_of_profit(
        sample_greeks,
        target_price=100.0,  # Above breakeven
        option_type=OptionType.CALL
    )
    
    assert 0.0 <= call_prob <= 1.0
    
    # Test put option probability
    put_prob = greeks_calculator.estimate_probability_of_profit(
        sample_greeks,
        target_price=90.0,  # Below breakeven
        option_type=OptionType.PUT
    )
    
    assert 0.0 <= put_prob <= 1.0

def test_time_decay_effect(greeks_calculator, sample_greeks):
    """Test that theta affects price over time"""
    # No price change, just time passing
    new_greeks = greeks_calculator.estimate_future_greeks(
        sample_greeks,
        underlying_price_change=0.0,
        time_elapsed=0.1  # About 1 month
    )
    
    assert 'price' in new_greeks
    # Price should decrease due to theta (time decay)
    assert new_greeks['price'] < sample_greeks.price

def test_volatility_effect(greeks_calculator, sample_greeks):
    """Test that vega affects price with volatility changes"""
    # Increase volatility
    new_greeks = greeks_calculator.estimate_future_greeks(
        sample_greeks,
        underlying_price_change=0.0,
        time_elapsed=0.0,
        volatility_change=0.05  # 5% volatility increase
    )
    
    assert 'price' in new_greeks
    # Price should increase due to higher volatility
    assert new_greeks['price'] > sample_greeks.price 