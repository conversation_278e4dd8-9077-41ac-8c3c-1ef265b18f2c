"""
Unit Tests for ML-Based Sentiment Analyzer

This module tests the ML-based sentiment analyzer for financial queries.
"""

import pytest
import asyncio
from typing import Dict, Any

from src.bot.pipeline.commands.ask.stages.ml_sentiment_analyzer import (
    MLSentimentAnalyzer, analyze_sentiment_ml
)

@pytest.fixture
def sentiment_analyzer():
    """Create a sentiment analyzer instance for testing"""
    return MLSentimentAnalyzer()

@pytest.mark.asyncio
async def test_sentiment_analyzer_initialization(sentiment_analyzer):
    """Test that sentiment analyzer initializes correctly"""
    assert sentiment_analyzer is not None
    assert sentiment_analyzer.model_loaded
    assert len(sentiment_analyzer.word_embeddings) > 0
    assert len(sentiment_analyzer.sentiment_weights) > 0
    assert len(sentiment_analyzer.financial_terms) > 0

@pytest.mark.asyncio
async def test_positive_sentiment_analysis():
    """Test positive sentiment analysis"""
    # Test queries with positive sentiment
    positive_queries = [
        "I'm bullish on AAPL",
        "MSFT looks like a great buy opportunity",
        "The market is showing strong upward momentum",
        "Excellent earnings report for GOOGL",
        "TSLA is breaking out with positive momentum"
    ]
    
    for query in positive_queries:
        result = await analyze_sentiment_ml(query)
        
        assert result is not None
        assert "score" in result
        assert "label" in result
        assert "confidence" in result
        
        assert result["label"] == "positive"
        assert result["score"] > 0
        assert result["confidence"] > 0

@pytest.mark.asyncio
async def test_negative_sentiment_analysis():
    """Test negative sentiment analysis"""
    # Test queries with negative sentiment
    negative_queries = [
        "I'm bearish on AAPL",
        "MSFT looks like a terrible investment right now",
        "The market is showing weak momentum",
        "Poor earnings report for GOOGL",
        "TSLA is breaking down with negative momentum"
    ]
    
    for query in negative_queries:
        result = await analyze_sentiment_ml(query)
        
        assert result is not None
        assert "score" in result
        assert "label" in result
        assert "confidence" in result
        
        assert result["label"] == "negative"
        assert result["score"] < 0
        assert result["confidence"] > 0

@pytest.mark.asyncio
async def test_neutral_sentiment_analysis():
    """Test neutral sentiment analysis"""
    # Test queries with neutral sentiment
    neutral_queries = [
        "What is the current price of AAPL?",
        "Show me the chart for MSFT",
        "How does the RSI indicator work?",
        "Compare GOOGL and AMZN",
        "What are the support and resistance levels for TSLA?"
    ]
    
    for query in neutral_queries:
        result = await analyze_sentiment_ml(query)
        
        assert result is not None
        assert "score" in result
        assert "label" in result
        assert "confidence" in result
        
        # Neutral queries might be classified as slightly positive or negative
        # but the confidence should be lower
        assert abs(result["score"]) < 0.5

@pytest.mark.asyncio
async def test_financial_phrase_detection():
    """Test financial phrase detection"""
    # Test queries with financial phrases
    queries_with_phrases = [
        ("The market is in a bull market phase", "bull market", "positive"),
        ("We're seeing a bear market in tech stocks", "bear market", "negative"),
        ("AAPL is in a strong uptrend", "uptrend", "positive"),
        ("MSFT is showing a clear downtrend", "downtrend", "negative"),
        ("TSLA just had a breakout above resistance", "breakout", "positive"),
        ("GOOGL experienced a breakdown below support", "breakdown", "negative")
    ]
    
    for query, expected_phrase, expected_sentiment in queries_with_phrases:
        result = await analyze_sentiment_ml(query)
        
        assert result is not None
        assert "financial_phrases" in result
        
        phrases = result["financial_phrases"]
        assert len(phrases) > 0
        
        # Check if expected phrase is detected
        phrase_found = False
        for phrase_data in phrases:
            if phrase_data["phrase"] == expected_phrase:
                phrase_found = True
                assert phrase_data["sentiment"] == expected_sentiment
                break
        
        assert phrase_found, f"Expected phrase '{expected_phrase}' not found in {phrases}"

@pytest.mark.asyncio
async def test_sentiment_confidence():
    """Test sentiment confidence calculation"""
    # Test queries with varying confidence levels
    queries = [
        # High confidence (many sentiment words)
        ("AAPL is a great stock with excellent growth, strong momentum, and positive outlook", 0.7),
        # Medium confidence (some sentiment words)
        ("MSFT looks good with decent potential", 0.5),
        # Low confidence (few sentiment words)
        ("GOOGL might go up", 0.3)
    ]
    
    for query, min_expected_confidence in queries:
        result = await analyze_sentiment_ml(query)
        
        assert result is not None
        assert "confidence" in result
        
        assert result["confidence"] >= min_expected_confidence, \
            f"Expected confidence >= {min_expected_confidence}, got {result['confidence']}"

@pytest.mark.asyncio
async def test_model_update():
    """Test model update with feedback"""
    analyzer = MLSentimentAnalyzer()
    
    # Get initial sentiment for a test query
    test_query = "This stock is performing adequately"
    initial_result = await analyzer.analyze_sentiment(test_query)
    
    # Provide feedback that this should be positive
    feedback = {
        "text": test_query,
        "expected_sentiment": "positive"
    }
    
    # Update model with feedback
    update_success = await analyzer.update_model(feedback)
    assert update_success
    
    # Get updated sentiment
    updated_result = await analyzer.analyze_sentiment(test_query)
    
    # Check if sentiment has shifted toward positive
    assert updated_result["score"] >= initial_result["score"]

@pytest.mark.asyncio
async def test_sentiment_extraction():
    """Test extraction of sentiment words"""
    analyzer = MLSentimentAnalyzer()
    
    # Test query with mixed sentiment
    query = "AAPL has strong growth but faces significant risks with weak margins"
    result = await analyzer.analyze_sentiment(query)
    
    assert "positive_words" in result
    assert "negative_words" in result
    assert "neutral_words" in result
    
    # Check for specific words
    assert "strong" in result["positive_words"]
    assert "growth" in result["positive_words"]
    assert "risks" in result["negative_words"]
    assert "weak" in result["negative_words"]

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
