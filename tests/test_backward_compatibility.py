"""
Tests for backward compatibility modules.

These tests verify that the backward compatibility modules correctly import
from the canonical locations and maintain the expected interface.
"""

import pytest
import warnings
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

# Import both canonical and backward compatibility modules
from src.shared.ai_services.ai_service_wrapper import AIChatProcessor as CanonicalAIChatProcessor
from src.bot.pipeline.ask.stages.ai_chat_processor import AIChatProcessor as AskAIChatProcessor
from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor as CommandsAIChatProcessor

from src.shared.market_analysis.unified_signal_analyzer import UnifiedSignalAnalyzer
from src.shared.market_analysis.signal_analyzer import SignalAnalyzer


@pytest.fixture
def mock_config():
    """Fixture for mock configuration."""
    return {
        'api_key': 'test-key',
        'model': 'test-model',
        'technical': {'enabled': True}
    }


def test_ai_chat_processor_inheritance():
    """Test that backward compatibility classes inherit from canonical class."""
    assert issubclass(AskAIChatProcessor, CanonicalAIChatProcessor)
    assert issubclass(CommandsAIChatProcessor, CanonicalAIChatProcessor)


def test_signal_analyzer_delegation():
    """Test that SignalAnalyzer delegates to UnifiedSignalAnalyzer."""
    signal_analyzer = SignalAnalyzer()
    assert hasattr(signal_analyzer, '_analyzer')
    assert isinstance(signal_analyzer._analyzer, UnifiedSignalAnalyzer)


def test_deprecation_warning_ask_ai_processor():
    """Test that importing AskAIChatProcessor raises a deprecation warning."""
    with warnings.catch_warnings(record=True) as w:
        # Cause all warnings to always be triggered
        warnings.simplefilter("always")
        
        # Import should trigger warning
        from src.bot.pipeline.ask.stages.ai_chat_processor import AIChatProcessor
        
        # Verify warning was raised
        assert len(w) >= 1
        assert issubclass(w[0].category, DeprecationWarning)
        assert "deprecated" in str(w[0].message).lower()


def test_deprecation_warning_commands_ai_processor():
    """Test that importing CommandsAIChatProcessor raises a deprecation warning."""
    with warnings.catch_warnings(record=True) as w:
        # Cause all warnings to always be triggered
        warnings.simplefilter("always")
        
        # Import should trigger warning
        from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor
        
        # Verify warning was raised
        assert len(w) >= 1
        assert issubclass(w[0].category, DeprecationWarning)
        assert "deprecated" in str(w[0].message).lower()


def test_deprecation_warning_signal_analyzer():
    """Test that importing SignalAnalyzer raises a deprecation warning."""
    with warnings.catch_warnings(record=True) as w:
        # Cause all warnings to always be triggered
        warnings.simplefilter("always")
        
        # Import should trigger warning
        from src.shared.market_analysis.signal_analyzer import SignalAnalyzer
        
        # Verify warning was raised
        assert len(w) >= 1
        assert issubclass(w[0].category, DeprecationWarning)
        assert "deprecated" in str(w[0].message).lower()


@pytest.mark.asyncio
async def test_ask_processor_function():
    """Test that the ask processor function works correctly."""
    with patch('src.shared.ai_services.ai_service_wrapper.AIChatProcessor') as mock_processor:
        mock_instance = AsyncMock()
        mock_instance.process.return_value = {
            'response': 'Test response',
            'data': {},
            'intent': 'general_question',
            'symbols': [],
            'needs_data': False
        }
        mock_processor.return_value = mock_instance
        
        # Import processor function
        from src.bot.pipeline.ask.stages.ai_chat_processor import processor
        
        # Create mock context
        class MockContext:
            def __init__(self):
                self.pipeline_id = "test-pipeline"
                self.original_query = "test query"
        
        # Call processor function
        results = {'query': 'test query'}
        result = await processor(MockContext(), results)
        
        # Verify result
        assert 'response' in result
        assert result['response'] == 'Test response'


@pytest.mark.asyncio
async def test_commands_processor_function():
    """Test that the commands processor function works correctly."""
    with patch('src.shared.ai_services.ai_service_wrapper.AIChatProcessor') as mock_processor:
        mock_instance = AsyncMock()
        mock_instance.process.return_value = {
            'response': 'Test response',
            'data': {},
            'intent': 'general_question',
            'symbols': [],
            'needs_data': False
        }
        mock_processor.return_value = mock_instance
        
        # Import processor function
        from src.bot.pipeline.commands.ask.stages.ai_chat_processor import processor
        
        # Create mock context
        class MockContext:
            def __init__(self):
                self.pipeline_id = "test-pipeline"
                self.original_query = "test query"
        
        # Call processor function
        results = {'query': 'test query'}
        result = await processor(MockContext(), results)
        
        # Verify result
        assert 'response' in result
        assert result['response'] == 'Test response'


@pytest.mark.asyncio
async def test_signal_analyzer_delegation_methods():
    """Test that SignalAnalyzer methods delegate to UnifiedSignalAnalyzer."""
    # Create mock UnifiedSignalAnalyzer
    mock_unified = AsyncMock()
    mock_unified.analyze_market_data.return_value = ["test_signal"]
    mock_unified.get_signal_summary.return_value = {"total_signals": 1}
    
    # Create SignalAnalyzer with mocked UnifiedSignalAnalyzer
    with patch('src.shared.market_analysis.unified_signal_analyzer.unified_signal_analyzer', mock_unified):
        from src.shared.market_analysis.signal_analyzer import SignalAnalyzer
        signal_analyzer = SignalAnalyzer()
        
        # Test analyze_market_data delegation
        result = await signal_analyzer.analyze_market_data("AAPL", "1d", {})
        assert result == ["test_signal"]
        mock_unified.analyze_market_data.assert_called_once_with("AAPL", "1d", {})
        
        # Test get_signal_summary delegation
        signals = ["signal1", "signal2"]
        result = signal_analyzer.get_signal_summary(signals)
        assert result == {"total_signals": 1}
        mock_unified.get_signal_summary.assert_called_once_with(signals)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
