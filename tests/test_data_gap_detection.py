"""
Tests for data gap detection and quality assessment functionality.
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.shared.data_validation import (
    GapDetector, 
    DataGap, 
    GapSeverity, 
    gap_detector,
    detect_data_gaps,
    assess_data_quality
)


class TestDataGap:
    """Test the DataGap dataclass."""
    
    def test_data_gap_creation(self):
        """Test creating a DataGap object."""
        start_time = datetime(2024, 1, 1, 9, 30)
        end_time = datetime(2024, 1, 1, 10, 30)
        
        gap = DataGap(
            symbol="AAPL",
            start_time=start_time,
            end_time=end_time,
            severity=GapSeverity.MAJOR,
            interval_type="1m",
            expected_count=100,
            actual_count=80,
            missing_count=20,
            gap_percentage=20.0
        )
        
        assert gap.symbol == "AAPL"
        assert gap.start_time == start_time
        assert gap.end_time == end_time
        assert gap.severity == GapSeverity.MAJOR
        assert gap.interval_type == "1m"
        assert gap.expected_count == 100
        assert gap.actual_count == 80
        assert gap.missing_count == 20
        assert gap.gap_percentage == 20.0
        assert gap.duration_seconds == 3600  # 1 hour
    
    def test_data_gap_to_dict(self):
        """Test converting DataGap to dictionary."""
        start_time = datetime(2024, 1, 1, 9, 30)
        end_time = datetime(2024, 1, 1, 10, 30)
        
        gap = DataGap(
            symbol="AAPL",
            start_time=start_time,
            end_time=end_time,
            severity=GapSeverity.MINOR,
            interval_type="5m",
            expected_count=12,
            actual_count=10,
            missing_count=2,
            gap_percentage=16.67
        )
        
        gap_dict = gap.to_dict()
        
        assert gap_dict['symbol'] == "AAPL"
        assert gap_dict['severity'] == "minor"
        assert gap_dict['interval_type'] == "5m"
        assert gap_dict['expected_count'] == 12
        assert gap_dict['actual_count'] == 10
        assert gap_dict['missing_count'] == 2
        assert gap_dict['gap_percentage'] == 16.67


class TestGapDetector:
    """Test the GapDetector class."""
    
    @pytest.fixture
    def detector(self):
        """Create a fresh GapDetector instance."""
        return GapDetector()
    
    @pytest.fixture
    def sample_data(self):
        """Create sample time series data."""
        # Create data with some gaps
        timestamps = [
            datetime(2024, 1, 1, 9, 30),  # 9:30 AM
            datetime(2024, 1, 1, 9, 31),  # 9:31 AM
            datetime(2024, 1, 1, 9, 32),  # 9:32 AM
            # Gap: 9:33-9:37 (5 minutes missing)
            datetime(2024, 1, 1, 9, 38),  # 9:38 AM
            datetime(2024, 1, 1, 9, 39),  # 9:39 AM
            datetime(2024, 1, 1, 9, 40),  # 9:40 AM
            # Gap: 9:41-10:00 (20 minutes missing)
            datetime(2024, 1, 1, 10, 1),  # 10:01 AM
            datetime(2024, 1, 1, 10, 2),  # 10:02 AM
        ]
        
        data = pd.DataFrame({
            'open': [100, 101, 102, 103, 104, 105, 106, 107],
            'high': [101, 102, 103, 104, 105, 106, 107, 108],
            'low': [99, 100, 101, 102, 103, 104, 105, 106],
            'close': [101, 102, 103, 104, 105, 106, 107, 108],
            'volume': [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700]
        }, index=timestamps)
        
        return data
    
    def test_detect_gaps_1m_interval(self, detector, sample_data):
        """Test gap detection for 1-minute interval data."""
        gaps = detector.detect_gaps(sample_data, "AAPL", "1m")
        
        assert len(gaps) == 2
        
        # First gap: 9:33-9:37 (5 minutes)
        first_gap = gaps[0]
        assert first_gap.start_time == datetime(2024, 1, 1, 9, 33)
        assert first_gap.end_time == datetime(2024, 1, 1, 9, 37)
        assert first_gap.severity == GapSeverity.MINOR
        assert first_gap.missing_count == 5
        
        # Second gap: 9:41-10:00 (20 minutes)
        second_gap = gaps[1]
        assert second_gap.start_time == datetime(2024, 1, 1, 9, 41)
        assert second_gap.end_time == datetime(2024, 1, 1, 10, 0)
        assert second_gap.severity == GapSeverity.MAJOR
        assert second_gap.missing_count == 20
    
    def test_detect_gaps_empty_data(self, detector):
        """Test gap detection with empty data."""
        empty_data = pd.DataFrame()
        gaps = detector.detect_gaps(empty_data, "AAPL", "1m")
        
        assert len(gaps) == 0
    
    def test_determine_gap_severity(self, detector):
        """Test gap severity determination."""
        assert detector._determine_gap_severity(300) == GapSeverity.MINOR      # 5 minutes
        assert detector._determine_gap_severity(1800) == GapSeverity.MODERATE  # 30 minutes
        assert detector._determine_gap_severity(3600) == GapSeverity.MAJOR     # 1 hour
        assert detector._determine_gap_severity(7200) == GapSeverity.MAJOR     # 2 hours
        assert detector._determine_gap_severity(10000) == GapSeverity.CRITICAL # >2 hours
    
    def test_calculate_expected_intervals(self, detector):
        """Test calculation of expected intervals."""
        start_time = datetime(2024, 1, 1, 9, 30)
        end_time = datetime(2024, 1, 1, 10, 30)
        
        # 1-hour range with 1-minute intervals
        expected = detector._calculate_expected_intervals(start_time, end_time, "1m")
        assert expected == 61  # 60 minutes + 1 (inclusive)
        
        # 1-hour range with 5-minute intervals
        expected = detector._calculate_expected_intervals(start_time, end_time, "5m")
        assert expected == 13  # 12 intervals + 1 (inclusive)
    
    def test_generate_expected_timestamps(self, detector):
        """Test generation of expected timestamps."""
        start_time = datetime(2024, 1, 1, 9, 30)
        end_time = datetime(2024, 1, 1, 9, 35)
        
        timestamps = detector._generate_expected_timestamps(start_time, end_time, "1m")
        
        assert len(timestamps) == 6  # 9:30, 9:31, 9:32, 9:33, 9:34, 9:35
        assert timestamps[0] == start_time
        assert timestamps[-1] == end_time


class TestDataQualityAssessment:
    """Test data quality assessment functionality."""
    
    @pytest.fixture
    def sample_data_with_gaps(self):
        """Create sample data with gaps for quality assessment."""
        timestamps = [
            datetime(2024, 1, 1, 9, 30),
            datetime(2024, 1, 1, 9, 31),
            datetime(2024, 1, 1, 9, 32),
            # Gap: 9:33-9:37
            datetime(2024, 1, 1, 9, 38),
            datetime(2024, 1, 1, 9, 39),
        ]
        
        data = pd.DataFrame({
            'open': [100, 101, 102, 103, 104],
            'high': [101, 102, 103, 104, 105],
            'low': [99, 100, 101, 102, 103],
            'close': [101, 102, 103, 104, 105],
            'volume': [1000, 1100, 1200, 1300, 1400]
        }, index=timestamps)
        
        return data
    
    def test_assess_data_quality_with_gaps(self, sample_data_with_gaps):
        """Test data quality assessment with gaps."""
        assessment = assess_data_quality(sample_data_with_gaps, "AAPL", "1m")
        
        assert 'quality_score' in assessment
        assert 'completeness' in assessment
        assert 'gap_count' in assessment
        assert 'gaps' in assessment
        assert 'status' in assessment
        assert 'recommendations' in assessment
        
        assert assessment['gap_count'] == 1
        assert assessment['completeness'] < 100  # Should be less than 100% due to gaps
        assert assessment['quality_score'] < 100  # Should be penalized for gaps
    
    def test_assess_data_quality_empty_data(self):
        """Test data quality assessment with empty data."""
        empty_data = pd.DataFrame()
        assessment = assess_data_quality(empty_data, "AAPL", "1m")
        
        assert assessment['quality_score'] == 0.0
        assert assessment['completeness'] == 0.0
        assert assessment['status'] == 'no_data'
        assert 'No data available' in assessment['recommendations'][0]
    
    def test_assess_data_quality_complete_data(self):
        """Test data quality assessment with complete data."""
        # Create complete 1-minute data for 1 hour
        timestamps = []
        for i in range(60):
            timestamps.append(datetime(2024, 1, 1, 9, 30) + timedelta(minutes=i))
        
        complete_data = pd.DataFrame({
            'open': [100] * 60,
            'high': [101] * 60,
            'low': [99] * 60,
            'close': [100] * 60,
            'volume': [1000] * 60
        }, index=timestamps)
        
        assessment = assess_data_quality(complete_data, "AAPL", "1m")
        
        assert assessment['completeness'] >= 95  # Should be very high
        assert assessment['quality_score'] >= 80  # Should be good
        assert assessment['gap_count'] == 0
        assert assessment['status'] in ['excellent', 'good']
    
    def test_quality_score_calculation(self):
        """Test quality score calculation with different scenarios."""
        detector = GapDetector()
        
        # Test with no gaps (high completeness)
        assessment = {
            'completeness': 95.0,
            'gaps': []
        }
        score = detector._calculate_quality_score(95.0, [])
        assert score >= 90  # Should be excellent
        
        # Test with minor gaps
        minor_gap = Mock()
        minor_gap.severity = GapSeverity.MINOR
        assessment = {
            'completeness': 90.0,
            'gaps': [minor_gap]
        }
        score = detector._calculate_quality_score(90.0, [minor_gap])
        assert 80 <= score < 90  # Should be penalized but still good
        
        # Test with critical gaps
        critical_gap = Mock()
        critical_gap.severity = GapSeverity.CRITICAL
        assessment = {
            'completeness': 80.0,
            'gaps': [critical_gap]
        }
        score = detector._calculate_quality_score(80.0, [critical_gap])
        assert score < 70  # Should be significantly penalized


class TestGapDetectionIntegration:
    """Test integration of gap detection with other systems."""
    
    def test_detect_data_gaps_convenience_function(self):
        """Test the convenience function for gap detection."""
        # Create data with gaps
        timestamps = [
            datetime(2024, 1, 1, 9, 30),
            datetime(2024, 1, 1, 9, 31),
            # Gap: 9:32-9:36
            datetime(2024, 1, 1, 9, 37),
        ]
        
        data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [101, 102, 103],
            'low': [99, 100, 101],
            'close': [101, 102, 103],
            'volume': [1000, 1100, 1200]
        }, index=timestamps)
        
        gaps = detect_data_gaps(data, "AAPL", "1m")
        
        assert len(gaps) == 1
        assert gaps[0].symbol == "AAPL"
        assert gaps[0].interval_type == "1m"
        assert gaps[0].missing_count == 5  # 5 minutes missing
    
    def test_assess_data_quality_convenience_function(self):
        """Test the convenience function for quality assessment."""
        # Create data with gaps
        timestamps = [
            datetime(2024, 1, 1, 9, 30),
            datetime(2024, 1, 1, 9, 31),
            # Gap: 9:32-9:36
            datetime(2024, 1, 1, 9, 37),
        ]
        
        data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [101, 102, 103],
            'low': [99, 100, 101],
            'close': [101, 102, 103],
            'volume': [1000, 1100, 1200]
        }, index=timestamps)
        
        assessment = assess_data_quality(data, "AAPL", "1m")
        
        assert 'quality_score' in assessment
        assert 'completeness' in assessment
        assert 'gap_count' in assessment
        assert assessment['gap_count'] == 1


if __name__ == "__main__":
    pytest.main([__file__]) 