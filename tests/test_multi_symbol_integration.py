"""
Integration test for multi-symbol queries with mocked services.
Tests response formatting, data handling, and Discord compatibility.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any

# Import the pipeline function
from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
from src.bot.pipeline.core.context_manager import PipelineStatus


class MockEnhancedMarketDataService:
    """Mock market data service for testing with realistic data"""
    
    async def get_comprehensive_stock_data(self, symbol: str) -> Dict[str, Any]:
        """Return realistic mock data for testing"""
        current_time = datetime.now().isoformat()
        
        # Realistic data patterns for different symbols
        data_templates = {
            "AAPL": {
                "symbol": "AAPL",
                "current_price": 178.72,
                "change_percent": 2.15,
                "volume": 45678900,
                "market_cap": 2800000000000,
                "timestamp": current_time,
                "support": 175.50,
                "resistance": 180.00,
                "status": "success"
            },
            "GOOGL": {
                "symbol": "GOOGL",
                "current_price": 2801.34,
                "change_percent": -0.87,
                "volume": 12345600,
                "market_cap": 1850000000000,
                "timestamp": current_time,
                "support": 2780.00,
                "resistance": 2850.00,
                "status": "success"
            },
            "TSLA": {
                "symbol": "TSLA",
                "current_price": 245.67,
                "change_percent": 5.23,
                "volume": 78901200,
                "market_cap": 780000000000,
                "timestamp": current_time,
                "support": 240.00,
                "resistance": 250.00,
                "status": "success"
            }
        }
        
        return data_templates.get(symbol, {
            "symbol": symbol,
            "current_price": 0.00,
            "change_percent": 0.00,
            "volume": 0,
            "status": "error",
            "message": f"Symbol {symbol} not found"
        })


@pytest.fixture
def mock_market_data(monkeypatch):
    """Fixture to mock the market data service"""
    mock_service = MockEnhancedMarketDataService()
    monkeypatch.setattr(
        'src.bot.pipeline.commands.ask.stages.ai_chat_processor.EnhancedMarketDataService',
        lambda: mock_service
    )
    return mock_service


@pytest.fixture
def mock_ai_response():
    """Fixture to mock AI responses for multi-symbol queries"""
    def _mock_ai_response(query: str) -> Dict[str, Any]:
        """Generate mock AI response based on query"""
        if "AAPL" in query and "GOOGL" in query:
            return {
                "intent": "market_overview",
                "symbols": ["AAPL", "GOOGL"],
                "needs_data": True,
                "response": "Comparing Apple and Google performance"
            }
        elif "tech" in query.lower():
            return {
                "intent": "market_overview", 
                "symbols": ["AAPL", "GOOGL", "TSLA"],
                "needs_data": True,
                "response": "Tech sector analysis"
            }
        else:
            return {
                "intent": "general_question",
                "symbols": [],
                "needs_data": False,
                "response": "General response"
            }
    
    return _mock_ai_response


@pytest.mark.asyncio
async def test_multi_symbol_market_overview(mock_market_data, mock_ai_response):
    """Test multi-symbol query with market overview intent"""
    # Mock the AI call to return multi-symbol response
    with patch('src.bot.pipeline.commands.ask.stages.ai_chat_processor.AIChatProcessor._call_ai_model') as mock_ai:
        mock_ai.return_value = mock_ai_response("Compare AAPL and GOOGL")
        
        # Execute pipeline
        context = await execute_ask_pipeline(
            query="Compare Apple and Google stock performance",
            user_id="test_user",
            guild_id="test_guild"
        )
        
        # Verify pipeline completed
        assert context.status == PipelineStatus.COMPLETED
        
        # Check response contains expected data
        assert 'response' in context.processing_results
        response = context.processing_results['response']
        
        # Verify response includes both symbols
        assert 'AAPL' in response
        assert 'GOOGL' in response
        
        # Verify response includes price information
        assert '$' in response  # Price formatting
        assert '%' in response  # Percentage change
        
        # Verify response includes timestamp/date
        assert '2025' in response or 'Updated:' in response or 'timestamp' in response.lower()
        
        # Verify response includes confidence or data quality
        assert 'confidence' in response.lower() or 'quality' in response.lower()
        
        # Verify data was fetched for both symbols
        assert 'data' in context.processing_results
        data = context.processing_results['data']
        assert 'AAPL' in data
        assert 'GOOGL' in data
        assert data['AAPL']['status'] == 'success'
        assert data['GOOGL']['status'] == 'success'


@pytest.mark.asyncio
async def test_tech_sector_analysis(mock_market_data, mock_ai_response):
    """Test tech sector analysis with multiple symbols"""
    with patch('src.bot.pipeline.commands.ask.stages.ai_chat_processor.AIChatProcessor._call_ai_model') as mock_ai:
        mock_ai.return_value = mock_ai_response("tech stocks analysis")
        
        context = await execute_ask_pipeline(
            query="Analyze tech sector stocks",
            user_id="test_user",
            guild_id="test_guild"
        )
        
        assert context.status == PipelineStatus.COMPLETED
        assert 'response' in context.processing_results
        
        response = context.processing_results['response']
        data = context.processing_results.get('data', {})
        
        # Should include multiple tech symbols
        tech_symbols = ['AAPL', 'GOOGL', 'TSLA']
        for symbol in tech_symbols:
            if symbol in data:
                assert symbol in response
        
        # Verify professional formatting
        assert 'Analysis' in response
        assert 'Price' in response or '$' in response
        assert 'Change' in response or '%' in response


@pytest.mark.asyncio
async def test_response_contains_required_elements(mock_market_data):
    """Test that responses contain all required elements: date, confidence, indicators, etc."""
    with patch('src.bot.pipeline.commands.ask.stages.ai_chat_processor.AIChatProcessor._call_ai_model') as mock_ai:
        mock_ai.return_value = {
            "intent": "stock_analysis",
            "symbols": ["AAPL"],
            "needs_data": True,
            "response": "Apple stock analysis"
        }
        
        context = await execute_ask_pipeline(
            query="What about AAPL stock?",
            user_id="test_user",
            guild_id="test_guild"
        )
        
        assert context.status == PipelineStatus.COMPLETED
        response = context.processing_results['response']
        
        # Check for required elements in the response
        required_elements = [
            'AAPL',  # Symbol
            '$',     # Price formatting
            '%',     # Percentage change
            'Updated', 'timestamp', '2025',  # Date/time indicators
            'confidence', 'quality', 'Certainty',  # Confidence indicators
            'Volume', 'volume',  # Trading volume
            'Analysis', 'analysis'  # Analytical content
        ]
        
        # At least 5 of these should be present
        present_elements = sum(1 for elem in required_elements if elem in response)
        assert present_elements >= 5, f"Missing required elements in response: {response}"


@pytest.mark.asyncio
async def test_discord_compatible_response_length(mock_market_data):
    """Test that responses are within Discord's character limits"""
    with patch('src.bot.pipeline.commands.ask.stages.ai_chat_processor.AIChatProcessor._call_ai_model') as mock_ai:
        mock_ai.return_value = {
            "intent": "market_overview",
            "symbols": ["AAPL", "GOOGL", "TSLA"],
            "needs_data": True,
            "response": "Market overview analysis"
        }
        
        context = await execute_ask_pipeline(
            query="Market overview please",
            user_id="test_user",
            guild_id="test_guild"
        )
        
        assert context.status == PipelineStatus.COMPLETED
        response = context.processing_results['response']
        
        # Discord has a 2000 character limit for messages
        assert len(response) <= 2000, f"Response too long for Discord: {len(response)} characters"
        
        # Response should be meaningful, not empty
        assert len(response) > 100, "Response too short"
        assert 'AAPL' in response or 'Google' in response or 'Tesla' in response


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])