import pytest
from src.database.supabase_client import supabase_client, test_supabase_connection

def test_supabase_client_initialization():
    """
    Test Supabase client initialization.
    """
    client = supabase_client.get_client()
    assert client is not None, "Supabase client should be initialized"

def test_supabase_connection_status():
    """
    Test Supabase connection status.
    """
    connection_status = test_supabase_connection()
    assert connection_status is True, "Supabase connection test should pass"

def test_supabase_insert_and_query():
    """
    Test inserting and querying data in Supabase.
    """
    client = supabase_client.get_client()
    assert client is not None, "Supabase client should be initialized"

    # Test data
    test_data = {
        'user_id': 'test_user_123',
        'channel_id': 'test_channel_456',
        'message_content': 'Test message',
        'response_content': 'Test response',
        'symbol': 'TEST'
    }

    # Insert test data
    insert_result = supabase_client.insert_data('discord_interactions', test_data)
    assert insert_result is not None, "Data insertion should succeed"

    # Query inserted data
    query_result = supabase_client.execute_query('discord_interactions', {'user_id': 'test_user_123'})
    assert len(query_result) > 0, "Query should return inserted data"
    assert query_result[0]['user_id'] == 'test_user_123', "Inserted data should match query"

def test_supabase_update():
    """
    Test updating data in Supabase.
    """
    client = supabase_client.get_client()
    assert client is not None, "Supabase client should be initialized"

    # Test update
    update_result = supabase_client.update_data(
        'discord_interactions', 
        {'user_id': 'test_user_123'},
        {'response_content': 'Updated test response'}
    )
    assert update_result is not None, "Data update should succeed"

    # Verify update
    query_result = supabase_client.execute_query('discord_interactions', {'user_id': 'test_user_123'})
    assert len(query_result) > 0, "Updated data should be retrievable"
    assert query_result[0]['response_content'] == 'Updated test response', "Data should be updated"
