"""
Tests for stale data detection and warning system.
"""

import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch

from src.core.stale_data_detector import (
    StaleDataDetector,
    StaleDataWarning,
    StaleDataSeverity,
    detect_stale_data,
    adjust_confidence_for_staleness,
    get_stale_data_summary
)


class TestStaleDataDetector:
    """Test the StaleDataDetector class."""
    
    @pytest.fixture
    def detector(self):
        """Create a fresh StaleDataDetector instance."""
        return StaleDataDetector()
    
    @pytest.fixture
    def current_time(self):
        """Create a fixed current time for testing."""
        return datetime(2024, 1, 1, 14, 30, 0, tzinfo=timezone.utc)  # 2:30 PM UTC
    
    def test_detect_stale_data_fresh(self, detector, current_time):
        """Test detection of fresh data."""
        # Data updated 2 minutes ago
        last_update = current_time - timedelta(minutes=2)
        
        warning = detector.detect_stale_data(
            symbol="AAPL",
            last_update=last_update,
            current_time=current_time
        )
        
        assert warning.severity == StaleDataSeverity.NONE
        assert warning.data_age_minutes == 2
        assert warning.confidence_adjustment == 0.0
        assert "fresh and reliable" in warning.warning_message
    
    def test_detect_stale_data_mild(self, detector, current_time):
        """Test detection of mildly stale data."""
        # Data updated 10 minutes ago
        last_update = current_time - timedelta(minutes=10)
        
        warning = detector.detect_stale_data(
            symbol="MSFT",
            last_update=last_update,
            current_time=current_time
        )
        
        assert warning.severity == StaleDataSeverity.MILD
        assert warning.data_age_minutes == 10
        assert warning.confidence_adjustment == 5.0
        assert "slightly stale" in warning.warning_message
    
    def test_detect_stale_data_moderate(self, detector, current_time):
        """Test detection of moderately stale data."""
        # Data updated 45 minutes ago
        last_update = current_time - timedelta(minutes=45)
        
        warning = detector.detect_stale_data(
            symbol="GOOGL",
            last_update=last_update,
            current_time=current_time
        )
        
        assert warning.severity == StaleDataSeverity.MODERATE
        assert warning.data_age_minutes == 45
        assert warning.confidence_adjustment == 15.0
        assert "moderately stale" in warning.warning_message
    
    def test_detect_stale_data_severe(self, detector, current_time):
        """Test detection of severely stale data."""
        # Data updated 3 hours ago
        last_update = current_time - timedelta(hours=3)
        
        warning = detector.detect_stale_data(
            symbol="TSLA",
            last_update=last_update,
            current_time=current_time
        )
        
        assert warning.severity == StaleDataSeverity.SEVERE
        assert warning.data_age_minutes == 180
        assert warning.confidence_adjustment == 30.0
        assert "significantly stale" in warning.warning_message
    
    def test_detect_stale_data_critical(self, detector, current_time):
        """Test detection of critically stale data."""
        # Data updated 2 days ago
        last_update = current_time - timedelta(days=2)
        
        warning = detector.detect_stale_data(
            symbol="NVDA",
            last_update=last_update,
            current_time=current_time
        )
        
        assert warning.severity == StaleDataSeverity.CRITICAL
        assert warning.data_age_minutes == 2880  # 2 days
        assert warning.confidence_adjustment == 50.0
        assert "critically stale" in warning.warning_message
    
    def test_detect_stale_data_timezone_handling(self, detector):
        """Test timezone handling in stale data detection."""
        # Test with naive datetime
        naive_time = datetime(2024, 1, 1, 12, 0)
        current_time = datetime(2024, 1, 1, 12, 30, tzinfo=timezone.utc)
        
        warning = detector.detect_stale_data(
            symbol="TEST",
            last_update=naive_time,
            current_time=current_time
        )
        
        # Should handle naive datetime gracefully
        assert warning.severity == StaleDataSeverity.MILD
        assert warning.data_age_minutes == 30
    
    def test_determine_severity(self, detector):
        """Test severity determination logic."""
        assert detector._determine_severity(2) == StaleDataSeverity.NONE
        assert detector._determine_severity(10) == StaleDataSeverity.MILD
        assert detector._determine_severity(30) == StaleDataSeverity.MODERATE
        assert detector._determine_severity(120) == StaleDataSeverity.SEVERE
        assert detector._determine_severity(2000) == StaleDataSeverity.CRITICAL
    
    def test_generate_recommendations_fresh(self, detector):
        """Test recommendation generation for fresh data."""
        recommendations = detector._generate_recommendations(
            StaleDataSeverity.NONE, 5, "AAPL"
        )
        
        assert len(recommendations) == 1
        assert "proceed with confidence" in recommendations[0]
    
    def test_generate_recommendations_moderate(self, detector):
        """Test recommendation generation for moderately stale data."""
        recommendations = detector._generate_recommendations(
            StaleDataSeverity.MODERATE, 45, "MSFT"
        )
        
        assert len(recommendations) >= 2
        assert any("verify current prices" in rec for rec in recommendations)
        assert any("market conditions" in rec for rec in recommendations)
    
    def test_generate_recommendations_critical(self, detector):
        """Test recommendation generation for critically stale data."""
        recommendations = detector._generate_recommendations(
            StaleDataSeverity.CRITICAL, 2000, "GOOGL"
        )
        
        assert len(recommendations) >= 3
        assert any("not use for real-time trading" in rec for rec in recommendations)
        assert any("alternative data sources" in rec for rec in recommendations)
    
    def test_generate_recommendations_market_hours(self, detector):
        """Test recommendation generation considering market hours."""
        # Test during market hours (2 PM UTC = 9 AM ET)
        with patch('src.core.stale_data_detector.datetime') as mock_datetime:
            mock_datetime.now.return_value = datetime(2024, 1, 1, 14, 0, tzinfo=timezone.utc)
            
            recommendations = detector._generate_recommendations(
                StaleDataSeverity.MODERATE, 30, "TSLA"
            )
            
            assert any("Market is open" in rec for rec in recommendations)
    
    def test_generate_recommendations_off_market_hours(self, detector):
        """Test recommendation generation during off-market hours."""
        # Test during off-market hours (6 AM UTC = 1 AM ET)
        with patch('src.core.stale_data_detector.datetime') as mock_datetime:
            mock_datetime.now.return_value = datetime(2024, 1, 1, 6, 0, tzinfo=timezone.utc)
            
            recommendations = detector._generate_recommendations(
                StaleDataSeverity.MILD, 30, "NVDA"
            )
            
            assert any("Market is closed" in rec for rec in recommendations)


class TestStaleDataWarning:
    """Test the StaleDataWarning dataclass."""
    
    def test_stale_data_warning_creation(self):
        """Test creating StaleDataWarning objects."""
        now = datetime.now(timezone.utc)
        last_update = now - timedelta(minutes=30)
        
        warning = StaleDataWarning(
            symbol="AAPL",
            severity=StaleDataSeverity.MODERATE,
            data_age_minutes=30,
            data_age_hours=0.5,
            last_update=last_update,
            warning_message="Data is moderately stale",
            confidence_adjustment=15.0,
            recommendations=["Verify current prices", "Check market conditions"],
            timestamp=now
        )
        
        assert warning.symbol == "AAPL"
        assert warning.severity == StaleDataSeverity.MODERATE
        assert warning.data_age_minutes == 30
        assert warning.confidence_adjustment == 15.0
        assert len(warning.recommendations) == 2
    
    def test_stale_data_warning_timezone_handling(self):
        """Test timezone handling in StaleDataWarning."""
        # Test with naive datetime
        naive_time = datetime(2024, 1, 1, 12, 0)
        
        warning = StaleDataWarning(
            symbol="TEST",
            severity=StaleDataSeverity.MILD,
            data_age_minutes=10,
            data_age_hours=0.17,
            last_update=naive_time,
            warning_message="Test warning",
            confidence_adjustment=5.0,
            recommendations=[],
            timestamp=naive_time
        )
        
        # Should automatically add UTC timezone
        assert warning.last_update.tzinfo == timezone.utc
        assert warning.timestamp.tzinfo == timezone.utc
    
    def test_stale_data_warning_to_dict(self):
        """Test converting StaleDataWarning to dictionary."""
        now = datetime.now(timezone.utc)
        last_update = now - timedelta(minutes=15)
        
        warning = StaleDataWarning(
            symbol="MSFT",
            severity=StaleDataSeverity.MILD,
            data_age_minutes=15,
            data_age_hours=0.25,
            last_update=last_update,
            warning_message="Data is slightly stale",
            confidence_adjustment=5.0,
            recommendations=["Consider recent changes"],
            timestamp=now
        )
        
        warning_dict = warning.to_dict()
        
        assert warning_dict['symbol'] == "MSFT"
        assert warning_dict['severity'] == "mild"
        assert warning_dict['data_age_minutes'] == 15
        assert warning_dict['confidence_adjustment'] == 5.0
        assert 'last_update' in warning_dict
        assert 'timestamp' in warning_dict


class TestConfidenceAdjustment:
    """Test confidence adjustment functionality."""
    
    def test_adjust_confidence_for_staleness_none(self):
        """Test confidence adjustment for fresh data."""
        detector = StaleDataDetector()
        warning = Mock()
        warning.confidence_adjustment = 0.0
        
        base_confidence = 85.0
        adjusted_confidence = detector.adjust_confidence_for_staleness(
            base_confidence, warning
        )
        
        assert adjusted_confidence == 85.0  # No change
    
    def test_adjust_confidence_for_staleness_mild(self):
        """Test confidence adjustment for mildly stale data."""
        detector = StaleDataDetector()
        warning = Mock()
        warning.confidence_adjustment = 5.0
        
        base_confidence = 85.0
        adjusted_confidence = detector.adjust_confidence_for_staleness(
            base_confidence, warning
        )
        
        assert adjusted_confidence == 80.0  # 85 - 5 = 80
    
    def test_adjust_confidence_for_staleness_severe(self):
        """Test confidence adjustment for severely stale data."""
        detector = StaleDataDetector()
        warning = Mock()
        warning.confidence_adjustment = 30.0
        
        base_confidence = 85.0
        adjusted_confidence = detector.adjust_confidence_for_staleness(
            base_confidence, warning
        )
        
        assert adjusted_confidence == 55.0  # 85 - 30 = 55
    
    def test_adjust_confidence_for_staleness_bounds(self):
        """Test confidence adjustment respects bounds."""
        detector = StaleDataDetector()
        warning = Mock()
        warning.confidence_adjustment = 50.0
        
        # Test with low base confidence
        base_confidence = 30.0
        adjusted_confidence = detector.adjust_confidence_for_staleness(
            base_confidence, warning
        )
        
        assert adjusted_confidence == 0.0  # Should not go below 0
        
        # Test with high base confidence
        base_confidence = 100.0
        adjusted_confidence = detector.adjust_confidence_for_staleness(
            base_confidence, warning
        )
        
        assert adjusted_confidence == 50.0  # 100 - 50 = 50


class TestStaleDataSummary:
    """Test stale data summary functionality."""
    
    def test_get_stale_data_summary_empty(self):
        """Test summary generation with no warnings."""
        detector = StaleDataDetector()
        summary = detector.get_stale_data_summary([])
        
        assert summary['total_warnings'] == 0
        assert summary['severity_distribution'] == {}
        assert summary['average_age_minutes'] == 0
        assert summary['total_confidence_reduction'] == 0.0
    
    def test_get_stale_data_summary_multiple_warnings(self):
        """Test summary generation with multiple warnings."""
        detector = StaleDataDetector()
        
        # Create mock warnings
        warnings = [
            Mock(
                severity=Mock(value='mild'),
                data_age_minutes=10,
                confidence_adjustment=5.0
            ),
            Mock(
                severity=Mock(value='moderate'),
                data_age_minutes=30,
                confidence_adjustment=15.0
            ),
            Mock(
                severity=Mock(value='severe'),
                data_age_minutes=120,
                confidence_adjustment=30.0
            )
        ]
        
        summary = detector.get_stale_data_summary(warnings)
        
        assert summary['total_warnings'] == 3
        assert summary['severity_distribution']['mild'] == 1
        assert summary['severity_distribution']['moderate'] == 1
        assert summary['severity_distribution']['severe'] == 1
        assert summary['average_age_minutes'] == 53  # (10 + 30 + 120) / 3
        assert summary['total_confidence_reduction'] == 50.0  # 5 + 15 + 30


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    def test_detect_stale_data_convenience(self):
        """Test the detect_stale_data convenience function."""
        last_update = datetime.now(timezone.utc) - timedelta(minutes=20)
        
        warning = detect_stale_data("AAPL", last_update)
        
        assert warning.symbol == "AAPL"
        assert warning.severity == StaleDataSeverity.MILD
        assert warning.data_age_minutes == 20
    
    def test_adjust_confidence_for_staleness_convenience(self):
        """Test the adjust_confidence_for_staleness convenience function."""
        warning = Mock()
        warning.confidence_adjustment = 15.0
        
        base_confidence = 80.0
        adjusted_confidence = adjust_confidence_for_staleness(base_confidence, warning)
        
        assert adjusted_confidence == 65.0  # 80 - 15 = 65
    
    def test_get_stale_data_summary_convenience(self):
        """Test the get_stale_data_summary convenience function."""
        warnings = [
            Mock(
                severity=Mock(value='mild'),
                data_age_minutes=15,
                confidence_adjustment=5.0
            )
        ]
        
        summary = get_stale_data_summary(warnings)
        
        assert summary['total_warnings'] == 1
        assert summary['severity_distribution']['mild'] == 1


if __name__ == "__main__":
    pytest.main([__file__]) 