import pytest
import json
import os
import tempfile
from datetime import datetime
from src.core.feedback_mechanism import ResponseFeedbackCollector

class TestResponseFeedbackCollector:
    def setup_method(self):
        """Create a temporary file for testing"""
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        self.temp_file.close()
        self.feedback_collector = ResponseFeedbackCollector(self.temp_file.name)
    
    def teardown_method(self):
        """Clean up temporary file"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_initialization_with_empty_file(self):
        """Test initialization with empty feedback file"""
        feedback_data = self.feedback_collector.feedback_data
        assert feedback_data['total_feedback_entries'] == 0
        assert feedback_data['feedback_by_type'] == {}
        assert feedback_data['average_ratings'] == {}
        assert feedback_data['feedback_trends'] == {}
    
    def test_record_feedback_valid_rating(self):
        """Test recording feedback with valid rating"""
        response = {
            'response_type': 'analysis',
            'response': 'Test response',
            'metadata': {'intent': 'test', 'symbols': ['AAPL']}
        }
        
        feedback_id = self.feedback_collector.record_feedback(
            response=response,
            rating=4,
            user_comment='Good analysis',
            feedback_type='analysis'
        )
        
        assert isinstance(feedback_id, str)
        assert len(feedback_id) > 0
        
        # Verify feedback data was updated
        assert self.feedback_collector.feedback_data['total_feedback_entries'] == 1
        assert 'analysis' in self.feedback_collector.feedback_data['feedback_by_type']
        assert self.feedback_collector.feedback_data['feedback_by_type']['analysis']['total_entries'] == 1
        assert self.feedback_collector.feedback_data['feedback_by_type']['analysis']['total_rating'] == 4
        assert self.feedback_collector.feedback_data['feedback_by_type']['analysis']['average_rating'] == 4.0
    
    def test_record_feedback_invalid_rating(self):
        """Test recording feedback with invalid rating"""
        response = {'response_type': 'analysis'}
        
        with pytest.raises(ValueError, match="Rating must be between 1 and 5"):
            self.feedback_collector.record_feedback(
                response=response,
                rating=0,
                user_comment='Invalid rating'
            )
        
        with pytest.raises(ValueError, match="Rating must be between 1 and 5"):
            self.feedback_collector.record_feedback(
                response=response,
                rating=6,
                user_comment='Invalid rating'
            )
    
    def test_record_feedback_multiple_types(self):
        """Test recording feedback for multiple response types"""
        analysis_response = {'response_type': 'analysis'}
        fallback_response = {'response_type': 'fallback'}
        
        # Record feedback for analysis type
        self.feedback_collector.record_feedback(
            response=analysis_response,
            rating=5,
            user_comment='Excellent analysis'
        )
        
        # Record feedback for fallback type
        self.feedback_collector.record_feedback(
            response=fallback_response,
            rating=3,
            user_comment='Average fallback'
        )
        
        # Verify both types are tracked
        assert self.feedback_collector.feedback_data['total_feedback_entries'] == 2
        assert 'analysis' in self.feedback_collector.feedback_data['feedback_by_type']
        assert 'fallback' in self.feedback_collector.feedback_data['feedback_by_type']
        assert self.feedback_collector.feedback_data['feedback_by_type']['analysis']['total_entries'] == 1
        assert self.feedback_collector.feedback_data['feedback_by_type']['fallback']['total_entries'] == 1
    
    def test_record_feedback_trends_tracking(self):
        """Test that feedback trends are tracked by date"""
        response = {'response_type': 'analysis'}
        
        feedback_id = self.feedback_collector.record_feedback(
            response=response,
            rating=4,
            user_comment='Test comment'
        )
        
        today = datetime.now().strftime('%Y-%m-%d')
        
        assert today in self.feedback_collector.feedback_data['feedback_trends']
        trend_data = self.feedback_collector.feedback_data['feedback_trends'][today]
        
        assert trend_data['total_entries'] == 1
        assert trend_data['average_rating'] == 4.0
        assert trend_data['ratings_distribution'][4] == 1
    
    def test_get_feedback_report(self):
        """Test generating a feedback report"""
        response = {'response_type': 'analysis'}
        
        # Record some feedback
        self.feedback_collector.record_feedback(response=response, rating=5)
        self.feedback_collector.record_feedback(response=response, rating=4)
        
        report = self.feedback_collector.get_feedback_report()
        
        assert report['total_feedback_entries'] == 2
        assert 'analysis' in report['feedback_by_type']
        assert report['feedback_by_type']['analysis']['total_entries'] == 2
        assert report['feedback_by_type']['analysis']['total_rating'] == 9
        assert report['feedback_by_type']['analysis']['average_rating'] == 4.5
        assert 'analysis' in report['average_ratings']
        assert 'feedback_trends' in report
    
    def test_reset_feedback(self):
        """Test resetting all feedback data"""
        response = {'response_type': 'analysis'}
        
        # Record some feedback
        self.feedback_collector.record_feedback(response=response, rating=5)
        
        # Reset feedback
        self.feedback_collector.reset_feedback()
        
        # Verify reset
        assert self.feedback_collector.feedback_data['total_feedback_entries'] == 0
        assert self.feedback_collector.feedback_data['feedback_by_type'] == {}
        assert self.feedback_collector.feedback_data['average_ratings'] == {}
        assert self.feedback_collector.feedback_data['feedback_trends'] == {}
    
    def test_persistence_across_instances(self):
        """Test that feedback data persists across collector instances"""
        response = {'response_type': 'analysis'}
        
        # Record feedback with first instance
        self.feedback_collector.record_feedback(response=response, rating=5)
        
        # Create a new instance pointing to the same file
        new_collector = ResponseFeedbackCollector(self.temp_file.name)
        
        # Verify data was loaded correctly
        assert new_collector.feedback_data['total_feedback_entries'] == 1
        assert 'analysis' in new_collector.feedback_data['feedback_by_type']
    
    def test_feedback_entry_structure(self):
        """Test the structure of individual feedback entries"""
        response = {
            'response_type': 'analysis',
            'response': 'Detailed analysis',
            'metadata': {'intent': 'stock_analysis', 'symbols': ['AAPL']}
        }
        
        feedback_id = self.feedback_collector.record_feedback(
            response=response,
            rating=4,
            user_comment='Helpful analysis'
        )
        
        # The feedback should be tracked in the data structure but individual entries
        # aren't stored separately in the current implementation
        type_data = self.feedback_collector.feedback_data['feedback_by_type']['analysis']
        assert type_data['total_entries'] == 1
        assert type_data['total_rating'] == 4
        assert type_data['average_rating'] == 4.0