"""
Test module for verifying database and Supabase connections.
"""
import pytest
from sqlalchemy import text

@pytest.mark.integration
def test_database_connection(database_connection):
    """Test that we can connect to the database and execute a query."""
    # Execute a simple query
    result = database_connection.execute(text("SELECT 1")).scalar()
    assert result == 1

@pytest.mark.integration
def test_supabase_connection(supabase_credentials):
    """Test that we have valid Supabase credentials."""
    # Check that we have all required credentials
    assert 'supabase_url' in supabase_credentials
    assert 'supabase_key' in supabase_credentials
    assert 'database_url' in supabase_credentials
    
    # Check that the URLs are in the expected format
    assert supabase_credentials['supabase_url'].startswith(('http://', 'https://'))
    assert supabase_credentials['database_url'].startswith(('postgresql://', 'postgresql+asyncpg://'))
    
    # The key should be a non-empty string
    assert isinstance(supabase_credentials['supabase_key'], str)
    assert len(supabase_credentials['supabase_key']) > 20  # Basic length check for API key
