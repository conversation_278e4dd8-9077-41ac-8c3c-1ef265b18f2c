"""
Tests for market calendar system and integration.
"""

import pytest
from datetime import datetime, time, timedelta, date
from unittest.mock import Mock, patch

from src.core.market_calendar import (
    MarketCalendar,
    MarketStatus,
    Exchange,
    MarketHours,
    Holiday,
    market_calendar,
    is_market_open,
    get_market_status,
    get_market_context,
    next_market_open,
    last_market_close
)


class TestMarketCalendar:
    """Test the MarketCalendar class."""
    
    @pytest.fixture
    def calendar(self):
        """Create a fresh MarketCalendar instance."""
        return MarketCalendar()
    
    @pytest.fixture
    def test_datetime(self):
        """Create a test datetime for testing."""
        return datetime(2024, 1, 16, 14, 30, 0)  # 2:30 PM UTC = 9:30 AM ET (Tuesday, not a holiday)
    
    def test_market_hours_creation(self):
        """Test creating MarketHours objects."""
        market_hours = MarketHours(
            exchange=Exchange.NYSE,
            timezone="America/New_York",
            regular_open=time(9, 30),
            regular_close=time(16, 0),
            pre_market_open=time(4, 0),
            after_hours_close=time(20, 0)
        )
        
        assert market_hours.exchange == Exchange.NYSE
        assert market_hours.regular_open == time(9, 30)
        assert market_hours.regular_close == time(16, 0)
        assert market_hours.timezone == "America/New_York"
    
    def test_holiday_creation(self):
        """Test creating Holiday objects."""
        holiday = Holiday(
            name="New Year's Day",
            date=date(2024, 1, 1),
            exchange=Exchange.NYSE,
            is_half_day=False,
            description="Federal holiday"
        )
        
        assert holiday.name == "New Year's Day"
        assert holiday.date == date(2024, 1, 1)
        assert holiday.exchange == Exchange.NYSE
        assert holiday.is_half_day is False
        assert holiday.description == "Federal holiday"
    
    def test_holiday_validation(self):
        """Test holiday validation logic."""
        # Should raise error for half-day without close time
        with pytest.raises(ValueError):
            Holiday(
                name="Test Holiday",
                date=date(2024, 1, 1),
                exchange=Exchange.NYSE,
                is_half_day=True  # Missing half_day_close
            )
    
    def test_market_hours_validation(self):
        """Test market hours validation logic."""
        # Should raise error for invalid half-day close
        with pytest.raises(ValueError):
            MarketHours(
                exchange=Exchange.NYSE,
                timezone="America/New_York",
                regular_open=time(9, 30),
                regular_close=time(16, 0),
                pre_market_open=time(4, 0),
                after_hours_close=time(20, 0),
                half_day_close=time(8, 0)  # Before regular open
            )
    
    def test_is_market_open_regular_hours(self, calendar):
        """Test market open status during regular hours."""
        # Test during regular market hours (2:30 PM UTC = 9:30 AM ET) - Tuesday, not a holiday
        test_time = datetime(2024, 1, 16, 14, 30, 0)  # Tuesday
        
        is_open = calendar.is_market_open(test_time, Exchange.NYSE)
        assert is_open is True
        
        is_open = calendar.is_market_open(test_time, Exchange.NASDAQ)
        assert is_open is True
    
    def test_is_market_open_weekend(self, calendar):
        """Test market open status during weekends."""
        # Test Saturday
        saturday_time = datetime(2024, 1, 13, 14, 30, 0)  # Saturday
        
        is_open = calendar.is_market_open(saturday_time, Exchange.NYSE)
        assert is_open is False
        
        is_open = calendar.is_market_open(saturday_time, Exchange.NASDAQ)
        assert is_open is False
    
    def test_is_market_open_holiday(self, calendar):
        """Test market open status during holidays."""
        # Test New Year's Day 2024
        new_years = datetime(2024, 1, 1, 14, 30, 0)  # New Year's Day
        
        is_open = calendar.is_market_open(new_years, Exchange.NYSE)
        assert is_open is False
        
        is_open = calendar.is_market_open(new_years, Exchange.NASDAQ)
        assert is_open is False
    
    def test_is_market_open_pre_market(self, calendar):
        """Test market open status during pre-market hours."""
        # Test pre-market (9:00 AM UTC = 4:00 AM ET) - Tuesday, not a holiday
        pre_market_time = datetime(2024, 1, 16, 9, 0, 0)  # Tuesday
        
        # Regular hours only
        is_open = calendar.is_market_open(pre_market_time, Exchange.NYSE, include_pre_after_hours=False)
        assert is_open is False
        
        # Include pre/after hours
        is_open = calendar.is_market_open(pre_market_time, Exchange.NYSE, include_pre_after_hours=True)
        assert is_open is True
    
    def test_is_market_open_after_hours(self, calendar):
        """Test market open status during after-hours."""
        # Test after-hours (22:00 UTC = 5:00 PM ET) - Tuesday, not a holiday
        after_hours_time = datetime(2024, 1, 16, 22, 0, 0)  # Tuesday
        
        # Regular hours only
        is_open = calendar.is_market_open(after_hours_time, Exchange.NYSE, include_pre_after_hours=False)
        assert is_open is False
        
        # Include pre/after hours
        is_open = calendar.is_market_open(after_hours_time, Exchange.NYSE, include_pre_after_hours=True)
        assert is_open is True
    
    def test_get_market_status(self, calendar):
        """Test getting detailed market status."""
        # Regular hours - Tuesday, not a holiday
        regular_time = datetime(2024, 1, 16, 14, 30, 0)  # Tuesday 2:30 PM UTC
        status = calendar.get_market_status(regular_time, Exchange.NYSE)
        assert status == MarketStatus.OPEN
        
        # Weekend
        weekend_time = datetime(2024, 1, 13, 14, 30, 0)  # Saturday
        status = calendar.get_market_status(weekend_time, Exchange.NYSE)
        assert status == MarketStatus.WEEKEND
        
        # Holiday
        holiday_time = datetime(2024, 1, 1, 14, 30, 0)  # New Year's Day
        status = calendar.get_market_status(holiday_time, Exchange.NYSE)
        assert status == MarketStatus.HOLIDAY
        
        # Pre-market - Tuesday, not a holiday
        pre_market_time = datetime(2024, 1, 16, 6, 0, 0)  # Tuesday 6:00 AM UTC
        status = calendar.get_market_status(pre_market_time, Exchange.NYSE)
        assert status == MarketStatus.PRE_MARKET
        
        # After-hours - Tuesday, not a holiday
        after_hours_time = datetime(2024, 1, 16, 22, 0, 0)  # Tuesday 10:00 PM UTC
        status = calendar.get_market_status(after_hours_time, Exchange.NYSE)
        assert status == MarketStatus.AFTER_HOURS
    
    def test_next_market_open(self, calendar):
        """Test getting next market open time."""
        # Test from Friday evening
        friday_evening = datetime(2024, 1, 12, 22, 0, 0)  # Friday 10:00 PM UTC
        
        next_open = calendar.next_market_open(friday_evening, Exchange.NYSE)
        assert next_open is not None
        
        # Should be Tuesday morning (Monday was MLK Day holiday)
        tuesday_morning = datetime(2024, 1, 16, 14, 30, 0)  # Tuesday 9:30 AM ET
        assert next_open.date() == tuesday_morning.date()
    
    def test_last_market_close(self, calendar):
        """Test getting last market close time."""
        # Test from Tuesday morning (Monday was MLK Day holiday)
        tuesday_morning = datetime(2024, 1, 16, 6, 0, 0)  # Tuesday 6:00 AM UTC
        
        last_close = calendar.last_market_close(tuesday_morning, Exchange.NYSE)
        assert last_close is not None
        
        # Should be Friday afternoon
        friday_afternoon = datetime(2024, 1, 12, 21, 0, 0)  # Friday 4:00 PM ET
        assert last_close.date() == friday_afternoon.date()
    
    def test_get_market_context(self, calendar):
        """Test getting comprehensive market context."""
        # Regular hours - Tuesday, not a holiday
        regular_time = datetime(2024, 1, 16, 14, 30, 0)  # Tuesday 2:30 PM UTC
        context = calendar.get_market_context(regular_time, Exchange.NYSE)
        
        assert context['status'] == 'open'
        assert context['is_open'] is True
        assert context['exchange'] == 'nyse'
        assert 'market_hours' in context
    
    def test_get_market_context_holiday(self, calendar):
        """Test market context during holidays."""
        # New Year's Day
        holiday_time = datetime(2024, 1, 1, 14, 30, 0)  # New Year's Day
        context = calendar.get_market_context(holiday_time, Exchange.NYSE)
        
        assert context['status'] == 'holiday'
        assert context['is_open'] is False
        assert 'holiday' in context
        assert context['holiday']['name'] == "New Year's Day"
    
    def test_get_market_context_weekend(self, calendar):
        """Test market context during weekends."""
        # Saturday
        weekend_time = datetime(2024, 1, 13, 14, 30, 0)  # Saturday
        context = calendar.get_market_context(weekend_time, Exchange.NYSE)
        
        assert context['status'] == 'weekend'
        assert context['is_open'] is False
        assert 'weekend_info' in context
        assert context['weekend_info']['day_of_week'] == 'Saturday'
    
    def test_get_trading_days(self, calendar):
        """Test getting list of trading days."""
        start_date = date(2024, 1, 1)
        end_date = date(2024, 1, 31)
        
        trading_days = calendar.get_trading_days(start_date, end_date, Exchange.NYSE)
        
        # Should exclude weekends and holidays
        assert len(trading_days) < 31  # Less than total days due to weekends/holidays
        
        # Should not include weekends
        for trading_day in trading_days:
            assert trading_day.weekday() < 5
        
        # Should not include New Year's Day
        assert date(2024, 1, 1) not in trading_days
    
    def test_add_custom_holiday(self, calendar):
        """Test adding custom holidays."""
        custom_holiday = date(2024, 2, 14)  # Valentine's Day (not a federal holiday)
        
        # Initially not a holiday
        assert not calendar._is_holiday(custom_holiday, Exchange.NYSE)
        
        # Add custom holiday
        calendar.add_holiday(
            name="Valentine's Day",
            holiday_date=custom_holiday,
            exchange=Exchange.NYSE,
            description="Custom trading holiday"
        )
        
        # Now should be a holiday
        assert calendar._is_holiday(custom_holiday, Exchange.NYSE)
    
    def test_market_hours_summary(self, calendar):
        """Test getting market hours summary."""
        summary = calendar.get_market_hours_summary(Exchange.NYSE)
        
        assert summary['exchange'] == 'nyse'
        assert summary['timezone'] == 'America/New_York'
        assert 'regular_hours' in summary
        assert 'pre_market' in summary
        assert 'after_hours' in summary
        assert summary['total_trading_hours'] == 6.5
        assert summary['extended_hours'] == 8.0


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    def test_is_market_open_convenience(self):
        """Test the is_market_open convenience function."""
        # Test during regular hours - Tuesday, not a holiday
        regular_time = datetime(2024, 1, 16, 14, 30, 0)  # Tuesday 2:30 PM UTC
        is_open = is_market_open(regular_time, Exchange.NYSE)
        assert is_open is True
        
        # Test during weekend
        weekend_time = datetime(2024, 1, 13, 14, 30, 0)  # Saturday
        is_open = is_market_open(weekend_time, Exchange.NYSE)
        assert is_open is False
    
    def test_get_market_status_convenience(self):
        """Test the get_market_status convenience function."""
        # Test during regular hours - Tuesday, not a holiday
        regular_time = datetime(2024, 1, 16, 14, 30, 0)  # Tuesday 2:30 PM UTC
        status = get_market_status(regular_time, Exchange.NYSE)
        assert status == MarketStatus.OPEN
        
        # Test during weekend
        weekend_time = datetime(2024, 1, 13, 14, 30, 0)  # Saturday
        status = get_market_status(weekend_time, Exchange.NYSE)
        assert status == MarketStatus.WEEKEND
    
    def test_get_market_context_convenience(self):
        """Test the get_market_context convenience function."""
        # Test during regular hours - Tuesday, not a holiday
        regular_time = datetime(2024, 1, 16, 14, 30, 0)  # Tuesday 2:30 PM UTC
        context = get_market_context(regular_time, Exchange.NYSE)
        
        assert context['status'] == 'open'
        assert context['is_open'] is True
        assert 'market_hours' in context
    
    def test_next_market_open_convenience(self):
        """Test the next_market_open convenience function."""
        # Test from Friday evening
        friday_evening = datetime(2024, 1, 12, 22, 0, 0)  # Friday 10:00 PM UTC
        
        next_open = next_market_open(friday_evening, Exchange.NYSE)
        assert next_open is not None
        assert next_open.date() == date(2024, 1, 16)  # Tuesday (Monday was MLK Day holiday)
    
    def test_last_market_close_convenience(self):
        """Test the last_market_close convenience function."""
        # Test from Tuesday morning (Monday was MLK Day holiday)
        tuesday_morning = datetime(2024, 1, 16, 6, 0, 0)  # Tuesday 6:00 AM UTC
        
        last_close = last_market_close(tuesday_morning, Exchange.NYSE)
        assert last_close is not None
        assert last_close.date() == date(2024, 1, 12)  # Friday


class TestMarketCalendarIntegration:
    """Test integration with other systems."""
    
    def test_timezone_handling(self):
        """Test timezone handling in market calendar."""
        # Test with naive datetime
        naive_time = datetime(2024, 1, 15, 14, 30, 0)
        
        # Should handle naive datetime gracefully
        context = get_market_context(naive_time, Exchange.NYSE)
        assert 'status' in context
    
    def test_edge_cases(self):
        """Test edge cases in market calendar."""
        # Test very old date
        old_date = datetime(2020, 1, 1, 14, 30, 0)
        context = get_market_context(old_date, Exchange.NYSE)
        assert 'status' in context
        
        # Test very future date
        future_date = datetime(2030, 1, 1, 14, 30, 0)
        context = get_market_context(future_date, Exchange.NYSE)
        assert 'status' in context
    
    def test_holiday_rollover(self):
        """Test holiday rollover scenarios."""
        # Test Friday close to Tuesday open (Monday was MLK Day holiday)
        friday_close = datetime(2024, 1, 12, 21, 0, 0)  # Friday 4:00 PM ET
        tuesday_open = datetime(2024, 1, 16, 14, 30, 0)  # Tuesday 9:30 AM ET
        
        # Friday should be open
        assert is_market_open(friday_close, Exchange.NYSE)
        
        # Tuesday should be open
        assert is_market_open(tuesday_open, Exchange.NYSE)
        
        # Weekend days should be closed
        saturday = datetime(2024, 1, 13, 14, 30, 0)
        sunday = datetime(2024, 1, 14, 14, 30, 0)
        
        assert not is_market_open(saturday, Exchange.NYSE)
        assert not is_market_open(sunday, Exchange.NYSE)


if __name__ == "__main__":
    pytest.main([__file__])