"""
Tests for Critical Fallback Value Remediation
Ensures that dangerous fallback values are properly validated and replaced.
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from src.core.config_validator import ConfigValidator
from src.core.data_quality_validator import DataQualityValidator, ActionValidator
from src.core.config_manager import TradingStrategyConfig


class TestConfigValidator:
    """Test configuration validation functionality"""
    
    def test_validate_trading_strategy_valid_config(self):
        """Test validation with valid configuration"""
        config = TradingStrategyConfig()
        is_valid, errors = ConfigValidator.validate_trading_strategy(config)
        
        assert is_valid
        assert len(errors) == 0
    
    def test_validate_trading_strategy_invalid_risk(self):
        """Test validation with invalid risk per trade"""
        with patch.dict(os.environ, {'RISK_PER_TRADE': '0.15'}):  # 15% - too high
            config = TradingStrategyConfig()
            is_valid, errors = ConfigValidator.validate_trading_strategy(config)
            
            assert not is_valid
            assert 'risk_per_trade' in errors
    
    def test_validate_environment_variables_missing(self):
        """Test detection of missing environment variables"""
        with patch.dict(os.environ, {}, clear=True):
            all_set, missing = ConfigValidator.validate_environment_variables()
            
            assert not all_set
            assert len(missing) > 0
            assert 'RISK_PER_TRADE' in missing
    
    def test_validate_environment_variables_present(self):
        """Test validation when all environment variables are set"""
        test_env = {
            'RISK_PER_TRADE': '0.02',
            'MAX_POSITION_SIZE': '0.1',
            'STOP_LOSS_MULTIPLIER': '2.0',
            'TAKE_PROFIT_MULTIPLIER': '3.0',
            'MAX_OPEN_POSITIONS': '5',
            'MINIMUM_VOLUME_THRESHOLD': '100000.0',
            'PRICE_CHANGE_THRESHOLD': '0.05'
        }
        
        with patch.dict(os.environ, test_env):
            all_set, missing = ConfigValidator.validate_environment_variables()
            
            assert all_set
            assert len(missing) == 0


class TestDataQualityValidator:
    """Test data quality validation functionality"""
    
    def test_calculate_confidence_high_quality(self):
        """Test confidence calculation with high-quality data"""
        confidence = DataQualityValidator.calculate_confidence(
            data_quality=90,
            completeness=0.95,
            technical_signals={'rsi': {'confirmed': True}}
        )
        
        assert confidence > 70
        assert confidence <= 90
    
    def test_calculate_confidence_low_quality(self):
        """Test confidence calculation with low-quality data"""
        confidence = DataQualityValidator.calculate_confidence(
            data_quality=20,
            completeness=0.3,
            technical_signals={}
        )
        
        assert confidence == 0.0  # Should reject low-quality data
    
    def test_calculate_confidence_medium_quality(self):
        """Test confidence calculation with medium-quality data"""
        confidence = DataQualityValidator.calculate_confidence(
            data_quality=60,
            completeness=0.7,
            technical_signals={'macd': {'confirmed': False}}
        )
        
        assert 0 < confidence < 70
    
    def test_assess_signal_strength_no_signals(self):
        """Test signal strength assessment with no signals"""
        strength = DataQualityValidator._assess_signal_strength({})
        assert strength == 0.5
    
    def test_assess_signal_strength_confirmed_signals(self):
        """Test signal strength assessment with confirmed signals"""
        signals = {
            'rsi': {'confirmed': True},
            'macd': {'confirmed': True},
            'volume': {'confirmed': False}
        }
        
        strength = DataQualityValidator._assess_signal_strength(signals)
        assert strength == 2/3  # 2 out of 3 confirmed
    
    def test_validate_support_resistance_insufficient_data(self):
        """Test S/R validation with insufficient data quality"""
        support, resistance = DataQualityValidator.validate_support_resistance(
            symbol='AAPL',
            timeframe='1D',
            data_quality=50  # Below 70 threshold
        )
        
        assert support is None
        assert resistance is None


class TestActionValidator:
    """Test action validation functionality"""
    
    def test_determine_action_insufficient_data(self):
        """Test action determination with insufficient data quality"""
        action = ActionValidator.determine_action(
            change=5.0,
            technical_signals={'rsi': {'confirmed': True}},
            data_quality=40,  # Below 60 threshold
            volatility=0.02
        )
        
        assert action == 'HOLD'
    
    def test_determine_action_buy_signal(self):
        """Test action determination for buy signal"""
        action = ActionValidator.determine_action(
            change=5.0,
            technical_signals={'rsi': {'confirmed': True}, 'macd': {'confirmed': True}},
            data_quality=80,
            volatility=0.02
        )
        
        assert action == 'BUY'
    
    def test_determine_action_sell_signal(self):
        """Test action determination for sell signal"""
        action = ActionValidator.determine_action(
            change=-5.0,
            technical_signals={'rsi': {'confirmed': True}, 'macd': {'confirmed': True}},
            data_quality=80,
            volatility=0.02
        )
        
        assert action == 'SELL'
    
    def test_determine_action_no_technical_confirmation(self):
        """Test action determination without technical confirmation"""
        action = ActionValidator.determine_action(
            change=5.0,
            technical_signals={'rsi': {'confirmed': False}},
            data_quality=80,
            volatility=0.02
        )
        
        assert action == 'HOLD'
    
    def test_calculate_dynamic_threshold_low_volatility(self):
        """Test dynamic threshold calculation for low volatility"""
        with patch.dict(os.environ, {'ACTION_THRESHOLD_BASE': '2.0'}):
            threshold = ActionValidator._calculate_dynamic_threshold(0.005)  # 0.5%
            assert threshold == 1.0  # 2.0 * 0.5
    
    def test_calculate_dynamic_threshold_high_volatility(self):
        """Test dynamic threshold calculation for high volatility"""
        with patch.dict(os.environ, {'ACTION_THRESHOLD_BASE': '2.0'}):
            threshold = ActionValidator._calculate_dynamic_threshold(0.08)  # 8%
            assert threshold == 4.0  # 2.0 * 2.0


class TestIntegration:
    """Test integration of all validation components"""
    
    def test_config_validation_on_startup(self):
        """Test configuration validation on startup"""
        # This would test the actual startup validation
        # For now, just test that the function exists
        assert hasattr(ConfigValidator, 'validate_environment_variables')
        assert hasattr(DataQualityValidator, 'calculate_confidence')
        assert hasattr(ActionValidator, 'determine_action')
    
    def test_fallback_value_logging(self):
        """Test that fallback value usage is properly logged"""
        with patch('src.core.config_validator.logger') as mock_logger:
            ConfigValidator.log_fallback_usage('test_param', 0.05, 'TEST_ENV_VAR')
            
            mock_logger.warning.assert_called_once()
            call_args = mock_logger.warning.call_args
            assert 'Fallback value used for test_param' in call_args[0][0]


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 