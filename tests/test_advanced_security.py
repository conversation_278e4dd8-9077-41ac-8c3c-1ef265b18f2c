import pytest
import re
from datetime import datetime, timedelta
from fastapi import HTTPException, Request
import pyotp

from src.core.advanced_security import (
    RoleBasedAccessControl, 
    UserRole, 
    MultiFactorAuthenticator, 
    AdaptiveRateLimiter,
    role_required
)
from src.core.security import SecurityConfig

class MockUser:
    def __init__(self, email='<EMAIL>', role=UserRole.VIEWER):
        self.email = email
        self.role = role
        self.mfa_secret = None

class MockRequest:
    def __init__(self, path='/test'):
        self.url = type('MockURL', (), {'path': path})()

class TestSecurityFeatures:
    def test_input_sanitization(self):
        """Test input sanitization functionality"""
        # Test HTML tag removal
        assert SecurityConfig.sanitize_input('<script>alert("XSS")</script>test') == 'test'
        
        # Test special character removal
        assert SecurityConfig.sanitize_input('test@#$%^&*()') == 'test'
        
        # Test max length
        long_input = 'a' * 300
        assert len(SecurityConfig.sanitize_input(long_input)) <= 255
    
    def test_role_based_access_control(self):
        """Test role-based access control hierarchy"""
        # Admin should have highest access
        assert RoleBasedAccessControl.has_permission(UserRole.ADMIN, UserRole.VIEWER) == True
        assert RoleBasedAccessControl.has_permission(UserRole.ADMIN, UserRole.TRADER) == True
        
        # Trader should have intermediate access
        assert RoleBasedAccessControl.has_permission(UserRole.TRADER, UserRole.VIEWER) == True
        assert RoleBasedAccessControl.has_permission(UserRole.TRADER, UserRole.ADMIN) == False
        
        # Viewer should have lowest access
        assert RoleBasedAccessControl.has_permission(UserRole.VIEWER, UserRole.TRADER) == False
        assert RoleBasedAccessControl.has_permission(UserRole.VIEWER, UserRole.ADMIN) == False
    
    def test_multi_factor_authentication(self):
        """Test multi-factor authentication features"""
        user = MockUser()
        mfa = MultiFactorAuthenticator(user)
        
        # Test TOTP challenge generation
        totp_challenge = mfa.generate_mfa_challenge('totp')
        assert 'challenge_type' in totp_challenge
        assert 'qr_code' in totp_challenge
        assert user.mfa_secret is not None
        
        # Test TOTP verification
        totp = pyotp.TOTP(user.mfa_secret)
        current_otp = totp.now()
        assert mfa.verify_mfa_token('totp', current_otp) == True
        
        # Test invalid TOTP
        assert mfa.verify_mfa_token('totp', '000000') == False
        
        # Test email and SMS challenge generation
        email_challenge = mfa.generate_mfa_challenge('email')
        sms_challenge = mfa.generate_mfa_challenge('sms')
        
        assert email_challenge['challenge_type'] == 'email'
        assert sms_challenge['challenge_type'] == 'sms'
    
    def test_adaptive_rate_limiter(self):
        """Test adaptive rate limiting functionality"""
        rate_limiter = AdaptiveRateLimiter(base_limit=5, learning_rate=0.2)
        user_id = 'test_user'
        
        # Initial requests should be allowed
        for _ in range(5):
            assert rate_limiter.is_allowed(user_id, MockRequest()) == True
        
        # Subsequent requests should be blocked
        assert rate_limiter.is_allowed(user_id, MockRequest()) == False
        
        # Wait and check dynamic limit adjustment
        rate_limiter.request_history[user_id] = [
            req for req in rate_limiter.request_history.get(user_id, [])
            if datetime.now() - req['timestamp'] < timedelta(minutes=1)
        ]
        
        # Dynamic limit should be reduced
        assert len(rate_limiter.request_history.get(user_id, [])) < 5
    
    @pytest.mark.asyncio
    async def test_role_required_decorator(self):
        """Test role-required decorator functionality"""
        @role_required(UserRole.TRADER)
        async def protected_function():
            return "Access Granted"
        
        # This should raise an HTTPException due to insufficient permissions
        with pytest.raises(HTTPException) as excinfo:
            await protected_function()
        
        assert excinfo.value.status_code == 403
        assert "Insufficient permissions" in str(excinfo.value.detail)

def test_security_suite():
    """Run all security tests"""
    test_features = TestSecurityFeatures()
    test_features.test_input_sanitization()
    test_features.test_role_based_access_control()
    test_features.test_multi_factor_authentication()
    test_features.test_adaptive_rate_limiter() 