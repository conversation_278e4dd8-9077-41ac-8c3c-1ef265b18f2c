"""
Load Testing for Bot Commands

This module provides load testing for bot commands,
simulating high traffic scenarios and measuring performance.
"""

import asyncio
import time
import statistics
import argparse
import logging
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.bot.client import TradingBot
from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
from src.bot.pipeline.commands.analyze.parallel_pipeline import execute_parallel_analyze_pipeline
from src.core.logger import get_logger, configure_logging

# Configure logging
configure_logging()
logger = get_logger(__name__)

class LoadTestResults:
    """Class to store and analyze load test results"""
    
    def __init__(self, name):
        self.name = name
        self.response_times = []
        self.error_count = 0
        self.success_count = 0
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """Start the test"""
        self.start_time = time.time()
    
    def end(self):
        """End the test"""
        self.end_time = time.time()
    
    def add_result(self, response_time, success):
        """Add a test result"""
        self.response_times.append(response_time)
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
    
    def get_summary(self):
        """Get test summary"""
        if not self.response_times:
            return {
                "name": self.name,
                "total_requests": 0,
                "success_rate": 0,
                "avg_response_time": 0,
                "median_response_time": 0,
                "min_response_time": 0,
                "max_response_time": 0,
                "p90_response_time": 0,
                "p95_response_time": 0,
                "p99_response_time": 0,
                "total_duration": 0,
                "requests_per_second": 0
            }
        
        total_requests = len(self.response_times)
        success_rate = (self.success_count / total_requests) * 100 if total_requests > 0 else 0
        
        # Sort response times for percentile calculations
        sorted_times = sorted(self.response_times)
        
        # Calculate percentiles
        p90_index = int(total_requests * 0.9)
        p95_index = int(total_requests * 0.95)
        p99_index = int(total_requests * 0.99)
        
        p90 = sorted_times[p90_index] if p90_index < total_requests else sorted_times[-1]
        p95 = sorted_times[p95_index] if p95_index < total_requests else sorted_times[-1]
        p99 = sorted_times[p99_index] if p99_index < total_requests else sorted_times[-1]
        
        # Calculate total duration and requests per second
        total_duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        requests_per_second = total_requests / total_duration if total_duration > 0 else 0
        
        return {
            "name": self.name,
            "total_requests": total_requests,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": success_rate,
            "avg_response_time": statistics.mean(self.response_times),
            "median_response_time": statistics.median(self.response_times),
            "min_response_time": min(self.response_times),
            "max_response_time": max(self.response_times),
            "p90_response_time": p90,
            "p95_response_time": p95,
            "p99_response_time": p99,
            "total_duration": total_duration,
            "requests_per_second": requests_per_second
        }
    
    def print_summary(self):
        """Print test summary"""
        summary = self.get_summary()
        
        print(f"\n=== Load Test Results: {self.name} ===")
        print(f"Total Requests: {summary['total_requests']}")
        print(f"Success Rate: {summary['success_rate']:.2f}%")
        print(f"Average Response Time: {summary['avg_response_time']:.3f} seconds")
        print(f"Median Response Time: {summary['median_response_time']:.3f} seconds")
        print(f"Min Response Time: {summary['min_response_time']:.3f} seconds")
        print(f"Max Response Time: {summary['max_response_time']:.3f} seconds")
        print(f"90th Percentile: {summary['p90_response_time']:.3f} seconds")
        print(f"95th Percentile: {summary['p95_response_time']:.3f} seconds")
        print(f"99th Percentile: {summary['p99_response_time']:.3f} seconds")
        print(f"Total Duration: {summary['total_duration']:.3f} seconds")
        print(f"Requests Per Second: {summary['requests_per_second']:.2f}")
        print(f"Success Count: {summary['success_count']}")
        print(f"Error Count: {summary['error_count']}")
        print("=" * 40)


async def create_mock_interaction(user_id):
    """Create a mock Discord interaction"""
    interaction = AsyncMock()
    interaction.user = MagicMock()
    interaction.user.id = str(user_id)
    interaction.user.display_name = f"TestUser{user_id}"
    interaction.user.display_avatar = MagicMock()
    interaction.user.display_avatar.url = f"https://example.com/avatar{user_id}.png"
    interaction.guild_id = "987654321"
    interaction.response = AsyncMock()
    interaction.followup = AsyncMock()
    return interaction


async def create_mock_trading_bot():
    """Create a mock TradingBot instance"""
    bot = MagicMock()
    bot.user = MagicMock()
    bot.user.id = "111222333"
    
    trading_bot = MagicMock(spec=TradingBot)
    trading_bot.bot = bot
    
    # Mock permission checker
    trading_bot.permission_checker = MagicMock()
    trading_bot.permission_checker.has_permission.return_value = (True, "")
    
    # Mock rate limiter
    trading_bot.rate_limiter = MagicMock()
    trading_bot.rate_limiter.can_make_request.return_value = True
    trading_bot.rate_limiter.get_cooldown_time.return_value = 0
    
    # Mock component status
    trading_bot.component_status = {
        'database': True,
        'watchlist_manager': True,
        'watchlist_alert_manager': True,
        'watchlist_realtime': True
    }
    
    # Mock initialization status
    trading_bot.initialization_complete = True
    
    return trading_bot


async def create_mock_pipeline_context():
    """Create a mock pipeline context with results"""
    context = MagicMock()
    context.processing_results = {
        'market_data': {
            'symbol': 'AAPL',
            'current_price': 150.0,
            'change_percent': 1.5,
            'volume': 1000000
        },
        'technical_analysis': {
            'trend': 'uptrend',
            'signal': 'buy',
            'rsi': 65,
            'macd': 0.5,
            'support_levels': [145.0, 140.0, 135.0],
            'resistance_levels': [155.0, 160.0, 165.0]
        },
        'price_targets': {
            'short_term': 155.0,
            'medium_term': 165.0,
            'long_term': 180.0
        },
        'enhanced_analysis': {
            'sentiment': 'bullish',
            'strength': 'moderate',
            'key_levels': [145.0, 155.0]
        },
        'response': 'This is a test response for AAPL analysis.'
    }
    return context


async def run_ask_command_test(trading_bot, user_id, query):
    """Run a single ask command test"""
    start_time = time.time()
    success = True
    
    try:
        interaction = await create_mock_interaction(user_id)
        
        # Mock the execute_ask_pipeline function
        with patch('src.bot.client.execute_ask_pipeline') as mock_execute:
            # Add a small delay to simulate processing time
            await asyncio.sleep(0.05)
            
            # Setup mock return value
            mock_context = MagicMock()
            mock_context.processing_results = {'response': f'This is a test response for query: {query}'}
            mock_execute.return_value = mock_context
            
            # Call the handle_ask_command method
            await trading_bot.handle_ask_command(interaction, query)
            
            # Verify interaction with Discord
            interaction.response.defer.assert_called_once()
            interaction.followup.send.assert_called_once()
    except Exception as e:
        logger.error(f"Error in ask command test: {e}")
        success = False
    
    end_time = time.time()
    return end_time - start_time, success


async def run_analyze_command_test(trading_bot, user_id, symbol):
    """Run a single analyze command test"""
    start_time = time.time()
    success = True
    
    try:
        interaction = await create_mock_interaction(user_id)
        
        # Create a mock cog instance
        from src.bot.commands.analyze_async import AsyncAnalyzeCommands
        cog = AsyncAnalyzeCommands(trading_bot)
        
        # Mock the execute_parallel_analyze_pipeline function
        with patch('src.bot.commands.analyze_async.execute_parallel_analyze_pipeline') as mock_execute:
            # Add a small delay to simulate processing time
            await asyncio.sleep(0.1)
            
            # Setup mock return value
            mock_context = await create_mock_pipeline_context()
            mock_execute.return_value = mock_context
            
            # Call the analyze_command method
            await cog.analyze_command(interaction, symbol, "1d")
            
            # Verify interaction with Discord
            interaction.response.defer.assert_called_once()
            assert interaction.followup.send.call_count >= 1
    except Exception as e:
        logger.error(f"Error in analyze command test: {e}")
        success = False
    
    end_time = time.time()
    return end_time - start_time, success


async def run_recommendations_command_test(trading_bot, user_id, symbol):
    """Run a single recommendations command test"""
    start_time = time.time()
    success = True
    
    try:
        interaction = await create_mock_interaction(user_id)
        
        # Create a mock cog instance
        from src.bot.commands.recommendations_command import EnhancedRecommendationsCommands
        cog = EnhancedRecommendationsCommands(trading_bot)
        
        # Mock the risk profile manager
        cog.risk_profile_manager = MagicMock()
        cog.risk_profile_manager.get_user_profile.return_value = MagicMock(
            user_id=str(user_id),
            risk_level="moderate",
            max_position_size=5.0,
            stop_loss=10.0,
            take_profit=20.0,
            time_horizon="medium-term",
            preferred_assets=["growth", "value"]
        )
        
        # Mock the execute_parallel_analyze_pipeline function
        with patch('src.bot.commands.recommendations_command.execute_parallel_analyze_pipeline') as mock_execute:
            # Add a small delay to simulate processing time
            await asyncio.sleep(0.15)
            
            # Setup mock return value
            mock_context = await create_mock_pipeline_context()
            mock_execute.return_value = mock_context
            
            # Call the recommendations_command method
            await cog.recommendations_command(interaction, symbol, True)
            
            # Verify interaction with Discord
            interaction.response.defer.assert_called_once()
            assert interaction.followup.send.call_count >= 1
    except Exception as e:
        logger.error(f"Error in recommendations command test: {e}")
        success = False
    
    end_time = time.time()
    return end_time - start_time, success


async def run_load_test(test_name, test_func, num_users, requests_per_user, concurrency_limit=10):
    """Run a load test with multiple users and requests"""
    results = LoadTestResults(test_name)
    results.start()
    
    # Create a semaphore to limit concurrency
    semaphore = asyncio.Semaphore(concurrency_limit)
    
    # Create a mock trading bot
    trading_bot = await create_mock_trading_bot()
    
    # Create tasks for all users and requests
    tasks = []
    for user_id in range(1, num_users + 1):
        for request_id in range(1, requests_per_user + 1):
            # Create a task for each request
            task = asyncio.create_task(
                run_request_with_semaphore(
                    semaphore,
                    test_func,
                    trading_bot,
                    user_id,
                    request_id,
                    results
                )
            )
            tasks.append(task)
    
    # Wait for all tasks to complete
    await asyncio.gather(*tasks)
    
    results.end()
    return results


async def run_request_with_semaphore(semaphore, test_func, trading_bot, user_id, request_id, results):
    """Run a request with semaphore to limit concurrency"""
    async with semaphore:
        # Generate test parameters based on test function
        if test_func == run_ask_command_test:
            # Generate a query for ask command
            queries = [
                f"What is the outlook for AAPL?",
                f"Tell me about MSFT stock",
                f"Should I invest in GOOGL?",
                f"What are the support and resistance levels for AMZN?",
                f"Compare TSLA and NVDA"
            ]
            query = queries[request_id % len(queries)]
            response_time, success = await test_func(trading_bot, user_id, query)
        elif test_func == run_analyze_command_test:
            # Generate a symbol for analyze command
            symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "NFLX"]
            symbol = symbols[request_id % len(symbols)]
            response_time, success = await test_func(trading_bot, user_id, symbol)
        elif test_func == run_recommendations_command_test:
            # Generate a symbol for recommendations command
            symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "NFLX"]
            symbol = symbols[request_id % len(symbols)]
            response_time, success = await test_func(trading_bot, user_id, symbol)
        else:
            # Default case
            response_time, success = await test_func(trading_bot, user_id, request_id)
        
        # Add result
        results.add_result(response_time, success)


async def run_all_load_tests(num_users=10, requests_per_user=5, concurrency_limit=10):
    """Run all load tests"""
    # Run ask command load test
    ask_results = await run_load_test(
        "Ask Command",
        run_ask_command_test,
        num_users,
        requests_per_user,
        concurrency_limit
    )
    ask_results.print_summary()
    
    # Run analyze command load test
    analyze_results = await run_load_test(
        "Analyze Command",
        run_analyze_command_test,
        num_users,
        requests_per_user,
        concurrency_limit
    )
    analyze_results.print_summary()
    
    # Run recommendations command load test
    recommendations_results = await run_load_test(
        "Recommendations Command",
        run_recommendations_command_test,
        num_users,
        requests_per_user,
        concurrency_limit
    )
    recommendations_results.print_summary()
    
    return {
        "ask": ask_results.get_summary(),
        "analyze": analyze_results.get_summary(),
        "recommendations": recommendations_results.get_summary()
    }


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Load Testing for Bot Commands")
    parser.add_argument("--users", type=int, default=10, help="Number of users")
    parser.add_argument("--requests", type=int, default=5, help="Requests per user")
    parser.add_argument("--concurrency", type=int, default=10, help="Concurrency limit")
    args = parser.parse_args()
    
    print(f"Running load tests with {args.users} users, {args.requests} requests per user, and concurrency limit {args.concurrency}")
    
    # Run all load tests
    asyncio.run(run_all_load_tests(args.users, args.requests, args.concurrency))


if __name__ == "__main__":
    main()
