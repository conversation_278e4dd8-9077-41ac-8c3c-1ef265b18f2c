"""
Tests for cache warming functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from src.api.data.cache import MarketDataCache, warm_top_symbols_cache, TOP_SYMBOLS
from src.api.data.scheduled_tasks import ScheduledTaskManager


class TestMarketDataCache:
    """Test the MarketDataCache class."""
    
    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client."""
        mock = AsyncMock()
        mock.setex = AsyncMock()
        mock.get = AsyncMock(return_value=None)
        mock.info = AsyncMock(return_value={
            'used_memory': 1000000,
            'used_memory_human': '1MB',
            'keyspace_hits': 100,
            'keyspace_misses': 10
        })
        return mock
    
    @pytest.fixture
    def cache(self, mock_redis):
        """Create cache instance with mocked Redis."""
        cache = MarketDataCache()
        cache._redis_client = mock_redis
        return cache
    
    @pytest.mark.asyncio
    async def test_warm_cache_for_symbols(self, cache, mock_redis):
        """Test warming cache for multiple symbols."""
        # Mock the historical data fetch
        mock_data = [{'symbol': 'AAPL', 'price': 150.0, 'timestamp': '2024-01-01'}]
        
        with patch.object(cache, '_fetch_historical_data', return_value=mock_data):
            results = await cache.warm_cache_for_symbols(['AAPL', 'MSFT'], days=30)
            
            # Verify results
            assert len(results) == 2
            assert results['AAPL'] is True
            assert results['MSFT'] is True
            
            # Verify Redis calls
            assert mock_redis.setex.call_count >= 2  # At least 2 symbols
            
    @pytest.mark.asyncio
    async def test_warm_single_symbol_already_cached(self, cache, mock_redis):
        """Test warming cache for a symbol that's already cached."""
        # Mock existing cache data
        mock_redis.get.return_value = '{"existing": "data"}'
        
        with patch.object(cache, '_fetch_historical_data', return_value=[]):
            result = await cache._warm_single_symbol(mock_redis, 'AAPL', 30, 86400)
            
            # Should return True (already cached)
            assert result is True
            
            # Should not fetch new data
            mock_redis.setex.assert_not_called()
            
    @pytest.mark.asyncio
    async def test_warm_single_symbol_no_data(self, cache, mock_redis):
        """Test warming cache when no historical data is available."""
        with patch.object(cache, '_fetch_historical_data', return_value=[]):
            result = await cache._warm_single_symbol(mock_redis, 'INVALID', 30, 86400)
            
            # Should return False (no data available)
            assert result is False
            
    @pytest.mark.asyncio
    async def test_cache_stats(self, cache, mock_redis):
        """Test getting cache statistics."""
        stats = await cache.get_cache_stats()
        
        assert 'hit_rate' in stats
        assert stats['hit_rate'] == 90.91  # 100 hits / 110 total * 100
        assert stats['used_memory'] == 1000000


class TestScheduledTaskManager:
    """Test the ScheduledTaskManager class."""
    
    @pytest.fixture
    def task_manager(self):
        """Create task manager instance."""
        return ScheduledTaskManager()
    
    @pytest.fixture
    def mock_cache(self):
        """Mock cache instance."""
        mock = Mock()
        mock.get_cache_stats = AsyncMock(return_value={'hit_rate': 85.0})
        return mock
    
    def test_should_run_cache_warming_before_market_open(self, task_manager):
        """Test cache warming timing before market open."""
        # 9:00 AM ET (30 minutes before market open)
        current_time = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        
        # Mock timezone to US/Eastern
        with patch('src.api.data.scheduled_tasks.datetime') as mock_datetime:
            mock_datetime.now.return_value = current_time
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            
            should_run = task_manager._should_run_cache_warming(current_time)
            assert should_run is True
    
    def test_should_run_cache_warming_after_market_open(self, task_manager):
        """Test cache warming timing after market open."""
        # 10:00 AM ET (after market open)
        current_time = datetime.now().replace(hour=10, minute=0, second=0, microsecond=0)
        
        should_run = task_manager._should_run_cache_warming(current_time)
        assert should_run is False
    
    @pytest.mark.asyncio
    async def test_run_cache_warming(self, task_manager, mock_cache):
        """Test running the cache warming task."""
        task_manager.cache = mock_cache
        
        # Mock the cache warming execution
        with patch.object(task_manager, '_execute_cache_warming') as mock_execute:
            await task_manager._run_cache_warming()
            
            # Verify execution was called
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_task_status(self, task_manager, mock_cache):
        """Test getting task status."""
        task_manager.cache = mock_cache
        
        status = await task_manager.get_task_status()
        
        assert 'is_running' in status
        assert 'active_tasks' in status
        assert 'cache_stats' in status
        assert status['cache_stats']['hit_rate'] == 85.0


class TestTopSymbols:
    """Test the top symbols configuration."""
    
    def test_top_symbols_list(self):
        """Test that top symbols list contains expected symbols."""
        assert 'AAPL' in TOP_SYMBOLS
        assert 'MSFT' in TOP_SYMBOLS
        assert 'GOOGL' in TOP_SYMBOLS
        assert len(TOP_SYMBOLS) == 50
        
    def test_top_symbols_format(self):
        """Test that all symbols are in uppercase format."""
        for symbol in TOP_SYMBOLS:
            assert symbol.isupper()
            assert symbol.isalpha() or '.' in symbol  # Allow for symbols like BRK.B


@pytest.mark.asyncio
async def test_warm_top_symbols_cache():
    """Test the convenience function for warming top symbols cache."""
    mock_cache = Mock()
    mock_cache.warm_cache_for_symbols = AsyncMock(return_value={
        'AAPL': True, 'MSFT': True, 'GOOGL': False
    })
    
    results = await warm_top_symbols_cache(mock_cache)
    
    # Verify the function was called with correct parameters
    mock_cache.warm_cache_for_symbols.assert_called_once_with(
        symbols=TOP_SYMBOLS,
        days=30,
        ttl=86400,
        batch_size=10
    )
    
    # Verify results
    assert results['AAPL'] is True
    assert results['MSFT'] is True
    assert results['GOOGL'] is False


if __name__ == "__main__":
    pytest.main([__file__]) 