tests/test_ai_chat_processor.py::test_ai_chat_processor_data_fetch_failure
tests/test_ai_chat_processor.py::test_processor_function_no_query
tests/test_correlation_integration.py::TestCorrelationIDIntegration::test_correlation_id_format_and_uniqueness
tests/test_correlation_wrappers.py::TestDatabaseCorrelationWrappers::test_transaction_with_correlation
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_success
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_empty_query
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_error
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_timeout
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_no_ai_config
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_multiple_symbols
tests/test_execute_ask_pipeline.py::test_execute_ask_pipeline_no_data_needed
tests/test_multi_symbol_integration.py::test_multi_symbol_market_overview
tests/test_multi_symbol_integration.py::test_tech_sector_analysis
tests/test_multi_symbol_integration.py::test_response_contains_required_elements
tests/test_multi_symbol_integration.py::test_discord_compatible_response_length
tests/test_supabase_connection.py::test_supabase_client_initialization
tests/test_supabase_connection.py::test_supabase_connection_status
tests/test_supabase_connection.py::test_supabase_insert_and_query
tests/test_supabase_connection.py::test_supabase_update
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_get_historical_data
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_invalid_symbol
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[1]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[7]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[30]
tests/integration/test_alpha_vantage_provider.py::TestAlphaVantageProvider::test_historical_data_with_different_periods[90]
tests/integration/test_market_data_service.py::TestMarketDataService::test_invalid_symbol
tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[1]
tests/integration/test_market_data_service.py::TestMarketDataService::test_historical_data_with_different_periods[7]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_get_current_price
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_get_historical_data
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[1]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[7]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[30]
tests/integration/test_polygon_provider.py::TestPolygonProvider::test_historical_data_with_different_periods[90]
tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_client_initialization
tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_connection_status
tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_crud_operations
tests/integration/test_supabase_integration.py::TestSupabaseIntegration::test_supabase_error_handling 