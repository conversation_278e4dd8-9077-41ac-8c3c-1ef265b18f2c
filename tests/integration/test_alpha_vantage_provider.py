import os
import pytest
import asyncio
import json
from datetime import datetime, timed<PERSON><PERSON>
from unittest.mock import patch, AsyncMock, MagicMock

from src.api.data.providers.base import MarketDataResponse
from src.core.exceptions import MarketDataError
from src.shared.data_providers.alpha_vantage import AlphaVantageProvider

# Mock response data for Alpha Vantage API
MOCK_TICKER_DATA = {
    "Global Quote": {
        "01. symbol": "AAPL",
        "02. open": "150.0",
        "03. high": "155.0",
        "04. low": "149.0",
        "05. price": "152.0",
        "06. volume": "10000000",
        "07. latest trading day": "2025-09-08",
        "08. previous close": "151.0",
        "09. change": "1.0",
        "10. change percent": "0.66%"
    }
}

MOCK_HISTORICAL_DATA = {
    "Meta Data": {
        "1. Information": "Daily Prices (open, high, low, close) and Volumes",
        "2. Symbol": "MSFT",
        "3. Last Refreshed": "2025-09-08",
        "4. Output Size": "Compact",
        "5. Time Zone": "US/Eastern"
    },
    "Time Series (Daily)": {
        "2025-09-08": {
            "1. open": "280.0",
            "2. high": "285.0",
            "3. low": "278.0",
            "4. close": "282.0",
            "5. volume": "15000000"
        },
        "2025-09-07": {
            "1. open": "279.0",
            "2. high": "283.0",
            "3. low": "277.0",
            "4. close": "280.0",
            "5. volume": "14000000"
        },
        "2025-09-06": {
            "1. open": "278.0",
            "2. high": "282.0",
            "3. low": "276.0",
            "4. close": "279.0",
            "5. volume": "13000000"
        }
    }
}

@pytest.mark.asyncio
class TestAlphaVantageProvider:
    @pytest.fixture
    def alpha_vantage_provider(self):
        """
        Create a mocked Alpha Vantage provider instance for testing.
        """
        provider = AlphaVantageProvider()
        provider._session = MagicMock()
        return provider
        
    @pytest.fixture
    def mock_response(self):
        """
        Create a mock response object for API calls.
        """
        mock_resp = MagicMock()
        mock_resp.status = 200
        mock_resp.json = AsyncMock()
        return mock_resp
    
    async def test_get_ticker_data(self, alpha_vantage_provider):
        """
        Test retrieving current ticker data for a stock symbol.
        Tests response quality and data integrity.
        """
        symbol = 'AAPL'  # Apple Inc.
        
        try:
            ticker_data = await alpha_vantage_provider.get_ticker(symbol)
            
            # Validate response quality - no errors
            assert "error" not in ticker_data, f"Should not have errors: {ticker_data}"
            
            # Validate data structure and quality
            assert isinstance(ticker_data, dict), "Should return dictionary"
            assert ticker_data.get('symbol') == symbol, "Symbol should match requested symbol"
            assert ticker_data.get('current_price', 0) > 0, "Price should be a positive number"
            assert ticker_data.get('timestamp') is not None, "Timestamp should be set"
            assert ticker_data.get('source') == 'alpha_vantage', "Source should be 'alpha_vantage'"
            
            # Test data quality - all required fields present
            required_fields = ['symbol', 'current_price', 'timestamp', 'source', 'volume', 'open', 'high', 'low', 'close']
            for field in required_fields:
                assert field in ticker_data, f"Missing required field: {field}"
            
            # Test data consistency - close price should match current_price
            assert ticker_data['close'] == ticker_data['current_price'], "Close price should match current price"
            
            # Test data validity - high should be >= low
            assert ticker_data['high'] >= ticker_data['low'], "High price should be >= low price"
            
            print(f"✅ Real Alpha Vantage data quality check passed for {symbol}")
            print(f"   Price: ${ticker_data['current_price']:.2f}")
            print(f"   Volume: {ticker_data['volume']:,}")
            print(f"   Range: ${ticker_data['low']:.2f} - ${ticker_data['high']:.2f}")
            
        except Exception as e:
            pytest.fail(f"Ticker data retrieval failed: {e}")
    
    async def test_get_historical_data(self, alpha_vantage_provider):
        """
        Test retrieving historical stock data.
        Tests data quality and consistency.
        """
        symbol = 'MSFT'  # Microsoft Corporation
        
        try:
            historical_data = await alpha_vantage_provider.get_history(symbol, period="1mo", interval="1d")
            
            # Validate response quality - no errors
            assert "error" not in historical_data, f"Should not have errors: {historical_data}"
            
            # Validate data structure
            assert isinstance(historical_data, dict), "Should return dictionary"
            assert historical_data.get('symbol') == symbol, "Symbol should match requested symbol"
            
            # Test data quality - check if we have meaningful historical data
            if 'data' in historical_data and historical_data['data']:
                data_points = historical_data['data']
                assert len(data_points) > 0, "Should return non-empty historical data"
                
                # Validate data consistency across time series
                for i, point in enumerate(data_points):
                    assert 'date' in point, f"Data point {i} missing date"
                    assert 'close' in point, f"Data point {i} missing close price"
                    assert point['close'] > 0, f"Data point {i} has invalid close price"
                
                print(f"✅ Real Alpha Vantage historical data quality check passed for {symbol}")
                print(f"   Data points: {len(data_points)}")
                print(f"   Date range: {data_points[0]['date']} to {data_points[-1]['date']}")
            else:
                print(f"⚠️ No historical data available for {symbol} - this may be normal for some symbols")
        
        except Exception as e:
            pytest.fail(f"Historical market data retrieval failed: {e}")
    
    async def test_invalid_symbol(self, alpha_vantage_provider):
        """
        Test handling of invalid stock symbol.
        """
        invalid_symbols = ['', '123', 'INVALID_SYMBOL_TOO_LONG']
        
        for symbol in invalid_symbols:
            with pytest.raises(
                (MarketDataError, ValueError), 
                match="Invalid symbol|Failed to get"
            ):
                await alpha_vantage_provider.get_current_price(symbol)
    
    @pytest.mark.parametrize("days", [1, 7, 30, 90])
    async def test_historical_data_with_different_periods(
        self, 
        alpha_vantage_provider, 
        days
    ):
        """
        Test historical data retrieval with different time periods.
        """
        symbol = 'GOOGL'  # Alphabet Inc.
        
        try:
            historical_data = await alpha_vantage_provider.get_historical_data(
                symbol, days=days
            )
            
            assert len(historical_data) > 0, f"Should return data for {days} days"
            assert len(historical_data) <= days, f"Should not return more than {days} data points"
        
        except MarketDataError as e:
            pytest.fail(f"Historical market data retrieval failed for {days} days: {e}") 