"""
Real-world test: QQQ $573 Call Options Price Estimation

Tests our Greeks calculator against actual market data to see how well
we can estimate option prices after price movements.
"""

import pytest
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import asyncio
import time # Added for time.time()

from src.shared.technical_analysis.options_greeks_calculator import (
    OptionsGreeksCalculator,
    GreeksSnapshot,
    OptionType
)
from src.api.data.providers.finnhub import Finn<PERSON>bProvider

def get_qqq_current_data_finnhub():
    """Get QQQ current data from Finnhub with better error handling"""
    try:
        print("📊 Getting QQQ underlying data from Finnhub...")
        
        # Initialize Finnhub provider
        finnhub_provider = FinnhubProvider()
        
        # Get current price
        current_data = asyncio.run(finnhub_provider.get_stock_data("QQQ"))
        
        if current_data and 'current_price' in current_data:
            current_price = current_data['current_price']
            
            # Get historical data for the last hour (15-minute intervals)
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            historical_data = asyncio.run(
                finnhub_provider.get_historical_data(
                    "QQQ",
                    start_time,
                    end_time,
                    "15"
                )
            )
            
            if historical_data and 'close' in historical_data:
                # Calculate 15-minute price change
                closes = historical_data['close']
                if len(closes) >= 2:
                    price_change_15m = closes[-1] - closes[-2]
                    price_change_pct = (price_change_15m / closes[-2]) * 100
                    
                    print(f"✅ QQQ Current Price: ${current_price:.2f}")
                    print(f"📈 15-min Change: ${price_change_15m:.2f} ({price_change_pct:.2f}%)")
                    
                    return {
                        'current_price': current_price,
                        'price_change_15m': price_change_15m,
                        'price_change_pct': price_change_pct,
                        'historical_closes': closes[-10:],  # Last 10 data points
                        'source': 'finnhub'
                    }
            
            print(f"⚠️ Got current price but no historical data")
            return {
                'current_price': current_price,
                'price_change_15m': 0,
                'price_change_pct': 0,
                'historical_closes': [current_price],
                'source': 'finnhub_current_only'
            }
        
        print("❌ No current price data from Finnhub")
        return None
        
    except Exception as e:
        print(f"❌ Error getting QQQ data from Finnhub: {e}")
        return None

def get_qqq_current_data():
    """Get QQQ data from Yahoo Finance as fallback"""
    try:
        print("📊 Getting QQQ data from Yahoo Finance...")
        
        # Get QQQ ticker
        qqq = yf.Ticker("QQQ")
        
        # Get current info
        info = qqq.info
        current_price = info.get('regularMarketPrice', 0)
        
        if current_price == 0:
            # Try to get from history
            hist = qqq.history(period="1d", interval="1m")
            if not hist.empty:
                current_price = hist['Close'].iloc[-1]
        
        if current_price > 0:
            # Get recent history for price change calculation
            hist_15m = qqq.history(period="1h", interval="15m")
            
            if not hist_15m.empty and len(hist_15m) >= 2:
                price_change_15m = hist_15m['Close'].iloc[-1] - hist_15m['Close'].iloc[-2]
                price_change_pct = (price_change_15m / hist_15m['Close'].iloc[-2]) * 100
                
                print(f"✅ QQQ Current Price: ${current_price:.2f}")
                print(f"📈 15-min Change: ${price_change_15m:.2f} ({price_change_pct:.2f}%)")
                
                return {
                    'current_price': current_price,
                    'price_change_15m': price_change_15m,
                    'price_change_pct': price_change_pct,
                    'historical_closes': hist_15m['Close'].tail(10).tolist(),
                    'source': 'yfinance'
                }
            
            print(f"✅ QQQ Current Price: ${current_price:.2f} (no historical data)")
            return {
                'current_price': current_price,
                'price_change_15m': 0,
                'price_change_pct': 0,
                'historical_closes': [current_price],
                'source': 'yfinance_current_only'
            }
        
        print("❌ No current price data from Yahoo Finance")
        return None
        
    except Exception as e:
        print(f"❌ Error getting QQQ data from Yahoo Finance: {e}")
        return None

def get_synthetic_qqq_data():
    """Generate synthetic QQQ data for testing when APIs fail"""
    print("🔧 Generating synthetic QQQ data for testing...")
    
    # Use realistic QQQ values
    base_price = 573.50  # Around the $573 strike we're testing
    price_change_15m = 2.75  # Realistic 15-minute move
    price_change_pct = (price_change_15m / base_price) * 100
    
    print(f"✅ Synthetic QQQ Current Price: ${base_price:.2f}")
    print(f"📈 Synthetic 15-min Change: ${price_change_15m:.2f} ({price_change_pct:.2f}%)")
    
    return {
        'current_price': base_price,
        'price_change_15m': price_change_15m,
        'price_change_pct': price_change_pct,
        'historical_closes': [base_price - price_change_15m, base_price],
        'source': 'synthetic'
    }

def get_qqq_573_call_data():
    """Get QQQ $573 call option data from Yahoo Finance"""
    try:
        print("📊 Getting QQQ $573 call option data...")
        
        # Get QQQ options chain
        qqq = yf.Ticker("QQQ")
        
        # Get options expiration dates
        expirations = qqq.options
        
        if not expirations:
            print("❌ No options expiration dates found")
            return None
        
        # Use the first available expiration (usually closest)
        expiration_date = expirations[0]
        print(f"📅 Using expiration: {expiration_date}")
        
        # Get options chain for this expiration
        options = qqq.option_chain(expiration_date)
        calls = options.calls
        
        # Find the $573 call
        target_strike = 573.0
        target_call = calls[calls['strike'] == target_strike]
        
        if target_call.empty:
            print(f"❌ No {target_strike} call found, looking for closest...")
            # Find closest strike
            closest_strike = calls.iloc[(calls['strike'] - target_strike).abs().argsort()[:1]]
            if not closest_strike.empty:
                actual_strike = closest_strike.iloc[0]['strike']
                print(f"📊 Using closest strike: ${actual_strike}")
                target_call = closest_strike
            else:
                print("❌ No calls found at all")
                return None
        
        if not target_call.empty:
            call_data = target_call.iloc[0]
            
            # Extract relevant data
            option_data = {
                'strike': call_data['strike'],
                'last_price': call_data['lastPrice'],
                'bid': call_data['bid'],
                'ask': call_data['ask'],
                'volume': call_data['volume'],
                'open_interest': call_data['openInterest'],
                'implied_volatility': call_data['impliedVolatility'],
                'expiration': expiration_date,
                'underlying_price': call_data['underlyingPrice']
            }
            
            print(f"✅ Found {option_data['strike']} call:")
            print(f"   💰 Last Price: ${option_data['last_price']:.2f}")
            print(f"   📊 IV: {option_data['implied_volatility']:.3f}")
            print(f"   📅 Expiration: {option_data['expiration']}")
            print(f"   📈 Underlying: ${option_data['underlying_price']:.2f}")
            
            return option_data
        
        print("❌ No suitable call option found")
        return None
        
    except Exception as e:
        print(f"❌ Error getting options data: {e}")
        return None

def estimate_option_price_with_greeks():
    """Estimate QQQ call option price using Greeks and current market data"""
    print("🚀 Testing QQQ $573 Call Options Price Estimation")
    print("=" * 60)
    
    # Get QQQ underlying data (try Finnhub first, then Yahoo, then synthetic)
    qqq_data = get_qqq_current_data_finnhub()
    
    if not qqq_data:
        qqq_data = get_qqq_current_data()
    
    if not qqq_data:
        print("⚠️ Both APIs failed, using synthetic data")
        qqq_data = get_synthetic_qqq_data()
    
    if not qqq_data:
        print("❌ Failed to get any QQQ data")
        return None
    
    # Get options data
    options_data = get_qqq_573_call_data()
    
    if not options_data:
        print("❌ Failed to get options data")
        return None
    
    # Calculate time to expiration
    expiration_date = datetime.strptime(options_data['expiration'], '%Y-%m-%d')
    current_date = datetime.now()
    time_to_expiry = (expiration_date - current_date).days / 365.0
    
    if time_to_expiry <= 0:
        print("❌ Option has expired")
        return None
    
    print(f"\n📊 Calculating Greeks for {options_data['strike']} call...")
    print(f"⏰ Time to expiry: {time_to_expiry:.3f} years")
    
    # Estimate Greeks based on option characteristics
    # These are rough estimates - in real trading you'd get these from your broker
    current_price = qqq_data['current_price']
    strike = options_data['strike']
    
    # Rough Greeks estimation based on option characteristics
    # Delta: Higher for ITM options, lower for OTM
    if current_price > strike:
        delta = 0.7  # ITM call
    elif current_price < strike:
        delta = 0.3  # OTM call
    else:
        delta = 0.5  # ATM call
    
    # Gamma: Higher for ATM options
    gamma = 0.02 if abs(current_price - strike) < 5 else 0.01
    
    # Theta: Time decay, higher for shorter time
    theta = -0.05 if time_to_expiry < 0.1 else -0.02
    
    # Vega: Volatility sensitivity
    vega = 0.15
    
    # Rho: Interest rate sensitivity (less important)
    rho = 0.02
    
    estimated_greeks = {
        'delta': delta,
        'gamma': gamma,
        'theta': theta,
        'vega': vega,
        'rho': rho
    }
    
    print(f"📊 Estimated Greeks:")
    print(f"   Δ Delta: {delta:.3f}")
    print(f"   Γ Gamma: {gamma:.3f}")
    print(f"   Θ Theta: {theta:.3f}")
    print(f"   ν Vega: {vega:.3f}")
    print(f"   ρ Rho: {rho:.3f}")
    
    # Create Greeks snapshot
    greeks_snapshot = GreeksSnapshot(
        delta=delta,
        gamma=gamma,
        theta=theta,
        vega=vega,
        rho=rho,
        price=options_data['last_price'],
        underlying_price=current_price,
        strike=strike,
        time_to_expiry=time_to_expiry,
        implied_volatility=options_data['implied_volatility'],
        risk_free_rate=0.05  # Assume 5% risk-free rate
    )
    
    # Initialize calculator
    calculator = OptionsGreeksCalculator()
    
    # Estimate new price based on 15-minute price change
    price_change = qqq_data['price_change_15m']
    time_elapsed = 15 / (24 * 60 * 365)  # 15 minutes in years
    
    print(f"\n🔄 Estimating new option price after 15-minute move...")
    print(f"📈 Underlying price change: ${price_change:.2f}")
    print(f"⏱️ Time elapsed: {time_elapsed:.6f} years")
    
    # Estimate future Greeks and price
    future_greeks = calculator.estimate_future_greeks(
        greeks_snapshot,
        underlying_price_change=price_change,
        time_elapsed=time_elapsed,
        volatility_change=0.0  # Assume no volatility change
    )
    
    print(f"\n📊 New Estimated Greeks:")
    print(f"   Δ Delta: {future_greeks['delta']:.3f}")
    print(f"   Γ Gamma: {future_greeks['gamma']:.3f}")
    print(f"   Θ Theta: {future_greeks['theta']:.3f}")
    print(f"   ν Vega: {future_greeks['vega']:.3f}")
    print(f"   ρ Rho: {future_greeks['rho']:.3f}")
    
    # Calculate accuracy
    actual_price = options_data['last_price']
    estimated_price = future_greeks['price']
    accuracy = (1 - abs(estimated_price - actual_price) / actual_price) * 100
    
    print(f"\n🎯 Price Estimation Results:")
    print(f"   💰 Actual Price: ${actual_price:.2f}")
    print(f"   🔮 Estimated Price: ${estimated_price:.2f}")
    print(f"   📊 Accuracy: {accuracy:.1f}%")
    
    # Breakeven analysis
    breakeven_data = calculator.calculate_breakeven_points(greeks_snapshot)
    
    print(f"\n💰 Breakeven Analysis:")
    print(f"   📈 Breakeven Price: ${breakeven_data['breakeven_price']:.2f}")
    print(f"   💵 Max Profit: ${breakeven_data['max_profit']:.2f}")
    print(f"   💸 Max Loss: ${breakeven_data['max_loss']:.2f}")
    
    # Probability of profit
    prob_profit = calculator.estimate_probability_of_profit(greeks_snapshot)
    print(f"   🎲 Probability of Profit: {prob_profit:.1f}%")
    
    return {
        'accuracy': accuracy,
        'actual_price': actual_price,
        'estimated_price': estimated_price,
        'estimated_greeks': future_greeks,
        'breakeven': breakeven_data,
        'probability_of_profit': prob_profit,
        'underlying_data': qqq_data,
        'options_data': options_data
    }

def test_qqq_options_estimation():
    """Pytest test function"""
    result = estimate_option_price_with_greeks()
    
    if result:
        # Assertions for testing
        assert result['accuracy'] > 0, "Accuracy should be positive"
        assert result['estimated_greeks']['price'] > 0, "Estimated price should be positive"
        assert 'breakeven' in result, "Breakeven analysis should be available"
        
        print(f"\n✅ Test completed successfully!")
        print(f"✅ Final Accuracy: {result['accuracy']:.1f}%")
    else:
        pytest.fail("Options estimation test failed") 