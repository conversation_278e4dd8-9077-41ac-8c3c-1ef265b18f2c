import pytest
import pandas as pd
import numpy as np
import asyncio

from src.shared.technical_analysis.indicators import calculate_supertrend
from src.shared.technical_analysis.strategy_calculator import PositionStrategyCalculator
from src.api.data.providers.finnhub import <PERSON><PERSON>b<PERSON><PERSON><PERSON>

def get_test_data(ticker='QQQ', start_date='2023-01-01'):
    """
    Retrieve stock data using existing Finnhub provider
    """
    try:
        # Use existing Finnhub provider
        provider = FinnhubProvider()
        
        # Convert start_date to datetime
        from datetime import datetime
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        
        # Get historical data
        async def fetch_data():
            return await provider.get_historical_data(
                symbol=ticker,
                start_date=start_dt
            )
        
        # Run async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            historical_data = loop.run_until_complete(fetch_data())
            
            # Convert to DataFrame format
            df = pd.DataFrame({
                'Close': historical_data.closes,
                'High': historical_data.highs,
                'Low': historical_data.lows
            }, index=historical_data.dates)
            
            return df
        finally:
            loop.close()
        
    except Exception as e:
        # Fallback to synthetic data if provider fails
        print(f"Warning: Could not fetch live data from Finnhub. Using synthetic data. Error: {e}")
        dates = pd.date_range(start=start_date, periods=100)
        return pd.DataFrame({
            'Close': np.linspace(100, 150, 100),
            'High': np.linspace(101, 155, 100),
            'Low': np.linspace(99, 145, 100)
        }, index=dates)

def test_supertrend_qqq_analysis():
    """
    Integration test for Supertrend analysis using Finnhub data
    """
    # Get test data
    df = get_test_data()

    # Ensure we have sufficient data
    assert len(df) > 0, "No data available for analysis"

    # Calculate Supertrend
    supertrend = calculate_supertrend(
        high=df['High'], 
        low=df['Low'], 
        close=df['Close']
    )

    # Validate Supertrend results
    assert 'trend' in supertrend, "Trend not calculated"
    assert supertrend['trend'] in ['up', 'down'], f"Invalid trend: {supertrend['trend']}"
    
    assert 'value' in supertrend, "Supertrend value missing"
    assert 'direction' in supertrend, "Trend direction missing"
    assert supertrend['direction'] in [1, -1], f"Invalid direction: {supertrend['direction']}"

def test_supertrend_strategy_integration():
    """
    Test Supertrend integration with strategy calculator using Finnhub data
    """
    # Get test data
    df = get_test_data()

    # Initialize strategy calculator
    strategy = PositionStrategyCalculator()

    # Calculate entry strategy
    entry_strategy = strategy.calculate_entry_strategy(
        high=df['High'], 
        low=df['Low'], 
        close=df['Close']
    )

    # Validate entry strategy
    assert 'direction' in entry_strategy, "No trade direction determined"
    assert entry_strategy['direction'] in ['long', 'short'], f"Invalid direction: {entry_strategy['direction']}"
    
    # Check Supertrend details
    assert 'supertrend' in entry_strategy, "Supertrend details missing"
    supertrend = entry_strategy['supertrend']
    
    assert 'trend' in supertrend, "Supertrend trend missing"
    assert 'value' in supertrend, "Supertrend value missing"
    assert 'direction' in supertrend, "Supertrend direction missing" 