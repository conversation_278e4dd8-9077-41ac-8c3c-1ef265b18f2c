import os
import pytest
from datetime import datetime, timedelta

from src.api.data.providers.polygon import PolygonProvider
from src.shared.data_providers.unified_base import HistoricalData, ProviderError
from src.core.exceptions import MarketDataError

@pytest.mark.asyncio
class TestPolygonProvider:
    @pytest.fixture
    def polygon_provider(self):
        """
        Create a Polygon provider instance for testing.
        Uses environment variable. Skips tests if not set.
        """
        api_key = os.getenv('POLYGON_API_KEY')
        if not api_key:
            pytest.skip('POLYGON_API_KEY environment variable not set')
        return PolygonProvider(api_key=api_key)
    
    async def test_get_current_price(self, polygon_provider):
        """
        Test retrieving current price for a stock symbol.
        """
        symbol = 'AAPL'  # Apple Inc.
        
        try:
            price_data = await polygon_provider.get_current_price(symbol)
            
            # Validate price data (returns MarketDataResponse from consolidated provider)
            assert hasattr(price_data, 'price'), "Should return MarketDataResponse with price attribute"
            assert price_data.price > 0, "Price should be a positive number"
            assert price_data.symbol == symbol, "Symbol should match requested symbol"
            print(f"✅ Real Polygon API data: {symbol} = ${price_data.price}")
        
        except ProviderError as e:
            pytest.fail(f"Market data retrieval failed: {e}")
    
    async def test_get_historical_data(self, polygon_provider):
        """
        Test retrieving historical stock data.
        """
        symbol = 'MSFT'  # Microsoft Corporation
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        try:
            historical_data = await polygon_provider.get_historical_data(
                symbol, start_date=start_date, end_date=end_date
            )
            
            # Validate historical data
            assert isinstance(historical_data, HistoricalData), "Should return HistoricalData"
            assert historical_data.symbol == symbol, "Symbol should match requested symbol"
            assert len(historical_data.dates) > 0, "Should return non-empty historical data"
            
            # Validate data structure
            assert len(historical_data.dates) == len(historical_data.closes), "Dates and closes should have same length"
            assert len(historical_data.dates) == len(historical_data.volumes), "Dates and volumes should have same length"
            
            # Validate each data point
            for i in range(len(historical_data.dates)):
                assert historical_data.closes[i] > 0, "Close price should be a positive number"
                assert historical_data.volumes[i] >= 0, "Volume should be non-negative"
        
        except Exception as e:
            pytest.fail(f"Historical market data retrieval failed: {e}")
    
    async def test_invalid_symbol(self, polygon_provider):
        """
        Test handling of invalid stock symbol.
        """
        invalid_symbols = ['', '123', 'INVALID_SYMBOL_TOO_LONG']
        
        for symbol in invalid_symbols:
            with pytest.raises(
                (ProviderError, ValueError),
                match="Invalid symbol|Failed to get|polygon error"
            ):
                await polygon_provider.get_current_price(symbol)
    
    @pytest.mark.parametrize("days", [1, 7, 30, 90])
    async def test_historical_data_with_different_periods(
        self, 
        polygon_provider, 
        days
    ):
        """
        Test historical data retrieval with different time periods.
        """
        symbol = 'GOOGL'  # Alphabet Inc.
        
        try:
            historical_data = await polygon_provider.get_historical_data(
                symbol, days=days
            )
            
            assert len(historical_data.dates) > 0, f"Should return data for {days} days"
            assert len(historical_data.dates) <= days, f"Should not return more than {days} data points"
        
        except Exception as e:
            pytest.fail(f"Historical data retrieval failed: {e}") 