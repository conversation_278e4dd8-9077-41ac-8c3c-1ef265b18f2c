import pytest
import os
from src.database.supabase_client import supabase_client, test_supabase_connection

@pytest.mark.supabase
@pytest.mark.integration
class TestSupabaseIntegration:
    def test_supabase_client_initialization(self, supabase_credentials):
        """
        Test Supabase client initialization with provided credentials.
        """
        client = supabase_client.get_client()
        assert client is not None, "Supabase client should be initialized"

    def test_supabase_connection_status(self, supabase_credentials):
        """
        Test Supabase connection status.
        """
        connection_status = test_supabase_connection()
        assert connection_status is True, "Supabase connection test should pass"

    def test_supabase_crud_operations(self, supabase_credentials, temp_test_data):
        """
        Test complete CRUD operations in Supabase.
        """
        # Insert test data
        insert_result = supabase_client.insert_data('discord_interactions', temp_test_data)
        assert insert_result is not None, "Data insertion should succeed"

        # Query inserted data
        query_result = supabase_client.execute_query('discord_interactions', {'user_id': temp_test_data['user_id']})
        assert len(query_result) > 0, "Query should return inserted data"
        assert query_result[0]['user_id'] == temp_test_data['user_id'], "Inserted data should match query"

        # Update test data
        update_data = {'message_content': 'Updated test message'}
        update_result = supabase_client.update_data(
            'discord_interactions', 
            {'user_id': temp_test_data['user_id']},
            update_data
        )
        assert update_result is not None, "Data update should succeed"

        # Verify update
        updated_query_result = supabase_client.execute_query('discord_interactions', {'user_id': temp_test_data['user_id']})
        assert len(updated_query_result) > 0, "Updated data should be retrievable"
        assert updated_query_result[0]['message_content'] == 'Updated test message', "Data should be updated"

    def test_supabase_error_handling(self, supabase_credentials):
        """
        Test error handling in Supabase client.
        """
        # Attempt to query with invalid table
        invalid_query_result = supabase_client.execute_query('non_existent_table', {})
        assert invalid_query_result == {}, "Query on non-existent table should return empty dict"

        # Attempt to insert invalid data
        invalid_data = {'invalid_key': 'invalid_value'}
        invalid_insert_result = supabase_client.insert_data('discord_interactions', invalid_data)
        assert invalid_insert_result is not None, "Invalid data insertion should not raise unhandled exception"
