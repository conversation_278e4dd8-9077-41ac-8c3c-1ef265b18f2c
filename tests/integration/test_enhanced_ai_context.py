"""
Integration Tests for Enhanced AI Context System

This module tests the integration of the enhanced AI context system
with the bot's query processing pipeline.
"""

import pytest
import asyncio
from typing import Dict, Any, List
import json
from datetime import datetime

from src.bot.pipeline.commands.ask.stages.enhanced_analyzer import analyze_query_with_enhanced_context
from src.bot.pipeline.commands.ask.stages.enhanced_context import enhanced_context, UserContext
from src.bot.pipeline.commands.ask.stages.advanced_classifier import advanced_classifier, QueryDomain
from src.shared.ai_services.ai_service_wrapper import AIServiceWrapper

@pytest.fixture
async def ai_service():
    """Create an AI service instance for testing"""
    return AIServiceWrapper()

@pytest.fixture
def test_user_id():
    """Test user ID"""
    return "test_user_123"

@pytest.fixture
def test_guild_id():
    """Test guild ID"""
    return "test_guild_456"

@pytest.fixture
async def setup_user_preferences(test_user_id):
    """Setup user preferences for testing"""
    # Create user context with specific preferences
    user_context = UserContext(
        user_id=test_user_id,
        tier_level="premium",
        preferred_symbols=["AAPL", "MSFT", "GOOGL"],
        risk_tolerance="aggressive",
        trading_style="swing",
        last_query_time=datetime.now(),
        query_frequency=5,
        preferred_timeframes=["1d", "4h"],
        preferred_indicators=["rsi", "macd", "ema"],
        language_preference="en"
    )
    
    # Add to enhanced context
    enhanced_context.user_contexts[test_user_id] = user_context
    
    yield
    
    # Cleanup
    if test_user_id in enhanced_context.user_contexts:
        del enhanced_context.user_contexts[test_user_id]

@pytest.mark.asyncio
async def test_enhanced_context_integration(test_user_id, test_guild_id, setup_user_preferences):
    """Test that enhanced context is properly integrated with query analysis"""
    # Test query
    query = "What's the RSI for AAPL and how does it compare to its historical average?"
    
    # Get enhanced analysis
    enhanced_analysis = await analyze_query_with_enhanced_context(
        query, test_user_id, test_guild_id
    )
    
    # Verify enhanced analysis structure
    assert enhanced_analysis is not None
    assert "base_analysis" in enhanced_analysis
    assert "enhanced_context" in enhanced_analysis
    assert "advanced_classification" in enhanced_analysis
    assert "integration_insights" in enhanced_analysis
    
    # Verify user context is included
    user_context = enhanced_analysis["enhanced_context"]["user_context"]
    assert user_context["user_id"] == test_user_id
    assert user_context["tier_level"] == "premium"
    assert "AAPL" in user_context["preferred_symbols"]
    assert user_context["risk_tolerance"] == "aggressive"
    
    # Verify classification is correct
    classification = enhanced_analysis["advanced_classification"]
    assert classification.primary_domain == QueryDomain.TECHNICAL_ANALYSIS
    assert "rsi" in classification.subcategories
    
    # Verify integration insights
    insights = enhanced_analysis["integration_insights"]
    assert "user_preferences" in insights
    assert insights["user_preferences"]["risk_tolerance"] == "aggressive"
    
    # Verify processing strategy
    strategy = enhanced_analysis["processing_strategy"]
    assert strategy["priority"] == "high"  # Premium user should get high priority

@pytest.mark.asyncio
async def test_ai_service_with_enhanced_context(ai_service, test_user_id, test_guild_id, setup_user_preferences):
    """Test that AI service wrapper properly uses enhanced context"""
    # Test query
    query = "Should I buy AAPL based on current RSI?"
    
    # Process with AI service
    result = await ai_service.process(query, test_user_id, test_guild_id)
    
    # Verify result structure
    assert result is not None
    assert "response" in result
    assert "intent" in result
    
    # Verify enhanced context is included
    assert "enhanced_context" in result
    
    # Verify the response is personalized based on user preferences
    enhanced_context = result.get("enhanced_context", {})
    if enhanced_context:
        insights = enhanced_context.get("integration_insights", {})
        user_prefs = insights.get("user_preferences", {})
        assert user_prefs.get("risk_tolerance") == "aggressive"

@pytest.mark.asyncio
async def test_domain_classification_accuracy():
    """Test the accuracy of domain classification"""
    test_queries = [
        ("What is the current RSI for AAPL?", QueryDomain.TECHNICAL_ANALYSIS),
        ("How are AAPL's earnings this quarter?", QueryDomain.FUNDAMENTAL_ANALYSIS),
        ("What's your outlook on the market this week?", QueryDomain.MARKET_OUTLOOK),
        ("What's a good entry strategy for MSFT?", QueryDomain.TRADING_STRATEGY),
        ("How should I size my position for TSLA?", QueryDomain.RISK_MANAGEMENT),
        ("Can you explain what RSI means?", QueryDomain.EDUCATION),
        ("What's the current price of NVDA?", QueryDomain.PRICE_QUERY),
        ("How does AAPL compare to MSFT?", QueryDomain.COMPARISON),
        ("How should I allocate my portfolio?", QueryDomain.PORTFOLIO_MANAGEMENT),
        ("What's a good call option strategy for AAPL?", QueryDomain.OPTIONS_TRADING),
        ("What's your prediction for Bitcoin?", QueryDomain.CRYPTO_ANALYSIS)
    ]
    
    for query, expected_domain in test_queries:
        # Classify query
        classification = await advanced_classifier.classify_query(query)
        
        # Verify classification
        assert classification.primary_domain == expected_domain, \
            f"Query '{query}' classified as {classification.primary_domain} but expected {expected_domain}"

@pytest.mark.asyncio
async def test_complexity_scoring():
    """Test the complexity scoring system"""
    test_queries = [
        ("What is the price of AAPL?", 1),  # SIMPLE
        ("What's the RSI and MACD for MSFT?", 2),  # MODERATE
        ("Compare the technical indicators for AAPL, MSFT, and GOOGL", 3),  # COMPLEX
        ("What's the probability of TSLA breaking resistance if the RSI crosses 70 while MACD shows bullish divergence?", 4),  # ADVANCED
        ("Given the current market conditions with rising interest rates, how would you adjust a portfolio of tech stocks with negative beta correlation to implement a risk-parity strategy using options as a hedge?", 5)  # EXPERT
    ]
    
    for query, expected_complexity in test_queries:
        # Classify query
        classification = await advanced_classifier.classify_query(query)
        
        # Verify complexity
        assert classification.complexity.value == expected_complexity, \
            f"Query '{query}' complexity scored as {classification.complexity.value} but expected {expected_complexity}"

@pytest.mark.asyncio
async def test_market_context_awareness():
    """Test market context awareness in enhanced analysis"""
    # Setup market context
    enhanced_context.market_context = enhanced_context._get_market_context.__annotations__["return"](
        market_status="open",
        major_indices={
            "SPY": {"price": 450.0, "change": 5.0, "change_pct": 1.1},
            "QQQ": {"price": 380.0, "change": 7.5, "change_pct": 2.0}
        },
        sector_performance={
            "Technology": 1.5,
            "Financials": -0.5,
            "Healthcare": 0.8
        },
        volatility_level="high",
        news_sentiment=0.3,
        economic_events=[
            {"event": "Fed Meeting", "date": "2025-09-10", "impact": "high"}
        ],
        is_earnings_season=True
    )
    
    # Test query
    query = "How is the market doing today?"
    
    # Get enhanced analysis
    enhanced_analysis = await analyze_query_with_enhanced_context(query, "test_user")
    
    # Verify market context is included
    market_context = enhanced_analysis["enhanced_context"]["market_context"]
    assert market_context["market_status"] == "open"
    assert market_context["volatility_level"] == "high"
    assert market_context["is_earnings_season"] == True
    
    # Verify classification
    classification = enhanced_analysis["advanced_classification"]
    assert classification.primary_domain == QueryDomain.MARKET_OUTLOOK

@pytest.mark.asyncio
async def test_conversation_context_tracking():
    """Test conversation context tracking"""
    conversation_id = "test_conversation_123"
    user_id = "test_user_456"
    
    # First query
    query1 = "What's the RSI for AAPL?"
    enhanced_analysis1 = await analyze_query_with_enhanced_context(
        query1, user_id, conversation_id=conversation_id
    )
    
    # Second query
    query2 = "And what about MSFT?"
    enhanced_analysis2 = await analyze_query_with_enhanced_context(
        query2, user_id, conversation_id=conversation_id
    )
    
    # Verify conversation context is tracked
    conv_context1 = enhanced_analysis1["enhanced_context"]["conversation_context"]
    conv_context2 = enhanced_analysis2["enhanced_context"]["conversation_context"]
    
    assert conv_context1["conversation_id"] == conversation_id
    assert conv_context2["conversation_id"] == conversation_id
    
    # Verify history is updated
    assert len(conv_context1["history"]) == 1
    assert len(conv_context2["history"]) == 2
    assert conv_context2["history"][0]["query"] == query1
    assert conv_context2["history"][1]["query"] == query2
    
    # Verify context drift
    assert conv_context2["context_drift_score"] > 0
    assert conv_context2["requires_context"] == True

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
