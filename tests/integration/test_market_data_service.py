import pytest
import asyncio
from datetime import datetime, timedelta

from src.api.data.market_data_service import MarketDataService
from src.api.data.providers.base import MarketDataResponse
from src.core.exceptions import MarketDataError

@pytest.mark.asyncio
class TestMarketDataService:
    @pytest.fixture
    def market_data_service(self):
        """
        Create a market data service instance for testing.
        """
        return MarketDataService()
    
    async def test_get_current_price(self, market_data_service):
        """
        Test retrieving current price for a stock symbol.
        """
        symbol = 'AAPL'  # Apple Inc.
        
        try:
            price_data = await market_data_service.get_current_price(symbol)
            
            # Validate MarketDataResponse
            assert isinstance(price_data, MarketDataResponse), "Should return MarketDataResponse"
            assert price_data.symbol == symbol, "Symbol should match requested symbol"
            assert price_data.price > 0, "Price should be a positive number"
            assert price_data.timestamp is not None, "Timestamp should be set"
            assert price_data.provider is not None, "Provider should be specified"
        
        except MarketDataError as e:
            pytest.fail(f"Market data retrieval failed: {e}")
    
    async def test_get_historical_data(self, market_data_service):
        """
        Test retrieving historical market data.
        """
        symbol = 'MSFT'  # Microsoft Corporation
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        try:
            historical_data = await market_data_service.get_historical_data(
                symbol, start_date, end_date
            )
            
            # Validate historical data
            assert isinstance(historical_data, list), "Should return list of MarketDataResponse"
            assert len(historical_data) > 0, "Should return non-empty historical data"
            
            # Validate each data point
            for data_point in historical_data:
                assert isinstance(data_point, MarketDataResponse), "Each item should be MarketDataResponse"
                assert data_point.symbol == symbol, "Symbol should match requested symbol"
                assert data_point.price > 0, "Price should be a positive number"
                assert data_point.timestamp is not None, "Timestamp should be set"
                assert data_point.provider is not None, "Provider should be specified"
        
        except MarketDataError as e:
            pytest.fail(f"Historical market data retrieval failed: {e}")
    
    async def test_invalid_symbol(self, market_data_service):
        """
        Test handling of invalid stock symbol.
        """
        invalid_symbols = ['', '123', 'INVALID_SYMBOL_TOO_LONG']
        
        for symbol in invalid_symbols:
            with pytest.raises(
                (MarketDataError, ValueError), 
                match="Invalid symbol|Failed to get"
            ):
                await market_data_service.get_current_price(symbol)
    
    @pytest.mark.parametrize("days", [1, 7, 30, 90])
    async def test_historical_data_with_different_periods(
        self, 
        market_data_service, 
        days
    ):
        """
        Test historical data retrieval with different time periods.
        """
        symbol = 'GOOGL'  # Alphabet Inc.
        
        try:
            historical_data = await market_data_service.get_historical_data(
                symbol, days=days
            )
            
            assert len(historical_data) > 0, f"Should return data for {days} days"
            assert len(historical_data) <= days, f"Should not return more than {days} data points"
        
        except MarketDataError as e:
            pytest.fail(f"Historical market data retrieval failed for {days} days: {e}")
