"""
Integration Tests for Cross-Platform Context Sharing

This module tests the cross-platform context sharing functionality
to ensure user context and preferences are shared across platforms.
"""

import pytest
import asyncio
import os
import json
import shutil
from typing import Dict, Any
from datetime import datetime

from src.bot.pipeline.commands.ask.stages.cross_platform_context import (
    CrossPlatformContext, get_user_context, update_user_context,
    get_conversation_context, add_conversation_message, CONTEXT_STORAGE_PATH
)

@pytest.fixture
async def context_manager():
    """Create a context manager for testing"""
    # Create test storage path
    test_storage_path = os.path.join('data', 'test_context')
    
    # Save original path
    original_path = CONTEXT_STORAGE_PATH
    
    # Override storage path for testing
    import src.bot.pipeline.commands.ask.stages.cross_platform_context as cross_platform_module
    cross_platform_module.CONTEXT_STORAGE_PATH = test_storage_path
    
    # Create instance
    context = CrossPlatformContext()
    
    yield context
    
    # Restore original path
    cross_platform_module.CONTEXT_STORAGE_PATH = original_path
    
    # Clean up test storage
    if os.path.exists(test_storage_path):
        shutil.rmtree(test_storage_path)

@pytest.mark.asyncio
async def test_user_context_creation(context_manager):
    """Test user context creation and retrieval"""
    # Test user IDs
    discord_user_id = "discord_user_123"
    
    # Get initial context (should create new)
    context = await context_manager.get_user_context(discord_user_id, "discord")
    
    # Verify context structure
    assert context is not None
    assert "user_id" in context
    assert "created_at" in context
    assert "last_updated" in context
    assert "preferences" in context
    assert "platforms" in context

@pytest.mark.asyncio
async def test_user_context_update(context_manager):
    """Test user context update"""
    # Test user ID
    discord_user_id = "discord_user_456"
    
    # Update context
    update_data = {
        "preferences": {
            "tier_level": "premium",
            "risk_tolerance": "aggressive",
            "preferred_symbols": ["AAPL", "MSFT", "GOOGL"]
        }
    }
    
    success = await context_manager.update_user_context(discord_user_id, update_data, "discord")
    assert success
    
    # Get updated context
    context = await context_manager.get_user_context(discord_user_id, "discord")
    
    # Verify updates
    assert context["preferences"]["tier_level"] == "premium"
    assert context["preferences"]["risk_tolerance"] == "aggressive"
    assert "AAPL" in context["preferences"]["preferred_symbols"]

@pytest.mark.asyncio
async def test_platform_linking(context_manager):
    """Test linking user IDs across platforms"""
    # Test user IDs
    discord_user_id = "discord_user_789"
    web_user_id = "web_user_789"
    
    # Create initial context on Discord
    discord_context = await context_manager.get_user_context(discord_user_id, "discord")
    
    # Update with preferences
    await context_manager.update_user_context(discord_user_id, {
        "preferences": {
            "tier_level": "premium",
            "risk_tolerance": "aggressive"
        }
    }, "discord")
    
    # Link platforms
    success = await context_manager.link_platform_ids(
        discord_user_id, "discord",
        web_user_id, "web"
    )
    assert success
    
    # Get context from web platform
    web_context = await context_manager.get_user_context(web_user_id, "web")
    
    # Verify shared context
    assert web_context["preferences"].get("tier_level") == "premium"
    assert web_context["preferences"].get("risk_tolerance") == "aggressive"

@pytest.mark.asyncio
async def test_conversation_context(context_manager):
    """Test conversation context across platforms"""
    # Test conversation ID
    conversation_id = "test_conversation_123"
    
    # Get initial conversation context
    context = await context_manager.get_conversation_context(conversation_id, "discord")
    
    # Verify structure
    assert context is not None
    assert "conversation_id" in context
    assert "history" in context
    assert isinstance(context["history"], list)
    
    # Add message
    message = {
        "query": "What is the RSI for AAPL?",
        "user_id": "test_user_123",
        "timestamp": datetime.now().isoformat()
    }
    
    success = await context_manager.add_conversation_message(conversation_id, message, "discord")
    assert success
    
    # Get updated context
    updated_context = await context_manager.get_conversation_context(conversation_id, "discord")
    
    # Verify message was added
    assert len(updated_context["history"]) == 1
    assert updated_context["history"][0]["query"] == "What is the RSI for AAPL?"

@pytest.mark.asyncio
async def test_cross_platform_conversation(context_manager):
    """Test conversation context sharing across platforms"""
    # Test conversation ID
    conversation_id = "cross_platform_conv_123"
    
    # Add message from Discord
    discord_message = {
        "query": "What is the outlook for MSFT?",
        "user_id": "discord_user_123",
        "platform": "discord",
        "timestamp": datetime.now().isoformat()
    }
    
    await context_manager.add_conversation_message(conversation_id, discord_message, "discord")
    
    # Add message from web
    web_message = {
        "query": "How about AAPL?",
        "user_id": "web_user_123",
        "platform": "web",
        "timestamp": datetime.now().isoformat()
    }
    
    await context_manager.add_conversation_message(conversation_id, web_message, "web")
    
    # Get conversation context from mobile
    mobile_context = await context_manager.get_conversation_context(conversation_id, "mobile")
    
    # Verify all messages are available
    assert len(mobile_context["history"]) == 2
    assert mobile_context["history"][0]["query"] == "What is the outlook for MSFT?"
    assert mobile_context["history"][1]["query"] == "How about AAPL?"

@pytest.mark.asyncio
async def test_convenience_functions():
    """Test convenience functions for cross-platform context"""
    # Create test storage path
    test_storage_path = os.path.join('data', 'test_context_functions')
    
    # Override storage path for testing
    import src.bot.pipeline.commands.ask.stages.cross_platform_context as cross_platform_module
    original_path = cross_platform_module.CONTEXT_STORAGE_PATH
    cross_platform_module.CONTEXT_STORAGE_PATH = test_storage_path
    
    try:
        # Test user ID
        user_id = "test_user_functions"
        
        # Test get_user_context
        context = await get_user_context(user_id)
        assert context is not None
        
        # Test update_user_context
        success = await update_user_context(user_id, {"preferences": {"tier_level": "premium"}})
        assert success
        
        # Test get_conversation_context
        conv_id = "test_conv_functions"
        conv_context = await get_conversation_context(conv_id)
        assert conv_context is not None
        
        # Test add_conversation_message
        message = {"query": "Test message", "timestamp": datetime.now().isoformat()}
        success = await add_conversation_message(conv_id, message)
        assert success
        
        # Verify message was added
        updated_conv = await get_conversation_context(conv_id)
        assert len(updated_conv["history"]) == 1
        assert updated_conv["history"][0]["query"] == "Test message"
    
    finally:
        # Restore original path
        cross_platform_module.CONTEXT_STORAGE_PATH = original_path
        
        # Clean up test storage
        if os.path.exists(test_storage_path):
            shutil.rmtree(test_storage_path)

@pytest.mark.asyncio
async def test_persistence(context_manager):
    """Test context persistence across instances"""
    # Test user ID
    user_id = "persistence_test_user"
    
    # Update context
    await context_manager.update_user_context(user_id, {
        "preferences": {
            "tier_level": "premium",
            "preferred_symbols": ["TSLA", "NVDA"]
        }
    })
    
    # Create new instance with same storage path
    new_context_manager = CrossPlatformContext()
    
    # Get context from new instance
    context = await new_context_manager.get_user_context(user_id, "discord")
    
    # Verify data persisted
    assert context["preferences"].get("tier_level") == "premium"
    assert "TSLA" in context["preferences"].get("preferred_symbols", [])

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
