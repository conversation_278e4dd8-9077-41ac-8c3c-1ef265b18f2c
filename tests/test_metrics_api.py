import pytest
import httpx
from datetime import datetime

from src.api.main import app
from src.core.monitoring import response_metrics_tracker


@pytest.mark.asyncio
async def test_metrics_endpoints_flow():
    # Reset metrics to ensure a clean slate
    response_metrics_tracker.reset_metrics()

    # Simulate metrics being tracked by various code paths
    response_metrics_tracker.track_response({"response_type": "success", "intent": "general"}, 0.12)
    response_metrics_tracker.track_response({"response_type": "error", "intent": "no_query"}, 0.05, error=Exception("parse_error"))
    response_metrics_tracker.track_response({"response_type": "cache_hit", "intent": "price_check"}, 0.01)

    transport = httpx.ASGITransport(app=app)
    async with httpx.AsyncClient(transport=transport, base_url="http://test") as ac:
        # Overview should reflect tracked responses
        r_overview = await ac.get("/api/v1/metrics/overview")
        assert r_overview.status_code == 200
        overview = r_overview.json()
        assert overview["total_responses"] == 3
        assert overview["response_type_distribution"].get("success", 0) == 1
        assert overview["response_type_distribution"].get("error", 0) == 1
        assert overview["response_type_distribution"].get("cache_hit", 0) == 1
        assert overview["avg_processing_time"] > 0

        # Daily metrics endpoint should include today's summary
        r_daily = await ac.get("/api/v1/metrics/daily")
        assert r_daily.status_code == 200
        daily = r_daily.json()
        today = datetime.now().strftime('%Y-%m-%d')
        assert today in daily
        assert daily[today]["total_responses"] == 3
        # Ensure response types are present under current day
        day_types = daily[today].get("response_types", {})
        assert day_types.get("success", 0) == 1
        assert day_types.get("error", 0) == 1
        assert day_types.get("cache_hit", 0) == 1

        # Response types distribution should match overview
        r_types = await ac.get("/api/v1/metrics/response-types")
        assert r_types.status_code == 200
        types_dist = r_types.json()
        assert types_dist.get("success", 0) == 1
        assert types_dist.get("error", 0) == 1
        assert types_dist.get("cache_hit", 0) == 1

    # Cleanup after test
    response_metrics_tracker.reset_metrics()
