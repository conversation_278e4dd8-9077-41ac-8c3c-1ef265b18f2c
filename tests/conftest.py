# Ensure project root is on sys.path so tests can `import src`
import os
import sys

ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

import pytest
import sqlalchemy
import sqlalchemy.orm
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def pytest_configure(config):
    """
    Configure pytest with additional settings.
    
    This function is called after command line options have been parsed 
    and all plugins and initial conftest files have been loaded.
    """
    # Add custom markers
    config.addinivalue_line(
        "markers", 
        "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", 
        "database: mark test as a database-related test"
    )
    config.addinivalue_line(
        "markers", 
        "supabase: mark test as a Supabase-related test"
    )

def pytest_collection_modifyitems(config, items):
    """
    Modify test items based on markers or other conditions.
    
    This allows for selective test running or additional test filtering.
    """
    # Example: Skip tests that require external services if not configured
    for item in items:
        if "supabase" in item.keywords:
            if not os.getenv('SUPABASE_URL') or not os.getenv('SUPABASE_KEY'):
                item.add_marker(
                    pytest.mark.skip(
                        reason="Supabase credentials not configured"
                    )
                )

@pytest.fixture(scope="session")
def supabase_credentials():
    """
    Fixture to provide Supabase credentials for tests.
    
    Returns:
        dict: Supabase connection credentials with both API and database URLs
    """
    # Get Supabase API credentials
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')
    database_url = os.getenv('DATABASE_URL')
    
    if not all([supabase_url, supabase_key, database_url]):
        pytest.skip("Supabase or database credentials not fully configured")
    
    return {
        'supabase_url': supabase_url,
        'supabase_key': supabase_key,
        'database_url': database_url
    }

@pytest.fixture(scope="function")
def database_connection(supabase_credentials):
    """
    Fixture to provide a synchronous database connection for tests.
    
    This is kept for backward compatibility with existing tests.
    New tests should use async_database_connection instead.
    
    Args:
        supabase_credentials: Fixture providing Supabase credentials
        
    Yields:
        Database connection object
    """
    import warnings
    warnings.warn(
        "Using synchronous database_connection fixture is deprecated. "
        "Use async_database_connection for new tests.",
        DeprecationWarning, stacklevel=2
    )
    
    # Import the synchronous session for backward compatibility
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    
    # Create a synchronous engine for backward compatibility
    db_url = supabase_credentials['database_url']
    if db_url.startswith('postgresql+asyncpg://'):
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    
    engine = create_engine(db_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # Create a new session using the configured engine
    db = SessionLocal()
    
    try:
        yield db
    finally:
        db.close()


@pytest.fixture(scope="function")
async def async_database_connection(supabase_credentials):
    """
    Fixture to provide an async database connection for tests.
    
    Args:
        supabase_credentials: Fixture providing Supabase credentials
        
    Yields:
        AsyncSession: Async database session object
    """
    # Import the async session factory
    from src.database.connection import configure_engine, SessionLocal
    
    # Ensure engine is configured
    await configure_engine()
    
    # Create a new async session
    async with SessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

@pytest.fixture(scope="function")
def temp_test_data():
    """
    Generate temporary test data for database operations.
    
    Returns:
        dict: Temporary test data
    """
    return {
        'user_id': 'test_user_' + os.urandom(4).hex(),
        'channel_id': 'test_channel_' + os.urandom(4).hex(),
        'message_content': 'Test message ' + os.urandom(4).hex(),
        'symbol': 'TEST'
    }

def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """
    Add custom summary information to test report.
    
    This provides additional context about the test run.
    """
    # Add custom summary information
    terminalreporter.write_line("")
    terminalreporter.write_line("🤖 AI Trading Bot Test Summary 🤖")
    terminalreporter.write_line("-" * 40)
    
    # Environment information
    terminalreporter.write_line(f"Python Version: {sys.version.split()[0]}")
    terminalreporter.write_line(f"Supabase Configured: {'Yes' if os.getenv('SUPABASE_URL') else 'No'}")
    terminalreporter.write_line(f"Discord Bot Token: {'Configured' if os.getenv('DISCORD_BOT_TOKEN') else 'Not Set'}")
    
    terminalreporter.write_line("-" * 40) 