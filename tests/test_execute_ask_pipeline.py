"""
Unit tests for execute_ask_pipeline with mocked services.
Tests cover happy paths, error handling, and edge cases.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

# Import the function to test
from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
from src.bot.pipeline.core.context_manager import PipelineContext, PipelineStatus


class MockAskPipeline:
    """Mock AskPipeline for testing"""
    def __init__(self, config, context):
        self.config = config
        self.context = context
        self.sections = {}
    
    async def run(self, query):
        return {}


@pytest.fixture
def mock_config():
    """Fixture for mock config"""
    return MagicMock()


@pytest.fixture
def mock_pipeline(monkeypatch):
    """Fixture to mock AskPipeline"""
    mock_instance = MagicMock()
    mock_instance.run = AsyncMock()
    monkeypatch.setattr('src.bot.pipeline.commands.ask.pipeline.AskPipeline', lambda config, context: mock_instance)
    return mock_instance


@pytest.mark.asyncio
async def test_execute_ask_pipeline_success(mock_pipeline, mock_config):
    """Test successful pipeline execution"""
    # Mock successful pipeline run
    mock_pipeline.run.return_value = {
        'ai_chat_processor': {
            'response': 'Test response',
            'data': {'AAPL': {'price': 150.0}},
            'intent': 'price_check',
            'symbols': ['AAPL'],
            'needs_data': True
        }
    }
    
    # Execute pipeline
    context = await execute_ask_pipeline(
        query="What is the price of AAPL?",
        user_id="test_user",
        guild_id="test_guild"
    )
    
    # Verify results
    assert context.status == PipelineStatus.COMPLETED
    assert 'response' in context.processing_results
    assert context.processing_results['response'] == 'Test response'
    assert 'data' in context.processing_results
    assert 'AAPL' in context.processing_results['data']


@pytest.mark.asyncio
async def test_execute_ask_pipeline_empty_query(mock_pipeline, mock_config):
    """Test pipeline execution with empty query"""
    # Mock pipeline to return empty query response
    mock_pipeline.run.return_value = {
        'ai_chat_processor': {
            'response': "I didn't receive a valid query. Please try asking about stocks, trading, or market analysis.",
            'data': {},
            'intent': 'no_query',
            'symbols': [],
            'tools_required': [],
            'error': 'EMPTY_QUERY'
        }
    }
    
    context = await execute_ask_pipeline(
        query="",
        user_id="test_user",
        guild_id="test_guild"
    )
    
    # Should handle empty query gracefully
    assert context.status == PipelineStatus.COMPLETED
    assert 'response' in context.processing_results
    assert 'valid query' in context.processing_results['response']


@pytest.mark.asyncio
async def test_execute_ask_pipeline_error(mock_pipeline, mock_config):
    """Test pipeline execution with error"""
    # Mock pipeline to raise exception
    mock_pipeline.run.side_effect = Exception("Pipeline failed")
    
    # Execute pipeline
    context = await execute_ask_pipeline(
        query="What is the price of AAPL?",
        user_id="test_user",
        guild_id="test_guild"
    )
    
    # Should handle error gracefully
    assert context.status == PipelineStatus.FAILED
    assert len(context.error_log) > 0
    assert any('Pipeline failed' in error['error_message'] for error in context.error_log)


@pytest.mark.asyncio
async def test_execute_ask_pipeline_timeout(mock_pipeline, mock_config):
    """Test pipeline execution timeout"""
    # Mock pipeline to timeout
    async def timeout_run(query):
        await asyncio.sleep(0.2)  # Longer than timeout
        return {}
    
    mock_pipeline.run = timeout_run
    
    # Execute pipeline with short timeout
    with patch('src.bot.pipeline.commands.ask.pipeline.config') as mock_config:
        mock_config.timeout = 0.1  # Very short timeout
        context = await execute_ask_pipeline(
            query="What is the price of AAPL?",
            user_id="test_user",
            guild_id="test_guild"
        )
    
    # Should handle timeout gracefully - the pipeline actually completes with fallback
    assert context.status == PipelineStatus.COMPLETED
    assert 'response' in context.processing_results


@pytest.mark.asyncio
async def test_execute_ask_pipeline_no_ai_config(mock_pipeline, mock_config):
    """Test pipeline execution without AI configuration"""
    # Mock pipeline to return no AI config response
    mock_pipeline.run.return_value = {
        'ai_chat_processor': {
            'response': 'AI not configured',
            'data': {},
            'intent': 'general_question',
            'symbols': [],
            'needs_data': False
        }
    }
    
    # Execute pipeline
    context = await execute_ask_pipeline(
        query="What is the price of AAPL?",
        user_id="test_user",
        guild_id="test_guild"
    )
    
    # Should handle no AI config gracefully
    assert context.status == PipelineStatus.COMPLETED
    assert 'response' in context.processing_results
    assert 'AI not configured' in context.processing_results['response']


@pytest.mark.asyncio
async def test_execute_ask_pipeline_multiple_symbols(mock_pipeline, mock_config):
    """Test pipeline execution with multiple symbols"""
    # Mock pipeline with multiple symbols
    mock_pipeline.run.return_value = {
        'ai_chat_processor': {
            'response': 'Market overview',
            'data': {
                'AAPL': {'price': 150.0, 'change': 2.5},
                'GOOGL': {'price': 2800.0, 'change': -1.2}
            },
            'intent': 'market_overview',
            'symbols': ['AAPL', 'GOOGL'],
            'needs_data': True
        }
    }
    
    # Execute pipeline
    context = await execute_ask_pipeline(
        query="Market overview for AAPL and GOOGL",
        user_id="test_user",
        guild_id="test_guild"
    )
    
    # Should handle multiple symbols
    assert context.status == PipelineStatus.COMPLETED
    assert 'data' in context.processing_results
    assert 'AAPL' in context.processing_results['data']
    assert 'GOOGL' in context.processing_results['data']


@pytest.mark.asyncio
async def test_execute_ask_pipeline_no_data_needed(mock_pipeline, mock_config):
    """Test pipeline execution when no data is needed"""
    # Mock pipeline with no data needed
    mock_pipeline.run.return_value = {
        'ai_chat_processor': {
            'response': 'General information response',
            'data': {},
            'intent': 'general_question',
            'symbols': [],
            'needs_data': False
        }
    }
    
    # Execute pipeline
    context = await execute_ask_pipeline(
        query="What is the stock market?",
        user_id="test_user",
        guild_id="test_guild"
    )
    
    # Should handle no data needed
    assert context.status == PipelineStatus.COMPLETED
    assert 'data' in context.processing_results
    assert context.processing_results['data'] == {}  # Empty data


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])