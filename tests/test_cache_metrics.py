"""
Tests for cache metrics collection and Prometheus integration.
"""

import pytest
import time
from unittest.mock import Mock, patch

from src.api.data.metrics import (
    CacheMetrics, 
    cache_metrics, 
    track_cache_operation,
    track_api_cache_usage,
    get_metrics_health
)


class TestCacheMetrics:
    """Test the CacheMetrics class."""
    
    @pytest.fixture
    def metrics(self):
        """Create a fresh metrics instance for testing."""
        return CacheMetrics()
    
    def test_record_cache_warming_job(self, metrics):
        """Test recording cache warming job metrics."""
        # Mock the Prometheus metrics
        with patch('src.api.data.metrics.CACHE_WARMING_DURATION') as mock_duration, \
             patch('src.api.data.metrics.CACHE_WARMING_SYMBOLS_TOTAL') as mock_total, \
             patch('src.api.data.metrics.CACHE_WARMING_SYMBOLS_SUCCESS') as mock_success, \
             patch('src.api.data.metrics.CACHE_WARMING_SUCCESS_RATE') as mock_rate, \
             patch('src.api.data.metrics.CACHE_WARMING_LAST_RUN') as mock_last_run:
            
            metrics.record_cache_warming_job('pre_market', 30.5, 50, 45)
            
            # Verify metrics were recorded
            mock_duration.labels.assert_called_once_with(job_type='pre_market')
            mock_total.labels.assert_called_once_with(job_type='pre_market')
            mock_success.labels.assert_called_once_with(job_type='pre_market')
            mock_rate.labels.assert_called_once_with(job_type='pre_market')
            mock_last_run.labels.assert_called_once_with(job_type='pre_market')
    
    def test_record_cache_stats(self, metrics):
        """Test recording cache statistics."""
        with patch('src.api.data.metrics.CACHE_HIT_RATE') as mock_hit_rate, \
             patch('src.api.data.metrics.CACHE_MEMORY_USAGE') as mock_memory:
            
            stats = {'hit_rate': 85.5, 'used_memory': 1024000}
            metrics.record_cache_stats(stats)
            
            mock_hit_rate.set.assert_called_once_with(85.5)
            mock_memory.set.assert_called_once_with(1024000)
    
    def test_record_cache_operation(self, metrics):
        """Test recording cache operations."""
        with patch('src.api.data.metrics.CACHE_OPERATIONS') as mock_operations:
            metrics.record_cache_operation('get', 'success')
            
            mock_operations.labels.assert_called_once_with(operation='get', result='success')
            mock_operations.labels().inc.assert_called_once()
    
    def test_record_api_cache_hit(self, metrics):
        """Test recording API cache hits."""
        with patch('src.api.data.metrics.API_CACHE_HITS') as mock_hits:
            metrics.record_api_cache_hit('/api/price', 'AAPL')
            
            mock_hits.labels.assert_called_once_with(endpoint='/api/price', symbol='AAPL')
            mock_hits.labels().inc.assert_called_once()
    
    def test_record_api_cache_miss(self, metrics):
        """Test recording API cache misses."""
        with patch('src.api.data.metrics.API_CACHE_MISSES') as mock_misses:
            metrics.record_api_cache_miss('/api/price', 'MSFT')
            
            mock_misses.labels.assert_called_once_with(endpoint='/api/price', symbol='MSFT')
            mock_misses.labels().inc.assert_called_once()
    
    def test_metrics_disabled(self, metrics):
        """Test that metrics are not recorded when disabled."""
        metrics.enabled = False
        
        with patch('src.api.data.metrics.CACHE_OPERATIONS') as mock_operations:
            metrics.record_cache_operation('get', 'success')
            
            # Should not call any Prometheus methods
            mock_operations.labels.assert_not_called()
    
    def test_get_current_metrics(self, metrics):
        """Test getting current metric values."""
        with patch('src.api.data.metrics.CACHE_HIT_RATE') as mock_hit_rate, \
             patch('src.api.data.metrics.CACHE_MEMORY_USAGE') as mock_memory, \
             patch('src.api.data.metrics.CACHE_WARMING_REGISTRY') as mock_registry:
            
            # Mock the metric values
            mock_hit_rate._value._value = 85.5
            mock_memory._value._value = 1024000
            
            # Mock the registry collection
            mock_registry.collect.return_value = []
            
            result = metrics.get_current_metrics()
            
            assert result['cache_hit_rate'] == 85.5
            assert result['cache_memory_bytes'] == 1024000
            assert 'last_warming_jobs' in result


class TestCacheMetricsDecorators:
    """Test the cache metrics decorators."""
    
    @pytest.mark.asyncio
    async def test_track_cache_operation_success(self):
        """Test tracking successful cache operations."""
        with patch.object(cache_metrics, 'record_cache_operation') as mock_record:
            
            @track_cache_operation('get')
            async def mock_cache_get():
                return {'data': 'test'}
            
            result = await mock_cache_get()
            
            assert result == {'data': 'test'}
            mock_record.assert_called_once_with('get', 'success')
    
    @pytest.mark.asyncio
    async def test_track_cache_operation_miss(self):
        """Test tracking cache misses."""
        with patch.object(cache_metrics, 'record_cache_operation') as mock_record:
            
            @track_cache_operation('get')
            async def mock_cache_get():
                return None
            
            result = await mock_cache_get()
            
            assert result is None
            mock_record.assert_called_once_with('get', 'miss')
    
    @pytest.mark.asyncio
    async def test_track_cache_operation_error(self):
        """Test tracking cache operation errors."""
        with patch.object(cache_metrics, 'record_cache_operation') as mock_record:
            
            @track_cache_operation('get')
            async def mock_cache_get():
                raise ValueError("Cache error")
            
            with pytest.raises(ValueError):
                await mock_cache_get()
            
            mock_record.assert_called_once_with('get', 'error')
    
    @pytest.mark.asyncio
    async def test_track_api_cache_usage_hit(self):
        """Test tracking API cache hits."""
        with patch.object(cache_metrics, 'record_api_cache_hit') as mock_record:
            
            @track_api_cache_usage('/api/price')
            async def mock_api_call(symbol):
                result = Mock()
                result._from_cache = True
                return result
            
            result = await mock_api_call('AAPL')
            
            mock_record.assert_called_once_with('/api/price', 'AAPL')
    
    @pytest.mark.asyncio
    async def test_track_api_cache_usage_miss(self):
        """Test tracking API cache misses."""
        with patch.object(cache_metrics, 'record_api_cache_miss') as mock_record:
            
            @track_api_cache_usage('/api/price')
            async def mock_api_call(symbol):
                result = Mock()
                result._from_cache = False
                return result
            
            result = await mock_api_call('MSFT')
            
            mock_record.assert_called_once_with('/api/price', 'MSFT')


class TestMetricsHealth:
    """Test the metrics health check functionality."""
    
    def test_get_metrics_health_healthy(self):
        """Test health check when metrics are working."""
        with patch.object(cache_metrics, 'get_current_metrics') as mock_get_metrics:
            mock_get_metrics.return_value = {
                'cache_hit_rate': 85.5,
                'cache_memory_bytes': 1024000
            }
            
            health = get_metrics_health()
            
            assert health['status'] == 'healthy'
            assert 'current_metrics' in health
            assert 'timestamp' in health
    
    def test_get_metrics_health_unhealthy(self):
        """Test health check when metrics are failing."""
        with patch.object(cache_metrics, 'get_current_metrics') as mock_get_metrics:
            mock_get_metrics.side_effect = Exception("Metrics error")
            
            health = get_metrics_health()
            
            assert health['status'] == 'unhealthy'
            assert 'error' in health
            assert 'timestamp' in health


class TestPrometheusMetrics:
    """Test Prometheus metric definitions."""
    
    def test_cache_warming_metrics_registry(self):
        """Test that all cache warming metrics are properly registered."""
        from src.api.data.metrics import CACHE_WARMING_REGISTRY
        
        # Check that the registry contains our metrics
        metrics = list(CACHE_WARMING_REGISTRY.collect())
        metric_names = [metric.name for metric in metrics]
        
        expected_metrics = [
            'cache_warming_duration_seconds',
            'cache_warming_symbols_total',
            'cache_warming_symbols_success',
            'cache_warming_success_rate',
            'cache_warming_last_run_timestamp',
            'redis_cache_hit_rate',
            'redis_cache_memory_bytes',
            'cache_operations_total',
            'api_cache_hits_total',
            'api_cache_misses_total'
        ]
        
        for expected in expected_metrics:
            assert expected in metric_names, f"Metric {expected} not found in registry"


if __name__ == "__main__":
    pytest.main([__file__]) 