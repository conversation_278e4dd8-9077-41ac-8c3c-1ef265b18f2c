"""
Tests for data quality scoring system and API endpoints.
"""

import pytest
import pandas as pd
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, AsyncMock

from src.core.data_quality import (
    DataQualityScorer, 
    QualityScore, 
    QualityLevel, 
    score_data_quality,
    get_quality_level_description
)
from src.api.routes.analytics import (
    get_data_quality_score,
    get_batch_data_quality,
    _generate_quality_recommendations,
    _calculate_data_coverage_hours
)


class TestDataQualityScorer:
    """Test the DataQualityScorer class."""
    
    @pytest.fixture
    def scorer(self):
        """Create a fresh DataQualityScorer instance."""
        return DataQualityScorer()
    
    @pytest.fixture
    def sample_data(self):
        """Create sample market data for testing."""
        timestamps = [
            datetime(2024, 1, 1, 9, 30) + timedelta(minutes=i)
            for i in range(60)  # 1 hour of 1-minute data
        ]
        
        data = pd.DataFrame({
            'open': [100 + i * 0.1 for i in range(60)],
            'high': [101 + i * 0.1 for i in range(60)],
            'low': [99 + i * 0.1 for i in range(60)],
            'close': [100.5 + i * 0.1 for i in range(60)],
            'volume': [1000000 + i * 1000 for i in range(60)]
        }, index=timestamps)
        
        return data
    
    @pytest.fixture
    def sample_provider_metadata(self):
        """Create sample provider metadata."""
        return {
            'provider_name': 'Polygon.io',
            'provider_type': 'polygon',
            'fetched_at': datetime.now(timezone.utc).isoformat(),
            'is_fallback': False,
            'response_time_ms': 150.5
        }
    
    @pytest.mark.asyncio
    async def test_score_data_quality_complete_data(self, scorer, sample_data, sample_provider_metadata):
        """Test quality scoring with complete, recent data."""
        quality_score = await scorer.score_data_quality(
            symbol="AAPL",
            data=sample_data,
            interval_type="1m",
            provider_metadata=sample_provider_metadata
        )
        
        assert quality_score.symbol == "AAPL"
        assert quality_score.overall_score > 80  # Should be high quality
        assert quality_score.completeness_score > 90  # Complete data
        assert quality_score.freshness_score > 90  # Recent data
        assert quality_score.consistency_score > 80  # Consistent data
        assert quality_score.quality_level in [QualityLevel.EXCELLENT, QualityLevel.GOOD]
    
    @pytest.mark.asyncio
    async def test_score_data_quality_incomplete_data(self, scorer):
        """Test quality scoring with incomplete data."""
        # Create data with gaps
        timestamps = [
            datetime(2024, 1, 1, 9, 30) + timedelta(minutes=i)
            for i in [0, 1, 2, 15, 16, 17, 45, 46, 47]  # Gaps in data
        ]
        
        incomplete_data = pd.DataFrame({
            'open': [100 + i * 0.1 for i in range(len(timestamps))],
            'high': [101 + i * 0.1 for i in range(len(timestamps))],
            'low': [99 + i * 0.1 for i in range(len(timestamps))],
            'close': [100.5 + i * 0.1 for i in range(len(timestamps))],
            'volume': [1000000 + i * 1000 for i in range(len(timestamps))]
        }, index=timestamps)
        
        quality_score = await scorer.score_data_quality(
            symbol="MSFT",
            data=incomplete_data,
            interval_type="1m"
        )
        
        assert quality_score.overall_score < 80  # Should be lower due to gaps
        assert quality_score.completeness_score < 90  # Incomplete data
        assert quality_score.gap_penalty > 0  # Should have gap penalties
    
    @pytest.mark.asyncio
    async def test_score_data_quality_stale_data(self, scorer):
        """Test quality scoring with stale data."""
        # Create old data
        old_timestamp = datetime.now(timezone.utc) - timedelta(hours=5)
        old_data = pd.DataFrame({
            'open': [100],
            'high': [101],
            'low': [99],
            'close': [100.5],
            'volume': [1000000]
        }, index=[old_timestamp])
        
        old_provider_metadata = {
            'provider_name': 'Alpha Vantage',
            'fetched_at': old_timestamp.isoformat(),
            'is_fallback': False
        }
        
        quality_score = await scorer.score_data_quality(
            symbol="GOOGL",
            data=old_data,
            interval_type="1d",
            provider_metadata=old_provider_metadata
        )
        
        assert quality_score.freshness_score < 70  # Should be penalized for age
        assert quality_score.overall_score < quality_score.completeness_score  # Overall score should be lower
    
    @pytest.mark.asyncio
    async def test_score_data_quality_fallback_provider(self, scorer, sample_data):
        """Test quality scoring with fallback provider."""
        fallback_metadata = {
            'provider_name': 'Yahoo Finance',
            'fetched_at': datetime.now(timezone.utc).isoformat(),
            'is_fallback': True,
            'fallback_reason': 'Primary provider timeout'
        }
        
        quality_score = await scorer.score_data_quality(
            symbol="TSLA",
            data=sample_data,
            interval_type="1m",
            provider_metadata=fallback_metadata
        )
        
        assert quality_score.provider_reliability_score < 80  # Should be penalized for fallback
        assert "fallback" in str(quality_score.provider_reliability_score).lower()
    
    @pytest.mark.asyncio
    async def test_score_data_quality_empty_data(self, scorer):
        """Test quality scoring with empty data."""
        empty_data = pd.DataFrame()
        
        quality_score = await scorer.score_data_quality(
            symbol="INVALID",
            data=empty_data,
            interval_type="1d"
        )
        
        assert quality_score.overall_score == 0.0
        assert quality_score.completeness_score == 0.0
        assert quality_score.quality_level == QualityLevel.VERY_POOR
    
    def test_quality_level_determination(self, scorer):
        """Test quality level determination based on scores."""
        assert scorer._determine_quality_level(95.0) == QualityLevel.EXCELLENT
        assert scorer._determine_quality_level(85.0) == QualityLevel.GOOD
        assert scorer._determine_quality_level(75.0) == QualityLevel.FAIR
        assert scorer._determine_quality_level(55.0) == QualityLevel.POOR
        assert scorer._determine_quality_level(25.0) == QualityLevel.VERY_POOR
    
    def test_gap_penalty_calculation(self, scorer):
        """Test gap penalty calculation."""
        # Test with no gaps
        no_gaps = []
        penalty = scorer._calculate_gap_penalty(no_gaps)
        assert penalty == 0.0
        
        # Test with minor gaps
        minor_gaps = [
            {'severity': 'minor', 'duration_seconds': 300}  # 5 minutes
        ]
        penalty = scorer._calculate_gap_penalty(minor_gaps)
        assert penalty > 0.0
        assert penalty < 20.0
        
        # Test with critical gaps
        critical_gaps = [
            {'severity': 'critical', 'duration_seconds': 7200}  # 2 hours
        ]
        penalty = scorer._calculate_gap_penalty(critical_gaps)
        assert penalty > 20.0
    
    def test_weighted_score_calculation(self, scorer):
        """Test weighted score calculation."""
        score = scorer._calculate_weighted_score(
            completeness=90.0,
            freshness=95.0,
            consistency=85.0,
            provider_reliability=80.0,
            gap_penalty=5.0
        )
        
        # Expected: (90*0.35 + 95*0.25 + 85*0.20 + 80*0.20) - 5 = 87.5 - 5 = 82.5
        expected_score = (90 * 0.35 + 95 * 0.25 + 85 * 0.20 + 80 * 0.20) - 5
        assert abs(score - expected_score) < 0.1


class TestQualityScore:
    """Test the QualityScore dataclass."""
    
    def test_quality_score_creation(self):
        """Test creating QualityScore objects."""
        now = datetime.now(timezone.utc)
        
        quality_score = QualityScore(
            symbol="AAPL",
            overall_score=85.5,
            completeness_score=90.0,
            freshness_score=95.0,
            consistency_score=80.0,
            provider_reliability_score=85.0,
            gap_penalty=5.0,
            quality_level=QualityLevel.GOOD,
            timestamp=now,
            interval_type="1d"
        )
        
        assert quality_score.symbol == "AAPL"
        assert quality_score.overall_score == 85.5
        assert quality_score.quality_level == QualityLevel.GOOD
        assert quality_score.timestamp == now
    
    def test_quality_score_to_dict(self):
        """Test converting QualityScore to dictionary."""
        now = datetime.now(timezone.utc)
        
        quality_score = QualityScore(
            symbol="MSFT",
            overall_score=92.3,
            completeness_score=95.0,
            freshness_score=90.0,
            consistency_score=95.0,
            provider_reliability_score=90.0,
            gap_penalty=2.0,
            quality_level=QualityLevel.EXCELLENT,
            timestamp=now,
            interval_type="1h"
        )
        
        score_dict = quality_score.to_dict()
        
        assert score_dict['symbol'] == "MSFT"
        assert score_dict['overall_score'] == 92.3
        assert score_dict['quality_level'] == "excellent"
        assert score_dict['interval_type'] == "1h"
        assert 'timestamp' in score_dict


class TestQualityLevelDescriptions:
    """Test quality level descriptions."""
    
    def test_quality_level_descriptions(self):
        """Test getting descriptions for quality levels."""
        descriptions = [
            get_quality_level_description(QualityLevel.EXCELLENT),
            get_quality_level_description(QualityLevel.GOOD),
            get_quality_level_description(QualityLevel.FAIR),
            get_quality_level_description(QualityLevel.POOR),
            get_quality_level_description(QualityLevel.VERY_POOR)
        ]
        
        # All descriptions should be non-empty strings
        for description in descriptions:
            assert isinstance(description, str)
            assert len(description) > 0
            assert "quality" in description.lower()


class TestQualityRecommendations:
    """Test quality recommendation generation."""
    
    def test_generate_quality_recommendations_excellent(self):
        """Test recommendations for excellent quality data."""
        excellent_score = Mock()
        excellent_score.overall_score = 95.0
        excellent_score.completeness_score = 98.0
        excellent_score.freshness_score = 95.0
        excellent_score.consistency_score = 95.0
        excellent_score.provider_reliability_score = 95.0
        excellent_score.gap_penalty = 0.0
        
        recommendations = _generate_quality_recommendations(excellent_score)
        
        assert len(recommendations) > 0
        assert any("excellent" in rec.lower() for rec in recommendations)
        assert not any("improvement" in rec.lower() for rec in recommendations)
    
    def test_generate_quality_recommendations_poor(self):
        """Test recommendations for poor quality data."""
        poor_score = Mock()
        poor_score.overall_score = 45.0
        poor_score.completeness_score = 60.0
        poor_score.freshness_score = 40.0
        poor_score.consistency_score = 50.0
        poor_score.provider_reliability_score = 30.0
        poor_score.gap_penalty = 25.0
        
        recommendations = _generate_quality_recommendations(poor_score)
        
        assert len(recommendations) > 0
        assert any("critically low" in rec.lower() for rec in recommendations)
        assert any("alternative data sources" in rec.lower() for rec in recommendations)


class TestDataCoverageCalculation:
    """Test data coverage calculation utilities."""
    
    def test_calculate_data_coverage_hours(self):
        """Test calculating data coverage in hours."""
        start_time = datetime(2024, 1, 1, 9, 0, tzinfo=timezone.utc)
        end_time = datetime(2024, 1, 1, 17, 0, tzinfo=timezone.utc)
        
        coverage_hours = _calculate_data_coverage_hours(start_time, end_time)
        
        assert coverage_hours == 8.0  # 8 hours difference
    
    def test_calculate_data_coverage_hours_none_values(self):
        """Test coverage calculation with None values."""
        coverage_hours = _calculate_data_coverage_hours(None, None)
        assert coverage_hours is None


class TestDataQualityAPI:
    """Test the data quality API endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_data_quality_score_success(self):
        """Test successful quality score API call."""
        # Mock the data source manager
        mock_data = [
            Mock(
                to_dict=lambda: {
                    'timestamp': '2024-01-01T09:30:00Z',
                    'open': 100.0,
                    'high': 101.0,
                    'low': 99.0,
                    'close': 100.5,
                    'volume': 1000000
                },
                metadata=Mock(
                    to_dict=lambda: {
                        'provider_name': 'Polygon.io',
                        'fetched_at': datetime.now(timezone.utc).isoformat()
                    }
                )
            )
        ]
        
        with patch('src.api.routes.analytics.DataSourceManager') as mock_manager_class:
            mock_manager = Mock()
            mock_manager.fetch_historical_data = AsyncMock(return_value=mock_data)
            mock_manager_class.return_value = mock_manager
            
            # Mock pandas DataFrame creation
            with patch('pandas.DataFrame') as mock_df_class:
                mock_df = Mock()
                mock_df.empty = False
                mock_df.index = [datetime(2024, 1, 1, 9, 30, tzinfo=timezone.utc)]
                mock_df_class.return_value = mock_df
                
                # Mock quality scoring
                with patch('src.api.routes.analytics.score_data_quality') as mock_score:
                    mock_quality_score = Mock()
                    mock_quality_score.to_dict.return_value = {
                        'overall_score': 85.5,
                        'quality_level': 'good'
                    }
                    mock_quality_score.quality_level.value = 'good'
                    mock_quality_score.data_window_start = datetime(2024, 1, 1, 9, 30, tzinfo=timezone.utc)
                    mock_quality_score.data_window_end = datetime(2024, 1, 1, 17, 30, tzinfo=timezone.utc)
                    mock_score.return_value = mock_quality_score
                    
                    response = await get_data_quality_score("AAPL", "1d", 30, True, True)
                    
                    assert response['symbol'] == "AAPL"
                    assert 'quality_score' in response
                    assert response['quality_score']['overall_score'] == 85.5
    
    @pytest.mark.asyncio
    async def test_get_data_quality_score_no_data(self):
        """Test quality score API when no data is available."""
        with patch('src.api.routes.analytics.DataSourceManager') as mock_manager_class:
            mock_manager = Mock()
            mock_manager.fetch_historical_data = AsyncMock(return_value=[])
            mock_manager_class.return_value = mock_manager
            
            with pytest.raises(Exception) as exc_info:
                await get_data_quality_score("INVALID", "1d", 30, True, True)
            
            assert "No historical data available" in str(exc_info.value)


if __name__ == "__main__":
    pytest.main([__file__]) 