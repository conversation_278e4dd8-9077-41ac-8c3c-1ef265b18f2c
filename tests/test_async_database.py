"""
Tests for async database operations.

This module demonstrates how to use the async_database_connection fixture
for testing database operations with async/await syntax.
"""

import pytest
import asyncio
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession


@pytest.mark.asyncio
async def test_async_database_connection(async_database_connection):
    """Test that the async database connection works."""
    # The fixture provides an AsyncSession
    assert isinstance(async_database_connection, AsyncSession)
    
    # Execute a simple query
    result = await async_database_connection.execute(text("SELECT 1 as value"))
    row = result.fetchone()
    assert row is not None
    assert row[0] == 1


@pytest.mark.asyncio
async def test_async_transaction(async_database_connection):
    """Test async transactions with the database connection."""
    # Start a transaction
    async with async_database_connection.begin():
        # Execute a query within the transaction
        await async_database_connection.execute(
            text("CREATE TEMPORARY TABLE IF NOT EXISTS test_table (id SERIAL PRIMARY KEY, name TEXT)")
        )
        
        # Insert some data
        await async_database_connection.execute(
            text("INSERT INTO test_table (name) VALUES (:name)"),
            {"name": "Test Item"}
        )
        
        # Query the data
        result = await async_database_connection.execute(
            text("SELECT * FROM test_table WHERE name = :name"),
            {"name": "Test Item"}
        )
        
        row = result.fetchone()
        assert row is not None
        assert row[1] == "Test Item"
    
    # Transaction is automatically committed at the end of the context manager
    # or rolled back if an exception occurs


@pytest.mark.asyncio
async def test_multiple_async_queries(async_database_connection):
    """Test running multiple async queries concurrently."""
    # Create a temporary table
    await async_database_connection.execute(
        text("CREATE TEMPORARY TABLE IF NOT EXISTS concurrent_test (id SERIAL PRIMARY KEY, value INTEGER)")
    )
    
    # Define a function to insert a value and return it
    async def insert_and_get(value):
        await async_database_connection.execute(
            text("INSERT INTO concurrent_test (value) VALUES (:value)"),
            {"value": value}
        )
        result = await async_database_connection.execute(
            text("SELECT value FROM concurrent_test WHERE value = :value"),
            {"value": value}
        )
        row = result.fetchone()
        return row[0] if row else None
    
    # Run multiple queries concurrently
    values = [1, 2, 3, 4, 5]
    results = await asyncio.gather(*(insert_and_get(v) for v in values))
    
    # Check results
    assert sorted(results) == values


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
