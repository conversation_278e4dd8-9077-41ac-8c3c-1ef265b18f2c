"""
Test suite for enhanced analysis engines

Tests price targets, probability assessment, and timeframe confirmation
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.analysis.technical.price_targets import (
    PriceTargetEngine, FibonacciCalculator, VolumeProfileAnalyzer, 
    TrendStrengthAnalyzer, HistoricalPatternRecognizer, TrendDirection
)
from src.analysis.probability.probability_engine import (
    ProbabilityEngine, StatisticalModels, RiskAdjustedCalculator,
    MarketRegimeDetector, SentimentCorrelator, HistoricalAccuracyTracker,
    MarketRegime
)
from src.analysis.technical.timeframe_confirmation import (
    TimeframeConfirmationAnalyzer, TimeframeAnalyzer, TimeframeBias
)

class TestPriceTargetEngine:
    """Test price target engine functionality"""
    
    def setup_method(self):
        """Set up test data"""
        # Create sample price data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # Generate realistic price data with trend
        base_price = 100
        trend = np.linspace(0, 20, 100)  # Upward trend
        noise = np.random.normal(0, 2, 100)
        prices = base_price + trend + noise
        
        self.price_data = pd.DataFrame({
            'open': prices * 0.99,
            'high': prices * 1.02,
            'low': prices * 0.98,
            'close': prices,
            'volume': np.random.randint(100000, 1000000, 100)
        }, index=dates)
        
        self.volume_data = pd.DataFrame({
            'volume': np.random.randint(100000, 1000000, 100)
        }, index=dates)
        
        self.engine = PriceTargetEngine()
    
    def test_fibonacci_calculator(self):
        """Test Fibonacci level calculations"""
        calc = FibonacciCalculator()
        
        # Test bullish trend
        levels = calc.calculate_levels(120, 100, TrendDirection.BULLISH)
        assert len(levels) > 0
        assert 'retracement_0.618' in levels
        assert 'extension_1.618' in levels
        
        # Test bearish trend
        levels = calc.calculate_levels(100, 80, TrendDirection.BEARISH)
        assert len(levels) > 0
        assert 'retracement_0.618' in levels
        assert 'extension_1.618' in levels
    
    def test_volume_profile_analyzer(self):
        """Test volume profile analysis"""
        analyzer = VolumeProfileAnalyzer()
        profile = analyzer.analyze_volume_profile(self.price_data, self.volume_data)
        
        assert 'vwap' in profile
        assert 'high_volume_threshold' in profile
        assert 'volume_peaks' in profile
    
    def test_trend_strength_analyzer(self):
        """Test trend strength analysis"""
        analyzer = TrendStrengthAnalyzer()
        direction, strength = analyzer.calculate_trend_strength(self.price_data, '1d')
        
        assert direction in TrendDirection
        assert 0.0 <= strength <= 1.0
    
    def test_price_target_calculation(self):
        """Test complete price target calculation"""
        targets = self.engine.calculate_targets(
            'TEST', '1d', self.price_data, self.volume_data
        )
        
        assert targets.symbol == 'TEST'
        assert targets.timeframe == '1d'
        assert targets.current_price > 0
        assert targets.conservative_target > 0
        assert targets.moderate_target > 0
        assert targets.aggressive_target > 0
        assert targets.stop_loss > 0
        assert len(targets.confidence_factors) > 0
        assert targets.trend_direction in TrendDirection
        assert 0.0 <= targets.trend_strength <= 1.0
        assert targets.volatility > 0
        assert len(targets.support_levels) > 0
        assert len(targets.resistance_levels) > 0

class TestProbabilityEngine:
    """Test probability engine functionality"""
    
    def setup_method(self):
        """Set up test data"""
        # Create sample price data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 100
        trend = np.linspace(0, 10, 100)  # Moderate upward trend
        noise = np.random.normal(0, 1.5, 100)
        prices = base_price + trend + noise
        
        self.price_data = pd.DataFrame({
            'open': prices * 0.99,
            'high': prices * 1.01,
            'low': prices * 0.99,
            'close': prices,
            'volume': np.random.randint(100000, 1000000, 100)
        }, index=dates)
        
        self.volume_data = pd.DataFrame({
            'volume': np.random.randint(100000, 1000000, 100)
        }, index=dates)
        
        self.engine = ProbabilityEngine()
    
    def test_statistical_models(self):
        """Test statistical probability models"""
        models = StatisticalModels()
        
        # Test normal probability
        prob = models.calculate_normal_probability(100, 100, 10, 110)
        assert 0.0 <= prob <= 1.0
        
        # Test trend probability
        prob = models.calculate_trend_probability(self.price_data, "bullish")
        assert 0.0 <= prob <= 1.0
    
    def test_risk_adjusted_calculator(self):
        """Test risk-adjusted calculations"""
        calc = RiskAdjustedCalculator()
        
        # Test Sharpe ratio
        returns = pd.Series([0.01, 0.02, -0.01, 0.03, -0.02])
        sharpe = calc.calculate_sharpe_ratio(returns)
        assert isinstance(sharpe, float)
        
        # Test max drawdown
        prices = pd.Series([100, 110, 105, 120, 115])
        drawdown = calc.calculate_max_drawdown(prices)
        assert drawdown >= 0.0
    
    def test_market_regime_detector(self):
        """Test market regime detection"""
        detector = MarketRegimeDetector()
        regime, confidence = detector.detect_regime(self.price_data, self.volume_data)
        
        assert regime in MarketRegime
        assert 0.0 <= confidence <= 1.0
    
    def test_sentiment_correlator(self):
        """Test sentiment correlation"""
        correlator = SentimentCorrelator()
        
        base_probs = {'bullish': 0.4, 'bearish': 0.3, 'sideways': 0.3}
        
        # Test bullish sentiment
        adjusted = correlator.correlate_sentiment(0.5, base_probs)
        assert adjusted['bullish'] >= base_probs['bullish']
        
        # Test bearish sentiment
        adjusted = correlator.correlate_sentiment(-0.5, base_probs)
        assert adjusted['bearish'] >= base_probs['bearish']
    
    def test_historical_accuracy_tracker(self):
        """Test historical accuracy tracking"""
        tracker = HistoricalAccuracyTracker()
        
        # Add some test predictions
        now = datetime.now()
        tracker.add_prediction('TEST', 'bullish', 'bullish', 0.8, now)
        tracker.add_prediction('TEST', 'bearish', 'bullish', 0.7, now)
        
        # Test accuracy calculation
        accuracy = tracker.calculate_accuracy('TEST', days=1)
        assert 0.0 <= accuracy <= 1.0
        
        # Test calibration
        calibration = tracker.get_confidence_calibration('TEST')
        assert 0.0 <= calibration <= 1.0
    
    def test_probability_assessment(self):
        """Test complete probability assessment"""
        assessment = self.engine.assess_probabilities(
            'TEST', '1d', self.price_data, self.volume_data, 0.2
        )
        
        assert assessment.symbol == 'TEST'
        assert assessment.timeframe == '1d'
        assert 0.0 <= assessment.bullish_probability <= 1.0
        assert 0.0 <= assessment.bearish_probability <= 1.0
        assert 0.0 <= assessment.sideways_probability <= 1.0
        assert 0.0 <= assessment.confidence_level <= 1.0
        assert len(assessment.supporting_factors) > 0
        assert len(assessment.risk_factors) > 0
        assert assessment.market_regime in MarketRegime
        assert 0.0 <= assessment.regime_confidence <= 1.0
        assert isinstance(assessment.risk_adjusted_return, float)
        assert 0.0 <= assessment.historical_accuracy <= 1.0

class TestTimeframeConfirmation:
    """Test timeframe confirmation functionality"""
    
    def setup_method(self):
        """Set up test data"""
        # Create sample data for multiple timeframes
        dates_1d = pd.date_range(start='2024-01-01', periods=50, freq='D')
        dates_4h = pd.date_range(start='2024-01-01', periods=200, freq='4H')
        dates_1h = pd.date_range(start='2024-01-01', periods=800, freq='H')
        
        np.random.seed(42)
        
        # Generate data for each timeframe
        def generate_data(dates, trend_strength=0.1):
            base_price = 100
            trend = np.linspace(0, len(dates) * trend_strength, len(dates))
            noise = np.random.normal(0, 1, len(dates))
            prices = base_price + trend + noise
            
            return pd.DataFrame({
                'open': prices * 0.99,
                'high': prices * 1.01,
                'low': prices * 0.99,
                'close': prices,
                'volume': np.random.randint(100000, 1000000, len(dates))
            }, index=dates)
        
        self.price_data = {
            '1d': generate_data(dates_1d, 0.2),
            '4h': generate_data(dates_4h, 0.15),
            '1h': generate_data(dates_1h, 0.1)
        }
        
        self.volume_data = {
            '1d': pd.DataFrame({'volume': np.random.randint(100000, 1000000, 50)}),
            '4h': pd.DataFrame({'volume': np.random.randint(100000, 1000000, 200)}),
            '1h': pd.DataFrame({'volume': np.random.randint(100000, 1000000, 800)})
        }
        
        self.analyzer = TimeframeConfirmationAnalyzer()
    
    def test_individual_timeframe_analysis(self):
        """Test individual timeframe analysis"""
        analyzer = TimeframeAnalyzer()
        
        # Test 1d timeframe
        analysis = analyzer.analyze_timeframe(
            'TEST', '1d', self.price_data['1d'], self.volume_data['1d']
        )
        
        assert analysis.symbol == 'TEST'
        assert analysis.timeframe == '1d'
        assert analysis.bias in TimeframeBias
        assert analysis.signal_strength.value in ['very_weak', 'weak', 'moderate', 'strong', 'very_strong']
        assert 0.0 <= analysis.confidence <= 1.0
        assert len(analysis.key_levels) > 0
        assert analysis.trend_direction in ['uptrend', 'downtrend', 'sideways', 'unknown']
        assert isinstance(analysis.momentum, float)
        assert isinstance(analysis.volume_profile, dict)
    
    def test_timeframe_confirmation(self):
        """Test multi-timeframe confirmation"""
        confirmation = self.analyzer.get_timeframe_agreement(
            'TEST', self.price_data, self.volume_data
        )
        
        assert confirmation.symbol == 'TEST'
        assert confirmation.short_term_bias in TimeframeBias
        assert confirmation.medium_term_bias in TimeframeBias
        assert confirmation.long_term_bias in TimeframeBias
        assert 0.0 <= confirmation.agreement_score <= 1.0
        assert isinstance(confirmation.conflicting_signals, list)
        assert isinstance(confirmation.weighted_probability, dict)
        assert 0.0 <= confirmation.overall_confidence <= 1.0
        assert len(confirmation.timeframe_analysis) > 0
        assert isinstance(confirmation.recommendation, str)

class TestIntegration:
    """Test integration between all components"""
    
    def test_end_to_end_analysis(self):
        """Test complete end-to-end analysis flow"""
        # This test would require more complex setup and mocking
        # For now, we'll test that all components can be imported and instantiated
        
        # Test price target engine
        price_engine = PriceTargetEngine()
        assert price_engine is not None
        
        # Test probability engine
        prob_engine = ProbabilityEngine()
        assert prob_engine is not None
        
        # Test timeframe analyzer
        timeframe_analyzer = TimeframeConfirmationAnalyzer()
        assert timeframe_analyzer is not None
        
        # Test that all required methods exist
        assert hasattr(price_engine, 'calculate_targets')
        assert hasattr(prob_engine, 'assess_probabilities')
        assert hasattr(timeframe_analyzer, 'get_timeframe_agreement')

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"]) 