import pytest
from src.core.response_generator import ResponseGenerator, ResponseType
from datetime import datetime

class TestResponseGenerator:
    def test_fallback_response_with_symbols(self):
        """Test fallback response generation with symbols"""
        context = {
            'intent': 'stock_analysis',
            'symbols': ['AAPL', 'GOOGL']
        }
        response = ResponseGenerator.generate_response(
            response_type=ResponseType.FALLBACK,
            context=context
        )
        
        assert response['response_type'] == 'fallback'
        assert 'AAPL' in response['response']
        assert 'GOOGL' in response['response']
        assert 'stock_analysis' in response['response']
    
    def test_fallback_response_without_symbols(self):
        """Test fallback response generation without symbols"""
        context = {
            'intent': 'general_question'
        }
        response = ResponseGenerator.generate_response(
            response_type=ResponseType.FALLBACK,
            context=context
        )
        
        assert response['response_type'] == 'fallback'
        assert 'general_question' in response['response']
        assert 'stocks' in response['response']
    
    def test_error_response(self):
        """Test error response generation"""
        class TestError(Exception):
            pass
        
        error = TestError("Test error message")
        context = {
            'intent': 'error_handling'
        }
        
        response = ResponseGenerator.generate_response(
            response_type=ResponseType.ERROR,
            context=context,
            error=error
        )
        
        assert response['response_type'] == 'error'
        assert 'Test error message' in response['response']
        assert response['error_details'] is not None
    
    def test_response_metadata(self):
        """Test response metadata generation"""
        context = {
            'intent': 'market_trend',
            'symbols': ['SPY'],
            'confidence': 0.85
        }
        
        response = ResponseGenerator.generate_response(
            response_type=ResponseType.ANALYSIS,
            context=context
        )
        
        assert response['metadata']['intent'] == 'market_trend'
        assert response['metadata']['symbols'] == ['SPY']
        assert response['metadata']['confidence'] == 0.85
        assert 'timestamp' in response['metadata']
    
    def test_multiple_response_types(self):
        """Test generation of different response types"""
        response_types = [
            ResponseType.FALLBACK,
            ResponseType.ANALYSIS,
            ResponseType.ERROR,
            ResponseType.KNOWLEDGE,
            ResponseType.DATA_DRIVEN
        ]
        
        for response_type in response_types:
            response = ResponseGenerator.generate_response(
                response_type=response_type,
                context={'intent': 'test'}
            )
            
            assert response['response_type'] == response_type.name.lower()
            assert 'response' in response
            assert 'metadata' in response 