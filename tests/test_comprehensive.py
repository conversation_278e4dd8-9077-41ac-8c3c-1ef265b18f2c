"""
Comprehensive Test Suite for AI Trading Discord Bot
Tests all components, integrations, and performance optimizations
"""

import pytest
import asyncio
import time
import os
import sys
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import all components to test
from src.bot.utils.rate_limiter import ThreadSafeRateLimiter
from src.bot.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorCategory
from src.bot.watchlist_manager import WatchlistManager
from src.bot.pipeline.performance_optimizer import IntelligentCache, pipeline_optimizer
from src.bot.enhancements.discord_ux import ProgressTracker, InteractiveEmbeds
from src.bot.monitoring.health_monitor import HealthChecker, HealthStatus
from src.bot.security.advanced_security import AdvancedSecurityManager, InputValidator

class TestRateLimiter:
    """Test thread-safe rate limiter"""
    
    @pytest.mark.asyncio
    async def test_rate_limiter_basic_functionality(self):
        """Test basic rate limiting functionality"""
        limiter = ThreadSafeRateLimiter(max_requests=5, time_window=60)
        user_id = "test_user_123"
        
        # Should allow first 5 requests
        for i in range(5):
            can_request = await limiter.can_make_request(user_id)
            assert can_request == True, f"Request {i+1} should be allowed"
            await limiter.record_request(user_id)
        
        # 6th request should be denied
        can_request = await limiter.can_make_request(user_id)
        assert can_request == False, "6th request should be denied"
    
    @pytest.mark.asyncio
    async def test_rate_limiter_concurrency(self):
        """Test rate limiter under concurrent load"""
        limiter = ThreadSafeRateLimiter(max_requests=10, time_window=60)
        user_id = "concurrent_test_user"
        
        async def make_request():
            if await limiter.can_make_request(user_id):
                await limiter.record_request(user_id)
                return True
            return False
        
        # Run 20 concurrent requests
        tasks = [make_request() for _ in range(20)]
        results = await asyncio.gather(*tasks)
        
        # Should have exactly 10 successful requests
        successful = sum(results)
        assert successful == 10, f"Expected 10 successful requests, got {successful}"

class TestErrorHandler:
    """Test standardized error handling"""
    
    def test_error_categorization(self):
        """Test error categorization and logging"""
        test_error = ValueError("Test error")
        
        error_details = ErrorHandler.log_error(
            error=test_error,
            category=ErrorCategory.VALIDATION,
            context={"test": "context"},
            user_id="test_user"
        )
        
        assert error_details['error_type'] == 'ValueError'
        assert error_details['error_message'] == 'Test error'
        assert error_details['category'] == ErrorCategory.VALIDATION
        assert 'error_id' in error_details
    
    def test_user_friendly_messages(self):
        """Test user-friendly error message generation"""
        timeout_error = asyncio.TimeoutError("Operation timed out")
        message = ErrorHandler.get_user_friendly_message(timeout_error, ErrorCategory.TIMEOUT)
        
        assert "took too long" in message.lower()
        assert "try again" in message.lower()

class TestWatchlistManager:
    """Test watchlist manager functionality"""
    
    @pytest.fixture
    def mock_db_pool(self):
        """Mock database pool"""
        pool = Mock()
        conn = AsyncMock()
        pool.acquire.return_value.__aenter__.return_value = conn
        return pool, conn
    
    @pytest.mark.asyncio
    async def test_watchlist_initialization(self, mock_db_pool):
        """Test watchlist manager initialization"""
        pool, conn = mock_db_pool
        
        # Test proper initialization
        manager = WatchlistManager(pool)
        assert manager.db_pool == pool
        assert manager._initialized == True
        
        # Test null pool rejection
        with pytest.raises(ValueError):
            WatchlistManager(None)
    
    @pytest.mark.asyncio
    async def test_create_watchlist(self, mock_db_pool):
        """Test watchlist creation"""
        pool, conn = mock_db_pool
        conn.fetchrow.return_value = {'id': 123}
        
        manager = WatchlistManager(pool)
        result = await manager.create_watchlist("user123", "Test Watchlist")
        
        assert result == 123
        conn.fetchrow.assert_called_once()

class TestPerformanceOptimizer:
    """Test performance optimization features"""
    
    @pytest.mark.asyncio
    async def test_intelligent_cache(self):
        """Test intelligent caching system"""
        cache = IntelligentCache(max_size=5, default_ttl=1)
        
        # Test cache set/get
        await cache.set("test_query", "test_result")
        result = await cache.get("test_query")
        assert result == "test_result"
        
        # Test cache miss
        result = await cache.get("nonexistent_query")
        assert result is None
        
        # Test TTL expiration
        await cache.set("expire_test", "data", ttl=1)
        await asyncio.sleep(1.1)  # Wait for expiration
        result = await cache.get("expire_test")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_cache_eviction(self):
        """Test LRU cache eviction"""
        cache = IntelligentCache(max_size=3, default_ttl=60)
        
        # Fill cache to capacity
        await cache.set("key1", "value1")
        await cache.set("key2", "value2")
        await cache.set("key3", "value3")
        
        # Access key1 to make it recently used
        await cache.get("key1")
        
        # Add new item, should evict key2 (least recently used)
        await cache.set("key4", "value4")
        
        assert await cache.get("key1") == "value1"  # Should still exist
        assert await cache.get("key2") is None      # Should be evicted
        assert await cache.get("key3") == "value3"  # Should still exist
        assert await cache.get("key4") == "value4"  # Should exist

class TestDiscordUX:
    """Test Discord UX enhancements"""
    
    def test_interactive_embeds(self):
        """Test enhanced embed creation"""
        mock_user = Mock()
        mock_user.display_name = "TestUser"
        mock_user.display_avatar.url = "http://example.com/avatar.png"
        
        data = {
            'current_price': 150.50,
            'price_change': 5.25,
            'change_percent': 3.5,
            'sentiment': 'bullish',
            'analysis': 'Stock is performing well'
        }
        
        embed = InteractiveEmbeds.create_analysis_embed("AAPL", data, mock_user)
        
        assert embed.title == "📊 AAPL Analysis"
        assert len(embed.fields) > 0
        assert any("$150.50" in field.value for field in embed.fields)

class TestHealthMonitor:
    """Test health monitoring system"""
    
    @pytest.mark.asyncio
    async def test_health_checker(self):
        """Test health checking functionality"""
        checker = HealthChecker()
        
        # Test system resource checking
        metrics = await checker.check_system_resources()
        
        assert 'cpu_usage' in metrics
        assert 'memory_usage' in metrics
        assert 'disk_usage' in metrics
        
        # Verify metric structure
        cpu_metric = metrics['cpu_usage']
        assert hasattr(cpu_metric, 'name')
        assert hasattr(cpu_metric, 'value')
        assert hasattr(cpu_metric, 'status')
        assert cpu_metric.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]

class TestAdvancedSecurity:
    """Test security hardening features"""
    
    def test_input_validation(self):
        """Test input validation and sanitization"""
        # Test valid input
        valid, sanitized, error = InputValidator.validate_user_input("AAPL analysis please")
        assert valid == True
        assert error == ""
        
        # Test dangerous input
        valid, sanitized, error = InputValidator.validate_user_input("<script>alert('xss')</script>")
        assert valid == False
        assert "dangerous" in error.lower()
        
        # Test SQL injection
        valid, sanitized, error = InputValidator.validate_user_input("'; DROP TABLE users; --")
        assert valid == False
        assert "injection" in error.lower()
    
    def test_symbol_validation(self):
        """Test stock symbol validation"""
        # Valid symbols
        valid, error = InputValidator.validate_symbol("AAPL")
        assert valid == True
        
        valid, error = InputValidator.validate_symbol("TSLA")
        assert valid == True
        
        # Invalid symbols
        valid, error = InputValidator.validate_symbol("invalid123")
        assert valid == False
        
        valid, error = InputValidator.validate_symbol("")
        assert valid == False
    
    @pytest.mark.asyncio
    async def test_rate_limiting_security(self):
        """Test security-focused rate limiting"""
        security_manager = AdvancedSecurityManager()
        
        # Test normal usage
        valid, message = await security_manager.validate_request("user123", input_data="AAPL analysis")
        assert valid == True
        
        # Test rate limit enforcement
        for _ in range(65):  # Exceed per-minute limit
            await security_manager.validate_request("user123", input_data="test")
        
        valid, message = await security_manager.validate_request("user123", input_data="test")
        assert valid == False
        assert "rate limit" in message.lower()

class TestIntegration:
    """Integration tests for component interactions"""
    
    @pytest.mark.asyncio
    async def test_pipeline_with_optimization(self):
        """Test AI pipeline with performance optimization"""
        # Mock the pipeline execution
        async def mock_pipeline_execution():
            await asyncio.sleep(0.1)  # Simulate processing time
            return {"response": "Test analysis result", "confidence": 0.85}
        
        # Test with optimization
        from bot.pipeline.performance_optimizer import optimize_pipeline_execution
        
        query = "Analyze AAPL stock"
        context = {"user_id": "test_user", "command": "analyze"}
        
        start_time = time.time()
        result1 = await optimize_pipeline_execution(query, mock_pipeline_execution, context)
        first_execution_time = time.time() - start_time
        
        # Second execution should be faster (cached)
        start_time = time.time()
        result2 = await optimize_pipeline_execution(query, mock_pipeline_execution, context)
        second_execution_time = time.time() - start_time
        
        assert result1 == result2
        assert second_execution_time < first_execution_time  # Should be cached and faster

# Performance benchmarks
class TestPerformanceBenchmarks:
    """Performance benchmark tests"""
    
    @pytest.mark.asyncio
    async def test_response_time_benchmark(self):
        """Test response time is under 2 seconds"""
        cache = IntelligentCache()
        
        # Simulate complex query processing
        async def complex_query():
            await asyncio.sleep(0.5)  # Simulate AI processing
            return "Complex analysis result"
        
        start_time = time.time()
        result = await complex_query()
        execution_time = time.time() - start_time
        
        assert execution_time < 2.0, f"Response time {execution_time}s exceeds 2s target"
    
    @pytest.mark.asyncio
    async def test_concurrent_user_handling(self):
        """Test handling of multiple concurrent users"""
        rate_limiter = ThreadSafeRateLimiter(max_requests=100, time_window=60)
        
        async def simulate_user_request(user_id):
            if await rate_limiter.can_make_request(f"user_{user_id}"):
                await rate_limiter.record_request(f"user_{user_id}")
                return True
            return False
        
        # Simulate 50 concurrent users
        tasks = [simulate_user_request(i) for i in range(50)]
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        execution_time = time.time() - start_time
        
        successful_requests = sum(results)
        assert successful_requests == 50, f"Only {successful_requests}/50 requests succeeded"
        assert execution_time < 5.0, f"Concurrent handling took {execution_time}s, should be <5s"

if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
