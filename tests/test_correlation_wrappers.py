"""
Test Correlation ID Wrappers

Tests the database and AI service wrappers to ensure correlation IDs flow through all operations.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from src.database.query_wrapper import execute_query_with_correlation, execute_many_with_correlation, transaction_with_correlation
from src.bot.pipeline.commands.ask.stages.ai_service_wrapper import call_ai_with_correlation, validate_ai_response_with_correlation


class TestDatabaseCorrelationWrappers:
    """Test database operations with correlation IDs"""
    
    @pytest.mark.asyncio
    async def test_execute_query_with_correlation(self):
        """Test that database queries include correlation ID logging"""
        correlation_id = "test-corr-123"
        
        # Mock the database connection - patch the import from connection module
        with patch('src.database.query_wrapper.get_db_connection') as mock_get_conn:
            # Create a proper async context manager mock
            mock_conn = AsyncMock()
            mock_result = MagicMock()
            mock_result.rowcount = 5
            mock_conn.execute.return_value = mock_result
            
            # Set up the async context manager properly
            mock_get_conn.return_value.__aenter__.return_value = mock_conn
            mock_get_conn.return_value.__aexit__.return_value = None
            
            # Execute query with correlation ID
            result = await execute_query_with_correlation(
                "SELECT * FROM test_table",
                correlation_id=correlation_id,
                query_type="select"
            )
            
            # Verify the query was executed
            mock_conn.execute.assert_called_once_with("SELECT * FROM test_table")
            assert result == mock_result
    
    @pytest.mark.asyncio
    async def test_execute_many_with_correlation(self):
        """Test that batch database operations include correlation ID logging"""
        correlation_id = "test-corr-456"
        params_list = [{"id": 1}, {"id": 2}]
        
        # Mock the database connection - patch the import from connection module
        with patch('src.database.query_wrapper.get_db_connection') as mock_get_conn:
            mock_conn = AsyncMock()
            mock_result = MagicMock()
            mock_result.rowcount = 2
            mock_conn.executemany.return_value = mock_result
            mock_get_conn.return_value.__aenter__.return_value = mock_conn
            mock_get_conn.return_value.__aexit__.return_value = None
            
            # Execute batch operation with correlation ID
            result = await execute_many_with_correlation(
                "INSERT INTO test_table (id) VALUES (%(id)s)",
                params_list,
                correlation_id=correlation_id,
                query_type="batch_insert"
            )
            
            # Verify the batch operation was executed
            mock_conn.executemany.assert_called_once_with(
                "INSERT INTO test_table (id) VALUES (%(id)s)",
                params_list
            )
            assert result == mock_result
    
    @pytest.mark.asyncio
    async def test_transaction_with_correlation(self):
        """Test that database transactions include correlation ID logging"""
        correlation_id = "test-corr-789"
        
        # Mock the database connection - patch the import from connection module
        with patch('src.database.query_wrapper.get_db_connection') as mock_get_conn:
            mock_conn = AsyncMock()
            mock_transaction = AsyncMock()
            mock_conn.transaction.return_value.__aenter__.return_value = mock_transaction
            mock_conn.transaction.return_value.__aexit__.return_value = None
            mock_get_conn.return_value.__aenter__.return_value = mock_conn
            mock_get_conn.return_value.__aexit__.return_value = None
            
            # Use transaction with correlation ID
            async with transaction_with_correlation(correlation_id, "test_transaction"):
                pass
            
            # Verify transaction was used
            mock_conn.transaction.assert_called_once()


class TestAICorrelationWrappers:
    """Test AI service operations with correlation IDs"""
    
    @pytest.mark.asyncio
    async def test_call_ai_with_correlation(self):
        """Test that AI calls include correlation ID logging"""
        correlation_id = "test-corr-ai-123"
        prompt = "What is the price of AAPL?"
        
        # Mock the AI processor
        with patch('src.bot.pipeline.commands.ask.stages.ai_service_wrapper.AIChatProcessor') as mock_ai_class:
            mock_ai_processor = AsyncMock()
            mock_response = {"response": "AAPL is trading at $150", "intent": "price_check"}
            mock_ai_processor.process.return_value = mock_response
            mock_ai_class.return_value = mock_ai_processor
            
            # Make AI call with correlation ID
            result = await call_ai_with_correlation(
                prompt,
                correlation_id=correlation_id,
                operation_type="price_query"
            )
            
            # Verify the AI call was made
            mock_ai_processor.process.assert_called_once_with(prompt)
            assert result == mock_response
    
    @pytest.mark.asyncio
    async def test_validate_ai_response_with_correlation(self):
        """Test that AI response validation includes correlation ID logging"""
        correlation_id = "test-corr-validate-456"
        response = {
            "response": "AAPL analysis",
            "intent": "stock_analysis",
            "symbols": ["AAPL"]
        }
        
        # Validate response with correlation ID
        result = await validate_ai_response_with_correlation(
            response,
            correlation_id=correlation_id,
            validation_type="response_validation"
        )
        
        # Verify validation result
        assert result["is_valid"] is True
        assert result["issues"] == []
        assert len(result["warnings"]) == 0
    
    @pytest.mark.asyncio
    async def test_validate_ai_response_with_issues(self):
        """Test AI response validation with missing fields"""
        correlation_id = "test-corr-validate-789"
        response = {
            "response": "Short",  # Too short
            # Missing intent field
        }
        
        # Validate response with correlation ID
        result = await validate_ai_response_with_correlation(
            response,
            correlation_id=correlation_id,
            validation_type="response_validation"
        )
        
        # Verify validation found issues
        assert result["is_valid"] is False
        assert "Missing required field: intent" in result["issues"]
        assert "Response seems very short" in result["warnings"]


class TestCorrelationIDFlow:
    """Test end-to-end correlation ID flow"""
    
    @pytest.mark.asyncio
    async def test_correlation_id_propagation(self):
        """Test that correlation IDs flow through the entire pipeline"""
        correlation_id = "end-to-end-test-123"
        
        # Test database operation
        with patch('src.database.query_wrapper.get_db_connection') as mock_get_conn:
            mock_conn = AsyncMock()
            mock_result = MagicMock()
            mock_result.rowcount = 1
            mock_conn.execute.return_value = mock_result
            mock_get_conn.return_value.__aenter__.return_value = mock_conn
            mock_get_conn.return_value.__aexit__.return_value = None
            
            db_result = await execute_query_with_correlation(
                "SELECT 1",
                correlation_id=correlation_id,
                query_type="test"
            )
        
        # Test AI operation
        with patch('src.bot.pipeline.commands.ask.stages.ai_service_wrapper.AIChatProcessor') as mock_ai_class:
            mock_ai_processor = AsyncMock()
            mock_ai_response = {"response": "Test response", "intent": "test"}
            mock_ai_processor.process.return_value = mock_ai_response
            mock_ai_class.return_value = mock_ai_processor
            
            ai_result = await call_ai_with_correlation(
                "Test prompt",
                correlation_id=correlation_id,
                operation_type="test"
            )
        
        # Test validation
        validation_result = await validate_ai_response_with_correlation(
            ai_result,
            correlation_id=correlation_id,
            validation_type="test"
        )
        
        # Verify all operations completed successfully
        assert db_result == mock_result
        assert ai_result == mock_ai_response
        assert validation_result["is_valid"] is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 