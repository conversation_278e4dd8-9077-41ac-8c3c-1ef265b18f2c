"""
Test for the mock fix functionality.

This module tests that the fix for the execute_ask_pipeline mock issue works correctly.
"""

import unittest
import unittest.mock
from src.utils.fix_mock_issue import is_mock_object, restore_original_function, fix_execute_ask_pipeline_mock, verify_function_integrity

class TestMockFix(unittest.TestCase):
    """Test cases for the mock fix functionality."""
    
    def test_is_mock_object_with_mock(self):
        """Test that is_mock_object correctly identifies mock objects."""
        mock = unittest.mock.Mock()
        self.assertTrue(is_mock_object(mock))
        
        magic_mock = unittest.mock.MagicMock()
        self.assertTrue(is_mock_object(magic_mock))
    
    def test_is_mock_object_with_non_mock(self):
        """Test that is_mock_object correctly identifies non-mock objects."""
        self.assertFalse(is_mock_object("string"))
        self.assertFalse(is_mock_object(123))
        self.assertFalse(is_mock_object([]))
        self.assertFalse(is_mock_object({}))
        
        # Test with a real function
        def test_function():
            pass
        self.assertFalse(is_mock_object(test_function))
    
    def test_restore_original_function_with_valid_module(self):
        """Test that restore_original_function works with a valid module."""
        # This test just verifies the function doesn't crash with a valid module
        result = restore_original_function('sys', 'exit')
        self.assertIsInstance(result, bool)
    
    def test_fix_execute_ask_pipeline_mock(self):
        """Test that fix_execute_ask_pipeline_mock runs without error."""
        # This test just verifies the function doesn't crash
        result = fix_execute_ask_pipeline_mock()
        self.assertIsInstance(result, bool)
    
    def test_verify_function_integrity(self):
        """Test that verify_function_integrity runs without error."""
        # This test just verifies the function doesn't crash
        result = verify_function_integrity()
        self.assertIsInstance(result, bool)

if __name__ == "__main__":
    unittest.main()