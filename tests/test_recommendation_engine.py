import pytest
from datetime import datetime, timezone
from unittest.mock import Mock, patch
from src.analysis.ai.recommendation_engine import AIRecommendationEngine, Recommendation
from src.data.models.stock_data import (
    AnalysisResult, StockQuote, TechnicalIndicators, FundamentalMetrics, 
    RiskAssessment, MarketContext, HistoricalData
)

def create_mock_analysis_result(
    symbol="AAPL",
    technical_data=None,
    fundamental_data=None,
    risk_data=None,
    market_context_data=None,
    quote_data=None,
    historical_data=None
):
    """Create a mock AnalysisResult for testing"""
    return AnalysisResult(
        symbol=symbol,
        timestamp=datetime.now(timezone.utc),
        quote=quote_data or StockQuote(
            symbol="AAPL",
            price=150.0,
            volume=1000000,
            timestamp=datetime.now(timezone.utc),
            change=2.5,
            change_percent=1.67,
            open=148.0,
            high=152.0,
            low=147.5,
            close=150.0,
            market_cap=2.5e12,
            pe_ratio=25.0,
            eps=6.0
        ),
        historical=historical_data or HistoricalData(
            symbol="AAPL",
            dates=[datetime.now(timezone.utc)],
            prices=[150.0],
            volumes=[1000000],
            highs=[152.0],
            lows=[147.5],
            opens=[148.0],
            closes=[150.0]
        ),
        technical=technical_data or TechnicalIndicators(
            rsi=45.0,
            macd={'histogram': 0.5},
            sma_50=145.0,
            sma_200=140.0,
            ema_12=146.0,
            ema_26=144.0,
            bollinger_bands={'upper': 155.0, 'middle': 150.0, 'lower': 145.0},
            support_level=145.0,
            resistance_level=155.0,
            volume_sma=900000.0
        ),
        fundamental=fundamental_data or FundamentalMetrics(
            pe_ratio=25.0,
            eps=6.0,
            revenue_growth=0.15,
            profit_margin=0.25,
            debt_to_equity=0.5,
            return_on_equity=0.20,
            price_to_book=5.0,
            peg_ratio=1.5,
            dividend_yield=0.015,
            free_cash_flow=1e9
        ),
        risk=risk_data or RiskAssessment(
            volatility=0.25,
            beta=1.2,
            risk_warnings=[],
            stop_loss_recommendation=140.0,
            risk_level="MEDIUM"
        ),
        market_context=market_context_data or MarketContext(
            sector="Technology",
            industry="Consumer Electronics",
            market_trend="BULLISH",
            sector_performance=0.08,
            market_sentiment="BULLISH"
        ),
        recommendation="HOLD",
        confidence=60,
        time_horizon="MEDIUM",
        key_insights=["Strong fundamentals", "Good technicals"],
        summary="Solid investment with moderate risk",
        data_sources=["Yahoo Finance", "Alpha Vantage"],
        analysis_quality="HIGH",
        confidence_factors={"technical": 70, "fundamental": 80, "risk": 60}
    )

def test_recommendation_engine_initialization():
    """Test that the recommendation engine initializes correctly"""
    engine = AIRecommendationEngine()
    assert engine.technical_calc is not None
    assert engine.fundamental_calc is not None
    assert engine.base_weights == {
        'technical': 0.40,
        'fundamental': 0.35,
        'risk': 0.20,
        'sentiment': 0.05
    }
    assert engine.ml_model == "heuristic"

def test_recommendation_engine_with_custom_weights():
    """Test initialization with custom weights"""
    custom_weights = {
        'technical': 0.50,
        'fundamental': 0.30,
        'risk': 0.15,
        'sentiment': 0.05
    }
    engine = AIRecommendationEngine(weights=custom_weights)
    assert engine.base_weights == custom_weights

def test_generate_recommendation_buy_signal():
    """Test generating a BUY recommendation"""
    engine = AIRecommendationEngine()
    analysis = create_mock_analysis_result()
    
    # Modify analysis to create strong buy signals
    analysis.technical.rsi = 25.0  # Oversold
    analysis.technical.macd = {'histogram': 2.0}  # Strong bullish momentum
    analysis.fundamental.revenue_growth = 0.25  # High growth
    analysis.market_context.market_sentiment = "VERY_BULLISH"
    
    recommendation = engine.generate_recommendation(analysis)
    
    assert isinstance(recommendation, Recommendation)
    assert recommendation.action == "BUY"
    assert recommendation.confidence >= 70
    assert recommendation.time_horizon in ["SHORT", "MEDIUM", "LONG"]
    assert len(recommendation.reasoning) > 0

def test_generate_recommendation_sell_signal():
    """Test generating a SELL recommendation"""
    engine = AIRecommendationEngine()
    analysis = create_mock_analysis_result()
    
    # Modify analysis to create strong sell signals
    analysis.technical.rsi = 75.0  # Overbought
    analysis.technical.macd = {'histogram': -2.0}  # Strong bearish momentum
    analysis.fundamental.revenue_growth = -0.10  # Declining revenue
    analysis.market_context.market_sentiment = "BEARISH"
    
    recommendation = engine.generate_recommendation(analysis)
    
    assert isinstance(recommendation, Recommendation)
    assert recommendation.action == "SELL"
    assert recommendation.confidence >= 30  # Confidence for SELL is based on (100 - score)
    assert recommendation.time_horizon in ["SHORT", "MEDIUM", "LONG"]
    assert len(recommendation.reasoning) > 0

def test_generate_recommendation_hold_signal():
    """Test generating a HOLD recommendation"""
    engine = AIRecommendationEngine()
    analysis = create_mock_analysis_result()
    
    # Use neutral/mixed signals that should result in HOLD
    analysis.technical.rsi = 50.0  # Neutral
    analysis.technical.macd = {'histogram': 0.1}  # Slightly bullish
    analysis.fundamental.revenue_growth = 0.05  # Moderate growth
    analysis.market_context.market_sentiment = "NEUTRAL"
    
    recommendation = engine.generate_recommendation(analysis)
    
    assert isinstance(recommendation, Recommendation)
    assert recommendation.action == "HOLD"
    assert 30 < recommendation.confidence < 70
    assert recommendation.time_horizon == "MEDIUM"
    assert len(recommendation.reasoning) > 0

def test_dynamic_weighting():
    """Test dynamic weight calculation based on data completeness"""
    engine = AIRecommendationEngine()
    analysis = create_mock_analysis_result()
    
    # Test with complete data
    weights_complete = engine._calculate_dynamic_weights(analysis, {})
    assert sum(weights_complete.values()) == pytest.approx(1.0, 0.01)
    
    # Test with incomplete technical data
    analysis.technical.rsi = None
    analysis.technical.macd = None
    weights_incomplete = engine._calculate_dynamic_weights(analysis, {})
    assert weights_incomplete['technical'] < weights_complete['technical']
    assert sum(weights_incomplete.values()) == pytest.approx(1.0, 0.01)

def test_sentiment_scoring():
    """Test sentiment scoring with various inputs"""
    engine = AIRecommendationEngine()
    analysis = create_mock_analysis_result()
    
    # Test bullish market sentiment
    analysis.market_context.market_sentiment = "BULLISH"
    score_bullish = engine._score_sentiment(analysis)
    assert score_bullish > 50
    
    # Test bearish market sentiment
    analysis.market_context.market_sentiment = "BEARISH"
    score_bearish = engine._score_sentiment(analysis)
    assert score_bearish < 50
    
    # Test with price-based sentiment (no market sentiment)
    analysis.market_context.market_sentiment = None
    analysis.quote.change_percent = 3.0  # Strong bullish
    score_price_bullish = engine._score_sentiment(analysis)
    assert score_price_bullish > 50

def test_ml_prediction_adjustment():
    """Test ML prediction adjustment functionality"""
    engine = AIRecommendationEngine()
    analysis = create_mock_analysis_result()
    
    # Create historical data for trend analysis
    analysis.historical.prices = [100, 105, 110, 115, 120, 125, 130, 135, 140, 145]  # Upward trend
    
    adjustment = engine._get_ml_prediction_adjustment(analysis)
    assert adjustment is not None
    assert adjustment > 60  # Should predict upward movement
    
    # Test downward trend
    analysis.historical.prices = [145, 140, 135, 130, 125, 120, 115, 110, 105, 100]  # Downward trend
    adjustment = engine._get_ml_prediction_adjustment(analysis)
    assert adjustment is not None
    assert adjustment < 40  # Should predict downward movement

def test_news_sentiment_integration():
    """Test news and social media sentiment integration"""
    engine = AIRecommendationEngine()
    analysis = create_mock_analysis_result()
    
    sentiment_data = engine._enhance_with_news_sentiment(analysis)
    assert 'news_sentiment' in sentiment_data
    assert 'social_media_sentiment' in sentiment_data
    assert 'recent_headlines' in sentiment_data
    assert 'sentiment_confidence' in sentiment_data
    
    # Test with bullish market sentiment
    analysis.market_context.market_sentiment = "BULLISH"
    sentiment_data_bullish = engine._enhance_with_news_sentiment(analysis)
    assert sentiment_data_bullish['news_sentiment'] > 50
    assert sentiment_data_bullish['social_media_sentiment'] > 50

def test_error_handling():
    """Test error handling in recommendation generation"""
    engine = AIRecommendationEngine()
    
    # Test with incomplete analysis data
    analysis = AnalysisResult(
        symbol="TEST",
        timestamp=datetime.now(timezone.utc),
        quote=None,
        historical=None,
        technical=None,
        fundamental=None,
        risk=None,
        market_context=None,
        recommendation="HOLD",
        confidence=50,
        time_horizon="MEDIUM",
        key_insights=[],
        summary="Incomplete analysis",
        data_sources=[],
        analysis_quality="LOW",
        confidence_factors={}
    )
    
    recommendation = engine.generate_recommendation(analysis)
    print(f"Recommendation reasoning: {recommendation.reasoning}")
    print(f"Recommendation action: {recommendation.action}")
    print(f"Recommendation confidence: {recommendation.confidence}")
    print(f"Recommendation time_horizon: {recommendation.time_horizon}")
    assert recommendation.action == "HOLD"
    assert recommendation.confidence == 50
    assert "Analysis incomplete" in recommendation.reasoning[0]

if __name__ == "__main__":
    pytest.main([__file__, "-v"])