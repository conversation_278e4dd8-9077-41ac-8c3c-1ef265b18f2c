#!/usr/bin/env python3
"""
Test Runner Script

This script runs the test suite for the trading bot,
including unit tests, integration tests, and load tests.
"""

import os
import sys
import argparse
import subprocess
import time
from datetime import datetime

def run_command(command, cwd=None):
    """Run a command and return the output"""
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        shell=True,
        cwd=cwd
    )
    stdout, stderr = process.communicate()
    return process.returncode, stdout.decode('utf-8'), stderr.decode('utf-8')

def run_unit_tests(verbose=False):
    """Run unit tests"""
    print("Running unit tests...")
    command = "pytest tests/unit"
    if verbose:
        command += " -v"
    
    returncode, stdout, stderr = run_command(command)
    
    if returncode == 0:
        print("✅ Unit tests passed")
    else:
        print("❌ Unit tests failed")
        print(stdout)
        print(stderr)
    
    return returncode == 0

def run_integration_tests(verbose=False):
    """Run integration tests"""
    print("Running integration tests...")
    command = "pytest tests/integration"
    if verbose:
        command += " -v"
    
    returncode, stdout, stderr = run_command(command)
    
    if returncode == 0:
        print("✅ Integration tests passed")
    else:
        print("❌ Integration tests failed")
        print(stdout)
        print(stderr)
    
    return returncode == 0

def run_e2e_tests(verbose=False):
    """Run end-to-end tests"""
    print("Running end-to-end tests...")
    command = "pytest tests/e2e"
    if verbose:
        command += " -v"
    
    returncode, stdout, stderr = run_command(command)
    
    if returncode == 0:
        print("✅ End-to-end tests passed")
    else:
        print("❌ End-to-end tests failed")
        print(stdout)
        print(stderr)
    
    return returncode == 0

def run_load_tests(users=10, requests=5, concurrency=10, verbose=False):
    """Run load tests"""
    print(f"Running load tests with {users} users, {requests} requests per user, and concurrency limit {concurrency}...")
    command = f"python tests/load/test_bot_load.py --users {users} --requests {requests} --concurrency {concurrency}"
    
    returncode, stdout, stderr = run_command(command)
    
    if returncode == 0:
        print("✅ Load tests completed")
        print(stdout)
    else:
        print("❌ Load tests failed")
        print(stdout)
        print(stderr)
    
    return returncode == 0

def generate_test_report(unit_result, integration_result, e2e_result, load_result):
    """Generate a test report"""
    report = f"""
# Test Report

Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary

| Test Type | Result |
|-----------|--------|
| Unit Tests | {'✅ PASSED' if unit_result else '❌ FAILED'} |
| Integration Tests | {'✅ PASSED' if integration_result else '❌ FAILED'} |
| End-to-End Tests | {'✅ PASSED' if e2e_result else '❌ FAILED'} |
| Load Tests | {'✅ COMPLETED' if load_result else '❌ FAILED'} |

## Details

### Unit Tests
Unit tests verify individual components in isolation.

### Integration Tests
Integration tests verify that components work together correctly.

### End-to-End Tests
End-to-end tests verify the entire system from user input to output.

### Load Tests
Load tests verify the system's performance under high load.

## Next Steps

- Review any failed tests
- Fix issues and re-run tests
- Consider adding more test coverage for new features
"""
    
    # Write report to file
    with open('test_results/test_report.md', 'w') as f:
        f.write(report)
    
    print(f"Test report generated: test_results/test_report.md")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Test Runner Script")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--e2e", action="store_true", help="Run end-to-end tests")
    parser.add_argument("--load", action="store_true", help="Run load tests")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--users", type=int, default=10, help="Number of users for load tests")
    parser.add_argument("--requests", type=int, default=5, help="Requests per user for load tests")
    parser.add_argument("--concurrency", type=int, default=10, help="Concurrency limit for load tests")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--report", action="store_true", help="Generate test report")
    args = parser.parse_args()
    
    # Create test_results directory if it doesn't exist
    os.makedirs('test_results', exist_ok=True)
    
    # Track test results
    unit_result = True
    integration_result = True
    e2e_result = True
    load_result = True
    
    # Run tests based on arguments
    if args.all or args.unit:
        unit_result = run_unit_tests(args.verbose)
    
    if args.all or args.integration:
        integration_result = run_integration_tests(args.verbose)
    
    if args.all or args.e2e:
        e2e_result = run_e2e_tests(args.verbose)
    
    if args.all or args.load:
        load_result = run_load_tests(args.users, args.requests, args.concurrency, args.verbose)
    
    # Generate test report if requested
    if args.report:
        generate_test_report(unit_result, integration_result, e2e_result, load_result)
    
    # Return non-zero exit code if any tests failed
    if not (unit_result and integration_result and e2e_result and load_result):
        sys.exit(1)

if __name__ == "__main__":
    main()
