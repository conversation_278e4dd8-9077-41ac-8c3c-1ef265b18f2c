#!/usr/bin/env python3
"""
Test script to check Alpha Vantage configuration.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_alpha_vantage_config():
    """Test Alpha Vantage configuration and basic functionality."""
    try:
        print("🔌 Testing Alpha Vantage Configuration")
        print("=" * 40)
        
        # Test config manager
        from src.core.config_manager import config
        
        print("✅ Config manager imported successfully")
        
        # Check Alpha Vantage config
        alpha_vantage_config = config.get('data_providers', 'alpha_vantage', {})
        print(f"📋 Alpha Vantage config: {alpha_vantage_config}")
        
        # Check if enabled
        enabled = config.get('data_providers', 'alpha_vantage', {}).get('enabled', False)
        print(f"🟢 Alpha Vantage enabled: {enabled}")
        
        # Check API key
        api_key = config.get('data_providers', 'alpha_vantage', {}).get('api_key', '')
        if api_key:
            print(f"🔑 API Key found: {api_key[:8]}...{api_key[-4:]}")
        else:
            print("❌ No API key found")
        
        # Check rate limit
        rate_limit = config.get('data_providers', 'alpha_vantage', {}).get('rate_limit', 0)
        print(f"⏱️ Rate limit: {rate_limit} requests per minute")
        
        # Test provider initialization
        from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
        
        print("\n🧪 Testing Alpha Vantage Provider Initialization...")
        provider = AlphaVantageProvider()
        
        # Check if configured
        is_configured = provider.is_configured()
        print(f"✅ Provider configured: {is_configured}")
        
        if is_configured:
            print("\n📊 Testing basic ticker fetch...")
            try:
                # Test with a simple symbol
                ticker_data = await provider.get_ticker("AAPL")
                if ticker_data and not ticker_data.get('error'):
                    print(f"✅ AAPL data fetched successfully: ${ticker_data['current_price']:.2f}")
                else:
                    print(f"⚠️ AAPL fetch returned: {ticker_data}")
            except Exception as e:
                print(f"❌ Error fetching AAPL: {e}")
        else:
            print("⚠️ Provider not configured, skipping data fetch test")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    await test_alpha_vantage_config()
    print("\n✅ Alpha Vantage configuration test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 