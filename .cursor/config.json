{"rules": ["🚫 NEVER use pip install or pip freeze", "🚫 NEVER suggest installing packages locally", "✅ All dependencies are managed via Docker + requirements.txt", "✅ Always assume code runs in Docker containers, not locally", "✅ Use docker-compose up --build to run the application", "✅ Dependencies are installed inside the container automatically", "✅ If adding new packages, update requirements.txt and rebuild Docker image", "✅ This is a containerized application - no local Python environment needed"], "defaultInstructions": "This is a Docker-based trading automation application. All code runs in containers. Never suggest pip install or local package installation. Use docker-compose commands for running the application."}