#!/usr/bin/env python3
"""
Comprehensive test script for all consolidated data providers.
Verifies that the consolidation was successful and all providers work correctly.
"""

import sys
import os
import asyncio

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all consolidated providers can be imported."""
    print("🧪 Testing imports...")
    
    try:
        # Test unified base classes
        from src.shared.data_providers.unified_base import (
            UnifiedDataProvider,
            ProviderType,
            ProviderStatus,
            ProviderMetrics,
            MarketDataResponse,
            HistoricalData,
            ProviderError
        )
        print("✅ Unified base classes imported successfully")
        
        # Test consolidated providers
        from src.api.data.providers.polygon import PolygonProvider
        print("✅ Consolidated Polygon provider imported successfully")
        
        from src.api.data.providers.finnhub import FinnhubProvider
        print("✅ Consolidated Finnhub provider imported successfully")
        
        from src.shared.data_providers.yfinance_provider import YFinanceProvider
        print("✅ Consolidated YFinance provider imported successfully")
        
        from src.shared.data_providers.alpaca_provider import AlpacaProvider
        print("✅ Consolidated Alpaca provider imported successfully")
        
        from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
        print("✅ Consolidated Alpha Vantage provider imported successfully")
        
        from src.shared.data_providers.aggregator import DataProviderAggregator
        print("✅ Data provider aggregator imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_unified_base_functionality():
    """Test that the unified base classes work correctly."""
    print("\n🧪 Testing unified base functionality...")
    
    try:
        from src.shared.data_providers.unified_base import (
            UnifiedDataProvider,
            ProviderType,
            ProviderStatus,
            ProviderMetrics
        )
        
        # Test enums
        print(f"✅ ProviderType.STOCK_DATA: {ProviderType.STOCK_DATA}")
        print(f"✅ ProviderStatus.AVAILABLE: {ProviderStatus.AVAILABLE}")
        
        # Test dataclasses
        metrics = ProviderMetrics()
        print(f"✅ ProviderMetrics created: {metrics}")
        
        return True
        
    except Exception as e:
        print(f"❌ Unified base functionality test failed: {e}")
        return False

def test_provider_inheritance():
    """Test that all providers correctly inherit from UnifiedDataProvider."""
    print("\n🧪 Testing provider inheritance...")
    
    try:
        from src.shared.data_providers.unified_base import UnifiedDataProvider
        from src.api.data.providers.polygon import PolygonProvider
        from src.api.data.providers.finnhub import FinnhubProvider
        from src.shared.data_providers.yfinance_provider import YFinanceProvider
        from src.shared.data_providers.alpaca_provider import AlpacaProvider
        from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
        
        providers = [
            ("PolygonProvider", PolygonProvider),
            ("FinnhubProvider", FinnhubProvider),
            ("YFinanceProvider", YFinanceProvider),
            ("AlpacaProvider", AlpacaProvider),
            ("AlphaVantageProvider", AlphaVantageProvider)
        ]
        
        for name, provider_class in providers:
            if issubclass(provider_class, UnifiedDataProvider):
                print(f"✅ {name} correctly inherits from UnifiedDataProvider")
            else:
                print(f"❌ {name} does not inherit from UnifiedDataProvider")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Provider inheritance test failed: {e}")
        return False

def test_provider_initialization():
    """Test that all providers can be initialized."""
    print("\n🧪 Testing provider initialization...")
    
    try:
        from src.api.data.providers.polygon import PolygonProvider
        from src.api.data.providers.finnhub import FinnhubProvider
        from src.shared.data_providers.yfinance_provider import YFinanceProvider
        from src.shared.data_providers.alpaca_provider import AlpacaProvider
        from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
        
        # Test configuration
        config = {
            'api_key': 'test_key',
            'rate_limit': 60,
            'timeout': 10.0,
            'max_retries': 3
        }
        
        # Test each provider (they may fail due to missing API keys, but should initialize)
        providers = [
            ("PolygonProvider", PolygonProvider, config),
            ("FinnhubProvider", FinnhubProvider, config),
            ("YFinanceProvider", YFinanceProvider, {}),
            ("AlpacaProvider", AlpacaProvider, {}),
            ("AlphaVantageProvider", AlphaVantageProvider, {})
        ]
        
        for name, provider_class, provider_config in providers:
            try:
                if provider_config:
                    provider = provider_class(**provider_config)
                else:
                    provider = provider_class()
                print(f"✅ {name} initialized successfully")
                
                # Check required attributes
                required_attrs = ['provider_name', 'provider_type', 'status', 'metrics', 'rate_limiter']
                for attr in required_attrs:
                    if hasattr(provider, attr):
                        print(f"  ✅ {name} has {attr}")
                    else:
                        print(f"  ❌ {name} missing {attr}")
                        return False
                        
            except Exception as e:
                print(f"⚠️ {name} initialization failed (expected for test environment): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Provider initialization test failed: {e}")
        return False

def test_required_methods():
    """Test that all providers have the required abstract methods."""
    print("\n🧪 Testing required methods...")
    
    try:
        from src.api.data.providers.polygon import PolygonProvider
        from src.api.data.providers.finnhub import FinnhubProvider
        from src.shared.data_providers.yfinance_provider import YFinanceProvider
        from src.shared.data_providers.alpaca_provider import AlpacaProvider
        from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
        
        providers = [
            ("PolygonProvider", PolygonProvider),
            ("FinnhubProvider", FinnhubProvider),
            ("YFinanceProvider", YFinanceProvider),
            ("AlpacaProvider", AlpacaProvider),
            ("AlphaVantageProvider", AlphaVantageProvider)
        ]
        
        required_methods = [
            'get_current_price',
            'get_historical_data',
            'get_stock_data',
            'get_health_status'
        ]
        
        for name, provider_class in providers:
            print(f"\n📋 Checking {name}:")
            for method in required_methods:
                if hasattr(provider_class, method):
                    print(f"  ✅ {method} exists")
                else:
                    print(f"  ❌ {method} missing")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Required methods test failed: {e}")
        return False

def test_data_source_manager_integration():
    """Test that the data source manager can use consolidated providers."""
    print("\n🧪 Testing data source manager integration...")
    
    try:
        from src.api.data.providers.data_source_manager import (
            RealPolygonProvider,
            RealFinnhubProvider,
            EnhancedYahooFinanceProvider,
            CONSOLIDATED_PROVIDERS_AVAILABLE
        )
        print("✅ Data source manager imported successfully")
        
        if CONSOLIDATED_PROVIDERS_AVAILABLE:
            print("✅ Consolidated providers are available in data source manager")
        else:
            print("⚠️ Consolidated providers are not available in data source manager")
        
        return True
        
    except Exception as e:
        print(f"❌ Data source manager integration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Consolidated Data Providers")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_unified_base_functionality,
        test_provider_inheritance,
        test_provider_initialization,
        test_required_methods,
        test_data_source_manager_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Data provider consolidation is 100% complete!")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)