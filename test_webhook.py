#!/usr/bin/env python3
"""
Test script for TradingView webhook endpoint
"""

import json
import hmac
import hashlib
import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get webhook secret from environment
webhook_secret = os.getenv("WEBHOOK_SECRET")
if not webhook_secret:
    print("Error: WEBHOOK_SECRET environment variable not set.")
    exit(1)

# Test payload
payload = {
    "symbol": "AAPL",
    "price": 150.25,
    "action": "BUY",
    "test": True,
    "timestamp": "2025-09-07T15:50:00Z"
}

# Create signature
payload_str = json.dumps(payload, separators=(',', ':'))
signature = hmac.new(
    webhook_secret.encode('utf-8'),
    payload_str.encode('utf-8'),
    hashlib.sha256
).hexdigest()

# Send request
url = "http://localhost:8001/webhook/tradingview"
headers = {
    "Content-Type": "application/json",
    "X-TradingView-Signature": signature
}

print(f"Sending webhook with signature: {signature}")
response = requests.post(url, json=payload, headers=headers)

# Print response
print(f"Status Code: {response.status_code}")
print(f"Response: {response.text}")
