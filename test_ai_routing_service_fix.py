#!/usr/bin/env python3
"""
Test script to identify and fix the ResponseStyle.PROFESSIONAL attribute error
in the AI routing service.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_response_style_imports():
    """Test ResponseStyle imports and PROFESSIONAL attribute."""
    print("Testing ResponseStyle imports...")
    
    try:
        # Test import from response_templates
        from src.bot.pipeline.commands.ask.stages.response_templates import ResponseStyle
        print(f"✅ Successfully imported ResponseStyle from response_templates")
        
        # Test all enum values
        styles = [
            ResponseStyle.SIMPLE,
            ResponseStyle.DETAILED,
            ResponseStyle.TECHNICAL,
            ResponseStyle.FUNDAMENTAL,
            ResponseStyle.ACADEMIC,
            ResponseStyle.TRADING,
            ResponseStyle.PROFESSIONAL
        ]
        
        print(f"✅ All ResponseStyle values accessible:")
        for style in styles:
            print(f"  - {style.name}: {style.value}")
        
        # Test that PROFESSIONAL is correctly aliased
        if ResponseStyle.PROFESSIONAL.value == "detailed":
            print(f"✅ ResponseStyle.PROFESSIONAL correctly aliased to 'detailed'")
        else:
            print(f"❌ ResponseStyle.PROFESSIONAL = '{ResponseStyle.PROFESSIONAL.value}', expected 'detailed'")
            return False
        
        return True
        
    except AttributeError as e:
        print(f"❌ AttributeError: {e}")
        return False
    except Exception as e:
        print(f"❌ Error importing ResponseStyle: {e}")
        return False

def test_depth_style_analyzer():
    """Test the depth style analyzer initialization."""
    print("\nTesting depth style analyzer...")
    
    try:
        from src.bot.pipeline.commands.ask.stages.depth_style_analyzer import EnhancedStyleAnalyzer
        
        # Initialize analyzer
        analyzer = EnhancedStyleAnalyzer()
        print("✅ EnhancedStyleAnalyzer initialized successfully")
        
        # Test a simple analysis with proper parameters
        test_query = "What is the price of $AAPL?"
        context_clues = {}
        features = None  # Let the analyzer extract features
        result = analyzer.analyze_style(test_query, context_clues, features)
        print(f"✅ Style analysis successful: {result[0].name} (confidence: {result[1]:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing depth style analyzer: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_routing_service():
    """Test the AI routing service initialization."""
    print("\nTesting AI routing service...")
    
    try:
        from src.bot.pipeline.commands.ask.stages.ai_routing_service import AdvancedAIRoutingService
        
        # Initialize routing service
        routing_service = AdvancedAIRoutingService()
        print(f"✅ AI routing service initialized with {len(routing_service.models)} models")
        
        # Test query analysis
        test_query = "What is the current price of $AAPL?"
        analysis = routing_service.analyze_query_complexity(test_query)
        print(f"✅ Query analysis successful: {analysis.complexity.value}")
        
        # Test model selection
        optimal_model, fallback_models = routing_service.select_optimal_model(analysis)
        print(f"✅ Model selection successful: {optimal_model.name}")
        print(f"   Fallback models: {[m.name for m in fallback_models]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing AI routing service: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_response_template_engine():
    """Test the response template engine."""
    print("\nTesting response template engine...")
    
    try:
        from src.bot.pipeline.commands.ask.stages.response_templates import ResponseTemplateEngine, ResponseStyle
        
        # Initialize template engine
        engine = ResponseTemplateEngine()
        print("✅ ResponseTemplateEngine initialized successfully")
        
        # Test response generation with PROFESSIONAL style
        test_data = {
            'symbol': 'AAPL',
            'price': 150.00,
            'change': 2.50,
            'change_percent': 1.67
        }
        
        test_analysis = {
            'complexity': 'moderate',
            'symbols': ['AAPL']
        }
        
        # Test with PROFESSIONAL style
        response = engine.generate_response(
            template_type="stock_analysis",
            style=ResponseStyle.PROFESSIONAL,
            data=test_data,
            query_analysis=test_analysis
        )
        
        print(f"✅ Response generation with PROFESSIONAL style successful")
        print(f"   Response length: {len(response)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing response template engine: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 AI Routing Service Fix Test Suite")
    print("=" * 50)
    
    tests = [
        ("ResponseStyle Imports", test_response_style_imports),
        ("Depth Style Analyzer", test_depth_style_analyzer),
        ("AI Routing Service", test_ai_routing_service),
        ("Response Template Engine", test_response_template_engine),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! AI routing service is working correctly.")
        return 0
    else:
        print("💥 SOME TESTS FAILED! Please review the failing cases above.")
        return 1

if __name__ == "__main__":
    exit(main())
