#!/usr/bin/env python3
"""
Test script to verify Phase 5 improvements:
- Advanced AI routing service
- Conversation memory service
- Multi-model AI selection
- Follow-up question handling
"""

import asyncio
import sys
import os
import time

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_phase5_improvements():
    """Test the Phase 5 improvements to the ask pipeline."""
    try:
        print("🔍 Testing Phase 5 improvements...")
        
        # Test importing the advanced AI routing service
        from src.bot.pipeline.commands.ask.stages.ai_routing_service import (
            AdvancedAIRoutingService, 
            QueryComplexity, 
            ModelCapability,
            AIModel
        )
        print("✅ Advanced AI Routing Service imported successfully")
        
        # Test importing the conversation memory service
        from src.bot.pipeline.commands.ask.stages.conversation_memory_service import (
            ConversationMemoryService,
            ConversationType,
            MemoryPriority
        )
        print("✅ Conversation Memory Service imported successfully")
        
        # Test the advanced AI routing service
        print("\n🧪 Testing advanced AI routing service...")
        
        # Create routing service instance
        routing_service = AdvancedAIRoutingService()
        print("✅ AI routing service instance created successfully")
        
        # Test model initialization
        print("\n🧪 Testing AI model initialization...")
        
        if routing_service.models:
            print(f"✅ {len(routing_service.models)} AI models initialized:")
            for model_name, model in list(routing_service.models.items())[:3]:
                print(f"   - {model.name} ({model.provider}): {len(model.capabilities)} capabilities")
        else:
            print("❌ No AI models initialized")
        
        # Test query complexity analysis
        print("\n🧪 Testing query complexity analysis...")
        
        test_queries = [
            "What is the current price of AAPL?",
            "Show me the RSI and MACD for TSLA",
            "Analyze the correlation between NVDA and AMD across multiple timeframes",
            "What's the risk assessment for my portfolio with high volatility stocks?",
            "I need live trading signals for day trading right now"
        ]
        
        for i, query in enumerate(test_queries, 1):
            analysis = routing_service.analyze_query_complexity(query)
            print(f"   {i}. Query: {query[:50]}...")
            print(f"      Complexity: {analysis.complexity.value}")
            print(f"      Capabilities: {len(analysis.required_capabilities)}")
            print(f"      Estimated tokens: {analysis.estimated_tokens}")
            print(f"      Urgency: {analysis.urgency}")
            print(f"      Real-time: {analysis.requires_real_time}")
        
        # Test optimal model selection
        print("\n🧪 Testing optimal model selection...")
        
        for i, query in enumerate(test_queries[:3], 1):
            analysis = routing_service.analyze_query_complexity(query)
            optimal_model, fallback_models = routing_service.select_optimal_model(analysis)
            print(f"   {i}. Query complexity: {analysis.complexity.value}")
            print(f"      Optimal model: {optimal_model.name}")
            print(f"      Provider: {optimal_model.provider}")
            print(f"      Capabilities: {len(optimal_model.capabilities)}")
            print(f"      Fallback models: {len(fallback_models)}")
        
        # Test the conversation memory service
        print("\n🧪 Testing conversation memory service...")
        
        # Create memory service instance
        memory_service = ConversationMemoryService()
        print("✅ Conversation memory service instance created successfully")
        
        # Test conversation session creation
        print("\n🧪 Testing conversation session creation...")
        
        test_user_id = "test_user_123"
        test_query = "What is the current price of AAPL?"
        
        session_id = memory_service.create_conversation_session(
            user_id=test_user_id,
            initial_query=test_query,
            conversation_type=ConversationType.STOCK_ANALYSIS
        )
        
        if session_id:
            print(f"✅ Conversation session created: {session_id}")
        else:
            print("❌ Failed to create conversation session")
            return False
        
        # Test adding conversation turns
        print("\n🧪 Testing conversation turns...")
        
        test_response = "AAPL is currently trading at $150.25, up 1.5% today."
        test_symbols = ["AAPL"]
        test_tools = ["price_data", "technical_analysis"]
        
        success = memory_service.add_conversation_turn(
            session_id=session_id,
            user_query=test_query,
            ai_response=test_response,
            symbols_mentioned=test_symbols,
            tools_used=test_tools,
            response_quality=0.9
        )
        
        if success:
            print("✅ Conversation turn added successfully")
        else:
            print("❌ Failed to add conversation turn")
        
        # Test follow-up question detection
        print("\n🧪 Testing follow-up question detection...")
        
        follow_up_queries = [
            "What about its RSI?",
            "How does it compare to MSFT?",
            "Show me the support levels"
        ]
        
        for i, follow_up in enumerate(follow_up_queries, 1):
            is_follow_up, context_info = memory_service.is_follow_up_question(session_id, follow_up)
            print(f"   {i}. Query: {follow_up[:30]}...")
            print(f"      Is follow-up: {is_follow_up}")
            print(f"      Symbol overlap: {context_info.get('symbol_overlap', False)}")
            print(f"      Session age: {context_info.get('session_age_hours', 0):.1f} hours")
        
        # Test conversation context retrieval
        print("\n🧪 Testing conversation context retrieval...")
        
        context = memory_service.get_conversation_context(session_id)
        if context:
            print("✅ Conversation context retrieved:")
            print(f"   - Session ID: {context.get('session_id', 'N/A')}")
            print(f"   - Type: {context.get('conversation_type', 'N/A')}")
            print(f"   - Symbols: {context.get('symbols_analyzed', [])}")
            print(f"   - Recent turns: {len(context.get('recent_turns', []))}")
            print(f"   - Context summary: {context.get('context_summary', 'N/A')}")
        else:
            print("❌ Failed to retrieve conversation context")
        
        # Test enhanced AI chat processor integration
        print("\n🧪 Testing enhanced AI chat processor integration...")
        
        from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor
        
        # Create a test config
        test_config = {
            'technical': {
                'rsi_period': 14,
                'sma_short': 20,
                'sma_long': 50,
                'ema_short': 12,
                'ema_long': 26,
                'decimal_places': 2
            }
        }
        
        # Create processor instance
        processor = AIChatProcessor(test_config)
        print("✅ Enhanced AI Chat Processor instance created successfully")
        
        # Test AI routing service integration
        if hasattr(processor, 'ai_routing_service') and processor.ai_routing_service:
            print("✅ Advanced AI routing service integrated successfully")
        else:
            print("ℹ️ Advanced AI routing service not available (using default model)")
        
        # Test conversation memory service integration
        if hasattr(processor, 'conversation_memory') and processor.conversation_memory:
            print("✅ Conversation memory service integrated successfully")
        else:
            print("ℹ️ Conversation memory service not available (no conversation context)")
        
        # Test conversation type determination
        if hasattr(processor, '_determine_conversation_type'):
            print("✅ Conversation type determination method exists")
        else:
            print("❌ Conversation type determination method missing")
        
        # Test memory statistics
        print("\n🧪 Testing memory statistics...")
        
        memory_stats = memory_service.get_memory_stats()
        if memory_stats:
            print("✅ Memory statistics retrieved:")
            print(f"   - Active sessions: {memory_stats.get('active_sessions', 0)}")
            print(f"   - Total turns: {memory_stats.get('total_turns', 0)}")
            print(f"   - Total symbols: {memory_stats.get('total_symbols', 0)}")
            print(f"   - Memory usage: {memory_stats.get('memory_usage_mb', 0)} MB")
        else:
            print("❌ Failed to retrieve memory statistics")
        
        print("\n🎉 Phase 5 improvements test completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Phase 5 improvements test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_phase5_improvements())
    sys.exit(0 if success else 1) 