#!/usr/bin/env python3
"""
Test script to verify Finnhub provider functionality.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_finnhub_provider():
    """Test Finnhub provider functionality."""
    try:
        print("🔌 Testing Finnhub Provider")
        print("=" * 40)
        
        # Test config manager
        from src.core.config_manager import config
        
        print("✅ Config manager imported successfully")
        
        # Check Finnhub config
        finnhub_config = config.get('data_providers', 'finnhub', {})
        print(f"📋 Finnhub config: {finnhub_config}")
        
        # Check if enabled
        enabled = config.get('data_providers', 'finnhub', {}).get('enabled', False)
        print(f"🟢 Finnhub enabled: {enabled}")
        
        # Check API key
        api_key = config.get('data_providers', 'finnhub', {}).get('api_key', '')
        if api_key:
            print(f"🔑 API Key found: {api_key[:8]}...{api_key[-4:]}")
        else:
            print("❌ No API key found")
        
        # Check rate limit
        rate_limit = config.get('data_providers', 'finnhub', {}).get('rate_limit', 0)
        print(f"⏱️ Rate limit: {rate_limit} requests per minute")
        
        # Test provider initialization - Using consolidated implementation
        from src.api.data.providers.finnhub import FinnhubProvider
        
        print("\n🧪 Testing Finnhub Provider Initialization...")
        provider = FinnhubProvider()
        
        # Check if configured
        is_configured = provider.is_configured()
        print(f"✅ Provider configured: {is_configured}")
        
        if is_configured:
            print("\n📊 Testing basic ticker fetch...")
            try:
                # Test with a simple symbol
                ticker_data = await provider.get_ticker("AAPL")
                if ticker_data and not ticker_data.get('error'):
                    print(f"✅ AAPL data fetched successfully: ${ticker_data['current_price']:.2f}")
                    print(f"  Change: {ticker_data['change']:.2f} ({ticker_data['change_percent']:.2f}%)")
                    print(f"  Volume: {ticker_data['volume']:,}")
                else:
                    print(f"⚠️ AAPL fetch returned: {ticker_data}")
            except Exception as e:
                print(f"❌ Error fetching AAPL: {e}")
            
            # Test batch functionality
            print("\n📦 Testing batch ticker fetch...")
            try:
                symbols = ["AAPL", "MSFT", "GOOGL"]
                batch_data = await provider.get_multiple_tickers(symbols)
                if batch_data:
                    print(f"✅ Batch fetch successful: {len(batch_data)} results")
                    for item in batch_data:
                        if not item.get('error'):
                            print(f"  {item['symbol']}: ${item['current_price']:.2f}")
                        else:
                            print(f"  {item['symbol']}: Error - {item['error']}")
                else:
                    print("⚠️ Batch fetch returned no data")
            except Exception as e:
                print(f"❌ Error in batch fetch: {e}")
        else:
            print("⚠️ Provider not configured, skipping data fetch test")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    await test_finnhub_provider()
    print("\n✅ Finnhub provider test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 