#!/usr/bin/env python3
"""
Test Pipeline with Mock Data

This test bypasses data provider issues and tests the enhanced analysis
stage directly in the pipeline context.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_pipeline_with_mock_data():
    """Test the pipeline stages with mock data"""
    
    print("🧪 TESTING PIPELINE STAGES WITH MOCK DATA")
    print("=" * 60)
    
    try:
        # Import pipeline components
        from src.bot.pipeline.commands.analyze.pipeline import PipelineContext, PipelineStatus
        from src.bot.pipeline.commands.analyze.stages.enhanced_analysis import EnhancedAnalysisStage
        
        # Create mock pipeline context
        ticker = "TSLA"
        print(f"📊 Testing pipeline stages for: {ticker}")
        
        # Generate realistic mock market data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        base_price = 150.0
        prices = base_price + np.linspace(0, 30, 100) + np.random.normal(0, 3, 100)
        
        mock_market_data = {
            'current_price': prices[-1],
            'price': prices[-1],
            'volume': np.random.randint(50000000, 200000000, 100).mean(),
            'historical_data': {
                'daily': [
                    {
                        'date': date.strftime('%Y-%m-%d'),
                        'open': price * 0.99,
                        'high': price * 1.02,
                        'low': price * 0.98,
                        'close': price,
                        'volume': np.random.randint(50000000, 200000000)
                    }
                    for date, price in zip(dates, prices)
                ]
            }
        }
        
        # Create pipeline context
        context = PipelineContext(
            ticker=ticker.upper(),
            user_id="test_user_123",
            guild_id="test_guild_456",
            correlation_id="test_corr_789",
            strict_mode=True
        )
        
        # Add mock market data
        context.processing_results["market_data"] = mock_market_data
        
        print(f"✅ Created pipeline context for {ticker}")
        print(f"   Current price: ${mock_market_data['current_price']:.2f}")
        print(f"   Historical data points: {len(mock_market_data['historical_data']['daily'])}")
        
        # Test 1: Enhanced Analysis Stage
        print("\n🚀 Testing Enhanced Analysis Stage...")
        try:
            enhanced_stage = EnhancedAnalysisStage()
            print("   ✅ Enhanced analysis stage created")
            
            # Test data preparation
            price_data, volume_data = enhanced_stage._prepare_data_for_analysis(mock_market_data)
            print(f"   ✅ Data preparation completed:")
            print(f"      Price data timeframes: {list(price_data.keys())}")
            print(f"      Volume data timeframes: {list(volume_data.keys())}")
            print(f"      Daily data points: {len(price_data['1d'])}")
            
            # Test the enhanced analysis stage through the pipeline context
            # This simulates how it actually works in the pipeline
            updated_context = await enhanced_stage.run(context)
            print("   ✅ Enhanced analysis stage completed through pipeline context")
            
            # Check what's in the context after the stage
            if 'enhanced_analysis' in updated_context.processing_results:
                enhanced_data = updated_context.processing_results['enhanced_analysis']
                print(f"   ✅ Enhanced analysis added to pipeline context")
                print(f"      Type: {type(enhanced_data)}")
                if isinstance(enhanced_data, dict):
                    print(f"      ✅ It's a dictionary with keys: {list(enhanced_data.keys())}")
                else:
                    print(f"      ❌ It's NOT a dictionary - it's: {type(enhanced_data).__name__}")
            else:
                print("   ❌ Enhanced analysis NOT in context!")
                return False
            
            # Use the updated context for the rest of the test
            context = updated_context
            
        except Exception as e:
            print(f"   ❌ Enhanced analysis stage failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 2: Report Generation Stage
        print("\n📝 Testing Report Generation Stage...")
        try:
            from src.bot.pipeline.commands.analyze.stages.report_generator import run as report_run
            
            # Add mock technical analysis and price targets
            context.processing_results['technical_analysis'] = {
                'rsi': 65.5,
                'macd': 'bullish',
                'trend': 'uptrend'
            }
            
            # Get enhanced analysis data from context
            enhanced_data = context.processing_results.get('enhanced_analysis', {})
            context.processing_results['price_targets'] = {
                'tp1': enhanced_data.get('price_targets', {}).get('conservative_target', 0),
                'tp2': enhanced_data.get('price_targets', {}).get('moderate_target', 0),
                'tp3': enhanced_data.get('price_targets', {}).get('aggressive_target', 0),
                'sl': enhanced_data.get('price_targets', {}).get('stop_loss', 0)
            }
            
            # Generate report
            updated_context = await report_run(context)
            
            if 'response' in updated_context.processing_results:
                report = updated_context.processing_results['response']
                print(f"   ✅ Report generated successfully!")
                print(f"      Report length: {len(report)} characters")
                print(f"      Contains enhanced analysis: {'enhanced analysis' in report.lower()}")
                print(f"      Contains price targets: {'price targets' in report.lower()}")
                print(f"      Contains probability: {'probability' in report.lower()}")
                
                # Show a preview of the report
                print(f"\n📋 REPORT PREVIEW:")
                print("=" * 40)
                lines = report.split('\n')[:15]  # First 15 lines
                for line in lines:
                    print(f"   {line}")
                if len(report.split('\n')) > 15:
                    print("   ... (truncated)")
                print("=" * 40)
            else:
                print("   ❌ No report generated")
                return False
                
        except Exception as e:
            print(f"   ❌ Report generation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 3: Pipeline Context Validation
        print("\n🔍 Validating Pipeline Context...")
        try:
            print(f"   ✅ Pipeline context validation:")
            print(f"      Ticker: {context.ticker}")
            print(f"      Status: {context.status}")
            print(f"      Processing results: {list(context.processing_results.keys())}")
            print(f"      Error log: {len(context.error_log)} errors")
            
            if context.error_log:
                print(f"      Errors found:")
                for error in context.error_log:
                    print(f"        • {error.get('stage', 'Unknown')}: {error.get('error_message', 'Unknown error')}")
            
        except Exception as e:
            print(f"   ❌ Context validation failed: {e}")
            return False
        
        print("\n🎉 ALL PIPELINE STAGES TESTED SUCCESSFULLY!")
        print("The enhanced analysis system is working in the pipeline context!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing pipeline with mock data: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🎯 Testing Pipeline with Mock Data")
    print("=" * 60)
    
    success = await test_pipeline_with_mock_data()
    
    if success:
        print("\n🎉 PIPELINE TEST COMPLETED SUCCESSFULLY!")
        print("Next steps:")
        print("1. Fix data provider issues for real data")
        print("2. Deploy enhanced analysis to Discord bot")
        print("3. Test with live market data")
    else:
        print("\n⚠️ Pipeline test failed. Check the error messages above.")
    
    return success

if __name__ == "__main__":
    import asyncio
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 