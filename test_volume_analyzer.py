#!/usr/bin/env python3
"""
Test Script for Volume Analysis Engine

Tests the volume analysis capabilities:
- Volume profile analysis
- Unusual volume detection
- Volume zones and clusters
- Volume indicators
"""

import sys
import os
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_volume_profile_analysis():
    """Test volume profile analysis functionality."""
    print("🔍 Testing Volume Profile Analysis...")
    
    try:
        from shared.technical_analysis.volume_analyzer import VolumeAnalyzer
        
        analyzer = VolumeAnalyzer()
        
        # Create sample data
        prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119]
        volumes = [1000000, 1100000, 1200000, 1300000, 1400000, 1500000, 1600000, 1700000, 1800000, 1900000,
                  2000000, 2100000, 2200000, 2300000, 2400000, 2500000, 2600000, 2700000, 2800000, 2900000]
        
        # Analyze volume profile
        volume_profile = analyzer.analyze_volume_profile(prices, volumes)
        
        if volume_profile:
            print("✅ Volume profile analysis successful")
            print(f"   VWAP: ${volume_profile.vwap:.2f}")
            print(f"   Volume Trend: {volume_profile.volume_trend}")
            print(f"   Volume Zones: {len(volume_profile.volume_zones)}")
            print(f"   High Volume Nodes: {len(volume_profile.high_volume_nodes)}")
            print(f"   Low Volume Nodes: {len(volume_profile.low_volume_nodes)}")
            
            return True
        else:
            print("❌ Volume profile analysis failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing volume profile analysis: {e}")
        return False

def test_volume_zones():
    """Test volume zone detection."""
    print("\n🔍 Testing Volume Zones...")
    
    try:
        from shared.technical_analysis.volume_analyzer import VolumeAnalyzer
        
        analyzer = VolumeAnalyzer()
        
        # Create sample data with distinct price levels
        prices = [100, 100, 100, 105, 105, 105, 110, 110, 110, 115, 115, 115, 120, 120, 120, 125, 125, 125, 130, 130]
        volumes = [1000000, 1100000, 1200000, 1300000, 1400000, 1500000, 1600000, 1700000, 1800000, 1900000,
                  2000000, 2100000, 2200000, 2300000, 2400000, 2500000, 2600000, 2700000, 2800000, 2900000]
        
        volume_profile = analyzer.analyze_volume_profile(prices, volumes)
        
        if volume_profile and volume_profile.volume_zones:
            print("✅ Volume zones detected successfully")
            
            # Check zone types
            zone_types = set(zone.zone_type for zone in volume_profile.volume_zones)
            print(f"   Zone Types: {', '.join(zone_types)}")
            
            # Show top zones by significance
            top_zones = sorted(volume_profile.volume_zones, key=lambda x: x.significance, reverse=True)[:3]
            for i, zone in enumerate(top_zones):
                print(f"   Top Zone {i+1}: ${zone.price_level:.2f} - {zone.zone_type} (significance: {zone.significance:.2f})")
            
            return True
        else:
            print("❌ Volume zones detection failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing volume zones: {e}")
        return False

def test_unusual_volume_detection():
    """Test unusual volume detection."""
    print("\n🔍 Testing Unusual Volume Detection...")
    
    try:
        from shared.technical_analysis.volume_analyzer import VolumeAnalyzer
        
        analyzer = VolumeAnalyzer()
        
        # Create sample data with 25 points and volume spike at the very end
        prices = [100] * 25
        volumes = [1000000] * 24 + [10000000]  # 10x spike at the very end
        
        volume_profile = analyzer.analyze_volume_profile(prices, volumes)
        
        if volume_profile and volume_profile.unusual_volume:
            anomaly = volume_profile.unusual_volume
            print("✅ Unusual volume detected successfully")
            print(f"   Anomaly Type: {anomaly.anomaly_type}")
            print(f"   Severity: {anomaly.severity:.2f}")
            print(f"   Description: {anomaly.description}")
            print(f"   Volume Ratio: {anomaly.volume_ratio:.1f}x")
            
            return True
        else:
            print("❌ Unusual volume detection failed")
            print("   Debug: Volume profile exists:", volume_profile is not None)
            if volume_profile:
                print(f"   Debug: Unusual volume detected: {volume_profile.unusual_volume is not None}")
                print(f"   Debug: Recent volumes: {volumes[-5:]}")
                print(f"   Debug: Historical volumes: {volumes[-25:-5]}")
                
                # Calculate the actual values the algorithm is using
                recent_avg = sum(volumes[-5:]) / 5
                historical_avg = sum(volumes[-25:-5]) / 20
                print(f"   Debug: Recent avg: {recent_avg:,.0f}")
                print(f"   Debug: Historical avg: {historical_avg:,.0f}")
                print(f"   Debug: Ratio: {recent_avg/historical_avg:.1f}x")
            return False
        
    except Exception as e:
        print(f"❌ Error testing unusual volume detection: {e}")
        return False

def test_volume_indicators():
    """Test volume indicator calculations."""
    print("\n🔍 Testing Volume Indicators...")
    
    try:
        from shared.technical_analysis.volume_analyzer import VolumeAnalyzer
        
        analyzer = VolumeAnalyzer()
        
        # Create sample data
        prices = [100, 101, 102, 101, 100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86, 85]
        volumes = [1000000, 1100000, 1200000, 1300000, 1400000, 1500000, 1600000, 1700000, 1800000, 1900000,
                  2000000, 2100000, 2200000, 2300000, 2400000, 2500000, 2600000, 2700000, 2800000, 2900000]
        
        volume_profile = analyzer.analyze_volume_profile(prices, volumes)
        
        if volume_profile and volume_profile.volume_indicators:
            indicators = volume_profile.volume_indicators
            print("✅ Volume indicators calculated successfully")
            
            # Check key indicators
            if "obv" in indicators:
                print(f"   OBV: {indicators['obv']:,.0f}")
            
            if "mfi" in indicators:
                print(f"   MFI: {indicators['mfi']:.2f}")
            
            if "volume_roc" in indicators:
                print(f"   Volume ROC: {indicators['volume_roc']:.2f}%")
            
            if "vwap" in indicators:
                print(f"   VWAP: ${indicators['vwap']:.2f}")
            
            if "volume_ratio" in indicators:
                print(f"   Volume Ratio: {indicators['volume_ratio']:.2f}")
            
            return True
        else:
            print("❌ Volume indicators calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing volume indicators: {e}")
        return False

def test_volume_divergence():
    """Test volume divergence detection."""
    print("\n🔍 Testing Volume Divergence Detection...")
    
    try:
        from shared.technical_analysis.volume_analyzer import VolumeAnalyzer
        
        analyzer = VolumeAnalyzer()
        
        # Create sample data with price up, volume down (bearish divergence)
        prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119]
        volumes = [2000000, 1900000, 1800000, 1700000, 1600000, 1500000, 1400000, 1300000, 1200000, 1100000,
                  1000000, 900000, 800000, 700000, 600000, 500000, 400000, 300000, 200000, 100000]
        
        divergence = analyzer.detect_volume_divergence(prices, volumes)
        
        if divergence:
            print("✅ Volume divergence detected successfully")
            print(f"   Divergence Type: {divergence.anomaly_type}")
            print(f"   Description: {divergence.description}")
            print(f"   Severity: {divergence.severity:.2f}")
            print(f"   Confidence: {divergence.confidence:.2f}")
            
            return True
        else:
            print("❌ Volume divergence detection failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing volume divergence: {e}")
        return False

def test_volume_summary():
    """Test volume summary generation."""
    print("\n🔍 Testing Volume Summary...")
    
    try:
        from shared.technical_analysis.volume_analyzer import VolumeAnalyzer
        
        analyzer = VolumeAnalyzer()
        
        # Create sample data
        prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119]
        volumes = [1000000, 1100000, 1200000, 1300000, 1400000, 1500000, 1600000, 1700000, 1800000, 1900000,
                  2000000, 2100000, 2200000, 2300000, 2400000, 2500000, 2600000, 2700000, 2800000, 2900000]
        
        volume_profile = analyzer.analyze_volume_profile(prices, volumes)
        
        if volume_profile:
            summary = analyzer.get_volume_summary(volume_profile)
            
            if summary:
                print("✅ Volume summary generated successfully")
                print(f"   VWAP: ${summary.get('vwap', 0):.2f}")
                print(f"   Volume Trend: {summary.get('volume_trend', 'unknown')}")
                print(f"   High Volume Nodes: {summary.get('high_volume_nodes_count', 0)}")
                print(f"   Low Volume Nodes: {summary.get('low_volume_nodes_count', 0)}")
                print(f"   Total Zones: {summary.get('total_volume_zones', 0)}")
                print(f"   Unusual Volume: {summary.get('unusual_volume_detected', False)}")
                
                return True
            else:
                print("❌ Volume summary generation failed")
                return False
        else:
            print("❌ Cannot generate summary - volume profile analysis failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing volume summary: {e}")
        return False

def run_all_tests():
    """Run all volume analysis tests."""
    print("🚀 Running Volume Analysis Engine Tests...")
    print("=" * 70)
    
    tests = [
        ("Volume Profile Analysis", test_volume_profile_analysis),
        ("Volume Zones", test_volume_zones),
        ("Unusual Volume Detection", test_unusual_volume_detection),
        ("Volume Indicators", test_volume_indicators),
        ("Volume Divergence", test_volume_divergence),
        ("Volume Summary", test_volume_summary)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Volume analysis engine is working properly!")
        print("🚀 Ready to integrate with multi-timeframe analysis")
    else:
        print("⚠️  Some volume analysis features need attention")
        print("🔧 Please fix the failing tests before continuing")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 