#!/usr/bin/env python3
"""
Simple test to verify correlation ID integration works
"""

import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from unittest.mock import MagicMock, patch
from src.database.supabase_client import test_supabase_connection

def test_database_correlation():
    """Test database operations with correlation IDs"""
    correlation_id = "test-corr-db-123"
    
    # Mock the singleton supabase_client instance
    with patch('src.database.supabase_client.supabase_client') as mock_supabase:
        mock_client = MagicMock()
        mock_supabase.get_client.return_value = mock_client
        
        # Set up the mock chain for table operations
        mock_table = MagicMock()
        mock_select = MagicMock()
        mock_limit = MagicMock()
        mock_execute = MagicMock()
        
        # Set up the mock chain
        mock_client.table.return_value = mock_table
        mock_table.select.return_value = mock_select
        mock_select.limit.return_value = mock_limit
        mock_limit.execute.return_value = MagicMock(data=[])
        
        # Test database connection with correlation ID
        result = test_supabase_connection(correlation_id)
        
        # Verify the mock was called
        mock_client.table.assert_called_once_with('test_table')
        mock_select.limit.assert_called_once_with(1)
        
        # Verify the test returns True
        assert result is True
        
        print(f"✅ Database test with correlation ID: {correlation_id}")
        print(f"✅ Database operations traced successfully")
        print(f"✅ Test passed: {result}")
        
        return True

if __name__ == "__main__":
    test_database_correlation()