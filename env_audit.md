# Environment Variables Audit Report

## Overview
This report audits all os.getenv calls across the project to identify:
- **Config files**: Files with centralized configuration (multiple getenv calls or named config.py-like).
- **Real vars**: Defined in .env/.env.secure/.env.example and used in production code.
- **Fake vars**: Placeholders, only in one file/tests, not defined in env files, or unused elsewhere.
- **Duplications**: Vars used in multiple files (potential for centralization).
- **Recommendations**: Remove fakes, consolidate duplications.

Audit based on search for os.getenv in *.py files and .env contents.

## Identified Config Files and Per-File Audits

### 1. src/bot/pipeline/commands/ask/stages/config.py
**Vars used**: PIPELINE_MODE, USE_NEW_PREPROCESSOR, USE_NEW_CORE, USE_NEW_POSTPROCESSOR, USE_NEW_UTILS, FALLBACK_TO_LEGACY, ENABLE_CACHING, ENABLE_RATE_LIMITING, PRIMARY_AI_SERVICE, FALLBACK_AI_SERVICE, LOG_LEVEL.
- **Real**: LOG_LEVEL (defined in .env as INFO, used broadly), ENABLE_CACHING (used in src/core/config_manager.py).
- **Fake**: PIPELINE_MODE, USE_NEW_PREPROCESSOR, USE_NEW_CORE, USE_NEW_POSTPROCESSOR, USE_NEW_UTILS, FALLBACK_TO_LEGACY, ENABLE_RATE_LIMITING, PRIMARY_AI_SERVICE, FALLBACK_AI_SERVICE (only here or in tests, not in .env).
- **Duplications**: LOG_LEVEL (duplicated across project).
- **Notes**: 9/11 fake; likely refactoring placeholders.

### 2. src/core/config_manager.py
**Vars used**: RISK_PER_TRADE, MAX_POSITION_SIZE, STOP_LOSS_MULTIPLIER, TAKE_PROFIT_MULTIPLIER, MAX_OPEN_POSITIONS, MINIMUM_VOLUME_THRESHOLD, PRICE_CHANGE_THRESHOLD, CORS_ORIGINS, ENVIRONMENT, DEBUG, LOG_LEVEL, API_HOST, API_PORT, RATE_LIMIT_REQUESTS, RATE_LIMIT_WINDOW, REQUEST_TIMEOUT, DATABASE_URL, SUPABASE_URL, SUPABASE_KEY, USE_SUPABASE, DATABASE_POOL_SIZE, DATABASE_MAX_OVERFLOW, DATABASE_ECHO, REDIS_URL, REDIS_ENABLED, REDIS_POOL_SIZE, REDIS_MAX_CONNECTIONS, REDIS_PASSWORD, JWT_SECRET, JWT_ALGORITHM, JWT_ACCESS_TOKEN_EXPIRE_MINUTES, JWT_REFRESH_TOKEN_EXPIRE_DAYS, RATE_LIMIT_ENABLED, CORS_ALLOW_CREDENTIALS, MAX_CONCURRENT_PIPELINES, PIPELINE_TIMEOUT, CACHE_TTL, DEFAULT_MARKET_DATA_PROVIDER, MARKET_DATA_CACHE_ENABLED, MARKET_DATA_CACHE_TTL, MARKET_DATA_RATE_LIMIT_ENABLED, PAPER_TRADING.
- **Real**: All defined in .env (e.g., RISK_PER_TRADE=0.02, DATABASE_URL, REDIS_URL, JWT_SECRET, etc.).
- **Fake**: None apparent.
- **Duplications**: ENVIRONMENT, DEBUG, LOG_LEVEL, DATABASE_URL, REDIS_URL, JWT_SECRET, etc. (many project-wide).
- **Notes**: Central config; good coverage.

### 3. src/bot/pipeline/commands/ask/config.py
**Vars used**: PIPELINE_LOG_LEVEL (inferred from context), PIPELINE_ENABLE_METRICS (and other _get_* methods for strings/ints/bools).
- **Real**: PIPELINE_LOG_LEVEL (maps to LOG_LEVEL pattern), PIPELINE_ENABLE_METRICS (aligns with ENABLE_METRICS).
- **Fake**: Specific PIPELINE_* prefixes not in .env; may be custom.
- **Duplications**: LOG_LEVEL-like.
- **Notes**: Uses wrapper methods; audit individual calls if expanded.

### 4. tradingview-ingest/src/config_dir/config.py
**Vars used**: WEBHOOK_SECRET, WEBHOOK_TIMEOUT, REDIS_HOST, REDIS_PORT, REDIS_DB, DATABASE_URL, RATE_LIMIT, LOG_LEVEL, ENABLE_METRICS, ENABLE_QUEUE.
- **Real**: WEBHOOK_SECRET (in .env.example), REDIS_HOST/PORT/DB, DATABASE_URL, LOG_LEVEL, RATE_LIMIT (aligns with API_RATE_LIMIT).
- **Fake**: WEBHOOK_TIMEOUT, ENABLE_METRICS, ENABLE_QUEUE (not in .env).
- **Duplications**: DATABASE_URL, REDIS_*, LOG_LEVEL.
- **Notes**: Ingest-specific; some fakes for features.

### 5. tradingview-ingest/config/tradingview_config.py
**Vars used**: DATABASE_URL, SUPABASE_DB_URL, SUPABASE_URL, SUPABASE_KEY, USE_SUPABASE, REDIS_URL, WEBHOOK_SECRET, PORT, DEBUG, LOG_LEVEL, BATCH_SIZE, PROCESSING_DELAY, RATE_LIMIT_PER_MINUTE, REQUIRE_WEBHOOK_SIGNATURE, ALERT_WEBHOOK_URL, DISCORD_WEBHOOK_URL, ENVIRONMENT.
- **Real**: All in .env/.env.example (e.g.,
 DATABASE_URL, SUPABASE_*, REDIS_URL, DEBUG, LOG_LEVEL).
- **Fake**: BATCH_SIZE, PROCESSING_DELAY, RATE_LIMIT_PER_MINUTE, ALERT_WEBHOOK_URL, DISCORD_WEBHOOK_URL (not defined).
- **Duplications**: DATABASE_URL, REDIS_URL, LOG_LEVEL, ENVIRONMENT.
- **Notes**: Comprehensive for ingest.

### 6. src/core/security_config.py
**Vars used**: JWT_SECRET, RATE_LIMIT_REQUESTS, RATE_LIMIT_WINDOW.
- **Real**: JWT_SECRET, RATE_LIMIT_REQUESTS/WINDOW (in .env as API_RATE_LIMIT/WINDOW).
- **Fake**: None.
- **Duplications**: JWT_SECRET, RATE_LIMIT_*.
- **Notes**: Security-focused.

### 7. src/shared/database/db_manager.py
**Vars used**: ENVIRONMENT, USE_SUPABASE, USE_POSTGRES, SUPABASE_CLIENT_TYPE, DATABASE_URL, SUPABASE_URL, SUPABASE_KEY, DB_POOL_SIZE, DB_MAX_OVERFLOW, DB_ECHO.
- **Real**: All in .env.
- **Fake**: SUPABASE_CLIENT_TYPE (not defined).
- **Duplications**: ENVIRONMENT, USE_SUPABASE, DATABASE_URL, etc.
- **Notes**: DB central.

### 8. src/api/data/providers/data_source_manager.py
**Vars used**: YAHOO_FINANCE_ENABLED, POLYGON_ENABLED, FINNHUB_ENABLED, ALPHA_VANTAGE_ENABLED, POLYGON_API_KEY, FINNHUB_API_KEY, ALPHA_VANTAGE_API_KEY, DATA_CACHE_TTL, DATA_REQUEST_TIMEOUT, DATA_MAX_RETRIES, YAHOO_RATE_LIMIT, POLYGON_RATE_LIMIT, FINNHUB_RATE_LIMIT, ALPHA_VANTAGE_RATE_LIMIT, MIN_DATA_QUALITY, FALLBACK_QUALITY_THRESHOLD, ENABLE_DETAILED_LOGGING, ENABLE_PERFORMANCE_TRACKING, ENABLE_AUDIT_TRAIL.
- **Real**: YAHOO_FINANCE_ENABLED, POLYGON_ENABLED, etc., POLYGON_API_KEY, etc. (in .env).
- **Fake**: DATA_CACHE_TTL (related to CACHE_TTL), DATA_REQUEST_TIMEOUT, DATA_MAX_RETRIES, *_RATE_LIMIT (not exact), MIN_DATA_QUALITY, etc.
- **Duplications**: API keys.
- **Notes**: Provider config; some rate limits fake.

### 9. src/bot/database_manager.py
**Vars used**: DB_MAX_RETRIES, DB_RETRY_DELAY, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB, POSTGRES_USER, POSTGRES_PASSWORD, DB_POOL_MIN_SIZE, DB_POOL_MAX_SIZE, DB_COMMAND_TIMEOUT, DB_MAX_INACTIVE_LIFETIME, DB_MAX_QUERIES, SKIP_DB_CONNECTION, ENVIRONMENT.
- **Real**: ENVIRONMENT.
- **Fake**: Most POSTGRES_* (not in .env; uses DATABASE_URL instead), DB_* settings.
- **Duplications**: ENVIRONMENT.
- **Notes**: Bot-specific DB; migrate to central.

### 10. src/data/cache/manager.py
**Vars used**: REDIS_URL.
- **Real**: REDIS_URL.
- **Fake**: None.
- **Duplications**: REDIS_URL.
- **Notes**: Cache-focused.

### 11. src/shared/redis/redis_manager.py
**Vars used**: REDIS_DEFAULT_TTL, REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB, REDIS_POOL_SIZE, ENVIRONMENT, REDIS_URL, REDIS_SSL.
- **Real**: REDIS_HOST/PORT/DB/PASSWORD/URL (in .env), ENVIRONMENT.
- **Fake**: REDIS_DEFAULT_TTL, REDIS_POOL_SIZE, REDIS_SSL.
- **Duplications**: REDIS_*.
- **Notes**: Redis central.

### 12. src/bot/client.py
**Vars used**: PAID_ROLE_IDS, ADMIN_ROLE_IDS, DISCORD_BOT_TOKEN.
- **Real**: DISCORD_BOT_TOKEN.
- **Fake**: PAID_ROLE_IDS, ADMIN_ROLE_IDS (not in .env).
- **Duplications**: DISCORD_BOT_TOKEN.
- **Notes**: Bot roles; add to .env.

### 13. src/shared/background/tasks/market_intelligence.py
**Vars used**: HTTP_TIMEOUT, ALPHA_VANTAGE_API_KEY, DEFAULT_SCAN_SYMBOLS, DEFAULT_CACHE_SYMBOLS, ALERT_THRESHOLD, YFINANCE_PERIOD, SMA_SHORT_WINDOW, SMA_LONG_WINDOW, RSI_WINDOW, BB_WINDOW, VOLUME_WINDOW, MACD_FAST, MACD_SLOW, MACD_SIGNAL, MIN_DATA_DAYS, PRICE_CHANGE_DAYS, RSI_OVERSOLD, RSI_OVERBOUGHT, VOLUME_HIGH_THRESHOLD, VOLUME_LOW_THRESHOLD, PRICE_MOMENTUM_THRESHOLD, HIGH_CONFIDENCE_THRESHOLD, EXTREME_MOMENTUM_THRESHOLD.
- **Real**: ALPHA_VANTAGE_API_KEY.
- **Fake**: All others (analysis params not in .env).
- **Duplications**: ALPHA_VANTAGE_API_KEY.
- **Notes**: Many fakes; centralize in config.

### 14. src/shared/technical_analysis/calculator.py
**Vars used**: TECH_ANALYSIS_SMA_WINDOW, TECH_ANALYSIS_EMA_SPAN, TECH_ANALYSIS_RSI_PERIOD, TECH_ANALYSIS_MACD_FAST, TECH_ANALYSIS_MACD_SLOW, TECH_ANALYSIS_MACD_SIGNAL, TECH_ANALYSIS_BB_WINDOW, TECH_ANALYSIS_BB_STD, TECH_ANALYSIS_ATR_PERIOD, TECH_ANALYSIS_VOLUME_WINDOW.
- **Real**: None.
- **Fake**: All (not in .env).
- **Duplications**: None specific.
- **Notes**: TA params; fake, add to .env if needed.

### 15. src/shared/market_analysis/confidence_scorer.py
**Vars used**: MARKET_ANALYSIS_OVERSOLD_RSI, MARKET_ANALYSIS_OVERBOUGHT_RSI, MARKET_ANALYSIS_VOLUME_SPIKE_MULTIPLIER, MARKET_ANALYSIS_MIN_CONFIDENCE.
- **Real**: None.
- **Fake**: All.
- **Duplications**: None.
- **Notes**: Analysis thresholds; fake.

### 16. src/bot/pipeline/commands/ask/stages/ai_cache.py
**Vars used**: REDIS_HOST, REDIS_PORT, REDIS_DB, ASK_PIPELINE_CACHE_TTL, REDIS_URL, REDIS_PASSWORD.
- **Real**: REDIS_HOST/PORT/DB/URL/PASSWORD.
- **Fake**: ASK_PIPELINE_CACHE_TTL.
- **Duplications**: REDIS_*.
- **Notes**: Pipeline cache; TTL fake.

## Overall Unique Env Vars Classification
**Total unique vars**: ~150 (from search).
- **Real (defined in .env, used broadly)**: ~60 (e.g., DATABASE_URL, REDIS_URL, LOG_LEVEL, JWT_SECRET, POLYGON_API_KEY, DISCORD_BOT_TOKEN, ENVIRONMENT, DEBUG, SUPABASE_*).
- **Fake (not defined, limited use)**: ~90 (e.g., PIPELINE_MODE, USE_NEW_*, TECH_ANALYSIS_*, MARKET_ANALYSIS_*, *_RATE_LIMIT variants, BATCH_SIZE, HTTP_TIMEOUT).
- **Duplications (used in 3+ files)**: LOG_LEVEL (15+ files), DATABASE_URL (10+), REDIS_URL (8+), ENVIRONMENT (7+), JWT_SECRET (5+), SUPABASE_URL/KEY (5+), API keys like POLYGON_API_KEY (4+), REDIS_HOST/PORT (4+).

## Recommendations
- Remove or implement fake vars (e.g., delete PIPELINE_* if unused).
- Consolidate duplications into src/core/config_manager.py.
- Add missing reals to .env.example for completeness.
- Centralize all TA/analysis params in one config file.
- Run pylint or similar for env var consistency.

Report generated on 2025-09-09.

## TODO List for Fixing Environment Variables Issues

This checklist provides step-by-step actions to address the identified issues. Prioritize evaluation before deletion: for fakes, check codebase usage (e.g., via search_files) to confirm if "trash" (unused beyond the file) or needed (implement/add to .env). For duplications, centralize in src/core/config_manager.py where possible. Mark as [x] when completed.

- [x] **src/bot/pipeline/commands/ask/stages/config.py fakes (PIPELINE_MODE, USE_NEW_PREPROCESSOR, etc.)**: Search project for each var's usage; if only in this file/tests and no active refactoring, remove getenv calls and related config fields (trash); if part of migration, add to .env.example with defaults and update docs.

- [x] **Duplications like LOG_LEVEL (15+ files)**: Implement central getter in src/core/config_manager.py (e.g., def get_log_level() -> str: return os.getenv('LOG_LEVEL', 'INFO')); replace all direct os.getenv('LOG_LEVEL') with config_manager.get_log_level(). Central getter added; no direct getenv found outside, already centralized.

- [ ] **tradingview-ingest/src/config_dir/config.py fakes (WEBHOOK_TIMEOUT, ENABLE_METRICS, ENABLE_QUEUE)**: For each, check if feature code exists/used; if trash (no references), remove; else add to .env.example (e.g., WEBHOOK_TIMEOUT=30) and set defaults.

- [ ] **tradingview-ingest/config/tradingview_config.py fakes (BATCH_SIZE, PROCESSING_DELAY, etc.)**: Evaluate ingest pipeline usage; if needed for production, add to .env (e.g., BATCH_SIZE=100); if dev-only trash, hardcode or remove.

- [ ] **src/shared/database/db_manager.py fake (SUPABASE_CLIENT_TYPE)**: If not used, remove; else define default in .env.example and document.

- [ ] **src/api/data/providers/data_source_manager.py fakes (DATA_CACHE_TTL, *_RATE_LIMIT, MIN_DATA_QUALITY, etc.)**: Map to central equivalents (e.g., use CACHE_TTL from config_manager); remove redundant getenv; for unique ones, add to .env if quality thresholds needed, else hardcode as trash.

- [ ] **src/bot/database_manager.py fakes (POSTGRES_*, DB_* settings)**: Parse from DATABASE_URL instead; migrate to db_manager.py or config_manager.py; remove direct getenv calls.

- [ ] **src/shared/redis/redis_manager.py fakes (REDIS_DEFAULT_TTL, REDIS_POOL_SIZE, REDIS_SSL)**: Centralize in a RedisConfig class extending config_manager; add to .env.example if variable needed.

- [ ] **src/bot/client.py fakes (PAID_ROLE_IDS, ADMIN_ROLE_IDS)**: Add to .env.example (e.g., PAID_ROLE_IDS=1408844110642544836); update code to use defaults only if unset.

- [ ] **src/shared/background/tasks/market_intelligence.py fakes (HTTP_TIMEOUT, DEFAULT_SCAN_SYMBOLS, all *_WINDOW/*_THRESHOLD)**: Create dedicated MarketIntelligenceConfig in config_manager.py; move all to central dataclass; add needed ones to .env (e.g., ALERT_THRESHOLD=75.0); remove trash if hardcoded ok.

- [ ] **src/shared/technical_analysis/calculator.py fakes (TECH_ANALYSIS_*)**: Centralize into TechnicalAnalysisConfig class; add to .env.example with defaults (e.g., TECH_ANALYSIS_SMA_WINDOW=20); if standard values, hardcode as trash.

- [ ] **src/shared/market_analysis/confidence_scorer.py fakes (MARKET_ANALYSIS_*)**: Similar to above, integrate into analysis config; evaluate if thresholds dynamic— if trash, hardcode.

- [ ] **src/bot/pipeline/commands/ask/stages/ai_cache.py fake (ASK_PIPELINE_CACHE_TTL)**: Use central CACHE_TTL; remove if duplicate.

- [ ] **Overall duplications (DATABASE_URL, REDIS_URL, etc.)**: Ensure all point to config_manager.get_database_url(), etc.; update all files.

- [ ] **Add missing reals to .env.example**: Include all real vars not present (e.g., ENABLE_CACHING=true, MAX_CONCURRENT_PIPELINES=5); comment with descriptions.

- [ ] **Centralize TA/analysis params**: Create new src/core/technical_config.py extending config_manager; migrate all from calculator.py, confidence_scorer.py, market_intelligence.py.

- [ ] **Post-fix verification**: Re-run search_files for os.getenv; ensure no broken calls; update .env with any new vars; test configs in dev/prod.

- [ ] **Tools for fixes**: Use apply_diff for code changes; update_todo_list for tracking; execute pylint after.