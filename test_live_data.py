#!/usr/bin/env python3
"""
Test Live Data Integration

Test that the AI automation system is now using real live market data.
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_live_data_collection():
    """Test live market data collection."""
    print("🧪 Testing Live Market Data Collection...")
    
    try:
        from src.core.automation.report_engine import AIReportEngine
        
        # Create report engine
        engine = AIReportEngine()
        print("✅ Report engine created successfully")
        
        # Test live data collection
        print("📊 Collecting live market data...")
        market_data = await engine._collect_market_data()
        
        if market_data:
            print(f"✅ Successfully collected live data for {len(market_data)} symbols")
            print("\n📈 Live Market Data Sample:")
            
            for i, stock in enumerate(market_data[:5]):  # Show first 5
                print(f"   {i+1}. {stock.symbol}")
                print(f"      Price: ${stock.current_price:.2f}")
                print(f"      Change: {stock.price_change:+.2f} ({stock.price_change_pct:+.2f}%)")
                print(f"      Volume: {stock.volume:,}")
                print(f"      Sector: {stock.sector}")
                print()
            
            # Verify it's real data (not mock)
            mock_indicators = [
                any(stock.current_price == 100.0 + (hash(stock.symbol) % 1000) for stock in market_data),
                any(stock.price_change == 5.0 + (hash(stock.symbol) % 20) for stock in market_data)
            ]
            
            if any(mock_indicators):
                print("⚠️ WARNING: Some data appears to be mock data!")
                return False
            else:
                print("✅ All data appears to be live (no mock patterns detected)")
                return True
        else:
            print("❌ No market data collected")
            return False
            
    except Exception as e:
        print(f"❌ Live data collection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_live_sector_analysis():
    """Test live sector performance analysis."""
    print("\n🧪 Testing Live Sector Analysis...")
    
    try:
        from src.core.automation.report_engine import AIReportEngine
        
        # Create report engine
        engine = AIReportEngine()
        
        # Test sector analysis
        print("🏭 Analyzing sector performance with live data...")
        sector_performance = await engine._analyze_sector_performance()
        
        if sector_performance:
            print(f"✅ Successfully analyzed {len(sector_performance)} sectors")
            print("\n📊 Live Sector Performance:")
            
            for sector in sector_performance:
                print(f"   🏭 {sector.sector}")
                print(f"      Performance: {sector.performance_pct:+.2f}%")
                print(f"      Top Performer: {sector.top_performer} ({sector.top_performer_pct:+.2f}%)")
                print(f"      Worst Performer: {sector.worst_performer} ({sector.worst_performer_pct:+.2f}%)")
                print(f"      Volume Trend: {sector.volume_trend}")
                print()
            
            return True
        else:
            print("❌ No sector performance data available")
            return False
            
    except Exception as e:
        print(f"❌ Live sector analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_live_market_health():
    """Test live market health assessment."""
    print("\n🧪 Testing Live Market Health Assessment...")
    
    try:
        from src.core.automation.report_engine import AIReportEngine
        
        # Create report engine
        engine = AIReportEngine()
        
        # Test market health assessment
        print("🏥 Assessing market health with live data...")
        market_health = await engine._assess_market_health()
        
        if market_health:
            print("✅ Market health assessment completed")
            print(f"\n📊 Live Market Health:")
            print(f"   Data Quality: {market_health.avg_data_quality:.1f}/100")
            print(f"   Total Symbols: {market_health.total_symbols}")
            print(f"   Stale Symbols: {market_health.stale_symbols_count}")
            print(f"   Gap Detections: {market_health.gap_detections}")
            print(f"   Market Sentiment: {market_health.market_sentiment}")
            print(f"   Risk Level: {market_health.risk_level}")
            print(f"   Provider Reliability:")
            for provider, reliability in market_health.provider_reliability.items():
                print(f"     - {provider}: {reliability:.1f}%")
            
            return True
        else:
            print("❌ Market health assessment failed")
            return False
            
    except Exception as e:
        print(f"❌ Live market health test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_complete_live_report():
    """Test complete live report generation."""
    print("\n🧪 Testing Complete Live Report Generation...")
    
    try:
        from src.core.automation.report_engine import AIReportEngine
        
        # Create report engine
        engine = AIReportEngine()
        
        # Generate complete report
        print("📊 Generating complete live market report...")
        report = await engine.generate_daily_market_report()
        
        if report:
            print("✅ Complete live report generated successfully")
            print(f"\n📋 Report Details:")
            print(f"   Title: {report.title}")
            print(f"   Market Data: {len(report.market_data)} symbols")
            print(f"   Sector Performance: {len(report.sector_performance)} sectors")
            print(f"   AI Insights: {len(report.ai_insights)} insights")
            print(f"   Data Quality: {report.market_health.avg_data_quality:.1f}/100")
            print(f"   Market Sentiment: {report.market_health.market_sentiment}")
            print(f"   Risk Level: {report.market_health.risk_level}")
            
            # Check for real data indicators
            if report.market_data:
                sample_stock = report.market_data[0]
                print(f"\n🔍 Sample Stock Data Verification:")
                print(f"   Symbol: {sample_stock.symbol}")
                print(f"   Price: ${sample_stock.current_price:.2f}")
                print(f"   Change: {sample_stock.price_change:+.2f} ({sample_stock.price_change_pct:+.2f}%)")
                print(f"   Volume: {sample_stock.volume:,}")
                
                # Verify it's not mock data
                if (sample_stock.current_price == 100.0 + (hash(sample_stock.symbol) % 1000) or
                    sample_stock.price_change == 5.0 + (hash(sample_stock.symbol) % 20)):
                    print("⚠️ WARNING: Sample data appears to be mock data!")
                    return False
                else:
                    print("✅ Sample data appears to be live market data")
            
            return True
        else:
            print("❌ Complete report generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Complete live report test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all live data tests."""
    print("🚀 Live Data Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Live Market Data Collection", test_live_data_collection),
        ("Live Sector Analysis", test_live_sector_analysis),
        ("Live Market Health Assessment", test_live_market_health),
        ("Complete Live Report Generation", test_complete_live_report)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} test passed!")
            else:
                print(f"❌ {test_name} test failed!")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All live data tests passed! Your AI automation system is now using REAL market data!")
        print("\n🚀 Ready to generate live market reports with:")
        print("   - Real-time stock prices and changes")
        print("   - Live sector performance analysis")
        print("   - Actual market health metrics")
        print("   - Real AI insights based on live data")
        return True
    else:
        print("⚠️ Some live data tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 