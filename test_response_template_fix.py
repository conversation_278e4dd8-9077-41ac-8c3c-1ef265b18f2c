#!/usr/bin/env python3
"""
Test script to reproduce and fix the 'Unknown format code 'f' for object of type 'str'' error
in response template generation.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_format_error_reproduction():
    """Reproduce the format error with string values in numeric format specifiers."""
    print("Testing format error reproduction...")
    
    # Test cases that would cause the error
    test_cases = [
        # Case 1: String value with numeric format specifier
        {"template": "Price: ${price:.2f}", "data": {"price": "150.50"}},
        {"template": "Support: ${support:.2f}", "data": {"support": "N/A"}},
        {"template": "Change: {change:.2f}%", "data": {"change": "unknown"}},
        {"template": "Volume: {volume:,.0f}", "data": {"volume": "high"}},
        {"template": "Market cap: ${market_cap:.2f}B", "data": {"market_cap": "large"}},
    ]
    
    errors_found = []
    
    for i, test_case in enumerate(test_cases):
        try:
            result = test_case["template"].format(**test_case["data"])
            print(f"✅ Test {i+1}: {result}")
        except ValueError as e:
            error_msg = str(e)
            if "Unknown format code 'f' for object of type 'str'" in error_msg:
                errors_found.append((i+1, test_case, error_msg))
                print(f"❌ Test {i+1}: {error_msg}")
            else:
                print(f"❌ Test {i+1}: Other error: {error_msg}")
        except Exception as e:
            print(f"❌ Test {i+1}: Unexpected error: {e}")
    
    print(f"\nFound {len(errors_found)} format errors")
    return errors_found

def test_safe_formatting_solution():
    """Test the safe formatting solution."""
    print("\nTesting safe formatting solution...")
    
    def safe_format_numeric(value, format_spec=".2f", default="0.00"):
        """Safely format a value as numeric, handling strings and None values."""
        if value is None:
            return default
        
        # If it's already a string that looks like a formatted number, return as-is
        if isinstance(value, str):
            # Try to parse as float first
            try:
                float_val = float(value)
                return f"{float_val:{format_spec}}"
            except (ValueError, TypeError):
                # If it can't be parsed as a number, return the default or the string itself
                if value.lower() in ['n/a', 'unknown', 'unavailable', 'none', '']:
                    return default
                else:
                    return value  # Return the string as-is for descriptive values
        
        # If it's a number, format it
        try:
            return f"{float(value):{format_spec}}"
        except (ValueError, TypeError):
            return default
    
    # Test the safe formatting function
    test_cases = [
        ("150.50", ".2f", "150.50"),  # String number
        (150.50, ".2f", "150.50"),    # Float
        ("N/A", ".2f", "0.00"),       # Non-numeric string
        (None, ".2f", "0.00"),        # None value
        ("high", ".2f", "high"),      # Descriptive string
        ("", ".2f", "0.00"),          # Empty string
    ]
    
    all_passed = True
    for value, format_spec, expected in test_cases:
        result = safe_format_numeric(value, format_spec)
        if result == expected:
            print(f"✅ safe_format_numeric({value!r}, {format_spec!r}) = {result!r}")
        else:
            print(f"❌ safe_format_numeric({value!r}, {format_spec!r}) = {result!r}, expected {expected!r}")
            all_passed = False
    
    return all_passed

def test_enhanced_safe_dict():
    """Test an enhanced SafeDict that handles format specifiers."""
    print("\nTesting enhanced SafeDict...")
    
    class EnhancedSafeDict(dict):
        """Enhanced SafeDict that safely handles format specifiers for numeric values."""
        
        def __missing__(self, key):
            return f"{{{key}}}"
        
        def __getitem__(self, key):
            value = super().__getitem__(key)
            return self._safe_value(value)
        
        def _safe_value(self, value):
            """Return a safe value that can handle format specifiers."""
            if value is None:
                return SafeValue(0.0)
            elif isinstance(value, str):
                # Try to convert string to number
                try:
                    return SafeValue(float(value))
                except (ValueError, TypeError):
                    # Return a SafeValue that handles format specs gracefully
                    return SafeValue(value)
            else:
                return SafeValue(value)
    
    class SafeValue:
        """A wrapper that safely handles format specifiers."""
        
        def __init__(self, value):
            self.value = value
            self.is_numeric = isinstance(value, (int, float))
            if not self.is_numeric and isinstance(value, str):
                try:
                    self.numeric_value = float(value)
                    self.is_numeric = True
                except (ValueError, TypeError):
                    self.numeric_value = 0.0
            else:
                self.numeric_value = float(value) if isinstance(value, (int, float)) else 0.0
        
        def __format__(self, format_spec):
            """Handle format specifications safely."""
            if not format_spec:
                return str(self.value)
            
            # Check if format spec is for numbers (contains 'f', 'd', etc.)
            if any(char in format_spec for char in 'fFeEgGdoxX'):
                if self.is_numeric:
                    return f"{self.numeric_value:{format_spec}}"
                else:
                    # For non-numeric strings, return the string itself or a default
                    if str(self.value).lower() in ['n/a', 'unknown', 'unavailable', 'none', '']:
                        return f"{0.0:{format_spec}}"
                    else:
                        return str(self.value)
            else:
                # Non-numeric format spec, just return the string
                return f"{self.value:{format_spec}}"
        
        def __str__(self):
            return str(self.value)
        
        def __repr__(self):
            return f"SafeValue({self.value!r})"
    
    # Test the enhanced SafeDict
    test_data = {
        'price': '150.50',
        'support': 'N/A',
        'change': 2.5,
        'volume': 'high',
        'market_cap': None
    }
    
    safe_data = EnhancedSafeDict(test_data)
    
    test_templates = [
        "Price: ${price:.2f}",
        "Support: ${support:.2f}",
        "Change: {change:.2f}%",
        "Volume: {volume}",
        "Market cap: ${market_cap:.2f}B"
    ]
    
    all_passed = True
    for template in test_templates:
        try:
            result = template.format(**safe_data)
            print(f"✅ Template: {template}")
            print(f"   Result: {result}")
        except Exception as e:
            print(f"❌ Template: {template}")
            print(f"   Error: {e}")
            all_passed = False
    
    return all_passed

def main():
    """Run all tests."""
    print("🧪 Response Template Format Error Fix Test Suite")
    print("=" * 60)
    
    # Test 1: Reproduce the error
    errors = test_format_error_reproduction()
    
    # Test 2: Test safe formatting solution
    safe_format_passed = test_safe_formatting_solution()
    
    # Test 3: Test enhanced SafeDict
    safe_dict_passed = test_enhanced_safe_dict()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"  Format errors reproduced: {len(errors)} (expected)")
    print(f"  Safe formatting solution: {'✅ PASSED' if safe_format_passed else '❌ FAILED'}")
    print(f"  Enhanced SafeDict solution: {'✅ PASSED' if safe_dict_passed else '❌ FAILED'}")
    
    if safe_format_passed and safe_dict_passed:
        print("\n🎉 All solutions working! Ready to implement the fix.")
        return 0
    else:
        print("\n💥 Some solutions failed. Please review the failing cases.")
        return 1

if __name__ == "__main__":
    exit(main())
