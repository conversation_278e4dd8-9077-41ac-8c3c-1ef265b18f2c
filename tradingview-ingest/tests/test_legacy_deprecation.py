"""
Tests for legacy code deprecation warnings
"""

import os
import sys
import warnings
import pytest
from unittest.mock import patch, MagicMock

# Add src directory to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

from src.parser import UnifiedParser
from src.storage_manager import StorageManager
from src.config.legacy_flags import legacy_flags


class TestLegacyDeprecation:
    """Test suite for legacy code deprecation warnings"""
    
    def setup_method(self):
        """Set up test environment"""
        # Reset warning filter to catch all deprecation warnings
        warnings.resetwarnings()
        warnings.simplefilter("always", DeprecationWarning)
        
        # Create test instances
        self.parser = UnifiedParser()
        self.storage_manager = StorageManager()
        
        # Sample data for testing
        self.emoji_text = "🟢 LONG TRADE ALERT $AAPL (1h) Entry: 150.00 TP1: 155.00 TP2: 160.00 TP3: 165.00 SL: 145.00"
        self.pipe_text = "AAPL|LONG_ENTRY|1625097600|1h|150.00|155.00|160.00|165.00|145.00"
        self.webhook_data = {"raw_text": self.pipe_text}
    
    def test_legacy_parser_deprecation_warning(self):
        """Test that legacy parser methods emit deprecation warnings"""
        with pytest.warns(DeprecationWarning, match="deprecated and will be removed"):
            self.parser._parse_legacy_format(self.emoji_text)
    
    def test_legacy_alert_type_deprecation_warning(self):
        """Test that legacy alert type method emits deprecation warnings"""
        with pytest.warns(DeprecationWarning, match="deprecated and will be removed"):
            self.parser._determine_legacy_alert_type(self.emoji_text)
    
    def test_legacy_signal_deprecation_warning(self):
        """Test that legacy signal method emits deprecation warnings"""
        with pytest.warns(DeprecationWarning, match="deprecated and will be removed"):
            self.parser._extract_legacy_signal(self.emoji_text)
    
    @patch('src.storage_manager.StorageManager._store_webhook_alert_legacy')
    def test_legacy_storage_deprecation_warning(self, mock_store):
        """Test that legacy storage method emits deprecation warnings"""
        # Configure mock to avoid actual database operations
        mock_store.return_value = True
        
        with pytest.warns(DeprecationWarning, match="deprecated and will be removed"):
            self.storage_manager._store_webhook_alert_legacy(self.webhook_data)
    
    def test_feature_flag_disables_legacy_parser(self):
        """Test that feature flag can disable legacy parser"""
        # Temporarily patch the legacy_flags to disable legacy parsers
        with patch('src.config.legacy_flags.LegacyFeatureFlags.USE_LEGACY_PARSERS', False):
            with pytest.raises(ValueError, match="Legacy parsers are disabled"):
                self.parser.parse_text_alert(self.emoji_text)
    
    @patch('src.storage_manager.use_legacy_storage')
    @patch('src.storage_manager.StorageManager.store_webhook_alert')
    def test_feature_flag_disables_legacy_storage(self, mock_store, mock_use_legacy):
        """Test that feature flag can disable legacy storage"""
        # Configure mocks
        mock_use_legacy.return_value = False
        mock_store.return_value = True
        
        # Call should be redirected to store_webhook_alert
        self.storage_manager._store_webhook_alert_legacy(self.webhook_data)
        mock_store.assert_called_once_with(self.webhook_data)
    
    @patch('src.metrics.legacy_metrics.track_legacy_parser_usage')
    def test_legacy_parser_metrics(self, mock_track):
        """Test that legacy parser usage is tracked"""
        self.parser._parse_legacy_format(self.emoji_text)
        mock_track.assert_called_once_with("emoji_based", "text")
    
    @patch('src.metrics.legacy_metrics.track_legacy_storage_usage')
    def test_legacy_storage_metrics(self, mock_track):
        """Test that legacy storage usage is tracked"""
        # Patch to avoid actual database operations
        with patch('src.storage_manager.StorageManager._store_webhook_alert_legacy'):
            self.storage_manager._store_webhook_alert_legacy(self.webhook_data)
            mock_track.assert_called_once_with("_store_webhook_alert_legacy", "webhook_data")


if __name__ == "__main__":
    pytest.main(["-v", "test_legacy_deprecation.py"])
