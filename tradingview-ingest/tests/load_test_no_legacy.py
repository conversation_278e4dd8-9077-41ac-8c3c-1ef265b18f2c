"""
Load testing script for testing the system with legacy code disabled
"""

import os
import sys
import time
import asyncio
import random
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Add src directory to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

# Import test configuration
from test_config import disable_legacy_code, enable_legacy_code

# Import components to test
from src.parser import UnifiedParser, parser
from src.storage_manager import StorageManager
from src.metrics.legacy_metrics import get_legacy_usage_stats

# Constants for load testing
NUM_REQUESTS = 1000
CONCURRENCY = 10
REPORT_INTERVAL = 100

# Sample data for testing
SYMBOLS = ["AAPL", "MSFT", "GOOGL", "AMZN", "FB", "TSLA", "NVDA", "PYPL", "NFLX", "INTC"]
TIMEFRAMES = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
SIGNAL_TYPES = ["LONG_ENTRY", "SHORT_ENTRY", "LONG_EXIT", "SHORT_EXIT"]

def generate_test_data():
    """Generate random test data in the uniform format"""
    symbol = random.choice(SYMBOLS)
    signal_type = random.choice(SIGNAL_TYPES)
    timestamp = int(time.time())
    timeframe = random.choice(TIMEFRAMES)
    price = round(random.uniform(50.0, 500.0), 2)
    tp1 = round(price * 1.01, 2) if "LONG" in signal_type else round(price * 0.99, 2)
    tp2 = round(price * 1.02, 2) if "LONG" in signal_type else round(price * 0.98, 2)
    tp3 = round(price * 1.03, 2) if "LONG" in signal_type else round(price * 0.97, 2)
    sl = round(price * 0.99, 2) if "LONG" in signal_type else round(price * 1.01, 2)
    
    # Generate uniform format text
    uniform_text = f"{symbol}|{signal_type}|{timestamp}|{timeframe}|{price}|{tp1}|{tp2}|{tp3}|{sl}"
    
    # Generate structured data
    structured_data = {
        "symbol": symbol,
        "signal_type": signal_type,
        "timestamp": timestamp,
        "timeframe": timeframe,
        "entry_price": price,
        "tp1_price": tp1,
        "tp2_price": tp2,
        "tp3_price": tp3,
        "sl_price": sl
    }
    
    return uniform_text, structured_data

async def process_text_request(text_data):
    """Process a text-based request"""
    try:
        start_time = time.time()
        result = parser.parse_text_alert(text_data)
        end_time = time.time()
        return True, end_time - start_time
    except Exception as e:
        return False, str(e)

async def process_json_request(json_data):
    """Process a JSON-based request"""
    try:
        start_time = time.time()
        result = parser.parse_webhook_data(json_data)
        end_time = time.time()
        return True, end_time - start_time
    except Exception as e:
        return False, str(e)

async def run_load_test():
    """Run the load test"""
    print(f"Starting load test with {NUM_REQUESTS} requests and concurrency {CONCURRENCY}")
    print("Legacy code is disabled for this test")
    
    # Disable legacy code
    patchers = disable_legacy_code()
    
    # Initialize metrics
    total_requests = 0
    successful_requests = 0
    failed_requests = 0
    total_time = 0
    max_time = 0
    min_time = float('inf')
    
    # Create a semaphore to limit concurrency
    semaphore = asyncio.Semaphore(CONCURRENCY)
    
    async def process_request(i):
        """Process a single request with concurrency control"""
        nonlocal total_requests, successful_requests, failed_requests, total_time, max_time, min_time
        
        async with semaphore:
            # Generate test data
            text_data, json_data = generate_test_data()
            
            # Randomly choose between text and JSON processing
            if random.choice([True, False]):
                success, result = await process_text_request(text_data)
            else:
                success, result = await process_json_request(json_data)
            
            # Update metrics
            total_requests += 1
            if success:
                successful_requests += 1
                total_time += result
                max_time = max(max_time, result)
                min_time = min(min_time, result)
            else:
                failed_requests += 1
            
            # Report progress
            if total_requests % REPORT_INTERVAL == 0:
                print(f"Processed {total_requests}/{NUM_REQUESTS} requests, "
                      f"{successful_requests} successful, {failed_requests} failed")
    
    # Create tasks for all requests
    tasks = [process_request(i) for i in range(NUM_REQUESTS)]
    
    # Wait for all tasks to complete
    start_time = time.time()
    await asyncio.gather(*tasks)
    end_time = time.time()
    
    # Calculate metrics
    total_elapsed = end_time - start_time
    avg_time = total_time / successful_requests if successful_requests > 0 else 0
    requests_per_second = total_requests / total_elapsed
    
    # Print results
    print("\nLoad Test Results:")
    print(f"Total requests: {total_requests}")
    print(f"Successful requests: {successful_requests}")
    print(f"Failed requests: {failed_requests}")
    print(f"Success rate: {successful_requests / total_requests * 100:.2f}%")
    print(f"Total elapsed time: {total_elapsed:.2f} seconds")
    print(f"Average request time: {avg_time * 1000:.2f} ms")
    print(f"Min request time: {min_time * 1000:.2f} ms")
    print(f"Max request time: {max_time * 1000:.2f} ms")
    print(f"Requests per second: {requests_per_second:.2f}")
    
    # Check legacy code usage
    legacy_stats = get_legacy_usage_stats()
    print("\nLegacy Code Usage:")
    print(json.dumps(legacy_stats, indent=2))
    
    # Re-enable legacy code
    enable_legacy_code(patchers)
    
    return {
        "total_requests": total_requests,
        "successful_requests": successful_requests,
        "failed_requests": failed_requests,
        "success_rate": successful_requests / total_requests * 100,
        "total_elapsed_time": total_elapsed,
        "average_request_time": avg_time * 1000,
        "min_request_time": min_time * 1000,
        "max_request_time": max_time * 1000,
        "requests_per_second": requests_per_second
    }

def save_results(results):
    """Save load test results to a file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"load_test_results_{timestamp}.json"
    filepath = os.path.join(os.path.dirname(__file__), filename)
    
    with open(filepath, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"Results saved to {filepath}")

if __name__ == "__main__":
    # Run the load test
    results = asyncio.run(run_load_test())
    
    # Save results
    save_results(results)
