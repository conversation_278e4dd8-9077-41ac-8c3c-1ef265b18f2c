"""
Test configuration for disabling legacy code
"""

import os
import sys
from unittest.mock import patch

# Add src directory to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

# Import legacy flags
from src.config.legacy_flags import LegacyFeatureFlags

# Create a patched version of legacy flags with all legacy features disabled
class TestLegacyFeatureFlags(LegacyFeatureFlags):
    """Test version of legacy feature flags with all legacy features disabled"""
    
    def __init__(self):
        """Initialize with all legacy features disabled"""
        # Don't call super().__init__() to avoid reading from environment
        self.USE_LEGACY_PARSERS = False
        self.USE_LEGACY_STORAGE = False
        self.USE_LEGACY_VISUALIZERS = False
        self.USE_LEGACY_IMPORTS = False
        
        # Log the status of legacy feature flags
        self._log_feature_flags()

def disable_legacy_code():
    """
    Disable all legacy code by patching the legacy flags
    
    Usage:
        from test_config import disable_legacy_code
        
        # In test setup
        disable_legacy_code()
        
        # Run tests with legacy code disabled
    """
    # Create patcher for legacy flags
    legacy_flags_patcher = patch('src.config.legacy_flags.legacy_flags', TestLegacyFeatureFlags())
    legacy_flags_patcher.start()
    
    # Create patcher for use_legacy_parsers function
    use_legacy_parsers_patcher = patch('src.config.use_legacy_parsers', return_value=False)
    use_legacy_parsers_patcher.start()
    
    # Create patcher for use_legacy_storage function
    use_legacy_storage_patcher = patch('src.config.use_legacy_storage', return_value=False)
    use_legacy_storage_patcher.start()
    
    # Create patcher for use_legacy_visualizers function
    use_legacy_visualizers_patcher = patch('src.config.use_legacy_visualizers', return_value=False)
    use_legacy_visualizers_patcher.start()
    
    # Create patcher for use_legacy_imports function
    use_legacy_imports_patcher = patch('src.config.use_legacy_imports', return_value=False)
    use_legacy_imports_patcher.start()
    
    return [
        legacy_flags_patcher,
        use_legacy_parsers_patcher,
        use_legacy_storage_patcher,
        use_legacy_visualizers_patcher,
        use_legacy_imports_patcher
    ]

def enable_legacy_code(patchers):
    """
    Re-enable legacy code by stopping the patchers
    
    Args:
        patchers: List of patchers returned by disable_legacy_code()
    """
    for patcher in patchers:
        patcher.stop()
