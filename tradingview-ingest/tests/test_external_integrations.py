"""
Tests for external system integrations with legacy code disabled
"""

import os
import sys
import json
import pytest
import asyncio
from unittest.mock import patch, MagicMock

# Add src directory to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

# Import test configuration
from test_config import disable_legacy_code, enable_legacy_code

# Import components to test
from src.parser import UnifiedParser
from src.storage_manager import StorageManager
from src.webhook_core import WebhookCore


class TestExternalIntegrations:
    """Test suite for external system integrations with legacy code disabled"""
    
    def setup_method(self):
        """Set up test environment with legacy code disabled"""
        # Disable all legacy code
        self.patchers = disable_legacy_code()
        
        # Create test instances
        self.parser = UnifiedParser()
        self.storage_manager = StorageManager()
        self.webhook_core = WebhookCore()
        
        # Sample data for testing
        self.uniform_text = "AAPL|LONG_ENTRY|1625097600|1h|150.00|155.00|160.00|165.00|145.00"
        self.webhook_data = {
            "symbol": "AAPL",
            "signal_type": "LONG_ENTRY",
            "timestamp": 1625097600,
            "timeframe": "1h",
            "entry_price": 150.00,
            "tp1_price": 155.00,
            "tp2_price": 160.00,
            "tp3_price": 165.00,
            "sl_price": 145.00
        }
        
        # TradingView webhook format
        self.tradingview_webhook = {
            "symbol": "AAPL",
            "exchange": "NASDAQ",
            "price": 150.00,
            "volume": 1000000,
            "time": "2023-09-07T12:00:00Z",
            "timenow": "2023-09-07T12:00:01Z",
            "action": "buy",
            "position_size": 100,
            "comment": "LONG_ENTRY",
            "alert_name": "AAPL Long Entry"
        }
    
    def teardown_method(self):
        """Clean up test environment"""
        # Re-enable legacy code
        enable_legacy_code(self.patchers)
    
    @patch('httpx.AsyncClient.post')
    async def test_tradingview_webhook_integration(self, mock_post):
        """Test integration with TradingView webhooks"""
        # Configure mock
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_post.return_value = mock_response
        
        # Process webhook
        with patch.object(self.webhook_core, 'process_webhook') as mock_process:
            mock_process.return_value = {"success": True, "webhook_id": "test123"}
            
            # Simulate webhook from TradingView
            result = await self.webhook_core.process_webhook(self.tradingview_webhook)
            
            # Verify that webhook was processed correctly
            assert result["success"] is True
            mock_process.assert_called_once()
    
    @patch('src.storage_manager.StorageManager.db_pool')
    async def test_database_integration(self, mock_db_pool):
        """Test integration with database"""
        # Configure mock
        mock_connection = MagicMock()
        mock_connection.fetch.return_value = [{"id": 1, "symbol": "AAPL"}]
        mock_connection.execute.return_value = "INSERT 1"
        
        mock_pool = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_connection
        mock_db_pool.return_value = mock_pool
        
        # Test database operations
        with patch.object(self.storage_manager, 'store_webhook_alert') as mock_store:
            mock_store.return_value = True
            
            # Store webhook data
            result = await self.storage_manager.store_webhook_alert(self.webhook_data)
            
            # Verify that data was stored correctly
            assert result is True
            mock_store.assert_called_once_with(self.webhook_data)
    
    @patch('src.alert_engine.AlertEngine.send_webhook_alert')
    async def test_external_webhook_integration(self, mock_send):
        """Test integration with external webhook endpoints"""
        # Configure mock
        mock_send.return_value = True
        
        # Import alert engine
        from src.alert_engine import AlertEngine
        alert_engine = AlertEngine()
        
        # Test sending webhook alert
        with patch.object(alert_engine, 'send_webhook_alert') as mock_send_webhook:
            mock_send_webhook.return_value = True
            
            # Send webhook alert
            result = await alert_engine.send_webhook_alert(self.webhook_data)
            
            # Verify that alert was sent correctly
            assert result is True
            mock_send_webhook.assert_called_once()
    
    @patch('src.alert_engine.AlertEngine.send_discord_alert')
    async def test_discord_integration(self, mock_send):
        """Test integration with Discord"""
        # Configure mock
        mock_send.return_value = True
        
        # Import alert engine
        from src.alert_engine import AlertEngine
        alert_engine = AlertEngine()
        
        # Test sending Discord alert
        with patch.object(alert_engine, 'send_discord_alert') as mock_send_discord:
            mock_send_discord.return_value = True
            
            # Send Discord alert
            result = await alert_engine.send_discord_alert(self.webhook_data)
            
            # Verify that alert was sent correctly
            assert result is True
            mock_send_discord.assert_called_once()
    
    async def test_api_integration(self):
        """Test integration with API endpoints"""
        # Import FastAPI app
        from src.main import app
        from fastapi.testclient import TestClient
        
        # Create test client
        client = TestClient(app)
        
        # Test health endpoint
        with patch('src.main.check_health') as mock_health:
            mock_health.return_value = {"status": "ok"}
            
            # Call health endpoint
            response = client.get("/health")
            
            # Verify response
            assert response.status_code == 200
            assert response.json() == {"status": "ok"}
    
    async def test_supabase_integration(self):
        """Test integration with Supabase"""
        # Import Supabase client
        from src.supabase_client import supabase_client
        
        # Test Supabase operations
        with patch.object(supabase_client, 'table') as mock_table:
            mock_select = MagicMock()
            mock_select.execute.return_value.data = [{"id": 1, "symbol": "AAPL"}]
            mock_table.return_value.select.return_value = mock_select
            
            # Query Supabase
            result = await supabase_client.table("webhooks").select("*").execute()
            
            # Verify result
            assert result.data == [{"id": 1, "symbol": "AAPL"}]
            mock_table.assert_called_once_with("webhooks")


if __name__ == "__main__":
    pytest.main(["-v", "test_external_integrations.py"])
