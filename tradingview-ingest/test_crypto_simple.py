#!/usr/bin/env python3
"""
Simple Crypto Webhook Test
Tests crypto-specific webhook data without complex dependencies
"""

import requests
import json
import time
import hmac
import hashlib

# Configuration
WEBHOOK_URL = "https://4eb03884db16.ngrok-free.app/webhook/tradingview"
WEBHOOK_SECRET = "test_webhook_secret"

def generate_signature(payload: dict, secret: str) -> str:
    """Generate HMAC signature for webhook payload"""
    payload_str = json.dumps(payload, separators=(',', ':'))
    signature = hmac.new(
        secret.encode('utf-8'),
        payload_str.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    return signature

def test_crypto_webhook():
    """Test crypto webhook with realistic data"""
    
    # Test 1: Bitcoin price alert
    btc_alert = {
        "symbol": "BTCUSD",
        "exchange": "BINANCE",
        "price": 43250.75,
        "volume": 1250000000,
        "timestamp": int(time.time()),
        "event_type": "price_update",
        "timeframe": "1h",
        "change_24h": 2.5,
        "market_cap": 850000000000
    }
    
    # Test 2: Ethereum RSI alert
    eth_rsi_alert = {
        "symbol": "ETHUSD",
        "exchange": "BINANCE",
        "rsi": 72.5,
        "macd": 15.3,
        "timestamp": int(time.time()),
        "event_type": "indicator_update",
        "timeframe": "4h",
        "price": 2650.25
    }
    
    # Test 3: Solana buy signal
    sol_signal = {
        "symbol": "SOLUSD",
        "exchange": "BINANCE",
        "signal": "BUY",
        "strength": 0.88,
        "entry_price": 98.50,
        "stop_loss": 92.00,
        "take_profit": 110.00,
        "timestamp": int(time.time()),
        "event_type": "signal_generated",
        "timeframe": "1h",
        "confidence": 0.85
    }
    
    # Test 4: Crypto market sentiment
    sentiment_alert = {
        "symbol": "CRYPTO_MARKET",
        "exchange": "GLOBAL",
        "fear_greed_index": 65,
        "btc_dominance": 52.3,
        "total_market_cap": 2100000000000,
        "timestamp": int(time.time()),
        "event_type": "market_sentiment",
        "trend": "bullish"
    }
    
    tests = [
        ("Bitcoin Price Alert", btc_alert),
        ("Ethereum RSI Alert", eth_rsi_alert),
        ("Solana Buy Signal", sol_signal),
        ("Market Sentiment", sentiment_alert)
    ]
    
    print("🚀 Testing Crypto Webhook Integration")
    print("=" * 50)
    print()
    
    success_count = 0
    
    for test_name, payload in tests:
        try:
            # Generate signature
            signature = generate_signature(payload, WEBHOOK_SECRET)
            
            # Send request
            headers = {
                "Content-Type": "application/json",
                "X-TradingView-Signature": signature
            }
            
            response = requests.post(WEBHOOK_URL, json=payload, headers=headers, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {test_name}")
                print(f"   Status: {response.status_code}")
                print(f"   Symbol: {payload.get('symbol', 'N/A')}")
                print(f"   Event: {payload.get('event_type', 'N/A')}")
                print(f"   Response: {response.json()}")
                success_count += 1
            else:
                print(f"❌ {test_name}")
                print(f"   Status: {response.status_code}")
                print(f"   Error: {response.text}")
            
            print()
            
        except Exception as e:
            print(f"❌ {test_name}")
            print(f"   Error: {str(e)}")
            print()
    
    # Summary
    print("=" * 50)
    print(f"📊 Crypto Webhook Test Results: {success_count}/{len(tests)} successful")
    
    if success_count == len(tests):
        print("🎉 All crypto webhooks processed successfully!")
        print("   Your system is ready for TradingView crypto alerts!")
    else:
        print("⚠️  Some crypto webhooks failed. Check the logs for details.")
    
    print()
    print("🔗 Webhook URL for TradingView:")
    print(f"   {WEBHOOK_URL}")
    print()
    print("💡 Next: Configure this URL in TradingView with crypto pairs!")

if __name__ == "__main__":
    test_crypto_webhook() 