#!/usr/bin/env python3
"""
Test script for TradingView webhook alerts
Tests various alert formats to ensure they're processed correctly
"""

import asyncio
import json
import time
import hmac
import hashlib
import requests
from typing import Dict, Any

# Configuration
WEBHOOK_URL = "https://4eb03884db16.ngrok-free.app/webhook/tradingview"
WEBHOOK_SECRET = "test_webhook_secret"  # Must match your .env file

def generate_signature(payload: Dict[str, Any], secret: str) -> str:
    """Generate HMAC signature for webhook payload"""
    payload_str = json.dumps(payload, separators=(',', ':'))
    signature = hmac.new(
        secret.encode('utf-8'),
        payload_str.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    return signature

def test_webhook_with_signature(payload: Dict[str, Any], description: str):
    """Test webhook with proper signature"""
    try:
        # Generate signature
        signature = generate_signature(payload, WEBHOOK_SECRET)
        
        # Send request with signature
        headers = {
            "Content-Type": "application/json",
            "X-TradingView-Signature": signature
        }
        
        response = requests.post(WEBHOOK_URL, json=payload, headers=headers, timeout=10)
        
        print(f"✅ {description}")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        print()
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {str(e)}")
        print()
        return False

def test_webhook_without_signature(payload: Dict[str, Any], description: str):
    """Test webhook without signature (should fail)"""
    try:
        headers = {"Content-Type": "application/json"}
        response = requests.post(WEBHOOK_URL, json=payload, headers=headers, timeout=10)
        
        print(f"🔒 {description}")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        print()
        
        return response.status_code == 401  # Should be unauthorized
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {str(e)}")
        print()
        return False

def main():
    """Run webhook tests"""
    print("🧪 Testing TradingView Webhook Integration")
    print("=" * 50)
    print()
    
    # Test 1: Price update alert
    price_alert = {
        "symbol": "AAPL",
        "exchange": "NASDAQ",
        "price": 150.25,
        "volume": 1000000,
        "timestamp": int(time.time()),
        "event_type": "price_update"
    }
    
    # Test 2: Technical indicator alert
    indicator_alert = {
        "symbol": "TSLA",
        "exchange": "NASDAQ",
        "rsi": 70.5,
        "macd": 2.3,
        "timestamp": int(time.time()),
        "event_type": "indicator_update"
    }
    
    # Test 3: Trading signal alert
    signal_alert = {
        "symbol": "NVDA",
        "exchange": "NASDAQ",
        "signal": "BUY",
        "strength": 0.85,
        "entry_price": 450.00,
        "stop_loss": 420.00,
        "take_profit": 500.00,
        "timestamp": int(time.time()),
        "event_type": "signal_generated"
    }
    
    # Test 4: Pine Script format alert (text)
    pine_alert = {
        "raw_text": "AAPL|BUY_SIGNAL|1745961600|1h|150.25|155.00|160.00|165.00|145.00"
    }
    
    # Run tests
    tests = [
        (price_alert, "Price Update Alert (with signature)"),
        (indicator_alert, "Technical Indicator Alert (with signature)"),
        (signal_alert, "Trading Signal Alert (with signature)"),
        (pine_alert, "Pine Script Format Alert (with signature)")
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for payload, description in tests:
        if test_webhook_with_signature(payload, description):
            success_count += 1
    
    # Test without signature (should fail)
    print("🔒 Testing Security - Webhook without signature (should fail):")
    test_webhook_without_signature(price_alert, "Price Alert without signature")
    
    # Summary
    print("=" * 50)
    print(f"📊 Test Results: {success_count}/{total_tests} webhooks processed successfully")
    
    if success_count == total_tests:
        print("🎉 All webhook tests passed! TradingView integration is working.")
    else:
        print("⚠️  Some webhook tests failed. Check the logs for details.")
    
    print()
    print("🔗 Your webhook URL for TradingView:")
    print(f"   {WEBHOOK_URL}")
    print()
    print("🔑 Webhook Secret (configure in TradingView):")
    print(f"   {WEBHOOK_SECRET}")

if __name__ == "__main__":
    main() 