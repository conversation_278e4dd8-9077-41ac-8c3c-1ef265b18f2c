-- Initialize database for TradingView webhook data
-- Simple schema for easy research and analysis

-- Create webhook_data table for raw webhook storage
CREATE TABLE IF NOT EXISTS webhook_data (
    id SERIAL PRIMARY KEY,
    webhook_id VARCHAR(50) UNIQUE NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    timestamp BIGINT NOT NULL,
    event_type VARCHAR(50),
    raw_data JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create trading_alerts table for parsed alert data
CREATE TABLE IF NOT EXISTS trading_alerts (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    signal_type VARCHAR(50),
    confidence DECIMAL(3,2),
    timestamp BIGINT NOT NULL,
    price DECIMAL(15,6),
    indicators_used JSONB,
    reasoning TEXT,
    raw_data JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for fast querying
CREATE INDEX IF NOT EXISTS idx_webhook_data_symbol ON webhook_data(symbol);
CREATE INDEX IF NOT EXISTS idx_webhook_data_timestamp ON webhook_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_webhook_data_event_type ON webhook_data(event_type);

CREATE INDEX IF NOT EXISTS idx_trading_alerts_symbol ON trading_alerts(symbol);
CREATE INDEX IF NOT EXISTS idx_trading_alerts_timestamp ON trading_alerts(timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_alerts_signal_type ON trading_alerts(signal_type);

-- Create a view for easy research queries
CREATE OR REPLACE VIEW webhook_summary AS
SELECT 
    symbol,
    COUNT(*) as total_alerts,
    COUNT(CASE WHEN event_type = 'price_update' THEN 1 END) as price_updates,
    COUNT(CASE WHEN event_type = 'signal_generated' THEN 1 END) as signals,
    COUNT(CASE WHEN event_type = 'indicator_update' THEN 1 END) as indicators,
    MIN(created_at) as first_alert,
    MAX(created_at) as last_alert
FROM webhook_data 
GROUP BY symbol
ORDER BY total_alerts DESC;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tradingview_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tradingview_user; 