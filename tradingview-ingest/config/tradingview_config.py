"""
TradingView Ingestion Configuration
"""
import os
from typing import Optional, Dict, Any, List

# Simple configuration class without pydantic dependency
class TradingViewConfig:
    """Configuration for TradingView ingestion system"""
    
    def __init__(self):
        # Database Configuration - Using Supabase only
        db_url = os.getenv("SUPABASE_DB_URL", "")
        # Convert SQLAlchemy format to asyncpg format if needed
        if db_url and "+asyncpg" in db_url:
            self.database_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
        else:
            self.database_url = db_url
        
        # Supabase Configuration
        self.supabase_url = os.getenv("SUPABASE_URL", "")
        self.supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "")
        self.use_supabase = os.getenv("USE_SUPABASE", "true").lower() == "true"
        
        # Redis Configuration
        redis_host = os.getenv("REDIS_HOST", "redis")
        redis_port = os.getenv("REDIS_PORT", "6379")
        redis_password = os.getenv("REDIS_PASSWORD", "")
        if redis_password:
            self.redis_url = f"redis://:{redis_password}@{redis_host}:{redis_port}"
        else:
            self.redis_url = f"redis://{redis_host}:{redis_port}"
        
        # Webhook Configuration
        self.webhook_secret = os.getenv("WEBHOOK_SECRET")
        self.webhook_endpoint = "/webhook/tradingview"
        
        # Server Configuration
        self.host = "0.0.0.0"
        self.port = int(os.getenv("PORT", "8001"))
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # Logging Configuration
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_format = "json"
        
        # Data Processing Configuration
        self.batch_size = int(os.getenv("BATCH_SIZE", "100"))
        self.processing_delay = float(os.getenv("PROCESSING_DELAY", "0.1"))  # seconds
        
        # Security Configuration
        self.rate_limit_per_minute = int(os.getenv("RATE_LIMIT_PER_MINUTE", "100"))  # Reduced for security
        self.max_payload_size = 1024 * 1024  # 1MB max for security
        self.webhook_ip_whitelist = []  # Empty means no IP restrictions
        self.require_signature = os.getenv("REQUIRE_WEBHOOK_SIGNATURE", "true").lower() == "true"
        self.max_webhook_age_seconds = 300  # 5 minutes max age
        
        # TradingView Specific Configuration
        self.supported_exchanges = ["NASDAQ", "NYSE", "AMEX", "BATS", "ARCA"]
        self.supported_timeframes = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
        
        # Alert Configuration
        self.enable_alerts = True
        self.alert_webhook_url = os.getenv("ALERT_WEBHOOK_URL")
        self.discord_webhook_url = os.getenv("DISCORD_WEBHOOK_URL")
    
# Global configuration instance
config = TradingViewConfig()

# Environment-specific overrides
environment = os.getenv("ENVIRONMENT", "development")
if environment == "production":
    config.debug = False
    config.log_level = "WARNING"
elif environment == "development":
    config.debug = True
    config.log_level = "DEBUG"