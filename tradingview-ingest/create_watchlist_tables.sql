-- Watchlist Database Schema
-- Creates tables for user watchlists and symbol tracking

-- User watchlists table
CREATE TABLE IF NOT EXISTS user_watchlists (
    id SERIAL PRIMARY KEY,
    discord_user_id VARCHAR(50) NOT NULL,
    watchlist_name VARCHAR(100) NOT NULL DEFAULT 'Default',
    is_active B<PERSON>OLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(discord_user_id, watchlist_name)
);

-- Watchlist symbols table
CREATE TABLE IF NOT EXISTS watchlist_symbols (
    id SERIAL PRIMARY KEY,
    watchlist_id INTEGER REFERENCES user_watchlists(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    alert_threshold DECIMAL(10,4),
    alert_type VARCHAR(20) DEFAULT 'price_change', -- price_change, volume_spike, technical_signal
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(watchlist_id, symbol)
);

-- Watchlist alerts table
CREATE TABLE IF NOT EXISTS watchlist_alerts (
    id SERIAL PRIMARY KEY,
    watchlist_id INTEGER REFERENCES user_watchlists(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    alert_type VARCHAR(20) NOT NULL,
    trigger_value DECIMAL(10,4),
    current_value DECIMAL(10,4),
    message TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    discord_user_id VARCHAR(50) NOT NULL
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_watchlists_discord_id ON user_watchlists(discord_user_id);
CREATE INDEX IF NOT EXISTS idx_watchlist_symbols_watchlist_id ON watchlist_symbols(watchlist_id);
CREATE INDEX IF NOT EXISTS idx_watchlist_symbols_symbol ON watchlist_symbols(symbol);
CREATE INDEX IF NOT EXISTS idx_watchlist_alerts_watchlist_id ON watchlist_alerts(watchlist_id);
CREATE INDEX IF NOT EXISTS idx_watchlist_alerts_symbol ON watchlist_alerts(symbol);

-- Insert default watchlist for testing
INSERT INTO user_watchlists (discord_user_id, watchlist_name) 
VALUES ('test_user_123', 'Default')
ON CONFLICT (discord_user_id, watchlist_name) DO NOTHING;

-- Insert some test symbols
INSERT INTO watchlist_symbols (watchlist_id, symbol, notes) 
SELECT w.id, 'BTCUSDT', 'Bitcoin - main crypto'
FROM user_watchlists w 
WHERE w.discord_user_id = 'test_user_123'
ON CONFLICT (watchlist_id, symbol) DO NOTHING;

INSERT INTO watchlist_symbols (watchlist_id, symbol, notes) 
SELECT w.id, 'ETHUSDT', 'Ethereum - smart contract platform'
FROM user_watchlists w 
WHERE w.discord_user_id = 'test_user_123'
ON CONFLICT (watchlist_id, symbol) DO NOTHING; 