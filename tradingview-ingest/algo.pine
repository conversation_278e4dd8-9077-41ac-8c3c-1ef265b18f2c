//@version=6   
// ============================================================================
// NHX TRADING ALGORITHM - COMPREHENSIVE TECHNICAL ANALYSIS SYSTEM
// ============================================================================
// This algorithm combines multiple SuperTrend configurations, multi-timeframe
// analysis, confidence scoring, volume analysis, and ML-based trend detection
// with comprehensive data dump functionality for AI integration.
// ============================================================================
 
strategy('  NHX.ai  ', overlay = true, initial_capital = 1000, default_qty_type = strategy.fixed, default_qty_value = 1, calc_on_every_tick = true, pyramiding = 0, max_bars_back = 4999, max_lines_count = 500, max_labels_count = 500, dynamic_requests = true) // Set default quantity to 1 for basic testing. User should adjust for live trading.

// Import the NightHawk Pro library for time filtering
import NightHawk_Pro/NHX_StrategyLib/1 as nhx
  
// ============================================================================
// GLOBAL CONSTANTS AND COLOR DEFINITIONS
// ============================================================================
bullColor = #2ecc71      // Green for bullish signals
bearColor = #e74c3c      // Red for bearish signals  
neutralColor = #f1c40f   // Yellow for neutral conditions

// ============================================================================
// TIME FILTER SETTINGS
// ============================================================================
// Configure trading hours and days for session-based filtering
useTimeFilter = input.bool(true, '🕒 Enable Time Filter', group='Time Settings', display = display.none)

// Trading Hours Configuration (Local Chart Time)
startH = input.int(9,  'Start Hour (Local)',   minval=0, maxval=23, group='Time Settings', inline='time', display = display.none)
startM = input.int(30, 'Start Minute',         minval=0, maxval=59, group='Time Settings', inline='time', display = display.none)
endH   = input.int(16, 'End Hour (Local)',     minval=0, maxval=23, group='Time Settings', inline='time', display = display.none)
endM   = input.int(0,  'End Minute',           minval=0, maxval=59, group='Time Settings', inline='time', display = display.none)

// Trading Days Configuration (Enable/Disable specific days)
tradeMon = input.bool(true,  'Mon', group='Time Settings', inline='days', display = display.none)
tradeTue = input.bool(true,  'Tue', group='Time Settings', inline='days', display = display.none)
tradeWed = input.bool(true,  'Wed', group='Time Settings', inline='days', display = display.none)
tradeThu = input.bool(true,  'Thu', group='Time Settings', inline='days', display = display.none)
tradeFri = input.bool(true,  'Fri', group='Time Settings', inline='days', display = display.none)
tradeSat = input.bool(false, 'Sat', group='Time Settings', inline='days', display = display.none)
tradeSun = input.bool(false, 'Sun', group='Time Settings', inline='days', display = display.none)

// Timezone offset for different chart time zones
timeOffset = input.int(0, "Timezone Offset (hours from chart time)", minval=-12, maxval=12, group="Time Settings", display = display.none)

// Initialize time filter using NightHawk Pro library
[isTradingTime, isOvernight, inSession, isTradingDay] = nhx.timeFilter(useTimeFilter, startH, startM, endH, endM, timeOffset, tradeMon, tradeTue, tradeWed, tradeThu, tradeFri)

// After hours behavior configuration
after_hours_behavior = input.string('Close Positions', title = '📈 After Hours', options = ['Close Positions', 'Hold Until Signal'],
     tooltip = 'What to do with open positions when trading hours end', group = 'Time Settings', display = display.none)
// ============================================================================
// STRATEGY AND SIGNAL SETTINGS
// ============================================================================
// Main strategy preset selection - determines SuperTrend parameters and behavior
preset = input.string('Balanced Daytrader', '🦅Strategy Preset',      options = ['Ultra Trend Follower', 'Conservative Swing', 'Breakout Sentinel', 'Precision Swing Scalper', 'Moderate Swing Trader', 'Balanced Daytrader', 'Aggressive Daytrader', 'Dynamic Swinger'],      group = 'Signal Settings', display = display.none)

// SuperTrend model selection - Classic vs Dynamic Adaptive
st_model = input.string('Classic', title = '🧠 SuperTrend Model',      options = ['Classic', 'Dynamic Adaptive'],      tooltip = 'Choose between Classic SuperTrend or Dynamic Adaptive SuperTrend that responds to market conditions.',      group = 'Signal Settings', display = display.none)

// ============================================================================
// LINE DISTANCE AND MULTIPLIER SETTINGS
// ============================================================================
// Global multipliers for adjusting all line distances
global_line_multiplier = input.float(1.0, title = '🎯 Global Line Distance Multiplier',      step = 0.1, minval = 0.1, maxval = 5.0,      tooltip = 'Multiplies all take profit and stop loss distances. Use to adjust overall size.',      group = 'Line Settings', display = display.none)

sl_distance_multiplier = input.float(1.0, title = '🛑 Stop Loss Distance Multiplier',      step = 0.1, minval = 0.1, maxval = 5.0,      tooltip = 'Additional multiplier for stop loss distance only.',      group = 'Line Settings', display = display.none)
// ============================================================================
// TAKE PROFIT DISTRIBUTION SETTINGS
// ============================================================================
// Position size distribution across three take profit levels
tp_distribution = input.string('33-33-33', title = '🎯 TP Distribution',      options = ['33-33-33', '40-40-20', '20-30-50', '50-30-20', '50-15-15-Flip', '40-20-10-Flip', '40-20-20-Flip', '30-30-25-Flip', '20-20-20-Flip', '15-50-15-Flip', '15-35-35-Flip', '10-10-60-Flip', 'Custom'],      tooltip = 'Distribution of position size across take profit levels. \'Flip\' means remaining % is held through signal change for potential trend continuation.',      group = 'TP Distribution Settings', display = display.none)

// Custom take profit percentages (only used when distribution is set to 'Custom')
custom_tp1 = input.float(33, title = '💰 Custom TP1 %', minval = 0, maxval = 100,      tooltip = 'Custom percentage for first take profit level. Only used when TP Distribution is set to \'Custom\'.',      group = 'TP Distribution Settings', display = display.none)
custom_tp2 = input.float(33, title = '💰 Custom TP2 %', minval = 0, maxval = 100,      tooltip = 'Custom percentage for second take profit level. Only used when TP Distribution is set to \'Custom\'.',      group = 'TP Distribution Settings', display = display.none)
custom_tp3 = input.float(34, title = '💰 Custom TP3 %', minval = 0, maxval = 100,      tooltip = 'Custom percentage for third take profit level. Only used when TP Distribution is set to \'Custom\'.',      group = 'TP Distribution Settings', display = display.none)

// ============================================================================
// DYNAMIC TAKE PROFIT SETTINGS
// ============================================================================
// Take profit style determines aggressiveness of TP levels
tp_preset = input.string('Moderate', title = '🎯Take Profit Style',      options = ['Scalper', 'Conservative', 'Moderate', 'Aggressive'],      tooltip = 'Determines the aggressiveness of take profit levels. Scalper is tightest, Aggressive is widest.',      group = 'Dynamic TP Settings', display = display.none)

// Global multiplier for all TP and SL levels
global_tp_multiplier = input.float(1.0, title = '🎯 Global TP/SL Size Multiplier',      step = 0.1, minval = 0.1, maxval = 5.0,      tooltip = 'Multiplies all take profit and stop loss levels. Use to adjust overall trade size based on volatility.',      group = 'Dynamic TP Settings', display = display.none)

// ============================================================================
// STOP LOSS SETTINGS
// ============================================================================
// Fine-tune stop loss distance independently from TP levels
sl_fine_tune = input.float(1.0, title = '🛑 SL Fine-Tune Multiplier',      step = 0.1, minval = 0.1, maxval = 5.0,      tooltip = 'Fine-tune stop loss distance independently. Higher values place stop loss further away.',      group = 'Stop Loss Settings', display = display.none)

// Stop loss behavior when hit
sl_behavior = input.string('Do Nothing', title = '🛑 Stop Loss Behavior',      options = ['Do Nothing', 'Stop Loss', 'Average Down'],      tooltip = 'How to handle stop loss hits: Close position, ignore, or attempt to average down.',      group = 'Stop Loss Settings', display = display.none)

// ============================================================================
// CONFLUENCE SETTINGS
// ============================================================================
// Hull Moving Average (HMA) period configuration for trend confluence
hmaPreset = input.string('Moderate', title = '📈SMA Style',      options = ['Safest', 'Cautious', 'Moderate', 'Dynamic', 'Aggressive', 'Scalp'],      tooltip = 'Adjusts the HMA (Hull Moving Average) period. Safest uses longest period, Scalp uses shortest.',      group = 'Confluence Settings', display = display.none)

// Machine Learning analysis style configuration
ml_style = input.string('Balanced (Original)', title = '🧠 ML Style',      options = ['Fast Scalping', 'Balanced (Original)', 'Swing Trading', 'Trend Following'],      tooltip = 'Machine Learning analysis style. Affects how quickly the ML system adapts to market changes.',      group = 'Confluence Settings', display = display.none)

// ============================================================================
// VISUAL SETTINGS
// ============================================================================
// Line styles for different visual elements
line_style_1 = input.string('Solid', title = '📏 Line Style 1 (Thick)',      options = ['Solid', 'Dashed', 'Dotted'],      tooltip = 'Style for thick lines showing active take profit levels.',      group = 'Visual Settings', display = display.none)

line_style_2 = input.string('Dashed', title = '📏 Line Style 2 (Thin)',      options = ['Solid', 'Dashed', 'Dotted'],      tooltip = 'Style for thin reference lines that show potential targets.',      group = 'Visual Settings', display = display.none)

// Historical line display options
show_plot_lines = input.bool(false, title = '🧾 Show Historical TP Lines',      tooltip = 'Show take profit lines for historical trades. Useful for backtesting analysis.',      group = 'Visual Settings', display = display.none)

// Label positioning configuration
label_x_offset_bars = input.int(2, title = '🔧 Label X Offset (bars)',      minval = 0, maxval = 20,      tooltip = 'Bars to offset labels from end of thin lines',      group = 'Visual Settings', display = display.none)

// ============================================================================
// PANEL SETTINGS
// ============================================================================
// Information panel display configuration
show_panel = input.bool(true, title = '📊 Show Info Panel',      tooltip = 'Display the information panel with ML insights and performance metrics.',      group = 'Panel Settings', display = display.none)

// Panel size and positioning
panel1_size_input = input.string('Tiny', title = '🔬 Panel Size',      options = ['Tiny', 'Small', 'Normal', 'Large', 'Extra Large'],      tooltip = 'Text size for the information panel.',      group = 'Panel Settings', inline = 'Panel', display = display.none)

panel_1_position = input.string('Top Right', title = '📍 Panel 1 Position',      options = ['Top Right', 'Top Left', 'Bottom Right', 'Bottom Left', 'Center Top', 'Center Bottom', 'Center Right', 'Center Left'],      tooltip = 'Screen position for the first panel.',      group = 'Panel Settings', display = display.none)

// ============================================================================
// CONFIDENCE SETTINGS
// ============================================================================
// Confidence calculation presets and smoothing
confidence_preset = input.string('Balanced', title='🛡️ Confidence Preset',      options=['Conservative', 'Balanced', 'Aggressive'],      group='Confidence Settings',      tooltip='Determines weighting for confidence score calculation.',      display = display.none)

confidence_smoothing = input.int(3, "Confidence Smoothing",      minval=1, maxval=10,      group="Confidence Settings",      tooltip="Number of bars to smooth confidence values over",      display = display.none)

// SuperTrend rolling window size (constant)
st_roll_window = 7

// ============================================================================
// LABEL CUSTOMIZATION
// ============================================================================
// Emoji settings for different label types
group_emojis = "Label Customization"
string tp1_emoji           = input.string("🎯", "TP1 Emoji", group=group_emojis, display = display.none)
string tp2_emoji           = input.string("🎯", "TP2 Emoji", group=group_emojis, display = display.none)
string tp3_emoji           = input.string("🎯", "TP3 Emoji", group=group_emojis, display = display.none)
string sl_emoji            = input.string("🛑", "SL Emoji", group=group_emojis, display = display.none)
string entry_emoji         = input.string("🦅", "Entry Emoji", group=group_emojis, display = display.none)
string hit_tp_emoji        = input.string("💰", "Hit TP Emoji", group=group_emojis, display = display.none)
string miss_tp_emoji       = input.string("❌", "Missed TP Emoji", group=group_emojis, display = display.none)
string hit_sl_emoji        = input.string("❌", "Hit SL Emoji", group=group_emojis, display = display.none)
string diamond_hands_emoji = input.string("💎", "Diamond Hands Emoji", group=group_emojis, display = display.none)
string rage_emoji          = input.string("😠", "Rage Emoji", group=group_emojis, display = display.none)

// ============================================================================
// DATA DUMP SETTINGS FOR AI INTEGRATION
// ============================================================================
// Configuration for webhook data export to AI trading system
data_dump_group = 'Data Dump Settings'

dump_all_indicators = input.bool(false, '✅ Dump ALL Indicators',
     group=data_dump_group,
     tooltip='Globally enable/disable all indicator data dumps below. Overrides individual settings.',
     inline='dumpall')

// Master control for historical data dumping
enableHistoryDump = input.bool(false, '🟢 ENABLE FULL HISTORY DUMP',      group=data_dump_group,      tooltip='On the last historical bar, send one webhook with enabled arrays for database seeding',      display = display.none)

// Maximum size for historical arrays
hist_array_max_size = input.int(1000, 'Max History Size',      group=data_dump_group,      tooltip='Max bars to keep for history arrays',      display = display.none)
hist_dump_items_per_series = input.int(40, 'Max Items Per Series in Payload', group=data_dump_group, tooltip='Caps items per array when building history dump payload to stay under Pine string length limits', display = display.none)

// Core data dump toggles - Primary analysis data
dump_strength = input.bool(true, '📈 Dump Strength Arrays',      group=data_dump_group, inline='dump1', display = display.none)
dump_confidence = input.bool(true, '🧠 Dump Confidence Arrays',      group=data_dump_group, inline='dump1', display = display.none)

// SuperTrend and volume data
dump_st_consensus = input.bool(true, '🤖 Dump ST Consensus',      group=data_dump_group, inline='dump2', display = display.none)
dump_volume = input.bool(true, '📊 Dump Volume Percent',      group=data_dump_group, inline='dump2', display = display.none)

// Signal price data
dump_signal_prices = input.bool(true, '🎯 Dump Signal Prices',      group=data_dump_group, inline='dump3', display = display.none)

// Optional technical indicators - Disabled by default for performance
dump_rsi = input.bool(false, '🧮 Dump RSI (14)',      group=data_dump_group, inline='dump4', display = display.none)
dump_adx = input.bool(false, '📐 Dump ADX (14)',      group=data_dump_group, inline='dump4', display = display.none)

// Additional optional indicators
dump_macd_hist = input.bool(false, '📊 Dump MACD Histogram',      group=data_dump_group, inline='dump5', display = display.none)
dump_atr_pct = input.bool(false, '🌡️ Dump ATR Percent',      group=data_dump_group, inline='dump5', display = display.none)

// Advanced technical indicators for comprehensive analysis
dump_directional_indicators = input.bool(false, '📐 Dump +DI/-DI',      group=data_dump_group, inline='dump6', display = display.none)
dump_momentum_indicators = input.bool(false, '🚀 Dump Momentum/Velocity',      group=data_dump_group, inline='dump6', display = display.none)
dump_market_state = input.bool(false, '🎯 Dump Market State',      group=data_dump_group, inline='dump7', display = display.none)
dump_trend_strength = input.bool(false, '💪 Dump Trend Strength',      group=data_dump_group, inline='dump7', display = display.none)
dump_stoch_rsi = input.bool(false, '📈 Dump Stochastic RSI',      group=data_dump_group, inline='dump8', display = display.none)
dump_volume_impulse = input.bool(false, '📊 Dump Volume Impulse',      group=data_dump_group, inline='dump8', display = display.none)

// ML Analysis and Market Intelligence indicators
dump_ml_analysis = input.bool(false, '🧠 Dump ML Analysis',      group=data_dump_group, inline='dump9', display = display.none)
dump_market_intelligence = input.bool(false, '🎯 Dump Market Intelligence',      group=data_dump_group, inline='dump9', display = display.none)
dump_consensus_data = input.bool(false, '🤝 Dump Consensus Data',      group=data_dump_group, inline='dump10', display = display.none)
dump_performance_metrics = input.bool(false, '📊 Dump Performance Metrics',      group=data_dump_group, inline='dump10', display = display.none)

// Advanced ML and Market State indicators for comprehensive AI analysis
dump_trend_state = input.bool(false, '🎯 Dump Advanced Trend State',      group=data_dump_group, inline='dump11', display = display.none)
dump_bullishness_level = input.bool(false, '📈 Dump Bullishness Level',      group=data_dump_group, inline='dump11', display = display.none)
dump_st_agreement = input.bool(false, '🤝 Dump ST Agreement',      group=data_dump_group, inline='dump12', display = display.none)
dump_mtf_bullish_count = input.bool(false, '📊 Dump MTF Bullish Count',      group=data_dump_group, inline='dump12', display = display.none)

// Multi-timeframe analysis indicators for comprehensive market analysis
dump_mtf_consensus = input.bool(false, '🔄 Dump MTF Consensus',      group=data_dump_group, inline='dump13', display = display.none)
dump_individual_tf_volumes = input.bool(false, '📊 Dump Individual TF Volumes',      group=data_dump_group, inline='dump13', display = display.none)
dump_directional_movement = input.bool(false, '📐 Dump Directional Movement',      group=data_dump_group, inline='dump14', display = display.none)
dump_volume_ratios = input.bool(false, '📈 Dump Volume Ratios',      group=data_dump_group, inline='dump14', display = display.none)

// ============================================================================
// GLOBAL HISTORICAL ARRAYS FOR DATA DUMP
// ============================================================================
// Arrays to store historical data for AI integration and webhook export
// These arrays maintain historical values for comprehensive market analysis

// Multi-timeframe strength arrays
var array<float> hist_m5_str = array.new_float(0)   // 5-minute timeframe strength
var array<float> hist_m15_str = array.new_float(0)  // 15-minute timeframe strength  
var array<float> hist_h1_str = array.new_float(0)   // 1-hour timeframe strength

// Confidence score arrays
var array<float> hist_bull_confidence = array.new_float(0)  // Bullish confidence scores
var array<float> hist_bear_confidence = array.new_float(0)  // Bearish confidence scores

// SuperTrend consensus and volume arrays
var array<float> hist_st_consensus = array.new_float(0)     // SuperTrend consensus percentage
var array<float> hist_overall_volume = array.new_float(0)   // Overall volume percentage

// Optional technical indicator arrays (enabled via dump settings)
var array<float> hist_rsi = array.new_float(0)        // RSI(14) historical values
var array<float> hist_adx = array.new_float(0)        // ADX(14) historical values
var array<float> hist_macd_hist = array.new_float(0)  // MACD histogram historical values
var array<float> hist_atr_pct = array.new_float(0)    // ATR percentage historical values

// Advanced technical indicator arrays for comprehensive AI analysis
var array<float> hist_plus_di = array.new_float(0)    // +DI (Positive Directional Indicator) values
var array<float> hist_minus_di = array.new_float(0)   // -DI (Negative Directional Indicator) values
var array<float> hist_price_momentum = array.new_float(0)  // Price momentum (ROC) values
var array<float> hist_price_velocity = array.new_float(0)  // Price velocity values
var array<string> hist_market_state = array.new<string>(0) // Market state classifications
var array<float> hist_trend_strength = array.new_float(0)  // Overall trend strength values
var array<float> hist_stoch_rsi = array.new_float(0)       // Stochastic RSI values
var array<bool> hist_volume_impulse = array.new<bool>(0)   // Volume impulse signals

// ML Analysis and Market Intelligence arrays for AI integration
var array<string> hist_trend_state = array.new<string>(0)  // Advanced trend state (BULL TREND, BEAR TREND, etc.)
var array<float> hist_bullishness_level = array.new_float(0) // Weighted bullishness calculation
var array<float> hist_st_agreement = array.new_float(0)    // SuperTrend agreement level
var array<int> hist_mtf_bullish_count = array.new_int(0)   // Multi-timeframe bullish count
var array<float> hist_confidence_score = array.new_float(0) // Overall confidence score
var array<float> hist_market_sentiment = array.new_float(0) // Market sentiment value

// Multi-timeframe analysis arrays for comprehensive market analysis
var array<float> hist_mtf_consensus = array.new_float(0)   // Multi-timeframe consensus values
var array<float> hist_m1_vol_percent = array.new_float(0)  // 1-minute volume percentage
var array<float> hist_m2_vol_percent = array.new_float(0)  // 2-minute volume percentage
var array<float> hist_m3_vol_percent = array.new_float(0)  // 3-minute volume percentage
var array<float> hist_m5_vol_percent = array.new_float(0)  // 5-minute volume percentage
var array<float> hist_m15_vol_percent = array.new_float(0) // 15-minute volume percentage
var array<float> hist_m30_vol_percent = array.new_float(0) // 30-minute volume percentage
var array<float> hist_h1_vol_percent = array.new_float(0)  // 1-hour volume percentage
var array<float> hist_h4_vol_percent = array.new_float(0)  // 4-hour volume percentage
var array<float> hist_d_vol_percent = array.new_float(0)   // Daily volume percentage
var array<float> hist_directional_movement = array.new_float(0) // Directional movement percentage
var array<float> hist_volume_ratio = array.new_float(0)    // Volume ratio (current vs SMA)

// ============================================================================
// LINE VARIABLES FOR VISUAL ELEMENTS
// ============================================================================
// Line objects for drawing take profit, stop loss, and entry levels on chart

// Main trading lines
var line entry_line = na        // Entry price line
var line sl_line = na          // Stop loss line

// Take profit lines - thin reference lines
var line tp1_thin_line = na    // TP1 thin reference line
var line tp2_thin_line = na    // TP2 thin reference line  
var line tp3_thin_line = na    // TP3 thin reference line

// Take profit lines - thick active lines
var line tp1_thick_line = na   // TP1 thick active line
var line tp2_thick_line = na   // TP2 thick active line
var line tp3_thick_line = na   // TP3 thick active line

// Non-trading hours lines (for after-hours display)
var line entry_non_trading_line = na  // Entry line for non-trading hours
var line tp1_non_trading_line = na    // TP1 line for non-trading hours
var line tp2_non_trading_line = na    // TP2 line for non-trading hours
var line tp3_non_trading_line = na    // TP3 line for non-trading hours
// ============================================================================
// CORE UTILITY FUNCTIONS
// ============================================================================
// Wrapper function for request.security with consistent settings
get_security(symbol, timeframe, expression) =>
    request.security(symbol, timeframe, expression, gaps=barmerge.gaps_off, lookahead=barmerge.lookahead_off)

// Function to determine if current time is within trading hours
is_trading_time() =>
    useTimeFilter ? (inSession and isTradingDay) : true

// Helper function to format labels with emoji support
format_label(emoji_setting, label_text) =>
    emoji_setting == "" ? label_text : emoji_setting + " " + label_text

// ============================================================================
// TRADING SESSION VISUALIZATION
// ============================================================================
// Store the trading time state for the current bar when it opens
var is_trading_bar = false
if barstate.isfirst or barstate.isrealtime
    is_trading_bar := is_trading_time()

// Visual background color for trading hours (green) vs non-trading hours (red)
bgcolor(is_trading_time() ? color.new(color.green, 95) : color.new(color.red, 95), title="Trading Hours")

// ============================================================================
// TAKE PROFIT AND POSITION SIZE VARIABLES
// ============================================================================
// Take profit size distribution (updated based on tp_distribution setting)
var float tp1_size = 0.33                    // First take profit size (33%)
var float tp2_size = 0.33                    // Second take profit size (33%)
var float tp3_size = 0.34                    // Third take profit size (34%)
var float current_tp1_mult = 0.0             // Current TP1 multiplier for SL calculations
var bool should_flip = false                 // Whether to flip remaining position on signal change

// Take profit and stop loss multipliers
var float tp1_mult = 0.0                     // TP1 distance multiplier
var float tp2_mult = 0.0                     // TP2 distance multiplier  
var float tp3_mult = 0.0                     // TP3 distance multiplier
var float sl_mult = 0.0                      // Stop loss distance multiplier

// ============================================================================
// TIMEFRAME MULTIPLIERS (CONSTANTS)
// ============================================================================
// Multipliers for adjusting TP/SL distances based on timeframe
var float tf_mult_1m = 1.0                   // 1-minute base multiplier
var float tf_mult_2m = 1.1                   // 2-minute multiplier
var float tf_mult_3m = 1.15                  // 3-minute multiplier
var float tf_mult_5m = 1.2                   // 5-minute multiplier
var float tf_mult_15m = 1.5                  // 15-minute multiplier
var float tf_mult_30m = 1.8                  // 30-minute multiplier
var float tf_mult_45m = 2.0                  // 45-minute multiplier
var float tf_mult_1h = 2.2                   // 1-hour multiplier
var float tf_mult_2h = 2.5                   // 2-hour multiplier
var float tf_mult_4h = 3.0                   // 4-hour multiplier
var float tf_mult_1d = 4.0                   // Daily multiplier

// ============================================================================
// TRADE TRACKING VARIABLES
// ============================================================================
// Position and trade state tracking
var bool trade_opened_during_trading_hours = false  // Track if trade opened during trading hours
var int lastSignalBar = na                          // Bar index of last signal
var float st_strength = na                          // SuperTrend strength value
var bool alertActive = false                        // Alert activation state

// Alert message strings for different trade events
var string longEntryAlert = na               // Long entry alert message
var string shortEntryAlert = na              // Short entry alert message
var string longTP1 = na                      // Long TP1 alert message
var string longTP2 = na                      // Long TP2 alert message
var string longTP3 = na                      // Long TP3 alert message
var string shortTP1 = na                     // Short TP1 alert message
var string shortTP2 = na                     // Short TP2 alert message
var string shortTP3 = na                     // Short TP3 alert message
var string longSL = na                       // Long stop loss alert message
var string shortSL = na                      // Short stop loss alert message

// ============================================================================
// TAKE PROFIT SIZE CALCULATION
// ============================================================================
// Calculate initial TP sizes based on distribution setting

// Assign values based on distribution
switch tp_distribution
    '33-33-33' =>
        tp1_size := 0.33
        tp2_size  := 0.33
        tp3_size  := 0.34
        should_flip  := false
    '40-40-20' =>
        tp1_size := 0.40
        tp2_size  := 0.40
        tp3_size  := 0.20
        should_flip := false
    '20-30-50' =>
        tp1_size := 0.20
        tp2_size := 0.30
        tp3_size := 0.50
        should_flip := false
    '50-30-20' =>
        tp1_size := 0.50
        tp2_size := 0.30
        tp3_size := 0.20
        should_flip := false
    '50-15-15-Flip' =>
        tp1_size := 0.50
        tp2_size := 0.15
        tp3_size := 0.15
        should_flip := true
    '40-20-10-Flip' =>
        tp1_size := 0.40
        tp2_size := 0.20
        tp3_size := 0.10
        should_flip := true
    '40-20-20-Flip' =>
        tp1_size := 0.40
        tp2_size := 0.20
        tp3_size := 0.20
        should_flip := true
    '30-30-25-Flip' =>
        tp1_size := 0.30
        tp2_size := 0.30
        tp3_size := 0.25
        should_flip := true
    '20-20-20-Flip' =>
        tp1_size := 0.20
        tp2_size := 0.20
        tp3_size := 0.20
        should_flip := true
    '15-50-15-Flip' =>
        tp1_size := 0.15
        tp2_size := 0.50
        tp3_size := 0.15
        should_flip := true
    '15-35-35-Flip' =>
        tp1_size := 0.15
        tp2_size := 0.35
        tp3_size := 0.35
        should_flip := true
    '10-10-60-Flip' =>
        tp1_size := 0.10
        tp2_size := 0.10
        tp3_size := 0.60
        should_flip := true
    'Custom' =>
        total = custom_tp1 + custom_tp2 + custom_tp3
        if total > 100
            tp1_size := custom_tp1 / total
            tp2_size := custom_tp2 / total
            tp3_size := custom_tp3 / total
            should_flip := false
        else
            tp1_size := custom_tp1 / 100
            tp2_size := custom_tp2 / 100
            tp3_size := custom_tp3 / 100
            should_flip := total < 100
    =>
        // Default fallback
        tp1_size := 0.33
        tp2_size := 0.33
        tp3_size := 0.34
        should_flip := false



// ============================================================================
// SUPERTREND AND ML VARIABLES
// ============================================================================
// SuperTrend trend direction variable
var float st_trend = na

// ML analysis group identifier
var string group_ml = 'ML Trend Analysis (Info Only)'

// ============================================================================
// MACHINE LEARNING PARAMETER FUNCTION
// ============================================================================
// Function to get ML parameter values based on selected style
getMLValues() =>
    switch ml_style
        'Fast Scalping' => [8, 8, 21, 8, 5, 10, 8, 2]           // Fast, responsive parameters
        'Balanced (Original)' => [14, 14, 26, 12, 9, 14, 14, 3] // Balanced parameters
        'Swing Trading' => [20, 14, 26, 12, 9, 21, 14, 3]       // Longer-term parameters
        'Trend Following' => [30, 21, 34, 13, 8, 24, 21, 5]     // Trend-following parameters
        => [14, 14, 26, 12, 9, 14, 14, 3]                       // Default to balanced

// Extract ML parameter values from the function
[ml_lookback_val, ml_rsi_val, ml_macd_slow_val, ml_macd_fast_val, ml_macd_signal_val, ml_atr_val, ml_stoch_val, ml_stoch_smooth_val] = getMLValues()

// ============================================================================
// PANEL POSITION FUNCTION
// ============================================================================
// Function to convert position string to Pine position enum
get_position(pos) =>
    switch pos
        'Top Right' => position.top_right
        'Top Left' => position.top_left
        'Bottom Right' => position.bottom_right
        'Bottom Left' => position.bottom_left
        'Center Top' => position.top_center
        'Center Bottom' => position.bottom_center
        'Center Right' => position.middle_right
        'Center Left' => position.middle_left
        'Bottom Center' => position.bottom_center
        => position.bottom_center // Default to Bottom Center

// ============================================================================
// ML PARAMETER ASSIGNMENTS
// ============================================================================
// Assign ML parameter values from the selected style
ml_rsi_length = ml_rsi_val           // RSI period for ML analysis
ml_macd_slow = ml_macd_slow_val      // MACD slow period
ml_macd_fast = ml_macd_fast_val      // MACD fast period
ml_macd_signal = ml_macd_signal_val  // MACD signal period
ml_atr_length = ml_atr_val           // ATR period for ML analysis
ml_stoch_length = ml_stoch_val       // Stochastic period
ml_stoch_smooth = ml_stoch_smooth_val // Stochastic smoothing period

// ============================================================================
// FIXED SIGNAL SETTINGS
// ============================================================================
// Fixed signal parameters (no longer user inputs for consistency)
var signal_hma_period = 20           // Hull Moving Average period for signals
var signal_stoch_period = 14         // Stochastic period for signals

// ============================================================================
// SUPERTREND CONSENSUS ARRAY
// ============================================================================
// Array to track SuperTrend bullish signals across multiple configurations
var array<float> st_bullish = array.new_float(7, 0) // Initialize with 7 elements for 7 SuperTrend configs

// ============================================================================
// TRADE PERFORMANCE TRACKING VARIABLES
// ============================================================================
// Hit counters for performance analysis
var int tp1_hits = 0                 // Count of TP1 hits
var int tp2_hits = 0                 // Count of TP2 hits
var int tp3_hits = 0                 // Count of TP3 hits
var int sl_hits = 0                  // Count of stop loss hits
var int total_trades = 0             // Total number of trades

// Attempt counters for success rate calculation
var int tp1_attempts = 0             // Number of TP1 attempts
var int tp2_attempts = 0             // Number of TP2 attempts
var int tp3_attempts = 0             // Number of TP3 attempts
var int sl_attempts = 0              // Number of SL attempts

// ============================================================================
// CURRENT TRADE STATE VARIABLES
// ============================================================================
// Current position and trade state tracking
var string current_position = 'none' // Current position: "none", "long", or "short"
var bool is_long = true              // Direction of current trade
var bool waiting_for_exit = false    // Whether waiting for exit conditions

// Current trade price levels
var float entry_price = na           // Current entry price
var float tp1_price = na             // Current TP1 price
var float tp2_price = na             // Current TP2 price
var float tp3_price = na             // Current TP3 price
var float sl_price = na              // Current stop loss price

// Trade hit status tracking
var bool tp1_hit = false             // Whether TP1 has been hit
var bool tp2_hit = false             // Whether TP2 has been hit
var bool tp3_hit = false             // Whether TP3 has been hit
var bool sl_hit = false              // Whether stop loss has been hit

// ============================================================================
// SIGNAL PRICE ARRAYS
// ============================================================================
// Arrays to store historical signal prices for analysis
var array<float> long_signal_prices = array.new_float(0)   // Long entry signal prices
var array<float> short_signal_prices = array.new_float(0)  // Short entry signal prices

// ============================================================================
// VISUAL ELEMENTS VARIABLES
// ============================================================================
// Line and label management variables
var int line_start_index = na        // Starting index for line drawing
var label current_signal = na        // Current signal label

// Line color definitions for visual consistency
var color tp_color = color.new(color.green, 40)           // Take profit line color
var color tp_color_transparent = color.new(tp_color, 40)  // Transparent TP color

// Label objects for different trade elements
var label tp1_label = label.new(na, na, '', color = color.green)  // TP1 label
var label tp2_label = label.new(na, na, '', color = color.green)  // TP2 label
var label tp3_label = label.new(na, na, '', color = color.green)  // TP3 label
var label entry_label = label.new(na, na, '', color = color.white) // Entry label
var label sl_label = label.new(na, na, '', color = color.red)     // Stop loss label
getSuperTrendParams() =>
    switch preset
        'Conservative Swing' => [28, 4.0, 1.2]
        'Ultra Trend Follower' => [9, 23.0, 1.1] // Fixed multiplier (23→3.0)
        'Breakout Sentinel' => [8, 3.3, 1.15] // New entry
        'Precision Swing Scalper' => [5, 5.0, 0.8]
        'Moderate Swing Trader' => [12, 2.9, 1.0]
        'Balanced Daytrader' => [10, 2.8, 1.0]// Swing Optimizer Pro values
        'Aggressive Daytrader' => [7, 2.5, 1.0]
        'Dynamic Swinger' => [5, 2.0, 0.8]
        => [11, 2.85, 1.02] // Default to updated Balanced Daytrader
[st_period, st_mult, change_factor] = getSuperTrendParams()

calcSuperTrend(period, multiplier, changeFactor) =>
    // Basic ATR calculation
    atr = ta.atr(int(period))  // Convert period to integer

    // Enhanced factor calculation with sensitivity adjustment
    factor = atr * multiplier * changeFactor

    // Upper band calculation
    up = hl2 - factor
    up1 = nz(up[1], up)
    up := close[1] > up1 ? math.max(up, up1) : up

    // Lower band calculation
    dn = hl2 + factor
    dn1 = nz(dn[1], dn)
    dn := close[1] < dn1 ? math.min(dn, dn1) : dn

    // Trend determination
    trend = 1
    trend := nz(trend[1], trend)
    trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

    supertrend = trend == 1 ? up : dn
    [supertrend, trend, up, dn]

var float alertTP1 = na
var float alertTP2 = na
var float alertTP3 = na
var float alertSL = na
 
var bool slHit = false
// =================== Preset Configurations ===================
var string SWING_SAFEST = 'Safest'
var string SWING_CAUTIOUS = 'Cautious'
var string SWING_MODERATE = 'Moderate'
var string SWING_DYNAMIC = 'Dynamic'
var string SWING_AGGRESSIVE = 'Aggressive'
var string DAY_CAUTIOUS = 'Scalp'
var string CUSTOM = 'Custom'


// ======== EMOJI POSITIONING SYSTEM ========
getEmojiXPosition() =>
    // For real-time bars, position 2 bars to the left
    if barstate.isrealtime
        math.max(bar_index - 2, 0)  // Ensure we don't get negative index
    else
        // For historical bars, use current bar_index
        bar_index

// Function to determine line style based on setting
get_line_style() =>
    switch line_style_1
        'Solid' => line.style_solid
        'Dashed' => line.style_dashed
        'Dotted' => line.style_dotted
        => line.style_solid // Default to solid

// Preset parameters
getPresetParams() =>
    [st_period, st_mult, signal_hma_period, signal_stoch_period]

getHmaPeriod() =>
    switch hmaPreset
        SWING_SAFEST => 100
        SWING_CAUTIOUS => 75
        SWING_MODERATE => 50
        SWING_DYNAMIC => 20
        SWING_AGGRESSIVE => 10
        DAY_CAUTIOUS => 5
        => 20 // Default to SWING_MODERATE

[supertrendPeriod, supertrendMultiplier, _, stochRsiPeriod] = getPresetParams()
hmaPeriod = getHmaPeriod()


// Function to calculate Dynamic SuperTrend that adapts to market conditions
hmaValue = ta.hma(close, hmaPeriod)
hmaSignal = ta.change(hmaValue) > 0
hma_color = ta.change(hmaValue) > 0 ? color.green : color.red
plot(hmaValue, 'SMA', color = hma_color, linewidth = 2)
custom_atr(length) =>
    // Validate length parameter
    valid_length = math.max(1, math.min(100, int(math.round(length))))
    true_range = math.max(high - low, math.max(math.abs(high - close[1]), math.abs(low - close[1])))
    ta.rma(true_range, valid_length)
calcDynamicSuperTrend(base_period, base_multiplier, change_factor) =>
    // Create mutable local copies of parameters
    local_period = base_period
    local_multiplier = base_multiplier
    
    // Validate inputs using LOCAL COPIES
    local_period := math.max(1, local_period)
    local_multiplier := math.max(0.1, local_multiplier)
    
    
    // 2. PROPER ADX CALCULATION
    adx_period = 14
    true_range = math.max(high - low, math.max(math.abs(high - close[1]), math.abs(low - close[1])))
    plus_dm = high - high[1] > low[1] - low ? math.max(high - high[1], 0) : 0
    minus_dm = low[1] - low > high - high[1] ? math.max(low[1] - low, 0) : 0
    
    // Use RMA (Wilder's smoothing) instead of SMA
    smoothed_tr = ta.rma(true_range, adx_period)
    smoothed_plus = ta.rma(plus_dm, adx_period)
    smoothed_minus = ta.rma(minus_dm, adx_period)
    
    plus_di = smoothed_tr > 0 ? 100 * smoothed_plus / smoothed_tr : 0
    minus_di = smoothed_tr > 0 ? 100 * smoothed_minus / smoothed_tr : 0
    di_diff = math.abs(plus_di - minus_di)
    di_sum = plus_di + minus_di
    
    dx = di_sum > 0 ? 100 * di_diff / di_sum : 0
    adx_value = ta.rma(dx, adx_period)
 
    // 3. DYNAMIC ADJUSTMENTS
    period_factor = adx_value > 25 ? 0.8 : adx_value < 20 ? 1.2 : 1.0
    multiplier_factor = adx_value > 25 ? 0.9 : adx_value < 20 ? 1.1 : 1.0
    
    // 4. CALCULATE PARAMETERS WITH CLAMPING (using LOCAL COPIES)
    dynamic_period = math.round(local_period * period_factor)
    dynamic_period := math.max(math.round(local_period * 0.7), math.min(math.round(local_period * 1.5), dynamic_period))
    dynamic_period := math.max(1, dynamic_period)
    
    dynamic_multiplier = local_multiplier * multiplier_factor
    dynamic_multiplier := math.max(local_multiplier * 0.7, math.min(local_multiplier * 1.5, dynamic_multiplier))
    dynamic_multiplier := math.max(0.1, dynamic_multiplier)
// 5. OPTIMIZED SUPERTREND CALCULATION
// Convert to simple integer using math.round() + int()


    atr_length = int(math.round(dynamic_period))

    atr_val = custom_atr(14) 
    factor = atr_val * dynamic_multiplier * change_factor

// Upper band
    up = hl2 - factor
    up_prev = nz(up[1], up)
    up := close[1] > up_prev ? math.max(up, up_prev) : up

// Lower band
    dn = hl2 + factor
    dn_prev = nz(dn[1], dn)
    dn := close[1] < dn_prev ? math.min(dn, dn_prev) : dn    
// 6. POSITION-LOCKED TREND SYSTEM
    prev_trend = nz(st_trend[1], 0)
    min_confirm_bars = input.int(2, "Min Confirmation Bars", minval=1, maxval=5)
    var bool position_locked = false
    var int confirm_count = 0

// Adaptive confirmation
    atr_ratio = ta.atr(14) / close
    volatility_factor = math.min(math.max(atr_ratio * 100, 1), 3)
    required_confirm = int(math.round(min_confirm_bars * volatility_factor))

// Unlock conditions
    unlock_long = close > dn_prev and close > close[1]
    unlock_short = close < up_prev and close < close[1]

// Signal detection
    valid_long_signal = close > up_prev and close > dn_prev and close > close[1]
    valid_short_signal = close < up_prev and close < dn_prev and close < close[1]
    st_trend_local = st_trend
// Position state machine
    if position_locked
    // Check unlock conditions
        if (prev_trend == 1 and unlock_long) or (prev_trend == -1 and unlock_short)
            position_locked := false
            confirm_count := 0
    else
    // Check new position signals
        if valid_long_signal and prev_trend != 1
            st_trend_local := 1
            position_locked := true
        else if valid_short_signal and prev_trend != -1
            st_trend_local := -1
            position_locked := true
        else
            st_trend_local := prev_trend

// Calculate st_value
    st_value = st_trend == 1 ? up : st_trend == -1 ? dn : hl2

    [st_value, st_trend_local, up, dn, dynamic_period, dynamic_multiplier]
// Fix the mismatch in return values
if st_model == 'Classic'
    [st_value, trend, up, dn] = calcSuperTrend(st_period, st_mult, change_factor)
    dynamic_period = st_period  // Use base period for classic model
    dynamic_multiplier = st_mult  // Use base multiplier for classic model
else
    [st_value, trend, up, dn, dynamic_period, dynamic_multiplier] = calcDynamicSuperTrend(st_period, st_mult, change_factor)

//var float supertrend = 0.0
var float direction = 0.0
var float upper_band = 0.0
var float lower_band = 0.0
var float dynamic_period = 0.0
var float dynamic_multiplier = 0.0

// Initialize trade tracking variables
var float prev_close = na // Initialize as 'na'
var float daily_high = na
var float daily_low = na
var float daily_volume = na

is_new_day = ta.change(time('D'))

if bool(is_new_day)
    daily_high := high
    daily_low := low
    daily_volume := volume
    prev_close := close[1] // Set prev_close to the previous close
    prev_close
else
    daily_high := math.max(daily_high, high)
    daily_low := math.min(daily_low, low)
    daily_volume := daily_volume + volume
    prev_close := close // Update prev_close to the current close
    prev_close

daily_perf = (close - prev_close) / prev_close * 100



// Create Info Panels (initialize once)
var table panel1 = na
var table panel2 = na
var table debug_panel = na
// =================== ML Analysis ===================
// Calculate ATR first
atr = ta.atr(14) // Using standard 14-period ATR
ml_lookback = ml_lookback_val

// Volume Analysis
var int vol_length = 20
volume_ratio = volume / ta.sma(volume, vol_length)

// Volume Category Calculation
volume_category = volume_ratio < 0.2 ? 'Extremely Low' : volume_ratio < 0.4 ? 'Low' : volume_ratio < 0.6 ? 'Normal' : volume_ratio < 0.8 ? 'High' : 'Extremely High'
// ======== MACHINE LEARNING TREND CALCULATION ========
calcMLTrend(src) =>
    // Get base indicators safely
    float safe_src = math.max(src, 0.0001)  // Prevent negative/zero price
    float safe_atr = math.max(atr, 0.0001)  // Prevent division by zero

    // Calculate RSI with momentum
    float ml_rsi = ta.rsi(safe_src, ml_rsi_length)
    float rsi_change = ta.change(ml_rsi)
    float rsi_distance = math.abs(ml_rsi - 50) / 50  // Normalized distance from midpoint

    // Calculate RSI trend with improved logic
    float rsiTrend =        ta.cross(ml_rsi, 50) ? (rsi_change > 0 ? 1.5 : -1.5) :        ml_rsi > 60 ? 1.2 :         ml_rsi > 50 ? 0.8 :        ml_rsi < 40 ? -1.2 :        ml_rsi < 50 ? -0.8 :         0                    // Neutral

    // Enhanced MACD calculation with proper error handling
    [macd_line, signal_line, histogram] = ta.macd(safe_src, ml_macd_fast, ml_macd_slow, ml_macd_signal)

    // Normalize MACD components relative to price
    float norm_macd = macd_line / safe_src * 100
    float norm_signal = signal_line / safe_src * 100
    float norm_hist = histogram / safe_src * 100

    // Calculate momentum with safety check
    float macd_direction = macd_line > signal_line ? 1 : -1
    float momentum = macd_direction * math.abs(norm_hist) / math.max(safe_atr / safe_src * 100, 0.01)

    // Calculate volatility for adaptive learning
    float volatility = safe_atr / safe_src * 100

    // Adaptive learning rate based on market conditions
    float learn_rate = math.max(0.1, math.min(0.5, volatility / 5))

    // Enhanced strength calculation with volume and volatility
    float strength_components = (        rsi_distance * 0.4 +           math.abs(norm_macd) * 0.4 +          math.min(volatility / 10, 1) * 0.2     )

    // Apply volume as a multiplier with diminishing returns
    float vol_factor = math.min(math.pow(volume_ratio, 0.7), 2.0)  // Cap at 2x
    float strength = strength_components * vol_factor

    // Calculate trend direction as weighted average
    float trend = (        rsiTrend * 0.4 +  momentum * 0.4 +             math.sign(macd_line) * 0.2  )

    // Return comprehensive analysis
    [strength * learn_rate, trend, ml_rsi, norm_macd, norm_signal]

// Calculate ML trend values with proper variable declarations
[mlStrength, mlTrend, mlRsi, mlMacd, mlSignal] = calcMLTrend(close)
ml_state = mlTrend > 0.5 ? 'Strong Bullish' : mlTrend > 0.2 ? 'Weak Bullish' : mlTrend > -0.2 ? 'Neutral' : mlTrend > -0.5 ? 'Weak Bearish' : 'Strong Bearish'
ml_strength_text = str.tostring(math.round(mlStrength * 100, 1)) + '%'
// Function to get color based on strength and direction
get_strength_color(strength_level, is_bullish) =>
    if is_bullish
        switch strength_level
            'EXTREME' => color.new(color.green, 0) // Deep green
            'HIGH' => color.new(color.green, 30) // Medium green
            'MEDIUM' => color.new(color.green, 60) // Light green
            'LOW' => color.new(color.green, 80) // Very light green
            => color.white // Default
    else
        switch strength_level
            'EXTREME' => color.new(color.red, 0) // Deep red
            'HIGH' => color.new(color.red, 30) // Medium red
            'MEDIUM' => color.new(color.red, 60) // Light red
            'LOW' => color.new(color.red, 80) // Very light red
            => color.white // Default


// Define is_bullish based on ML state
is_bullish = ml_state == 'Strong Bullish' or ml_state == 'Weak Bullish'
// =================== Market State Analysis ===================
// ============================================================================
// CORE TECHNICAL INDICATORS CALCULATION
// ============================================================================
// Primary technical indicators used throughout the algorithm

// RSI (Relative Strength Index) - 14-period standard
rsi_value = ta.rsi(close, 14) // Using standard 14-period RSI

// True Range (TR) calculation for volatility measurement
tr = math.max(high - low, math.max(math.abs(high - close[1]), math.abs(low - close[1])))

// Directional Movement calculations for ADX system
plus_dm = high - high[1] > low[1] - low ? math.max(high - high[1], 0) : 0.0   // Positive directional movement
minus_dm = low[1] - low > high - high[1] ? math.max(low[1] - low, 0) : 0.0    // Negative directional movement

// Smoothed values using SMA for directional indicators
smoothed_tr = ta.sma(tr, 14)           // Smoothed True Range
smoothed_plus_dm = ta.sma(plus_dm, 14) // Smoothed +DM
smoothed_minus_dm = ta.sma(minus_dm, 14) // Smoothed -DM

// Directional Indicators (+DI and -DI) - key components for trend strength
plus_di = smoothed_plus_dm / smoothed_tr * 100   // Positive Directional Indicator
minus_di = smoothed_minus_dm / smoothed_tr * 100  // Negative Directional Indicator
// ============================================================================
// TECHNICAL ANALYSIS INPUT PARAMETERS
// ============================================================================
// Configuration parameters for various technical indicators and analysis

// Trend Analysis Settings
adx_length = input.int(14, "ADX Length", group="Trend Settings", display = display.none)
roc_length = input.int(14, "ROC Length", group="Trend Settings", display = display.none)
adx_thresh = input.int(20, "ADX Threshold", group="Trend Settings", display = display.none)

// Volume Analysis Settings
volume_short = input.int(10, "Short Volume MA", group="Volume Settings", display = display.none)
volume_long = input.int(100, "Long Volume MA", group="Volume Settings", display = display.none)

// Volatility Analysis Settings
atr_len = input.int(14, "ATR Length", group="Volatility Settings", display = display.none)
atr_div = input.float(2.0, "ATR Divisor", group="Volatility Settings", display = display.none)

// Strength Smoothing Settings
strength_smoothing = input.int(14, "Strength Smoothing (EMA Length)", group="Strength Settings", display = display.none)

// Alert Configuration
alert_group = 'Alert Settings'
enableTradeAlerts = input.bool(true, "Enable Trade Alerts", group=alert_group, display = display.none)
input_group_alerts = "Alert Settings"
 
// ============================================================================
// SIMPLE ROC THRESHOLDS FOR PANEL DISPLAY
// ============================================================================
// Fixed thresholds for ROC momentum labeling - simple and consistent
// No complex ATR calculations, just straightforward percentage-based categorization
// ============================================================================
strong_thresh = 3.0  // ROC >= 3% = "🚀 STRONG UP/DOWN"
rising_thresh = 1.5  // ROC >= 1.5% = "📈 RISING/📉 FALLING"  
mild_thresh = 0.5    // ROC >= 0.5% = "↗️ MILD UP/↘️ MILD DOWN"

// ============================================================================
// ADVANCED TECHNICAL ANALYSIS FUNCTIONS
// ============================================================================

// ============================================================================
// MOMENTUM STATE ANALYSIS FUNCTION
// ============================================================================
// Advanced ROC Momentum Analysis - Compares short and long-term ROC with sigmoid normalization
// Parameters:
//   - source: Price source for ROC calculation
//   - short_length: Period for short-term ROC
//   - long_length: Period for long-term ROC
// Returns: [short_roc, long_roc, momentum_state, acceleration_score]
//   - momentum_state: "ACCELERATING", "DECELERATING", or "STEADY"
//   - acceleration_score: -25 to +25 (negative = decelerating, positive = accelerating)
calcMomentumState(source, short_length, long_length) =>
    // Calculate short and long-term Rate of Change
    roc_short = ta.roc(source, short_length)  // Short-term momentum
    roc_long = ta.roc(source, long_length)    // Long-term momentum
    roc_diff = roc_short - roc_long           // Difference between momentum periods

    // Sigmoid normalization parameters for smooth transitions
    max_diff = 0.1                           // Threshold for maximum expected ROC difference
    neutral_threshold = 0.02                 // 2% difference considered neutral zone
    k = 20                                   // Sigmoid curve steepness (10-30 range)
    
    // Apply sigmoid normalization to prevent extreme values
    normalized = 2 * (1 / (1 + math.exp(-k * roc_diff)) - 0.5)

    // Volatility-adjusted scaling using ATR
    atr = ta.atr(14)
    volatility_factor = math.min(atr/close * 100, 2.0)  // Cap volatility impact at 2x
    acceleration_score_val = normalized * 25 * volatility_factor  // Scale to ±25 range

    // Apply neutral zone buffer to reduce noise
    if math.abs(roc_diff) < neutral_threshold
        acceleration_score_val := 0

    // Determine momentum state with hysteresis to prevent oscillation
    var string last_state = "STEADY"
    momentum_state_txt = if acceleration_score_val > 15
        "ACCELERATING"
    else if acceleration_score_val < -15
        "DECELERATING"
    else
        last_state  // Maintain previous state in neutral zone

    last_state := momentum_state_txt

    [roc_short, roc_long, momentum_state_txt, acceleration_score_val]



// ============================================================================
// TREND STRENGTH CALCULATOR FUNCTION
// ============================================================================
// Comprehensive trend strength calculator with improved error handling and performance
// Parameters:
//   - adx_length: Period for ADX calculation
//   - roc_length: Period for Rate of Change calculation
//   - volume_short_length: Short period for volume moving average
//   - volume_long_length: Long period for volume moving average
//   - atr_length: Period for ATR calculation
//   - adx_weight: Weight for ADX component (0.0-1.0)
//   - roc_weight: Weight for ROC component (0.0-1.0)
//   - volume_weight: Weight for volume component (0.0-1.0)
//   - volatility_weight: Weight for volatility component (0.0-1.0)
//   - adx_threshold: Minimum ADX value to consider trending
//   - atr_divisor: Divisor for ATR normalization
//   - smooth_length: Length for final smoothing
// Returns: Trend strength value (0-100)
calcTrendStrength(adx_length, roc_length, volume_short_length, volume_long_length, atr_length, adx_weight, roc_weight, volume_weight, volatility_weight, adx_threshold, atr_divisor, smooth_length) =>
    // ========================================================================
    // ADX Component with Directional Bias
    // ========================================================================
    [diplus, diminus, adx] = ta.dmi(adx_length, adx_length)
    adx_normalized = adx / 100

    // Calculate directional bias with zero protection
    denominator = diplus + diminus + 0.0001  // Prevent division by zero
    directional_bonus = adx > adx_threshold ? 
         (diplus > diminus ? (diplus - diminus) / denominator : (diminus - diplus) / denominator) * 0.2 : 
         0

    adx_component = (adx > adx_threshold ? adx_normalized : 0) + directional_bonus

    // ========================================================================
    // Momentum (ROC) Component with Volatility Scaling
    // ========================================================================
    roc = ta.roc(close, roc_length)
    roc_stdev = math.max(0.0001, ta.stdev(roc, roc_length))  // Prevent zero division
    roc_normalized = math.min(2, math.max(-2, roc / roc_stdev))

    // ========================================================================
    // Volume Analysis Component
    // ========================================================================
    safe_volume = math.max(volume, 1)  // Ensure no zero volume
    volume_short_ma = ta.sma(safe_volume, volume_short_length)
    volume_long_ma = ta.sma(safe_volume, volume_long_length)
    volume_ratio = volume_short_ma / math.max(volume_long_ma, 0.0001)
    volume_normalized = math.log(math.max(volume_ratio, 0.01))

    // ========================================================================
    // Volatility (ATR) Component
    // ========================================================================
    atr = ta.atr(atr_length)
    safe_close = math.max(close, 0.01)
    atr_normalized = (atr / safe_close) * 100 / atr_divisor

    // ========================================================================
    // Weight Normalization and Final Calculation
    // ========================================================================
    total_weight = adx_weight + roc_weight + volume_weight + volatility_weight
    normalized_weight = math.max(total_weight, 1)  // Prevent division by zero

    // Calculate weighted strength components
    strength = (        (adx_component * adx_weight) +        (roc_normalized * roc_weight) +        (math.min(1, volume_normalized) * volume_weight) +        (math.min(1, atr_normalized) * volatility_weight)    ) / normalized_weight

    // Apply adaptive smoothing based on market volatility
    smooth_factor = math.min(smooth_length, math.max(1, math.round(nz(ta.atr(10)/safe_close*100, 1))))

    // Final output with range constraints (0-100)
    smoothed_strength = ta.wma(strength, smooth_factor)
    math.round(math.min(100, math.max(0, smoothed_strength * 100)))

// ============================================================================
// CORE TECHNICAL CALCULATIONS
// ============================================================================
// Primary technical indicator calculations used throughout the algorithm

// ============================================================================
// PRICE MOMENTUM AND VELOCITY ANALYSIS
// ============================================================================
// Price momentum using Rate of Change (5-period)
price_momentum = ta.roc(close, 5)
safe_close = math.max(close, 0.01)  // Prevent division by zero

// Price velocity - smoothed rate of change as percentage
price_velocity = (ta.sma(close - close[1], 10) / safe_close) * 100

// ============================================================================
// VOLUME ANALYSIS AND IMPULSE DETECTION
// ============================================================================
// Volume analysis with safe volume handling
safe_volume = math.max(volume, 1)  // Prevent zero volume errors
volume_short_ma = ta.sma(safe_volume, volume_short)  // Short-term volume MA
volume_long_ma = ta.sma(safe_volume, volume_long)    // Long-term volume MA

// Volume impulse detection - indicates significant volume increase
volume_impulse = volume_short_ma > 1.25 * volume_long_ma

// ============================================================================
// VOLATILITY ASSESSMENT
// ============================================================================
// ATR-based volatility calculations
atr_value = ta.atr(atr_len)
atr_percent = (atr_value / safe_close) * 100  // ATR as percentage of price

// Volatility multiplier for adaptive calculations
volatility_mult = math.min(3, 1 + atr_percent / 2)

// Price change volatility analysis
price_change_stdev = ta.stdev(close - close[1], 3)  // Standard deviation of price changes
price_change_avg = ta.sma(close - close[1], 3)      // Average price change

// Volatile market detection
volatile_market = price_change_stdev > price_change_avg * volatility_mult

// ============================================================================
// STOCHASTIC RSI CALCULATION
// ============================================================================
// Stochastic RSI for overbought/oversold conditions
stoch_k = ta.sma(ta.stoch(rsi_value, rsi_value, rsi_value, stochRsiPeriod), 3)
stochRsi = stoch_k / 100  // Normalize to 0-1 range

// ============================================================================
// MACD CALCULATION FOR MARKET STRENGTH
// ============================================================================
// MACD components for trend and momentum analysis
[macdLine, signalLine, histLine] = ta.macd(close, 12, 26, 9)
//mlMacd = macdLine
//tr = ta.tr(true)  // True Range calculation
// ======== ADX CALCULATION ========
// Add this section before calcTrendStrength() function
[diplus, diminus, adx_value] = ta.dmi(14, 14)  // Built-in DMI/ADX function

// Alternative manual calculation if needed:

len = 14
lendmi = 14
up = ta.change(high)
down = -ta.change(low)
plusDM = na(up) ? na : (up > down and up > 0 ? up : 0)
minusDM = na(down) ? na : (down > up and down > 0 ? down : 0)
trur = ta.rma(ta.tr, len)
plus = fixnan(ta.rma(plusDM, len) / trur * 100)
minus = fixnan(ta.rma(minusDM, len) / trur * 100)
sum = plus + minus
adx_value := 100 * ta.rma(math.abs(plus - minus) / (sum == 0 ? 1 : sum), lendmi)


// Then update your existing code that uses adx_value
adx_component = adx_value > adx_thresh ? (adx_value - adx_thresh) / (100 - adx_thresh) : 0
// Market Strength Calculation
adx_component := adx_value / 100
macd_component = math.min(1, math.abs(mlMacd) / (safe_close * 0.05))
volatility_component = math.log(tr / math.max(ta.sma(tr, 14), 0.0001) + 1)

market_strength = math.avg(    math.min(1, adx_component),    math.min(1, macd_component),    math.min(1, volatility_component))

// Market strength level text representation
market_strength_level_text =     market_strength > 0.8 ? 'EXTREME' :    market_strength > 0.6 ? 'HIGH' :    market_strength > 0.4 ? 'MEDIUM' : 'LOW'

// Calculate overall trend strength
market_trend_strength = calcTrendStrength(    adx_length,     roc_length,      volume_short,      volume_long,      atr_len,       0.4,      0.3,      0.2,      0.1,      adx_thresh,      atr_div,   5   )

// =================== Market State Determination ===================
// Trend confirmation with clear criteria
bullish_trend = (    market_trend_strength > 25 and    rsi_value > 55 and    price_momentum > 0 and    price_velocity > 0.5 and    volume_impulse)

bearish_trend = (    market_trend_strength > 25 and    rsi_value < 45 and    price_momentum < 0 and    price_velocity < -0.5 and    volume_impulse)

// State representation as numeric values for easier manipulation
var int market_state_num = 0
var int last_state_num = 0

// Update market state with hysteresis to prevent rapid changes
new_state_num = bullish_trend ? 1 :                bearish_trend ? -1 :                volatile_market ? 2 :                market_trend_strength > 15 ? 0 : -2

// Only update state if it's consistently different for stability
if (new_state_num != market_state_num)
    // Could add persistence check here if needed
    market_state_num := new_state_num

last_state_num := nz(market_state_num[1], 0)

// Convert numeric state to text representation
market_state = switch market_state_num
    1 => 'BULL TREND'
    -1 => 'BEAR TREND'
    2 => 'VOLATILE'
    -2 => 'CHOPPY'
    => 'NEUTRAL'  // Default case includes 0 and any other values

var string last_state = 'CHOPPY'
last_state := market_state

// =================== Visualization ===================
// Color coding for various market states
market_state_color = switch market_state
    'BULL TREND' => color.new(#00FF00, 70)  // Green with transparency
    'BEAR TREND' => color.new(#FF0000, 70)  // Red with transparency
    'VOLATILE'   => color.new(#FFA500, 50)  // Orange with transparency
    'NEUTRAL'    => color.new(#0000FF, 50)  // Blue with transparency
    'CHOPPY'     => color.new(#808080, 50)  // Gray with transparency
    => color.new(#FFFFFF, 90)               // White with high transparency (default)

// =================== Alert Logic ===================
// Define conditions for alerts (to be implemented as needed)
bull_signal = market_state == 'BULL TREND' and last_state != 'BULL TREND'
bear_signal = market_state == 'BEAR TREND' and last_state != 'BEAR TREND'

is_visual_trading_time() =>
    is_trading_time() or not is_trading_time() and after_hours_behavior == 'Hold Until Signal'


show_mtf_panel = input.bool(true, title = '🔍 Show Multi-TimeFrame Panel', group = '🎨 MTF Panel Settings', tooltip = 'Toggle multi-timeframe analysis panel', inline = 'MTFPanel', display = display.none)
mtf_panel_position = input.string('Bottom Right', title = '📍 Screener Position', options = ['Top Right', 'Top Left', 'Bottom Right', 'Bottom Left', 'Center Top', 'Center Bottom', 'Center Right', 'Center Left', 'Bottom Center'], group = '🎨 MTF Panel Settings', inline = 'MTFPanel', display = display.none)
mtf_panel__size = input.string('Tiny', 'Panel Text Size', options = ['Tiny', 'Small', 'Normal', 'Large', 'Huge'], display = display.none)
// t
// SuperTrend calculation and signal generation
if st_model == 'Classic'
    // Classic model returns 4 values
    [std_supertrend, std_direction, std_upper_band, std_lower_band] = calcSuperTrend(st_period, st_mult, change_factor)
    // Just store the static values for display
    dynamic_period := st_period
    dynamic_multiplier := st_mult
else
    // Dynamic model returns 6 values
    [std_supertrend, std_direction, std_upper_band, std_lower_band, dyn_period_value, dyn_mult_value] = calcDynamicSuperTrend(st_period, st_mult, change_factor)
    // Update the display variables
    dynamic_period := dyn_period_value
    dynamic_multiplier := dyn_mult_value

// Calculate Standard SuperTrend with unique variable names
[std_supertrend, std_direction, std_upper_band, std_lower_band] = calcSuperTrend(st_period, st_mult, change_factor)

// Calculate Dynamic SuperTrend with completely different variable names
[dyn_supertrend, dyn_direction, dyn_upper_band, dyn_lower_band, dyn_period_value, dyn_mult_value] = calcDynamicSuperTrend(st_period, st_mult, change_factor)

// Choose which method to use based on user selection
final_supertrend = st_model == 'Dynamic Adaptive' ? dyn_supertrend : std_supertrend
final_direction = st_model == 'Dynamic Adaptive' ? dyn_direction : std_direction
final_upper = st_model == 'Dynamic Adaptive' ? dyn_upper_band : std_upper_band
final_lower = st_model == 'Dynamic Adaptive' ? dyn_lower_band : std_lower_band
// Generate base signals using the FINAL values from selected method

base_longCondition = ta.crossover(close, final_supertrend) and final_direction == 1
base_shortCondition = ta.crossunder(close, final_supertrend) and final_direction == -1

// Apply timeframe multiplier to percentage
apply_timeframe_multiplier(base_percentage) =>
    tf = timeframe.period
    tf_mins = timeframe.multiplier

    // Convert timeframe to minutes for comparison
    tf_in_minutes = if tf == 'D' or tf == '1D'
        1440
    else
        tf_mins

    multiplier = switch
        tf_in_minutes <= 1 => tf_mult_1m
        tf_in_minutes == 2 => tf_mult_2m
        tf_in_minutes == 3 => tf_mult_3m
        tf_in_minutes == 5 => tf_mult_5m
        tf_in_minutes == 15 => tf_mult_15m
        tf_in_minutes == 30 => tf_mult_30m
        tf_in_minutes == 45 => tf_mult_45m
        tf_in_minutes == 60 => tf_mult_1h
        tf_in_minutes == 120 => tf_mult_2h
        tf_in_minutes == 240 => tf_mult_4h
        tf_in_minutes >= 1440 => tf_mult_1d
        => 1.0 // Default multiplier

    math.min(base_percentage * multiplier, 100.0) // Cap at 100%

// ============================================================================
// DYNAMIC TAKE PROFIT CALCULATION FUNCTION
// ============================================================================
// Calculates dynamic take profit and stop loss levels based on market conditions
// Parameters:
//   - is_long: Boolean indicating if this is for a long position
// Returns: [final_tp1, final_tp2, final_tp3, final_sl] - Percentage moves for each level
get_dynamic_tp_levels(is_long) =>
    // Use existing ATR value to avoid recalculation
    current_atr = atr

    // ========================================================================
    // Base ATR Multipliers by Trading Style
    // ========================================================================
    [tp1_atr_mult, tp2_atr_mult, tp3_atr_mult, sl_atr_mult] = switch tp_preset
        'Scalper' => [1.0, 2.0, 3.0, 0.75]         // Tight levels for scalping
        'Conservative' => [1.5, 3.0, 4.5, 1.0]     // Balanced approach
        'Moderate' => [2.0, 4.0, 6.0, 1.5]         // Standard swing levels
        'Aggressive' => [3.0, 6.0, 9.0, 2.0]       // Wide levels for trends
        => [2.0, 4.0, 6.0, 1.5]                    // Default to Moderate

    // Apply global multipliers to all levels
    tp1_atr_mult := tp1_atr_mult * global_tp_multiplier
    tp2_atr_mult := tp2_atr_mult * global_tp_multiplier
    tp3_atr_mult := tp3_atr_mult * global_tp_multiplier
    sl_atr_mult := sl_atr_mult * global_tp_multiplier * sl_fine_tune

    // ========================================================================
    // Calculate Base Percentage Moves
    // ========================================================================
    atr_percent = current_atr / close * 100
    tp1_move = atr_percent * tp1_atr_mult
    tp2_move = atr_percent * tp2_atr_mult
    tp3_move = atr_percent * tp3_atr_mult

    // ========================================================================
    // Market Condition Adjustments
    // ========================================================================
    // Adjust targets based on current market state
    market_mult = switch market_state
        'TRENDING' => 1.2    // Extend targets in trending markets
        'VOLATILE' => 0.8    // Tighten targets in volatile markets
        => 1.0               // Normal adjustment for other states

    // Volume-based adjustment (0.8 to 1.2 range)
    vol_mult = switch volume_category
        'Extremely Low' => 0.8
        'Low' => 0.9
        'Normal' => 1.0
        'High' => 1.1
        'Extremely High' => 1.2
        => 1.0

    // ML trend impact adjustment (0.8 to 1.2 range)
    ml_mult = switch ml_state
        'Strong Bullish' => is_long ? 1.2 : 0.8
        'Weak Bullish' => is_long ? 1.1 : 0.9
        'Neutral' => 1.0
        'Weak Bearish' => is_long ? 0.9 : 1.1
        'Strong Bearish' => is_long ? 0.8 : 1.2
        => 1.0

    // ========================================================================
    // Final Calculation with All Adjustments
    // ========================================================================
    final_mult = market_mult * vol_mult * ml_mult

    // Apply all multipliers and timeframe scaling
    final_tp1 = apply_timeframe_multiplier(tp1_move * final_mult)
    final_tp2 = apply_timeframe_multiplier(tp2_move * final_mult)
    final_tp3 = apply_timeframe_multiplier(tp3_move * final_mult)
    final_sl = current_atr * sl_atr_mult / close * 100 * market_mult

    [final_tp1, final_tp2, final_tp3, final_sl]


// Update volume color logic
volume_color = switch volume_category
    'Extremely Low' => color.new(color.gray, 50) // Grey
    'Low' => color.new(color.gray, 30) // Light grey
    'Normal' => is_bullish ? color.new(color.green, 30) : color.new(color.red, 30) // Dull green/red
    'High' => is_bullish ? color.new(color.green, 0) : color.new(color.red, 0) // Deep green/red
    'Extremely High' => is_bullish ? color.new(color.green, 0) : color.new(color.red, 0) // Deep green/red
    => color.white
    // Variables to track last signals
var int last_long_index = na
var int last_short_index = na
var label last_long_label = na
var label last_short_label = na
// Function to determine line visibility
line_visible(is_historical) =>
    show_plot_lines ? true : waiting_for_exit




// Calculate TP sizes based on distribution
tp1_size := if tp_distribution == 'Custom'
    custom_tp1 / 100
else
    switch tp_distribution
        '33-33-33' => 0.33
        '40-40-20' => 0.40
        '20-30-50' => 0.20
        '50-30-20' => 0.50
        '50-15-15-Flip' => 0.50
        '40-20-10-Flip' => 0.40
        '40-20-20-Flip' => 0.40
        '30-30-25-Flip' => 0.30
        '20-20-20-Flip' => 0.20
        '15-50-15-Flip' => 0.15
        '15-35-35-Flip' => 0.15
        '10-10-60-Flip' => 0.10
        => 0.33

tp2_size := if tp_distribution == 'Custom'
    custom_tp2 / 100
else
    switch tp_distribution
        '33-33-33' => 0.33
        '40-40-20' => 0.40
        '20-30-50' => 0.30
        '50-30-20' => 0.30
        '50-15-15-Flip' => 0.15
        '40-20-10-Flip' => 0.20
        '40-20-20-Flip' => 0.20
        '30-30-25-Flip' => 0.30
        '20-20-20-Flip' => 0.20
        '15-50-15-Flip' => 0.50
        '15-35-35-Flip' => 0.35
        '10-10-60-Flip' => 0.10
        => 0.33

tp3_size := if tp_distribution == 'Custom'
    custom_tp3 / 100
else
    switch tp_distribution
        '33-33-33' => 0.34
        '40-40-20' => 0.20
        '20-30-50' => 0.50
        '50-30-20' => 0.20
        '50-15-15-Flip' => 0.15
        '40-20-10-Flip' => 0.10
        '40-20-20-Flip' => 0.20
        '30-30-25-Flip' => 0.25
        '20-20-20-Flip' => 0.20
        '15-50-15-Flip' => 0.15
        '15-35-35-Flip' => 0.35
        '10-10-60-Flip' => 0.60
        => 0.34

// Handle custom distribution normalization and flip amount
if tp_distribution == 'Custom'
    total = custom_tp1 + custom_tp2 + custom_tp3
    if total > 100 // If over 100%, normalize
        tp1_size := custom_tp1 / total / 100
        tp2_size := custom_tp2 / total / 100
        tp3_size := custom_tp3 / total / 100
        should_flip := false
        should_flip
    else // If under 100%, allocate remaining as flip
        remaining = 100 - total
        tp1_size := custom_tp1 / 100
        tp2_size := custom_tp2 / 100
        tp3_size := custom_tp3 / 100
        // Adjust flip size based on remaining
        flip_size = remaining / 100
        should_flip := true
        should_flip
else
    should_flip := str.contains(tp_distribution, 'Flip')
    should_flip


// ======== TIMEFRAME CONVERSION FUNCTION ========
get_tf_label(tf) =>
    switch tf
        '1' => '1M'
        '2' => '2M'
        '3' => '3M'
        '4' => '4M'
        '5' => '5M'
        '10' => '10M'
        '15' => '15M'
        '20' => '20M'
        '30' => '30M'
        '60' => '1H'
        '120' => '2H'
        '180' => '3H'
        '240' => '4H'
        'D' => '1D'
        'W' => '1W'
        'M' => '1MN'
        => tf + ''

var array<int> signal_bars = array.new_int(0)  // Track bar indices of signals

int line_extension = 4  // Use the same value consistently
var bool longAlertFired = false

var float entryBar = na
var bool shortAlertFired = false





//color=#49d84bcc
//color=#f35621cc
//olor=#818384ccc
//↑ ↓ → ← ↗ ↘ ↙ ↖🔼 🔽 ⏫ ⏬➡️ ⬅️ ⬆️ ⬇️  Currency Symbols   //$ € £ ¥ ₩ ₹ ₿ (Bitcoin)//💵 💶 💷 💴 💸 (Money bags)//💰 (Money with wings)
//Technical Indicators
//📈 📉 (Charts)//🎯 (Target)//⚖️ (Balance)//📊 (Bar chart)//🔍 (Magnifying glass)//📌 (Pin)//📉 (Falling chart)//📈 (Rising chart)
//Market Sentiment🐂 (Bull)🐻 (Bear)🔥 (Hot/Fire)//❄️ (Cold/Ice)//💹 (Chart with Upwards Trend)//💱 (Currency Exchange)//Alerts & Notifications//🔔 (Bell)
//🚨 (Siren)//⚠️ (Warning)//⛔ (No Entry)//✅ (Check mark)//❌ (Cross mark)//❗ ❕ (Exclamation) //❓ ❔ (Question)//Status Indicators//🟢 (Green circle)//🔴 (Red circle)
//🟡 (Yellow circle)//🔵 (Blue circle)//⭐ (Star)//🌟 (Glowing star)//✨ (Sparkles)//💎 (Gem)//🎪 (Circus tent - volatility)//⚡ (Zap - fast move)//Time & Duration
//⏰ (Alarm clock)//⌛ (Hourglass)//⏳ (Running hourglass)//📅 (Calendar)//🕒 (Clock)//Animals & Nature//🦅 (Eagle)//🦉 (Owl)//🐍 (Snake - choppy markets)
//🌙 (Crescent moon)//🌞 (Sun)//🌈 (Rainbow)//🌊 (Wave)//Tools//🔨 (Hammer)//🛠️ (Tools)//🎛️ (Control knobs)//🧮 (Abacus)//⚙️ (Gear)//🚀 (Rocket)
//🎢 (Roller coaster - volatility)//🏦 (Bank)//💼 (Briefcase)//🛑 (Stop sign)//🎲 (Dice - randomness)🧩 (Puzzle piece)🔮 (Crystal ball)
//📜 (Scroll)🏷️ (Label)🎁 (Gift box)⚖️ (Scales)

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

// Timeframe conversion helper function
timeframeToHours(tf) =>
    tf == 'D' ? 24.0 : tf == '240' ? 4.0 : str.tonumber(tf) / 60.0

// ============================================================================
// ML ANALYSIS AND PERFORMANCE TRACKING VARIABLES
// ============================================================================
// Variables for machine learning analysis, performance tracking, and market sentiment

// Visual and display variables
var color bg_color = color.new(#2B2B2B, 90)        // Background color for panels
var int normalized_atr = 100                        // Normalized ATR value
var int st_count = 7                                // SuperTrend count

// Volume analysis variables
var int last_volume_update = na                     // Last volume update bar
var float rolling_volume = na                       // Rolling volume calculation
var int bar_count = 0                               // Bar counter

// ML performance and confidence tracking
var int confidence_score = 0                        // Overall confidence score (0-100)
var int ml_hit_rate = 0                            // ML model hit rate percentage
var int win_rate = 0                               // Overall win rate percentage

// Market analysis variables
var float volume_sensitivity = na                   // Volume sensitivity factor
var string st_meter = na                           // SuperTrend meter display
var float volume_direction = na                     // Volume direction indicator
var float market_sentiment = na                     // Market sentiment score

// Performance tracking variables
var float gross_profit = 0.0                       // Total gross profit
var float gross_loss = 0.0                         // Total gross loss
var float max_equity = na                          // Maximum equity reached
var float max_drawdown = 0.0                       // Maximum drawdown percentage


// ============================================================================
// CONFIDENCE BAR VISUALIZATION FUNCTION
// ============================================================================
// Creates a visual confidence bar representation for display panels
// Parameters:
//   - score: Confidence score (0-100)
// Returns: String representation of confidence bar with filled/empty blocks
getConfidenceBar(score) =>
    // Add safety checks and value clamping to prevent display errors
    safe_score = math.max(0, math.min(100, score))  // Force score into 0-100 range
    blocks = 10                                      // Total number of blocks in bar
    filled = math.round(safe_score / (100.0 / blocks))  // Calculate filled blocks
    filled := math.max(0, math.min(blocks, filled)) // Double-clamp filled value
    
    // Create visual bar with filled (▓) and empty (░) blocks
    bar = str.repeat('▓', filled) + str.repeat('░', blocks - filled)
    '[' + bar + ']'

// Returns an array [trend, strength] computed on data from the given timeframe.
calcTrueDirectionalStrength(tf) =>
    // Get data from the specified timeframe with proper error handling
    [close_tf, high_tf, low_tf] = request.security(syminfo.tickerid, tf, [close, high, low])
    
    // Ensure we have valid data before proceeding
    if na(close_tf) or na(high_tf) or na(low_tf)
        [0.0, 0.0]  // Return neutral values if data is not available
    else
        period = 14
        atr_val = ta.atr(period)
        
        // Handle na values in ATR calculation
        if na(atr_val)
            atr_val := 0.0001  // Use small default value to prevent division by zero
        
        priceRange = ta.highest(high_tf, period) - ta.lowest(low_tf, period)
        
        // Handle zero or na price range
        if na(priceRange) or priceRange == 0
            priceRange := atr_val
        
        trend_val = ta.change(close_tf, period)
        momentum_val = ta.rsi(close_tf, period)
        
        // Handle na values in momentum calculation
        if na(momentum_val)
            momentum_val := 50.0  // Default to neutral RSI
        
        volume_factor = volume > ta.sma(volume, period) ? 1.2 : 0.8
        
        // Calculate raw strength with safety checks
        raw_strength = if trend_val > 0
            (close_tf - ta.lowest(low_tf, period)) / priceRange * 100 * volume_factor * (momentum_val / 50)
        else if trend_val < 0
            (close_tf - ta.highest(high_tf, period)) / priceRange * 100 * volume_factor * ((100 - momentum_val) / 50)
        else
            0.0
        
        // Handle na values in strength calculation
        if na(raw_strength)
            raw_strength := 0.0
        
        smoothed_strength = ta.ema(raw_strength, period)
        
        // Handle na values in smoothed strength
        if na(smoothed_strength)
            smoothed_strength := 0.0
        
        scaled_strength = math.abs(smoothed_strength) < 1 ? smoothed_strength * 10 : smoothed_strength
        
        // Ensure final values are within bounds and not na
        final_trend = na(trend_val) ? 0.0 : trend_val
        final_strength = math.round(math.max(math.min(scaled_strength, 200), -200), 1)
        
        [final_trend, final_strength]

calcDirectionalTrend(tf) =>
    [trendLocal, strengthLocal] = calcTrueDirectionalStrength(tf)
    trendLocal

calcDirectionalStrengthValue(tf) =>
    // Use the actual strength calculation instead of undefined variables
    [current_trend, current_strength] = calcTrueDirectionalStrength(tf)
    
    // Handle na values
    if na(current_strength)
        current_strength := 0.0
    
    timeframe_multiplier = switch tf
        '1'   => 0.45    // 1m
        '2'   => 0.675   // 2m
        '3'   => 0.69    // 3m
        '5'   => 0.7     // 5m
        '15'  => 1.0     // 15m
        '30'  => 1.5     // 30m
        '60'  => 1.75    // 1h
        '240' => 2.8     // 4h
        'D'   => 3.5     // 1d
        => 1.0

    adjusted_strength = current_strength * timeframe_multiplier
    adjusted_strength

// ============================================================================
// GLOBAL VARIABLES FOR DATA DUMP SYNCHRONIZATION
// ============================================================================
// These global variables ensure that calculated values are properly synchronized
// between the calculation functions and the data dump arrays

var float global_m5_strength = 0.0
var float global_m15_strength = 0.0
var float global_h1_strength = 0.0
var float global_bull_confidence = 0.0
var float global_bear_confidence = 0.0
var float global_st_consensus = 0.0
var float global_overall_volume = 0.0
var float global_rsi_value = 0.0
var float global_adx_value = 0.0
var float global_macd_hist = 0.0
var float global_atr_percent = 0.0
var float global_plus_di = 0.0
var float global_minus_di = 0.0
var float global_price_momentum = 0.0
var float global_price_velocity = 0.0
var string global_market_state = "NEUTRAL"
var float global_trend_strength = 0.0
var float global_stoch_rsi = 0.0
var bool global_volume_impulse = false

// ======== VOLUME SYSTEM ========
// Rolling volume calculation for a single timeframe
// Rolling volume calculation for a single timeframe
f_calcRollingVolume(array<int> array_time, array<float> array_vol, int buffer_size, float new_volume, string tf) =>
    var float smoothed_vol = na
    var float smoothing_factor = 0.05

    // Update only on timeframe-specific bars
    bool update = timeframe.isminutes and timeframe.multiplier == str.tonumber(tf) or tf == '1' or bar_index % str.tonumber(tf) == 0

    if update and is_trading_time()
        if array.size(array_time) >= buffer_size
            array.shift(array_time)
            array.shift(array_vol)
        array.push(array_time, time)
        array.push(array_vol, math.max(new_volume, 0))

        // Calculate smoothed volume
        float avg_vol = array.avg(array_vol)
        smoothed_vol := na(smoothed_vol) ? avg_vol : smoothed_vol * (1 - smoothing_factor) + avg_vol * smoothing_factor
        smoothed_vol

    // FIXED: Calculate ta.sma unconditionally outside the ternary operator
    float volume_sma = ta.sma(volume, 60)

    // Calculate historical average using array.avg instead of conditional ta.sma
    float hist_avg = array.avg(array_vol)
    hist_avg := na(hist_avg) or hist_avg <= 0 ? volume_sma : hist_avg

    // Compute rolling percentage
    float vol_percent = smoothed_vol / hist_avg * 100
    vol_percent := math.min(math.max(vol_percent, 50), 200)

    [smoothed_vol, math.round(vol_percent, 1)]

    // Compute rolling percentage

    vol_percent := math.min(math.max(vol_percent, 50), 200)

    [smoothed_vol, math.round(vol_percent, 1)]
    // Declare arrays for each timeframe
    // Declare arrays for each timeframe
var array<int> time_1m = array.new_int(0)
var array<float> vol_1m = array.new_float(0)
var array<int> time_2m = array.new_int(0)
var array<float> vol_2m = array.new_float(0)
var array<int> time_3m = array.new_int(0)
var array<float> vol_3m = array.new_float(0)
var array<int> time_5m = array.new_int(0)
var array<float> vol_5m = array.new_float(0)
var array<int> time_15m = array.new_int(0)
var array<float> vol_15m = array.new_float(0)
var array<int> time_30m = array.new_int(0)
var array<float> vol_30m = array.new_float(0)
var array<int> time_60m = array.new_int(0)
var array<float> vol_60m = array.new_float(0)
var array<int> time_240m = array.new_int(0)
var array<float> vol_240m = array.new_float(0)
var array<int> time_d = array.new_int(0)
var array<float> vol_d = array.new_float(0)


// ============================================================================
// MULTI-TIMEFRAME VOLUME ANALYSIS
// ============================================================================

// Signal condition variables for trade logic
var bool longCondition = false
var bool shortCondition = false

// ============================================================================
// ROLLING VOLUME CALCULATIONS FOR ALL TIMEFRAMES
// ============================================================================
// Calculate rolling volume percentages for each timeframe using dedicated arrays
[m1_smoothed_vol, m1_vol_percent] = f_calcRollingVolume(time_1m, vol_1m, 60, volume, '1')    // 1-minute volume analysis
[m2_smoothed_vol, m2_vol_percent] = f_calcRollingVolume(time_2m, vol_2m, 30, volume, '2')    // 2-minute volume analysis
[m3_smoothed_vol, m3_vol_percent] = f_calcRollingVolume(time_3m, vol_3m, 20, volume, '3')    // 3-minute volume analysis
[m5_smoothed_vol, m5_vol_percent] = f_calcRollingVolume(time_5m, vol_5m, 12, volume, '5')    // 5-minute volume analysis
[m15_smoothed_vol, m15_vol_percent] = f_calcRollingVolume(time_15m, vol_15m, 4, volume, '15') // 15-minute volume analysis
[m30_smoothed_vol, m30_vol_percent] = f_calcRollingVolume(time_30m, vol_30m, 2, volume, '30') // 30-minute volume analysis
[h1_smoothed_vol, h1_vol_percent] = f_calcRollingVolume(time_60m, vol_60m, 1, volume, '60')   // 1-hour volume analysis
[h4_smoothed_vol, h4_vol_percent] = f_calcRollingVolume(time_240m, vol_240m, 1, volume, '240') // 4-hour volume analysis
[d_smoothed_vol, d_vol_percent] = f_calcRollingVolume(time_d, vol_d, 1, volume, 'D')          // Daily volume analysis

// ============================================================================
// WEIGHTED OVERALL VOLUME PERCENTAGE CALCULATION
// ============================================================================
// Calculate weighted overall volume percentage with emphasis on shorter timeframes
// Weights: 1m(30%) + 2m(20%) + 3m(15%) + 5m(20%) + 15m(15%) = 100%
overall_volume_percent = m1_vol_percent * 0.3 + m2_vol_percent * 0.2 + m3_vol_percent * 0.15 + m5_vol_percent * 0.2 + m15_vol_percent * 0.15

// Apply smoothing to reduce noise and clamp values to reasonable range
overall_volume_percent := na(overall_volume_percent[1]) ? overall_volume_percent : overall_volume_percent[1] * 0.8 + overall_volume_percent * 0.2
overall_volume_percent := math.min(math.max(overall_volume_percent, 50), 200)  // Clamp between 50% and 200%

// ============================================================================
// SUPERTREND CONSENSUS CALCULATION
// ============================================================================
// Rolling window calculation for SuperTrend consensus across multiple configurations
var array<float> st_consensus_array = array.new_float(0)

// Add current SuperTrend bullish percentage to rolling window
array.push(st_consensus_array, array.sum(st_bullish) / 7.0 * 100)

// Maintain rolling window size by removing oldest values
if array.size(st_consensus_array) > st_roll_window
    array.shift(st_consensus_array)

// Calculate consensus metrics
st_consensus = array.avg(st_consensus_array)                    // Average consensus percentage
st_direction = st_consensus >= 50 ? 'Bullish' : 'Bearish'      // Overall direction
st_agreement = math.round(math.abs(50 - st_consensus))         // Agreement strength (0-50)



macdScore = (macdLine - macdLine[1]) * 10

// RSI with Adjustable Window
rsiScore = math.min(1, (ta.rsi(close, 3) - 30) / (70 - 30))

// Volume Score
volumeScore = math.min(1, volume / ta.sma(volume, 3))

// Weighted Bullishness
bullishnessLevel = rsiScore * 0.4 + macdScore * 0.4 + volumeScore * 0.2

// 1. Create a function that returns a tuple of security calls
fetch_mtf_data() =>
    [        get_security(syminfo.tickerid, '1', ta.change(close, 1)),        get_security(syminfo.tickerid, '2', ta.change(close, 1)),        get_security(syminfo.tickerid, '3', ta.change(close, 1)),        get_security(syminfo.tickerid, '5', ta.change(close, 1)),       get_security(syminfo.tickerid, '15', ta.change(close, 1)),        get_security(syminfo.tickerid, '30', ta.change(close, 1)),        get_security(syminfo.tickerid, '60', ta.change(close, 1)),       get_security(syminfo.tickerid, '240', ta.change(close, 1)),        get_security(syminfo.tickerid, 'D', ta.change(close, 1))    ]

// 2. Assign directly to variables using tuple syntax
[m1, m2, m3, m5, m15, m30, h1, h4, d1] = fetch_mtf_data()

calcADX(length) =>
    up = high - high[1]
    down = low[1] - low
    plusDM = up > down and up > 0 ? up : 0
    minusDM = down > up and down > 0 ? down : 0
    trueRange = math.max(high - low, math.max(math.abs(high - close[1]), math.abs(low - close[1])))
    plusDI = 100 * ta.rma(plusDM, length) / ta.rma(trueRange, length)
    minusDI = 100 * ta.rma(minusDM, length) / ta.rma(trueRange, length)
    dx = 100 * math.abs(plusDI - minusDI) / (plusDI + minusDI)
    adx = ta.rma(dx, length)
    [adx, plusDI, minusDI]

// Helper function to get just the ADX value
getADX(length) =>
    [adx, _, _] = calcADX(length)
    adx

detectTrendState() =>
    // Multi-timeframe ADX with dynamic smoothing
    currentADX = ta.ema(getADX(14), 3)
    adx5 = ta.ema(get_security(syminfo.tickerid, '5', getADX(14)), 3)
    adx15 = ta.ema(request.security(syminfo.tickerid, '15', getADX(14)), 3)

    // Enhanced volatility analysis with adaptive thresholds
    atr_normalized = ta.atr(14)/close
    price_range = (ta.highest(high, 20) - ta.lowest(low, 20))/close
    range_tightness = ta.ema(price_range, 5) < 0.015  // 1.5% range

    // Volume analysis with dynamic scaling
    volume_ratio = volume/ta.sma(volume, 20)
    volume_spike = volume_ratio > 1.25
    volume_dryup = volume_ratio < 0.65

    // Momentum confluence with confirmation checks
    rsi = ta.rsi(close, 14)
    macd_line = ta.ema(close, 12) - ta.ema(close, 26)
    momentum_divergence = ta.crossover(macd_line, ta.ema(macd_line, 9)) ? 1 :
                         ta.crossunder(macd_line, ta.ema(macd_line, 9)) ? -1 : 0

    // Improved trend identification with more sensitivity
    sma20 = ta.sma(close, 20)
    sma50 = ta.sma(close, 50)

    // Price direction - use 1-period changes instead of 3 for more sensitivity
    price_rising = close > close[1]
    price_falling = close < close[1]

    // Get directional indicators for trend bias
    [_, plusDI, minusDI] = calcADX(14)
    trend_bias = plusDI > minusDI ? "bullish" : "bearish"

    // More flexible trend confirmation with adaptive criteria based on ADX strength
    // When ADX is very high, we're more lenient with other conditions
    adx_very_high = currentADX > 30
    adx_high = currentADX > 20

    // Adaptive criteria based on ADX strength
    bullish_trend_confirm = adx_very_high ?         (close > sma20 and trend_bias == "bullish") :         (close > sma20 and currentADX > 18 and (volume_ratio > 0.85 or price_rising))  // More conditions otherwise

    bearish_trend_confirm = adx_very_high ?         (close < sma20 and trend_bias == "bearish") :          (close < sma20 and currentADX > 18 and (volume_ratio > 0.85 or price_falling))  // More conditions otherwise

    // Directional strength with more sensitivity
    directional_movement = ta.change(close, 5) / close * 100
    strong_directional = math.abs(directional_movement) > 0.5  // Reduced threshold for sensitivity

    // Condition flags for debugging
    bool bull_price_above_ma = close > sma20
    bool bear_price_below_ma = close < sma20
    bool adx_high_enough = currentADX > 18
    bool vol_sufficient = volume_ratio > 0.85

    // Trend strength score (0-100) for more granular classification
    trend_strength = math.min(100, currentADX * 3 + math.abs(directional_movement) * 10 + volume_ratio * 20)

    // State machine with improved logic, using ADX as the primary indicator
    phase = if currentADX > 30 and bull_price_above_ma and trend_bias == "bullish"
        'BULL TREND'
    else if currentADX > 30 and bear_price_below_ma and trend_bias == "bearish"
        'BEAR TREND'
    else if bullish_trend_confirm
        'BULL TREND'
    else if bearish_trend_confirm
        'BEAR TREND'
    else if currentADX > 15 and ta.change(currentADX, 3) > 0
        'EARLY TREND'
    else if atr_normalized > 0.015 or (ta.stdev(close, 14)/close > 0.012)
        'VOLATILE'
    else if range_tightness and volume_dryup and currentADX < 18
        'RANGING'
    else
        'NEUTRAL'

    // Comprehensive debug information
    if barstate.islast
        var label debug_label = na
        if not na(debug_label)
            label.delete(debug_label)

        // Create detailed status messages
        string bull_status = "BULL TEST:\n" +
                           "Price > MA20: " + (bull_price_above_ma ? "✓" : "✗") + "\n" +
                           "ADX > 18: " + (adx_high_enough ? "✓" : "✗") + "\n" +
                           "Vol Ratio > 0.85: " + (vol_sufficient ? "✓" : "✗") + "\n" +
                           "Price Rising: " + (price_rising ? "✓" : "✗") + "\n" +
                           "Trend Bias: " + trend_bias

        string bear_status = "BEAR TEST:\n" +
                           "Price < MA20: " + (bear_price_below_ma ? "✓" : "✗") + "\n" +
                           "ADX > 18: " + (adx_high_enough ? "✓" : "✗") + "\n" +
                           "Vol Ratio > 0.85: " + (vol_sufficient ? "✓" : "✗") + "\n" +
                           "Price Falling: " + (price_falling ? "✓" : "✗") + "\n" +
                           "Trend Bias: " + trend_bias

        string main_metrics = "METRICS:\n" +                            "ADX: " + str.tostring(math.round(currentADX, 1)) + "\n" +                            "Dir Move: " + str.tostring(math.round(directional_movement, 2)) + "%\n" +                            "ATR%: " + str.tostring(math.round(atr_normalized * 100, 2)) + "\n" +                            "Vol Ratio: " + str.tostring(math.round(volume_ratio, 2)) + "\n" +                            "Range: " + str.tostring(math.round(price_range * 100, 2)) + "%\n" +                            "Trend Strength: " + str.tostring(math.round(trend_strength, 0)) + "/100"


    phase
trend_state = detectTrendState()
// Add this function definition to your code
getConsensusBatteryBar() =>
    // Calculate consensus percentage based on supertrend directions
    int bullish_count = 0
    for i = 0 to 6 by 1
        if array.get(st_bullish, i) == 1
            bullish_count := bullish_count + 1
            bullish_count

    // Determine if majority is bullish
    bool isMajorityBullish = bullish_count >= 4 // 4 or more out of 7 is majority

    // Calculate percentage (always 50% or higher for the majority direction)
    float percent = 0.0
    if isMajorityBullish
        percent := math.max(50, bullish_count / 7.0 * 100)
        percent
    else
        percent := math.max(50, (7 - bullish_count) / 7.0 * 100)
        percent

    // Generate battery bar
    int segments = 7
    int filled = int(math.round(percent / (100.0 / segments)))
    filled := math.min(segments, math.max(0, filled))

    string filledChar = '▓'
    string emptyChar = '░'

    string bar = str.repeat(filledChar, filled) + str.repeat(emptyChar, segments - filled)

    '[' + bar + '] ' + str.tostring(math.round(percent, 1)) + '%'

    // Return both the bar string and the count
    [bar, bullish_count]  // Return tuple

// 2. Call the function and store the count
[battery_bar, mtf_bullish_count] = getConsensusBatteryBar()

// ======== CONFIDENCE PRESET SYSTEM ========
getPresetWeights() =>
    switch confidence_preset
        'Conservative' => array.from(0.40, 0.20, 0.15, 0.15, 0.10, 70)
        'Balanced' => array.from(0.30, 0.25, 0.20, 0.15, 0.10, 50)
        'Aggressive' => array.from(0.20, 0.30, 0.25, 0.15, 0.10, 40)
// Add this at the top of your script with other utility functions
clamp(value, min_val, max_val) =>
    math.max(math.min(value, max_val), min_val)

adjustTFStrength(strength_value, target_tf_minutes) =>
    // Convert all timeframes to minutes for accurate comparison
    current_tf_minutes = timeframe.in_seconds() / 60
    target_tf = target_tf_minutes

    // Calculate ratio using logarithmic scale to prevent over-amplification
    ratio = math.log(target_tf) / math.log(current_tf_minutes)

    // Apply non-linear scaling factor
    scaling_factor = math.pow(ratio, 1.2)

    // Get base multiplier from presets
    base_mult = switch math.round(target_tf)
        1      => tf_mult_1m
        2      => tf_mult_2m
        3      => tf_mult_3m
        5      => tf_mult_5m
        15     => tf_mult_15m
        30     => tf_mult_30m
        45     => tf_mult_45m
        60     => tf_mult_1h
        120    => tf_mult_2h
        240    => tf_mult_4h
        1440   => tf_mult_1d
        => 1.0

    // Final adjusted strength with volatility damping
    adjusted = strength_value * base_mult * scaling_factor
    math.min(100, math.max(0, adjusted))  // Keep within 0-100 range


mtf_consensus = int((    adjustTFStrength(m1, 1) +    adjustTFStrength(m2, 2) +    adjustTFStrength(m3, 3) +    adjustTFStrength(m5, 5) +    adjustTFStrength(m15, 15) +    adjustTFStrength(m30, 30) +    adjustTFStrength(h1, 60) +    adjustTFStrength(h4, 240) +    adjustTFStrength(d1, 1440)) / 9)


calcBullConfidence() =>
    weights = getPresetWeights()

    // Enhanced bull-specific components with increased sensitivity
    bull_st_conf = array.sum(st_bullish) / 7.0

    // Add price momentum component (3-bar rate of change)
    price_momentum = (close - close[3]) / close[3] * 100
    momentum_factor = math.min(math.max(price_momentum * 2, 0), 1.5)

    // Smart volume direction with acceleration - ONLY enhance if price is rising
    vol_direction = close > close[1] ? 1.5 : close < close[1] ? 0.5 : 1.0
    vol_acc = close > close[1] and close[1] > close[2] ? 1.8 : close < close[1] and close[1] < close[2] ? 0.4 : vol_direction
    vol_score = math.min(overall_volume_percent / 100, 1.5) * vol_acc

    // Volatility-adjusted confidence (higher volatility = more extreme readings)
    market_volatility = ta.atr(14) / close * 100
    volatility_factor = math.max(1.0, market_volatility / 2)

    // Phase confidence with stronger trend recognition
    phase_conf =        trend_state == 'BULL TREND' ? 1.0 :       trend_state == 'BEAR TREND' ? 0.0 :       trend_state == 'EARLY TREND' ? 0.6 :       trend_state == 'VOLATILE' ? 0.7 :       trend_state == 'RANGING' ? 0.5 : 0.3
    // Enhanced MTF confidence with stronger exponential scaling
    mtf_conf = math.pow(mtf_consensus / 100, 2.0)

    // ML trend confidence with binary assignment
    trend_conf = str.contains(ml_state, 'Bull') ? 1.0 :
                 str.contains(ml_state, 'Bear') ? 0.0 : 0.4

    // Raw confidence with increased multiplier for stronger signals
    raw_conf = (bull_st_conf * weights.get(0) +
               vol_score * weights.get(1) +
               phase_conf * weights.get(2) +
               trend_conf * weights.get(3) +
               mtf_conf * weights.get(4) +
               momentum_factor * 0.1) * 120 * volatility_factor

    // Use shorter smoothing period for faster response
    bull_final = ta.wma(raw_conf, confidence_smoothing)

    // Final output with proper clamping
    clamp(bull_final, 0, 100)

calcBearConfidence() =>
    weights = getPresetWeights()

    // Bear-specific components with increased sensitivity
    bear_st_conf = 1 - (array.sum(st_bullish) / 7.0)

    // Add price momentum component (3-bar rate of change) - INVERTED for bearish
    price_momentum = (close - close[3]) / close[3] * 100
    momentum_factor = math.min(math.max(-price_momentum * 2, 0), 1.5)  // Note the negative sign

    // Smart volume direction with acceleration - ONLY enhance if price is falling
    vol_direction = close < close[1] ? 1.5 : close > close[1] ? 0.5 : 1.0  // Reversed from bull case
    vol_acc = close < close[1] and close[1] < close[2] ? 1.8 : close > close[1] and close[1] > close[2] ? 0.4 : vol_direction
    vol_score = math.min(overall_volume_percent / 100, 1.5) * vol_acc

    // Volatility-adjusted confidence (higher volatility = more extreme readings)
    market_volatility = ta.atr(14) / close * 100
    volatility_factor = math.max(1.0, market_volatility / 2)

    // Phase confidence with stronger trend recognition
    phase_conf =
       trend_state == 'BEAR TREND' ? 1.0 :  // Full confidence in bear trend
       trend_state == 'BULL TREND' ? 0.0 :  // Zero confidence in bull trend
       trend_state == 'EARLY TREND' ? 0.4 : // Less confidence than bull case
       trend_state == 'VOLATILE' ? 0.7 :    // High in volatile markets - bears thrive in chaos
       trend_state == 'RANGING' ? 0.5 : 0.3 // Neutral in ranges

    // Enhanced MTF confidence with stronger exponential scaling
    mtf_conf = math.pow(1 - (mtf_consensus / 100), 2.0)

    // ML trend confidence with binary assignment
    trend_conf = str.contains(ml_state, 'Bear') ? 1.0 :
                 str.contains(ml_state, 'Bull') ? 0.0 : 0.4

    // Raw confidence with increased multiplier for stronger signals
    raw_conf = (bear_st_conf * weights.get(0) +
               vol_score * weights.get(1) +
               phase_conf * weights.get(2) +
               trend_conf * weights.get(3) +
               mtf_conf * weights.get(4) +
               momentum_factor * 0.1) * 120 * volatility_factor

    // Use shorter smoothing period for faster response
    bear_final = ta.wma(raw_conf, confidence_smoothing)

    // Final output with proper clamping
    clamp(bear_final, 0, 100)

// Function to normalize bull and bear confidence to sum to 100
// This helps create a clearer signal direction
normalizeConfidence() =>
    bull = calcBullConfidence()
    bear = calcBearConfidence()

    // Adjust if both are very low - we want some signal
    if bull < 20 and bear < 20
        avg = (bull + bear) / 2
        min_signal = 30
        bull := bull > bear ? min_signal : min_signal * (bull / (bull + bear))
        bear := bear > bull ? min_signal : min_signal * (bear / (bull + bear))

    // If values are very close, enhance the difference
    if math.abs(bull - bear) < 10
        if bull > bear
            bull := bull + 5
            bear := bear - 5
        else
            bull := bull - 5
            bear := bear + 5

    // Normalize to sum to 100
    total = bull + bear
    if total > 0
        bull_normalized = (bull / total) * 100
        bear_normalized = (bear / total) * 100
        [bull_normalized, bear_normalized]
    else
        [50, 50]  // Default to neutral if both are zero

// Use this in your panel display
[bull_confidence, bear_confidence] = normalizeConfidence()

// ============================================================================
// CONSENSUS BATTERY VISUALIZATION SYSTEM
// ============================================================================
// Functions to create visual battery representations for confidence scores

// Bull confidence battery visualization
getBullBattery(score) =>
    blocks = 10
    filled = math.round(score / int(100 / blocks))
    bar = str.repeat('▓', filled) + str.repeat('░', blocks - filled)
    '[' + bar + ']'

// Bear confidence battery visualization
getBearBattery(score) =>
    blocks = 10
    filled = math.round(score / int(100 / blocks))
    bar = str.repeat('▓', filled) + str.repeat('░', blocks - filled)
    '[' + bar + ']'

// Get appropriate battery based on current position
getPositionBattery() =>
    bull_conf = calcBullConfidence()
    bear_conf = calcBearConfidence()
    // Use current conditions to determine which battery to display
    longCondition ? getBullBattery(bull_conf) : shortCondition ? getBearBattery(bear_conf) : is_long ? getBullBattery(bull_conf) : getBearBattery(bear_conf)

// ============================================================================
// CONSENSUS CALCULATIONS
// ============================================================================
// Calculate consensus excluding current signal for analysis
bull_consensus_excl_self = array.sum(array.slice(st_bullish, 0, 6)) / 6.0 * 100
bear_consensus_excl_self = 100 - bull_consensus_excl_self

// Position state variables
is_short = shortCondition
// ======== SUPER TREND CONFIGURATIONS ========
[st1, dir1, up1, dn1] = calcSuperTrend(9, 23.0, 1.0)
[st2, dir2, up2, dn2] = calcSuperTrend(15, 3.2, 1.0)
[st3, dir3, up3, dn3] = calcSuperTrend(21, 3.5, 1.0)
[st4, dir4, up4, dn4] = calcSuperTrend(12, 2.9, 1.0)
[st5, dir5, up5, dn5] = calcSuperTrend(10, 2.8, 1.0)
[st6, dir6, up6, dn6] = calcSuperTrend(9, 2.6, 1.0)
[st7, dir7, up7, dn7] = calcSuperTrend(7, 2.5, 1.0)

st1_signal = dir1 == 1 ? 'LONG' : 'SHORT'
st1_color = dir1 == 1 ? color.green : color.red
st2_signal = dir2 == 1 ? 'LONG' : 'SHORT'
st2_color = dir2 == 1 ? color.green : color.red
st3_signal = dir3 == 1 ? 'LONG' : 'SHORT'
st3_color = dir3 == 1 ? color.green : color.red
st4_signal = dir4 == 1 ? 'LONG' : 'SHORT'
st4_color = dir4 == 1 ? color.green : color.red
st5_signal = dir5 == 1 ? 'LONG' : 'SHORT'
st5_color = dir5 == 1 ? color.green : color.red
st6_signal = dir6 == 1 ? 'LONG' : 'SHORT'
st6_color = dir6 == 1 ? color.green : color.red
st7_signal = dir7 == 1 ? 'LONG' : 'SHORT'
st7_color = dir7 == 1 ? color.green : color.red
// Create panel if enabled

// Improved directional strength calculation
calcTrueDirectionalStrength() =>
    market_state = ta.sma(ta.ema(close, 20), 50)
    use_hma = market_state > 50
    tr = math.max(high - low, math.max(math.abs(high - close[1]), math.abs(low - close[1])))
    plus_dm = high - high[1] > low[1] - low ? math.max(high - high[1], 0) : 0
    minus_dm = low[1] - low > high - high[1] ? math.max(low[1] - low, 0) : 0
    hmaLength = 14
    emaLength = 28
    hma_tr_smooth = ta.hma(tr, hmaLength)
    ema_tr_smooth = ta.ema(tr, emaLength)
    tr_smooth = use_hma ? hma_tr_smooth : ema_tr_smooth
    hma_plus_dm_smooth = ta.hma(plus_dm, hmaLength)
    ema_plus_dm_smooth = ta.ema(plus_dm, emaLength)
    plus_dm_smooth = use_hma ? hma_plus_dm_smooth : ema_plus_dm_smooth
    hma_minus_dm_smooth = ta.hma(minus_dm, hmaLength)
    ema_minus_dm_smooth = ta.ema(minus_dm, emaLength)
    minus_dm_smooth = use_hma ? hma_minus_dm_smooth : ema_minus_dm_smooth
    tr_smooth := math.max(tr_smooth, 0.001)
    di_plus = 100 * plus_dm_smooth / tr_smooth
    di_minus = 100 * minus_dm_smooth / tr_smooth
    sum_di = di_plus + di_minus
    sum_di := math.max(sum_di, 0.001)
    dx = math.abs(di_plus - di_minus) / sum_di * 100
    hma_strength = ta.hma(dx, hmaLength)
    ema_strength = ta.ema(dx, emaLength)
    strength = use_hma ? hma_strength : ema_strength
    avg_strength = ta.sma(strength, 50)
    scale_factor = avg_strength > 50 ? 1.2 : 1
    normalized = strength * scale_factor
    math.min(math.max(normalized, 0), 100)

// Improved Volatility Calculation using Parkinson's Estimator
calcVolatility(tf) =>
    var array<float> volatility_buffer = array.new_float(20, 0)
    var int volatility_index = 0
    [high_tf, low_tf] = request.security(syminfo.tickerid, tf, [high, low])
    high_low_range = math.log(high_tf / low_tf)
    parkinson_factor = 1.0 / (4.0 * math.log(2.0))
    parkinsons_vol = math.sqrt(parkinson_factor * math.pow(high_low_range, 2))
    base_length = 14
    dynamic_length = math.round(base_length * (1 + parkinsons_vol))
    array.set(volatility_buffer, volatility_index, math.pow(high_low_range, 2))
    volatility_index := (volatility_index + 1) % 20
    buffered_vol = math.sqrt(parkinson_factor * array.avg(volatility_buffer))
    dynamic_vol = ta.stdev(hlc3, dynamic_length) / hlc3 * 100
    volty = (buffered_vol + dynamic_vol) / 2
    volty := na(volty) or volty == 0 ? 0.0001 : volty
    tf_hours = timeframeToHours(tf)
    annualization_factor = math.sqrt(8760 / math.max(tf_hours, 0.0167))
    math.round(volty * annualization_factor, 2)

current_volume = request.security(syminfo.tickerid, '60', volume)
timestamp = request.security(syminfo.tickerid, '60', time)

// ———— NEW HOURLY VOLATILITY ————
var array<float> hourly_highs = array.new_float(24)
var array<float> hourly_lows = array.new_float(24)
var int hour_counter = 0
calcVolatilityHourly(tf) =>
    debug_label = 'TF: ' + tf + ' | '
    is_new_hour = ta.change(time('60'))
    current_high = ta.valuewhen(bool(is_new_hour), high, 0)
    current_low = ta.valuewhen(bool(is_new_hour), low, 0)
    if bool(is_new_hour)
        array.push(hourly_highs, current_high)
        array.push(hourly_lows, current_low)
        array.shift(hourly_highs)
        array.shift(hourly_lows)
        debug_label := debug_label + 'New Hour | '
        debug_label
    hourly_mid = array.avg(hourly_highs) / 2 + array.avg(hourly_lows) / 2
    hourly_atr = request.security(syminfo.tickerid, '60', ta.atr(14))
    hourly_mid := hourly_mid > 0 ? hourly_mid : 1
    volatility_ratio = hourly_atr / hourly_mid * 100
    debug_label := debug_label + 'ATR: ' + str.tostring(hourly_atr) + ' | Mid: ' + str.tostring(hourly_mid) + ' | Raw: ' + str.tostring(volatility_ratio)
    lookback = 3 * 5
    max_vol = ta.highest(volatility_ratio, lookback)
    min_vol = ta.lowest(volatility_ratio, lookback)
    vol_range = math.max(max_vol - min_vol, 0.0001)
    normalized_vol = (volatility_ratio - min_vol) / vol_range * 100
    normalized_vol := math.round(math.min(math.max(normalized_vol, 0), 100), 2)
    debug_label := debug_label + ' | 3H Norm: ' + str.tostring(normalized_vol, '#.##') + '%'
    [normalized_vol, debug_label]

// ======== VOLUME SPIKE DETECTION ========
calcVolumeStats(tf) =>
    [vol_tf] = request.security(syminfo.tickerid, tf, [volume])
    avgVol = ta.sma(vol_tf, 20)
    spikePct = vol_tf / avgVol * 100
    debugInfo = str.format('Curr: {0}\nAvg: {1}', math.round(vol_tf, 0), math.round(avgVol, 0))
    [avgVol, spikePct, debugInfo]

// ======== UNIVERSAL DATA FETCHER ========
fetchAllData(tf) =>
    [c, h, l, v] = request.security(syminfo.tickerid, tf, [close, high, low, volume])
    [rsi, atr, vol_sma] = request.security(syminfo.tickerid, tf, [ta.rsi(close, 14), ta.atr(14), ta.sma(volume, 20)])
    volty = request.security(syminfo.tickerid, tf, calcVolatility(tf))
    [c, h, l, v, rsi, atr, vol_sma, volty]

// ======== DATA PROCESSOR ========
processTimeframe(tf) =>
    [close_tf, high_tf, low_tf, volume_tf, rsi_tf, atr_tf, volume_sma, volatility] = fetchAllData(tf)
    priceRange = ta.highest(high_tf, 14) - ta.lowest(low_tf, 14)
    priceRange := priceRange == 0 ? atr_tf : priceRange
    trend_val = ta.change(close_tf, 14)
    // Calculate trend direction: 1 for up, -1 for down. Use nz to handle 0 or na, carrying forward the previous direction.
    var int trend_direction_prev = 0
    trend_direction = int(nz(math.sign(trend_val), trend_direction_prev))
    trend_direction_prev := trend_direction // Store current direction for next bar if needed

    volume_factor = volume_tf > volume_sma ? 1.2 : 0.8
    raw_strength = trend_val > 0 ? (close_tf - ta.lowest(low_tf, 14)) / priceRange * 100 * volume_factor * (rsi_tf / 50) : trend_val < 0 ? (close_tf - ta.highest(high_tf, 14)) / priceRange * 100 * volume_factor * ((100 - rsi_tf) / 50) : 0
    smoothed_strength = ta.ema(raw_strength, 14)
    scaled_strength = math.abs(smoothed_strength) < 1 ? smoothed_strength * 10 : smoothed_strength
    // Strength is already signed based on calculation logic
    strength = math.round(math.max(math.min(scaled_strength, 200), -200), 1)
    float spikePct = tf == '1' ? m1_vol_percent : tf == '2' ? m2_vol_percent : tf == '3' ? m3_vol_percent : tf == '5' ? m5_vol_percent : tf == '15' ? m15_vol_percent : tf == '30' ? m30_vol_percent : tf == '60' ? h1_vol_percent : tf == '240' ? h4_vol_percent : tf == 'D' ? d_vol_percent : 100.0
    debugInfo = str.format('Vol: {0}%', math.round(spikePct, 0))
    // Return trend_direction instead of trend_val
    [trend_direction, strength, volatility, volume_sma, spikePct, debugInfo]
var table panel3 = na

// Optimized SuperTrend calculations - reduce redundant calculations
var float ist1 = na, var float ist2 = na, var float ist3 = na, var float ist4 = na
var float ist5 = na, var float ist6 = na, var float ist7 = na
var int idir1 = na, var int idir2 = na, var int idir3 = na, var int idir4 = na
var int idir5 = na, var int idir6 = na, var int idir7 = na



// Calculate SuperTrends using selected model
if st_model == 'Dynamic Adaptive'
    [ist1, idir1, _, _, _, _] = calcDynamicSuperTrend(10, 3.0, 1.0)
    [ist2, idir2, _, _, _, _] = calcDynamicSuperTrend(9, 3.3, 1.0)
    [ist3, idir3, _, _, _, _] = calcDynamicSuperTrend(11, 2.7, 1.0)
    [ist4, idir4, _, _, _, _] = calcDynamicSuperTrend(8, 3.6, 1.0)
    [ist5, idir5, _, _, _, _] = calcDynamicSuperTrend(12, 2.4, 1.0)
    [ist6, idir6, _, _, _, _] = calcDynamicSuperTrend(7, 3.9, 1.0)
    [ist7, idir7, _, _, _, _] = calcDynamicSuperTrend(13, 2.1, 1.0)
else
    [ist1, idir1, _, _] = calcSuperTrend(10, 3.0, 1.0)
    [ist2, idir2, _, _] = calcSuperTrend(9, 3.3, 1.0)
    [ist3, idir3, _, _] = calcSuperTrend(11, 2.7, 1.0)
    [ist4, idir4, _, _] = calcSuperTrend(8, 3.6, 1.0)
    [ist5, idir5, _, _] = calcSuperTrend(12, 2.4, 1.0)
    [ist6, idir6, _, _] = calcSuperTrend(7, 3.9, 1.0)
    [ist7, idir7, _, _] = calcSuperTrend(13, 2.1, 1.0)





// ======== HELPER FUNCTIONS FOR AVERAGING (Add these) ========

// Function to calculate average strength from processed timeframes
calcAverageStrength() =>
    [_, d_str, _, _, _, _] = processTimeframe('D')
    [_, h4_str, _, _, _, _] = processTimeframe('240')
    [_, h1_str, _, _, _, _] = processTimeframe('60')
    [_, m30_str, _, _, _, _] = processTimeframe('30')
    [_, m15_str, _, _, _, _] = processTimeframe('15')
    [_, m5_str, _, _, _, _] = processTimeframe('5')
    [_, m3_str, _, _, _, _] = processTimeframe('3')
    [_, m2_str, _, _, _, _] = processTimeframe('2')
    [_, m1_str, _, _, _, _] = processTimeframe('1')

    // Calculate the average, excluding any 'na' values
    total_str = 0.0
    count_str = 0
    if not na(d_str)
        total_str += d_str
        count_str += 1
    if not na(h4_str)
        total_str += h4_str
        count_str += 1
    if not na(h1_str)
        total_str += h1_str
        count_str += 1
    if not na(m30_str)
        total_str += m30_str
        count_str += 1
    if not na(m15_str)
        total_str += m15_str
        count_str += 1
    if not na(m5_str)
        total_str += m5_str
        count_str += 1
    if not na(m3_str)
        total_str += m3_str
        count_str += 1
    if not na(m2_str)
        total_str += m2_str
        count_str += 1
    if not na(m1_str)
        total_str += m1_str
        count_str += 1

    count_str > 0 ? total_str / count_str : 0.0  // Return 0 if no valid values


// Function to calculate average volume spike percentage
calcAverageVolumeSpikePct() =>
    [_, _, _, _, d_volSpike, _] = processTimeframe('D')
    [_, _, _, _, h4_volSpike, _] = processTimeframe('240')
    [_, _, _, _, h1_volSpike, _] = processTimeframe('60')
    [_, _, _, _, m30_volSpike, _] = processTimeframe('30')
    [_, _, _, _, m15_volSpike, _] = processTimeframe('15')
    [_, _, _, _, m5_volSpike, _] = processTimeframe('5')
    [_, _, _, _, m3_volSpike, _] = processTimeframe('3')
    [_, _, _, _, m2_volSpike, _] = processTimeframe('2')
    [_, _, _, _, m1_volSpike, _] = processTimeframe('1')

    // Calculate the average, excluding any 'na' values
    total_vol = 0.0
    count_vol = 0
    if not na(d_volSpike)
        total_vol += d_volSpike
        count_vol += 1
    if not na(h4_volSpike)
        total_vol += h4_volSpike
        count_vol += 1
    if not na(h1_volSpike)
        total_vol += h1_volSpike
        count_vol += 1
    if not na(m30_volSpike)
        total_vol += m30_volSpike
        count_vol += 1
    if not na(m15_volSpike)
        total_vol += m15_volSpike
        count_vol += 1
    if not na(m5_volSpike)
        total_vol += m5_volSpike
        count_vol += 1
    if not na(m3_volSpike)
        total_vol += m3_volSpike
        count_vol += 1
    if not na(m2_volSpike)
        total_vol += m2_volSpike
        count_vol += 1
    if not na(m1_volSpike)
        total_vol += m1_volSpike
        count_vol += 1

    count_vol > 0 ? total_vol / count_vol : 100.0  // Return 100 (neutral) if no valid values


// Function to calculate average volatility
calcAverageVolatility() =>
    [_, _, d_vol, _, _, _] = processTimeframe('D')
    [_, _, h4_vol, _, _, _] = processTimeframe('240')
    [_, _, h1_vol, _, _, _] = processTimeframe('60')
    [_, _, m30_vol, _, _, _] = processTimeframe('30')
    [_, _, m15_vol, _, _, _] = processTimeframe('15')
    [_, _, m5_vol, _, _, _] = processTimeframe('5')
    [_, _, m3_vol, _, _, _] = processTimeframe('3')
    [_, _, m2_vol, _, _, _] = processTimeframe('2')
    [_, _, m1_vol, _, _, _] = processTimeframe('1')

    // Calculate the average, excluding any 'na' values
    total_vty = 0.0
    count_vty = 0
    if not na(d_vol)
        total_vty += d_vol
        count_vty += 1
    if not na(h4_vol)
        total_vty += h4_vol
        count_vty += 1
    if not na(h1_vol)
        total_vty += h1_vol
        count_vty += 1
    if not na(m30_vol)
        total_vty += m30_vol
        count_vty += 1
    if not na(m15_vol)
        total_vty += m15_vol
        count_vty += 1
    if not na(m5_vol)
        total_vty += m5_vol
        count_vty += 1
    if not na(m3_vol)
        total_vty += m3_vol
        count_vty += 1
    if not na(m2_vol)
        total_vty += m2_vol
        count_vty += 1
    if not na(m1_vol)
        total_vty += m1_vol
        count_vty += 1

    count_vty > 0 ? total_vty / count_vty : 0.0  // Return 0 if no valid values


// --- (Rest of your existing code) ---

// Calculate average values for alerts (Add these lines)
avg_strength = calcAverageStrength()
avg_volume_spike_pct = calcAverageVolumeSpikePct()
avg_volatility = calcAverageVolatility()
avg_trend = avg_strength >= 0 ? 1 : -1


// User Agreement
user_agreement = input.string('I agree', title = '================USER AGREEMENT\n\n=================\n\n\n\n \n \n Trading involves significant risk, with potential losses exceeding your initial investment. This indicator is intended for educational purposes only and does not guarantee any results. By typing \'I agree,\' you confirm that all trading decisions and outcomes are made at your own risk, and you accept full responsibility. The creator is not liable for any financial losses or damages.\n\nThis tool offers extensive customization options, including multiple modes and settings. Explore the indicator settings to tailor it to your trading preferences===========================================\n\n TYPE \'I agree\' TO ADD TO CHART.', confirm = true, display = display.none)
//.............................................................
// Check Agreement
if user_agreement != 'I agree'
    label.new(bar_index, na, ' ', style = label.style_label_down, color = color.red, textcolor = color.white)
    // Define strength arrays if they don't exist already
    // Alert frequency toggle
    // Alert Settings Group
 

 
// Calculate strength values
var array<float> m5_str = array.new_float(0)
var array<float> m15_str = array.new_float(0)
var array<float> h1_str = array.new_float(0)

float m5_strength_value = calcDirectionalStrengthValue('5')
float m15_strength_value = calcDirectionalStrengthValue('15')
float h1_strength_value = calcDirectionalStrengthValue('60')

array.push(m5_str, m5_strength_value)
array.push(m15_str, m15_strength_value)
array.push(h1_str, h1_strength_value)

if array.size(m5_str) > 100
    array.shift(m5_str)
if array.size(m15_str) > 100
    array.shift(m15_str)
if array.size(h1_str) > 100
    array.shift(h1_str)

// Access the strength values - declare as var to make them globally accessible
var float m5_strength = 0.0
var float m15_strength = 0.0
var float h1_strength = 0.0



// Calculate average strength
strength_measure = (m5_strength + m15_strength + h1_strength) / 3

// Calculate volume ratio
volRatio = volume / ta.sma(volume, 20) * 100

// Calculate volatility (ATR as percentage of price)
atr_value1 = ta.atr(atr_len)
volatilityRatio = atr_value1 / close * 100 * 10 // Multiply by 10 to make it more readable



// Add this to your existing code

    // Get weights array first
weights_array = getPresetWeights()

    // Then extract individual elements
adx_weight = array.get(weights_array, 0)
momentum_weight = array.get(weights_array, 1)
volume_weight = array.get(weights_array, 2)
volatility_weight = array.get(weights_array, 3)
ml_weight = array.get(weights_array, 4)
confidence_thresh = array.get(weights_array, 5)

    // Get component values (ensure these variables exist in your code)
adx_score = adx_value / 100  // Normalize ADX
momentum_score = math.abs(price_velocity) / 2
volume_score = volume_ratio
volatility_score = atr_percent / 5
ml_strength = mlStrength
    // Get raw component values (you already have these calculated)
// Add near your struct_score calculation (~line 2230)


var float trend_dir = na

// In confidence calculation (~line 2062)
//adx_contrib = (adx_score * (bull_confidence - bear_confidence)) * adx_weight
//mom_contrib = momentum_score * momentum_weight * trend_dir
//vol_contrib = volume_score * volume_weight * (bull_confidence > bear_confidence ? 1 : -1)
//v_contrib = volatility_score * volatility_weight * trend_dir
//ml_contrib = ml_strength * ml_weight * (bull_confidence - bear_confidence)
//total_confidence = (adx_contrib + mom_contrib + vol_contrib + v_contrib + ml_contrib) * 100
// 1. PLACE THE FUNCTION DEFINITION HERE - after your inputs but before calculations
// It should be placed with your other functions, before any variables or calculations that use it
// Add the generateSignal function if it doesn't exist already
// Add these inputs near your other strategy settings
 

// Make sure these are defined globally or passed as arguments if needed:
// var float volume_ratio = ... // Define how volume_ratio is calculated globally
// var float daily_perf = ... // Define how daily_perf is calculated globally
 

// -- Global Calculations Needed --
// Calculate volume ratio (example)


// -- Core Market Structure Function --
calcMarketStructure() =>
    // Define lengths (or use global inputs)
    int localDiLen = 14       // Or use global diLen
    int localAdxSmoothLen = 14 // Or use global adxSmoothLen

    // 1. Calculate DMI/ADX FIRST on current timeframe
    [diPlus, diMinus, adx] = ta.dmi(14, 14)

    // 2. Get higher timeframe data with PROPER security call
    [close_tf, high_tf, low_tf, volume_tf, adx_tf, diPlus_tf, diMinus_tf] = request.security(        syminfo.tickerid,        timeframe.period,        [close, high, low, volume, adx, diPlus, diMinus]    )

    // 3. Calculate remaining indicators using HTF data
    rsi_value = ta.rsi(close_tf, 14)
    roc_value = ta.roc(close_tf, 5)
    sma20 = ta.sma(close_tf, 20)
    sma50 = ta.sma(close_tf, 50)

    // 4. Calculate momentum state for market structure analysis
    [roc_short_ms, roc_long_ms, momentum_state_ms, acceleration_score_ms] = calcMomentumState(close_tf, 5, 15)
    atr_value = ta.atr(14)
 
    // 1. Handle potential NaN values for ADX
    float safe_adx_tf = nz(adx_tf, 0.0) // Replace NaN with 0
    float adx_norm = safe_adx_tf / 100  // Normalize ADX to 0-1 scale

    // 4. Calculate ATR using *current* timeframe data (ATR is often kept on the execution timeframe)
    // Or use request.security if you need higher timeframe ATR: atr_tf = request.security(..., ta.atr(14))
    float atr_factor = na(close_tf) or na(atr_value) or close_tf == 0 ? 1.0 : math.min(1.5, math.max(0.7, (atr_value / close_tf) * 100)) // Added NaN/zero check

    // --- Market Structure Components ---

    // 5. Volume Normalization (requires global 'volume_ratio')
    // Ensure 'volume_ratio' is calculated and available globally
    bool price_rising = close_tf > close_tf[5]
    float vol_norm = volume_ratio < 0.8 ?          -0.05 :         price_rising ? math.min(0.5, (volume_ratio - 0.8) * 0.3) :          math.max(-0.3, -(volume_ratio - 0.8) * 0.2)

    // 6. Daily Performance Scaling (requires global 'daily_perf')
    // Ensure 'daily_perf' is calculated and available globally
    float daily_scaled = daily_perf == 0 ? 0 : math.min(0.5, math.max(-0.5, daily_perf / 100))

    // 7. Swing Point Analysis using higher timeframe data
    float tf_mins = timeframe.in_seconds() / 60
    bool is_low_tf = tf_mins <= 15
    int swing_bars = is_low_tf ? 3 : 5

    float swingHigh = ta.pivothigh(high_tf, swing_bars, swing_bars)
    float swingLow = ta.pivotlow(low_tf, swing_bars, swing_bars)

    bool validSwingHigh = not na(swingHigh) and close_tf < close_tf[1]
    bool validSwingLow = not na(swingLow) and close_tf > close_tf[1]

    // Use volume_tf (higher timeframe volume) for swing validation
    float sma20_volume_tf = ta.sma(volume_tf, 20) // SMA of higher timeframe volume
    float vol_mult = 0.5
    bool higherHigh = validSwingHigh and swingHigh > ta.valuewhen(validSwingHigh, swingHigh, 1) and volume_tf > sma20_volume_tf * vol_mult
    bool lowerLow = validSwingLow and swingLow < ta.valuewhen(validSwingLow, swingLow, 1) and volume_tf > sma20_volume_tf * vol_mult

    // --- Scoring Logic ---

    // 8. Base conditions and score
    bool bull_condition_base = rsi_value > 50 and roc_value > -0.1 and sma20 >= sma50
    bool bear_condition_base = rsi_value < 50 and roc_value < 0.1 and sma20 < sma50

    float base_score = adx_norm * 0.5 * (bull_condition_base ? 1 : -1) + vol_norm + daily_scaled

    // 9. Swing score impact
    float swing_score = higherHigh ? 0.2 : lowerLow ? -0.2 : (bull_condition_base ? 0.1 : bear_condition_base ? -0.1 : 0)

    // 10. Raw score calculation
    raw_score = base_score + (swing_score * atr_factor)
    score = math.min(1, math.max(-1, raw_score)) * 100 // Clamp to -100 to 100

    // --- Score Adjustments & Validation ---
    float final_score = score

    // MA Alignment
    if sma20 > sma50
        final_score += 10
    else if sma20 < sma50
        final_score -= 10

    // RSI Extremes/Health
    bool extreme_bullish_rsi = rsi_value > 75
    bool extreme_bearish_rsi = rsi_value < 25
    bool healthy_bullish_rsi = rsi_value >= 55 and rsi_value <= 70
    bool healthy_bearish_rsi = rsi_value >= 30 and rsi_value <= 45

    if extreme_bullish_rsi
        final_score -= 5
    else if extreme_bearish_rsi
        final_score += 5
    else if healthy_bullish_rsi
        final_score += 5
    else if healthy_bearish_rsi
        final_score -= 5

    // Volume Confirmation (using volume_tf)
    bool volume_ok = volume_tf > sma20_volume_tf * 0.7 // Use higher timeframe volume
    if volume_ok and price_rising
        final_score += 5
    else if volume_ok and not price_rising
        final_score -= 5

    // RSI > 50 / < 50
    if rsi_value > 50
        final_score += 10
    else
        final_score -= 10

    // ROC Momentum
    if roc_value > 0.2
        final_score += 10
    else if roc_value > 0
        final_score += 5
    else if roc_value < -0.2
        final_score -= 10
    else if roc_value < 0
        final_score -= 5

    // Clamp final score again after adjustments
    final_score := math.min(100, math.max(-100, final_score))

    // --- Smoothing ---
    var float smoothed_score = 0.0 // Initialize with 0.0 instead of na for smoother start
    // Manual EMA calculation
    float alpha = 2.0 / (2 + 1) // EMA alpha for period 2
    smoothed_score := alpha * final_score + (1 - alpha) * nz(smoothed_score[1], final_score) // Use final_score as initial value if smoothed_score[1] is na

    // Ensure smoothed score stays within bounds
    smoothed_score := math.min(100, math.max(-100, smoothed_score))

    // --- Final Output Condition ---
    // Bull condition based on the smoothed score crossing zero
    bool final_bull_condition = smoothed_score >= 0

    // Return all relevant calculated values including momentum state
    [smoothed_score, final_bull_condition, adx_norm, vol_norm, daily_scaled, swing_score, atr_factor, rsi_value, sma20, sma50, roc_value, roc_short_ms, roc_long_ms, momentum_state_ms, acceleration_score_ms, close_tf]



[struct_score, struct_bull, adx_n, vol_n, daily_scale, swing_sc, atr_fact, struct_rsi, sma20_tf, sma50_tf, struct_roc, struct_roc_short, struct_roc_long, struct_momentum_state, struct_acceleration_score, close_tf_ms] = calcMarketStructure()
trend_dir := struct_bull ? 1 : -1  // Use your existing struct_bull boolean from calcMarketStructure
// === REWRITE generateSignal ===
generateSignal(int trend, float strength,
               float bull_conf=na, float bear_conf=na,
               float struct_score=na, string market_phase=na,
               float volume_pct=na, float volatility=na,
               float rsi=na, float roc=na) =>
    int transp = 0
    color sigColor = color.new(color.gray, transp)
    string sigText = "NEUTRAL"
    float sigStrength = 0.0
    float sigQuality = 0.0
    bool adv = not na(bull_conf) and not na(bear_conf) and not na(struct_score) and not na(market_phase) and not na(volume_pct) and not na(volatility) and not na(rsi) and not na(roc)
    if not adv
        // BASIC MODE: strength thresholds 40/60/80 on 0–100 scale
        float s = math.min(math.max(strength, 0), 100)
        sigStrength := s * trend
        if trend == 1
            if s >= 80
                sigColor := color.new(color.green, transp)
                sigText := "STRONG BUY"
                sigQuality := 80
            else if s >= 60
                sigColor := color.new(color.lime, transp)
                sigText := "BUY"
                sigQuality := 60
            else if s >= 40
                sigColor := color.new(#90EE90, transp)
                sigText := "WEAK BUY"
                sigQuality := 40
        else if trend == -1
            if s >= 80
                sigColor := color.new(color.red, transp)
                sigText := "STRONG SELL"
                sigQuality := -80
            else if s >= 60
                sigColor := color.new(color.orange, transp)
                sigText := "SELL"
                sigQuality := -60
            else if s >= 40
                sigColor := color.new(#FFA07A, transp)
                sigText := "WEAK SELL"
                sigQuality := -40
    else
        float bc = nz(bull_conf, 0), be = nz(bear_conf, 0)
        float sc = math.min(math.max(nz(struct_score, 0), -100), 100)
        float vp = math.min(math.max(nz(volume_pct, 0), 0), 200)
        float rn = math.min(math.max(nz(rsi, 0), 0), 100)
        float baseC = trend == 1 ? bc : trend == -1 ? be : 0
        float raw = baseC*0.3 + sc*0.3 + vp*0.2 + rn*0.2
        raw := math.min(math.max(raw, -100), 100)
        sigQuality := trend == 1 ? raw : trend == -1 ? -raw : 0
        sigStrength := sigQuality
        if sigQuality >= 75
            sigColor := color.new(color.green, transp)
            sigText := "STRONG BUY"
        else if sigQuality >= 60
            sigColor := color.new(color.lime, transp)
            sigText := "BUY"
            sigQuality := 60
        else if sigQuality >= 45
            sigColor := color.new(#90EE90, transp)
            sigText := "WEAK BUY"
            sigQuality := 40
        else if sigQuality <= -75
            sigColor := color.new(color.red, transp)
            sigText := "STRONG SELL"
            sigQuality := -80
        else if sigQuality <= -60
            sigColor := color.new(color.orange, transp)
            sigText := "SELL"
            sigQuality := -60
        else if sigQuality <= -45
            sigColor := color.new(#FFA07A, transp)
            sigText := "WEAK SELL"
            sigQuality := -40
    // Make sure quality is always positive
    sigQuality := math.abs(sigQuality)
    [sigColor, sigText, sigStrength, sigQuality]
// === END generateSignal OVERHAUL ===






// Right side - NightHawk Performance   // Right side - NightHawk Performance
// --- Base Set: Panel3 (MTF Panel) ---
display_metrics(row, tfLabel, trend, strength_param, volOrig, avgVol, volSpikePct) =>
    textSize = mtf_panel__size == 'Tiny' ? size.tiny : mtf_panel__size == 'Small' ? size.small : mtf_panel__size == 'Normal' ? size.normal : mtf_panel__size == 'Large' ? size.large : size.huge

    // Unified background color with darker tone
    bg_color = color.new(#1A1A1A, 95)
    text_color = color.new(color.gray, 40)

    table.cell(panel3, 0, row, tfLabel, text_color = text_color, text_size = textSize, bgcolor = bg_color)

    // Muted trend colors
    trend_str = trend > 0 ? '▲ Bull' : '▼ Bear'
    trend_color = trend > 0 ? color.new(#2D5A27, 90) : color.new(#5A272D, 90) // Dark muted green/red
    table.cell(panel3, 1, row, trend_str, text_color = trend > 0 ? color.new(#4CAF50, 60) : color.new(#FF5252, 60), bgcolor = trend_color, text_size = textSize) // Semi-transparent colors bgcolor=trend_color,  text_size=textSize)

    // Subdued strength colors
    strength_label = str.tostring(strength_param) + '% ' + (strength_param >= 80 ? '🔥 XTREME' : strength_param >= 50 ? '💪 STRONG' : strength_param >= 20 ? '⚡ BULL' : strength_param > 0 ? '🔼 MILD' : strength_param <= -80 ? '🔥 XTREME' : strength_param <= -50 ? '💪 STRONG' : strength_param <= -20 ? '⚡ BEAR' : strength_param < 0 ? '🔽 MILD' : '➖ NEUTRAL')

    strength_color = strength_param >= 80 ? color.new(#FF8C00, 85) : strength_param >= 50 ? color.new(#32CD32, 85) : strength_param >= 20 ? color.new(#228B22, 85) : strength_param > 0 ? color.new(#006400, 85) : strength_param <= -80 ? color.new(#FF4500, 85) : strength_param <= -50 ? color.new(#8B0000, 85) : strength_param <= -20 ? color.new(#B22222, 85) : strength_param < 0 ? color.new(#CD5C5C, 85) : color.new(#696969, 85)

    table.cell(panel3, 2, row, strength_label, text_color = color.new(color.white, 30), bgcolor = strength_color, text_size = textSize)

    // Revised volume color scale
    vol_color_orig = volOrig >= 70 ? color.new(#006400, 85) : volOrig >= 50 ? color.new(#90EE90, 85) : volOrig >= 30 ? color.new(#FFFF00, 85) : volOrig >= 10 ? color.new(#808080, 85) : color.new(#FF0000, 85)

    table.cell(panel3, 3, row, str.tostring(volOrig) + '%', text_color = color.new(color.white, 30), bgcolor = vol_color_orig, text_size = textSize)

    // Volume spike colors
    vol_stats_color = volSpikePct >= 200 ? color.new(#006400, 85) : volSpikePct >= 150 ? color.new(#90EE90, 85) : volSpikePct >= 120 ? color.new(#FFFF00, 85) : volSpikePct >= 100 ? color.new(#808080, 85) : color.new(#FF0000, 85)

    vol_stats = str.tostring(math.round(volSpikePct, 0)) + '%'

    table.cell(panel3, 0, row, tfLabel, text_color = text_color, text_size = textSize, bgcolor = bg_color)


    table.cell(panel3, 1, row, trend_str, text_color = trend > 0 ? color.new(#4CAF50, 60) : color.new(#FF5252, 60), bgcolor = trend_color, text_size = textSize) // Semi-transparent colors bgcolor=trend_color,  text_size=textSize)

    table.cell(panel3, 2, row, strength_label, text_color = color.new(color.white, 30), bgcolor = strength_color, text_size = textSize)

    table.cell(panel3, 3, row, str.tostring(volOrig) + '%', text_color = color.new(color.white, 30), bgcolor = vol_color_orig, text_size = textSize)
    table.cell(panel3, 4, row, vol_stats, text_color = color.new(color.white, 30), bgcolor = vol_stats_color, text_size = textSize)



if show_mtf_panel
    if na(panel3)
        panel3 := table.new(position = get_position(mtf_panel_position), columns = 13, rows = 24, bgcolor = color.new(#000000, 90), border_width = 1, border_color = color.gray) // Increased columns to 13
        panel3
    table.clear(panel3, start_row = 1, start_column = 0)
    table.cell(panel3, 0, 0, 'TF', text_color = color.white, bgcolor = color.new(#333333, 70), text_size = size.small)
    table.cell(panel3, 1, 0, 'Trend', text_color = color.white, bgcolor = color.new(#333333, 70), text_size = size.small)
    table.cell(panel3, 2, 0, 'Strength', text_color = color.white, bgcolor = color.new(#333333, 70), text_size = size.small)
    table.cell(panel3, 3, 0, 'Volatility', text_color = color.white, bgcolor = color.new(#333333, 70), text_size = size.small)
    table.cell(panel3, 4, 0, 'Volume', text_color = color.white, bgcolor = color.new(#333333, 70), text_size = size.small)
    table.cell(panel3, 5, 0, 'Basic Sig', text_color = color.white, bgcolor = color.new(#333333, 70), text_size = size.small) // New column header
    table.cell(panel3, 6, 0, 'Adv Signal', text_color = color.white, bgcolor = color.new(#333333, 70), text_size = size.small) // Renamed existing header
 
    // Calculate all timeframe values first - receiving trend_direction now
    [m1_trend_dir, m1_signed_str, m1_vol, m1_avgVol, m1_volSpike, m1_debug] = processTimeframe('1')
    [m2_trend_dir, m2_signed_str, m2_vol, m2_avgVol, m2_volSpike, m2_debug] = processTimeframe('2')
    [m3_trend_dir, m3_signed_str, m3_vol, m3_avgVol, m3_volSpike, m3_debug] = processTimeframe('3')
    [m5_trend_dir, m5_signed_str, m5_vol, m5_avgVol, m5_volSpike, m5_debug] = processTimeframe('5')
    [m15_trend_dir, m15_signed_str, m15_vol, m15_avgVol, m15_volSpike, m15_debug] = processTimeframe('15')
    [m30_trend_dir, m30_signed_str, m30_vol, m30_avgVol, m30_volSpike, m30_debug] = processTimeframe('30')
    [h1_trend_dir, h1_signed_str, h1_vol, h1_avgVol, h1_volSpike, h1_debug] = processTimeframe('60')
    [h4_trend_dir, h4_signed_str, h4_vol, h4_avgVol, h4_volSpike, h4_debug] = processTimeframe('240')
    [d_trend_dir, d_signed_str, d_vol, d_avgVol, d_volSpike, d_debug] = processTimeframe('D')
 
    m5_strength := array.size(m5_str) > 0 ? array.get(m5_str, array.size(m5_str) - 1) : 0
    m15_strength := array.size(m15_str) > 0 ? array.get(m15_str, array.size(m15_str) - 1) : 0
    h1_strength := array.size(h1_str) > 0 ? array.get(h1_str, array.size(h1_str) - 1) : 0

    // Calculate simple average strength (properly preserving sign)
    // Assuming calcAverageStrength() correctly calculates the signed average using the signed strength from processTimeframe
    avg_strength = calcAverageStrength() // This function needs adjustment later if it was relying on the old structure
    avg_volume_spike_pct = calcAverageVolumeSpikePct() // Needs adjustment later
    avg_volatility = calcAverageVolatility() // Needs adjustment later

    // Determine average trend direction based on average signed strength
    avg_trend_dir = avg_strength == 0 ? 0 : math.sign(avg_strength)
    avg_volatility_pct = nz(avg_volatility) // Ensure no NA

    // --- Calculate Basic and Advanced Signals for AVG Row ---
    [basic_signal_color_avg, basic_signal_text_avg, _, _] = generateSignal(        int(avg_trend_dir),        math.abs(avg_strength)    )
    [adv_signal_color_avg, adv_signal_text_avg, _, _] = generateSignal(         int(avg_trend_dir),          avg_strength,         bull_confidence,        bear_confidence,         avg_strength,          market_state,      avg_volume_spike_pct,         avg_volatility_pct,         struct_rsi,         struct_roc     )

    // Display metrics with AVG first (row 1), followed by individual timeframes
    textSize = mtf_panel__size == 'Tiny' ? size.tiny : mtf_panel__size == 'Small' ? size.small : mtf_panel__size == 'Normal' ? size.normal : mtf_panel__size == 'Large' ? size.large : size.huge

    // For AVG row - special highlighting
    avg_bg_color = color.new(#1A1A1A, 85)
    avg_text_color = color.new(color.white, 0)

    // Row 1: AVG
    table.cell(panel3, 0, 1, 'AVG', text_color = avg_text_color, text_size = textSize, bgcolor = avg_bg_color)
    // Use avg_trend_dir for display logic
    avg_trend_str = avg_trend_dir > 0 ? '▲ Bull' : avg_trend_dir < 0 ? '▼ Bear' : '➖ Neut'
    avg_trend_color = avg_trend_dir > 0 ? color.new(#2D5A27, 80) : avg_trend_dir < 0 ? color.new(#5A272D, 80) : color.new(#444444, 80)
    table.cell(panel3, 1, 1, avg_trend_str, text_color = avg_trend_dir > 0 ? color.new(#4CAF50, 0) : avg_trend_dir < 0 ? color.new(#FF5252, 0) : color.new(color.gray, 0), bgcolor = avg_trend_color, text_size = textSize)
    avg_strength_label = str.tostring(math.round(avg_strength, 1)) + '% ' + (avg_strength >= 80 ? '🔥 XTREME' : avg_strength >= 50 ? '💪 STRONG' : avg_strength >= 20 ? '⚡ BULL' : avg_strength > 0 ? '🔼 MILD' : avg_strength <= -80 ? '🔥 XTREME' : avg_strength <= -50 ? '💪 STRONG' : avg_strength <= -20 ? '⚡ BEAR' : avg_strength < 0 ? '🔽 MILD' : '➖ NEUTRAL')
    avg_strength_color = avg_strength >= 80 ? color.new(#FF8C00, 80) : avg_strength >= 50 ? color.new(#32CD32, 80) : avg_strength >= 20 ? color.new(#228B22, 80) : avg_strength > 0 ? color.new(#006400, 80) : avg_strength <= -80 ? color.new(#FF4500, 80) : avg_strength <= -50 ? color.new(#8B0000, 80) : avg_strength <= -20 ? color.new(#B22222, 80) : avg_strength < 0 ? color.new(#CD5C5C, 80) : color.new(#696969, 80)
    table.cell(panel3, 2, 1, avg_strength_label, text_color = color.new(color.white, 0), bgcolor = avg_strength_color, text_size = textSize)
    vol_color_avg = avg_volatility_pct >= 70 ? color.new(#006400, 80) : avg_volatility_pct >= 50 ? color.new(#90EE90, 80) : avg_volatility_pct >= 30 ? color.new(#FFFF00, 80) : avg_volatility_pct >= 10 ? color.new(#808080, 80) : color.new(#FF0000, 80)
    table.cell(panel3, 3, 1, str.tostring(math.round(avg_volatility_pct, 1)) + '%', text_color = color.new(color.white, 0), bgcolor = vol_color_avg, text_size = textSize)
    vol_stats_color_avg = avg_volume_spike_pct >= 200 ? color.new(#006400, 80) : avg_volume_spike_pct >= 150 ? color.new(#90EE90, 80) : avg_volume_spike_pct >= 120 ? color.new(#FFFF00, 80) : avg_volume_spike_pct >= 100 ? color.new(#808080, 80) : color.new(#FF0000, 80)
    table.cell(panel3, 4, 1, str.tostring(math.round(avg_volume_spike_pct, 0)) + '%', text_color = color.new(color.white, 0), bgcolor = vol_stats_color_avg, text_size = textSize)

    // Signal columns for AVG
    table.cell(panel3, 5, 1, basic_signal_text_avg, text_color = color.white, bgcolor = basic_signal_color_avg, text_size = textSize) // Basic Signal Col 5
    table.cell(panel3, 6, 1, adv_signal_text_avg, text_color = color.white, bgcolor = adv_signal_color_avg, text_size = textSize)   // Adv Signal Col 6
    // Add Debug Columns for AVG
  //  table.cell(panel3, 7, 1, str.tostring(math.round(avg_trend_dir)), text_color = color.gray, bgcolor = avg_bg_color, text_size = textSize) // Tnd Raw now shows direction
   // table.cell(panel3, 8, 1, str.tostring(math.round(math.abs(avg_strength),1)), text_color = color.gray, bgcolor = avg_bg_color, text_size = textSize) // Str Abs
 //   table.cell(panel3, 9, 1, str.tostring(math.round(avg_strength,1)), text_color = color.gray, bgcolor = avg_bg_color, text_size = textSize) // Str Sign
 //   table.cell(panel3, 10, 1, str.tostring(bull_confidence), text_color = color.gray, bgcolor = avg_bg_color, text_size = textSize)
 //   table.cell(panel3, 11, 1, str.tostring(bear_confidence), text_color = color.gray, bgcolor = avg_bg_color, text_size = textSize)
 //   table.cell(panel3, 12, 1, market_state, text_color = color.gray, bgcolor = avg_bg_color, text_size = textSize)



    // Now display individual timeframes using display_metrics and add signal cells
    // Row 2: 1M
    display_metrics(2, '1M', m1_trend_dir, m1_signed_str, m1_vol, m1_avgVol, m1_volSpike) // display_metrics fills cols 0-4
    [m1_basic_color, m1_basic_text, _, _] = generateSignal(m1_trend_dir, math.abs(m1_signed_str))
    [m1_adv_color, m1_adv_text, _, _] = generateSignal(m1_trend_dir, m1_signed_str, bull_confidence, bear_confidence, m1_signed_str, market_state, m1_volSpike, m1_vol, struct_rsi, struct_roc)
    table.cell(panel3, 5, 2, m1_basic_text, text_color = m1_basic_color, text_size = textSize) // Basic Sig Col 5
    table.cell(panel3, 6, 2, m1_adv_text, text_color = m1_adv_color, text_size = textSize)   // Adv Signal Col 6
    // Add Debug Columns for 1M
  //  table.cell(panel3, 7, 2, str.tostring(m1_trend_dir), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 8, 2, str.tostring(math.abs(m1_signed_str)), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 9, 2, str.tostring(m1_signed_str), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 10, 2, str.tostring(bull_confidence), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 11, 2, str.tostring(bear_confidence), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 12, 2, market_state, text_color = color.gray, text_size = textSize)

    // Row 3: 2M
    display_metrics(3, '2M', m2_trend_dir, m2_signed_str, m2_vol, m2_avgVol, m2_volSpike)
    [m2_basic_color, m2_basic_text, _, _] = generateSignal(m2_trend_dir, math.abs(m2_signed_str))
    [m2_adv_color, m2_adv_text, _, _] = generateSignal(m2_trend_dir, m2_signed_str, bull_confidence, bear_confidence, m2_signed_str, market_state, m2_volSpike, m2_vol, struct_rsi, struct_roc)
    table.cell(panel3, 5, 3, m2_basic_text, text_color = m2_basic_color, text_size = textSize) // Basic Sig Col 5
    table.cell(panel3, 6, 3, m2_adv_text, text_color = m2_adv_color, text_size = textSize)   // Adv Signal Col 6
    // Add Debug Columns for 2M
//    table.cell(panel3, 7, 3, str.tostring(m2_trend_dir), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 8, 3, str.tostring(math.abs(m2_signed_str)), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 9, 3, str.tostring(m2_signed_str), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 10, 3, str.tostring(bull_confidence), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 11, 3, str.tostring(bear_confidence), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 12, 3, market_state, text_color = color.gray, text_size = textSize)

    // Row 4: 3M
    display_metrics(4, '3M', m3_trend_dir, m3_signed_str, m3_vol, m3_avgVol, m3_volSpike)
    [m3_basic_color, m3_basic_text, _, _] = generateSignal(m3_trend_dir, math.abs(m3_signed_str))
    [m3_adv_color, m3_adv_text, _, _] = generateSignal(m3_trend_dir, m3_signed_str, bull_confidence, bear_confidence, m3_signed_str, market_state, m3_volSpike, m3_vol, struct_rsi, struct_roc)
    table.cell(panel3, 5, 4, m3_basic_text, text_color = m3_basic_color, text_size = textSize) // Basic Sig Col 5
    table.cell(panel3, 6, 4, m3_adv_text, text_color = m3_adv_color, text_size = textSize)   // Adv Signal Col 6
    // Add Debug Columns for 3M
  //  table.cell(panel3, 7, 4, str.tostring(m3_trend_dir), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 8, 4, str.tostring(math.abs(m3_signed_str)), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 9, 4, str.tostring(m3_signed_str), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 10, 4, str.tostring(bull_confidence), text_color = color.gray, text_size = textSize)
//    table.cell(panel3, 11, 4, str.tostring(bear_confidence), text_color = color.gray, text_size = textSize)
//    table.cell(panel3, 12, 5, market_state, text_color = color.gray, text_size = textSize)

    // Row 6: 15M
    display_metrics(6, '15M', m15_trend_dir, m15_signed_str, m15_vol, m15_avgVol, m15_volSpike)
    [m15_basic_color, m15_basic_text, _, _] = generateSignal(m15_trend_dir, math.abs(m15_signed_str))
    [m15_adv_color, m15_adv_text, _, _] = generateSignal(m15_trend_dir, m15_signed_str, bull_confidence, bear_confidence, m15_signed_str, market_state, m15_volSpike, m15_vol, struct_rsi, struct_roc)
    table.cell(panel3, 5, 6, m15_basic_text, text_color = m15_basic_color, text_size = textSize) // Basic Sig Col 5
    table.cell(panel3, 6, 6, m15_adv_text, text_color = m15_adv_color, text_size = textSize)   // Adv Signal Col 6
    // Add Debug Columns for 15M
 //   table.cell(panel3, 7, 6, str.tostring(m15_trend_dir), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 8, 6, str.tostring(math.abs(m15_signed_str)), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 9, 6, str.tostring(m15_signed_str), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 10, 6, str.tostring(bull_confidence), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 11, 6, str.tostring(bear_confidence), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 12, 6, market_state, text_color = color.gray, text_size = textSize)
 
    // Row 7: 30M
    display_metrics(7, '30M', m30_trend_dir, m30_signed_str, m30_vol, m30_avgVol, m30_volSpike)
    [m30_basic_color, m30_basic_text, _, _] = generateSignal(m30_trend_dir, math.abs(m30_signed_str))
    [m30_adv_color, m30_adv_text, _, _] = generateSignal(m30_trend_dir, m30_signed_str, bull_confidence, bear_confidence, m30_signed_str, market_state, m30_volSpike, m30_vol, struct_rsi, struct_roc)
    table.cell(panel3, 5, 7, m30_basic_text, text_color = m30_basic_color, text_size = textSize) // Basic Sig Col 5
    table.cell(panel3, 6, 7, m30_adv_text, text_color = m30_adv_color, text_size = textSize)   // Adv Signal Col 6
    // Add Debug Columns for 30M
//    table.cell(panel3, 7, 7, str.tostring(m30_trend_dir), text_color = color.gray, text_size = textSize)
//    table.cell(panel3, 8, 7, str.tostring(math.abs(m30_signed_str)), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 9, 7, str.tostring(m30_signed_str), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 10, 7, str.tostring(bull_confidence), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 11, 7, str.tostring(bear_confidence), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 12, 7, market_state, text_color = color.gray, text_size = textSize)

    // Row 8: 1H
    display_metrics(8, '1H', h1_trend_dir, h1_signed_str, h1_vol, h1_avgVol, h1_volSpike)
    [h1_basic_color, h1_basic_text, _, _] = generateSignal(h1_trend_dir, math.abs(h1_signed_str))
    [h1_adv_color, h1_adv_text, _, _] = generateSignal(h1_trend_dir, h1_signed_str, bull_confidence, bear_confidence, h1_signed_str, market_state, h1_volSpike, h1_vol, struct_rsi, struct_roc)
    table.cell(panel3, 5, 8, h1_basic_text, text_color = h1_basic_color, text_size = textSize) // Basic Sig Col 5
    table.cell(panel3, 6, 8, h1_adv_text, text_color = h1_adv_color, text_size = textSize)   // Adv Signal Col 6
    // Add Debug Columns for 1H
//    table.cell(panel3, 7, 8, str.tostring(h1_trend_dir), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 8, 8, str.tostring(math.abs(h1_signed_str)), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 9, 8, str.tostring(h1_signed_str), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 10, 8, str.tostring(bull_confidence), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 11, 8, str.tostring(bear_confidence), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 12, 8, market_state, text_color = color.gray, text_size = textSize)

    // Row 9: 4H
    display_metrics(9, '4H', h4_trend_dir, h4_signed_str, h4_vol, h4_avgVol, h4_volSpike)
    [h4_basic_color, h4_basic_text, _, _] = generateSignal(h4_trend_dir, math.abs(h4_signed_str))
    [h4_adv_color, h4_adv_text, _, _] = generateSignal(h4_trend_dir, h4_signed_str, bull_confidence, bear_confidence, h4_signed_str, market_state, h4_volSpike, h4_vol, struct_rsi, struct_roc)
    table.cell(panel3, 5, 9, h4_basic_text, text_color = h4_basic_color, text_size = textSize) // Basic Sig Col 5
    table.cell(panel3, 6, 9, h4_adv_text, text_color = h4_adv_color, text_size = textSize)   // Adv Signal Col 6
    // Add Debug Columns for 4H
 //   table.cell(panel3, 7, 9, str.tostring(h4_trend_dir), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 8, 9, str.tostring(math.abs(h4_signed_str)), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 9, 9, str.tostring(h4_signed_str), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 10, 9, str.tostring(bull_confidence), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 11, 9, str.tostring(bear_confidence), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 12, 9, market_state, text_color = color.gray, text_size = textSize)

    // Row 10: 1D
    display_metrics(10, '1D', d_trend_dir, d_signed_str, d_vol, d_avgVol, d_volSpike)
    [d_basic_color, d_basic_text, _, _] = generateSignal(d_trend_dir, math.abs(d_signed_str))
    [d_adv_color, d_adv_text, _, _] = generateSignal(d_trend_dir, d_signed_str, bull_confidence, bear_confidence, d_signed_str, market_state, d_volSpike, d_vol, struct_rsi, struct_roc)
    table.cell(panel3, 5, 10, d_basic_text, text_color = d_basic_color, text_size = textSize) // Basic Sig Col 5
    table.cell(panel3, 6, 10, d_adv_text, text_color = d_adv_color, text_size = textSize)   // Adv Signal Col 6
    // Add Debug Columns for 1D
   // table.cell(panel3, 7, 10, str.tostring(d_trend_dir), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 8, 10, str.tostring(math.abs(d_signed_str)), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 9, 10, str.tostring(d_signed_str), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 10, 10, str.tostring(bull_confidence), text_color = color.gray, text_size = textSize)
  //  table.cell(panel3, 11, 10, str.tostring(bear_confidence), text_color = color.gray, text_size = textSize)
 //   table.cell(panel3, 12, 10, market_state, text_color = color.gray, text_size = textSize)


else if not na(panel3)
    table.delete(panel3)
    panel3 := na
    panel3






// You can now use these variables: struct_score, struct_bull, etc.
// plot(struct_score, title="Market Structure Score")
// bgcolor(struct_bull ? color.new(color.green, 85) : color.new(color.red, 85))

//

if show_panel
    var table panel1 = table.new(position = get_position(panel_1_position), columns = 3, rows = 16, bgcolor = color.new(#000000, 90), border_width = 1, border_color = color.gray)
    panel1_size = panel1_size_input == 'Tiny' ? size.tiny : panel1_size_input == 'Small' ? size.small : panel1_size_input == 'Normal' ? size.normal : panel1_size_input == 'Large' ? size.large : size.huge
    header_bg = color.new(#1A1A1A, 20)
    section_bg = color.new(#2A2A2A, 30)
    label_bg = color.new(#444444, 50) // Define a background for labels

    // --- Row 0 & 1: Header ---
    table.cell(panel1, 0, 0, '🦅 Nighthawk Matrix', text_color = color.white, width = 3, text_size = panel1_size, bgcolor = header_bg)
    table.cell(panel1, 2, 0, '3.0 -NHX.ai-', text_color = color.white, width = 3, text_size = panel1_size, bgcolor = header_bg)

    // --- Calculate necessary values ---
    [bull_confidence, bear_confidence] = normalizeConfidence()
    string bull_text = "BULL " + str.tostring(math.round(bull_confidence, 1)) + "% " + getBullBattery(bull_confidence)
    string bear_text = "BEAR " + str.tostring(math.round(bear_confidence, 1)) + "% " + getBearBattery(bear_confidence)
    trend_state_color = str.contains(ml_state, 'Bull') ? color.new(#00FF00, 80) : color.new(#FF0000, 80)
    [struct_score, bull_condition, adx_n, vol_n, daily_scale, swing_sc, atr_fact, struct_rsi, sma20_tf, sma50_tf, struct_roc, struct_roc_short, struct_roc_long, struct_momentum_state, struct_acceleration_score, close_tf_ms] = calcMarketStructure()
    string structure_description = struct_score >= 70 ? "Extreme Bull" : struct_score >= 40 ? "Strong Bull" : struct_score >= 20 ? "Mod Bull" : struct_score >= 0 ? "Mild Bull" : struct_score >= -20 ? "Mild Bear" : struct_score >= -40 ? "Mod Bear" : struct_score >= -70 ? "Strong Bear" : "Extreme Bear"
    string structure_text_val = (struct_score >= 0 ? "+" : "") + str.tostring(math.round(struct_score, 1)) + ' (' + structure_description + ')'
    color structure_color_val = struct_score >= 0 ? color.green : color.red
    string trend_state = market_state
    color trend_state_bgcolor = market_state_color
    int bull_count = 0 // Recalculate bull count based on actual ST directions (dir1..7)
    bull_count := (dir1 == 1 ? 1 : 0) + (dir2 == 1 ? 1 : 0) + (dir3 == 1 ? 1 : 0) + (dir4 == 1 ? 1 : 0) + (dir5 == 1 ? 1 : 0) + (dir6 == 1 ? 1 : 0) + (dir7 == 1 ? 1 : 0)
    float direct_bull_percent = bull_count / 7.0 * 100
    float direct_bear_percent = 100 - direct_bull_percent
    string battery_text = direct_bull_percent >= direct_bear_percent ? "Bull " + str.tostring(math.round(direct_bull_percent)) + "% " + getBullBattery(direct_bull_percent) : "Bear " + str.tostring(math.round(direct_bear_percent)) + "% " + getBearBattery(direct_bear_percent)
    color battery_color = direct_bull_percent >= direct_bear_percent ? color.green : color.red
    float overall_confidence = bull_confidence - bear_confidence

    // Calculate AVG Entry Quality Score (use the AVG values as input)
    [_, _, _, avg_entry_quality] = generateSignal(       avg_trend,        avg_strength,         bull_confidence, bear_confidence,        struct_score, market_state,        avg_volume_spike_pct, avg_volatility,        struct_rsi, struct_roc)

    // Ensure quality is always positive for grading
    avg_entry_quality := avg_entry_quality

    // --- Column 0 ---
    rsi_desc = struct_rsi >= 70 ? "Overbought 🚨" :
           struct_rsi >= 60 ? "Strong Bullish 💪" :
           struct_rsi >= 50 ? "Mild Bullish 🟢" :
           struct_rsi > 40  ? "Mild Bearish 🔴" :
           struct_rsi > 30  ? "Strong Bearish 💪" :
           "Oversold 🚨"
    // Row 2: Label: RSI
    table.cell(panel1, 0, 2, "RSI", text_color = color.white, bgcolor = label_bg, text_size = panel1_size)
    // Row 3: Value: RSI - improved color scale
    color rsi_bg_color = struct_rsi >= 70 ? color.new(#8B0000, 80) :                         struct_rsi >= 60 ? color.new(#32CD32, 80) :                          struct_rsi >= 50 ? color.new(#90EE90, 80) :                         struct_rsi >= 40 ? color.new(#FFA07A, 80) :                          struct_rsi >= 30 ? color.new(#FF0000, 80) :                          color.new(#006400, 80)                      // Extreme oversold - dark green (potential reversal)

    table.cell(panel1, 0, 3, str.tostring(math.round(struct_rsi, 1)) + "  " + rsi_desc,        text_color = color.white,        bgcolor = rsi_bg_color,        text_size = panel1_size,        tooltip = "RSI(14): " + str.tostring(math.round(struct_rsi, 1)) + "\n" +                 (struct_rsi > 70 ? "Overbought - Potential reversal" :                  struct_rsi < 30 ? "Oversold - Potential reversal" :                  struct_rsi > 50 ? "Bullish momentum" : "Bearish momentum"))

    // Row 4: Label: Structure
    table.cell(panel1, 0, 4, 'Structure', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)
    // Row 5: Value: Structure
    color structure_bg_color = structure_color_val == color.green ? color.new(#006400, 80) :
                              structure_color_val == color.red ? color.new(#8B0000, 80) :
                              color.new(#808080, 80)

    table.cell(panel1, 0, 5, structure_text_val,         text_color = color.white,        bgcolor = structure_bg_color,        text_size = panel1_size)

    // Row 6: Label: Momentum State
    [roc_short_ms, roc_long_ms, momentum_state_ms, acceleration_score_ms] = calcMomentumState(close_tf_ms, 5, 15)  // Added source (close_tf) and both lengths

    table.cell(panel1, 0, 6, 'Momentum', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)
    // Row 7: Value: Momentum State
    acceleration_score = struct_acceleration_score


    // Row 7: Value: Momentum State
    color momentum_panel_color = struct_momentum_state == "ACCELERATING" ? color.new(color.green, 80) :
                               struct_momentum_state == "DECELERATING" ? color.new(color.red, 80) :
                               color.new(color.gray, 80)
    string momentum_panel_icon = struct_momentum_state == "ACCELERATING" ? "⚡ " :
                               struct_momentum_state == "DECELERATING" ? "🔻 " :
                               "⇄ "
    table.cell(panel1, 0, 7, momentum_panel_icon + struct_momentum_state + str.tostring(acceleration_score, '#'), text_color = color.white, bgcolor = momentum_panel_color, text_size = panel1_size)

   // Row 8: Label: Trend State
    table.cell(panel1, 0, 8, 'Trend State', text_color = color.white, bgcolor = section_bg, text_size = panel1_size)
    // Row 9: Value: Trend State
    table.cell(panel1, 0, 9, trend_state, text_color = color.white, bgcolor = trend_state_bgcolor, text_size = panel1_size)
    // Row 10: Label: ATR Factor

    // Row 11: Value: ATR Factor
     
// In Confidence Calculations section
    total_confidence =     (struct_score * 0.3) +        (vol_n * 0.2 * 100) +         (acceleration_score * 0.15) +      (mlStrength * 0.2 * 100) +     (mtf_bullish_count/7.0 * 0.15 * 100) // MTF Confluence

// Apply volatility scaling from market structure
    volatility_factor = math.min(1.5, math.max(0.7, atr_fact)) // atr_fact from calcMarketStructure()
    total_confidence := total_confidence * volatility_factor

// Softmax normalization
    total_confidence := 100 / (1 + math.exp(-0.1 * total_confidence)) 
 
// Final clamping
    total_confidence := math.min(100, math.max(0, total_confidence))
    // Row 10: Label: Total Confidence
    table.cell(panel1, 0, 10, 'Confidence Composite', text_color = color.white, bgcolor = label_bg, text_size = panel1_size ,             tooltip = "**Currently In Development**")
    // Define background color and descriptor based on total_confidence thresholds
    color total_confidence_bg = total_confidence >= 80 ? color.new(#006400, 80) :
                               total_confidence >= 60 ? color.new(#32CD32, 80) :
                               total_confidence >= 40 ? color.new(#FFD700, 80) :
                               total_confidence >= 20 ? color.new(#FFA500, 80) :
                               color.new(#8B0000, 80)
    string total_confidence_desc = total_confidence >= 80 ? "High" :
                                   total_confidence >= 60 ? "Good" :
                                   total_confidence >= 40 ? "Moderate" :
                                   total_confidence >= 20 ? "Low" :
                                   "Very Low"
    // Row 11: Styled Total Confidence value 
    table.cell(panel1, 0, 11, str.tostring(math.round(total_confidence)) + '% - ' + total_confidence_desc,
              text_color = color.white,
              bgcolor = total_confidence_bg,
              text_size = panel1_size,
              tooltip = "Overall confidence score combining momentum, entry quality, and trend")
 
    table.cell(panel1, 0, 11, str.tostring(total_confidence), text_color = color.white, text_size = panel1_size)
    // Row 12: Label: Entry Quality
    table.cell(panel1, 2, 10, 'Entry Quality', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)
    // Row 13: Value: Entry Quality Score with new styling - colored background with white text
    color entry_quality_bg = avg_entry_quality >= 80 ? color.new(#006400, 80) :  // Excellent - dark green
                           avg_entry_quality >= 70 ? color.new(#32CD32, 80) :  // Good - green
                           avg_entry_quality >= 50 ? color.new(#90EE90, 80) :  // Moderate - light green
                           avg_entry_quality >= 30 ? color.new(#FFA07A, 80) :  // Poor - light red
                           color.new(#8B0000, 80)                            // Very poor - dark red

    string quality_text = avg_entry_quality >= 80 ? " - Excellent" :
                         avg_entry_quality >= 70 ? " - Good" :
                         avg_entry_quality >= 50 ? " - Moderate" :
                         avg_entry_quality >= 30 ? " - Fair" :
                         " - Poor"

    table.cell(panel1, 2, 11, str.tostring(math.round(avg_entry_quality)) + '/100' + quality_text,
              text_color = color.white,
              bgcolor = entry_quality_bg,
              text_size = panel1_size,
              tooltip = "Entry quality score based on current market conditions")

    // --- Column 1 ---

    float roc_short_pct = roc_short_ms   // Convert to percentage for display
    float roc_long_pct = roc_long_ms    // Convert to percentage for display
    float roc_diff_pct = (roc_short_ms - roc_long_ms)    // Convert difference to percentage

    // Row 2: Label: ROC with momentum indicator
    string roc_header = 'ROC: ' + (roc_short_ms > roc_long_ms ? "↗️" : roc_short_ms < roc_long_ms ? "↘️" : "↔️")
    table.cell(panel1, 1, 2, roc_header, text_color = color.white, bgcolor = label_bg, text_size = panel1_size)
    roc_short = roc_short_ms
    roc_long = roc_long_ms
    // Row 3: Value: ROC(5) with description (dynamic thresholds)
    roc_desc = roc_short >= strong_thresh ? " 🚀 STRONG UP" :
               roc_short >= rising_thresh ? " 📈 RISING" :
               roc_short >= mild_thresh ? " ↗️ MILD UP" :
               roc_short <= -strong_thresh ? " 💥 STRONG DOWN" :
               roc_short <= -rising_thresh ? " 📉 FALLING" :
               roc_short <= -mild_thresh ? " ↘️ MILD DOWN" :
               " ↔️ FLAT"

    // Background color based on ROC value (dynamic thresholds)
    color roc_bg_color = roc_short >= strong_thresh ? color.new(#006400, 80) :
                         roc_short >= rising_thresh ? color.new(#32CD32, 80) :
                         roc_short >= mild_thresh ? color.new(#90EE90, 80) :
                         roc_short <= -strong_thresh ? color.new(#8B0000, 80) :
                         roc_short <= -rising_thresh ? color.new(#FF0000, 80) :
                         roc_short <= -mild_thresh ? color.new(#FFA07A, 80) :
                         color.new(#808080, 80)

    // Add momentum state indicator
    string momentum_indicator = momentum_state_ms == "ACCELERATING" ? " ⚡" :
                               momentum_state_ms == "DECELERATING" ? " 🔻" :
                               " ⇄"

    // Display ROC with description and momentum indicator
    table.cell(panel1, 1, 3, str.tostring(math.round(roc_short_pct, 2)) + '%' + roc_desc + momentum_indicator,
               text_color = color.white,
               bgcolor = roc_bg_color,
               text_size = panel1_size)

    // Add a row to display the longer-term ROC and the difference
    table.cell(panel1, 1, 4, 'ROC(15):', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)

    // Format the difference with a sign and determine color
    string diff_str = roc_diff_pct > 0 ? "+" + str.tostring(math.round(roc_diff_pct, 2)) : str.tostring(math.round(roc_diff_pct, 2))

    // Determine the background color based on the ROC value
    color roc15_bg_color = roc_long_pct >= strong_thresh ? color.new(#006400, 80) :  // Strong uptrend - dark green
                          roc_long_pct >= rising_thresh ? color.new(#32CD32, 80) :  // Moderate uptrend - green
                          roc_long_pct >= mild_thresh ? color.new(#90EE90, 80) :  // Slight uptrend - light green
                          roc_long_pct <= -rising_thresh ? color.new(#8B0000, 80) : // Strong downtrend - dark red
                          roc_long_pct <= -mild_thresh ? color.new(#FF0000, 80) : // Moderate downtrend - red
                          roc_long_pct <= -strong_thresh ? color.new(#FFA07A, 80) : // Slight downtrend - light red
                          color.new(#808080, 80)                         // Neutral - gray

    // Display longer-term ROC and the difference
    string long_roc_display = str.tostring(math.round(roc_long_pct, 2)) + "% (Δ: " + diff_str + "%)"
    table.cell(panel1, 1, 5, long_roc_display,
               text_color = color.white,
               bgcolor = roc15_bg_color,
               text_size = panel1_size)

    // Row 6: Label: ADX Norm
    table.cell(panel1, 1, 6, 'ADX', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)
    // Row 7: Value: ADX Norm
    table.cell(panel1, 1, 7, str.tostring(math.round(getADX(14), 2)), text_color = adx_n > 0.2 ? color.green : color.gray, text_size = panel1_size)
    // Row 8: Label: ML State
    table.cell(panel1, 1, 8, 'ML State', text_color = color.white, bgcolor = section_bg, text_size = panel1_size)
    // Row 9: Value: ML State
    table.cell(panel1, 1, 9, ml_state, text_color = str.contains(ml_state, 'Bull') ? color.green : color.red, bgcolor = trend_state_color, text_size = panel1_size)
    // Row 10: Label: Volume %
    table.cell(panel1, 1, 10, 'Volume %', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)
    // Row 11: Value: Volume %
    color volume_bg_color = overall_volume_percent >= 140 ? color.new(#006400, 80) :  // Very high volume - dark green
                           overall_volume_percent >= 110 ? color.new(#32CD32, 80) :  // Above average - green
                           overall_volume_percent >= 90 ? color.new(#808080, 80) :   // Average - gray
                           overall_volume_percent >= 70 ? color.new(#FFA07A, 80) :   // Below average - light red
                           color.new(#8B0000, 80)                                   // Very low volume - dark red

    table.cell(panel1, 1, 11, str.tostring(math.round(overall_volume_percent, 1)) + '%',
               text_color = color.white,
               bgcolor = volume_bg_color,
               text_size = panel1_size,
               tooltip = "Current volume compared to average: " + str.tostring(math.round(overall_volume_percent, 1)) + "%")

    // Row 2: Label: ST Battery
    table.cell(panel1, 2, 2, 'ST Battery', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)
    // Row 3: Value: ST Battery (restored original styling)
    table.cell(panel1, 2, 3, battery_text, text_color = battery_color, text_size = panel1_size)

    // Calculate TP hit percentages
    float tp1_pct = math.round(tp1_hits/math.max(tp1_attempts, 1) * 100, 0)
    float tp2_pct = math.round(tp2_hits/math.max(tp2_attempts, 1) * 100, 0)
    float tp3_pct = math.round(tp3_hits/math.max(tp3_attempts, 1) * 100, 0)

    // Create a single line with all percentages
    string tp_hit_rates = str.tostring(tp1_pct) + "% / " + str.tostring(tp2_pct) + "% / " + str.tostring(tp3_pct) + "%"

    // Row 4: Label: TP Hit Rates
    table.cell(panel1, 2, 4, 'TP Hit Rates', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)

    // Row 5: Value: TP Hit Rates with TP1/TP2/TP3 percentages
    table.cell(panel1, 2, 5, tp_hit_rates,
          text_color = (tp1_pct >= 50 or tp2_pct >= 40 or tp3_pct >= 30) ? color.green :
                     (tp1_pct >= 30 or tp2_pct >= 20 or tp3_pct >= 15) ? color.orange : color.gray,
          text_size = panel1_size,
          tooltip = "TP1: " + str.tostring(tp1_hits) + "/" + str.tostring(tp1_attempts) + " (" + str.tostring(tp1_pct) + "%)\n" +
                   "TP2: " + str.tostring(tp2_hits) + "/" + str.tostring(tp2_attempts) + " (" + str.tostring(tp2_pct) + "%)\n" +
                   "TP3: " + str.tostring(tp3_hits) + "/" + str.tostring(tp3_attempts) + " (" + str.tostring(tp3_pct) + "%)")

    // Row 6: Label: Bull Conf (restore original)
    table.cell(panel1, 2, 6, 'Bull Conf', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)

    // Row 7: Value: Bull Conf (restore original)
    table.cell(panel1, 2, 7, bull_text, text_color = bull_confidence >= 60 ? color.green : bull_confidence >= 40 ? color.orange : color.gray, text_size = panel1_size)

    // Row 8: Label: Bear Conf (restore original)
    table.cell(panel1, 2, 8, 'Bear Conf', text_color = color.white, bgcolor = label_bg, text_size = panel1_size)

    // Row 9: Value: Bear Conf (restore original)
    table.cell(panel1, 2, 9, bear_text, text_color = bear_confidence >= 60 ? color.red : bear_confidence >= 40 ? color.orange : color.gray, text_size = panel1_size)

    // Row 6: Label: ADX Strength
    table.cell(panel1, 1, 6, '📈 ADX Strength', text_color = color.white, bgcolor = label_bg, text_size = panel1_size,
         tooltip = "Trend Strength Indicator\n0-25: Weak/Choppy\n25-50: Strong Trend\n50+: Extreme Trend")

    // Row 7: Value with contextual coloring
    adx_value = math.round(getADX(14), 0)  // Get actual ADX value (0-100 scale)
    adx_strength =
         adx_value >= 50 ? "🔥 Extreme" :
         adx_value >= 40 ? "💪 Very Strong" :
         adx_value >= 25 ? "📈 Strong Trend" :
         adx_value >= 20 ? "🌊 Developing" :
         "🌫️ Choppy"

    adx_color =
         adx_value >= 25 ? color.green :
         adx_value >= 20 ? color.orange :
         color.red

    table.cell(panel1, 1, 7, str.tostring(adx_value) + " - " + adx_strength,
         text_color = color.white,
         bgcolor = color.new(adx_color, 80),  // Semi-transparent version of trend color
         text_size = panel1_size,
         tooltip = str.format("ADX: {0}\n{1}", adx_value,
              adx_value >= 50 ? "Extreme trending market - high probability continuation" :
              adx_value >= 40 ? "Very strong directional movement" :
              adx_value >= 25 ? "Valid trending market conditions" :
              adx_value >= 20 ? "Potential trend development - watch for confirmation" :
              "Choppy/Ranging market - consider mean reversion strategies"))
    
else if not na(panel1) // If panel exists but shouldn't be shown
    table.delete(panel1)
    panel1 := na // Reset variable

  
 

// Add momentum conditions to base signals
// Use the momentum state from market structure analysis for consistency
// This ensures we're using the same momentum calculation throughout the code
string momentum_state_entry = na(struct_momentum_state) ? "STEADY" : struct_momentum_state
float acceleration_score_entry = na(struct_acceleration_score) ? 0.0 : struct_acceleration_score

// For long entries: prefer accelerating momentum or at least not strongly decelerating
momentum_long_condition = momentum_state_entry == "ACCELERATING" or
                         (momentum_state_entry == "STEADY") or
                         (momentum_state_entry == "DECELERATING" and acceleration_score_entry > -50)

// For short entries: prefer decelerating momentum or at least not strongly accelerating
momentum_short_condition = momentum_state_entry == "DECELERATING" or
                          (momentum_state_entry == "STEADY") or
                          (momentum_state_entry == "ACCELERATING" and acceleration_score_entry < 50)



// Generate Trading Signals with momentum analysis
longCondition := base_longCondition and barstate.isconfirmed // Include momentum analysis
shortCondition := base_shortCondition and barstate.isconfirmed  // Include momentum analysis

// Plot signal shapes with different visuals based on trading hours
plotshape(longCondition and is_trading_time(), title = 'Long Signal', style = shape.labelup, location = location.belowbar, color = color.green, size = size.normal, text = 'Long', textcolor = color.white)

plotshape(shortCondition and is_trading_time(), 'Short Signal', style = shape.labeldown, location = location.abovebar, color = color.red, size = size.normal, text = 'Short', textcolor = color.white)

// Plot pre-market signals
plotshape(longCondition and not is_trading_time(), title = 'Pre-Market Long', style = shape.labelup, location = location.belowbar, color = color.new(color.black, 50), size = size.small, text = 'Pre-Long', textcolor = color.new(color.green, 20))

plotshape(shortCondition and not is_trading_time(), 'Pre-Market Short', style = shape.labeldown, location = location.abovebar, color = color.new(color.black, 50), size = size.small, text = 'Pre-Short', textcolor = color.new(color.red, 20))

// State Management
if longCondition or shortCondition
    // Only clear lines if we're getting a new signal
    line.delete(tp1_thick_line)
    line.delete(tp2_thick_line)
    line.delete(tp3_thick_line)
    line.delete(tp1_thin_line)
    line.delete(tp2_thin_line)
    line.delete(tp3_thin_line)
    line.delete(sl_line)
    line.delete(entry_line)
    label.delete(tp1_label)
    label.delete(tp2_label)
    label.delete(tp3_label)
    label.delete(entry_label)
    label.delete(sl_label)
    line_start_index := bar_index
    sl_line := na // Reset stop loss line to na
    sl_label := na // Reset stop loss label to na
    trade_opened_during_trading_hours := is_trading_time()

    // Reset alert fired flags when we get a new signal
    longAlertFired := false
    shortAlertFired := false

    // Store the signal price in the appropriate array
    if longCondition
        array.push(long_signal_prices, close)
        array.push(signal_bars, bar_index)
    else
        array.push(short_signal_prices, close)
        array.push(signal_bars, bar_index)

    // Get stored entry price instead of using close directly
    entry_price := longCondition ?         array.get(long_signal_prices, array.size(long_signal_prices) - 1) :         array.get(short_signal_prices, array.size(short_signal_prices) - 1)
  
    is_long := longCondition
    [tp1_mult, tp2_mult, tp3_mult, sl_mult] = get_dynamic_tp_levels(is_long)
    current_tp1_mult := tp1_mult
   
    // Calculate prices based on direction
    if is_long
        tp1_price := entry_price * (1 + tp1_mult / 100 * global_line_multiplier)
        tp2_price := entry_price * (1 + tp2_mult / 100 * global_line_multiplier)
        tp3_price := entry_price * (1 + tp3_mult / 100 * global_line_multiplier)
        sl_price := entry_price * (1 - tp1_mult / 100 * global_line_multiplier * sl_distance_multiplier) // Match TP1 distance
    else
        tp1_price := entry_price * (1 - tp1_mult / 100 * global_line_multiplier)
        tp2_price := entry_price * (1 - tp2_mult / 100 * global_line_multiplier)
        tp3_price := entry_price * (1 - tp3_mult / 100 * global_line_multiplier)
        sl_price := entry_price * (1 + tp1_mult / 100 * global_line_multiplier * sl_distance_multiplier) // Match TP1 distance

    // CRITICAL FIX: Synchronize alert values with display values
    alertTP1 := tp1_price
    alertTP2 := tp2_price
    alertTP3 := tp3_price
    alertSL := sl_price

    // ======== ALERT MESSAGES ========
    // Define alert messages BEFORE using them
    // Format: TICKER|SIGNAL_TYPE|TIMESTAMP|TIMEFRAME|ENTRY|TP1|TP2|TP3|SL
    // This puts ticker first for easy filtering, then signal type for categorization
    longEntryAlert := syminfo.ticker + '|LONG_ENTRY|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    shortEntryAlert := syminfo.ticker + '|SHORT_ENTRY|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    // Long TP alerts
    longTP1 := syminfo.ticker + '|LONG_TP1|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    longTP2 := syminfo.ticker + '|LONG_TP2|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    longTP3 := syminfo.ticker + '|LONG_TP3|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    // Short TP alerts
    shortTP1 := syminfo.ticker + '|SHORT_TP1|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    shortTP2 := syminfo.ticker + '|SHORT_TP2|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    shortTP3 := syminfo.ticker + '|SHORT_TP3|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    longSL := syminfo.ticker + '|LONG_SL|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    shortSL := syminfo.ticker + '|SHORT_SL|' + str.tostring(timenow) + '|' + get_tf_label(timeframe.period) + '|' + str.tostring(entry_price, '#.####') + '|' + str.tostring(tp1_price, '#.####') + '|' + str.tostring(tp2_price, '#.####') + '|' + str.tostring(tp3_price, '#.####') + '|' + str.tostring(sl_price, '#.####')

    // Create all lines here - single place for line creation
    // Entry line (thin white line) with label
    entry_line := line.new(        x1=line_start_index,         y1=entry_price,         x2=bar_index + line_extension,         y2=entry_price,         color=is_trading_time() ? color.new(color.white, 60) : color.new(color.gray, 60),         width=1,         style=line.style_dashed)

    // Take profit thin lines - FIXED: All thin lines should extend the same length
    tp1_thin_line := line.new(        x1=line_start_index,         y1=tp1_price,         x2=bar_index + line_extension,         y2=tp1_price,         color=trade_opened_during_trading_hours ? #05bf02 : color.gray,         width=1)

    tp2_thin_line := line.new(        x1=line_start_index,         y1=tp2_price,         x2=bar_index + line_extension,         y2=tp2_price,         color=trade_opened_during_trading_hours ? #05bf02 : color.gray,         width=1)

    tp3_thin_line := line.new(        x1=line_start_index,         y1=tp3_price,         x2=bar_index + line_extension,         y2=tp3_price,         color=trade_opened_during_trading_hours ? #05bf02 : color.gray,         width=1)

    // Take profit thick lines - these will be frozen when targets are hit
    tp1_thick_line := line.new(        x1=line_start_index,         y1=tp1_price,         x2=bar_index,         y2=tp1_price,         color=trade_opened_during_trading_hours ? color.green : color.gray,         width=3,         style=get_line_style())

    tp2_thick_line := line.new(        x1=line_start_index,         y1=tp2_price,         x2=bar_index,         y2=tp2_price,         color=trade_opened_during_trading_hours ? color.green : color.gray,         width=3,         style=get_line_style())

    tp3_thick_line := line.new(        x1=line_start_index,         y1=tp3_price,         x2=bar_index,         y2=tp3_price,         color=trade_opened_during_trading_hours ? color.green : color.gray,         width=3,         style=get_line_style())

    // Stop loss line
    sl_line := line.new(        x1=line_start_index,         y1=sl_price,         x2=bar_index + line_extension,         y2=sl_price,         color=color.new(color.red, 30),         width=1,         style=get_line_style())

    // FIXED: Position labels at end of thin lines
    tp1_label := label.new(        x=bar_index + line_extension + label_x_offset_bars,         y=tp1_price,         text=format_label(tp1_emoji, 'TP1: ' + str.tostring(tp1_price, '#.##')),         color=color.new(color.black, 100),         textcolor=color.new(color.green, 0),         style=label.style_none,         size=size.small,         textalign=text.align_left)

    tp2_label := label.new(        x=bar_index + line_extension + label_x_offset_bars,         y=tp2_price,         text=format_label(tp2_emoji, 'TP2: ' + str.tostring(tp2_price, '#.##')),         color=color.new(color.black, 100),         textcolor=color.new(color.green, 0),         style=label.style_none,         size=size.small,         textalign=text.align_left)

    tp3_label := label.new(   x=bar_index + line_extension + label_x_offset_bars,         y=tp3_price,         text=format_label(tp3_emoji, 'TP3: ' + str.tostring(tp3_price, '#.##')),         color=color.new(color.black, 100),         textcolor=color.new(color.green, 0),         style=label.style_none,         size=size.small,         textalign=text.align_left)

    sl_label := label.new(        x=bar_index + line_extension + label_x_offset_bars,         y=sl_price,         text=format_label(sl_emoji, 'SL: ' + str.tostring(sl_price, '#.##')),         color=color.new(color.black, 100),         textcolor=waiting_for_exit ? is_long ? color.red : #f35621 : color.new(color.red, 40),         style=label.style_none,         size=size.small,         textalign=text.align_left)

    entry_label := label.new(        x=bar_index + line_extension + label_x_offset_bars,         y=entry_price,         text=format_label(entry_emoji, 'Entry: ' + str.tostring(entry_price, '#.##')),         color=color.new(color.black, 100),         textcolor=color.white,         style=label.style_none,         size=size.small,         textalign=text.align_left)

    // CRITICAL FIX: Fire alerts first, then execute trades if in trading hours
    if is_trading_time()
        // 1. Fire entry alerts if enabled
        if enableTradeAlerts
            if longCondition and not longAlertFired
                alert(longEntryAlert)
                longAlertFired := true
            else if shortCondition and not shortAlertFired
                alert(shortEntryAlert)
                shortAlertFired := true

        // 2. Close opposite positions
        strategy.close(is_long ? 'SHORT' : 'LONG', comment = 'Opposite Signal')

        // 3. Execute strategy entry
        strategy.entry(longCondition ? 'LONG' : 'SHORT', longCondition ? strategy.long : strategy.short)

        // 4. Reset tracking variables
        tp1_hit := false
        tp2_hit := false
        tp3_hit := false
        sl_hit := false

        // 5. Update statistics
        tp1_attempts := tp1_attempts + 1
        tp2_attempts := tp2_attempts + 1
        tp3_attempts := tp3_attempts + 1
        total_trades := total_trades + 1

        // 6. Set state
        waiting_for_exit := true
        entryBar := bar_index


if waiting_for_exit
    // ===== STOP LOSS LOGIC =====
    if is_long
        // Handle stop loss for longs
        if low <= sl_price and sl_behavior != 'Do Nothing' and not sl_hit
            if sl_behavior == 'Stop Loss'
                strategy.close_all(comment = 'Stop Loss')
                waiting_for_exit := false
                sl_hits := sl_hits + 1
                sl_hit := true
                // Freeze the SL line where it was hit
                if not na(sl_line)
                    line.set_x2(sl_line, bar_index)
                    line.set_y2(sl_line, sl_price)
                if enableTradeAlerts
                    alert(longSL)
    else
        // Handle stop loss for shorts
        if high >= sl_price and sl_behavior != 'Do Nothing' and not sl_hit
            if sl_behavior == 'Stop Loss'
                strategy.close_all(comment = 'Stop Loss')
                waiting_for_exit := false
                sl_hits := sl_hits + 1
                sl_hit := true
                // Freeze the SL line where it was hit
                if not na(sl_line)
                    line.set_x2(sl_line, bar_index)
                    line.set_y2(sl_line, sl_price)
                if enableTradeAlerts
                    alert(shortSL)

    // ===== TAKE PROFIT LOGIC =====
    if is_long
        // Handle take profits for longs
        if high >= tp1_price and not tp1_hit
            if tp1_size > 0
                strategy.close('LONG', qty = tp1_size * strategy.position_size, comment = 'TP1')
            tp1_hits := tp1_hits + 1
            tp1_hit := true
            // FIXED: Freeze thick line at hit point
            if not na(tp1_thick_line)
                line.set_x2(tp1_thick_line, bar_index)
                line.set_y2(tp1_thick_line, tp1_price)
            if enableTradeAlerts
                alert(longTP1)

        if high >= tp2_price and not tp2_hit
            if tp2_size > 0
                strategy.close('LONG', qty = tp2_size * strategy.position_size, comment = 'TP2')
            tp2_hits := tp2_hits + 1
            tp2_hit := true
            // FIXED: Freeze thick line at hit point
            if not na(tp2_thick_line)
                line.set_x2(tp2_thick_line, bar_index)
                line.set_y2(tp2_thick_line, tp2_price)
            if enableTradeAlerts
                alert(longTP2)

        if high >= tp3_price and not tp3_hit
            if not should_flip and tp3_size > 0
                strategy.close('LONG', qty = tp3_size * strategy.position_size, comment = 'TP3')
            tp3_hits := tp3_hits + 1
            tp3_hit := true
            // FIXED: Freeze thick line at hit point
            if not na(tp3_thick_line)
                line.set_x2(tp3_thick_line, bar_index)
                line.set_y2(tp3_thick_line, tp3_price)
            if enableTradeAlerts
                alert(longTP3)
    else
        // Handle take profits for shorts
        if low <= tp1_price and not tp1_hit
            if tp1_size > 0
                strategy.close('SHORT', qty = tp1_size * strategy.position_size, comment = 'TP1')
            tp1_hits := tp1_hits + 1
            tp1_hit := true
            // FIXED: Freeze thick line at hit point
            if not na(tp1_thick_line)
                line.set_x2(tp1_thick_line, bar_index)
                line.set_y2(tp1_thick_line, tp1_price)
            if enableTradeAlerts
                alert(shortTP1)

        if low <= tp2_price and not tp2_hit
            if tp2_size > 0
                strategy.close('SHORT', qty = tp2_size * strategy.position_size, comment = 'TP2')
            tp2_hits := tp2_hits + 1
            tp2_hit := true
            // FIXED: Freeze thick line at hit point
            if not na(tp2_thick_line)
                line.set_x2(tp2_thick_line, bar_index)
                line.set_y2(tp2_thick_line, tp2_price)
            if enableTradeAlerts
                alert(shortTP2)

        if low <= tp3_price and not tp3_hit
            if not should_flip and tp3_size > 0
                strategy.close('SHORT', qty = tp3_size * strategy.position_size, comment = 'TP3')
            tp3_hits := tp3_hits + 1
            tp3_hit := true
            // FIXED: Freeze thick line at hit point
            if not na(tp3_thick_line)
                line.set_x2(tp3_thick_line, bar_index)
                line.set_y2(tp3_thick_line, tp3_price)
            if enableTradeAlerts
                alert(shortTP3)

    // ===== UPDATE LINE POSITIONS AND LABELS =====
    // Update entry line
    if not na(entry_line)
        line.set_x2(entry_line, bar_index + line_extension)
        label.set_x(entry_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned
    // Update entry label text if needed
    if waiting_for_exit
        label.set_text(entry_label, format_label(entry_emoji, 'Entry: ' + str.tostring(entry_price, '#.##')))

    // Update SL line and label if not hit
    if not na(sl_line) and not sl_hit
        line.set_x2(sl_line, bar_index + line_extension)
        label.set_x(sl_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned
        // Update SL label text based on conditions
        if tp3_hit
            if sl_hit and sl_behavior == 'Stop Loss'
                label.set_text(sl_label, format_label(rage_emoji, 'UGHHH SL: ' + str.tostring(sl_price, '#.##')))
            else if sl_hit
                label.set_text(sl_label, format_label(diamond_hands_emoji, 'Diamond Hands SL: ' + str.tostring(sl_price, '#.##')))
        else if sl_hit
            label.set_text(sl_label, format_label(hit_sl_emoji, 'SL: ' + str.tostring(sl_price, '#.##')))
        else
            label.set_text(sl_label, format_label(sl_emoji, 'SL: ' + str.tostring(sl_price, '#.##')))

    // Update TP1 thin line and label
    if not na(tp1_thin_line)
        line.set_x2(tp1_thin_line, bar_index + line_extension)
        label.set_x(tp1_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned
        if tp1_hit and (is_trading_time() or waiting_for_exit)
            label.set_text(tp1_label, format_label(hit_tp_emoji, 'TP1: ' + str.tostring(tp1_price, '#.##')))
        else if sl_behavior == 'Stop Loss' and sl_hit and not tp1_hit
            label.set_text(tp1_label, format_label(miss_tp_emoji, 'TP1: ' + str.tostring(tp1_price, '#.##')))
        else
            label.set_text(tp1_label, format_label(tp1_emoji, 'TP1: ' + str.tostring(tp1_price, '#.##')))

    // Update TP2 thin line and label
    if not na(tp2_thin_line)
        line.set_x2(tp2_thin_line, bar_index + line_extension)
        label.set_x(tp2_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned
        if tp2_hit and (is_trading_time() or waiting_for_exit)
            label.set_text(tp2_label, format_label(hit_tp_emoji, 'TP2: ' + str.tostring(tp2_price, '#.##')))
        else if sl_behavior == 'Stop Loss' and sl_hit and not tp2_hit
            label.set_text(tp2_label, format_label(miss_tp_emoji, 'TP2: ' + str.tostring(tp2_price, '#.##')))
        else
            label.set_text(tp2_label, format_label(tp2_emoji, 'TP2: ' + str.tostring(tp2_price, '#.##')))

    // Update TP3 thin line and label
    if not na(tp3_thin_line)
        line.set_x2(tp3_thin_line, bar_index + line_extension)
        label.set_x(tp3_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned
        if tp3_hit and (is_trading_time() or waiting_for_exit)
            label.set_text(tp3_label, format_label(hit_tp_emoji, 'TP3: ' + str.tostring(tp3_price, '#.##')))
        else if sl_behavior == 'Stop Loss' and sl_hit and not tp3_hit
            label.set_text(tp3_label, format_label(miss_tp_emoji, 'TP3: ' + str.tostring(tp3_price, '#.##')))
        else
            label.set_text(tp3_label, format_label(tp3_emoji, 'TP3: ' + str.tostring(tp3_price, '#.##')))

    // FIXED: Update thick lines that haven't been hit yet
    if not tp1_hit and not na(tp1_thick_line)
        line.set_x2(tp1_thick_line, bar_index)

    if not tp2_hit and not na(tp2_thick_line)
        line.set_x2(tp2_thick_line, bar_index)

    if not tp3_hit and not na(tp3_thick_line)
        line.set_x2(tp3_thick_line, bar_index)

// ===== LINE EXTENSION LOGIC FOR NON-TRADING HOURS =====
// Extend lines for trades opened outside of trading hours
if not is_trading_time()
    // Update ALL thin lines to the same extension
    if not na(tp1_thin_line)
        line.set_x2(tp1_thin_line, bar_index + line_extension)
        label.set_x(tp1_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned

    if not na(tp2_thin_line)
        line.set_x2(tp2_thin_line, bar_index + line_extension)
        label.set_x(tp2_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned

    if not na(tp3_thin_line)
        line.set_x2(tp3_thin_line, bar_index + line_extension)
        label.set_x(tp3_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned

    if not na(entry_line)
        line.set_x2(entry_line, bar_index + line_extension)
        line.set_width(entry_line, 1)  // Force thin width
        line.set_style(entry_line, line.style_dashed)  // Match non-trading style
        label.set_x(entry_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned

    if not na(sl_line)
        line.set_x2(sl_line, bar_index + line_extension)
        line.set_width(sl_line, 1)  // Force thin width
        line.set_style(sl_line, line.style_dashed)  // Match non-trading style
        label.set_x(sl_label, bar_index + line_extension + label_x_offset_bars) // FIXED: Keep label consistently positioned
// ===== PLOT PRICE LEVELS =====
// Plot crosses with color based on when the trade was opened
plot(show_plot_lines and line_visible(false) ? tp1_price : na, 'TP1 Cross',
     color = trade_opened_during_trading_hours ? color.new(#49d84bcc, 50) : color.new(color.gray, 30),
     style = plot.style_cross, linewidth = 1)

plot(show_plot_lines and line_visible(false) ? tp2_price : na, 'TP2 Cross',
     color = trade_opened_during_trading_hours ? color.new(#49d84bcc, 65) : color.new(color.gray, 30),
     style = plot.style_cross, linewidth = 1)

plot(show_plot_lines and line_visible(false) ? tp3_price : na, 'TP3 Cross',
     color = trade_opened_during_trading_hours ? color.new(#49d84bcc, 77) : color.new(color.gray, 30),
     style = plot.style_cross, linewidth = 1)

plot(show_plot_lines and line_visible(false) ? sl_price : na, 'SL Cross',
     color = trade_opened_during_trading_hours ? color.new(#f35621cc, 60) : color.new(color.gray, 30),
     style = plot.style_cross, linewidth = 1)

plot(show_plot_lines and line_visible(false) ? entry_price : na, 'Entry Cross',     color = trade_opened_during_trading_hours ? color.new(#818384cc, 60) : color.new(color.gray, 30),     style = plot.style_cross, linewidth = 1)



// BEGIN: Full-array history dump to webhook (optional)
// Dynamic, toggle-driven payload builder //dump_all_indicators = input.bool(false, 'Enable All Data Dump', group=hist_group, tooltip='If enabled, all historical data for indicators will be dumped, overriding individual indicator dump settings.')
enableHistoryDump := enableHistoryDump 

array_to_json(arr) =>
    max_items = math.min(array.size(arr), hist_dump_items_per_series)
    s = "["
    if max_items > 0
        for i = 0 to max_items - 1
            // Numeric arrays only
            s += str.tostring(array.get(arr, i), "#.####") + (i < max_items - 1 ? "," : "")
    s + "]"

// String array serializer (e.g., market_state)
array_to_json_str(arr) =>
    max_items = math.min(array.size(arr), hist_dump_items_per_series)
    s = "["
    if max_items > 0
        for i = 0 to max_items - 1
            _raw = str.tostring(array.get(arr, i))
            // Basic sanitization to keep JSON valid and compact
            _san = str.replace(_raw, '"', "'")
            _san := str.replace(_san, "\n", " ")
            s += '"' + _san + '"' + (i < max_items - 1 ? "," : "")
    s + "]"

// Boolean array serializer (e.g., volume_impulse)
array_to_json_bool(arr) =>
    max_items = math.min(array.size(arr), hist_dump_items_per_series)
    s = "["
    if max_items > 0
        for i = 0 to max_items - 1
            _b = array.get(arr, i)
            s += (_b ? "true" : "false") + (i < max_items - 1 ? "," : "")
    s + "]"

if enableHistoryDump and barstate.islast
    string indicators_json = ""
    bool first = true
    if dump_all_indicators or dump_strength
        indicators_json += (first ? "" : ",") + '"m5_strength":' + array_to_json(hist_m5_str)
        first := false
        indicators_json += ',"m15_strength":' + array_to_json(hist_m15_str)
        indicators_json += ',"h1_strength":' + array_to_json(hist_h1_str)
    if dump_all_indicators or dump_confidence
        indicators_json += (first ? "" : ",") + '"bull_confidence":' + array_to_json(hist_bull_confidence)
        first := false
        indicators_json += ',"bear_confidence":' + array_to_json(hist_bear_confidence)
    if dump_all_indicators or dump_st_consensus
        indicators_json += (first ? "" : ",") + '"st_consensus":' + array_to_json(hist_st_consensus)
        first := false
    if dump_all_indicators or dump_volume
        indicators_json += (first ? "" : ",") + '"overall_volume_pct":' + array_to_json(hist_overall_volume)
        first := false
    if dump_all_indicators or dump_signal_prices
        indicators_json += (first ? "" : ",") + '"long_signal_prices":' + array_to_json(long_signal_prices)
        first := false
        indicators_json += ',"short_signal_prices":' + array_to_json(short_signal_prices)
    if dump_all_indicators or dump_rsi
        indicators_json += (first ? "" : ",") + '"rsi_14":' + array_to_json(hist_rsi)
        first := false
    if dump_all_indicators or dump_adx
        indicators_json += (first ? "" : ",") + '"adx_14":' + array_to_json(hist_adx)
        first := false
    if dump_all_indicators or dump_macd_hist
        indicators_json += (first ? "" : ",") + '"macd_hist":' + array_to_json(hist_macd_hist)
        first := false
    if dump_all_indicators or dump_atr_pct
        indicators_json += (first ? "" : ",") + '"atr_percent":' + array_to_json(hist_atr_pct)
        first := false

    // Include advanced indicators in payload (match accumulation toggles)
    if dump_all_indicators or dump_directional_indicators
        indicators_json += (first ? "" : ",") + '"plus_di":' + array_to_json(hist_plus_di)
        first := false
        indicators_json += ',"minus_di":' + array_to_json(hist_minus_di)
    if dump_all_indicators or dump_momentum_indicators
        indicators_json += (first ? "" : ",") + '"price_momentum":' + array_to_json(hist_price_momentum)
        first := false
        indicators_json += ',"price_velocity":' + array_to_json(hist_price_velocity)
    if dump_all_indicators or dump_market_state
        indicators_json += (first ? "" : ",") + '"market_state":' + array_to_json_str(hist_market_state)
        first := false
    if dump_all_indicators or dump_trend_strength
        indicators_json += (first ? "" : ",") + '"trend_strength":' + array_to_json(hist_trend_strength)
        first := false
    if dump_all_indicators or dump_stoch_rsi
        indicators_json += (first ? "" : ",") + '"stoch_rsi":' + array_to_json(hist_stoch_rsi)
        first := false
    if dump_all_indicators or dump_volume_impulse
        indicators_json += (first ? "" : ",") + '"volume_impulse":' + array_to_json_bool(hist_volume_impulse)
        first := false

    tf = timeframe.period
    payload = '{"symbol":"' + syminfo.ticker + '","timeframe":"' + tf + '","bar_time":' + str.tostring(timenow) + ',"price":' + str.tostring(close) + ',"indicators":{' + indicators_json + '}}'
    alert(payload, alert.freq_once_per_bar_close)
// END: Full-array history dump to webhook (optional)
 
// ============================================================================
// GLOBAL VALUE SYNCHRONIZATION
// ============================================================================
// Update global variables with calculated values to ensure consistency
if barstate.isconfirmed
    // Update strength values
    global_m5_strength := calcDirectionalStrengthValue('5')
    global_m15_strength := calcDirectionalStrengthValue('15')
    global_h1_strength := calcDirectionalStrengthValue('60')
    
    // Update confidence values
    [global_bull_confidence, global_bear_confidence] = normalizeConfidence()
    
    // Update other global values
    global_st_consensus := st_consensus
    global_overall_volume := overall_volume_percent
    global_rsi_value := rsi_value
    global_adx_value := adx_value
    global_macd_hist := histLine
    global_atr_percent := atr_percent
    global_plus_di := plus_di
    global_minus_di := minus_di
    global_price_momentum := price_momentum
    global_price_velocity := price_velocity
    global_market_state := market_state
    global_trend_strength := market_trend_strength
    global_stoch_rsi := stochRsi
    global_volume_impulse := volume_impulse

// ============================================================================
// IMPROVED DATA ACCUMULATION FOR DUMP WITH NA HANDLING
// ============================================================================
if barstate.isconfirmed
    if dump_all_indicators or dump_strength
        // Only add valid values to arrays
        m5_val = na(global_m5_strength) ? 0.0 : global_m5_strength
        m15_val = na(global_m15_strength) ? 0.0 : global_m15_strength
        h1_val = na(global_h1_strength) ? 0.0 : global_h1_strength
        
        array.unshift(hist_m5_str, m5_val)
        array.unshift(hist_m15_str, m15_val)
        array.unshift(hist_h1_str, h1_val)
        
        if array.size(hist_m5_str) > hist_array_max_size
            array.pop(hist_m5_str)
        if array.size(hist_m15_str) > hist_array_max_size
            array.pop(hist_m15_str)
        if array.size(hist_h1_str) > hist_array_max_size
            array.pop(hist_h1_str)
            
    if dump_all_indicators or dump_confidence
        // Only add valid confidence values
        bull_val = na(global_bull_confidence) ? 50.0 : global_bull_confidence
        bear_val = na(global_bear_confidence) ? 50.0 : global_bear_confidence
        
        array.unshift(hist_bull_confidence, bull_val)
        array.unshift(hist_bear_confidence, bear_val)
        
        if array.size(hist_bull_confidence) > hist_array_max_size
            array.pop(hist_bull_confidence)
            array.pop(hist_bear_confidence)
            
    if dump_all_indicators or dump_st_consensus
        consensus_val = na(global_st_consensus) ? 50.0 : global_st_consensus
        array.unshift(hist_st_consensus, consensus_val)
        if array.size(hist_st_consensus) > hist_array_max_size
            array.pop(hist_st_consensus)
            
    if dump_all_indicators or dump_volume
        volume_val = na(global_overall_volume) ? 100.0 : global_overall_volume
        array.unshift(hist_overall_volume, volume_val)
        if array.size(hist_overall_volume) > hist_array_max_size
            array.pop(hist_overall_volume)
            
    if dump_all_indicators or dump_rsi
        rsi_val = na(global_rsi_value) ? 50.0 : global_rsi_value
        array.unshift(hist_rsi, rsi_val)
        if array.size(hist_rsi) > hist_array_max_size
            array.pop(hist_rsi)
    if dump_all_indicators or dump_adx
        adx_val = na(global_adx_value) ? 25.0 : global_adx_value
        array.unshift(hist_adx, adx_val)
        if array.size(hist_adx) > hist_array_max_size
            array.pop(hist_adx)
    if dump_all_indicators or dump_macd_hist
        macd_val = na(global_macd_hist) ? 0.0 : global_macd_hist
        array.unshift(hist_macd_hist, macd_val)
        if array.size(hist_macd_hist) > hist_array_max_size
            array.pop(hist_macd_hist)
    if dump_all_indicators or dump_atr_pct
        atr_val = na(global_atr_percent) ? 1.0 : global_atr_percent
        array.unshift(hist_atr_pct, atr_val)
        if array.size(hist_atr_pct) > hist_array_max_size
            array.pop(hist_atr_pct)
    
    // ========================================================================
    // ADVANCED TECHNICAL INDICATORS DATA ACCUMULATION
    // ========================================================================
    // Add new technical indicators to historical arrays for AI analysis
    
    if dump_all_indicators or dump_directional_indicators
        plus_di_val = na(global_plus_di) ? 25.0 : global_plus_di
        minus_di_val = na(global_minus_di) ? 25.0 : global_minus_di
        
        array.unshift(hist_plus_di, plus_di_val)
        array.unshift(hist_minus_di, minus_di_val)
        
        if array.size(hist_plus_di) > hist_array_max_size
            array.pop(hist_plus_di)
            array.pop(hist_minus_di)
    
    if dump_all_indicators or dump_momentum_indicators
        momentum_val = na(global_price_momentum) ? 0.0 : global_price_momentum
        velocity_val = na(global_price_velocity) ? 0.0 : global_price_velocity
        
        array.unshift(hist_price_momentum, momentum_val)
        array.unshift(hist_price_velocity, velocity_val)
        
        if array.size(hist_price_momentum) > hist_array_max_size
            array.pop(hist_price_momentum)
            array.pop(hist_price_velocity)
    
    if dump_all_indicators or dump_market_state
        // For string values, check if they're na or empty
        state_val = na(global_market_state) or global_market_state == "" ? "NEUTRAL" : global_market_state
        array.unshift(hist_market_state, state_val)
        if array.size(hist_market_state) > hist_array_max_size
            array.pop(hist_market_state)
    
    if dump_all_indicators or dump_trend_strength
        trend_val = na(global_trend_strength) ? 25.0 : global_trend_strength
        array.unshift(hist_trend_strength, trend_val)
        if array.size(hist_trend_strength) > hist_array_max_size
            array.pop(hist_trend_strength)
    
    if dump_all_indicators or dump_stoch_rsi
        stoch_val = na(global_stoch_rsi) ? 0.5 : global_stoch_rsi
        array.unshift(hist_stoch_rsi, stoch_val)
        if array.size(hist_stoch_rsi) > hist_array_max_size
            array.pop(hist_stoch_rsi)
    
    if dump_all_indicators or dump_volume_impulse
        // For boolean values, we don't need na() check since they're always defined
        array.unshift(hist_volume_impulse, global_volume_impulse)
        if array.size(hist_volume_impulse) > hist_array_max_size
            array.pop(hist_volume_impulse) 