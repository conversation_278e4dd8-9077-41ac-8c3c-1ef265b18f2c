-- Research Queries for TradingView Webhook Data
-- These queries help analyze the stored webhook data for research purposes

-- 1. Summary of all webhook activity
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_webhooks,
    COUNT(DISTINCT webhook_id) as unique_webhooks
FROM webhooks 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 2. Trading alerts by symbol (most active tickers)
SELECT 
    symbol,
    COUNT(*) as alert_count,
    MIN(created_at) as first_alert,
    MAX(created_at) as last_alert,
    COUNT(CASE WHEN alert_type = 'BUY' THEN 1 END) as buy_signals,
    COUNT(CASE WHEN alert_type = 'SELL' THEN 1 END) as sell_signals
FROM webhook_alerts 
GROUP BY symbol
ORDER BY alert_count DESC;

-- 3. Recent webhook activity (last 24 hours)
SELECT 
    webhook_id,
    timestamp,
    status,
    created_at,
    raw_data->>'symbol' as extracted_symbol
FROM webhooks 
WHERE created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;

-- 4. Webhook processing performance
SELECT 
    DATE(created_at) as date,
    COUNT(*) as webhooks,
    AVG(EXTRACT(EPOCH FROM (created_at - to_timestamp(timestamp))) as avg_processing_delay_seconds
FROM webhooks 
WHERE timestamp IS NOT NULL
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 5. Alert types distribution
SELECT 
    alert_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM webhook_alerts 
GROUP BY alert_type
ORDER BY count DESC;

-- 6. Time-based analysis (hourly distribution)
SELECT 
    EXTRACT(HOUR FROM created_at) as hour,
    COUNT(*) as webhook_count
FROM webhooks 
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY EXTRACT(HOUR FROM created_at)
ORDER BY hour;

-- 7. Raw webhook data analysis (extract specific fields)
SELECT 
    webhook_id,
    raw_data->>'symbol' as symbol,
    raw_data->>'price' as price,
    raw_data->>'event_type' as event_type,
    created_at
FROM webhooks 
WHERE raw_data->>'symbol' IS NOT NULL
ORDER BY created_at DESC
LIMIT 20;

-- 8. Trading signal analysis
SELECT 
    symbol,
    alert_type,
    entry_price,
    tp1_price,
    tp2_price,
    tp3_price,
    sl_price,
    created_at
FROM webhook_alerts 
WHERE entry_price IS NOT NULL
ORDER BY created_at DESC
LIMIT 20;

-- 9. Webhook health check (failed vs successful)
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM webhooks 
GROUP BY status
ORDER BY count DESC;

-- 10. Symbol correlation analysis (which symbols appear together)
WITH symbol_pairs AS (
    SELECT 
        w1.raw_data->>'symbol' as symbol1,
        w2.raw_data->>'symbol' as symbol2,
        COUNT(*) as pair_count
    FROM webhooks w1
    JOIN webhooks w2 ON 
        w1.webhook_id != w2.webhook_id 
        AND ABS(EXTRACT(EPOCH FROM (w1.created_at - w2.created_at))) < 60
    WHERE w1.raw_data->>'symbol' IS NOT NULL 
        AND w2.raw_data->>'symbol' IS NOT NULL
        AND w1.raw_data->>'symbol' < w2.raw_data->>'symbol'
    GROUP BY w1.raw_data->>'symbol', w2.raw_data->>'symbol'
)
SELECT * FROM symbol_pairs 
WHERE pair_count > 1
ORDER BY pair_count DESC; 