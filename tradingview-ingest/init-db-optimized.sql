-- Optimized Database Schema for Multi-Ticker TradingView Ingestion
-- Designed for high-throughput 3-minute candle data

-- Drop existing tables if they exist
DROP TABLE IF EXISTS ticker_data CASCADE;
DROP TABLE IF EXISTS webhook_events CASCADE;
DROP TABLE IF EXISTS time_series_cache CASCADE;

-- Main ticker data table - stores individual ticker records
CREATE TABLE ticker_data (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    candle_interval VARCHAR(10) DEFAULT '3m',
    
    -- Price data (all optional to handle various TradingView formats)
    price NUMERIC(10,4),
    open_price NUMERIC(10,4),
    high_price NUMERIC(10,4),
    low_price NUMERIC(10,4),
    close_price NUMERIC(10,4),
    volume BIGINT,
    
    -- Raw data storage (preserves all TradingView data)
    raw_data JSONB NOT NULL,
    metadata JSONB,
    
    -- System fields
    ingested_at TIMESTAMPTZ DEFAULT NOW(),
    webhook_id VARCHAR(50),
    
    -- Indexes for fast querying
    CONSTRAINT valid_symbol CHECK (symbol ~ '^[A-Z]{1,5}$'),
    CONSTRAINT valid_price CHECK (price IS NULL OR price > 0),
    CONSTRAINT valid_timestamp CHECK (timestamp > '2020-01-01'::timestamptz)
);

-- Webhook events table - tracks incoming webhooks
CREATE TABLE webhook_events (
    id BIGSERIAL PRIMARY KEY,
    webhook_id VARCHAR(50) UNIQUE NOT NULL,
    received_at TIMESTAMPTZ DEFAULT NOW(),
    payload_size INTEGER,
    ticker_count INTEGER,
    processing_time_ms NUMERIC(10,3),
    status VARCHAR(20) DEFAULT 'success',
    error_message TEXT,
    
    -- Raw webhook data
    raw_payload JSONB
);

-- Raw webhooks table - stores complete webhook data
CREATE TABLE webhooks (
    webhook_id VARCHAR(50) PRIMARY KEY,
    raw_data JSONB NOT NULL,
    client_ip VARCHAR(45),
    timestamp NUMERIC(20,3),
    status VARCHAR(20) DEFAULT 'received',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Parsed webhooks table - stores processed webhook data
CREATE TABLE parsed_webhooks (
    webhook_id VARCHAR(50) PRIMARY KEY,
    parsed_data JSONB NOT NULL,
    timestamp NUMERIC(20,3),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tickers table - tracks active tickers
CREATE TABLE tickers (
    symbol VARCHAR(20) PRIMARY KEY,
    first_seen TIMESTAMPTZ DEFAULT NOW(),
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    alert_count INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE
);

-- Time series cache table - pre-aggregated data for fast analysis
CREATE TABLE time_series_cache (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    candle_start TIMESTAMPTZ NOT NULL,
    candle_interval VARCHAR(10) NOT NULL,
    
    -- Aggregated OHLCV data
    open_price NUMERIC(10,4),
    high_price NUMERIC(10,4),
    low_price NUMERIC(10,4),
    close_price NUMERIC(10,4),
    volume BIGINT,
    
    -- Count of data points in this candle
    data_points INTEGER DEFAULT 1,
    
    -- Last update
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    
    -- Unique constraint
    UNIQUE(symbol, candle_start, candle_interval)
);

-- Create indexes for fast querying
CREATE INDEX idx_ticker_data_symbol_timestamp ON ticker_data(symbol, timestamp DESC);
CREATE INDEX idx_ticker_data_timestamp ON ticker_data(timestamp DESC);
CREATE INDEX idx_ticker_data_symbol ON ticker_data(symbol);
CREATE INDEX idx_ticker_data_webhook_id ON ticker_data(webhook_id);
CREATE INDEX idx_ticker_data_raw_data_gin ON ticker_data USING GIN (raw_data);

CREATE INDEX idx_webhook_events_received_at ON webhook_events(received_at DESC);
CREATE INDEX idx_webhook_events_status ON webhook_events(status);

-- Indexes for new webhook tables
CREATE INDEX idx_webhooks_timestamp ON webhooks(timestamp DESC);
CREATE INDEX idx_webhooks_status ON webhooks(status);
CREATE INDEX idx_webhooks_client_ip ON webhooks(client_ip);
CREATE INDEX idx_webhooks_raw_data_gin ON webhooks USING GIN (raw_data);

CREATE INDEX idx_parsed_webhooks_timestamp ON parsed_webhooks(timestamp DESC);
CREATE INDEX idx_parsed_webhooks_parsed_data_gin ON parsed_webhooks USING GIN (parsed_data);

-- Index for tickers table
CREATE INDEX idx_tickers_symbol ON tickers(symbol);
CREATE INDEX idx_tickers_is_active ON tickers(is_active);
CREATE INDEX idx_tickers_last_seen ON tickers(last_seen DESC);

CREATE INDEX idx_time_series_cache_symbol_candle ON time_series_cache(symbol, candle_start DESC);
CREATE INDEX idx_time_series_cache_candle_start ON time_series_cache(candle_start DESC);

-- Create views for easy analysis
CREATE OR REPLACE VIEW latest_ticker_data AS
SELECT DISTINCT ON (symbol) 
    symbol,
    timestamp,
    price,
    open_price,
    high_price,
    low_price,
    close_price,
    volume,
    raw_data,
    ingested_at
FROM ticker_data 
ORDER BY symbol, timestamp DESC;

CREATE OR REPLACE VIEW ticker_summary AS
SELECT 
    symbol,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    AVG(price) as avg_price,
    MAX(high_price) as all_time_high,
    MIN(low_price) as all_time_low
FROM ticker_data 
WHERE price IS NOT NULL
GROUP BY symbol
ORDER BY total_records DESC;

CREATE OR REPLACE VIEW recent_webhooks AS
SELECT 
    webhook_id,
    received_at,
    ticker_count,
    processing_time_ms,
    status,
    payload_size
FROM webhook_events 
ORDER BY received_at DESC 
LIMIT 100;

-- Function to get ticker data for a specific time range
CREATE OR REPLACE FUNCTION get_ticker_data(
    p_symbol VARCHAR(20),
    p_start_time TIMESTAMPTZ,
    p_end_time TIMESTAMPTZ DEFAULT NOW()
)
RETURNS TABLE (
    symbol VARCHAR(20),
    data_timestamp TIMESTAMPTZ,
    price NUMERIC(10,4),
    open_price NUMERIC(10,4),
    high_price NUMERIC(10,4),
    low_price NUMERIC(10,4),
    close_price NUMERIC(10,4),
    volume BIGINT,
    raw_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        td.symbol,
        td.timestamp,
        td.price,
        td.open_price,
        td.high_price,
        td.low_price,
        td.close_price,
        td.volume,
        td.raw_data
    FROM ticker_data td
    WHERE td.symbol = p_symbol
      AND td.timestamp BETWEEN p_start_time AND p_end_time
    ORDER BY td.timestamp ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to get latest data for multiple tickers
CREATE OR REPLACE FUNCTION get_latest_tickers(p_symbols VARCHAR(20)[])
RETURNS TABLE (
    symbol VARCHAR(20),
    data_timestamp TIMESTAMPTZ,
    price NUMERIC(10,4),
    raw_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT ON (td.symbol)
        td.symbol,
        td.timestamp,
        td.price,
        td.raw_data
    FROM ticker_data td
    WHERE td.symbol = ANY(p_symbols)
    ORDER BY td.symbol, td.timestamp DESC;
END;
$$ LANGUAGE plpgsql;

-- Insert sample data for testing
INSERT INTO ticker_data (symbol, timestamp, price, raw_data) VALUES
('SHOP', NOW() - INTERVAL '5 minutes', 45.67, '{"symbol": "SHOP", "price": 45.67, "test": true}'),
('AAPL', NOW() - INTERVAL '4 minutes', 150.25, '{"symbol": "AAPL", "price": 150.25, "test": true}'),
('TSLA', NOW() - INTERVAL '3 minutes', 250.50, '{"symbol": "TSLA", "price": 250.50, "test": true}');

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tradingview_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tradingview_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO tradingview_user; 