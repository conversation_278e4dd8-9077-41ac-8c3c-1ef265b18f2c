-- Fix security issues found in Supabase audit

-- 1. Enable RLS on tables that don't have it
ALTER TABLE public.tradingview_candles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pipeline_executions ENABLE ROW LEVEL SECURITY;

-- 2. Create appropriate RLS policies for these tables
-- For tradingview_candles
CREATE POLICY "Allow authenticated users to read tradingview_candles" 
    ON public.tradingview_candles FOR SELECT 
    USING ((SELECT auth.role()) = 'authenticated');

CREATE POLICY "Allow service role to manage tradingview_candles" 
    ON public.tradingview_candles FOR ALL 
    USING ((SELECT auth.role()) = 'service_role');

-- For pipeline_executions
CREATE POLICY "Allow authenticated users to read pipeline_executions" 
    ON public.pipeline_executions FOR SELECT 
    USING ((SELECT auth.role()) = 'authenticated');

CREATE POLICY "Allow service role to manage pipeline_executions" 
    ON public.pipeline_executions FOR ALL 
    USING ((SELECT auth.role()) = 'service_role');

-- 3. Fix performance issues with RLS policies
-- Replace direct auth.uid() calls with (SELECT auth.uid()) to avoid re-evaluation for each row

-- Fix conversations table policies
DROP POLICY IF EXISTS conversations_select_own ON public.conversations;
CREATE POLICY conversations_select_own ON public.conversations
    FOR SELECT USING (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS conversations_modify_own ON public.conversations;
CREATE POLICY conversations_modify_own ON public.conversations
    FOR ALL USING (user_id = (SELECT auth.uid()));

-- Fix messages table policies
DROP POLICY IF EXISTS messages_select_own ON public.messages;
CREATE POLICY messages_select_own ON public.messages
    FOR SELECT USING (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS messages_modify_own ON public.messages;
CREATE POLICY messages_modify_own ON public.messages
    FOR ALL USING (user_id = (SELECT auth.uid()));

-- Fix analysis_results table policies
DROP POLICY IF EXISTS analysis_results_select_own ON public.analysis_results;
CREATE POLICY analysis_results_select_own ON public.analysis_results
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversations
            WHERE id = conversation_id AND user_id = (SELECT auth.uid())
        )
    );

DROP POLICY IF EXISTS analysis_results_modify_own ON public.analysis_results;
CREATE POLICY analysis_results_modify_own ON public.analysis_results
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.conversations
            WHERE id = conversation_id AND user_id = (SELECT auth.uid())
        )
    );

-- Fix settings table policies
-- First, consolidate multiple permissive policies
DROP POLICY IF EXISTS settings_select_own ON public.settings;
DROP POLICY IF EXISTS settings_modify_own ON public.settings;
CREATE POLICY settings_access_own ON public.settings
    FOR ALL USING (user_id = (SELECT auth.uid()));

-- Fix strategy_configs table policies
-- First, consolidate multiple permissive policies
DROP POLICY IF EXISTS strategy_configs_select_own ON public.strategy_configs;
DROP POLICY IF EXISTS strategy_configs_modify_own ON public.strategy_configs;
CREATE POLICY strategy_configs_access_own ON public.strategy_configs
    FOR ALL USING (user_id = (SELECT auth.uid()));

-- Fix tasks table policies
-- First, consolidate multiple permissive policies
DROP POLICY IF EXISTS tasks_select_own ON public.tasks;
DROP POLICY IF EXISTS tasks_modify_own ON public.tasks;
CREATE POLICY tasks_access_own ON public.tasks
    FOR ALL USING (user_id = (SELECT auth.uid()));

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON public.conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_messages_user_id ON public.messages(user_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_conversation_id ON public.analysis_results(conversation_id);
CREATE INDEX IF NOT EXISTS idx_settings_user_id ON public.settings(user_id);
CREATE INDEX IF NOT EXISTS idx_strategy_configs_user_id ON public.strategy_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON public.tasks(user_id);

-- Add comments for better documentation
COMMENT ON TABLE public.tradingview_candles IS 'Stores candle data from TradingView';
COMMENT ON TABLE public.pipeline_executions IS 'Stores execution details of processing pipelines';
