-- Create webhooks table to store incoming webhook data
CREATE TABLE IF NOT EXISTS public.webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    webhook_id TEXT NOT NULL UNIQUE,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    client_ip TEXT,
    raw_data JSONB NOT NULL,
    status TEXT NOT NULL DEFAULT 'received',
    processed_at TIMESTAMPTZ,
    processed_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create tickers table to store information about trading symbols
CREATE TABLE IF NOT EXISTS public.tickers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol TEXT NOT NULL UNIQUE,
    last_price NUMERIC,
    last_updated TIMESTAMPTZ NOT NULL DEFAULT now(),
    first_seen TIMESTAMPTZ NOT NULL DEFAULT now(),
    is_active BOOLEAN NOT NULL DEFAULT true,
    alert_count INTEGER NOT NULL DEFAULT 0,
    metadata JSONB
);

-- <PERSON><PERSON> signals table to store trading signals
CREATE TABLE IF NOT EXISTS public.signals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol TEXT NOT NULL,
    signal_type TEXT NOT NULL,
    timeframe TEXT,
    entry_price NUMERIC,
    tp1_price NUMERIC,
    tp2_price NUMERIC,
    tp3_price NUMERIC,
    sl_price NUMERIC,
    confidence NUMERIC,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    metadata JSONB
);

-- Add tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.webhooks;
ALTER PUBLICATION supabase_realtime ADD TABLE public.tickers;
ALTER PUBLICATION supabase_realtime ADD TABLE public.signals;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_webhooks_status ON public.webhooks (status);
CREATE INDEX IF NOT EXISTS idx_webhooks_timestamp ON public.webhooks (timestamp);
CREATE INDEX IF NOT EXISTS idx_tickers_symbol ON public.tickers (symbol);
CREATE INDEX IF NOT EXISTS idx_signals_symbol ON public.signals (symbol);
CREATE INDEX IF NOT EXISTS idx_signals_timestamp ON public.signals (timestamp);

-- Add comments for better documentation
COMMENT ON TABLE public.webhooks IS 'Stores incoming webhook data from TradingView';
COMMENT ON TABLE public.tickers IS 'Stores information about trading symbols';
COMMENT ON TABLE public.signals IS 'Stores trading signals generated from webhooks';

-- Add row level security policies
ALTER TABLE public.webhooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.signals ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users
CREATE POLICY "Allow authenticated users to read webhooks" 
    ON public.webhooks FOR SELECT 
    USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to read tickers" 
    ON public.tickers FOR SELECT 
    USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to read signals" 
    ON public.signals FOR SELECT 
    USING (auth.role() = 'authenticated');

-- Create policies for service role
CREATE POLICY "Allow service role to manage webhooks" 
    ON public.webhooks FOR ALL 
    USING (auth.role() = 'service_role');

CREATE POLICY "Allow service role to manage tickers" 
    ON public.tickers FOR ALL 
    USING (auth.role() = 'service_role');

CREATE POLICY "Allow service role to manage signals" 
    ON public.signals FOR ALL 
    USING (auth.role() = 'service_role');
