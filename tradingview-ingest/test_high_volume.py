#!/usr/bin/env python3
"""
High-Volume Webhook Test Script
Tests the system's ability to handle multiple simultaneous webhooks
"""

import asyncio
import aiohttp
import json
import time
import random
from typing import List, Dict, Any
import hmac
import hashlib

# Configuration
WEBHOOK_URL = "http://localhost:8001/webhook/tradingview"
WEBHOOK_SECRET = "your_super_secret_webhook_key_here_change_this_immediately"
NUM_WEBHOOKS = 20  # Number of simultaneous webhooks
DELAY_BETWEEN_BATCHES = 1.0  # Seconds between batches

# Sample TradingView webhook payloads
SAMPLE_PAYLOADS = [
    {
        "symbol": "AAPL",
        "event_type": "price_update",
        "price": 150.25,
        "volume": 1000000,
        "timestamp": int(time.time()),
        "exchange": "NASDAQ"
    },
    {
        "symbol": "MSFT",
        "event_type": "indicator_update",
        "rsi": 65.5,
        "macd": 0.25,
        "timestamp": int(time.time()),
        "exchange": "NASDAQ"
    },
    {
        "symbol": "GOOGL",
        "event_type": "signal_generated",
        "signal_type": "buy",
        "confidence": 0.85,
        "timestamp": int(time.time()),
        "exchange": "NASDAQ"
    },
    {
        "symbol": "TSLA",
        "event_type": "alert_triggered",
        "alert_type": "price_breakout",
        "threshold": 250.0,
        "timestamp": int(time.time()),
        "exchange": "NASDAQ"
    }
]

def generate_webhook_signature(payload: Dict[str, Any], secret: str) -> str:
    """Generate HMAC signature for webhook payload"""
    payload_str = json.dumps(payload, separators=(',', ':'))
    return hmac.new(
        secret.encode('utf-8'),
        payload_str.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

def create_webhook_payload(symbol: str = None) -> Dict[str, Any]:
    """Create a random webhook payload"""
    base_payload = random.choice(SAMPLE_PAYLOADS).copy()
    
    if symbol:
        base_payload["symbol"] = symbol
    
    # Add some randomness
    base_payload["timestamp"] = int(time.time()) + random.randint(-60, 60)
    
    if "price" in base_payload:
        base_payload["price"] = round(base_payload["price"] * random.uniform(0.95, 1.05), 2)
    
    if "volume" in base_payload:
        base_payload["volume"] = int(base_payload["volume"] * random.uniform(0.8, 1.2))
    
    return base_payload

async def send_single_webhook(session: aiohttp.ClientSession, payload: Dict[str, Any]) -> Dict[str, Any]:
    """Send a single webhook and return the response"""
    try:
        # Generate signature
        signature = generate_webhook_signature(payload, WEBHOOK_SECRET)
        
        # Prepare headers
        headers = {
            "Content-Type": "application/json",
            "X-TradingView-Signature": signature,
            "User-Agent": "TradingView/1.0"
        }
        
        # Send request
        start_time = time.time()
        async with session.post(WEBHOOK_URL, json=payload, headers=headers) as response:
            response_time = time.time() - start_time
            
            response_data = await response.json()
            
            return {
                "status_code": response.status,
                "response_time": response_time,
                "response_data": response_data,
                "success": response.status == 200,
                "payload": payload
            }
            
    except Exception as e:
        return {
            "status_code": 0,
            "response_time": 0,
            "response_data": {"error": str(e)},
            "success": False,
            "payload": payload
        }

async def send_webhook_batch(session: aiohttp.ClientSession, batch_size: int) -> List[Dict[str, Any]]:
    """Send a batch of webhooks simultaneously"""
    print(f"📤 Sending batch of {batch_size} webhooks...")
    
    # Create tasks for all webhooks in the batch
    tasks = []
    symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN", "NVDA", "META", "NFLX", "AMD", "INTC"]
    
    for i in range(batch_size):
        symbol = symbols[i % len(symbols)]
        payload = create_webhook_payload(symbol)
        task = send_single_webhook(session, payload)
        tasks.append(task)
    
    # Execute all webhooks simultaneously
    start_time = time.time()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    batch_time = time.time() - start_time
    
    # Process results
    successful = sum(1 for r in results if isinstance(r, dict) and r.get("success", False))
    failed = len(results) - successful
    
    print(f"✅ Batch completed in {batch_time:.3f}s: {successful} successful, {failed} failed")
    
    return results

async def test_high_volume():
    """Test high-volume webhook processing"""
    print("🚀 Starting High-Volume Webhook Test")
    print("=" * 50)
    
    # Test configuration
    print(f"📊 Test Configuration:")
    print(f"   Webhook URL: {WEBHOOK_URL}")
    print(f"   Total webhooks: {NUM_WEBHOOKS}")
    print(f"   Batch size: {NUM_WEBHOOKS}")
    print(f"   Delay between batches: {DELAY_BETWEEN_BATCHES}s")
    print()
    
    # Create HTTP session
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        # Test single webhook first
        print("🧪 Testing single webhook...")
        single_result = await send_single_webhook(session, create_webhook_payload("AAPL"))
        
        if single_result["success"]:
            print(f"✅ Single webhook test passed: {single_result['response_time']:.3f}s")
        else:
            print(f"❌ Single webhook test failed: {single_result['response_data']}")
            return
        
        print()
        
        # Test high-volume batch
        print("🔥 Testing high-volume batch...")
        start_time = time.time()
        
        batch_results = await send_webhook_batch(session, NUM_WEBHOOKS)
        
        total_time = time.time() - start_time
        
        # Analyze results
        successful_webhooks = [r for r in batch_results if isinstance(r, dict) and r.get("success", False)]
        failed_webhooks = [r for r in batch_results if isinstance(r, dict) and not r.get("success", False)]
        
        if successful_webhooks:
            avg_response_time = sum(r["response_time"] for r in successful_webhooks) / len(successful_webhooks)
            min_response_time = min(r["response_time"] for r in successful_webhooks)
            max_response_time = max(r["response_time"] for r in successful_webhooks)
        else:
            avg_response_time = min_response_time = max_response_time = 0
        
        # Print results
        print("\n📊 Test Results:")
        print("=" * 30)
        print(f"Total webhooks sent: {NUM_WEBHOOKS}")
        print(f"Successful: {len(successful_webhooks)}")
        print(f"Failed: {len(failed_webhooks)}")
        print(f"Success rate: {(len(successful_webhooks) / NUM_WEBHOOKS) * 100:.1f}%")
        print(f"Total time: {total_time:.3f}s")
        print(f"Throughput: {NUM_WEBHOOKS / total_time:.1f} webhooks/second")
        print()
        
        if successful_webhooks:
            print(f"Response Time Analysis:")
            print(f"  Average: {avg_response_time:.3f}s")
            print(f"  Minimum: {min_response_time:.3f}s")
            print(f"  Maximum: {max_response_time:.3f}s")
            print()
        
        if failed_webhooks:
            print(f"Failed Webhooks:")
            for i, failed in enumerate(failed_webhooks[:5]):  # Show first 5 failures
                print(f"  {i+1}. Status: {failed.get('status_code', 'Unknown')}")
                print(f"     Error: {failed.get('response_data', {}).get('error', 'Unknown error')}")
                print(f"     Payload: {failed.get('payload', {}).get('symbol', 'Unknown')}")
            if len(failed_webhooks) > 5:
                print(f"  ... and {len(failed_webhooks) - 5} more failures")
        
        # Performance assessment
        print("\n🎯 Performance Assessment:")
        if len(successful_webhooks) == NUM_WEBHOOKS:
            print("✅ EXCELLENT: All webhooks processed successfully")
        elif len(successful_webhooks) >= NUM_WEBHOOKS * 0.9:
            print("🟢 GOOD: 90%+ webhooks processed successfully")
        elif len(successful_webhooks) >= NUM_WEBHOOKS * 0.7:
            print("🟡 ACCEPTABLE: 70%+ webhooks processed successfully")
        else:
            print("🔴 POOR: Less than 70% webhooks processed successfully")
        
        if avg_response_time < 0.1:
            print("✅ EXCELLENT: Sub-100ms response times")
        elif avg_response_time < 0.5:
            print("🟢 GOOD: Sub-500ms response times")
        elif avg_response_time < 1.0:
            print("🟡 ACCEPTABLE: Sub-1s response times")
        else:
            print("🔴 POOR: Response times over 1 second")

async def main():
    """Main test function"""
    try:
        await test_high_volume()
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 