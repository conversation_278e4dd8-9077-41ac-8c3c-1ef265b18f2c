-- Create table for storing automated analysis results
CREATE TABLE IF NOT EXISTS analysis_results (
    id SERIAL PRIMARY KEY,
    analysis_timestamp VARCHAR(50) NOT NULL,
    insights JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for fast querying
CREATE INDEX IF NOT EXISTS idx_analysis_results_timestamp ON analysis_results(analysis_timestamp);
CREATE INDEX IF NOT EXISTS idx_analysis_results_created_at ON analysis_results(created_at DESC);

-- Create a view for easy analysis querying
CREATE OR REPLACE VIEW latest_analysis AS
SELECT 
    analysis_timestamp,
    insights,
    created_at,
    insights->>'symbol_activity' as symbol_activity,
    insights->>'market_sentiment' as market_sentiment,
    insights->>'alert_frequency' as alert_frequency
FROM analysis_results 
ORDER BY created_at DESC 
LIMIT 1;

-- Grant permissions
GRANT ALL PRIVILEGES ON analysis_results TO tradingview_user;
GRANT ALL PRIVILEGES ON latest_analysis TO tradingview_user; 