"""
Database Package
Provides standardized database access patterns for the tradingview-ingest service
"""

from .manager import DatabaseManager, db_manager
from .operations import (
    insert_data,
    update_data,
    delete_data,
    query_data,
    ensure_table_exists,
    ensure_record_exists
)

__all__ = [
    'DatabaseManager',
    'db_manager',
    'insert_data',
    'update_data',
    'delete_data',
    'query_data',
    'ensure_table_exists',
    'ensure_record_exists'
]
