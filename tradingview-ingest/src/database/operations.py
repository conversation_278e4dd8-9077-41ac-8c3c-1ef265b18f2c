"""
Database Operations Module
Provides standardized database operations
"""

import time
from typing import Dict, Any, Optional, List, Union, Tuple
import structlog
import json

from .manager import db_manager
from ..error_handler import DatabaseError, with_async_error_handling

logger = structlog.get_logger()

@with_async_error_handling(error_mapping={Exception: DatabaseError})
async def insert_data(table: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Insert data into a database table
    
    Args:
        table: Table name
        data: Data to insert
        
    Returns:
        Inserted record or None if failed
    """
    start_time = time.time()
    
    try:
        client = await db_manager.get_client()
        if not client:
            logger.warning(f"Database client not available, skipping insert to {table}")
            return None
        
        # Handle Supabase client
        if hasattr(client, 'table'):
            response = await client.table(table).insert(data).execute()
            
            if hasattr(response, 'data') and response.data:
                logger.info(f"Data inserted into {table}", 
                          duration_ms=round((time.time() - start_time) * 1000, 2))
                return response.data[0]
            else:
                logger.warning(f"No data returned from insert to {table}")
                return None
        
        # Handle PostgreSQL pool
        else:
            # Build query dynamically
            columns = list(data.keys())
            placeholders = [f"${i+1}" for i in range(len(columns))]
            values = [data[col] for col in columns]
            
            query = f"""
            INSERT INTO {table} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            RETURNING *
            """
            
            # Execute query
            result = await db_manager.execute_query(query, *values)
            
            if result:
                logger.info(f"Data inserted into {table}", 
                          duration_ms=round((time.time() - start_time) * 1000, 2))
                return result[0]
            else:
                logger.warning(f"No data returned from insert to {table}")
                return None
                
    except Exception as e:
        logger.error(f"Failed to insert data into {table}", error=str(e))
        raise DatabaseError(f"Insert operation failed: {str(e)}", 
                          details={"table": table, "data": data})

@with_async_error_handling(error_mapping={Exception: DatabaseError})
async def update_data(table: str, data: Dict[str, Any], match_column: str, match_value: Any) -> Optional[Dict[str, Any]]:
    """
    Update data in a database table
    
    Args:
        table: Table name
        data: Data to update
        match_column: Column to match for update
        match_value: Value to match for update
        
    Returns:
        Updated record or None if failed
    """
    start_time = time.time()
    
    try:
        client = await db_manager.get_client()
        if not client:
            logger.warning(f"Database client not available, skipping update to {table}")
            return None
        
        # Handle Supabase client
        if hasattr(client, 'table'):
            response = await client.table(table).update(data).eq(match_column, match_value).execute()
            
            if hasattr(response, 'data') and response.data:
                logger.info(f"Data updated in {table}", 
                          duration_ms=round((time.time() - start_time) * 1000, 2))
                return response.data[0]
            else:
                logger.warning(f"No data returned from update to {table}")
                return None
        
        # Handle PostgreSQL pool
        else:
            # Build query dynamically
            set_clauses = [f"{col} = ${i+1}" for i, col in enumerate(data.keys())]
            values = list(data.values())
            
            query = f"""
            UPDATE {table}
            SET {', '.join(set_clauses)}
            WHERE {match_column} = ${len(values) + 1}
            RETURNING *
            """
            
            # Execute query
            result = await db_manager.execute_query(query, *(values + [match_value]))
            
            if result:
                logger.info(f"Data updated in {table}", 
                          duration_ms=round((time.time() - start_time) * 1000, 2))
                return result[0]
            else:
                logger.warning(f"No data returned from update to {table}")
                return None
                
    except Exception as e:
        logger.error(f"Failed to update data in {table}", error=str(e))
        raise DatabaseError(f"Update operation failed: {str(e)}", 
                          details={"table": table, "data": data, "match_column": match_column})

@with_async_error_handling(error_mapping={Exception: DatabaseError})
async def delete_data(table: str, match_column: str, match_value: Any) -> bool:
    """
    Delete data from a database table
    
    Args:
        table: Table name
        match_column: Column to match for deletion
        match_value: Value to match for deletion
        
    Returns:
        True if successful, False otherwise
    """
    start_time = time.time()
    
    try:
        client = await db_manager.get_client()
        if not client:
            logger.warning(f"Database client not available, skipping delete from {table}")
            return False
        
        # Handle Supabase client
        if hasattr(client, 'table'):
            response = await client.table(table).delete().eq(match_column, match_value).execute()
            
            success = hasattr(response, 'data')
            logger.info(f"Data deleted from {table}", 
                      success=success,
                      duration_ms=round((time.time() - start_time) * 1000, 2))
            return success
        
        # Handle PostgreSQL pool
        else:
            query = f"""
            DELETE FROM {table}
            WHERE {match_column} = $1
            """
            
            # Execute query
            await db_manager.execute_query(query, match_value)
            
            logger.info(f"Data deleted from {table}", 
                      duration_ms=round((time.time() - start_time) * 1000, 2))
            return True
                
    except Exception as e:
        logger.error(f"Failed to delete data from {table}", error=str(e))
        raise DatabaseError(f"Delete operation failed: {str(e)}", 
                          details={"table": table, "match_column": match_column})

@with_async_error_handling(error_mapping={Exception: DatabaseError})
async def query_data(table: str, columns: List[str] = None, 
                   filters: Dict[str, Any] = None, 
                   order_by: str = None, 
                   limit: int = None) -> List[Dict[str, Any]]:
    """
    Query data from a database table
    
    Args:
        table: Table name
        columns: Columns to select (None for all)
        filters: Filter conditions
        order_by: Column to order by
        limit: Maximum number of records to return
        
    Returns:
        List of records
    """
    start_time = time.time()
    
    try:
        client = await db_manager.get_client()
        if not client:
            logger.warning(f"Database client not available, skipping query to {table}")
            return []
        
        # Handle Supabase client
        if hasattr(client, 'table'):
            query = client.table(table)
            
            # Select columns
            if columns:
                query = query.select(','.join(columns))
            
            # Apply filters
            if filters:
                for column, value in filters.items():
                    query = query.eq(column, value)
            
            # Apply order by
            if order_by:
                query = query.order(order_by)
            
            # Apply limit
            if limit:
                query = query.limit(limit)
            
            # Execute query
            response = await query.execute()
            
            if hasattr(response, 'data'):
                logger.info(f"Data queried from {table}", 
                          record_count=len(response.data),
                          duration_ms=round((time.time() - start_time) * 1000, 2))
                return response.data
            else:
                logger.warning(f"No data returned from query to {table}")
                return []
        
        # Handle PostgreSQL pool
        else:
            # Build query dynamically
            select_clause = '*' if not columns else ', '.join(columns)
            where_clause = ''
            order_clause = ''
            limit_clause = ''
            values = []
            
            # Apply filters
            if filters:
                conditions = []
                for i, (column, value) in enumerate(filters.items()):
                    conditions.append(f"{column} = ${i+1}")
                    values.append(value)
                
                where_clause = f"WHERE {' AND '.join(conditions)}"
            
            # Apply order by
            if order_by:
                order_clause = f"ORDER BY {order_by}"
            
            # Apply limit
            if limit:
                limit_clause = f"LIMIT {limit}"
            
            query = f"""
            SELECT {select_clause}
            FROM {table}
            {where_clause}
            {order_clause}
            {limit_clause}
            """
            
            # Execute query
            result = await db_manager.execute_query(query, *values)
            
            logger.info(f"Data queried from {table}", 
                      record_count=len(result),
                      duration_ms=round((time.time() - start_time) * 1000, 2))
            return result
                
    except Exception as e:
        logger.error(f"Failed to query data from {table}", error=str(e))
        raise DatabaseError(f"Query operation failed: {str(e)}", 
                          details={"table": table, "filters": filters})

@with_async_error_handling(error_mapping={Exception: DatabaseError})
async def ensure_table_exists(table: str, schema: Dict[str, str]) -> bool:
    """
    Ensure a table exists in the database
    
    Args:
        table: Table name
        schema: Table schema as column_name: column_type
        
    Returns:
        True if table exists or was created
    """
    try:
        client = await db_manager.get_client()
        if not client:
            logger.warning(f"Database client not available, skipping table creation: {table}")
            return False
        
        # Handle PostgreSQL pool
        if not hasattr(client, 'table'):
            # Check if table exists
            check_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = $1
            )
            """
            
            result = await db_manager.execute_query(check_query, table)
            table_exists = result[0]['exists'] if result else False
            
            if not table_exists:
                # Create table
                columns = [f"{col} {col_type}" for col, col_type in schema.items()]
                create_query = f"""
                CREATE TABLE {table} (
                    {', '.join(columns)}
                )
                """
                
                await db_manager.execute_query(create_query)
                logger.info(f"Table created: {table}")
            
            return True
        
        # Supabase doesn't support direct table creation through API
        else:
            logger.warning("Table creation not supported with Supabase client")
            return False
            
    except Exception as e:
        logger.error(f"Failed to ensure table exists: {table}", error=str(e))
        raise DatabaseError(f"Table creation failed: {str(e)}", 
                          details={"table": table, "schema": schema})

@with_async_error_handling(error_mapping={Exception: DatabaseError})
async def ensure_record_exists(table: str, match_column: str, match_value: Any, 
                             default_data: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
    """
    Ensure a record exists in the database, creating it if not
    
    Args:
        table: Table name
        match_column: Column to match
        match_value: Value to match
        default_data: Default data to insert if record doesn't exist
        
    Returns:
        Tuple of (record, was_created)
    """
    try:
        # Query for existing record
        filters = {match_column: match_value}
        existing_records = await query_data(table, filters=filters, limit=1)
        
        if existing_records:
            return existing_records[0], False
        
        # Create new record
        default_data[match_column] = match_value
        new_record = await insert_data(table, default_data)
        
        return new_record, True
            
    except Exception as e:
        logger.error(f"Failed to ensure record exists in {table}", error=str(e))
        raise DatabaseError(f"Record ensure operation failed: {str(e)}", 
                          details={"table": table, "match_column": match_column})
