"""
Database Manager Module
Provides centralized database connection management using Supabase only
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional, List, Union
import structlog
import asyncpg
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from ..config import config
from ..error_handler import DatabaseError, with_async_error_handling
from ..supabase_client import supabase_client

logger = structlog.get_logger()

class DatabaseManager:
    """Manages database connections and provides access to database clients"""
    
    def __init__(self):
        """Initialize database manager"""
        self.initialized = False
        self.supabase_client = None
        self.connection_id = str(uuid.uuid4())[:8]
        
        # Connection settings
        self.use_supabase = config.use_supabase
        self.supabase_url = config.supabase_url
        self.supabase_key = config.supabase_key
        
        # Debug tracking
        try:
            from ..debug.database import connection_monitor
            connection_monitor.register_connection(
                self.connection_id,
                "database_manager",
                {
                    "use_supabase": self.use_supabase,
                    "has_supabase_url": bool(self.supabase_url)
                }
            )
        except ImportError:
            pass
    
    @with_async_error_handling(error_mapping={Exception: DatabaseError})
    async def initialize(self) -> bool:
        """Initialize database connections - Supabase only"""
        try:
            # Import here to avoid circular imports
            from ..supabase_client import supabase_client
            
            self.supabase_client = supabase_client
            
            # Initialize Supabase client if not already initialized
            if self.supabase_client and not self.supabase_client.initialized:
                await self.supabase_client.initialize()
            
            self.initialized = True
            logger.info("Database manager initialized with Supabase only", 
                      use_supabase=self.use_supabase)
            return True
            
        except Exception as e:
            logger.error("Failed to initialize database manager", error=str(e))
            self.initialized = False
            return False
    
    @with_async_error_handling(error_mapping={Exception: DatabaseError})
    async def get_client(self) -> Optional[Any]:
        """Get the appropriate database client - Supabase only"""
        if not self.initialized:
            await self.initialize()
            
        if self.use_supabase and self.supabase_client:
            return self.supabase_client
        else:
            return None
    
    @with_async_error_handling(error_mapping={Exception: DatabaseError})
    async def close(self) -> None:
        """Close all database connections"""
        if self.supabase_client:
            await self.supabase_client.close()
            
        self.initialized = False
        logger.info("Database connections closed")
    
    @with_async_error_handling(error_mapping={Exception: DatabaseError})
    async def get_health_status(self) -> Dict[str, bool]:
        """Get database health status - Supabase only"""
        status = {
            "initialized": self.initialized,
            "supabase": False
        }
        
        # Check Supabase connection
        if self.supabase_client and self.supabase_client.initialized:
            try:
                # Simple health check query
                result = await self.supabase_client.client.get("/rest/v1/webhooks?limit=1")
                status["supabase"] = result.status_code == 200
            except Exception:
                status["supabase"] = False
        
        return status

# Global instance
db_manager = DatabaseManager()
