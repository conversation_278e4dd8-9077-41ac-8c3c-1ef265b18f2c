"""
Simple High-Throughput TradingView Ingestion System
Handles multiple tickers per webhook with minimal processing
"""

import json
import time
from typing import Dict, Any, List
from fastapi import FastAP<PERSON>, Request, HTTPException, status
from fastapi.responses import JSONResponse
import structlog
from prometheus_client import Counter, Histogram, Gauge

# Initialize FastAPI app
app = FastAPI(title="TradingView Simple Ingestion", version="1.0.0")

# Prometheus metrics
webhook_requests_total = Counter('webhook_requests_total', 'Total webhook requests received')
tickers_processed_total = Counter('tickers_processed_total', 'Total tickers processed')
webhook_processing_duration = Histogram('webhook_processing_duration_seconds', 'Webhook processing duration')
webhook_queue_size = Gauge('webhook_queue_size', 'Current webhook queue size')

logger = structlog.get_logger()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "simple-ingestion", "timestamp": time.time()}

@app.get("/metrics")
async def get_metrics():
    """Get Prometheus metrics"""
    return {
        "webhook_requests_total": webhook_requests_total._value.sum(),
        "tickers_processed_total": tickers_processed_total._value.sum(),
        "webhook_queue_size": 0  # Simple system, no queue
    }

@app.post("/webhook/tradingview")
async def tradingview_webhook(request: Request):
    """
    Simple webhook endpoint for TradingView data
    Handles multiple tickers per webhook with minimal processing
    """
    start_time = time.time()
    
    try:
        # Get webhook payload
        payload = await request.json()
        logger.info("Webhook received", payload_size=len(str(payload)))
        
        # Increment request counter
        webhook_requests_total.inc()
        
        # Process the webhook data
        ticker_count = await process_webhook_data(payload)
        
        processing_time = time.time() - start_time
        webhook_processing_duration.observe(processing_time)
        
        logger.info("Webhook processed successfully", 
                   ticker_count=ticker_count,
                   processing_time=processing_time)
        
        return {
            "status": "success",
            "message": f"Processed {ticker_count} tickers",
            "processing_time": processing_time,
            "ticker_count": ticker_count
        }
        
    except json.JSONDecodeError as e:
        logger.error("Invalid JSON payload", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid JSON payload"
        )
    except Exception as e:
        logger.error("Webhook processing error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

async def process_webhook_data(payload: Dict[str, Any]) -> int:
    """
    Process webhook data with multiple tickers
    
    Args:
        payload: Raw webhook data from TradingView
        
    Returns:
        Number of tickers processed
    """
    ticker_count = 0
    
    # Handle different payload formats
    if isinstance(payload, dict):
        # Single ticker or multi-ticker format
        if "tickers" in payload:
            # Multi-ticker format: {"tickers": [{"symbol": "AAPL", ...}, {"symbol": "SHOP", ...}]}
            tickers = payload["tickers"]
            if isinstance(tickers, list):
                for ticker_data in tickers:
                    if await store_ticker_data(ticker_data, payload):
                        ticker_count += 1
        elif "symbol" in payload:
            # Single ticker format: {"symbol": "AAPL", "price": 150.25, ...}
            if await store_ticker_data(payload, payload):
                ticker_count += 1
        else:
            # Unknown format, try to extract ticker info
            logger.warning("Unknown payload format", payload_keys=list(payload.keys()))
            if await store_raw_data(payload):
                ticker_count += 1
    
    elif isinstance(payload, list):
        # Array format: [{"symbol": "AAPL", ...}, {"symbol": "SHOP", ...}]
        for ticker_data in payload:
            if await store_ticker_data(ticker_data, {"batch": True}):
                ticker_count += 1
    
    # Increment ticker counter
    tickers_processed_total.inc(ticker_count)
    
    return ticker_count

async def store_ticker_data(ticker_data: Dict[str, Any], metadata: Dict[str, Any]) -> bool:
    """
    Store individual ticker data
    
    Args:
        ticker_data: Data for a single ticker
        metadata: Additional metadata from the webhook
        
    Returns:
        True if stored successfully
    """
    try:
        # Extract basic ticker info
        symbol = ticker_data.get("symbol", "UNKNOWN")
        timestamp = ticker_data.get("timestamp") or ticker_data.get("time") or time.time()
        
        # Create storage record
        record = {
            "symbol": symbol,
            "timestamp": timestamp,
            "raw_data": ticker_data,
            "metadata": metadata,
            "ingested_at": time.time()
        }
        
        # Store in database (simplified for now)
        # In production, this would use async database operations
        logger.info("Ticker data stored", 
                   symbol=symbol,
                   timestamp=timestamp,
                   data_keys=list(ticker_data.keys()))
        
        return True
        
    except Exception as e:
        logger.error("Failed to store ticker data", 
                    symbol=ticker_data.get("symbol", "UNKNOWN"),
                    error=str(e))
        return False

async def store_raw_data(raw_data: Dict[str, Any]) -> bool:
    """
    Store raw webhook data when ticker info can't be extracted
    
    Args:
        raw_data: Raw webhook data
        
    Returns:
        True if stored successfully
    """
    try:
        record = {
            "symbol": "UNKNOWN",
            "timestamp": time.time(),
            "raw_data": raw_data,
            "metadata": {"source": "raw_webhook"},
            "ingested_at": time.time()
        }
        
        logger.info("Raw data stored", data_keys=list(raw_data.keys()))
        return True
        
    except Exception as e:
        logger.error("Failed to store raw data", error=str(e))
        return False

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions"""
    logger.warning("HTTP exception", 
                   status_code=exc.status_code,
                   detail=exc.detail)
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error("Unhandled exception", error=str(exc))
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"error": "Internal server error"}
    ) 