"""
Configuration module for tradingview-ingest service.
Centralizes all configuration settings.
"""
import os
from pathlib import Path

# Load configuration from environment variables with defaults
class Config:
    # Webhook settings
    WEBHOOK_SECRET = os.getenv("WEBHOOK_SECRET", "default_webhook_secret")
    WEBHOOK_TIMEOUT = int(os.getenv("WEBHOOK_TIMEOUT", "30"))
    
    # Redis settings
    REDIS_HOST = os.getenv("REDIS_HOST", "redis")
    REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB = int(os.getenv("REDIS_DB", "0"))
    REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "")
    
    # Database settings
    DATABASE_URL = os.getenv("DATABASE_URL", "")
    
    # Rate limiting
    RATE_LIMIT = int(os.getenv("RATE_LIMIT", "100"))  # requests per minute
    
    # Logging
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    
    # Feature flags
    ENABLE_METRICS = os.getenv("ENABLE_METRICS", "true").lower() == "true"
    ENABLE_QUEUE = os.getenv("ENABLE_QUEUE", "true").lower() == "true"

# Create config instance
config = Config()