"""
Legacy Feature Flags
Controls the usage of legacy code components
"""

import os
from typing import Dict, Any, Optional, Union, List
import structlog

logger = structlog.get_logger()

class LegacyFeatureFlags:
    """Feature flags for controlling legacy code usage"""
    
    def __init__(self):
        """Initialize feature flags from environment variables"""
        # Default is to enable legacy code for backward compatibility
        self.USE_LEGACY_PARSERS = self._get_bool("USE_LEGACY_PARSERS", True)
        self.USE_LEGACY_STORAGE = self._get_bool("USE_LEGACY_STORAGE", True)
        self.USE_LEGACY_VISUALIZERS = self._get_bool("USE_LEGACY_VISUALIZERS", True)
        self.USE_LEGACY_IMPORTS = self._get_bool("USE_LEGACY_IMPORTS", True)
        
        # Log the status of legacy feature flags
        self._log_feature_flags()
    
    def _get_bool(self, name: str, default: bool) -> bool:
        """Get boolean value from environment variable"""
        value = os.environ.get(name)
        if value is None:
            return default
        return value.lower() in ("yes", "true", "t", "1")
    
    def _log_feature_flags(self):
        """Log the status of all legacy feature flags"""
        logger.info(
            "Legacy feature flags initialized",
            USE_LEGACY_PARSERS=self.USE_LEGACY_PARSERS,
            USE_LEGACY_STORAGE=self.USE_LEGACY_STORAGE,
            USE_LEGACY_VISUALIZERS=self.USE_LEGACY_VISUALIZERS,
            USE_LEGACY_IMPORTS=self.USE_LEGACY_IMPORTS
        )
    
    def is_enabled(self, flag_name: str) -> bool:
        """Check if a specific legacy feature flag is enabled"""
        if hasattr(self, flag_name):
            return getattr(self, flag_name)
        return False

# Global instance
legacy_flags = LegacyFeatureFlags()

# Convenience functions
def use_legacy_parsers() -> bool:
    """Check if legacy parsers should be used"""
    return legacy_flags.USE_LEGACY_PARSERS

def use_legacy_storage() -> bool:
    """Check if legacy storage methods should be used"""
    return legacy_flags.USE_LEGACY_STORAGE

def use_legacy_visualizers() -> bool:
    """Check if legacy visualizers should be used"""
    return legacy_flags.USE_LEGACY_VISUALIZERS

def use_legacy_imports() -> bool:
    """Check if legacy imports should be used"""
    return legacy_flags.USE_LEGACY_IMPORTS
