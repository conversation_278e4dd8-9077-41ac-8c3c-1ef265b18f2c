"""
Webhook Core Processing Logic
Centralized webhook processing functionality for TradingView alerts
"""

import asyncio
import json
import time
import logging
from typing import Dict, Any, Optional, Union
import structlog

# Import error handling
from .error_handler import (
    with_async_error_handling, 
    ParsingError, 
    ProcessingError,
    DatabaseError
)

from .data_parser import DataParser
from .storage_manager import StorageManager
from .visualizer import get_webhook_visualizer, create_webhook_visualizer
from .models import WebhookAlert, WebhookData

logger = structlog.get_logger()

class WebhookCore:
    """Core webhook processing functionality shared across components"""
    
    def __init__(self):
        """Initialize the webhook core processor"""
        self.storage_manager = None
        self.data_parser = None
        self.initialized = False
    
    async def initialize(self):
        """Initialize core components"""
        if self.initialized:
            return True
            
        try:
            from .storage_manager import StorageManager
            from .data_parser import DataParser
            
            self.storage_manager = StorageManager()
            await self.storage_manager.initialize()
            
            self.data_parser = DataParser()
            
            self.initialized = True
            logger.info("Webhook core processor initialized successfully")
            return True
            
        except Exception as e:
            logger.error("Failed to initialize webhook core processor", error=str(e))
            return False
    
    async def process_webhook(self, webhook_data_raw: Union[str, Dict[str, Any]], webhook_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a webhook from raw data
        
        Args:
            webhook_data_raw: Raw webhook data (string or dict)
            webhook_id: Optional webhook ID
            
        Returns:
            Dict with processing results
        """
        start_time = time.time()
        
        # Ensure initialization
        if not self.initialized:
            await self.initialize()
        
        # Parse webhook data if it's a string
        if isinstance(webhook_data_raw, str):
            try:
                webhook_data = json.loads(webhook_data_raw)
            except json.JSONDecodeError as e:
                logger.error("Failed to parse webhook JSON", error=str(e))
                return {"success": False, "error": "Invalid JSON", "webhook_id": webhook_id}
        else:
            webhook_data = webhook_data_raw
        
        # Get or generate webhook ID
        if webhook_id is None:
            webhook_id = webhook_data.get("webhook_id", f"webhook_{int(time.time() * 1000)}")
        
        # Get webhook visualizer if it exists
        visualizer = await get_webhook_visualizer(webhook_id)
        
        try:
            # Extract payload
            payload = webhook_data.get("payload", {})
            raw_text = webhook_data.get("raw_text", "")
            
            # Log data processing in visualizer if available
            if visualizer:
                await visualizer.log_data_flow("webhook_processor_received", {
                    "webhook_id": webhook_id,
                    "timestamp": time.time(),
                    "payload_size": len(json.dumps(payload)) if isinstance(payload, (dict, list)) else len(str(payload))
                })
            
            # Parse the webhook data
            parsed_data = await self._parse_webhook_payload(payload, raw_text)
            
            if parsed_data:
                # Log successful parsing in visualizer
                if visualizer:
                    await visualizer.log_data_flow("webhook_parsed", {
                        "success": True,
                        "data_type": type(parsed_data).__name__,
                        "timestamp": time.time()
                    })
                
                # Store the parsed data
                await self._store_webhook_data(webhook_id, webhook_data, parsed_data, visualizer)
                
                # Record success metrics
                processing_time = time.time() - start_time
                
                # Log completion in visualizer
                if visualizer:
                    await visualizer.log_data_flow("webhook_processing_complete", {
                        "success": True,
                        "processing_time_ms": round(processing_time * 1000, 2),
                        "timestamp": time.time()
                    })
                
                logger.info("Webhook processed successfully", 
                           webhook_id=webhook_id,
                           symbol=getattr(parsed_data, 'symbol', 'unknown'),
                           processing_time_ms=round(processing_time * 1000, 2))
                           
                return {
                    "success": True,
                    "webhook_id": webhook_id,
                    "parsed_data": parsed_data,
                    "processing_time": processing_time
                }
            else:
                # Log parsing failure in visualizer
                if visualizer:
                    await visualizer.log_error("parsing", Exception("Failed to parse webhook data"))
                
                logger.warning("Failed to parse webhook data", webhook_id=webhook_id)
                return {"success": False, "error": "Failed to parse webhook data", "webhook_id": webhook_id}
                
        except Exception as e:
            # Log error in visualizer if available
            if visualizer:
                await visualizer.log_error("processing", e)
                
            logger.error("Failed to process webhook", error=str(e))
            return {"success": False, "error": str(e), "webhook_id": webhook_id}
    
    @with_async_error_handling(error_mapping={Exception: ParsingError}, reraise=False)
    async def _parse_webhook_payload(self, payload: Any, raw_text: str) -> Optional[Any]:
        """Parse webhook payload into structured data with format detection"""
        # Try to parse as structured data first
        if isinstance(payload, dict) and payload:
            # Use the data parser for structured data
            return self.data_parser.parse_webhook_data(payload)
        
        # Fallback to text parsing for raw text alerts
        elif raw_text and isinstance(raw_text, str):
            # Try to parse as Pine Script alert format
            from .text_parser import PineScriptAlertParser
            text_parser = PineScriptAlertParser()
            return text_parser.parse_alert(raw_text)
        
        else:
            logger.warning("Unable to parse webhook payload", 
                         payload_type=type(payload).__name__,
                         has_raw_text=bool(raw_text))
            return None
    
    @with_async_error_handling(error_mapping={Exception: DatabaseError})
    async def _store_webhook_data(self, webhook_id: str, webhook_data: Dict[str, Any], parsed_data: Any, visualizer=None):
        """Store webhook data in database"""
        # Log storage start in visualizer if available
        if visualizer:
            await visualizer.log_data_flow("webhook_storage_start", {
                "webhook_id": webhook_id,
                "timestamp": time.time()
            })
            
        # Store raw webhook data in local storage
        await self.storage_manager.save_raw_webhook(webhook_id, webhook_data)
        
        # Prepare data for storage
        if hasattr(parsed_data, 'to_dict'):
            parsed_dict = parsed_data.to_dict()
        else:
            # Use as-is if it's already a dict
            parsed_dict = parsed_data
            
        # Store in local database
        success = await self.storage_manager.store_webhook_alert(parsed_dict)
        if not success:
            error_msg = "Failed to store webhook alert in local storage"
            logger.error(error_msg, webhook_id=webhook_id)
            
            # Log storage failure in visualizer
            if visualizer:
                await visualizer.log_error("storage", DatabaseError(error_msg, {"webhook_id": webhook_id}))
            
            raise DatabaseError(error_msg, {"webhook_id": webhook_id})
        
        # Ensure ticker exists in database if symbol is available
        if hasattr(parsed_data, 'symbol'):
            symbol = parsed_data.symbol
            if symbol and symbol != "UNKNOWN":
                await self.storage_manager.ensure_ticker_exists(symbol)
                logger.info("Ticker ensured in database", symbol=symbol, webhook_id=webhook_id)
        
        # Log storage completion in visualizer
        if visualizer:
            await visualizer.log_data_flow("webhook_storage_complete", {
                "success": True,
                "timestamp": time.time()
            })

# Global instance for shared use
webhook_core = WebhookCore()

async def process_webhook(webhook_data: Union[str, Dict[str, Any]], webhook_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Process a webhook (convenience function using global instance)
    
    Args:
        webhook_data: Raw webhook data (string or dict)
        webhook_id: Optional webhook ID
        
    Returns:
        Dict with processing results
    """
    return await webhook_core.process_webhook(webhook_data, webhook_id)

async def generate_webhook_id() -> str:
    """Generate a unique webhook ID"""
    return f"webhook_{int(time.time() * 1000)}"
