"""
Error Handler Module
Provides consistent error handling across components
"""

import logging
import traceback
import functools
import asyncio
from typing import Any, Callable, Dict, Optional, Type, TypeVar, Union, cast

import structlog

logger = structlog.get_logger()

# Type variables for function signatures
F = TypeVar('F', bound=Callable[..., Any])
AsyncF = TypeVar('AsyncF', bound=Callable[..., Any])

class ErrorLevel:
    """Error severity levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ErrorCategory:
    """Error categories for classification"""
    NETWORK = "network"
    DATABASE = "database"
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    PARSING = "parsing"
    PROCESSING = "processing"
    EXTERNAL_API = "external_api"
    INTERNAL = "internal"
    UNKNOWN = "unknown"

class AppError(Exception):
    """Base application error class"""
    
    def __init__(
        self, 
        message: str, 
        category: str = ErrorCategory.UNKNOWN,
        level: str = ErrorLevel.ERROR,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        self.message = message
        self.category = category
        self.level = level
        self.details = details or {}
        self.original_exception = original_exception
        super().__init__(message)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary representation"""
        result = {
            "message": self.message,
            "category": self.category,
            "level": self.level,
            "details": self.details
        }
        
        if self.original_exception:
            result["original_error"] = str(self.original_exception)
            result["traceback"] = traceback.format_exception(
                type(self.original_exception),
                self.original_exception,
                self.original_exception.__traceback__
            )
            
        return result

class NetworkError(AppError):
    """Network-related errors"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.NETWORK, ErrorLevel.ERROR, details, original_exception)

class DatabaseError(AppError):
    """Database-related errors"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.DATABASE, ErrorLevel.ERROR, details, original_exception)

class ValidationError(AppError):
    """Validation errors"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.VALIDATION, ErrorLevel.WARNING, details, original_exception)

class ParsingError(AppError):
    """Data parsing errors"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.PARSING, ErrorLevel.WARNING, details, original_exception)

class ProcessingError(AppError):
    """Data processing errors"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.PROCESSING, ErrorLevel.ERROR, details, original_exception)

class ExternalAPIError(AppError):
    """External API errors"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.EXTERNAL_API, ErrorLevel.ERROR, details, original_exception)

def handle_error(
    error: Exception,
    context: Dict[str, Any] = None,
    log: bool = True,
    reraise: bool = True,
    error_mapping: Dict[Type[Exception], Type[AppError]] = None
) -> Optional[AppError]:
    """
    Handle an exception with consistent logging and conversion
    
    Args:
        error: The exception to handle
        context: Additional context information
        log: Whether to log the error
        reraise: Whether to re-raise the error after handling
        error_mapping: Mapping of exception types to AppError types
        
    Returns:
        Converted AppError if not reraising, otherwise None
    """
    context = context or {}
    error_mapping = error_mapping or {}
    
    # Convert to AppError if not already
    if isinstance(error, AppError):
        app_error = error
    else:
        # Check if we have a specific mapping for this error type
        error_type = type(error)
        if error_type in error_mapping:
            app_error_class = error_mapping[error_type]
            app_error = app_error_class(str(error), context, error)
        else:
            # Default to generic AppError
            app_error = AppError(str(error), ErrorCategory.UNKNOWN, ErrorLevel.ERROR, context, error)
    
    # Log the error if requested
    if log:
        log_kwargs = {
            "error_message": app_error.message,
            "error_category": app_error.category,
            "error_level": app_error.level,
            **app_error.details
        }
        
        if app_error.level == ErrorLevel.DEBUG:
            logger.debug(f"Error: {app_error.message}", **log_kwargs)
        elif app_error.level == ErrorLevel.INFO:
            logger.info(f"Error: {app_error.message}", **log_kwargs)
        elif app_error.level == ErrorLevel.WARNING:
            logger.warning(f"Error: {app_error.message}", **log_kwargs)
        elif app_error.level == ErrorLevel.CRITICAL:
            logger.critical(f"Error: {app_error.message}", **log_kwargs)
        else:
            # Default to ERROR level
            logger.error(f"Error: {app_error.message}", **log_kwargs)
    
    # Re-raise if requested
    if reraise:
        raise app_error
    
    return app_error

def with_error_handling(
    error_mapping: Dict[Type[Exception], Type[AppError]] = None,
    log: bool = True,
    reraise: bool = True
) -> Callable[[F], F]:
    """
    Decorator for consistent error handling in synchronous functions
    
    Args:
        error_mapping: Mapping of exception types to AppError types
        log: Whether to log errors
        reraise: Whether to re-raise errors after handling
        
    Returns:
        Decorated function with error handling
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "args": str(args),
                    "kwargs": str(kwargs)
                }
                return handle_error(e, context, log, reraise, error_mapping)
        return cast(F, wrapper)
    return decorator

def with_async_error_handling(
    error_mapping: Dict[Type[Exception], Type[AppError]] = None,
    log: bool = True,
    reraise: bool = True
) -> Callable[[AsyncF], AsyncF]:
    """
    Decorator for consistent error handling in asynchronous functions
    
    Args:
        error_mapping: Mapping of exception types to AppError types
        log: Whether to log errors
        reraise: Whether to re-raise errors after handling
        
    Returns:
        Decorated async function with error handling
    """
    def decorator(func: AsyncF) -> AsyncF:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "args": str(args),
                    "kwargs": str(kwargs)
                }
                return handle_error(e, context, log, reraise, error_mapping)
        return cast(AsyncF, wrapper)
    return decorator

# Default error mappings for common exceptions
DEFAULT_ERROR_MAPPING = {
    ConnectionError: NetworkError,
    TimeoutError: NetworkError,
    asyncio.TimeoutError: NetworkError,
    ValueError: ValidationError,
    TypeError: ValidationError,
    KeyError: ValidationError,
    IndexError: ValidationError,
    json.JSONDecodeError: ParsingError
}

# Create convenience functions with default mappings
def handle_error_with_defaults(
    error: Exception,
    context: Dict[str, Any] = None,
    log: bool = True,
    reraise: bool = True
) -> Optional[AppError]:
    """Handle error with default error mappings"""
    return handle_error(error, context, log, reraise, DEFAULT_ERROR_MAPPING)

def with_default_error_handling(log: bool = True, reraise: bool = True) -> Callable[[F], F]:
    """Decorator for error handling with default mappings"""
    return with_error_handling(DEFAULT_ERROR_MAPPING, log, reraise)

def with_default_async_error_handling(log: bool = True, reraise: bool = True) -> Callable[[AsyncF], AsyncF]:
    """Async decorator for error handling with default mappings"""
    return with_async_error_handling(DEFAULT_ERROR_MAPPING, log, reraise)
