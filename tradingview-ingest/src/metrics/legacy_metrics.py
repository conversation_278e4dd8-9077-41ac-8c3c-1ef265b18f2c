"""
Legacy Code Usage Metrics
Tracks usage of legacy code paths to help determine when they can be safely removed
"""

import time
from typing import Dict, Any, Optional
import structlog
from prometheus_client import Counter, Histogram, Gauge

logger = structlog.get_logger()

# Legacy code usage counters
legacy_parser_counter = Counter(
    'tradingview_legacy_parser_usage_total',
    'Total number of times legacy parsers are used',
    ['parser_type', 'format']
)

legacy_storage_counter = Counter(
    'tradingview_legacy_storage_usage_total',
    'Total number of times legacy storage methods are used',
    ['method', 'format']
)

legacy_visualizer_counter = Counter(
    'tradingview_legacy_visualizer_usage_total',
    'Total number of times legacy visualizers are used',
    ['visualizer_type']
)

legacy_import_counter = Counter(
    'tradingview_legacy_import_usage_total',
    'Total number of times legacy imports are used',
    ['module', 'import_name']
)

# Legacy code performance metrics
legacy_parser_time = Histogram(
    'tradingview_legacy_parser_seconds',
    'Time spent in legacy parsers',
    ['parser_type']
)

legacy_storage_time = Histogram(
    'tradingview_legacy_storage_seconds',
    'Time spent in legacy storage methods',
    ['method']
)

# Legacy code usage gauge (for current active usage)
legacy_code_active = Gauge(
    'tradingview_legacy_code_active',
    'Number of active legacy code instances',
    ['component']
)

def track_legacy_parser_usage(parser_type: str, format: str = "unknown"):
    """
    Track usage of a legacy parser
    
    Args:
        parser_type: Type of parser (e.g., 'emoji', 'pipe_separated')
        format: Format being parsed
    """
    legacy_parser_counter.labels(
        parser_type=parser_type,
        format=format
    ).inc()
    logger.debug(f"Legacy parser usage tracked: {parser_type}", format=format)

def track_legacy_storage_usage(method: str, format: str = "unknown"):
    """
    Track usage of a legacy storage method
    
    Args:
        method: Storage method name
        format: Format being stored
    """
    legacy_storage_counter.labels(
        method=method,
        format=format
    ).inc()
    logger.debug(f"Legacy storage usage tracked: {method}", format=format)

def track_legacy_visualizer_usage(visualizer_type: str):
    """
    Track usage of a legacy visualizer
    
    Args:
        visualizer_type: Type of visualizer
    """
    legacy_visualizer_counter.labels(
        visualizer_type=visualizer_type
    ).inc()
    logger.debug(f"Legacy visualizer usage tracked: {visualizer_type}")

def track_legacy_import_usage(module: str, import_name: str):
    """
    Track usage of a legacy import
    
    Args:
        module: Module containing the import
        import_name: Name of the imported item
    """
    legacy_import_counter.labels(
        module=module,
        import_name=import_name
    ).inc()
    logger.debug(f"Legacy import usage tracked: {import_name} in {module}")

class track_legacy_parser_time:
    """Context manager for tracking time spent in legacy parsers"""
    
    def __init__(self, parser_type: str):
        self.parser_type = parser_type
        self.start_time = None
        
    def __enter__(self):
        self.start_time = time.time()
        legacy_code_active.labels(component=f"parser_{self.parser_type}").inc()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            legacy_parser_time.labels(parser_type=self.parser_type).observe(duration)
            legacy_code_active.labels(component=f"parser_{self.parser_type}").dec()

class track_legacy_storage_time:
    """Context manager for tracking time spent in legacy storage methods"""
    
    def __init__(self, method: str):
        self.method = method
        self.start_time = None
        
    def __enter__(self):
        self.start_time = time.time()
        legacy_code_active.labels(component=f"storage_{self.method}").inc()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            legacy_storage_time.labels(method=self.method).observe(duration)
            legacy_code_active.labels(component=f"storage_{self.method}").dec()

def get_legacy_usage_stats() -> Dict[str, Any]:
    """
    Get statistics on legacy code usage
    
    Returns:
        Dictionary with usage statistics
    """
    # This is a placeholder - in a real implementation, we would query Prometheus
    # or another metrics storage system to get actual usage data
    return {
        "parsers": {
            "emoji": 0,
            "pipe_separated": 0
        },
        "storage": {
            "_store_webhook_alert_legacy": 0
        },
        "visualizers": {
            "webhook_visualizer": 0,
            "simple_visualizer": 0
        },
        "imports": {
            "supabase_client": 0
        }
    }
