"""
Data Models for TradingView Webhook Data
Provides validation and type safety for incoming webhook data
"""

from pydantic import BaseModel, validator, Field
from typing import Optional, Dict, Any, Union
from datetime import datetime
import time

class WebhookAlert(BaseModel):
    """Validated webhook alert data"""
    symbol: str = Field(..., min_length=1, max_length=20)
    signal_type: str = Field(default="alert", max_length=50)
    timestamp: Union[int, float] = Field(default_factory=time.time)
    timeframe: str = Field(default="1h", max_length=10)
    entry_price: Optional[float] = None
    tp1_price: Optional[float] = None
    tp2_price: Optional[float] = None
    tp3_price: Optional[float] = None
    sl_price: Optional[float] = None
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    reasoning: Optional[str] = None
    raw_data: Optional[Dict[str, Any]] = None
    
    @validator('timestamp')
    def validate_timestamp(cls, v):
        """Validate timestamp is within reasonable bounds"""
        if v < 0 or v > 2**31:  # Unix timestamp bounds
            raise ValueError('Invalid timestamp')
        return v
    
    @validator('symbol')
    def sanitize_symbol(cls, v):
        """Sanitize trading symbol for database storage"""
        import re
        # Remove special characters, keep alphanumeric and common symbols
        sanitized = re.sub(r'[^A-Za-z0-9._-]', '', v)
        return sanitized[:20]  # Limit length
    
    @validator('entry_price', 'tp1_price', 'tp2_price', 'tp3_price', 'sl_price')
    def validate_prices(cls, v):
        """Validate price values are reasonable"""
        if v is not None and (v < 0 or v > 1000000):
            raise ValueError('Price out of reasonable range')
        return v

class WebhookData(BaseModel):
    """Validated raw webhook data"""
    webhook_id: str = Field(..., min_length=1, max_length=50)
    symbol: Optional[str] = None
    timestamp: Union[int, float] = Field(default_factory=time.time)
    event_type: Optional[str] = None
    client_ip: Optional[str] = None
    raw_data: Dict[str, Any] = Field(default_factory=dict)
    
    @validator('timestamp')
    def validate_timestamp(cls, v):
        """Validate timestamp is within reasonable bounds"""
        if v < 0 or v > 2**31:
            raise ValueError('Invalid timestamp')
        return v

class StorageConfig(BaseModel):
    """Configuration for storage manager"""
    database_url: str
    redis_url: str
    batch_size: int = Field(default=100, ge=1, le=10000)
    processing_delay: float = Field(default=1.0, ge=0.1, le=60.0)
    max_queue_size: int = Field(default=10000, ge=100, le=100000)
    max_retries: int = Field(default=3, ge=1, le=10)
    redis_ttl: int = Field(default=3600, ge=60, le=86400)
    
    @classmethod
    def from_env(cls) -> 'StorageConfig':
        """Load configuration from environment variables"""
        import os
        return cls(
            database_url=os.getenv('DATABASE_URL', ''),
            redis_url=os.getenv('REDIS_URL', ''),
            batch_size=int(os.getenv('BATCH_SIZE', '100')),
            processing_delay=float(os.getenv('PROCESSING_DELAY', '1.0')),
            max_queue_size=int(os.getenv('MAX_QUEUE_SIZE', '10000')),
            max_retries=int(os.getenv('MAX_RETRIES', '3')),
            redis_ttl=int(os.getenv('REDIS_TTL', '3600'))
        ) 