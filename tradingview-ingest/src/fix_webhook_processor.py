"""
Fix script for webhook processor
"""
import os
import sys
import shutil
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main fix function"""
    logger.info("Starting webhook processor fix")
    
    # Check if webhook_visualizer.py exists and remove it
    webhook_visualizer_path = os.path.join(os.path.dirname(__file__), 'webhook_visualizer.py')
    if os.path.exists(webhook_visualizer_path):
        logger.info(f"Removing webhook_visualizer.py at {webhook_visualizer_path}")
        os.remove(webhook_visualizer_path)
    else:
        logger.info(f"webhook_visualizer.py does not exist at {webhook_visualizer_path}")
    
    # Check webhook_receiver.py for references to webhook_visualizer
    webhook_receiver_path = os.path.join(os.path.dirname(__file__), 'webhook_receiver.py')
    if os.path.exists(webhook_receiver_path):
        logger.info(f"Checking webhook_receiver.py at {webhook_receiver_path}")
        with open(webhook_receiver_path, 'r') as f:
            content = f.read()
        
        # Replace webhook_visualizer with simple_visualizer
        if 'from .webhook_visualizer import' in content:
            logger.info("Replacing webhook_visualizer import with simple_visualizer import")
            content = content.replace('from .webhook_visualizer import', 'from .simple_visualizer import')
            
            # Write the updated content back to the file
            with open(webhook_receiver_path, 'w') as f:
                f.write(content)
            logger.info("Updated webhook_receiver.py")
        else:
            logger.info("No webhook_visualizer import found in webhook_receiver.py")
    else:
        logger.info(f"webhook_receiver.py does not exist at {webhook_receiver_path}")
    
    # Check webhook_processor.py for references to src.bot
    webhook_processor_path = os.path.join(os.path.dirname(__file__), 'webhook_processor.py')
    if os.path.exists(webhook_processor_path):
        logger.info(f"Checking webhook_processor.py at {webhook_processor_path}")
        with open(webhook_processor_path, 'r') as f:
            content = f.read()
        
        # Replace any references to src.bot
        if 'from src.bot' in content:
            logger.info("Replacing src.bot imports")
            content = content.replace('from src.bot', '# from src.bot')
            
            # Write the updated content back to the file
            with open(webhook_processor_path, 'w') as f:
                f.write(content)
            logger.info("Updated webhook_processor.py")
        else:
            logger.info("No src.bot import found in webhook_processor.py")
    else:
        logger.info(f"webhook_processor.py does not exist at {webhook_processor_path}")
    
    logger.info("Fix complete")

if __name__ == "__main__":
    main()
