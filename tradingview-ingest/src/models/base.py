"""
Base Models Module
Contains shared data models and interfaces to break circular dependencies
"""

import time
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from enum import Enum

class AlertType(Enum):
    """Types of alerts"""
    ENTRY = "entry"
    EXIT = "exit"
    PRICE_TARGET = "price_target"
    STOP_LOSS = "stop_loss"
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    UNKNOWN = "unknown"

class SignalType(Enum):
    """Types of trading signals"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    UNKNOWN = "unknown"

@dataclass
class BaseModel:
    """Base model with common functionality"""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {k: v for k, v in self.__dict__.items()}
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create model from dictionary"""
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

@dataclass
class WebhookData(BaseModel):
    """Raw webhook data"""
    webhook_id: str
    payload: Dict[str, Any]
    client_ip: str = "unknown"
    timestamp: float = field(default_factory=lambda: datetime.now().timestamp())
    raw_text: str = ""
    processed: bool = False
    
@dataclass
class WebhookAlert(BaseModel):
    """Processed webhook alert"""
    symbol: str
    alert_type: str
    signal: str
    timestamp: float
    timeframe: str = "unknown"
    entry_price: Optional[float] = None
    tp1_price: Optional[float] = None
    tp2_price: Optional[float] = None
    tp3_price: Optional[float] = None
    sl_price: Optional[float] = None
    raw_text: str = ""
    webhook_id: str = ""

@dataclass
class MarketData(BaseModel):
    """Market data model"""
    symbol: str
    price: float
    timestamp: float
    volume: Optional[float] = None
    open: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    close: Optional[float] = None
    source: str = "unknown"

@dataclass
class TechnicalIndicator(BaseModel):
    """Technical indicator model"""
    symbol: str
    indicator_name: str  # Changed from indicator_type to match analyzer.py
    value: float  # Changed to float to support comparison operations
    timestamp: float
    timeframe: str = "unknown"
    parameters: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TradingSignal(BaseModel):
    """Trading signal model"""
    symbol: str
    signal_type: str
    confidence: float
    timestamp: float
    entry_price: Optional[float] = None
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    timeframe: str = "unknown"
    source: str = "unknown"
    # Additional fields needed by analyzer.py
    price: Optional[float] = None
    indicators_used: Optional[Dict[str, Any]] = None
    reasoning: Optional[str] = None

@dataclass
class AnalysisResult(BaseModel):
    """Analysis result model"""
    symbol: str
    signal_type: str  # 'buy', 'sell', 'hold'
    confidence: float  # 0.0 to 1.0
    price: Optional[float] = None
    timestamp: Optional[float] = None
    indicators_used: Optional[Dict[str, Any]] = None
    reasoning: Optional[str] = None
    should_alert: bool = False
    data_source: str = "tradingview"
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
