"""
Supabase Client for TradingView Webhook Ingest Service
Provides integration with Supabase for storing webhook data
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
import httpx
import structlog

logger = structlog.get_logger()

class SupabaseClient:
    """Client for interacting with Supabase APIs"""
    
    def __init__(self):
        self.supabase_url = os.getenv("SUPABASE_URL", "")
        # Try service role key first, fallback to regular key
        self.supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY", os.getenv("SUPABASE_KEY", ""))
        self.supabase_db_url = os.getenv("SUPABASE_DB_URL")
        self.use_supabase = os.getenv("USE_SUPABASE", "true").lower() == "true"
        
        # Validate configuration
        if self.use_supabase:
            if not self.supabase_url:
                logger.warning("SUPABASE_URL not configured")
            if not self.supabase_key:
                logger.warning("SUPABASE_SERVICE_ROLE_KEY not configured")
    
    async def insert_webhook_data(self, table: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Insert webhook data into Supabase table"""
        if not self.use_supabase or not self.supabase_url or not self.supabase_key:
            logger.debug("Supabase not configured or disabled, skipping insert")
            return None
            
        try:
            url = f"{self.supabase_url}/rest/v1/{table}"
            headers = {
                "apikey": self.supabase_key,
                "Authorization": f"Bearer {self.supabase_key}",
                "Content-Type": "application/json",
                "Prefer": "return=representation"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=data, headers=headers)
                response.raise_for_status()
                result = response.json()
                logger.info(f"Successfully inserted data into {table}", table=table, record_id=result[0].get('id') if result else None)
                return result[0] if result else None
                
        except Exception as e:
            logger.error("Failed to insert data into Supabase", error=str(e), table=table)
            return None
    
    async def update_webhook_status(self, table: str, record_id: str, status: str) -> bool:
        """Update webhook processing status"""
        if not self.use_supabase or not self.supabase_url or not self.supabase_key:
            return False
            
        try:
            url = f"{self.supabase_url}/rest/v1/{table}?id=eq.{record_id}"
            headers = {
                "apikey": self.supabase_key,
                "Authorization": f"Bearer {self.supabase_key}",
                "Content-Type": "application/json",
                "Prefer": "return=representation"
            }
            
            update_data = {
                "status": status,
                "updated_at": "now()"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.patch(url, json=update_data, headers=headers)
                response.raise_for_status()
                logger.info(f"Successfully updated record status", table=table, record_id=record_id, status=status)
                return True
                
        except Exception as e:
            logger.error("Failed to update record status", error=str(e), table=table, record_id=record_id)
            return False