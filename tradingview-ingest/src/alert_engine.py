"""
Alert Engine for TradingView Signals
Sends notifications for important trading signals and market events
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, List
import structlog
from prometheus_client import Counter, Histogram
import httpx

from .analyzer import AnalysisResult
from config.tradingview_config import config

logger = structlog.get_logger()

class AlertEngine:
    """Sends alerts for trading signals and market events"""
    
    def __init__(self):
        self.alert_counter = Counter(
            'tradingview_alerts_sent_total',
            'Total number of alerts sent',
            ['alert_type', 'symbol', 'status']
        )
        self.alert_time = Histogram(
            'tradingview_alert_send_seconds',
            'Time spent sending alerts',
            ['alert_type', 'symbol']
        )
        
        # Alert configuration
        self.enable_alerts = config.enable_alerts
        self.alert_webhook_url = config.alert_webhook_url
        self.discord_webhook_url = config.discord_webhook_url
        
        # Alert history
        self.alert_history: List[Dict[str, Any]] = []
        self.max_history = 1000
        
        # Rate limiting for alerts
        self.last_alert_time: Dict[str, float] = {}
        self.min_alert_interval = 300  # 5 minutes between alerts for same symbol
    
    async def initialize(self):
        """Initialize the alert engine"""
        try:
            if self.enable_alerts:
                logger.info("Alert engine initialized with alerts enabled")
            else:
                logger.info("Alert engine initialized with alerts disabled")
        except Exception as e:
            logger.error("Failed to initialize alert engine", error=str(e))
            raise
    
    async def cleanup(self):
        """Cleanup alert engine resources"""
        try:
            # Clear alert history
            self.alert_history.clear()
            self.last_alert_time.clear()
            
            logger.info("Alert engine cleanup completed")
        except Exception as e:
            logger.error("Error during alert engine cleanup", error=str(e))
    
    async def send_alert(self, analysis_result: AnalysisResult):
        """
        Send alert for a trading signal
        
        Args:
            analysis_result: Analysis result containing signal information
        """
        if not self.enable_alerts:
            return
        
        start_time = time.time()
        
        try:
            # Check rate limiting
            if not self._should_send_alert(analysis_result.symbol):
                logger.debug("Alert rate limited for symbol", symbol=analysis_result.symbol)
                return
            
            # Create alert message
            alert_message = self._create_alert_message(analysis_result)
            
            # Send alerts through different channels
            tasks = []
            
            if self.alert_webhook_url:
                tasks.append(self._send_webhook_alert(alert_message))
            
            if self.discord_webhook_url:
                tasks.append(self._send_discord_alert(alert_message))
            
            # Execute all alert tasks
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Record results
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error("Alert failed", error=str(result), alert_type="webhook" if i == 0 else "discord")
                        self._record_alert_failure(analysis_result, str(result))
                    else:
                        self._record_alert_success(analysis_result, result)
            
            # Record metrics
            self.alert_time.labels(
                alert_type="trading_signal",
                symbol=analysis_result.symbol
            ).observe(time.time() - start_time)
            
        except Exception as e:
            logger.error("Failed to send alert", error=str(e), symbol=analysis_result.symbol)
            self._record_alert_failure(analysis_result, str(e))
    
    def process_alert(self, parsed_data: Dict[str, Any], analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process alert data and return alert information
        
        Args:
            parsed_data: Parsed webhook data (can be dict or object with to_dict method)
            analysis_results: Market analysis results
            
        Returns:
            Alert information dictionary
        """
        try:
            # Handle both dict and object types
            if hasattr(parsed_data, 'to_dict'):
                data_dict = parsed_data.to_dict()
                symbol = data_dict.get('symbol', 'unknown')
            else:
                data_dict = parsed_data
                symbol = data_dict.get('symbol', 'unknown')
            
            # Create basic alert info
            alert_info = {
                "symbol": symbol,
                "timestamp": time.time(),
                "alert_type": "webhook_received",
                "data": data_dict,
                "analysis": analysis_results,
                "processed": True
            }
            
            # Log the alert processing
            logger.info("Alert processed", symbol=symbol, alert_type="webhook_received")
            
            return alert_info
            
        except Exception as e:
            logger.error("Failed to process alert", error=str(e))
            return {
                "error": str(e),
                "processed": False,
                "timestamp": time.time()
            }
    
    def _should_send_alert(self, symbol: str) -> bool:
        """Check if we should send an alert for this symbol (rate limiting)"""
        current_time = time.time()
        
        if symbol in self.last_alert_time:
            time_since_last = current_time - self.last_alert_time[symbol]
            if time_since_last < self.min_alert_interval:
                return False
        
        self.last_alert_time[symbol] = current_time
        return True
    
    def _create_alert_message(self, analysis_result: AnalysisResult) -> Dict[str, Any]:
        """Create alert message from analysis result"""
        # Create timestamp
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S UTC", time.gmtime(analysis_result.timestamp))
        
        # Create message
        message = {
            "alert_type": "trading_signal",
            "symbol": analysis_result.symbol,
            "signal_type": analysis_result.signal_type,
            "confidence": analysis_result.confidence,
            "price": analysis_result.price,
            "timestamp": timestamp,
            "reasoning": analysis_result.reasoning,
            "indicators_used": analysis_result.indicators_used,
            "data_source": analysis_result.data_source,
            "alert_id": f"{analysis_result.symbol}_{analysis_result.timestamp}_{analysis_result.signal_type}"
        }
        
        return message
    
    async def _send_webhook_alert(self, alert_message: Dict[str, Any]) -> bool:
        """Send alert via webhook"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    self.alert_webhook_url,
                    json=alert_message,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    logger.debug("Webhook alert sent successfully")
                    return True
                else:
                    logger.warning("Webhook alert failed",
                                  status_code=response.status_code,
                                  response=response.text)
                    return False
                    
        except Exception as e:
            logger.error("Failed to send webhook alert", error=str(e))
            return False
    
    async def _send_discord_alert(self, alert_message: Dict[str, Any]) -> bool:
        """Send alert via Discord webhook"""
        try:
            # Create Discord embed
            embed = {
                "title": f"🚨 Trading Signal: {alert_message['symbol']}",
                "description": f"**Signal**: {alert_message['signal_type'].upper()}\n**Confidence**: {alert_message['confidence']:.1%}",
                "color": self._get_discord_color(alert_message['signal_type']),
                "fields": [
                    {
                        "name": "Price",
                        "value": f"${alert_message['price']:.2f}" if alert_message['price'] else "N/A",
                        "inline": True
                    },
                    {
                        "name": "Time",
                        "value": alert_message['timestamp'],
                        "inline": True
                    }
                ],
                "footer": {
                    "text": f"Source: {alert_message['data_source']}"
                }
            }
            
            # Add reasoning if available
            if alert_message['reasoning']:
                embed["fields"].append({
                    "name": "Reasoning",
                    "value": alert_message['reasoning'][:1024],  # Discord limit
                    "inline": False
                })
            
            # Add indicators if available
            if alert_message['indicators_used']:
                indicators_text = "\n".join([f"{k}: {v}" for k, v in alert_message['indicators_used'].items()])
                embed["fields"].append({
                    "name": "Indicators",
                    "value": indicators_text[:1024],
                    "inline": False
                })
            
            discord_message = {
                "embeds": [embed]
            }
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    self.discord_webhook_url,
                    json=discord_message,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    logger.debug("Discord alert sent successfully")
                    return True
                else:
                    logger.warning("Discord alert failed",
                                  status_code=response.status_code,
                                  response=response.text)
                    return False
                    
        except Exception as e:
            logger.error("Failed to send Discord alert", error=str(e))
            return False
    
    def _get_discord_color(self, signal_type: str) -> int:
        """Get Discord embed color based on signal type"""
        colors = {
            "buy": 0x00FF00,      # Green
            "sell": 0xFF0000,     # Red
            "hold": 0xFFFF00,     # Yellow
            "alert": 0xFFA500     # Orange
        }
        return colors.get(signal_type, 0x808080)  # Default gray
    
    def _record_alert_success(self, analysis_result: AnalysisResult, alert_message: Dict[str, Any]):
        """Record successful alert"""
        alert_record = {
            "timestamp": time.time(),
            "symbol": analysis_result.symbol,
            "signal_type": analysis_result.signal_type,
            "confidence": analysis_result.confidence,
            "status": "success",
            "message": alert_message
        }
        
        self.alert_history.append(alert_record)
        
        # Keep history size manageable
        if len(self.alert_history) > self.max_history:
            self.alert_history = self.alert_history[-self.max_history:]
    
    def _record_alert_failure(self, analysis_result: AnalysisResult, error_message: str):
        """Record failed alert"""
        alert_record = {
            "timestamp": time.time(),
            "symbol": analysis_result.symbol,
            "signal_type": analysis_result.signal_type,
            "confidence": analysis_result.confidence,
            "status": "failed",
            "error": error_message
        }
        
        self.alert_history.append(alert_record)
        
        # Keep history size manageable
        if len(self.alert_history) > self.max_history:
            self.alert_history = self.alert_history[-self.max_history:]
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get alert engine metrics"""
        return {
            "total_alerts": 0,  # Prometheus handles this automatically
            "alert_time_avg": 0.0,  # Prometheus handles this automatically
            "alert_history_size": len(self.alert_history),
            "alerts_enabled": self.enable_alerts,
            "webhook_url_configured": bool(self.alert_webhook_url),
            "discord_url_configured": bool(self.discord_webhook_url)
        } 