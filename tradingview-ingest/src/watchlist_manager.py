"""
Watchlist Manager for TradingView Automation
Manages user watchlists, symbols, and alerts

This module provides backward compatibility by re-exporting the shared watchlist implementation.
"""

from typing import Dict, List, Any, Optional
from src.shared.watchlist.models import WatchlistSymbol, UserWatchlist
from src.shared.watchlist.ingest_watchlist_manager import IngestWatchlistManager

# Re-export for backward compatibility
__all__ = ['WatchlistManager', 'WatchlistSymbol', 'UserWatchlist']

class WatchlistManager(IngestWatchlistManager):
    """Watchlist manager implementation that uses the shared base manager"""
    
    def __init__(self, db_pool):
        """Initialize with database pool"""
        super().__init__(db_pool)
