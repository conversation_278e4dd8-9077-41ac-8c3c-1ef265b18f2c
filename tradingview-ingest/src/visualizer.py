"""
Unified Webhook Visualizer
A consolidated visualization system for tracking webhook processing flow
"""

import asyncio
import json
import time
import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)

class StageStatus(Enum):
    """Status of a processing stage"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class VisualizerMode(Enum):
    """Visualization detail level"""
    SIMPLE = "simple"      # Basic visualization with minimal data capture
    DETAILED = "detailed"  # Detailed visualization with full data capture
    DEBUG = "debug"        # Debug mode with maximum data capture

class Visualizer:
    """
    Unified visualization system for webhook processing
    Tracks stages, data flow, and errors with configurable detail levels
    """
    
    def __init__(self, webhook_id: Optional[str] = None, mode: VisualizerMode = VisualizerMode.DETAILED):
        """Initialize the visualizer with a webhook ID and mode"""
        self.webhook_id = webhook_id if webhook_id is not None else f"webhook-{int(time.time())}"
        self.mode = mode
        self.stages: Dict[str, Dict[str, Any]] = {}
        self.data_snapshots: Dict[str, Dict[str, Any]] = {}
        self.errors: List[Dict[str, Any]] = []
        self.start_time = time.time()
        self.is_active = False
        
        logger.debug(f"Visualizer created for webhook: {self.webhook_id} in {mode.value} mode")
        
    async def start(self):
        """Start visualization tracking"""
        self.is_active = True
        self.start_time = time.time()
        
        # Register standard webhook processing stages
        self.stages = {
            "reception": {"name": "reception", "description": "Webhook reception and validation", "status": StageStatus.PENDING},
            "parsing": {"name": "parsing", "description": "Parsing webhook payload", "status": StageStatus.PENDING},
            "processing": {"name": "processing", "description": "Processing webhook data", "status": StageStatus.PENDING},
            "storage": {"name": "storage", "description": "Storing webhook data", "status": StageStatus.PENDING},
            "response": {"name": "response", "description": "Generating webhook response", "status": StageStatus.PENDING}
        }
        
        logger.info(f"Started visualization for webhook: {self.webhook_id} in {self.mode.value} mode")
    
    async def stop(self):
        """Stop visualization tracking"""
        self.is_active = False
        total_time = time.time() - self.start_time
        
        # Log summary
        completed_stages = sum(1 for s in self.stages.values() if s["status"] == StageStatus.COMPLETED)
        failed_stages = sum(1 for s in self.stages.values() if s["status"] == StageStatus.FAILED)
        
        logger.info(f"Completed visualization for webhook: {self.webhook_id} in {total_time:.2f}s")
        logger.info(f"Summary: {completed_stages} stages completed, {failed_stages} failed")
        
        if self.errors:
            logger.error(f"{len(self.errors)} errors occurred during webhook processing")
    
    async def register_stage(self, stage_name: str, description: str = ""):
        """Register a new processing stage"""
        self.stages[stage_name] = {
            "name": stage_name,
            "description": description,
            "status": StageStatus.PENDING,
            "start_time": None,
            "end_time": None,
            "duration": None,
            "input_data": None,
            "output_data": None,
            "order": len(self.stages) + 1
        }
        
        logger.debug(f"Registered stage: {stage_name} for webhook: {self.webhook_id}")
    
    async def start_stage(self, stage_name: str, input_data: Any = None):
        """Mark a stage as started and capture input data"""
        if stage_name not in self.stages:
            await self.register_stage(stage_name)
        
        self.stages[stage_name]["status"] = StageStatus.RUNNING
        self.stages[stage_name]["start_time"] = time.time()
        
        # Capture input data snapshot based on mode
        if input_data is not None and self.mode != VisualizerMode.SIMPLE:
            try:
                # Store data snapshot
                self.stages[stage_name]["input_data"] = input_data
                self.data_snapshots[f"{stage_name}_input"] = self._create_data_snapshot(input_data)
            except Exception as e:
                logger.warning(f"Failed to capture input data for stage {stage_name}: {e}")
        
        logger.info(f"Started stage: {stage_name} for webhook: {self.webhook_id}")
    
    async def complete_stage(self, stage_name: str, output_data: Any = None):
        """Mark a stage as completed and capture output data"""
        if stage_name not in self.stages:
            logger.warning(f"Completing unregistered stage: {stage_name}")
            await self.register_stage(stage_name)
            await self.start_stage(stage_name)
        
        self.stages[stage_name]["status"] = StageStatus.COMPLETED
        self.stages[stage_name]["end_time"] = time.time()
        
        if self.stages[stage_name]["start_time"]:
            duration = self.stages[stage_name]["end_time"] - self.stages[stage_name]["start_time"]
            self.stages[stage_name]["duration"] = duration
        
        # Capture output data snapshot based on mode
        if output_data is not None and self.mode != VisualizerMode.SIMPLE:
            try:
                # Store data snapshot
                self.stages[stage_name]["output_data"] = output_data
                self.data_snapshots[f"{stage_name}_output"] = self._create_data_snapshot(output_data)
            except Exception as e:
                logger.warning(f"Failed to capture output data for stage {stage_name}: {e}")
        
        duration_str = f"{self.stages[stage_name].get('duration', 0):.2f}s" if self.stages[stage_name].get('duration') else "unknown"
        logger.info(f"Completed stage: {stage_name} in {duration_str} for webhook: {self.webhook_id}")
    
    async def fail_stage(self, stage_name: str, error: Exception):
        """Mark a stage as failed and capture error information"""
        if stage_name not in self.stages:
            logger.warning(f"Failing unregistered stage: {stage_name}")
            await self.register_stage(stage_name)
            await self.start_stage(stage_name)
        
        self.stages[stage_name]["status"] = StageStatus.FAILED
        self.stages[stage_name]["end_time"] = time.time()
        self.stages[stage_name]["error"] = str(error)
        
        if self.stages[stage_name]["start_time"]:
            duration = self.stages[stage_name]["end_time"] - self.stages[stage_name]["start_time"]
            self.stages[stage_name]["duration"] = duration
        
        # Capture error information
        error_info = {
            "stage": stage_name,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": time.time()
        }
        
        self.errors.append(error_info)
        
        logger.error(f"Stage failed: {stage_name} - {type(error).__name__}: {error} for webhook: {self.webhook_id}")
    
    async def skip_stage(self, stage_name: str, reason: str = ""):
        """Mark a stage as skipped"""
        if stage_name not in self.stages:
            await self.register_stage(stage_name)
        
        self.stages[stage_name]["status"] = StageStatus.SKIPPED
        self.stages[stage_name]["skip_reason"] = reason
        
        logger.info(f"Skipped stage: {stage_name} - {reason} for webhook: {self.webhook_id}")
    
    # Webhook-specific visualization methods
    async def log_reception(self, client_ip: str, headers: Dict[str, str], payload: Any):
        """Log webhook reception"""
        await self.start_stage("reception", {
            "client_ip": client_ip,
            "headers": headers,
            "payload_size": len(json.dumps(payload)) if isinstance(payload, (dict, list)) else len(str(payload)),
            "timestamp": time.time()
        })
        logger.info(f"Received webhook from {client_ip}, ID: {self.webhook_id}")
    
    async def complete_reception(self, validation_result: bool):
        """Complete webhook reception stage"""
        await self.complete_stage("reception", {
            "validation_result": validation_result,
            "timestamp": time.time()
        })
    
    async def log_parsing(self, payload: Any):
        """Log webhook parsing"""
        await self.start_stage("parsing", {
            "payload": payload,
            "timestamp": time.time()
        })
    
    async def complete_parsing(self, parsed_data: Any):
        """Complete webhook parsing stage"""
        await self.complete_stage("parsing", {
            "parsed_data": parsed_data,
            "timestamp": time.time()
        })
    
    async def log_processing(self, data: Any):
        """Log webhook processing"""
        await self.start_stage("processing", {
            "data": data,
            "timestamp": time.time()
        })
    
    async def complete_processing(self, result: Any):
        """Complete webhook processing stage"""
        await self.complete_stage("processing", {
            "result": result,
            "timestamp": time.time()
        })
    
    async def log_storage(self, data: Any):
        """Log webhook storage"""
        await self.start_stage("storage", {
            "data": data,
            "timestamp": time.time()
        })
    
    async def complete_storage(self, storage_result: Any):
        """Complete webhook storage stage"""
        await self.complete_stage("storage", {
            "storage_result": storage_result,
            "timestamp": time.time()
        })
    
    async def log_response(self, response_data: Any):
        """Log webhook response"""
        await self.start_stage("response", {
            "response_data": response_data,
            "timestamp": time.time()
        })
    
    async def complete_response(self, response_sent: bool):
        """Complete webhook response stage"""
        await self.complete_stage("response", {
            "response_sent": response_sent,
            "timestamp": time.time()
        })
    
    async def log_error(self, stage: str, error: Exception):
        """Log error in webhook processing"""
        await self.fail_stage(stage, error)
    
    async def log_data_flow(self, key: str, data: Any):
        """Log arbitrary data flow between stages"""
        try:
            self.data_snapshots[key] = self._create_data_snapshot(data)
            logger.debug(f"Data flow: {key} for webhook: {self.webhook_id}")
        except Exception as e:
            logger.warning(f"Failed to log data flow for {key}: {e}")
    
    def _create_data_snapshot(self, data: Any) -> Dict[str, Any]:
        """Create a limited snapshot of data for visualization"""
        try:
            snapshot = {
                "timestamp": time.time(),
                "type": type(data).__name__
            }
            
            # For debug mode, store more complete data
            if self.mode == VisualizerMode.DEBUG:
                # Still limit extremely large data
                if isinstance(data, dict) and len(data) > 100:
                    keys = list(data.keys())[:100]
                    snapshot["data"] = {k: data[k] for k in keys}
                    snapshot["truncated"] = True
                    snapshot["total_keys"] = len(data)
                elif isinstance(data, list) and len(data) > 100:
                    snapshot["data"] = data[:100]
                    snapshot["truncated"] = True
                    snapshot["total_items"] = len(data)
                elif isinstance(data, str) and len(data) > 2000:
                    snapshot["data"] = data[:2000] + "..."
                    snapshot["truncated"] = True
                    snapshot["total_length"] = len(data)
                else:
                    snapshot["data"] = data
                    snapshot["truncated"] = False
            else:
                # For detailed mode, store limited data
                if isinstance(data, dict):
                    if len(data) > 10:
                        keys = list(data.keys())[:10]
                        snapshot["data"] = {k: data[k] for k in keys}
                        snapshot["truncated"] = True
                        snapshot["total_keys"] = len(data)
                    else:
                        snapshot["data"] = data
                        snapshot["truncated"] = False
                elif isinstance(data, list):
                    if len(data) > 10:
                        snapshot["data"] = data[:10]
                        snapshot["truncated"] = True
                        snapshot["total_items"] = len(data)
                    else:
                        snapshot["data"] = data
                        snapshot["truncated"] = False
                elif isinstance(data, str):
                    if len(data) > 500:
                        snapshot["data"] = data[:500] + "..."
                        snapshot["truncated"] = True
                        snapshot["total_length"] = len(data)
                    else:
                        snapshot["data"] = data
                        snapshot["truncated"] = False
                else:
                    # For other types, use string representation
                    snapshot["data"] = str(data)
                    
            return snapshot
        except Exception as e:
            logger.warning(f"Failed to create data snapshot: {e}")
            return {
                "timestamp": time.time(),
                "type": type(data).__name__,
                "error": f"Failed to capture: {e}"
            }

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the visualization data"""
        return {
            "webhook_id": self.webhook_id,
            "mode": self.mode.value,
            "start_time": self.start_time,
            "duration": time.time() - self.start_time if self.start_time else None,
            "stages_count": len(self.stages),
            "completed_stages": sum(1 for s in self.stages.values() if s["status"] == StageStatus.COMPLETED),
            "failed_stages": sum(1 for s in self.stages.values() if s["status"] == StageStatus.FAILED),
            "errors_count": len(self.errors),
            "is_active": self.is_active
        }

# Global visualizer registry to track active webhook visualizations
webhook_visualizers: Dict[str, Visualizer] = {}

async def create_webhook_visualizer(webhook_id: Optional[str] = None, mode: VisualizerMode = VisualizerMode.DETAILED) -> Visualizer:
    """Create and register a new webhook visualizer"""
    visualizer = Visualizer(webhook_id, mode)
    await visualizer.start()
    
    # Register in global registry
    webhook_visualizers[visualizer.webhook_id] = visualizer
    
    return visualizer

async def get_webhook_visualizer(webhook_id: str) -> Optional[Visualizer]:
    """Get an existing webhook visualizer by ID"""
    return webhook_visualizers.get(webhook_id)

async def cleanup_visualizers(max_age_seconds: int = 3600):
    """Clean up old visualizers"""
    current_time = time.time()
    to_remove = []
    
    for webhook_id, visualizer in webhook_visualizers.items():
        # Remove visualizers older than specified age
        if current_time - visualizer.start_time > max_age_seconds:
            await visualizer.stop()
            to_remove.append(webhook_id)
    
    for webhook_id in to_remove:
        del webhook_visualizers[webhook_id]
    
    return len(to_remove)

# Compatibility functions for legacy code
async def create_simple_visualizer(webhook_id: Optional[str] = None) -> Visualizer:
    """Create a simple visualizer (for backward compatibility)"""
    return await create_webhook_visualizer(webhook_id, VisualizerMode.SIMPLE)

async def create_detailed_visualizer(webhook_id: Optional[str] = None) -> Visualizer:
    """Create a detailed visualizer (for backward compatibility)"""
    return await create_webhook_visualizer(webhook_id, VisualizerMode.DETAILED)
