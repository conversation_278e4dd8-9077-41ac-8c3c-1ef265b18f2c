"""
Data Parser for TradingView Webhooks
Converts incoming webhook data into structured market data objects
"""

import time
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import structlog
from prometheus_client import Counter, Histogram

logger = structlog.get_logger()

@dataclass
class MarketData:
    """Structured market data from TradingView"""
    symbol: str
    exchange: str
    price: float
    volume: Optional[int] = None
    open_price: Optional[float] = None
    high_price: Optional[float] = None
    low_price: Optional[float] = None
    close_price: Optional[float] = None
    timestamp: float = None
    data_source: str = "tradingview"
    data_type: str = "price_update"
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "symbol": self.symbol,
            "exchange": self.exchange,
            "price": self.price,
            "volume": self.volume,
            "open_price": self.open_price,
            "high_price": self.high_price,
            "low_price": self.low_price,
            "close_price": self.close_price,
            "timestamp": self.timestamp,
            "data_source": self.data_source,
            "data_type": self.data_type
        }

@dataclass
class TechnicalIndicator:
    """Technical indicator data from TradingView"""
    symbol: str
    indicator_name: str
    value: float
    timestamp: float
    parameters: Optional[Dict[str, Any]] = None
    data_source: str = "tradingview"
    data_type: str = "indicator_update"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "symbol": self.symbol,
            "indicator_name": self.indicator_name,
            "value": self.value,
            "timestamp": self.timestamp,
            "parameters": self.parameters,
            "data_source": self.data_source,
            "data_type": self.data_type
        }

@dataclass
class TradingSignal:
    """Trading signal from TradingView"""
    symbol: str
    signal_type: str  # 'buy', 'sell', 'hold'
    confidence: float  # 0.0 to 1.0
    price: Optional[float] = None
    timestamp: float = None
    indicators_used: Optional[Dict[str, Any]] = None
    reasoning: Optional[str] = None
    data_source: str = "tradingview"
    data_type: str = "signal_generated"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "symbol": self.symbol,
            "signal_type": self.signal_type,
            "confidence": self.confidence,
            "price": self.price,
            "timestamp": self.timestamp,
            "indicators_used": self.indicators_used,
            "reasoning": self.reasoning,
            "data_source": self.data_source,
            "data_type": self.data_type
        }

class DataParser:
    """Parses TradingView webhook data into structured objects"""
    
    def __init__(self):
        self.parse_counter = Counter(
            'tradingview_data_parsed_total',
            'Total number of data records parsed',
            ['data_type', 'symbol', 'status']
        )
        self.parse_time = Histogram(
            'tradingview_data_parse_seconds',
            'Time spent parsing data',
            ['data_type', 'symbol']
        )
    
    def parse_webhook_data(self, webhook_data: Dict[str, Any]) -> Union[MarketData, TechnicalIndicator, TradingSignal]:
        """
        Parse webhook data into appropriate data structure
        
        Args:
            webhook_data: Raw webhook data from TradingView
            
        Returns:
            Parsed data object (MarketData, TechnicalIndicator, or TradingSignal)
        """
        start_time = time.time()
        
        try:
            # Get the symbol/ticker - this is the only thing we really care about
            symbol = webhook_data.get("symbol", "unknown")
            
            # Just store everything as a generic alert - no complex parsing needed
            # TradingView sends whatever format they want, we just store it
            parsed_data = self._parse_generic_alert(webhook_data)
            
            # Record metrics
            parse_time = time.time() - start_time
            self.parse_counter.labels(
                data_type=parsed_data.data_type,
                symbol=symbol,
                status="success"
            ).inc()
            self.parse_time.labels(
                data_type=parsed_data.data_type,
                symbol=symbol
            ).observe(parse_time)
            
            logger.info("Data parsed successfully",
                       symbol=symbol,
                       data_type=parsed_data.data_type,
                       parse_time_ms=round(parse_time * 1000, 2))
            
            return parsed_data
            
        except Exception as e:
            symbol = webhook_data.get("symbol", "unknown")
            
            self.parse_counter.labels(
                data_type="unknown",
                symbol=symbol,
                status="error"
            ).inc()
            
            logger.error("Failed to parse webhook data",
                        symbol=symbol,
                        error=str(e))
            raise
    
    def _parse_generic_alert(self, data: Dict[str, Any]) -> TradingSignal:
        """Parse any webhook data as a generic trading signal/alert"""
        # Just store everything as-is, no validation needed
        return TradingSignal(
            symbol=data.get("symbol", "unknown"),
            signal_type=data.get("signal_type", "alert"),
            confidence=data.get("confidence", 0.5),
            price=data.get("price"),
            timestamp=data.get("timestamp", time.time()),
            indicators_used=data.get("indicators_used", {}),
            reasoning=data.get("reasoning", "TradingView alert"),
            data_source=data.get("data_source", "tradingview"),
            data_type="alert_triggered"
        )
    
    def validate_data_quality(self, parsed_data: Union[MarketData, TechnicalIndicator, TradingSignal]) -> bool:
        """
        Validate the quality of parsed data
        
        Args:
            parsed_data: Parsed data object
            
        Returns:
            True if data quality is acceptable
        """
        try:
            if isinstance(parsed_data, MarketData):
                return self._validate_market_data(parsed_data)
            elif isinstance(parsed_data, TechnicalIndicator):
                return self._validate_technical_indicator(parsed_data)
            elif isinstance(parsed_data, TradingSignal):
                return self._validate_trading_signal(parsed_data)
            else:
                return False
        except Exception as e:
            logger.error("Data quality validation failed", error=str(e))
            return False
    
    def _validate_market_data(self, data: MarketData) -> bool:
        """Validate market data quality"""
        # Check for reasonable price values
        if data.price <= 0 or data.price > 1000000:
            return False
        
        # Check for reasonable volume values
        if data.volume is not None and (data.volume < 0 or data.volume > 1000000000):
            return False
        
        # Check timestamp is recent (within last hour)
        if time.time() - data.timestamp > 3600:
            return False
        
        return True
    
    def _validate_technical_indicator(self, data: TechnicalIndicator) -> bool:
        """Validate technical indicator data quality"""
        # Check for reasonable indicator values
        if abs(data.value) > 1000000:
            return False
        
        # Check timestamp is recent
        if time.time() - data.timestamp > 3600:
            return False
        
        return True
    
    def _validate_trading_signal(self, data: TradingSignal) -> bool:
        """Validate trading signal data quality"""
        # Check confidence is within valid range
        if not (0.0 <= data.confidence <= 1.0):
            return False
        
        # Check signal type is valid
        valid_signals = ["buy", "sell", "hold", "alert"]
        if data.signal_type not in valid_signals:
            return False
        
        # Check timestamp is recent
        if time.time() - data.timestamp > 3600:
            return False
        
        return True
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get data parser metrics"""
        return {
            "total_parsed": 0,  # Prometheus handles this automatically
            "parse_time_avg": 0.0  # Prometheus handles this automatically
        } 