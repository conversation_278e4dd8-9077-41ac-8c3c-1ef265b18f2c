"""
Discord Notification Service for TradingView Analysis
Sends automated analysis reports to Discord webhook
"""

import aiohttp
import json
from typing import Dict, Any, Optional
import structlog
from prometheus_client import Counter, Histogram
import time

logger = structlog.get_logger()

# Prometheus metrics
discord_notifications_sent = Counter('discord_notifications_sent_total', 'Total Discord notifications sent')
discord_notification_duration = Histogram('discord_notification_duration_seconds', 'Discord notification duration')
discord_notification_errors = Counter('discord_notification_errors_total', 'Total Discord notification errors')

class DiscordNotifier:
    """Sends notifications to Discord webhook"""
    
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def initialize(self):
        """Initialize HTTP session"""
        if not self.session:
            self.session = aiohttp.ClientSession()
            logger.info("Discord notifier initialized")
    
    async def cleanup(self):
        """Cleanup HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("Discord notifier cleaned up")
    
    async def send_analysis_report(self, insights: Dict[str, Any], analysis_timestamp: str):
        """Send analysis report to Discord"""
        start_time = time.time()
        
        try:
            await self.initialize()
            
            # Create Discord embed
            embed = self._create_analysis_embed(insights, analysis_timestamp)
            
            # Send to Discord
            payload = {
                "embeds": [embed],
                "username": "TradingView Analyzer",
                "avatar_url": "https://cdn.discordapp.com/emojis/1234567890.png"  # Optional: add custom avatar
            }
            
            async with self.session.post(
                self.webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 204:  # Discord success
                    logger.info("Analysis report sent to Discord successfully")
                    discord_notifications_sent.inc()
                    
                    # Record metrics
                    duration = time.time() - start_time
                    discord_notification_duration.observe(duration)
                    
                    return True
                else:
                    error_text = await response.text()
                    logger.error("Discord webhook failed", 
                               status=response.status, 
                               error=error_text)
                    discord_notification_errors.inc()
                    return False
                    
        except Exception as e:
            logger.error("Failed to send Discord notification", error=str(e))
            discord_notification_errors.inc()
            return False
    
    def _create_analysis_embed(self, insights: Dict[str, Any], timestamp: str) -> Dict[str, Any]:
        """Create Discord embed from analysis insights"""
        
        # Extract key data
        symbol_activity = insights.get('symbol_activity', {})
        market_sentiment = insights.get('market_sentiment', {})
        alert_frequency = insights.get('alert_frequency', {})
        time_patterns = insights.get('time_patterns', {})
        
        # Create embed
        embed = {
            "title": "🔍 TradingView Analysis Report",
            "description": f"Automated analysis completed at {timestamp}",
            "color": 0x00ff00,  # Green color
            "fields": [],
            "timestamp": timestamp,
            "footer": {
                "text": "TradingView Automation System"
            }
        }
        
        # Add symbol activity field
        if symbol_activity:
            most_active = symbol_activity.get('most_active_symbols', [])
            if most_active:
                top_symbols = ", ".join([f"**{symbol}**({count})" for symbol, count in most_active[:5]])
                embed["fields"].append({
                    "name": "📊 Most Active Symbols",
                    "value": top_symbols,
                    "inline": True
                })
            
            embed["fields"].append({
                "name": "📈 Activity Summary",
                "value": f"**{symbol_activity.get('total_symbols', 0)}** symbols\n**{symbol_activity.get('total_alerts', 0)}** total alerts",
                "inline": True
            })
        
        # Add alert frequency field
        if alert_frequency:
            embed["fields"].append({
                "name": "⚡ Alert Frequency",
                "value": f"**{alert_frequency.get('alerts_per_minute', 0)}** alerts/min\nTrend: **{alert_frequency.get('frequency_trend', 'unknown')}**",
                "inline": True
            })
        
        # Add market sentiment field
        if market_sentiment:
            sentiment = market_sentiment.get('overall_sentiment', 'unknown')
            sentiment_emoji = {
                'bullish': '🟢',
                'bearish': '🔴',
                'neutral': '🟡'
            }.get(sentiment, '⚪')
            
            embed["fields"].append({
                "name": "🎯 Market Sentiment",
                "value": f"{sentiment_emoji} **{sentiment.title()}**",
                "inline": True
            })
        
        # Add time patterns if available
        if time_patterns and time_patterns.get('peak_hour'):
            peak_hour = time_patterns['peak_hour'][0] if isinstance(time_patterns['peak_hour'], list) else time_patterns['peak_hour']
            embed["fields"].append({
                "name": "⏰ Peak Activity",
                "value": f"Hour: **{peak_hour}:00** UTC",
                "inline": True
            })
        
        return embed
    
    async def send_simple_notification(self, message: str, title: str = "TradingView Alert"):
        """Send simple text notification to Discord"""
        try:
            await self.initialize()
            
            payload = {
                "content": message,
                "username": "TradingView Bot",
                "avatar_url": "https://cdn.discordapp.com/emojis/1234567890.png"
            }
            
            async with self.session.post(
                self.webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 204:
                    logger.info("Simple notification sent to Discord")
                    return True
                else:
                    logger.error("Failed to send simple notification", status=response.status)
                    return False
                    
        except Exception as e:
            logger.error("Failed to send simple Discord notification", error=str(e))
            return False

# Global Discord notifier instance
discord_notifier = None 