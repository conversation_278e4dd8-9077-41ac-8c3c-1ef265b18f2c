"""
Unified Parser Module for TradingView Data
Handles both text-based alerts and structured JSON webhooks
"""

import re
import time
from typing import Dict, Any, Optional, Union, List
from datetime import datetime
import structlog
from prometheus_client import Counter, Histogram

from .models import (
    WebhookAlert, 
    MarketData, 
    TechnicalIndicator, 
    TradingSignal
)
from .error_handler import (
    with_error_handling,
    ParsingError
)

logger = structlog.get_logger()

class UnifiedParser:
    """
    Unified parser for TradingView data
    Handles both text-based alerts and structured JSON webhooks
    """
    
    def __init__(self):
        """Initialize the parser with metrics"""
        self.parse_counter = Counter(
            'tradingview_data_parsed_total',
            'Total number of data records parsed',
            ['data_type', 'symbol', 'status']
        )
        self.parse_time = Histogram(
            'tradingview_data_parse_seconds',
            'Time spent parsing data',
            ['data_type', 'symbol']
        )
    
    def parse_data(self, data: Union[str, Dict[str, Any]]) -> Union[WebhookAlert, MarketData, TechnicalIndicator, TradingSignal]:
        """
        Parse data from TradingView (either text or JSON)
        
        Args:
            data: Raw data from TradingView (string or dict)
            
        Returns:
            Parsed data object
        """
        start_time = time.time()
        
        try:
            # Determine data type and parse accordingly
            if isinstance(data, str):
                parsed_data = self.parse_text_alert(data)
                data_type = "text_alert"
            elif isinstance(data, dict):
                parsed_data = self.parse_webhook_data(data)
                data_type = "json_webhook"
            else:
                raise ParsingError(f"Unsupported data type: {type(data)}")
            
            # Get symbol for metrics
            symbol = getattr(parsed_data, 'symbol', 'unknown')
            
            # Record metrics
            parse_time = time.time() - start_time
            self.parse_counter.labels(
                data_type=data_type,
                symbol=symbol,
                status="success"
            ).inc()
            self.parse_time.labels(
                data_type=data_type,
                symbol=symbol
            ).observe(parse_time)
            
            logger.info("Data parsed successfully",
                       symbol=symbol,
                       data_type=data_type,
                       parse_time_ms=round(parse_time * 1000, 2))
            
            return parsed_data
            
        except Exception as e:
            # Get symbol if possible
            symbol = "unknown"
            if isinstance(data, dict):
                symbol = data.get("symbol", "unknown")
            
            self.parse_counter.labels(
                data_type="unknown",
                symbol=symbol,
                status="error"
            ).inc()
            
            logger.error("Failed to parse data",
                        symbol=symbol,
                        error=str(e))
            raise ParsingError(f"Failed to parse data: {str(e)}", details={"symbol": symbol})
    
    @with_error_handling(error_mapping={Exception: ParsingError}, reraise=True)
    def parse_webhook_data(self, webhook_data: Dict[str, Any]) -> Union[MarketData, TechnicalIndicator, TradingSignal]:
        """
        Parse webhook data (JSON) into appropriate data structure
        
        Args:
            webhook_data: Raw webhook data from TradingView
            
        Returns:
            Parsed data object
        """
        # Get the symbol/ticker
        symbol = webhook_data.get("symbol", "unknown")
        
        # Check if this is market data
        if "price" in webhook_data and "exchange" in webhook_data:
            return self._parse_market_data(webhook_data)
        
        # Check if this is a technical indicator
        elif "indicator_name" in webhook_data and "value" in webhook_data:
            return self._parse_technical_indicator(webhook_data)
        
        # Default to trading signal/alert
        else:
            return self._parse_trading_signal(webhook_data)
    
    @with_error_handling(error_mapping={Exception: ParsingError}, reraise=True)
    def parse_text_alert(self, alert_text: str) -> WebhookAlert:
        """
        Parse text-based alert from TradingView Pine Script
        
        Args:
            alert_text: Raw alert text
            
        Returns:
            Parsed WebhookAlert object
        """
        # Only support the uniform format
        if '|' in alert_text and alert_text.count('|') >= 8:
            return self._parse_uniform_format(alert_text)
        else:
            raise ValueError("Unsupported alert format. Please use the uniform pipe-separated format.")
    
    def parse_multiple_alerts(self, text: str) -> List[WebhookAlert]:
        """
        Parse multiple alerts from a single text
        
        Args:
            text: Raw text containing multiple alerts
            
        Returns:
            List of parsed WebhookAlert objects
        """
        # Split by common alert separators
        alert_separators = ['\n\n', '---', '===']
        
        for separator in alert_separators:
            if separator in text:
                alert_parts = text.split(separator)
                alerts = []
                for part in alert_parts:
                    if part.strip():
                        try:
                            alert = self.parse_text_alert(part.strip())
                            alerts.append(alert)
                        except Exception:
                            continue  # Skip invalid alerts
                return alerts
        
        # If no separators found, try to parse as single alert
        try:
            return [self.parse_text_alert(text)]
        except Exception:
            return []
    
    def _parse_uniform_format(self, text: str) -> WebhookAlert:
        """Parse the uniform pipe-separated format"""
        parts = text.strip().split('|')
        if len(parts) < 9:
            raise ParsingError(f"Invalid uniform format: expected at least 9 parts, got {len(parts)}")
        
        # Format: TICKER|SIGNAL_TYPE|TIMESTAMP|TIMEFRAME|ENTRY|TP1|TP2|TP3|SL
        ticker = parts[0].strip()
        signal_type = parts[1].strip()
        timestamp = int(parts[2].strip())
        timeframe = parts[3].strip()
        
        # Parse prices (handle potential empty values)
        entry_price = self._safe_float(parts[4])
        tp1_price = self._safe_float(parts[5])
        tp2_price = self._safe_float(parts[6])
        tp3_price = self._safe_float(parts[7])
        sl_price = self._safe_float(parts[8])
        
        # Determine alert type from signal type
        if 'ENTRY' in signal_type:
            alert_type = 'ENTRY'
        elif 'TP_HIT' in signal_type or 'TP' in signal_type:
            alert_type = 'TP_HIT'
        elif 'SL_HIT' in signal_type or 'SL' in signal_type:
            alert_type = 'SL_HIT'
        else:
            alert_type = 'UNKNOWN'
        
        return WebhookAlert(
            symbol=ticker,
            alert_type=alert_type,
            signal=signal_type,
            timestamp=timestamp,
            timeframe=timeframe,
            entry_price=entry_price,
            tp1_price=tp1_price,
            tp2_price=tp2_price,
            tp3_price=tp3_price,
            sl_price=sl_price,
            raw_text=text
        )
    
    
    def _parse_market_data(self, data: Dict[str, Any]) -> MarketData:
        """Parse market data from webhook"""
        return MarketData(
            symbol=data.get("symbol", "unknown"),
            exchange=data.get("exchange", "unknown"),
            price=float(data.get("price", 0.0)),
            volume=data.get("volume"),
            open_price=data.get("open"),
            high_price=data.get("high"),
            low_price=data.get("low"),
            close_price=data.get("close"),
            timestamp=data.get("timestamp", time.time()),
            source=data.get("source", "tradingview")
        )
    
    def _parse_technical_indicator(self, data: Dict[str, Any]) -> TechnicalIndicator:
        """Parse technical indicator from webhook"""
        return TechnicalIndicator(
            symbol=data.get("symbol", "unknown"),
            indicator_name=data.get("indicator_name", "unknown"),
            value=float(data.get("value", 0.0)),
            timestamp=data.get("timestamp", time.time()),
            parameters=data.get("parameters"),
            timeframe=data.get("timeframe", "unknown")
        )
    
    def _parse_trading_signal(self, data: Dict[str, Any]) -> TradingSignal:
        """Parse trading signal from webhook"""
        return TradingSignal(
            symbol=data.get("symbol", "unknown"),
            signal_type=data.get("signal_type", "alert"),
            confidence=float(data.get("confidence", 0.5)),
            timestamp=data.get("timestamp", time.time()),
            price=data.get("price"),
            indicators_used=data.get("indicators_used"),
            reasoning=data.get("reasoning", "TradingView alert"),
            source=data.get("source", "tradingview")
        )
    
    def _safe_float(self, value: str) -> Optional[float]:
        """Safely convert string to float"""
        try:
            if not value or value.strip() == '':
                return None
            return float(value.strip())
        except (ValueError, TypeError):
            return None
    
    def _extract_with_pattern(self, text: str, pattern: str, default: str) -> str:
        """Extract value using regex pattern with default fallback"""
        match = re.search(pattern, text)
        return match.group(1) if match else default
    
    def _extract_price_with_pattern(self, text: str, pattern: str) -> Optional[float]:
        """Extract price value using regex pattern"""
        match = re.search(pattern, text)
        if match:
            try:
                return float(match.group(1))
            except (ValueError, TypeError):
                return None
        return None
    
    
    
    def validate_data_quality(self, parsed_data: Union[WebhookAlert, MarketData, TechnicalIndicator, TradingSignal]) -> bool:
        """
        Validate the quality of parsed data
        
        Args:
            parsed_data: Parsed data object
            
        Returns:
            True if data quality is acceptable
        """
        try:
            if isinstance(parsed_data, WebhookAlert):
                return self._validate_webhook_alert(parsed_data)
            elif isinstance(parsed_data, MarketData):
                return self._validate_market_data(parsed_data)
            elif isinstance(parsed_data, TechnicalIndicator):
                return self._validate_technical_indicator(parsed_data)
            elif isinstance(parsed_data, TradingSignal):
                return self._validate_trading_signal(parsed_data)
            else:
                return False
        except Exception as e:
            logger.error("Data quality validation failed", error=str(e))
            return False
    
    def _validate_webhook_alert(self, data: WebhookAlert) -> bool:
        """Validate webhook alert quality"""
        # Check symbol is valid
        if data.symbol == "UNKNOWN":
            return False
        
        # Check timestamp is recent (within last hour)
        if time.time() - data.timestamp > 3600:
            return False
        
        return True
    
    def _validate_market_data(self, data: MarketData) -> bool:
        """Validate market data quality"""
        # Check for reasonable price values
        if data.price <= 0 or data.price > 1000000:
            return False
        
        # Check for reasonable volume values
        if data.volume is not None and (data.volume < 0 or data.volume > 1000000000):
            return False
        
        # Check timestamp is recent (within last hour)
        if time.time() - data.timestamp > 3600:
            return False
        
        return True
    
    def _validate_technical_indicator(self, data: TechnicalIndicator) -> bool:
        """Validate technical indicator data quality"""
        # Check for reasonable indicator values
        if abs(data.value) > 1000000:
            return False
        
        # Check timestamp is recent
        if time.time() - data.timestamp > 3600:
            return False
        
        return True
    
    def _validate_trading_signal(self, data: TradingSignal) -> bool:
        """Validate trading signal data quality"""
        # Check confidence is within valid range
        if not (0.0 <= data.confidence <= 1.0):
            return False
        
        # Check signal type is valid
        valid_signals = ["buy", "sell", "hold", "alert", "LONG", "SHORT"]
        if data.signal_type.lower() not in [s.lower() for s in valid_signals]:
            return False
        
        # Check timestamp is recent
        if time.time() - data.timestamp > 3600:
            return False
        
        return True
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get parser metrics"""
        return {
            "total_parsed": 0,  # Prometheus handles this automatically
            "parse_time_avg": 0.0  # Prometheus handles this automatically
        }

# Create a global instance for convenience
parser = UnifiedParser()

# Convenience functions for backward compatibility
def parse_data(data: Union[str, Dict[str, Any]]) -> Union[WebhookAlert, MarketData, TechnicalIndicator, TradingSignal]:
    """Parse data using the global parser instance"""
    return parser.parse_data(data)

def parse_webhook_data(webhook_data: Dict[str, Any]) -> Union[MarketData, TechnicalIndicator, TradingSignal]:
    """Parse webhook data using the global parser instance"""
    return parser.parse_webhook_data(webhook_data)

def parse_text_alert(alert_text: str) -> WebhookAlert:
    """Parse text alert using the global parser instance"""
    return parser.parse_text_alert(alert_text)

def parse_multiple_alerts(text: str) -> List[WebhookAlert]:
    """Parse multiple alerts using the global parser instance"""
    return parser.parse_multiple_alerts(text)
