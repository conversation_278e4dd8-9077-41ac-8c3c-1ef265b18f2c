"""
Enhanced AI Trading Analysis Service
Runs every 5 minutes to analyze market data and make human-like trading decisions
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import structlog
from prometheus_client import Counter, Histogram, Gauge
import random

from .discord_notifier import DiscordNotifier

logger = structlog.get_logger()

# Prometheus metrics
analysis_runs_total = Counter('automated_analysis_runs_total', 'Total automated analysis runs')
analysis_duration = Histogram('automated_analysis_duration_seconds', 'Analysis duration')
trading_signals_generated = Counter('trading_signals_generated_total', 'Total trading signals generated')
signal_confidence = Histogram('signal_confidence', 'Trading signal confidence levels')

class AITradingAnalyzer:
    """Advanced Multi-Stage Market Analysis Framework"""
    
    def __init__(self, db_pool, discord_webhook_url: Optional[str] = None):
        self.db_pool = db_pool
        self.discord_notifier = DiscordNotifier(discord_webhook_url) if discord_webhook_url else None
        self.is_running = False
        
        # Initialize Specialized Trader Analyzers
        self.trader_analyzers = {
            'day_trader': DayTraderAnalyzer(),
            'swing_trader': SwingTraderAnalyzer(),
            'short_squeeze_specialist': ShortSqueezeAnalyzer(),
            'value_investor': ValueInvestorAnalyzer(),
            'momentum_trader': MomentumTraderAnalyzer(),
            'qqq_spy_0dte_specialist': QQQSPYOptionsAnalyzer()
        }
    
    async def start(self, interval_minutes: int = 5):
        """Start the automated analysis loop"""
        self.is_running = True
        logger.info(f"AI Trading Analyzer started with {interval_minutes}-minute intervals")
        
        while self.is_running:
            try:
                await self._run_analysis_cycle()
                await asyncio.sleep(interval_minutes * 60)  # Convert to seconds
            except asyncio.CancelledError:
                logger.info("AI Trading Analyzer cancelled")
                break
            except Exception as e:
                logger.error(f"Error in AI analysis cycle: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def stop(self):
        """Stop the automated analysis loop"""
        self.is_running = False
        logger.info("AI Trading Analyzer stopped")
    
    async def _run_analysis_cycle(self):
        """Run one complete analysis cycle"""
        try:
            # Get symbols to analyze from recent webhooks
            symbols = await self.get_recent_symbols()
            
            if not symbols:
                logger.info("No symbols to analyze in this cycle")
                return
            
            logger.info(f"Analyzing {len(symbols)} symbols")
            
            for symbol in symbols:
                try:
                    # Add timeout protection for each symbol analysis
                    analysis_task = asyncio.create_task(self.analyze_symbol(symbol))
                    try:
                        analysis_result = await asyncio.wait_for(analysis_task, timeout=30.0)  # 30 second timeout
                        if analysis_result.get('discord_worthy'):
                            await self._send_discord_report(symbol, analysis_result)
                    except asyncio.TimeoutError:
                        logger.error(f"Analysis timeout for {symbol} after 30 seconds")
                        analysis_task.cancel()
                    except Exception as e:
                        logger.error(f"Failed to analyze {symbol}: {e}")
                        
                except Exception as e:
                    logger.error(f"Failed to analyze {symbol}: {e}")
                    
        except Exception as e:
            logger.error(f"Failed to run analysis cycle: {e}")
    
    async def get_recent_symbols(self, hours: int = 24) -> List[str]:
        """Get list of symbols with recent activity"""
        try:
            # Skip database query if db_pool is None
            if self.db_pool is None:
                logger.debug("Database pool is None (using Supabase only), skipping symbol retrieval")
                return []
                
            # Get symbols with recent activity
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT DISTINCT symbol FROM webhook_alerts
                    WHERE created_at >= NOW() - INTERVAL '1 hour' * $1
                    ORDER BY symbol
                """, hours)
                
            return [row['symbol'] for row in rows]
            
        except Exception as e:
            logger.error(f"Failed to get recent symbols: {e}")
            return []
    
    async def _send_discord_report(self, symbol: str, analysis_result: Dict[str, Any]):
        """Send analysis report to Discord"""
        if self.discord_notifier:
            try:
                await self.discord_notifier.send_analysis_report(symbol, analysis_result)
                logger.info(f"Discord report sent for {symbol}")
            except Exception as e:
                logger.error(f"Failed to send Discord report for {symbol}: {e}")
    
    async def analyze_symbol(self, symbol: str) -> Dict[str, Any]:
        """
        Comprehensive multi-stage symbol analysis
        
        Stages:
        1. Grand Overview Check
        2. Conditional Deep Dive
        3. Prepare Discord-Ready Format
        """
        # Stage 1: Grand Overview
        overview_results = await self._perform_grand_overview(symbol)
        
        # Stage 2: Conditional Deep Dive (if overview indicates potential)
        if overview_results['requires_deep_dive']:
            deep_dive_results = await self._perform_deep_dive(symbol, overview_results)
            
            # Stage 3: Prepare for Discord
            if deep_dive_results['discord_worthy']:
                return await self._prepare_discord_report(symbol, deep_dive_results)
        
        return overview_results

    async def _perform_grand_overview(self, symbol: str) -> Dict[str, Any]:
        """Stage 1: Grand Overview Check using actual webhook data"""
        try:
            # Get recent webhook data for this symbol
            async with self.db_pool.acquire() as conn:
                result = await conn.fetchrow("""
                    SELECT raw_data->'payload' as payload, created_at
                    FROM webhooks 
                    WHERE raw_data->'payload'->>'symbol' = $1
                    AND created_at > NOW() - INTERVAL '1 hour'
                    ORDER BY created_at DESC
                    LIMIT 1
                """, symbol)
                
                if not result:
                    return {
                        'requires_deep_dive': False,
                        'overview_score': 0.0,
                        'reason': 'No recent data available'
                    }
                
                payload = result['payload']
                
                # Handle case where payload might be a string
                if isinstance(payload, str):
                    try:
                        import json
                        payload = json.loads(payload)
                    except json.JSONDecodeError:
                        logger.error(f"Failed to parse payload string: {payload}")
                        return {
                            'requires_deep_dive': False,
                            'overview_score': 0.0,
                            'reason': 'Invalid payload format'
                        }
                
                indicators = payload.get('indicators', {})
                
                # Calculate overview score based on actual indicators
                overview_score = 0.0
                analysis_factors = []
                trend_strength = 0
                market_state = "UNKNOWN"
                price_momentum = 0.0
                rsi = None
                rsi_values = []
                
                # Check if we have technical indicators
                if indicators and isinstance(indicators, dict):
                    # RSI Analysis
                    rsi_values = indicators.get('rsi_14', [])
                    if rsi_values:
                        rsi = rsi_values[0]
                        if rsi > 70:
                            analysis_factors.append(f"RSI overbought ({rsi:.2f})")
                            overview_score += 0.2
                        elif rsi < 30:
                            analysis_factors.append(f"RSI oversold ({rsi:.2f})")
                            overview_score += 0.3
                        else:
                            analysis_factors.append(f"RSI neutral ({rsi:.2f})")
                    
                    # Trend Strength Analysis
                    trend_strength = indicators.get('trend_strength', [0])[0]
                    if trend_strength > 80:
                        analysis_factors.append(f"Strong trend ({trend_strength})")
                        overview_score += 0.3
                    elif trend_strength < 20:
                        analysis_factors.append(f"Weak trend ({trend_strength})")
                        overview_score += 0.1
                    
                    # Volume Analysis
                    volume_impulse = indicators.get('volume_impulse', [False])[0]
                    if volume_impulse:
                        analysis_factors.append("High volume activity")
                        overview_score += 0.2
                    
                    # Market State Analysis
                    market_state = indicators.get('market_state', ['UNKNOWN'])[0]
                    if market_state == 'VOLATILE':
                        analysis_factors.append("High volatility market")
                        overview_score += 0.2
                    
                    # Price Momentum Analysis
                    price_momentum = indicators.get('price_momentum', [0])[0]
                    if abs(price_momentum) > 0.1:
                        direction = "bullish" if price_momentum > 0 else "bearish"
                        analysis_factors.append(f"Strong {direction} momentum ({price_momentum:.4f})")
                        overview_score += 0.3
                else:
                    # Fallback analysis for webhooks without technical indicators
                    price = payload.get('price')
                    if price is not None:
                        analysis_factors.append(f"Price data available: {price}")
                        overview_score += 0.1
                    
                    # Check for basic signal data
                    if 'signal' in payload:
                        analysis_factors.append(f"Signal detected: {payload['signal']}")
                        overview_score += 0.2
                    
                    if 'alert' in payload:
                        analysis_factors.append("Alert triggered")
                        overview_score += 0.1
                    
                    analysis_factors.append("Basic webhook data - limited technical analysis")
                    overview_score += 0.1
                
                # Determine if deep dive is needed
                requires_deep_dive = overview_score > 0.5
                
                return {
                    'requires_deep_dive': requires_deep_dive,
                    'overview_score': min(overview_score, 1.0),
                    'analysis_factors': analysis_factors,
                    'trend_strength': trend_strength,
                    'market_state': market_state,
                    'price_momentum': price_momentum,
                    'rsi': rsi_values[0] if rsi_values else None
                }
                
        except Exception as e:
            logger.error(f"Failed to perform grand overview for {symbol}: {e}")
            return {
                'requires_deep_dive': False,
                'overview_score': 0.0,
                'reason': f'Analysis error: {str(e)}'
            }
    
    async def _perform_deep_dive(self, symbol: str, overview: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 2: Conditional Deep Dive using comprehensive data analysis"""
        try:
            # Get comprehensive data for deep analysis
            async with self.db_pool.acquire() as conn:
                result = await conn.fetch("""
                    SELECT raw_data->'payload' as payload, created_at
                    FROM webhooks 
                    WHERE raw_data->'payload'->>'symbol' = $1
                    AND created_at > NOW() - INTERVAL '1 hour'
                    ORDER BY created_at DESC
                    LIMIT 5
                """, symbol)
                
                if not result:
                    return {
                        'discord_worthy': False,
                        'confidence': 0.0,
                        'reason': 'Insufficient data for deep analysis'
                    }
                
                # Analyze multiple data points for pattern recognition
                analysis_points = []
                total_confidence = 0.0
                
                for row in result:
                    payload = row['payload']
                    indicators = payload.get('indicators', {})
                    
                    # Handle both technical and basic webhook data
                    if indicators and isinstance(indicators, dict):
                        point_analysis = self._analyze_data_point(indicators)
                    else:
                        point_analysis = self._analyze_basic_webhook(payload)
                    
                    analysis_points.append(point_analysis)
                    total_confidence += point_analysis['confidence']
                
                avg_confidence = total_confidence / len(analysis_points)
                
                # Determine if this is Discord-worthy
                discord_worthy = avg_confidence > 0.6
                
                # Generate trading insight
                trading_insight = self._generate_trading_insight(symbol, analysis_points, overview)
                
                return {
                    'discord_worthy': discord_worthy,
                    'confidence': avg_confidence,
                    'analysis_points': analysis_points,
                    'trading_insight': trading_insight,
                    'overview_summary': overview
                }
                
        except Exception as e:
            logger.error(f"Failed to perform deep dive for {symbol}: {e}")
            return {
                'discord_worthy': False,
                'confidence': 0.0,
                'reason': f'Deep dive error: {str(e)}'
            }
    
    def _analyze_data_point(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a single data point for patterns"""
        confidence = 0.0
        signals = []
        
        # RSI Analysis
        rsi_values = indicators.get('rsi_14', [])
        if rsi_values:
            rsi = rsi_values[0]
            if rsi > 75:
                signals.append(f"Strong overbought (RSI: {rsi:.2f})")
                confidence += 0.3
            elif rsi < 25:
                signals.append(f"Strong oversold (RSI: {rsi:.2f})")
                confidence += 0.3
        
        # MACD Analysis
        macd_hist = indicators.get('macd_hist', [0])[0]
        if abs(macd_hist) > 0.05:
            direction = "bullish" if macd_hist > 0 else "bearish"
            signals.append(f"MACD {direction} momentum ({macd_hist:.4f})")
            confidence += 0.2
        
        # Trend Strength
        trend_strength = indicators.get('trend_strength', [0])[0]
        if trend_strength > 85:
            signals.append(f"Very strong trend ({trend_strength})")
            confidence += 0.25
        elif trend_strength < 15:
            signals.append(f"Very weak trend ({trend_strength})")
            confidence += 0.15
        
        # Volume Analysis
        volume_impulse = indicators.get('volume_impulse', [False])[0]
        if volume_impulse:
            signals.append("High volume activity")
            confidence += 0.15
        
        return {
            'confidence': min(confidence, 1.0),
            'signals': signals,
            'timestamp': time.time()
        }
    
    def _analyze_basic_webhook(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze basic webhook data without technical indicators"""
        confidence = 0.0
        signals = []
        
        # Price analysis
        price = payload.get('price')
        if price is not None:
            signals.append(f"Price: {price}")
            confidence += 0.1
        
        # Symbol analysis
        symbol = payload.get('symbol')
        if symbol:
            signals.append(f"Symbol: {symbol}")
            confidence += 0.1
        
        # Test/alert detection
        if 'test' in payload:
            signals.append("Test webhook detected")
            confidence += 0.05
        elif 'alert' in payload:
            signals.append("Alert webhook detected")
            confidence += 0.1
        
        # Signal detection
        if 'signal' in payload:
            signal_type = payload['signal']
            signals.append(f"Signal: {signal_type}")
            confidence += 0.2
        
        # Additional data
        if len(payload) > 3:  # More than just basic fields
            signals.append("Additional data available")
            confidence += 0.05
        
        return {
            'confidence': min(confidence, 1.0),
            'signals': signals,
            'timestamp': time.time()
        }
    
    def _generate_trading_insight(self, symbol: str, analysis_points: List[Dict], overview: Dict) -> str:
        """Generate human-readable trading insight"""
        if not analysis_points:
            return f"No actionable signals for {symbol}"
        
        # Aggregate signals
        all_signals = []
        for point in analysis_points:
            all_signals.extend(point['signals'])
        
        if not all_signals:
            return f"{symbol} showing neutral conditions"
        
        # Count signal types
        bullish_count = sum(1 for signal in all_signals if 'bullish' in signal.lower() or 'oversold' in signal.lower())
        bearish_count = sum(1 for signal in all_signals if 'bearish' in signal.lower() or 'overbought' in signal.lower())
        
        # Generate insight
        if bullish_count > bearish_count:
            sentiment = "bullish"
            action = "consider long positions"
        elif bearish_count > bullish_count:
            sentiment = "bearish"
            action = "consider short positions"
        else:
            sentiment = "neutral"
            action = "wait for clearer signals"
        
        # Get key metrics
        rsi = overview.get('rsi', 'N/A')
        trend_strength = overview.get('trend_strength', 'N/A')
        momentum = overview.get('price_momentum', 'N/A')
        
        insight = f"{symbol} showing {sentiment} bias. RSI: {rsi}, Trend: {trend_strength}, Momentum: {momentum:.4f}. {action}."
        
        return insight

class BaseTraderAnalyzer:
    """Base class for specialized trader analyzers"""
    
    async def analyze(self, symbol: str) -> Dict[str, Any]:
        """Abstract method to be implemented by each trader type"""
        raise NotImplementedError

class DayTraderAnalyzer(BaseTraderAnalyzer):
    """Specialized Day Trader Analysis"""
    
    async def analyze(self, symbol: str) -> Dict[str, Any]:
        # Focus: Intraday price movements, Volume, Volatility
        analysis = {
            'timeframe': 'intraday',
            'key_metrics': ['volume', 'volatility', 'momentum'],
            'risk_tolerance': 'high'
        }
        # Implement specific day trading logic here
        return analysis

class SwingTraderAnalyzer(BaseTraderAnalyzer):
    """Specialized Swing Trader Analysis"""
    
    async def analyze(self, symbol: str) -> Dict[str, Any]:
        # Focus: Multi-day trend captures, Technical patterns, Support/Resistance
        analysis = {
            'timeframe': 'multi_day',
            'key_metrics': ['technical_patterns', 'support_resistance', 'trend_strength'],
            'risk_tolerance': 'medium'
        }
        return analysis

class ShortSqueezeAnalyzer(BaseTraderAnalyzer):
    """Specialized Short Squeeze Analysis"""
    
    async def analyze(self, symbol: str) -> Dict[str, Any]:
        # Focus: High short interest stocks, Float, Volume
        analysis = {
            'timeframe': 'rapid',
            'key_metrics': ['short_interest_ratio', 'float', 'volume'],
            'risk_tolerance': 'very_high'
        }
        return analysis

class ValueInvestorAnalyzer(BaseTraderAnalyzer):
    """Specialized Value Investor Analysis"""
    
    async def analyze(self, symbol: str) -> Dict[str, Any]:
        # Focus: Fundamental analysis, P/E Ratio, Earnings Growth
        analysis = {
            'timeframe': 'months_years',
            'key_metrics': ['pe_ratio', 'earnings_growth', 'balance_sheet'],
            'risk_tolerance': 'low'
        }
        return analysis

class MomentumTraderAnalyzer(BaseTraderAnalyzer):
    """Specialized Momentum Trader Analysis"""
    
    async def analyze(self, symbol: str) -> Dict[str, Any]:
        # Focus: Strong trending stocks, RSI, MACD, Price Momentum
        analysis = {
            'timeframe': 'weeks',
            'key_metrics': ['rsi', 'macd', 'price_momentum'],
            'risk_tolerance': 'medium_high'
        }
        return analysis

class QQQSPYOptionsAnalyzer(BaseTraderAnalyzer):
    """Specialized QQQ/SPY 0DTE Options Analysis"""
    
    async def analyze(self, symbol: str) -> Dict[str, Any]:
        # Focus: QQQ/SPY 0DTE options, Intraday volatility
        analysis = {
            'timeframe': 'intraday',
            'key_metrics': ['iv_percentile', 'gamma_exposure', 'put_call_ratio'],
            'risk_tolerance': 'extreme'
        }
        return analysis 