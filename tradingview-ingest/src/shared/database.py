"""
Database client wrapper for webhook-ingest
Provides a unified interface for database operations
"""

import os
import json
import structlog
from typing import Dict, Any, Optional

logger = structlog.get_logger()

class DatabaseManager:
    """Database connection manager"""
    
    def __init__(self):
        self.initialized = False
        self.supabase_client = None
    
    async def initialize(self):
        """Initialize database connections"""
        try:
            # Import here to avoid circular imports
            from ..supabase_client import supabase_client
            
            self.supabase_client = supabase_client
            self.initialized = True
            logger.info("Database connections established")
            return True
            
        except Exception as e:
            logger.error("Failed to initialize database connections", error=str(e))
            self.initialized = False
            return False
    
    async def get_client(self):
        """Get database client, initializing if needed"""
        if not self.initialized:
            await self.initialize()
        return self.supabase_client
    
    async def close(self):
        """Close database connections"""
        if self.supabase_client:
            await self.supabase_client.close()
            logger.info("Database connections closed")

# Global instance
db_manager = DatabaseManager()

async def insert_data(table: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Insert data into database table"""
    try:
        client = await db_manager.get_client()
        if not client:
            logger.warning(f"Database client not available, skipping insert to {table}")
            return None
        
        # Use the appropriate method based on the table
        if table == "webhooks":
            success = await client.save_webhook_data(data.get("webhook_id", ""), data)
            return data if success else None
        elif table == "tickers":
            success = await client.save_ticker_data(data.get("symbol", ""), data)
            return data if success else None
        else:
            logger.warning(f"Unknown table: {table}, no handler available")
            return None
        
    except Exception as e:
        logger.error(f"Failed to insert data into {table}", error=str(e))
        return None

async def update_data(table: str, data: Dict[str, Any], match_column: str, match_value: Any) -> Optional[Dict[str, Any]]:
    """Update data in database table"""
    try:
        client = await db_manager.get_client()
        if not client:
            logger.warning(f"Database client not available, skipping update to {table}")
            return None
        
        # Use the appropriate method based on the table
        if table == "webhooks":
            success = await client.update_webhook_status(match_value, data.get("status", "unknown"), data)
            return data if success else None
        else:
            logger.warning(f"Unknown table update: {table}, no handler available")
            return None
        
    except Exception as e:
        logger.error(f"Failed to update data in {table}", error=str(e))
        return None
