"""
Watchlist Manager for TradingView Ingest
Extends the shared BaseWatchlistManager with ingest-specific functionality
"""

import time
import structlog
from prometheus_client import Counter, Histogram
from typing import Optional

from src.shared.watchlist.base_manager import BaseWatchlistManager

# Prometheus metrics
watchlist_operations_total = Counter('watchlist_operations_total', 'Total watchlist operations', ['operation', 'status'])
watchlist_operation_duration = Histogram('watchlist_operation_duration_seconds', 'Watchlist operation duration')

logger = structlog.get_logger()

class IngestWatchlistManager(BaseWatchlistManager):
    """Ingest-specific watchlist manager implementation"""
    
    def __init__(self, db_pool):
        """Initialize with database pool"""
        super().__init__(db_pool)
    
    def log_info(self, message: str, **kwargs):
        """Log information using structlog"""
        logger.info(message, **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """Log error using structlog"""
        logger.error(message, **kwargs)
    
    def record_metric(self, operation: str, status: str, duration: Optional[float] = None):
        """Record metrics using Prometheus"""
        if status in ('success', 'error'):
            watchlist_operations_total.labels(operation=operation, status=status).inc()
        
        if duration is not None:
            watchlist_operation_duration.observe(duration)
