"""
Redis client wrapper for webhook-ingest
Provides a unified interface for Redis operations
"""

import os
import redis.asyncio as redis
import structlog

logger = structlog.get_logger()

class RedisManager:
    """Redis connection manager"""
    
    def __init__(self):
        self.redis_client = None
        self.initialized = False
    
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            # Get Redis configuration from environment variables
            redis_password = os.getenv("REDIS_PASSWORD", "123SECURE_REDIS_PASSWORD!@#")
            redis_host = os.getenv("REDIS_HOST", "redis")
            redis_port = int(os.getenv("REDIS_PORT", "6379"))
            redis_db = int(os.getenv("REDIS_DB", "0"))
            
            # Make sure we're using the container name, not localhost
            if redis_host == 'localhost':
                redis_host = 'redis'
                logger.info(f"Changed Redis host from localhost to container name: {redis_host}")
            
            logger.info(f"Connecting to Redis at {redis_host}:{redis_port}/{redis_db} with password")
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                password=redis_password,
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            self.initialized = True
            logger.info("Redis connection established")
            return True
            
        except Exception as e:
            logger.error("Failed to connect to Redis", error=str(e))
            self.initialized = False
            return False
    
    async def get_client(self):
        """Get Redis client, initializing if needed"""
        if not self.initialized:
            await self.initialize()
        return self.redis_client
    
    async def close(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Redis connection closed")

# Global instance
redis_manager = RedisManager()

async def get_redis_client():
    """Get Redis client from manager"""
    return await redis_manager.get_client()
