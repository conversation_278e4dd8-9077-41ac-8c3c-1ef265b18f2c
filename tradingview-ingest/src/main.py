"""
TradingView Webhook Ingestion Service
Main FastAPI application for receiving and processing TradingView webhooks
"""

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog
import time
import asyncio
import json
import os
from typing import Dict, Any, Optional
import redis.asyncio as redis

from .data_parser import DataParser
from .storage_manager import StorageManager
from .analyzer import MarketAnalyzer
from .alert_engine import AlertEngine
from .text_parser import PineScriptAlertParser
from config.tradingview_config import config

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Redis connection
redis_client: Optional[redis.Redis] = None
WEBHOOK_QUEUE = "webhook_queue" # Must match the queue name in webhook_receiver.py

# Initialize FastAPI app
app = FastAPI(
    title="TradingView Ingestion Service",
    description="Real-time market data ingestion and analysis from TradingView webhooks",
    version="1.0.0",
    docs_url="/docs" if config.debug else None,
    redoc_url="/redoc" if config.debug else None
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure appropriately for production
)

# Initialize services
data_parser = DataParser()
storage_manager = StorageManager()
market_analyzer = MarketAnalyzer()
alert_engine = AlertEngine()

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    try:
        logger.info("Starting TradingView Ingestion Service")
        global redis_client
        # Redis Configuration
        redis_host = os.getenv("REDIS_HOST", "redis")
        redis_port = os.getenv("REDIS_PORT", "6379")
        redis_password = os.getenv("REDIS_PASSWORD", "")
        if redis_password:
            redis_url = f"redis://:{redis_password}@{redis_host}:{redis_port}"
        else:
            redis_url = f"redis://{redis_host}:{redis_port}"
        logger.info(f"Connecting to Redis at: {redis_url}")
        redis_client = redis.from_url(redis_url, decode_responses=True)

        logger.info("Testing Redis connection...")
        await redis_client.ping()
        logger.info("Redis connection established")
        
        logger.info("Initializing storage manager...")
        await storage_manager.initialize()
        logger.info("Storage manager initialized")
        
        logger.info("Initializing market analyzer...")
        await market_analyzer.initialize()
        logger.info("Market analyzer initialized")
        
        logger.info("Initializing alert engine...")
        await alert_engine.initialize()
        logger.info("Alert engine initialized")
        
        logger.info("All services initialized successfully")
        
        # Start the webhook consumer as a background task
        logger.info("Starting Redis webhook consumer...")
        asyncio.create_task(consume_webhooks_from_redis())
        logger.info("Redis webhook consumer started")

    except Exception as e:
        logger.error("Failed to initialize services or connect to Redis", error=str(e), error_type=type(e).__name__)
        import traceback
        logger.error("Full startup error traceback", traceback=traceback.format_exc())
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down TradingView Ingestion Service")
    if redis_client:
        await redis_client.close()
        logger.info("Redis connection closed")
    await storage_manager.cleanup()
    await market_analyzer.cleanup()
    await alert_engine.cleanup()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "tradingview-ingest",
        "version": "1.0.0"
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "TradingView Ingestion Service",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


# ============================================================================
# TICKER MANAGEMENT ENDPOINTS
# ============================================================================

@app.get("/tickers")
async def get_all_tickers(active_only: bool = True):
    """Get all tickers with optional filtering by active status"""
    try:
        tickers = await storage_manager.get_all_tickers(active_only=active_only)
        return {
            "status": "success",
            "count": len(tickers),
            "tickers": tickers
        }
    except Exception as e:
        logger.error("Failed to get tickers", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get tickers: {str(e)}")

@app.get("/tickers/{ticker}")
async def get_ticker_info(ticker: str):
    """Get detailed information about a specific ticker"""
    try:
        summary = await storage_manager.get_ticker_summary(ticker)
        if not summary:
            raise HTTPException(status_code=404, detail=f"Ticker {ticker} not found")
        
        return {
            "status": "success",
            "ticker": ticker,
            "summary": summary
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get ticker info for {ticker}", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get ticker info: {str(e)}")

@app.get("/tickers/{ticker}/alerts")
async def get_ticker_alerts(ticker: str, limit: int = 100):
    """Get alerts for a specific ticker"""
    try:
        alerts = await storage_manager.get_alerts_by_ticker(ticker, limit=limit)
        return {
            "status": "success",
            "ticker": ticker,
            "count": len(alerts),
            "alerts": alerts
        }
    except Exception as e:
        logger.error(f"Failed to get alerts for ticker {ticker}", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get ticker alerts: {str(e)}")

@app.post("/tickers/{ticker}/deactivate")
async def deactivate_ticker(ticker: str):
    """Deactivate a ticker (mark as inactive)"""
    try:
        success = await storage_manager.deactivate_ticker(ticker)
        if success:
            return {
                "status": "success",
                "message": f"Ticker {ticker} deactivated successfully"
            }
        else:
            raise HTTPException(status_code=404, detail=f"Ticker {ticker} not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to deactivate ticker {ticker}", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to deactivate ticker: {str(e)}")

@app.post("/tickers/{ticker}/reactivate")
async def reactivate_ticker(ticker: str):
    """Reactivate a ticker (mark as active)"""
    try:
        success = await storage_manager.reactivate_ticker(ticker)
        if success:
            return {
                "status": "success",
                "message": f"Ticker {ticker} reactivated successfully"
            }
        else:
            raise HTTPException(status_code=404, detail=f"Ticker {ticker} not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reactivate ticker {ticker}", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to reactivate ticker: {str(e)}")

@app.get("/tickers/stats/overview")
async def get_ticker_stats():
    """Get overall ticker statistics"""
    try:
        stats = await storage_manager.get_ticker_stats()
        return {
            "status": "success",
            "stats": stats
        }
    except Exception as e:
        logger.error("Failed to get ticker stats", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get ticker stats: {str(e)}")

@app.get("/metrics")
async def get_metrics():
    """Get service metrics"""
    return {
        "data_processed": data_parser.get_metrics(),
        "storage_stats": await storage_manager.get_metrics(),
        "analysis_stats": market_analyzer.get_metrics(),
        "alert_stats": alert_engine.get_metrics()
    }

@app.post("/webhook/tradingview")
async def tradingview_webhook(request: Request):
    """Receive TradingView webhook alerts"""
    try:
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Parse the request body
        try:
            body = await request.body()
            raw_text = body.decode('utf-8')
            
            # Try to parse as JSON first
            try:
                payload = json.loads(raw_text)
                logger.info("Received JSON webhook", client_ip=client_ip, payload_type="json")
            except json.JSONDecodeError:
                # Fall back to text parsing
                payload = raw_text
                logger.info("Received text webhook", client_ip=client_ip, payload_type="text")
            
            # Generate webhook ID
            webhook_id = f"webhook_{int(time.time() * 1000)}"
            
            # Store in Redis queue for processing
            webhook_data = {
                "webhook_id": webhook_id,
                "payload": payload,
                "client_ip": client_ip,
                "timestamp": time.time(),
                "raw_text": raw_text
            }
            
            if redis_client:
                await redis_client.rpush(WEBHOOK_QUEUE, json.dumps(webhook_data))
                logger.info("Webhook queued for processing", webhook_id=webhook_id, client_ip=client_ip)
            else:
                logger.error("Redis client not available for webhook queuing")
                raise HTTPException(status_code=500, detail="Service temporarily unavailable")
            
            return {
                "status": "success",
                "message": "Webhook received and queued for processing",
                "webhook_id": webhook_id
            }
            
        except Exception as e:
            logger.error("Failed to process webhook body", error=str(e), client_ip=client_ip)
            raise HTTPException(status_code=400, detail="Invalid webhook data")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Unexpected error processing webhook", error=str(e), client_ip=client_ip)
        raise HTTPException(status_code=500, detail="Internal server error")

async def consume_webhooks_from_redis():
    """
    Consumes webhooks from the Redis queue and processes them.
    This runs as a background task.
    """
    logger.info("Starting Redis webhook consumer")
    if not redis_client:
        logger.error("Redis client not initialized for consumer.")
        return

    while True:
        try:
            # BLPOP blocks until an item is available
            # Returns a tuple: (queue_name, item_data) or None if timeout
            result = await redis_client.blpop(WEBHOOK_QUEUE, timeout=30)
            
            if result:
                _queue_name, webhook_data_raw = result
                if webhook_data_raw:
                    webhook_data = json.loads(webhook_data_raw)
                    logger.info("Webhook data loaded from Redis", 
                              webhook_data_type=type(webhook_data).__name__,
                              webhook_data_keys=list(webhook_data.keys()) if isinstance(webhook_data, dict) else "N/A")
                    
                    webhook_id = webhook_data.get("webhook_id", "N/A")
                    client_ip = webhook_data.get("client_ip", "N/A")

                    logger.info("Processing webhook from Redis queue", webhook_id=webhook_id, client_ip=client_ip)
                    
                    # Use the centralized webhook core processor
                    from .webhook_core import process_webhook
                    
                    # Process the webhook using the core module
                    result = await process_webhook(webhook_data_raw)
                    
                    if result["success"]:
                        parsed_data = result["parsed_data"]
                        
                        # 3. Analyze market conditions
                        analysis_results = await market_analyzer.analyze_data(parsed_data)
                        
                        # 4. Generate and send alerts if conditions met
                        alert_info = alert_engine.process_alert(parsed_data, analysis_results)
                        
                        logger.info("Webhook processed and analyzed successfully", 
                                  webhook_id=webhook_id,
                                  symbol=getattr(parsed_data, 'symbol', 'UNKNOWN'))
                    else:
                        logger.error("Failed to process webhook", 
                                   webhook_id=webhook_id, 
                                   error=result.get("error", "Unknown error"))
                    
                    logger.info("Webhook processed successfully", webhook_id=webhook_id)
            else:
                # Timeout reached, no webhook in queue
                await asyncio.sleep(1)  # Longer delay when queue is empty
            
            await asyncio.sleep(0.1) # Small delay to prevent busy-waiting if queue is empty
        
        except asyncio.CancelledError:
            logger.info("Redis webhook consumer cancelled.")
            break
        except Exception as e:
            logger.error("Error consuming webhook from Redis", error=str(e), error_type=type(e).__name__)
            # Log the full error details for debugging
            import traceback
            logger.error("Full error traceback", traceback=traceback.format_exc())
            await asyncio.sleep(1) # Wait a bit before retrying on error

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=config.host,
        port=config.port,
        reload=config.debug,
        log_level=config.log_level.lower()
    )