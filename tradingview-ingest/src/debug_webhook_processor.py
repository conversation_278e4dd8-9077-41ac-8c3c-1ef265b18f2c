"""
Debug script to check webhook processor errors
"""
import sys
import os
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main debug function"""
    logger.info("Starting webhook processor debug")
    
    # Check if webhook_visualizer.py exists
    webhook_visualizer_path = os.path.join(os.path.dirname(__file__), 'webhook_visualizer.py')
    if os.path.exists(webhook_visualizer_path):
        logger.info(f"webhook_visualizer.py exists at {webhook_visualizer_path}")
        with open(webhook_visualizer_path, 'r') as f:
            content = f.read()
            logger.info(f"First 200 chars: {content[:200]}")
    else:
        logger.info(f"webhook_visualizer.py does not exist at {webhook_visualizer_path}")
    
    # Check if simple_visualizer.py exists
    simple_visualizer_path = os.path.join(os.path.dirname(__file__), 'simple_visualizer.py')
    if os.path.exists(simple_visualizer_path):
        logger.info(f"simple_visualizer.py exists at {simple_visualizer_path}")
    else:
        logger.info(f"simple_visualizer.py does not exist at {simple_visualizer_path}")
    
    # List all files in the directory
    dir_path = os.path.dirname(__file__)
    logger.info(f"Files in {dir_path}:")
    for file in os.listdir(dir_path):
        logger.info(f"- {file}")
    
    # Check Python path
    logger.info(f"Python path: {sys.path}")
    
    # Try to import the modules that are causing issues
    try:
        from webhook_visualizer import create_webhook_visualizer
        logger.info("Successfully imported create_webhook_visualizer from webhook_visualizer")
    except ImportError as e:
        logger.error(f"Failed to import from webhook_visualizer: {e}")
    
    try:
        from simple_visualizer import create_webhook_visualizer
        logger.info("Successfully imported create_webhook_visualizer from simple_visualizer")
    except ImportError as e:
        logger.error(f"Failed to import from simple_visualizer: {e}")
    
    # Check if src.bot exists
    try:
        import src.bot
        logger.info("Successfully imported src.bot")
    except ImportError as e:
        logger.error(f"Failed to import src.bot: {e}")
    
    logger.info("Debug complete")

if __name__ == "__main__":
    main()
