"""
Debug script to check webhook processor errors
"""
import sys
import os
import json
import time
import logging
import structlog

from . import DebugFeatures, debug_function
from ..config import config

logger = structlog.get_logger()

@debug_function("webhook_processor_debug")
def check_visualizer_files():
    """Check if visualizer files exist"""
    logger.info("Checking visualizer files")
    
    # Check if visualizer.py exists
    visualizer_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'visualizer.py')
    if os.path.exists(visualizer_path):
        logger.info(f"visualizer.py exists at {visualizer_path}")
        with open(visualizer_path, 'r') as f:
            content = f.read()
            logger.debug(f"First 200 chars: {content[:200]}")
    else:
        logger.warning(f"visualizer.py does not exist at {visualizer_path}")
    
    # Check for legacy visualizer files
    legacy_files = ['webhook_visualizer.py', 'simple_visualizer.py']
    for file in legacy_files:
        file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), file)
        if os.path.exists(file_path):
            logger.warning(f"Legacy file {file} exists at {file_path}")
        else:
            logger.info(f"Legacy file {file} does not exist at {file_path}")

@debug_function("webhook_processor_debug")
def list_directory_files():
    """List all files in the src directory"""
    dir_path = os.path.dirname(os.path.dirname(__file__))
    logger.info(f"Files in {dir_path}:")
    for file in os.listdir(dir_path):
        logger.info(f"- {file}")

@debug_function("webhook_processor_debug")
def check_imports():
    """Try to import modules that might cause issues"""
    try:
        from ..visualizer import create_webhook_visualizer
        logger.info("Successfully imported create_webhook_visualizer from visualizer")
    except ImportError as e:
        logger.error(f"Failed to import from visualizer: {e}")
    
    # Check if webhook_core exists
    try:
        from ..webhook_core import process_webhook
        logger.info("Successfully imported process_webhook from webhook_core")
    except ImportError as e:
        logger.error(f"Failed to import from webhook_core: {e}")

@debug_function("webhook_processor_debug")
def check_python_path():
    """Check Python path"""
    logger.info(f"Python path: {sys.path}")

@debug_function("webhook_processor_debug")
def main():
    """Main debug function"""
    if not DebugFeatures.WEBHOOK_PROCESSOR_DEBUG:
        logger.warning("Webhook processor debug is disabled")
        return
        
    logger.info("Starting webhook processor debug")
    
    # Run all debug checks
    check_visualizer_files()
    list_directory_files()
    check_imports()
    check_python_path()
    
    logger.info("Debug complete")

if __name__ == "__main__":
    main()
