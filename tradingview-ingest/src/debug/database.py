"""
Debug Database Utilities
Provides tools for debugging database operations
"""

import time
import json
from typing import Dict, Any, Optional, List, Union
import structlog
import asyncio

from . import DebugFeatures, debug_function

logger = structlog.get_logger()

class QueryTracker:
    """Tracks database queries for debugging"""
    
    def __init__(self):
        self.queries: List[Dict[str, Any]] = []
        self.enabled = DebugFeatures.DATABASE_DEBUG
    
    def track_query(self, query: str, params: Any = None, duration_ms: Optional[float] = None, 
                   success: bool = True, error: Optional[str] = None, 
                   connection_type: str = "unknown"):
        """Track a database query"""
        if not self.enabled:
            return
            
        self.queries.append({
            "timestamp": time.time(),
            "query": query,
            "params": params,
            "duration_ms": duration_ms,
            "success": success,
            "error": error,
            "connection_type": connection_type
        })
        
        # Log the query
        log_data = {
            "duration_ms": duration_ms,
            "success": success,
            "connection_type": connection_type
        }
        if error:
            log_data["error"] = error
            
        if len(query) > 100:
            query_preview = query[:100] + "..."
        else:
            query_preview = query
            
        logger.debug(f"DB Query: {query_preview}", **log_data)
    
    def get_queries(self, limit: int = 100, success_only: bool = False) -> List[Dict[str, Any]]:
        """Get tracked queries"""
        if not self.enabled:
            return []
            
        if success_only:
            filtered = [q for q in self.queries if q["success"]]
        else:
            filtered = self.queries
            
        return filtered[-limit:]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get query statistics"""
        if not self.enabled or not self.queries:
            return {}
            
        successful = [q for q in self.queries if q["success"]]
        failed = [q for q in self.queries if not q["success"]]
        
        # Calculate average duration for queries with duration
        durations = [q["duration_ms"] for q in self.queries if q["duration_ms"] is not None]
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        return {
            "total_queries": len(self.queries),
            "successful_queries": len(successful),
            "failed_queries": len(failed),
            "success_rate": len(successful) / len(self.queries) if self.queries else 0,
            "average_duration_ms": avg_duration,
            "connection_types": {
                conn_type: len([q for q in self.queries if q["connection_type"] == conn_type])
                for conn_type in set(q["connection_type"] for q in self.queries)
            }
        }
    
    def clear(self):
        """Clear tracked queries"""
        if self.enabled:
            self.queries = []

# Global query tracker instance
query_tracker = QueryTracker()

class ConnectionMonitor:
    """Monitors database connections"""
    
    def __init__(self):
        self.connections: Dict[str, Dict[str, Any]] = {}
        self.enabled = DebugFeatures.DATABASE_DEBUG
    
    def register_connection(self, connection_id: str, connection_type: str, 
                          details: Optional[Dict[str, Any]] = None):
        """Register a database connection"""
        if not self.enabled:
            return
            
        self.connections[connection_id] = {
            "connection_id": connection_id,
            "connection_type": connection_type,
            "created_at": time.time(),
            "last_used": time.time(),
            "query_count": 0,
            "error_count": 0,
            "details": details or {}
        }
        
        logger.debug(f"DB Connection registered: {connection_id}", 
                   connection_type=connection_type)
    
    def update_connection(self, connection_id: str, query_success: bool = True):
        """Update connection statistics"""
        if not self.enabled or connection_id not in self.connections:
            return
            
        self.connections[connection_id]["last_used"] = time.time()
        self.connections[connection_id]["query_count"] += 1
        
        if not query_success:
            self.connections[connection_id]["error_count"] += 1
    
    def remove_connection(self, connection_id: str):
        """Remove a connection from monitoring"""
        if not self.enabled or connection_id not in self.connections:
            return
            
        del self.connections[connection_id]
        logger.debug(f"DB Connection removed: {connection_id}")
    
    def get_connections(self) -> List[Dict[str, Any]]:
        """Get all monitored connections"""
        if not self.enabled:
            return []
            
        return list(self.connections.values())
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        if not self.enabled:
            return {}
            
        connection_types = {}
        for conn in self.connections.values():
            conn_type = conn["connection_type"]
            if conn_type not in connection_types:
                connection_types[conn_type] = 0
            connection_types[conn_type] += 1
            
        return {
            "total_connections": len(self.connections),
            "connection_types": connection_types,
            "active_connections": len([c for c in self.connections.values() 
                                    if time.time() - c["last_used"] < 60])
        }

# Global connection monitor instance
connection_monitor = ConnectionMonitor()

@debug_function("database_debug")
async def analyze_query(query: str) -> Dict[str, Any]:
    """Analyze a SQL query for potential issues"""
    if not DebugFeatures.DATABASE_DEBUG:
        return {}
        
    issues = []
    warnings = []
    
    # Check for SELECT *
    if "SELECT *" in query.upper():
        issues.append("Query uses 'SELECT *' which can be inefficient")
    
    # Check for missing WHERE clause in UPDATE/DELETE
    if ("UPDATE" in query.upper() or "DELETE" in query.upper()) and "WHERE" not in query.upper():
        warnings.append("Query contains UPDATE/DELETE without WHERE clause")
    
    # Check for potential table scans
    if "WHERE" in query.upper() and "INDEX" not in query.upper() and len(query) > 100:
        warnings.append("Long query might benefit from indexing")
    
    return {
        "query": query,
        "issues": issues,
        "warnings": warnings,
        "recommendation": "No issues detected" if not issues and not warnings else "Review query for potential improvements"
    }

@debug_function("database_debug")
def get_query_history(limit: int = 20) -> List[Dict[str, Any]]:
    """Get recent query history"""
    return query_tracker.get_queries(limit=limit)

@debug_function("database_debug")
def get_database_stats() -> Dict[str, Any]:
    """Get database statistics"""
    if not DebugFeatures.DATABASE_DEBUG:
        return {"enabled": False}
        
    return {
        "enabled": True,
        "queries": query_tracker.get_stats(),
        "connections": connection_monitor.get_connection_stats()
    }

@debug_function("database_debug")
def clear_database_debug_data():
    """Clear all database debug data"""
    if DebugFeatures.DATABASE_DEBUG:
        query_tracker.clear()
        logger.info("Database debug data cleared")
