"""
Debug Performance Monitoring
Provides tools for monitoring and debugging performance issues
"""

import time
import functools
from typing import Dict, Any, Optional, Callable, List
import structlog
from prometheus_client import Histogram

from . import DebugFeatures, debug_function

logger = structlog.get_logger()

# Performance metrics
performance_histogram = Histogram(
    'tradingview_debug_performance_seconds',
    'Time spent in debug-monitored functions',
    ['function_name', 'module']
)

class PerformanceTracker:
    """Tracks performance of functions and code blocks"""
    
    def __init__(self):
        self.active_timers: Dict[str, float] = {}
        self.results: Dict[str, List[float]] = {}
    
    def start_timer(self, name: str) -> None:
        """Start a named timer"""
        if not DebugFeatures.PERFORMANCE_METRICS:
            return
        self.active_timers[name] = time.time()
    
    def stop_timer(self, name: str) -> Optional[float]:
        """Stop a named timer and return elapsed time"""
        if not DebugFeatures.PERFORMANCE_METRICS or name not in self.active_timers:
            return None
            
        elapsed = time.time() - self.active_timers[name]
        if name not in self.results:
            self.results[name] = []
        self.results[name].append(elapsed)
        
        # Log the result
        logger.debug(f"Performance: {name}", duration_ms=round(elapsed * 1000, 2))
        
        # Remove from active timers
        del self.active_timers[name]
        
        return elapsed
    
    def get_stats(self, name: str) -> Dict[str, float]:
        """Get statistics for a named timer"""
        if not DebugFeatures.PERFORMANCE_METRICS or name not in self.results:
            return {}
            
        times = self.results[name]
        if not times:
            return {}
            
        return {
            "count": len(times),
            "total": sum(times),
            "average": sum(times) / len(times),
            "min": min(times),
            "max": max(times)
        }
    
    def reset(self) -> None:
        """Reset all timers and results"""
        self.active_timers.clear()
        self.results.clear()

# Global performance tracker instance
performance_tracker = PerformanceTracker()

def track_performance(func=None, *, name: Optional[str] = None, module: Optional[str] = None):
    """
    Decorator to track function performance
    
    Args:
        func: The function to decorate
        name: Optional name for the timer (defaults to function name)
        module: Optional module name for grouping
    """
    def decorator(func):
        if not DebugFeatures.PERFORMANCE_METRICS:
            return func
            
        func_name = name or func.__name__
        func_module = module or func.__module__
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                return func(*args, **kwargs)
            finally:
                elapsed = time.time() - start_time
                performance_histogram.labels(
                    function_name=func_name,
                    module=func_module
                ).observe(elapsed)
                logger.debug(
                    f"Function performance: {func_name}",
                    duration_ms=round(elapsed * 1000, 2),
                    module=func_module
                )
        return wrapper
    
    if func is None:
        return decorator
    return decorator(func)

@debug_function("performance_metrics")
def get_performance_report() -> Dict[str, Any]:
    """Get a performance report for all tracked functions"""
    if not DebugFeatures.PERFORMANCE_METRICS:
        return {"enabled": False}
        
    report = {
        "enabled": True,
        "timers": {}
    }
    
    for name in performance_tracker.results:
        report["timers"][name] = performance_tracker.get_stats(name)
    
    return report

@debug_function("performance_metrics")
def reset_performance_metrics() -> None:
    """Reset all performance metrics"""
    if DebugFeatures.PERFORMANCE_METRICS:
        performance_tracker.reset()
        logger.info("Performance metrics reset")

# Context manager for performance tracking
class track_block:
    """Context manager for tracking code block performance"""
    
    def __init__(self, name: str, module: Optional[str] = None):
        self.name = name
        self.module = module or "unknown"
        self.start_time = None
        self.enabled = DebugFeatures.PERFORMANCE_METRICS
    
    def __enter__(self):
        if self.enabled:
            self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.enabled and self.start_time:
            elapsed = time.time() - self.start_time
            performance_histogram.labels(
                function_name=self.name,
                module=self.module
            ).observe(elapsed)
            logger.debug(
                f"Block performance: {self.name}",
                duration_ms=round(elapsed * 1000, 2),
                module=self.module
            )
