"""
Debug Visualizer
Provides enhanced visualization tools for debugging webhook processing
"""

import json
import time
from typing import Dict, Any, Optional, List
import structlog
from datetime import datetime

from . import DebugFeatures, debug_function
from ..visualizer import Visualizer, VisualizerMode, get_webhook_visualizer

logger = structlog.get_logger()

class DebugVisualizer:
    """Enhanced visualizer for debugging webhook processing"""
    
    def __init__(self, webhook_id: str):
        """Initialize debug visualizer"""
        self.webhook_id = webhook_id
        self.base_visualizer = None
        self.debug_data: Dict[str, Any] = {
            "webhook_id": webhook_id,
            "created_at": time.time(),
            "stages": [],
            "data_snapshots": {},
            "errors": [],
            "performance": {},
            "memory_usage": {}
        }
        
        # Try to get the base visualizer
        self._init_base_visualizer()
    
    def _init_base_visualizer(self):
        """Initialize the base visualizer if available"""
        try:
            # Use the debug mode for the base visualizer
            self.base_visualizer = Visualizer(
                webhook_id=self.webhook_id,
                mode=VisualizerMode.DEBUG
            )
        except Exception as e:
            logger.warning(f"Failed to initialize base visualizer: {e}")
    
    async def start(self):
        """Start visualization tracking"""
        if self.base_visualizer:
            await self.base_visualizer.start()
        
        self.debug_data["started_at"] = time.time()
        logger.debug(f"Debug visualizer started for webhook: {self.webhook_id}")
    
    async def complete(self):
        """Mark visualization as complete"""
        if self.base_visualizer:
            await self.base_visualizer.complete()
        
        self.debug_data["completed_at"] = time.time()
        self.debug_data["duration"] = self.debug_data["completed_at"] - self.debug_data["started_at"]
        logger.debug(f"Debug visualizer completed for webhook: {self.webhook_id}")
    
    async def log_reception(self, client_ip: str, headers: Dict[str, str], payload: Any):
        """Log webhook reception details"""
        if self.base_visualizer:
            await self.base_visualizer.log_reception(client_ip, headers, payload)
        
        # Store additional debug information
        self.debug_data["client_ip"] = client_ip
        self.debug_data["headers"] = headers
        self.debug_data["payload_size"] = len(json.dumps(payload)) if isinstance(payload, (dict, list)) else len(str(payload))
        self.debug_data["payload_type"] = type(payload).__name__
        
        logger.debug(f"Debug visualizer logged reception for webhook: {self.webhook_id}")
    
    async def log_error(self, stage: str, error: Exception):
        """Log error during webhook processing"""
        if self.base_visualizer:
            await self.base_visualizer.log_error(stage, error)
        
        # Store additional debug information
        self.debug_data["errors"].append({
            "stage": stage,
            "error": str(error),
            "error_type": type(error).__name__,
            "timestamp": time.time()
        })
        
        logger.debug(f"Debug visualizer logged error in stage {stage} for webhook: {self.webhook_id}")
    
    async def log_data_flow(self, key: str, data: Any):
        """Log data flow between stages"""
        if self.base_visualizer:
            await self.base_visualizer.log_data_flow(key, data)
        
        # Store additional debug information
        self.debug_data["data_snapshots"][key] = {
            "timestamp": time.time(),
            "data_type": type(data).__name__,
            "data_size": len(json.dumps(data)) if isinstance(data, (dict, list)) else len(str(data))
        }
        
        logger.debug(f"Debug visualizer logged data flow for key {key} for webhook: {self.webhook_id}")
    
    async def log_performance(self, stage: str, duration_ms: float):
        """Log performance metrics for a stage"""
        self.debug_data["performance"][stage] = {
            "duration_ms": duration_ms,
            "timestamp": time.time()
        }
        
        logger.debug(f"Debug visualizer logged performance for stage {stage}: {duration_ms}ms")
    
    def get_debug_data(self) -> Dict[str, Any]:
        """Get all debug data collected"""
        return self.debug_data

# Registry of debug visualizers
_debug_visualizers: Dict[str, DebugVisualizer] = {}

@debug_function("visualizer_debug")
async def create_debug_visualizer(webhook_id: str) -> Optional[DebugVisualizer]:
    """Create and register a debug visualizer"""
    if not DebugFeatures.VISUALIZER_DEBUG:
        return None
    
    visualizer = DebugVisualizer(webhook_id)
    await visualizer.start()
    
    _debug_visualizers[webhook_id] = visualizer
    return visualizer

@debug_function("visualizer_debug")
async def get_debug_visualizer(webhook_id: str) -> Optional[DebugVisualizer]:
    """Get a registered debug visualizer"""
    if not DebugFeatures.VISUALIZER_DEBUG:
        return None
    
    if webhook_id in _debug_visualizers:
        return _debug_visualizers[webhook_id]
    
    # Try to create a new one
    return await create_debug_visualizer(webhook_id)

@debug_function("visualizer_debug")
def list_active_debug_visualizers() -> List[str]:
    """List all active debug visualizer IDs"""
    return list(_debug_visualizers.keys())

@debug_function("visualizer_debug")
async def cleanup_debug_visualizers(max_age_seconds: int = 3600):
    """Clean up old debug visualizers"""
    if not DebugFeatures.VISUALIZER_DEBUG:
        return
    
    current_time = time.time()
    to_remove = []
    
    for webhook_id, visualizer in _debug_visualizers.items():
        if current_time - visualizer.debug_data["created_at"] > max_age_seconds:
            to_remove.append(webhook_id)
    
    for webhook_id in to_remove:
        del _debug_visualizers[webhook_id]
    
    logger.debug(f"Cleaned up {len(to_remove)} debug visualizers")

@debug_function("visualizer_debug")
async def export_debug_data(webhook_id: str) -> Optional[Dict[str, Any]]:
    """Export debug data for a webhook"""
    if not DebugFeatures.VISUALIZER_DEBUG:
        return None
    
    visualizer = await get_debug_visualizer(webhook_id)
    if not visualizer:
        return None
    
    return visualizer.get_debug_data()
