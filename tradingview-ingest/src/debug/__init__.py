"""
Debug Module
Contains debug tools and feature flags for the tradingview-ingest service
"""

import os
import logging
from typing import Dict, Any, Optional, Callable
import structlog

from ..config import config

logger = structlog.get_logger()

# Debug feature flags
class DebugFeatures:
    """Debug feature flags"""
    
    # Main debug flag (enables all debug features)
    ENABLED = config.debug
    
    # Individual feature flags
    VISUALIZER_DEBUG = ENABLED and True
    WEBHOOK_PROCESSOR_DEBUG = ENABLED and True
    DATABASE_DEBUG = ENABLED and True
    API_DOCS = ENABLED and True
    PERFORMANCE_METRICS = ENABLED and True
    EXTENDED_LOGGING = ENABLED and True
    
    @classmethod
    def is_enabled(cls, feature: str) -> bool:
        """Check if a specific debug feature is enabled"""
        if not cls.ENABLED:
            return False
            
        feature_upper = feature.upper()
        if hasattr(cls, feature_upper):
            return getattr(cls, feature_upper)
        return False

# Debug logging setup
def setup_debug_logging():
    """Configure debug logging"""
    if DebugFeatures.EXTENDED_LOGGING:
        # Set root logger to DEBUG level
        logging.getLogger().setLevel(logging.DEBUG)
        
        # Configure structlog for more verbose output
        structlog.configure(
            processors=[
                structlog.stdlib.add_log_level,
                structlog.stdlib.add_logger_name,
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        logger.debug("Debug logging enabled")

# Debug context manager
class DebugContext:
    """Context manager for debug operations"""
    
    def __init__(self, feature: str, context: Dict[str, Any] = None):
        self.feature = feature
        self.context = context or {}
        self.enabled = DebugFeatures.is_enabled(feature)
        self.start_time = None
        
    def __enter__(self):
        if self.enabled:
            import time
            self.start_time = time.time()
            logger.debug(f"Debug context started: {self.feature}", **self.context)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.enabled and self.start_time:
            import time
            duration = time.time() - self.start_time
            logger.debug(
                f"Debug context ended: {self.feature}",
                duration_ms=round(duration * 1000, 2),
                **self.context
            )
            
    def log(self, message: str, **kwargs):
        """Log a debug message within this context"""
        if self.enabled:
            context = {**self.context, **kwargs}
            logger.debug(f"{self.feature}: {message}", **context)

# Debug decorator
def debug_function(feature: str):
    """Decorator for debug functions"""
    def decorator(func: Callable):
        import functools
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not DebugFeatures.is_enabled(feature):
                return None
                
            return func(*args, **kwargs)
            
        return wrapper
    return decorator

# Initialize debug module
if DebugFeatures.ENABLED:
    setup_debug_logging()
    logger.info("Debug mode enabled")
else:
    logger.info("Debug mode disabled")
