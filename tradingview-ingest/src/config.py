"""
Centralized Configuration Module
Provides configuration values for the tradingview-ingest service
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# Determine project root path
# This allows the code to work regardless of where it's run from
PROJECT_ROOT = Path(__file__).parent.parent.absolute()

# Add project root to path for imports
if str(PROJECT_ROOT) not in sys.path:
    sys.path.append(str(PROJECT_ROOT))

class Config:
    """Configuration class for tradingview-ingest service"""
    
    def __init__(self):
        """Initialize configuration with environment variables and defaults"""
        # Service configuration
        self.debug = self._get_bool("DEBUG", False)
        self.log_level = self._get_string("LOG_LEVEL", "INFO")
        self.host = self._get_string("API_HOST", "0.0.0.0")
        self.port = self._get_int("API_PORT", 8000)
        
        # Redis configuration
        self.redis_enabled = self._get_bool("REDIS_ENABLED", True)
        self.redis_host = self._get_string("REDIS_HOST", "redis")
        self.redis_port = self._get_int("REDIS_PORT", 6379)
        self.redis_db = self._get_int("REDIS_DB", 0)
        self.redis_password = self._get_string("REDIS_PASSWORD", "123SECURE_REDIS_PASSWORD!@#")
        self.redis_url = self._get_string("REDIS_URL", f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}")
        
        # Database configuration - Use Supabase only
        self.use_supabase = self._get_bool("USE_SUPABASE", True)
        self.supabase_url = self._get_string("SUPABASE_URL", "")
        self.supabase_key = self._get_string("SUPABASE_KEY", "")
        self.supabase_anon_key = self._get_string("SUPABASE_ANON_KEY", "")
        self.supabase_db_url = self._get_string("SUPABASE_DB_URL", "")
        
        # Webhook configuration
        self.webhook_queue = self._get_string("WEBHOOK_QUEUE", "webhook_queue")
        self.webhook_retention_days = self._get_int("WEBHOOK_RETENTION_DAYS", 30)
        
        # Discord configuration
        self.discord_enabled = self._get_bool("DISCORD_ENABLED", False)
        self.discord_webhook_url = self._get_string("DISCORD_WEBHOOK_URL", "")
        self.discord_bot_token = self._get_string("DISCORD_BOT_TOKEN", "")
        self.discord_guild_id = self._get_string("DISCORD_GUILD_ID", "")
        self.discord_channel_id = self._get_string("DISCORD_CHANNEL_ID", "")
        
        # Paths
        self.project_root = PROJECT_ROOT
        self.src_path = PROJECT_ROOT / "src"
        self.config_path = PROJECT_ROOT / "config"
        self.data_path = PROJECT_ROOT / "data"
    
    def _get_string(self, key: str, default: str) -> str:
        """Get string environment variable with default"""
        return os.environ.get(key, default)
    
    def _get_int(self, key: str, default: int) -> int:
        """Get integer environment variable with default"""
        try:
            return int(os.environ.get(key, default))
        except (ValueError, TypeError):
            return default
    
    def _get_bool(self, key: str, default: bool) -> bool:
        """Get boolean environment variable with default"""
        val = os.environ.get(key, str(default)).lower()
        if val in ("true", "1", "yes", "on"):
            return True
        elif val in ("false", "0", "no", "off"):
            return False
        else:
            return default
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration values as a dictionary"""
        return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
    
    def __str__(self) -> str:
        """String representation of configuration"""
        # Filter out sensitive values
        filtered = {k: '***' if 'password' in k.lower() or 'key' in k.lower() else v 
                   for k, v in self.get_all().items()}
        return f"Config({filtered})"

# Global configuration instance
config = Config()

def get_config() -> Config:
    """Get the global configuration instance"""
    return config
