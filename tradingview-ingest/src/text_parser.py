"""
Text Parser for TradingView Pine Script Alerts
Parses human-readable alert messages into structured data
"""

import re
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class ParsedAlert:
    """Parsed TradingView alert data"""
    symbol: str
    alert_type: str
    signal: str
    timestamp: int
    timeframe: str
    entry_price: Optional[float]
    tp1_price: Optional[float]
    tp2_price: Optional[float]
    tp3_price: Optional[float]
    sl_price: Optional[float]
    raw_text: str


class PineScriptAlertParser:
    """Parser for TradingView Pine Script alerts with uniform format"""
    
    def __init__(self):
        # The new format is: TYPE|SIGNAL|TIME|SYMBOL|TIMEFRAME|ENTRY|TP1|TP2|TP3|SL
        pass
    
    def parse_alert(self, alert_text: str) -> ParsedAlert:
        """
        Parse TradingView alert text in the new uniform format:
        TICKER|SIGNAL_TYPE|TIMESTAMP|TIMEFRAME|ENTRY|TP1|TP2|TP3|SL
        """
        try:
            # Try the new uniform format first
            if '|' in alert_text and alert_text.count('|') >= 8:
                parts = alert_text.strip().split('|')
                if len(parts) >= 9:
                    # Format: TICKER|SIGNAL_TYPE|TIMESTAMP|TIMEFRAME|ENTRY|TP1|TP2|TP3|SL
                    ticker = parts[0].strip()
                    signal_type = parts[1].strip()
                    timestamp = int(parts[2].strip())
                    timeframe = parts[3].strip()
                    entry_price = float(parts[4].strip())
                    tp1_price = float(parts[5].strip())
                    tp2_price = float(parts[6].strip())
                    tp3_price = float(parts[7].strip())
                    sl_price = float(parts[8].strip())
                    
                    # Determine alert type from signal type
                    if 'ENTRY' in signal_type:
                        alert_type = 'ENTRY'
                    elif 'TP_HIT' in signal_type or 'TP' in signal_type:
                        alert_type = 'TP_HIT'
                    elif 'SL_HIT' in signal_type or 'SL' in signal_type:
                        alert_type = 'SL_HIT'
                    else:
                        alert_type = 'UNKNOWN'
                    
                    return ParsedAlert(
                        alert_type=alert_type,
                        signal=signal_type,
                        symbol=ticker,
                        timestamp=timestamp,
                        timeframe=timeframe,
                        entry_price=entry_price,
                        tp1_price=tp1_price,
                        tp2_price=tp2_price,
                        tp3_price=tp3_price,
                        sl_price=sl_price,
                        raw_text=alert_text
                    )
            
            # Fallback to legacy emoji-based format
            return self._parse_legacy_format(alert_text)
            
        except (ValueError, IndexError) as e:
            # logger.error(f"Failed to parse alert: {alert_text}, Error: {e}") # Original code had this line commented out
            raise ValueError(f"Invalid alert format: {alert_text}")
    
    def _parse_uniform_format(self, text: str) -> ParsedAlert:
        """Parse the new uniform pipe-separated format"""
        try:
            parts = text.split('|')
            if len(parts) < 10:
                raise ValueError(f"Expected 10 parts, got {len(parts)}")
            
            alert_type = parts[0].strip()
            signal = parts[1].strip()
            timestamp = int(parts[2].strip())
            symbol = parts[3].strip()
            timeframe = parts[4].strip()
            
            # Parse prices (handle potential empty values)
            entry_price = self._safe_float(parts[5])
            tp1_price = self._safe_float(parts[6])
            tp2_price = self._safe_float(parts[7])
            tp3_price = self._safe_float(parts[8])
            sl_price = self._safe_float(parts[9])
            
            # Create parsed alert
            parsed_alert = ParsedAlert(
                symbol=symbol,
                alert_type=alert_type,
                signal=signal,
                timestamp=timestamp,
                timeframe=timeframe,
                entry_price=entry_price,
                tp1_price=tp1_price,
                tp2_price=tp2_price,
                tp3_price=tp3_price,
                sl_price=sl_price,
                raw_text=text
            )
            
            return parsed_alert
            
        except Exception as e:
            raise ValueError(f"Failed to parse uniform format: {str(e)}")
    
    def _parse_legacy_format(self, text: str) -> ParsedAlert:
        """Parse legacy emoji-based format as fallback"""
        # Legacy patterns for extracting data from alert text
        patterns = {
            'symbol': r'\$([A-Z]+)',
            'timeframe': r'\(([^)]+)\)',
            'entry_price': r'Entry:\s*([\d.]+)',
            'tp1_price': r'TP1:\s*([\d.]+)',
            'tp2_price': r'TP2:\s*([\d.]+)',
            'tp3_price': r'TP3:\s*([\d.]+)',
            'tp3_price': r'TP3:\s*([\d.]+)',
            'sl_price': r'SL:\s*([\d.]+)',
        }
        
        # Extract basic information
        symbol = self._extract_with_pattern(text, patterns['symbol'], 'UNKNOWN')
        timeframe = self._extract_with_pattern(text, patterns['timeframe'], 'UNKNOWN')
        alert_type = self._determine_legacy_alert_type(text)
        signal = self._extract_legacy_signal(text)
        
        # Extract prices
        entry_price = self._extract_price_with_pattern(text, patterns['entry_price'])
        tp1_price = self._extract_price_with_pattern(text, patterns['tp1_price'])
        tp2_price = self._extract_price_with_pattern(text, patterns['tp2_price'])
        tp3_price = self._extract_price_with_pattern(text, patterns['tp3_price'])
        sl_price = self._extract_price_with_pattern(text, patterns['sl_price'])
        
        # Create parsed alert with current timestamp
        parsed_alert = ParsedAlert(
            symbol=symbol,
            alert_type=alert_type,
            signal=signal,
            timestamp=int(datetime.now().timestamp()),
            timeframe=timeframe,
            entry_price=entry_price,
            tp1_price=tp1_price,
            tp2_price=tp2_price,
            tp3_price=tp3_price,
            sl_price=sl_price,
            raw_text=text
        )
        
        return parsed_alert
    
    def _safe_float(self, value: str) -> Optional[float]:
        """Safely convert string to float, returning None if invalid"""
        try:
            if not value or value.strip() == '':
                return None
            return float(value.strip())
        except (ValueError, TypeError):
            return None
    
    def is_valid_alert(self, alert_text: str) -> bool:
        """
        Validate if the alert text follows the expected format:
        TICKER|SIGNAL_TYPE|TIMESTAMP|TIMEFRAME|ENTRY|TP1|TP2|TP3|SL
        """
        if not alert_text or not alert_text.strip():
            return False
        
        # Check if it's the new uniform format
        if '|' in alert_text and alert_text.count('|') >= 8:
            parts = alert_text.strip().split('|')
            if len(parts) >= 9:
                try:
                    # Validate ticker (first field)
                    ticker = parts[0].strip()
                    if not ticker or len(ticker) > 10:
                        return False
                    
                    # Validate signal type (second field)
                    signal_type = parts[1].strip()
                    valid_signals = ['LONG_ENTRY', 'SHORT_ENTRY', 'LONG_TP1', 'LONG_TP2', 'LONG_TP3', 
                                   'SHORT_TP1', 'SHORT_TP2', 'SHORT_TP3', 'LONG_SL', 'SHORT_SL']
                    if signal_type not in valid_signals:
                        return False
                    
                    # Validate timestamp (third field) - should be a positive integer
                    timestamp = int(parts[2].strip())
                    if timestamp <= 0:
                        return False
                    
                    # Validate timeframe (fourth field)
                    timeframe = parts[3].strip()
                    if not timeframe or len(timeframe) > 10:
                        return False
                    
                    # Validate prices (remaining fields) - should be positive numbers
                    for i in range(4, 9):
                        price = float(parts[i].strip())
                        if price <= 0:
                            return False
                    
                    return True
                    
                except (ValueError, IndexError):
                    return False
        
        # Check if it's legacy emoji format
        if any(emoji in alert_text for emoji in ['🚨', '🟢', '🔴', '🎯', '⚠️', '🔥']):
            return True
        
        return False
    
    def _extract_with_pattern(self, text: str, pattern: str, default: str) -> str:
        """Extract value using regex pattern with default fallback"""
        match = re.search(pattern, text)
        return match.group(1) if match else default
    
    def _extract_price_with_pattern(self, text: str, pattern: str) -> Optional[float]:
        """Extract price value using regex pattern"""
        match = re.search(pattern, text)
        if match:
            try:
                return float(match.group(1))
            except (ValueError, TypeError):
                return None
        return None
    
    def _determine_legacy_alert_type(self, text: str) -> str:
        """Determine alert type from legacy format"""
        text_upper = text.upper()
        
        if 'LONG TRADE ALERT' in text_upper:
            return 'ENTRY'
        elif 'SHORT TRADE ALERT' in text_upper:
            return 'ENTRY'
        elif 'TP1 HIT' in text_upper:
            return 'TP_HIT'
        elif 'TP2 HIT' in text_upper:
            return 'TP_HIT'
        elif 'TP3 HIT' in text_upper:
            return 'TP_HIT'
        elif 'SL HIT' in text_upper:
            return 'SL_HIT'
        else:
            return 'UNKNOWN'
    
    def _extract_legacy_signal(self, text: str) -> str:
        """Extract signal from legacy format"""
        text_upper = text.upper()
        
        if 'LONG TRADE ALERT' in text_upper or '🟢' in text:
            return 'LONG'
        elif 'SHORT TRADE ALERT' in text_upper or '🔴' in text:
            return 'SHORT'
        elif 'TP1 HIT' in text_upper:
            return 'LONG_TP1' if '🟢' in text else 'SHORT_TP1'
        elif 'TP2 HIT' in text_upper:
            return 'LONG_TP2' if '🟢' in text else 'SHORT_TP2'
        elif 'TP3 HIT' in text_upper:
            return 'LONG_TP3' if '🟢' in text else 'SHORT_TP3'
        elif 'SL HIT' in text_upper:
            return 'LONG_SL' if '🟢' in text else 'SHORT_SL'
        else:
            return 'UNKNOWN'
    
    def parse_multiple_alerts(self, text: str) -> list[ParsedAlert]:
        """Parse multiple alerts from a single text (if multiple tickers per webhook)"""
        # Split by common alert separators
        alert_separators = ['\n\n', '---', '===']
        
        for separator in alert_separators:
            if separator in text:
                alert_parts = text.split(separator)
                alerts = []
                for part in alert_parts:
                    if part.strip():
                        try:
                            alert = self.parse_alert(part.strip())
                            alerts.append(alert)
                        except ValueError:
                            continue  # Skip invalid alerts
                return alerts
        
        # If no separators found, try to parse as single alert
        try:
            return [self.parse_alert(text)]
        except ValueError:
            return []
