"""
Security Middleware for TradingView Webhooks
Implements strict security measures to only allow legitimate TradingView webhooks
"""

import time
import hashlib
import hmac
from typing import Optional, List
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
import structlog

from config.tradingview_config import config

logger = structlog.get_logger()

class TradingViewSecurityMiddleware:
    """Security middleware specifically for TradingView webhooks"""
    
    def __init__(self):
        self.blocked_ips = set()
        self.suspicious_activity = {}
        self.max_suspicious_attempts = 5
    
    async def validate_request(self, request: Request) -> bool:
        """
        Comprehensive security validation for incoming requests
        
        Args:
            request: FastAPI request object
            
        Returns:
            True if request is valid, raises HTTPException otherwise
        """
        try:
            # 1. IP Address Validation
            if not await self._validate_ip_address(request):
                return False
            
            # 2. Request Method Validation
            if not await self._validate_request_method(request):
                return False
            
            # 3. Content Type Validation
            if not await self._validate_content_type(request):
                return False
            
            # 4. Payload Size Validation
            if not await self._validate_payload_size(request):
                return False
            
            # 5. Rate Limiting
            if not await self._validate_rate_limit(request):
                return False
            
            # 6. User Agent Validation (TradingView specific)
            if not await self._validate_user_agent(request):
                return False
            
            # 7. Request Path Validation
            if not await self._validate_request_path(request):
                return False
            
            logger.info("Request passed all security validations", 
                       client_ip=request.client.host,
                       path=request.url.path)
            
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("Security validation error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Security validation failed"
            )
    
    async def _validate_ip_address(self, request: Request) -> bool:
        """Validate IP address"""
        client_ip = request.client.host
        
        # Check if IP is blocked
        if client_ip in self.blocked_ips:
            logger.warning("Blocked IP attempted access", ip=client_ip)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="IP address is blocked"
            )
        
        # Check suspicious activity
        if client_ip in self.suspicious_activity:
            attempts = self.suspicious_activity[client_ip]
            if attempts >= self.max_suspicious_attempts:
                self.blocked_ips.add(client_ip)
                logger.warning("IP blocked due to suspicious activity", ip=client_ip)
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="IP address blocked due to suspicious activity"
                )
        
        return True
    
    async def _validate_request_method(self, request: Request) -> bool:
        """Validate request method"""
        if request.method != "POST":
            logger.warning("Invalid request method", 
                          method=request.method,
                          client_ip=request.client.host)
            raise HTTPException(
                status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
                detail="Only POST requests are allowed"
            )
        return True
    
    async def _validate_content_type(self, request: Request) -> bool:
        """Validate content type"""
        content_type = request.headers.get("content-type", "")
        # Accept both application/json and text/plain (TradingView sometimes sends text/plain)
        if not (content_type.startswith("application/json") or content_type.startswith("text/plain")):
            logger.warning("Invalid content type", 
                          content_type=content_type,
                          client_ip=request.client.host)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Content-Type must be application/json or text/plain"
            )
        return True
    
    async def _validate_payload_size(self, request: Request) -> bool:
        """Validate payload size"""
        content_length = request.headers.get("content-length")
        if content_length:
            size = int(content_length)
            if size > config.max_payload_size:
                logger.warning("Payload too large", 
                              size=size,
                              max_size=config.max_payload_size,
                              client_ip=request.client.host)
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail=f"Payload size {size} exceeds maximum {config.max_payload_size}"
                )
        return True
    
    async def _validate_rate_limit(self, request: Request) -> bool:
        """Validate rate limiting"""
        client_ip = request.client.host
        current_time = time.time()
        
        # Simple in-memory rate limiting
        if not hasattr(self, '_rate_limit_data'):
            self._rate_limit_data = {}
        
        if client_ip not in self._rate_limit_data:
            self._rate_limit_data[client_ip] = []
        
        # Clean old requests
        self._rate_limit_data[client_ip] = [
            req_time for req_time in self._rate_limit_data[client_ip]
            if current_time - req_time < 60
        ]
        
        # Check rate limit
        if len(self._rate_limit_data[client_ip]) >= config.rate_limit_per_minute:
            logger.warning("Rate limit exceeded", 
                          ip=client_ip,
                          requests=len(self._rate_limit_data[client_ip]))
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded"
            )
        
        # Add current request
        self._rate_limit_data[client_ip].append(current_time)
        return True
    
    async def _validate_user_agent(self, request: Request) -> bool:
        """Validate User-Agent header"""
        user_agent = request.headers.get("user-agent", "")
        
        # Allow TradingView webhooks (they might not have a specific User-Agent)
        # But block obvious bot/script User-Agents
        blocked_patterns = [
            "bot", "crawler", "spider", "scraper", 
            # "curl",  # Temporarily allow curl for testing
            "wget", 
            # "python-requests",  # Temporarily allow requests for testing
            "httpclient", "postman"
        ]
        
        user_agent_lower = user_agent.lower()
        for pattern in blocked_patterns:
            if pattern in user_agent_lower:
                logger.warning("Blocked User-Agent", 
                              user_agent=user_agent,
                              client_ip=request.client.host)
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User-Agent not allowed"
                )
        
        return True
    
    async def _validate_request_path(self, request: Request) -> bool:
        """Validate request path"""
        path = request.url.path
        
        # Only allow the specific webhook endpoint
        allowed_paths = [
            "/webhook/tradingview",
            "/health",  # Keep health check for monitoring
            "/metrics"  # Keep metrics for monitoring
        ]
        
        if path not in allowed_paths:
            logger.warning("Invalid request path", 
                          path=path,
                          client_ip=request.client.host)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Endpoint not found"
            )
        
        return True
    
    def record_suspicious_activity(self, client_ip: str):
        """Record suspicious activity for an IP"""
        if client_ip not in self.suspicious_activity:
            self.suspicious_activity[client_ip] = 0
        self.suspicious_activity[client_ip] += 1
        
        logger.warning("Suspicious activity recorded", 
                      ip=client_ip,
                      attempts=self.suspicious_activity[client_ip])
    
    def get_security_stats(self) -> dict:
        """Get security statistics"""
        return {
            "blocked_ips": len(self.blocked_ips),
            "suspicious_ips": len(self.suspicious_activity),
            "total_blocked": sum(self.suspicious_activity.values())
        }

# Global security middleware instance
security_middleware = TradingViewSecurityMiddleware() 