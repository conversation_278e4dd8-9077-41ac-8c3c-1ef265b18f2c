#!/bin/bash

# Secure TradingView Webhook System Startup Script
# This script starts the secure containerized system with proper network isolation

set -e

echo "🔒 Starting Secure TradingView Webhook System"
echo "=" * 50

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if required environment variables are set
if [ -z "$WEBHOOK_SECRET" ]; then
    echo "❌ WEBHOOK_SECRET environment variable is not set."
    echo "Please set it with: export WEBHOOK_SECRET=your_secret_here"
    exit 1
fi

if [ -z "$REDIS_PASSWORD" ]; then
    echo "❌ REDIS_PASSWORD environment variable is not set."
    echo "Please set it with: export REDIS_PASSWORD=your_redis_password_here"
    exit 1
fi

echo "✅ Environment variables are configured"
echo "🔐 Webhook secret: ${WEBHOOK_SECRET:0:8}..."
echo "🔑 Redis password: ${REDIS_PASSWORD:0:8}..."

# Create secure environment file
echo "📝 Creating secure environment file..."
cat > .env.secure << EOF
# Secure environment configuration
WEBHOOK_SECRET=${WEBHOOK_SECRET}
REDIS_PASSWORD=${REDIS_PASSWORD}
ENVIRONMENT=production
LOG_LEVEL=WARNING
EOF

echo "✅ Secure environment file created"

# Start the secure containerized system
echo "🐳 Starting secure containers..."
docker-compose -f docker-compose.secure.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🏥 Checking service health..."
if docker-compose -f docker-compose.secure.yml ps | grep -q "Up"; then
    echo "✅ All services are running"
else
    echo "❌ Some services failed to start"
    docker-compose -f docker-compose.secure.yml logs --tail=20
    exit 1
fi

# Test webhook endpoint
echo "🧪 Testing webhook endpoint..."
if curl -f http://localhost:8001/health > /dev/null 2>&1; then
    echo "✅ Webhook endpoint is responding"
else
    echo "❌ Webhook endpoint is not responding"
    exit 1
fi

# Show service status
echo ""
echo "📊 Service Status:"
echo "=================="
docker-compose -f docker-compose.secure.yml ps

echo ""
echo "🌐 Network Configuration:"
echo "========================"
echo "Webhook Receiver: Exposed on port 8001 (Internet accessible)"
echo "Processing Engine: Internal network only (No internet access)"
echo "Database: Internal network only (No internet access)"
echo "Redis: Internal network only (No internet access)"
echo "Monitoring: Internal network only (No internet access)"

echo ""
echo "🔒 Security Features Active:"
echo "============================"
echo "✅ Container isolation with read-only filesystems"
echo "✅ Non-root user execution"
echo "✅ Network segmentation (webhook vs internal)"
echo "✅ Rate limiting and IP blocking"
echo "✅ Webhook signature validation"
echo "✅ Request validation and sanitization"

echo ""
echo "🚀 System is ready for ngrok tunnel!"
echo "Next steps:"
echo "1. Start ngrok: ./start_ngrok.sh"
echo "2. Copy the ngrok URL to TradingView"
echo "3. Monitor logs: docker-compose -f docker-compose.secure.yml logs -f"
echo "4. Check metrics: curl http://localhost:8001/metrics"

echo ""
echo "🔍 Useful Commands:"
echo "==================="
echo "View logs: docker-compose -f docker-compose.secure.yml logs -f"
echo "Check status: docker-compose -f docker-compose.secure.yml ps"
echo "Restart services: docker-compose -f docker-compose.secure.yml restart"
echo "Stop services: docker-compose -f docker-compose.secure.yml down"
echo "Test webhook: python test_high_volume.py" 