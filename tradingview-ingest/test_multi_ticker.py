#!/usr/bin/env python3
"""
Test Multi-Ticker Webhook System
Simulates TradingView sending multiple tickers per 3-minute candle
"""

import asyncio
import aiohttp
import json
import time
import random
from typing import List, Dict, Any

# Configuration
WEBHOOK_URL = "http://localhost:8001/webhook/tradingview"
TEST_TICKERS = ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN", "NVDA", "META", "NFLX", "AMD", "INTC", "SHOP", "SQ", "ROKU", "ZM", "CRWD"]

def create_ticker_data(symbol: str, base_price: float = None) -> Dict[str, Any]:
    """Create realistic ticker data for a symbol"""
    if base_price is None:
        # Generate realistic base prices for different tickers
        base_prices = {
            "AAPL": 150.0, "MSFT": 300.0, "GOOGL": 2800.0, "TSLA": 250.0,
            "AMZN": 3300.0, "NVDA": 500.0, "META": 350.0, "NFLX": 500.0,
            "AMD": 100.0, "INTC": 50.0, "SHOP": 45.0, "SQ": 250.0,
            "ROKU": 300.0, "ZM": 100.0, "CRWD": 200.0
        }
        base_price = base_prices.get(symbol, 100.0)
    
    # Add some realistic price movement
    price_change = random.uniform(-0.02, 0.02)  # ±2% change
    current_price = base_price * (1 + price_change)
    
    # Create OHLC data
    high_price = current_price * random.uniform(1.0, 1.01)
    low_price = current_price * random.uniform(0.99, 1.0)
    open_price = base_price
    close_price = current_price
    
    return {
        "symbol": symbol,
        "price": round(current_price, 2),
        "open_price": round(open_price, 2),
        "high_price": round(high_price, 2),
        "low_price": round(low_price, 2),
        "close_price": round(close_price, 2),
        "volume": random.randint(1000000, 10000000),
        "timestamp": int(time.time()),
        "exchange": "NASDAQ",
        "candle_interval": "3m"
    }

def create_multi_ticker_webhook(tickers: List[str], format_type: str = "tickers") -> Dict[str, Any]:
    """Create webhook payload with multiple tickers"""
    if format_type == "tickers":
        # Format: {"tickers": [{"symbol": "AAPL", ...}, {"symbol": "SHOP", ...}]}
        return {
            "tickers": [create_ticker_data(ticker) for ticker in tickers],
            "candle_interval": "3m",
            "timestamp": int(time.time()),
            "batch_id": f"batch_{int(time.time())}"
        }
    elif format_type == "array":
        # Format: [{"symbol": "AAPL", ...}, {"symbol": "SHOP", ...}]
        return [create_ticker_data(ticker) for ticker in tickers]
    else:
        # Single ticker format
        return create_ticker_data(tickers[0])

async def send_webhook(session: aiohttp.ClientSession, payload: Dict[str, Any], description: str) -> Dict[str, Any]:
    """Send a webhook and return the response"""
    try:
        start_time = time.time()
        
        async with session.post(WEBHOOK_URL, json=payload) as response:
            response_time = time.time() - start_time
            response_data = await response.json()
            
            return {
                "description": description,
                "status_code": response.status,
                "response_time": response_time,
                "response_data": response_data,
                "success": response.status == 200,
                "ticker_count": len(payload.get("tickers", [payload])) if isinstance(payload, dict) else len(payload)
            }
            
    except Exception as e:
        return {
            "description": description,
            "status_code": 0,
            "response_time": 0,
            "response_data": {"error": str(e)},
            "success": False,
            "ticker_count": 0
        }

async def test_multi_ticker_formats():
    """Test different multi-ticker webhook formats"""
    print("🧪 Testing Multi-Ticker Webhook Formats")
    print("=" * 50)
    
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        
        # Test 1: Multi-ticker format
        print("📊 Testing Multi-Ticker Format...")
        tickers = random.sample(TEST_TICKERS, 5)  # Random 5 tickers
        payload = create_multi_ticker_webhook(tickers, "tickers")
        result1 = await send_webhook(session, payload, f"Multi-ticker ({len(tickers)} tickers)")
        
        if result1["success"]:
            print(f"✅ Multi-ticker format: {result1['ticker_count']} tickers processed in {result1['response_time']:.3f}s")
        else:
            print(f"❌ Multi-ticker format failed: {result1['response_data']}")
        
        await asyncio.sleep(1)  # Small delay between tests
        
        # Test 2: Array format
        print("\n📊 Testing Array Format...")
        tickers = random.sample(TEST_TICKERS, 3)  # Random 3 tickers
        payload = create_multi_ticker_webhook(tickers, "array")
        result2 = await send_webhook(session, payload, f"Array format ({len(tickers)} tickers)")
        
        if result2["success"]:
            print(f"✅ Array format: {result2['ticker_count']} tickers processed in {result2['response_time']:.3f}s")
        else:
            print(f"❌ Array format failed: {result2['response_data']}")
        
        await asyncio.sleep(1)
        
        # Test 3: Single ticker format
        print("\n📊 Testing Single Ticker Format...")
        ticker = random.choice(TEST_TICKERS)
        payload = create_multi_ticker_webhook([ticker], "single")
        result3 = await send_webhook(session, payload, f"Single ticker ({ticker})")
        
        if result3["success"]:
            print(f"✅ Single ticker format: {result3['ticker_count']} ticker processed in {result3['response_time']:.3f}s")
        else:
            print(f"❌ Single ticker format failed: {result3['response_data']}")
        
        return [result1, result2, result3]

async def test_high_volume():
    """Test high-volume webhook processing"""
    print("\n🔥 Testing High-Volume Processing...")
    print("=" * 40)
    
    timeout = aiohttp.ClientTimeout(total=60)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        
        # Send multiple webhooks with different ticker counts
        test_cases = [
            (3, "Small batch"),
            (5, "Medium batch"),
            (8, "Large batch"),
            (12, "Extra large batch")
        ]
        
        results = []
        for ticker_count, description in test_cases:
            tickers = random.sample(TEST_TICKERS, ticker_count)
            payload = create_multi_ticker_webhook(tickers, "tickers")
            
            result = await send_webhook(session, payload, f"{description} ({ticker_count} tickers)")
            results.append(result)
            
            if result["success"]:
                print(f"✅ {description}: {result['ticker_count']} tickers in {result['response_time']:.3f}s")
            else:
                print(f"❌ {description}: Failed - {result['response_data']}")
            
            await asyncio.sleep(0.5)  # Small delay
        
        return results

async def test_3_minute_candle_simulation():
    """Simulate 3-minute candle data flow"""
    print("\n⏰ Testing 3-Minute Candle Simulation...")
    print("=" * 45)
    
    timeout = aiohttp.ClientTimeout(total=120)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        
        # Simulate 3 webhooks over 9 minutes (3-minute intervals)
        results = []
        for i in range(3):
            # Create realistic 3-minute candle data
            tickers = random.sample(TEST_TICKERS, random.randint(4, 8))
            payload = create_multi_ticker_webhook(tickers, "tickers")
            
            # Add candle metadata
            payload["candle_start"] = int(time.time() - (i * 180))  # 3 minutes apart
            payload["candle_end"] = int(time.time() - (i * 180) + 180)
            
            result = await send_webhook(session, payload, f"Candle {i+1} ({len(tickers)} tickers)")
            results.append(result)
            
            if result["success"]:
                print(f"✅ Candle {i+1}: {result['ticker_count']} tickers processed")
            else:
                print(f"❌ Candle {i+1}: Failed - {result['response_data']}")
            
            if i < 2:  # Don't wait after the last one
                await asyncio.sleep(2)  # Wait 2 seconds between candles
        
        return results

async def main():
    """Main test function"""
    print("🚀 Starting Multi-Ticker Webhook Tests")
    print("=" * 60)
    
    try:
        # Test different formats
        format_results = await test_multi_ticker_formats()
        
        # Test high volume
        volume_results = await test_high_volume()
        
        # Test 3-minute candle simulation
        candle_results = await test_3_minute_candle_simulation()
        
        # Summary
        print("\n📊 Test Summary:")
        print("=" * 30)
        
        all_results = format_results + volume_results + candle_results
        successful = sum(1 for r in all_results if r["success"])
        total_tickers = sum(r["ticker_count"] for r in all_results if r["success"])
        
        print(f"Total webhooks: {len(all_results)}")
        print(f"Successful: {successful}")
        print(f"Failed: {len(all_results) - successful}")
        print(f"Total tickers processed: {total_tickers}")
        print(f"Success rate: {(successful / len(all_results)) * 100:.1f}%")
        
        if successful == len(all_results):
            print("\n🎉 All tests passed! Multi-ticker system is ready for production.")
        else:
            print(f"\n⚠️  {len(all_results) - successful} tests failed. Check the output above.")
        
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 