events {
    worker_connections 1024;
}

http {
    upstream tradingview_ingest {
        server tradingview-ingest:8001;
    }

    server {
        listen 8001;
        
        # Only allow POST requests to webhook endpoint
        location /webhook/tradingview {
            proxy_pass http://tradingview_ingest;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Only allow POST method
            if ($request_method != POST) {
                return 405;
            }
        }
        
        # Health check endpoint
        location /health {
            proxy_pass http://tradingview_ingest;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Block all other endpoints
        location / {
            return 404;
        }
    }
}
