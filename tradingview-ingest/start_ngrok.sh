#!/bin/bash

# Secure ngrok startup script for TradingView webhooks
# This script starts ngrok with strict security settings

set -e

echo "🔒 Starting ngrok with secure configuration..."

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not installed. Please install it first."
    exit 1
fi

# Check if auth token is set
if [ -z "$NGROK_AUTH_TOKEN" ]; then
    echo "❌ NGROK_AUTH_TOKEN environment variable is not set."
    echo "Please set it with: export NGROK_AUTH_TOKEN=your_token_here"
    exit 1
fi

# Check if webhook secret is set
if [ -z "$WEBHOOK_SECRET" ]; then
    echo "❌ WEBHOOK_SECRET environment variable is not set."
    echo "Please set it with: export WEBHOOK_SECRET=your_secret_here"
    exit 1
fi

echo "✅ Environment variables are configured"
echo "🔐 Webhook secret: ${WEBHOOK_SECRET:0:8}..."
echo "🌐 Starting ngrok tunnel..."

# Start ngrok with secure configuration
ngrok http 8001 \
    --config=./ngrok.yml \
    --log=stdout \
    --log-level=info \
    --region=us \
    --host-header=localhost:8001

echo "✅ ngrok tunnel started successfully!"
echo "📡 Your webhook URL will be displayed above"
echo "🔒 Only TradingView webhooks will be accepted"
echo "🚫 All other traffic is blocked" 