events {
    worker_connections 1024;
}

http {
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=30r/s;
    limit_req_zone $binary_remote_addr zone=webhook:10m rate=10r/s;
    
    # Upstream definitions (unified architecture)
    upstream api_backend {
        server api:8000;
    }
    
    upstream webhook_backend {
        server webhook-proxy:8001;
    }
    
    # HTTP to HTTPS redirect with health check exception
    server {
        listen 80;
        server_name _;

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        location / {
            return 301 https://$host$request_uri;
        }
    }
    
    # Main HTTPS server
    server {
        listen 443 ssl;
        http2 on;
        server_name _;
        
        # SSL configuration (self-signed for development)
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        
        # Security: Hide nginx version
        server_tokens off;
        
        # API endpoints (rate limited)
        location /api/ {
            # Rate limiting
            limit_req zone=api burst=50 nodelay;
            
            # Security: Only allow specific methods
            if ($request_method !~ ^(GET|POST|PUT|DELETE)$) {
                return 405;
            }
            
            # Proxy to API backend
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
        }
        
        # Webhook endpoint (rate limited)
        location /webhook/ {
            # Rate limiting
            limit_req zone=webhook burst=20 nodelay;
            
            # Security: Only allow POST method
            if ($request_method != POST) {
                return 405;
            }
            
            # Proxy to webhook backend
            proxy_pass http://webhook_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Default: deny all other requests
        location / {
            return 403 "Access Forbidden";
            add_header Content-Type text/plain;
        }
    }
} 