events {
    worker_connections 1024;
}

http {
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=webhook:10m rate=10r/s;
    
    # Upstream for webhook-ingest service (unified architecture)
    upstream webhook_backend {
        server webhook-ingest:8001;
    }
    
    server {
        listen 0.0.0.0:8001;
        
        # ONLY allow POST to webhook endpoint - everything else is forbidden
        location /webhook/tradingview {
            # Rate limiting
            limit_req zone=webhook burst=20 nodelay;
            
            # Security: Only allow POST method
            if ($request_method != POST) {
                return 405;
            }
            
            # Security: Validate content type
            # if ($content_type !~ ^(application/json|text/plain)$) {
            #     return 400;
            # }
            
            # Proxy to backend
            proxy_pass http://webhook_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
        }
        
        location /health {
            return 200 "OK";
            add_header Content-Type text/plain;
        }
        # BLOCK ALL OTHER ENDPOINTS - Return 403 Forbidden
        location / {
            return 403 "Access Forbidden - Only webhook endpoint allowed";
            add_header Content-Type text/plain;
        }
    }
} 