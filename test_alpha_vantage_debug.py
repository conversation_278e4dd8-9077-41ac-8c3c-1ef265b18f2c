#!/usr/bin/env python3
"""
Debug script to get detailed Alpha Vantage API responses.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def debug_alpha_vantage():
    """Debug Alpha Vantage API responses."""
    try:
        print("🔍 Debugging Alpha Vantage API")
        print("=" * 40)
        
        from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
        import httpx
        
        provider = AlphaVantageProvider()
        
        if not provider.is_configured():
            print("❌ Provider not configured")
            return
        
        print(f"✅ Provider configured with API key: {provider.api_key[:8]}...{provider.api_key[-4:]}")
        print(f"⏱️ Rate limit delay: {provider.rate_limit_delay} seconds")
        
        # Test direct API call
        print("\n🌐 Testing direct API call...")
        
        params = {
            "function": "GLOBAL_QUOTE",
            "symbol": "AAPL",
            "apikey": provider.api_key
        }
        
        print(f"📡 API URL: {provider.base_url}")
        print(f"📋 Parameters: {params}")
        
        async with httpx.AsyncClient(timeout=provider.timeout) as client:
            response = await client.get(provider.base_url, params=params)
            print(f"📊 Response status: {response.status_code}")
            print(f"📄 Response headers: {dict(response.headers)}")
            
            try:
                data = response.json()
                print(f"📝 Response data: {data}")
                
                # Check for specific error messages
                if "Error Message" in data:
                    print(f"❌ Alpha Vantage error: {data['Error Message']}")
                
                if "Note" in data:
                    print(f"⚠️ Alpha Vantage note: {data['Note']}")
                
                if "Global Quote" in data:
                    quote_data = data["Global Quote"]
                    print(f"📈 Quote data: {quote_data}")
                    
                    if quote_data:
                        price = quote_data.get("05. price")
                        if price:
                            print(f"💰 Price: {price}")
                        else:
                            print("❌ No price data in quote")
                    else:
                        print("❌ Empty quote data")
                        
            except Exception as e:
                print(f"❌ Error parsing JSON: {e}")
                print(f"📄 Raw response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main debug function."""
    await debug_alpha_vantage()
    print("\n✅ Alpha Vantage debugging completed!")

if __name__ == "__main__":
    asyncio.run(main()) 