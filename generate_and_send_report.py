#!/usr/bin/env python3
"""
Generate and send market report to Discord.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def generate_and_send_report():
    """Generate market report and send to Discord."""
    try:
        print("🚀 Generating Market Report for Discord")
        print("=" * 50)
        
        from src.core.automation.report_engine import AIReportEngine
        from src.core.automation.report_formatter import ReportFormatter, OutputFormat
        from src.core.automation.discord_handler import DiscordWebhookHandler
        
        # Generate the report
        print("📊 Generating daily market report...")
        engine = AIReportEngine()
        report = await engine.generate_daily_market_report()
        
        if not report:
            print("❌ Failed to generate report")
            return
        
        print(f"✅ Report generated: {report.title}")
        print(f"   Type: {report.report_type.value}")
        print(f"   AI Insights: {len(report.ai_insights)}")
        print(f"   Market Data: {len(report.market_data)} symbols")
        
        # Format the report for Discord
        print("\n🎨 Formatting report for Discord...")
        formatter = ReportFormatter()
        
        # Create formatted report
        formatted_report = formatter.format_for_discord(report)
        
        print("✅ Report formatted for Discord")
        print(f"   Content length: {len(formatted_report.content)} characters")
        
        # Show what the Discord message would look like
        print("\n📱 Discord Message Preview:")
        print("=" * 50)
        print(formatted_report.content)
        print("=" * 50)
        
        # Check if Discord webhook is configured
        webhook_url = os.getenv("DISCORD_WEBHOOK_URL")
        if not webhook_url or webhook_url == "https://discord.com/api/webhooks/REPLACE_ME/REPLACE_ME":
            print("\n⚠️  Discord webhook not configured!")
            print("To send to Discord, add DISCORD_WEBHOOK_URL to your .env file")
            print("Example: DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR_ID/YOUR_TOKEN")
            
            # Show how to set it up
            print("\n🔧 Setup Instructions:")
            print("1. Go to your Discord server")
            print("2. Right-click on the channel you want to send reports to")
            print("3. Select 'Edit Channel' > 'Integrations' > 'Webhooks'")
            print("4. Create a new webhook and copy the URL")
            print("5. Add it to your .env file")
            
        else:
            print(f"\n🔗 Discord webhook configured: {webhook_url[:50]}...")
            
            # Test the connection
            print("🧪 Testing Discord connection...")
            async with DiscordWebhookHandler(webhook_url) as handler:
                if await handler.test_connection():
                    print("✅ Discord connection successful!")
                    
                    # Send the report
                    print("📤 Sending report to Discord...")
                    if await handler.send_report(formatted_report):
                        print("✅ Report sent to Discord successfully!")
                    else:
                        print("❌ Failed to send report to Discord")
                else:
                    print("❌ Discord connection failed")
        
        print("\n🎯 Report generation complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(generate_and_send_report()) 