#!/usr/bin/env python3
"""
Test script for the new uniform Pine Script alert format
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from text_parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ars<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_uniform_format():
    """Test the new uniform pipe-separated format"""
    print("Testing new uniform alert format...")
    
    parser = PineScriptAlertParser()
    
    # Test the new uniform format
    test_alerts = [
        # Entry alerts
        "ENTRY|LONG|1756318028|AAPL|3M|150.50|152.00|154.00|156.00|148.00",
        "ENTRY|SHORT|1756318028|TSLA|5M|250.00|248.00|245.00|242.00|252.00",
        
        # TP hit alerts
        "TP_HIT|LONG_TP1|1756318028|AAPL|3M|150.50|152.00|154.00|156.00|148.00",
        "TP_HIT|LONG_TP2|1756318028|AAPL|3M|150.50|152.00|154.00|156.00|148.00",
        "TP_HIT|LONG_TP3|1756318028|AAPL|3M|150.50|152.00|154.00|156.00|148.00",
        
        # Short TP alerts
        "TP_HIT|SHORT_TP1|1756318028|TSLA|5M|250.00|248.00|245.00|242.00|252.00",
        "TP_HIT|SHORT_TP2|1756318028|TSLA|5M|250.00|248.00|245.00|242.00|252.00",
        "TP_HIT|SHORT_TP3|1756318028|TSLA|5M|250.00|248.00|245.00|242.00|252.00",
        
        # Stop loss alerts
        "SL_HIT|LONG_SL|1756318028|AAPL|3M|150.50|152.00|154.00|156.00|148.00",
        "SL_HIT|SHORT_SL|1756318028|TSLA|5M|250.00|248.00|245.00|242.00|252.00",
    ]
    
    for i, alert_text in enumerate(test_alerts):
        try:
            parsed = parser.parse_alert(alert_text)
            print(f"✓ Alert {i+1}: {parsed.alert_type} - {parsed.signal} - {parsed.symbol}")
            print(f"  Timeframe: {parsed.timeframe}")
            print(f"  Entry: {parsed.entry_price}, TP1: {parsed.tp1_price}, TP2: {parsed.tp2_price}, TP3: {parsed.tp3_price}, SL: {parsed.sl_price}")
            print(f"  Timestamp: {parsed.timestamp}")
            print()
        except Exception as e:
            print(f"✗ Alert {i+1} failed: {e}")
            print()

def test_legacy_format():
    """Test legacy emoji-based format as fallback"""
    print("Testing legacy alert format fallback...")
    
    parser = PineScriptAlertParser()
    
    # Test legacy format
    legacy_alerts = [
        "🚨 🟢 LONG TRADE ALERT 🟢 🚨\nSymbol: $AAPL (3M)\nEntry: 150.50 🦅\nTP1: 152.00 🎯\nTP2: 154.00 🎯\nTP3: 156.00 🎯\nSL: 148.00 ⚠️",
        "🔥🟢 TP1 HIT 🟢🔥\nSymbol: $AAPL (3M)\nEntry: 150.50 🦅\nTP1: 152.00 ✅\nTP2: 154.00 🎯\nTP3: 156.00 🎯\nSL: 148.00 ⚠️",
    ]
    
    for i, alert_text in enumerate(legacy_alerts):
        try:
            parsed = parser.parse_alert(alert_text)
            print(f"✓ Legacy Alert {i+1}: {parsed.alert_type} - {parsed.signal} - {parsed.symbol}")
            print(f"  Timeframe: {parsed.timeframe}")
            print(f"  Entry: {parsed.entry_price}, TP1: {parsed.tp1_price}, TP2: {parsed.tp2_price}, TP3: {parsed.tp3_price}, SL: {parsed.sl_price}")
            print()
        except Exception as e:
            print(f"✗ Legacy Alert {i+1} failed: {e}")
            print()

def test_multiple_alerts():
    """Test parsing multiple alerts from a single text"""
    print("Testing multiple alert parsing...")
    
    parser = PineScriptAlertParser()
    
    # Test multiple alerts separated by newlines
    multi_alert_text = """ENTRY|LONG|1756318028|AAPL|3M|150.50|152.00|154.00|156.00|148.00

TP_HIT|LONG_TP1|1756318028|AAPL|3M|150.50|152.00|154.00|156.00|148.00

SL_HIT|LONG_SL|1756318028|AAPL|3M|150.50|152.00|154.00|156.00|148.00"""
    
    try:
        alerts = parser.parse_multiple_alerts(multi_alert_text)
        print(f"✓ Parsed {len(alerts)} alerts from multi-alert text")
        for i, alert in enumerate(alerts):
            print(f"  Alert {i+1}: {alert.alert_type} - {alert.signal} - {alert.symbol}")
        print()
    except Exception as e:
        print(f"✗ Multi-alert parsing failed: {e}")
        print()

if __name__ == "__main__":
    print("=" * 60)
    print("TESTING UNIFORM PINE SCRIPT ALERT PARSER")
    print("=" * 60)
    print()
    
    test_uniform_format()
    test_legacy_format()
    test_multiple_alerts()
    
    print("=" * 60)
    print("TESTING COMPLETE")
    print("=" * 60) 