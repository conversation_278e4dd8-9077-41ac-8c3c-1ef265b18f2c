# TradingView Automation & Market Analysis Platform

## Overview

This is a systematic market data analysis platform that processes TradingView webhooks and provides data-driven trading insights through automated analysis.

## Documentation

All project documentation has been consolidated in the [unified_docs](unified_docs/) directory:

- [PROJECT_OVERVIEW.md](unified_docs/PROJECT_OVERVIEW.md) - Complete project documentation
- [Docker Configuration](unified_docs/DOCKER_SUMMARY.md) - Information about the Docker setup

## Docker Configuration

The project includes a reorganized Docker configuration system with improved structure and maintainability:

### Directory Structure
```
docker/
├── compose/                 # Docker Compose files
│   ├── production.yml       # Full production deployment
│   ├── development.yml      # Basic development setup
│   ├── development.optimized.yml  # Enhanced development setup
│   └── services/            # Service-specific configurations
│       ├── tradingview-ingest.yml  # Webhook processing system
│       └── pipeline.yml     # AI pipeline system
├── dockerfiles/             # Docker build files
│   ├── app.Dockerfile       # Main application
│   ├── bot.Dockerfile       # Discord bot service
│   ├── app.optimized.Dockerfile  # Optimized application build
│   ├── test.Dockerfile      # Testing environment
│   └── services/            # Service Dockerfiles
│       ├── webhook.Dockerfile     # Webhook receiver
│       ├── webhook.secure.Dockerfile  # Secure webhook receiver
│       ├── processor.Dockerfile   # Data processor
│       ├── monitor.Dockerfile     # Monitoring service
│       └── pipeline.Dockerfile    # Pipeline system
└── config/                  # Docker configuration files
    ├── env.template         # Template for environment variables
    ├── env.development      # Development environment variables
    └── env.production       # Production environment variables
```

### Usage

#### Production
```bash
# From the project root directory
docker-compose -f docker/compose/production.yml up --build
```

#### Development
```bash
# Basic development setup
docker-compose -f docker/compose/development.yml up --build

# Enhanced development setup
docker-compose -f docker/compose/development.optimized.yml up --build
```

#### Services
```bash
# Webhook processing system
docker-compose -f docker/compose/services/tradingview-ingest.yml up --build

# AI pipeline system
docker-compose -f docker/compose/services/pipeline.yml up --build
```

## License

This project is licensed under the MIT License.