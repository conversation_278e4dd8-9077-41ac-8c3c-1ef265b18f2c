#!/usr/bin/env python3
"""
Test Critical Pipeline Fixes

This script tests the fixes for critical pipeline errors:
1. Symbol extraction logic (should only extract $-prefixed symbols)
2. ResponseStyle enum conflicts
3. Template format errors
4. Data provider integration issues
"""

import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_symbol_extraction():
    """Test the fixed symbol extraction logic."""
    logger.info("Testing symbol extraction fixes...")
    
    try:
        from src.bot.pipeline.commands.ask.batch_processor import extract_symbols_from_query
        
        # Test cases that should work correctly now
        test_cases = [
            ("What is the price of $AAPL?", ["AAPL"]),
            ("Compare $TSLA and $NVDA", ["TSLA", "NVDA"]),
            ("What is the price of AAPL?", []),  # No $ prefix, should return empty
            ("Tell me about $SPY and $QQQ performance", ["SPY", "QQQ"]),
            ("How are stocks doing today?", []),  # No symbols, should return empty
            ("$MSFT vs $GOOGL analysis", ["MSFT", "GOOGL"]),
        ]
        
        all_passed = True
        for query, expected in test_cases:
            result = extract_symbols_from_query(query)
            if result == expected:
                logger.info(f"✅ '{query}' -> {result} (expected {expected})")
            else:
                logger.error(f"❌ '{query}' -> {result} (expected {expected})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"Error testing symbol extraction: {e}")
        return False


def test_response_style_enums():
    """Test ResponseStyle enum consistency."""
    logger.info("Testing ResponseStyle enum fixes...")
    
    try:
        from src.bot.pipeline.commands.ask.stages.response_templates import ResponseStyle
        from src.bot.pipeline.commands.ask.stages.depth_style_analyzer import EnhancedStyleAnalyzer
        
        # Test that all ResponseStyle values are valid
        valid_styles = [
            ResponseStyle.SIMPLE,
            ResponseStyle.DETAILED, 
            ResponseStyle.TECHNICAL,
            ResponseStyle.FUNDAMENTAL,
            ResponseStyle.ACADEMIC,
            ResponseStyle.TRADING,
            ResponseStyle.PROFESSIONAL  # This should be an alias for DETAILED
        ]
        
        logger.info(f"Available ResponseStyle values: {[style.value for style in valid_styles]}")
        
        # Test that the style analyzer can be initialized without errors
        analyzer = EnhancedStyleAnalyzer()
        logger.info("✅ EnhancedStyleAnalyzer initialized successfully")
        
        # Test that PROFESSIONAL is correctly aliased to DETAILED
        if ResponseStyle.PROFESSIONAL.value == ResponseStyle.DETAILED.value:
            logger.info("✅ ResponseStyle.PROFESSIONAL correctly aliased to DETAILED")
        else:
            logger.error(f"❌ ResponseStyle.PROFESSIONAL = {ResponseStyle.PROFESSIONAL.value}, expected 'detailed'")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing ResponseStyle enums: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_template_formatting():
    """Test template formatting fixes."""
    logger.info("Testing template formatting fixes...")
    
    try:
        from src.bot.pipeline.commands.ask.stages.response_templates import ResponseTemplateEngine
        from src.bot.pipeline.commands.ask.stages.response_templates import ResponseStyle
        
        # Create template engine
        engine = ResponseTemplateEngine()
        
        # Test data with various types that might cause format errors
        test_data = {
            'symbol': 'AAPL',
            'price': 150.50,
            'change': 2.5,
            'change_percent': 1.67,
            'volume': 50000000,
            'confidence': 85,
            'action': 'BUY',
            'risk_level': 'Medium',
            'data_quality': 90,
            'timestamp': '2024-01-15T10:30:00',
            'market_trend': 'Bullish trend detected',
            'technical_analysis': 'RSI indicates oversold conditions',
            'data_freshness_warning': '✅ Data is current'
        }
        
        # Test template generation
        try:
            response = engine.generate_response(
                template_type="stock_analysis",
                style=ResponseStyle.DETAILED.value,
                data=test_data,
                query_analysis={}
            )
            
            if response and len(response) > 100:
                logger.info("✅ Template generation successful")
                logger.info(f"Response length: {len(response)} characters")
                return True
            else:
                logger.error("❌ Template generation returned empty or short response")
                return False
                
        except Exception as e:
            logger.error(f"❌ Template generation failed: {e}")
            return False
        
    except Exception as e:
        logger.error(f"Error testing template formatting: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_data_provider_imports():
    """Test that data provider imports work correctly."""
    logger.info("Testing data provider imports...")
    
    try:
        # Test yfinance import (should work in Docker)
        try:
            import yfinance as yf
            logger.info("✅ yfinance import successful")
            yfinance_available = True
        except ImportError:
            logger.warning("⚠️ yfinance not available (expected in development environment)")
            yfinance_available = False
        
        # Test data provider manager
        from src.data.providers.manager import DataProviderManager
        manager = DataProviderManager()
        logger.info("✅ DataProviderManager import successful")
        
        # Test provider config
        from src.data.providers.config import get_enabled_providers
        enabled = get_enabled_providers()
        logger.info(f"✅ Enabled providers: {enabled}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing data provider imports: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_ai_routing_service():
    """Test AI routing service with fixed configurations."""
    logger.info("Testing AI routing service...")
    
    try:
        from src.bot.pipeline.commands.ask.stages.ai_routing_service import AdvancedAIRoutingService
        
        # Initialize routing service
        routing_service = AdvancedAIRoutingService()
        logger.info(f"✅ AI routing service initialized with {len(routing_service.models)} models")
        
        # Test query analysis
        test_query = "What is the current price of $AAPL?"
        analysis = routing_service.analyze_query_complexity(test_query)
        logger.info(f"✅ Query analysis successful: {analysis.complexity.value}")
        
        # Test model selection
        optimal_model, fallback_models = routing_service.select_optimal_model(analysis)
        logger.info(f"✅ Model selection successful: {optimal_model.name}")
        logger.info(f"   Fallback models: {[m.name for m in fallback_models]}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing AI routing service: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """Run all critical pipeline tests."""
    logger.info("Starting Critical Pipeline Fixes Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Symbol Extraction Logic", test_symbol_extraction),
        ("ResponseStyle Enum Consistency", test_response_style_enums),
        ("Template Formatting", test_template_formatting),
        ("Data Provider Imports", test_data_provider_imports),
        ("AI Routing Service", test_ai_routing_service)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'=' * 20} {test_name} {'=' * 20}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        except Exception as e:
            logger.error(f"❌ ERROR in {test_name}: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'=' * 60}")
    logger.info("CRITICAL PIPELINE FIXES TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All critical pipeline fixes are working correctly!")
        return 0
    else:
        logger.error("❌ Some critical issues remain. Check the logs above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
