"""
Supabase Client for TradingView Automation
"""

# Import the shared Supabase client implementation
from src.shared.database.supabase_sdk_client import supabase_sdk_client

# Re-export the client for backward compatibility
supabase_client = supabase_sdk_client

# Re-export test function for backward compatibility
def test_supabase_connection(correlation_id=None):
    """Test Supabase connection and basic functionality"""
    return supabase_sdk_client.initialize()
