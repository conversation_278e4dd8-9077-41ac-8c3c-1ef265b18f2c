from __future__ import annotations

import os
import socket
import time
import asyncio
from contextlib import contextmanager, asynccontextmanager
from typing import Any, Dict, Generator, Optional, TypeVar, cast, AsyncGenerator
from urllib.parse import parse_qsl, urlencode, urlparse, urlunparse

from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import QueuePool
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine

from src.core.config_manager import get_config
from src.core.exceptions import ConfigurationError
from src.core.logger import get_logger

from .query_wrapper import (
    execute_many_with_correlation,
    execute_query_with_correlation,
    transaction_with_correlation,
)

# Type variables for better type hints
T = TypeVar('T')

# Type aliases
DatabaseSession = Session

logger = get_logger(__name__)

# Initialize engine as None, will be set in configure_engine()
engine: Optional[Engine] = None

async def configure_engine() -> AsyncEngine:
    """
    Configure and return the async database engine.
    
    This function should be called during application startup.
    
    Returns:
        AsyncEngine: Configured SQLAlchemy async engine
        
    Raises:
        ConfigurationError: If there's an error configuring the engine
    """
    global engine, SessionLocal
    
    if engine is not None:
        return engine
        
    try:
        config = get_config()
        db_url = config.get('database', 'url', '')
        if not db_url:
            raise ConfigurationError("Database URL is not configured. Please set DATABASE_URL in your environment.")
        
        if not db_url.startswith('postgresql+asyncpg://'):
            if db_url.startswith('postgresql://'):
                db_url = db_url.replace('postgresql://', 'postgresql+asyncpg://')
            else:
                raise ConfigurationError(f"Invalid database URL for async engine. Expected postgresql+asyncpg://, got: {db_url}")

        # Simplified connection optimization
        try:
            parsed = urlparse(db_url)
            query = dict(parse_qsl(parsed.query))
            query.setdefault('ssl', 'require')
            query.setdefault('timeout', str(config.get('database', 'connect_timeout', 10)))
            
            # Use DNS round-robin, rely on asyncpg's handling
            hostname = parsed.hostname or ''
            if 'supabase.co' in hostname:
                 logger.info("Connecting to Supabase. Ensure network policies allow DNS resolution.")

            db_url = urlunparse(parsed._replace(query=urlencode(query)))
            logger.info("Applied TLS and connection optimizations for async database URL")
        except Exception as e:
            logger.warning(f"Could not optimize database URL: {e}")

        engine = create_async_engine(
            db_url,
            poolclass=QueuePool,
            pool_size=config.get('database', 'pool_size', 10),
            max_overflow=config.get('database', 'max_overflow', 20),
            pool_pre_ping=True,
            pool_recycle=1800,
            pool_timeout=30,
            echo=config.get('app', 'debug', False),
        )
        
        SessionLocal = sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autocommit=False,
            autoflush=False,
        )
        
        logger.info("Async PostgreSQL database engine and session factory created successfully")
        return engine
    except Exception as e:
        logger.error(f"Database connection error: {e}", exc_info=True)
        raise ConfigurationError(f"Could not create async database engine: {e}")


# Base class for declarative models
Base = declarative_base()

@asynccontextmanager
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency that creates a new async database session for each request.
    """
    if SessionLocal is None:
        await configure_engine()
    
    if SessionLocal is None:
        logger.error("Cannot create database session: SessionLocal is None")
        raise ConfigurationError("Database session factory not initialized")
        
    async with SessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

async def init_db():
    """
    Initialize the database by creating all tables.
    """
    try:
        if engine is None:
            await configure_engine()
        
        if engine is None:
            raise ConfigurationError("Cannot initialize database: engine is None")

        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database tables: {e}", exc_info=True)
        raise ConfigurationError(f"Could not initialize database tables: {e}")

async def verify_db_connectivity(retries: int = 5, initial_delay: float = 1.0) -> None:
    """
    Verify async database connectivity with retries and exponential backoff.
    """
    if engine is None:
        await configure_engine()
    
    if engine is None:
        raise ConfigurationError("Cannot verify connectivity: engine is None")
    
    delay = initial_delay
    last_err = None
    for attempt in range(1, retries + 1):
        try:
            async with engine.connect() as conn:
                await conn.execute(text("SELECT 1"))
            logger.info("Database connectivity verified")
            return
        except Exception as e:
            last_err = e
            logger.warning(f"DB connectivity attempt {attempt}/{retries} failed: {e}. Retrying in {delay:.1f}s")
            await asyncio.sleep(delay)
            delay = min(delay * 2, 8.0)
    raise ConfigurationError(f"Database connectivity could not be verified after retries: {last_err}")

@asynccontextmanager
async def get_db_with_correlation(correlation_id: Optional[str] = None) -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency that creates a new async database session with correlation ID support.
    """
    if correlation_id:
        logger.set_correlation_id(correlation_id)
    
    if SessionLocal is None:
        await configure_engine()

    if SessionLocal is None:
        logger.error("Cannot create database session: SessionLocal is None", extra={"correlation_id": correlation_id})
        raise ConfigurationError("Database session factory not initialized")
    
    async with SessionLocal() as session:
        logger.debug("Database session created", extra={"correlation_id": correlation_id})
        try:
            yield session
        except Exception:
            await session.rollback()
            logger.error("Rolling back session due to exception", extra={"correlation_id": correlation_id}, exc_info=True)
            raise
        finally:
            await session.close()
            logger.debug("Database session closed", extra={"correlation_id": correlation_id})

@asynccontextmanager
async def get_db_connection(correlation_id: Optional[str] = None) -> AsyncGenerator[AsyncSession, None]:
    """
    Get an async database connection with correlation ID support.
    """
    if correlation_id:
        logger.set_correlation_id(correlation_id)
    
    logger.debug("Database connection requested", extra={"correlation_id": correlation_id})
    
    if engine is None:
        await configure_engine()
    
    if engine is None:
        logger.error("Cannot create database connection: engine is None", extra={"correlation_id": correlation_id})
        raise ConfigurationError("Database engine not initialized")
    
    async with engine.connect() as connection:
        try:
            yield connection
        finally:
            await connection.close()