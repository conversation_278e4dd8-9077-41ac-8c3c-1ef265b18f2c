"""
Enhanced Redis Cache Manager

This module provides advanced caching capabilities on top of the Redis connection manager.
Features include:
- TTL management with configurable defaults
- LRU cache implementation for high-performance caching
- JSON serialization/deserialization
- Automatic key prefixing for namespacing
"""

import json
import time
import logging
import functools
from typing import Any, Dict, List, Optional, Union, Callable, TypeVar, cast
from functools import lru_cache

import redis.asyncio as redis
from .redis_manager import redis_manager, get_redis_client

logger = logging.getLogger(__name__)

# Type variables for function signatures
T = TypeVar('T')
R = TypeVar('R')

# Default cache settings
DEFAULT_TTL = 600  # 10 minutes (increased from 5 minutes)
DEFAULT_LRU_MAXSIZE = 128  # Default size for in-memory LRU cache
DEFAULT_KEY_PREFIX = "app:"  # Default namespace prefix for Redis keys

class CacheManager:
    """
    Enhanced cache manager that provides advanced caching capabilities
    on top of the Redis connection manager.
    """
    
    def __init__(
        self,
        namespace: str = "cache",
        default_ttl: int = DEFAULT_TTL,
        lru_maxsize: int = DEFAULT_LRU_MAXSIZE
    ):
        """
        Initialize the cache manager.
        
        Args:
            namespace: Namespace prefix for cache keys
            default_ttl: Default TTL in seconds for cached items
            lru_maxsize: Maximum size for in-memory LRU cache
        """
        self.namespace = namespace
        self.default_ttl = default_ttl
        self.lru_maxsize = lru_maxsize
        self._local_cache = {}  # Simple in-memory cache for frequently accessed items
        
    def _build_key(self, key: str) -> str:
        """
        Build a namespaced Redis key.
        
        Args:
            key: Base key name
            
        Returns:
            Namespaced key
        """
        return f"{DEFAULT_KEY_PREFIX}{self.namespace}:{key}"
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value from cache.
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        # Check local LRU cache first for performance
        local_key = self._build_key(key)
        if local_key in self._local_cache:
            value, expiry = self._local_cache[local_key]
            if expiry > time.time():
                logger.debug(f"Cache hit (local): {key}")
                return value
            else:
                # Expired from local cache
                del self._local_cache[local_key]
        
        # Check Redis cache
        try:
            redis_client = await get_redis_client()
            if redis_client:
                redis_key = self._build_key(key)
                value = await redis_client.get(redis_key)
                
                if value is not None:
                    logger.debug(f"Cache hit (Redis): {key}")
                    try:
                        # Deserialize JSON data
                        deserialized = json.loads(value)
                        
                        # Store in local cache with TTL
                        ttl = await redis_client.ttl(redis_key)
                        if ttl > 0:
                            self._update_local_cache(local_key, deserialized, ttl)
                            
                        return deserialized
                    except json.JSONDecodeError:
                        # If not JSON, return as is
                        return value
                else:
                    logger.debug(f"Cache miss: {key}")
            else:
                logger.warning("Redis client not available for cache get")
        except Exception as e:
            logger.error(f"Error getting from cache: {e}")
            
        return default
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set a value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for default)
            
        Returns:
            bool: True if successful, False otherwise
        """
        ttl = ttl if ttl is not None else self.default_ttl
        
        try:
            # Serialize value to JSON if possible
            if not isinstance(value, (str, bytes)):
                try:
                    serialized = json.dumps(value)
                except (TypeError, ValueError):
                    logger.warning(f"Could not serialize value for key {key}, storing as string")
                    serialized = str(value)
            else:
                serialized = value
                
            # Store in Redis
            redis_client = await get_redis_client()
            if redis_client:
                redis_key = self._build_key(key)
                await redis_client.set(redis_key, serialized, ex=ttl)
                
                # Also update local cache
                if not isinstance(value, (str, bytes)):
                    self._update_local_cache(redis_key, value, ttl)
                    
                return True
            else:
                logger.warning("Redis client not available for cache set")
        except Exception as e:
            logger.error(f"Error setting cache: {e}")
            
        return False
    
    def _update_local_cache(self, key: str, value: Any, ttl: int):
        """
        Update the local LRU cache with a value and expiry time.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds
        """
        # Implement LRU behavior by removing oldest items if cache is full
        if len(self._local_cache) >= self.lru_maxsize:
            # Remove oldest item (first item in dict)
            try:
                oldest_key = next(iter(self._local_cache))
                del self._local_cache[oldest_key]
            except (StopIteration, KeyError):
                pass
                
        # Store with absolute expiry time
        expiry = time.time() + ttl
        self._local_cache[key] = (value, expiry)
    
    async def delete(self, key: str) -> bool:
        """
        Delete a value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Remove from local cache
            local_key = self._build_key(key)
            if local_key in self._local_cache:
                del self._local_cache[local_key]
            
            # Remove from Redis
            redis_client = await get_redis_client()
            if redis_client:
                redis_key = self._build_key(key)
                await redis_client.delete(redis_key)
                return True
            else:
                logger.warning("Redis client not available for cache delete")
        except Exception as e:
            logger.error(f"Error deleting from cache: {e}")
            
        return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in cache.
        
        Args:
            key: Cache key
            
        Returns:
            bool: True if key exists, False otherwise
        """
        # Check local cache first
        local_key = self._build_key(key)
        if local_key in self._local_cache:
            _, expiry = self._local_cache[local_key]
            if expiry > time.time():
                return True
            else:
                # Expired from local cache
                del self._local_cache[local_key]
        
        # Check Redis
        try:
            redis_client = await get_redis_client()
            if redis_client:
                redis_key = self._build_key(key)
                return bool(await redis_client.exists(redis_key))
            else:
                logger.warning("Redis client not available for cache exists check")
        except Exception as e:
            logger.error(f"Error checking cache existence: {e}")
            
        return False
    
    async def get_ttl(self, key: str) -> int:
        """
        Get the TTL of a cached key in seconds.
        
        Args:
            key: Cache key
            
        Returns:
            int: TTL in seconds, -1 if key doesn't exist, -2 if key exists but has no TTL
        """
        try:
            redis_client = await get_redis_client()
            if redis_client:
                redis_key = self._build_key(key)
                return await redis_client.ttl(redis_key)
            else:
                logger.warning("Redis client not available for TTL check")
        except Exception as e:
            logger.error(f"Error getting TTL: {e}")
            
        return -1
    
    async def clear_namespace(self) -> int:
        """
        Clear all keys in the current namespace.
        
        Returns:
            int: Number of keys deleted
        """
        try:
            # Clear local cache entries for this namespace
            namespace_prefix = f"{DEFAULT_KEY_PREFIX}{self.namespace}:"
            keys_to_delete = [k for k in self._local_cache if k.startswith(namespace_prefix)]
            for k in keys_to_delete:
                del self._local_cache[k]
            
            # Clear Redis keys
            redis_client = await get_redis_client()
            if redis_client:
                pattern = f"{namespace_prefix}*"
                cursor = b'0'
                deleted_count = 0
                
                while cursor:
                    cursor, keys = await redis_client.scan(cursor=cursor, match=pattern, count=100)
                    if keys:
                        deleted_count += await redis_client.delete(*keys)
                    
                    if cursor == b'0':
                        break
                        
                return deleted_count
            else:
                logger.warning("Redis client not available for namespace clearing")
        except Exception as e:
            logger.error(f"Error clearing namespace: {e}")
            
        return 0
    
    def cached(self, ttl: Optional[int] = None) -> Callable[[Callable[..., R]], Callable[..., R]]:
        """
        Decorator for caching function results.
        
        Args:
            ttl: Time-to-live in seconds (None for default)
            
        Returns:
            Decorator function
        """
        def decorator(func: Callable[..., R]) -> Callable[..., R]:
            @functools.wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> R:
                # Create a cache key from function name and arguments
                key_parts = [func.__name__]
                key_parts.extend(str(arg) for arg in args)
                key_parts.extend(f"{k}:{v}" for k, v in sorted(kwargs.items()))
                cache_key = ":".join(key_parts)
                
                # Try to get from cache
                cached_result = await self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Call the function and cache the result
                result = await func(*args, **kwargs)
                await self.set(cache_key, result, ttl)
                return result
                
            return wrapper
        return decorator

# Create default cache manager instances for different domains
market_data_cache = CacheManager(namespace="market_data", default_ttl=900)  # 15 minutes
user_data_cache = CacheManager(namespace="user_data", default_ttl=1800)  # 30 minutes
analysis_cache = CacheManager(namespace="analysis", default_ttl=1200)  # 20 minutes

# Convenience function to get a cache manager for a specific namespace
def get_cache_manager(namespace: str, default_ttl: Optional[int] = None) -> CacheManager:
    """
    Get a cache manager for a specific namespace.
    
    Args:
        namespace: Cache namespace
        default_ttl: Default TTL in seconds (None for default)
        
    Returns:
        CacheManager: Cache manager instance
    """
    ttl = default_ttl if default_ttl is not None else DEFAULT_TTL
    return CacheManager(namespace=namespace, default_ttl=ttl)
