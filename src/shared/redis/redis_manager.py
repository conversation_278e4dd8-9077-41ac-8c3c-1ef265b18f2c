"""
Centralized Redis Connection Manager

This module provides a singleton Redis client that can be used across the application.
It handles connection pooling, error handling, and consistent environment variable usage.
"""

import os
import asyncio
import logging
import time
from typing import Optional, Dict, Any, Union, Tuple
import redis.asyncio as redis
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class RedisConnectionManager:
    """
    Singleton Redis connection manager that provides a centralized way to
    access Redis throughout the application.
    
    Features:
    - Singleton pattern ensures only one connection pool is created
    - Connection pooling for efficient resource usage
    - Consistent environment variable handling
    - Proper error handling and logging
    - No localhost fallbacks in production
    """
    
    _instance = None
    _client: Optional[redis.Redis] = None
    _initialized = False
    _connection_lock = asyncio.Lock()
    _health_check_task = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisConnectionManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._initialized = True
            self._connection_attempts = 0
            self._max_retries = 5
            self._retry_delay = 2  # seconds
            self._default_ttl = int(os.getenv("REDIS_DEFAULT_TTL", "600"))  # 10 minutes default TTL
            
            # Read configuration from environment variables
            from src.core.config_manager import get_config
            config = get_config()
            redis_config = config.get_redis_config()
            self._host = os.getenv("REDIS_HOST", "redis")
            self._port = int(os.getenv("REDIS_PORT", "6379"))
            self._password = os.getenv("REDIS_PASSWORD", "")
            self._db = int(os.getenv("REDIS_DB", "0"))
            self._pool_size = int(os.getenv("REDIS_POOL_SIZE", "10"))
            from src.core.config_manager import get_config
            config = get_config()
            self._environment = config.get('app', 'environment', 'development')
            
            # URL construction for compatibility
            self._url = redis_config.url
            if not self._url:
                protocol = "rediss" if os.getenv("REDIS_SSL", "false").lower() == "true" else "redis"
                auth = f":{self._password}@" if self._password else ""
                self._url = f"{protocol}://{auth}{self._host}:{self._port}/{self._db}"
    
    async def initialize(self) -> bool:
        """
        Initialize the Redis connection.
        
        Returns:
            bool: True if connection was successful, False otherwise
        """
        if self._client is not None:
            return True
            
        async with self._connection_lock:
            if self._client is not None:  # Double-check inside lock
                return True
                
            try:
                logger.info(f"Initializing Redis connection to {self._host}:{self._port}")
                
                # Create connection pool with appropriate settings
                connection_pool = redis.ConnectionPool.from_url(
                    self._url,
                    max_connections=self._pool_size,
                    decode_responses=True
                )
                
                # Create Redis client with connection pool
                self._client = redis.Redis(
                    connection_pool=connection_pool
                )
                
                # Test connection
                await self._client.ping()
                logger.info("Redis connection established successfully")
                
                # Start health check task
                self._start_health_check()
                
                return True
                
            except Exception as e:
                self._connection_attempts += 1
                
                # In production, fail fast if Redis is required
                if self._environment == "production":
                    logger.error(f"Failed to connect to Redis: {e}")
                    if self._connection_attempts >= self._max_retries:
                        logger.critical("Max Redis connection attempts reached in production environment")
                        # Don't raise in __init__, but log critical error
                        return False
                else:
                    # In development, log warning but allow application to continue
                    logger.warning(f"Failed to connect to Redis: {e}. Application will continue without Redis.")
                    return False
    
    def _start_health_check(self):
        """Start background task for periodic health checks"""
        if self._health_check_task is None or self._health_check_task.done():
            self._health_check_task = asyncio.create_task(self._health_check_loop())
    
    async def _health_check_loop(self):
        """Periodically check Redis connection health"""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                if self._client:
                    await self._client.ping()
            except Exception as e:
                logger.warning(f"Redis health check failed: {e}")
                # Try to reconnect
                self._client = None
                await self.initialize()
    
    async def get_client(self) -> Optional[redis.Redis]:
        """
        Get the Redis client instance, initializing if necessary.
        
        Returns:
            Optional[redis.Redis]: Redis client or None if connection failed
        """
        if self._client is None:
            await self.initialize()
        return self._client
    
    @asynccontextmanager
    async def client(self):
        """
        Context manager for Redis client access.
        
        Usage:
            async with redis_manager.client() as redis_client:
                await redis_client.set("key", "value")
        
        Yields:
            redis.Redis: Redis client
        
        Raises:
            ConnectionError: If Redis connection is not available
        """
        client = await self.get_client()
        if client is None:
            raise ConnectionError("Redis connection not available")
        try:
            yield client
        except redis.RedisError as e:
            logger.error(f"Redis operation error: {e}")
            raise
    
    async def close(self):
        """Close the Redis connection"""
        if self._client:
            await self._client.close()
            self._client = None
            logger.info("Redis connection closed")
            
            # Cancel health check task
            if self._health_check_task:
                self._health_check_task.cancel()
                try:
                    await self._health_check_task
                except asyncio.CancelledError:
                    pass
                self._health_check_task = None

# Singleton instance for easy import and use
redis_manager = RedisConnectionManager()

# Convenience function to get Redis client
async def get_redis_client() -> Optional[redis.Redis]:
    """
    Get the Redis client instance.
    
    Returns:
        Optional[redis.Redis]: Redis client or None if connection failed
    """
    return await redis_manager.get_client()


async def set_with_ttl(key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """
    Set a value in Redis with TTL.
    
    Args:
        key: Redis key
        value: Value to store
        ttl: Time-to-live in seconds (None for default)
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        client = await get_redis_client()
        if client:
            ttl = ttl if ttl is not None else redis_manager._default_ttl
            await client.set(key, value, ex=ttl)
            return True
    except Exception as e:
        logger.error(f"Error setting Redis key {key}: {e}")
    return False


async def get_with_ttl(key: str) -> Tuple[Optional[str], int]:
    """
    Get a value from Redis with its remaining TTL.
    
    Args:
        key: Redis key
        
    Returns:
        Tuple[Optional[str], int]: (value, ttl) where ttl is in seconds
    """
    try:
        client = await get_redis_client()
        if client:
            value = await client.get(key)
            ttl = await client.ttl(key) if value is not None else -1
            return value, ttl
        return None, -1
    except Exception as e:
        logger.error(f"Error getting Redis key {key}: {e}")
        return None, -1
