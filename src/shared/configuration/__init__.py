"""
Centralized Configuration Module
Provides type-safe, validated configuration management for the entire application.
"""

from src.core.config_manager import config as Config, get_config
from src.core.config_manager import ConfigurationError as ConfigValidationError

def validate_env_var(key: str, value: str, validator=None):
    """
    Compatibility function for validating environment variables.
    
    Args:
        key: Environment variable name
        value: Environment variable value
        validator: Optional validation function
    
    Returns:
        Validated value
    
    Raises:
        ConfigValidationError if validation fails
    """
    try:
        # If a validator is provided, use it
        if validator:
            return validator(value)
        
        # Default validation (just return the value)
        return value
    except Exception as e:
        raise ConfigValidationError(f"Validation failed for {key}: {str(e)}")

__all__ = [
    'Config',
    'get_config',
    'validate_env_var',
    'ConfigValidationError'
]