"""
Core technical indicator calculation functions.
All functions are designed to work with pandas DataFrames and support configurable parameters.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime

from src.core.logger import get_logger

logger = get_logger(__name__)


def calculate_sma(series: pd.Series, window: int = 20) -> Optional[float]:
    """
    Calculate Simple Moving Average (SMA).
    
    Args:
        series: Pandas Series of prices (close, open, high, low)
        window: Moving average window size
        
    Returns:
        float: SMA value or None if insufficient data
    """
    try:
        if len(series) < window:
            return None
        sma = series.rolling(window=window).mean().iloc[-1]
        return float(sma) if not pd.isna(sma) else None
    except Exception as e:
        logger.error(f"Error calculating SMA: {e}")
        return None


def calculate_ema(series: pd.Series, span: int = 20) -> Optional[float]:
    """
    Calculate Exponential Moving Average (EMA).
    
    Args:
        series: Pandas Series of prices
        span: EMA span parameter
        
    Returns:
        float: EMA value or None if insufficient data
    """
    try:
        if len(series) < span:
            return None
        ema = series.ewm(span=span).mean().iloc[-1]
        return float(ema) if not pd.isna(ema) else None
    except Exception as e:
        logger.error(f"Error calculating EMA: {e}")
        return None


def calculate_rsi(prices: pd.Series, period: int = 14) -> Optional[float]:
    """
    Calculate Relative Strength Index (RSI).
    
    Args:
        prices: Pandas Series of closing prices
        period: RSI period (typically 14)
        
    Returns:
        float: RSI value between 0-100 or None if insufficient data
    """
    try:
        if len(prices) < period + 1:
            return None
        
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        # Avoid division by zero
        rs = gain / loss.where(loss != 0, 1e-9)
        rsi = 100 - (100 / (1 + rs))
        
        return float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else None
    except Exception as e:
        logger.error(f"Error calculating RSI: {e}")
        return None


def calculate_macd(prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, Optional[float]]:
    """
    Calculate MACD (Moving Average Convergence Divergence).
    
    Args:
        prices: Pandas Series of closing prices
        fast: Fast EMA period (typically 12)
        slow: Slow EMA period (typically 26)
        signal: Signal line period (typically 9)
        
    Returns:
        Dict with MACD line, signal line, and histogram values
    """
    try:
        if len(prices) < slow:
            return {"macd": None, "signal": None, "histogram": None}
        
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        
        return {
            "macd": float(macd_line.iloc[-1]) if not pd.isna(macd_line.iloc[-1]) else None,
            "signal": float(signal_line.iloc[-1]) if not pd.isna(signal_line.iloc[-1]) else None,
            "histogram": float(histogram.iloc[-1]) if not pd.isna(histogram.iloc[-1]) else None
        }
    except Exception as e:
        logger.error(f"Error calculating MACD: {e}")
        return {"macd": None, "signal": None, "histogram": None}


def calculate_bollinger_bands(prices: pd.Series, window: int = 20, num_std: float = 2.0) -> Dict[str, Optional[float]]:
    """
    Calculate Bollinger Bands.
    
    Args:
        prices: Pandas Series of closing prices
        window: Moving average window (typically 20)
        num_std: Number of standard deviations for bands (typically 2.0)
        
    Returns:
        Dict with middle, upper, and lower band values
    """
    try:
        if len(prices) < window:
            return {"middle": None, "upper": None, "lower": None}
        
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        
        return {
            "middle": float(sma.iloc[-1]) if not pd.isna(sma.iloc[-1]) else None,
            "upper": float(upper_band.iloc[-1]) if not pd.isna(upper_band.iloc[-1]) else None,
            "lower": float(lower_band.iloc[-1]) if not pd.isna(lower_band.iloc[-1]) else None
        }
    except Exception as e:
        logger.error(f"Error calculating Bollinger Bands: {e}")
        return {"middle": None, "upper": None, "lower": None}


def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> Optional[float]:
    """
    Calculate Average True Range (ATR).
    
    Args:
        high: Pandas Series of high prices
        low: Pandas Series of low prices
        close: Pandas Series of closing prices
        period: ATR period (typically 14)
        
    Returns:
        float: ATR value or None if insufficient data
    """
    try:
        if len(high) < period or len(low) < period or len(close) < period:
            return None
        
        # Calculate True Range
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        atr = true_range.rolling(window=period).mean()
        return float(atr.iloc[-1]) if not pd.isna(atr.iloc[-1]) else None
    except Exception as e:
        logger.error(f"Error calculating ATR: {e}")
        return None


def calculate_vwap(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> Optional[float]:
    """
    Calculate Volume Weighted Average Price (VWAP).
    
    Args:
        high: Pandas Series of high prices
        low: Pandas Series of low prices
        close: Pandas Series of closing prices
        volume: Pandas Series of volume data
        
    Returns:
        float: VWAP value or None if insufficient data
    """
    try:
        if len(high) == 0 or len(low) == 0 or len(close) == 0 or len(volume) == 0:
            return None
        
        # Calculate typical price
        typical_price = (high + low + close) / 3
        
        # Calculate VWAP
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        return float(vwap.iloc[-1]) if not pd.isna(vwap.iloc[-1]) else None
    except Exception as e:
        logger.error(f"Error calculating VWAP: {e}")
        return None


def calculate_volume_analysis(volume: pd.Series, window: int = 20) -> Dict[str, Optional[float]]:
    """
    Calculate volume analysis indicators.
    
    Args:
        volume: Pandas Series of volume data
        window: Moving average window for volume analysis
        
    Returns:
        Dict with volume MA, volume ratio, and volume spike detection
    """
    try:
        if len(volume) < window:
            return {
                "volume_ma": None,
                "volume_ratio": None,
                "volume_spike": None
            }
        
        volume_ma = volume.rolling(window=window).mean()
        current_volume = volume.iloc[-1]
        
        # Volume ratio (current volume / average volume)
        volume_ratio = current_volume / volume_ma.iloc[-1] if volume_ma.iloc[-1] > 0 else None
        
        # Volume spike detection (current volume > 2x average)
        volume_spike = current_volume > (2 * volume_ma.iloc[-1]) if volume_ma.iloc[-1] > 0 else None
        
        return {
            "volume_ma": float(volume_ma.iloc[-1]) if not pd.isna(volume_ma.iloc[-1]) else None,
            "volume_ratio": float(volume_ratio) if volume_ratio is not None else None,
            "volume_spike": bool(volume_spike) if volume_spike is not None else None
        }
    except Exception as e:
        logger.error(f"Error calculating volume analysis: {e}")
        return {
            "volume_ma": None,
            "volume_ratio": None,
            "volume_spike": None
        }


def calculate_supertrend(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series, 
    period: int = 10, 
    multiplier: float = 3.0,
    prev_trend: Optional[str] = None
) -> Dict[str, Any]:
    """
    Calculate Supertrend indicator with enhanced trend flip tracking.
    
    Args:
        high: Series of high prices
        low: Series of low prices
        close: Series of closing prices
        period: ATR period (default 10)
        multiplier: ATR multiplier (default 3.0)
        prev_trend: Previous trend state for change detection
    
    Returns:
        Dictionary with comprehensive Supertrend details:
        - 'trend': Current trend direction ('up', 'down', 'neutral')
        - 'value': Supertrend line value
        - 'direction': Numeric trend direction (1 for up, -1 for down, 0 for neutral)
        - 'flip_price': Price at which trend flipped (if applicable)
        - 'flip_timestamp': Timestamp of trend flip
        - 'upper_band': Upper Supertrend band
        - 'lower_band': Lower Supertrend band
        - 'atr': Calculated Average True Range
    """
    try:
        # Calculate True Range and ATR
        tr1 = high - low
        tr2 = np.abs(high - close.shift(1))
        tr3 = np.abs(low - close.shift(1))
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        # Basic upper and lower bands
        basic_upper = ((high + low) / 2) + (multiplier * atr)
        basic_lower = ((high + low) / 2) - (multiplier * atr)
        
        # Initialize Supertrend
        supertrend = pd.Series(index=close.index, dtype=float)
        direction = pd.Series(index=close.index, dtype=int)
        
        # First iteration
        supertrend.iloc[period-1] = basic_upper.iloc[period-1]
        direction.iloc[period-1] = 1
        
        # Tracking variables for trend flip
        flip_price = None
        flip_timestamp = None
        
        # Calculate Supertrend
        for i in range(period, len(close)):
            curr_basic_upper = basic_upper.iloc[i]
            curr_basic_lower = basic_lower.iloc[i]
            prev_close = close.iloc[i-1]
            curr_close = close.iloc[i]
            prev_supertrend = supertrend.iloc[i-1]
            
            # Adjust Supertrend line
            if curr_basic_upper < prev_supertrend or prev_close > prev_supertrend:
                supertrend.iloc[i] = curr_basic_upper
            else:
                supertrend.iloc[i] = prev_supertrend
            
            # Determine direction and track trend flip
            if curr_close > supertrend.iloc[i]:
                new_direction = 1
            else:
                new_direction = -1
            
            # Detect trend flip
            if prev_trend is not None:
                current_trend = 'up' if new_direction == 1 else 'down'
                if current_trend != prev_trend:
                    flip_price = curr_close
                    flip_timestamp = close.index[i]
            
            direction.iloc[i] = new_direction
        
        # Final result
        current_trend = 'up' if direction.iloc[-1] == 1 else 'down'
        
        return {
            'trend': current_trend,
            'value': supertrend.iloc[-1],
            'direction': int(direction.iloc[-1]),
            'flip_price': flip_price,
            'flip_timestamp': flip_timestamp,
            'upper_band': basic_upper.iloc[-1],
            'lower_band': basic_lower.iloc[-1],
            'atr': atr.iloc[-1]
        }
    
    except Exception as e:
        logger.error(f"Error calculating Supertrend: {e}")
        return {
            'trend': None,
            'value': None,
            'direction': None,
            'flip_price': None,
            'flip_timestamp': None,
            'upper_band': None,
            'lower_band': None,
            'atr': None
        }