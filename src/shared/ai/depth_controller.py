"""
AI Analysis Depth Controller

This module manages different analysis depths for the trading bot,
controlling the level of detail and time investment in AI analysis.
"""

import logging
from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class AnalysisDepth(Enum):
    """Analysis depth levels with corresponding time and detail constraints."""
    
    QUICK = "quick"      # <5 minutes, basic indicators, surface-level analysis
    STANDARD = "standard"  # 5-15 minutes, full indicators, comprehensive analysis
    DEEP = "deep"        # 15-30 minutes, all indicators, deep-dive analysis


@dataclass
class DepthConfiguration:
    """Configuration for a specific analysis depth."""
    
    depth: AnalysisDepth
    max_analysis_time: int  # Maximum time in seconds
    required_indicators: List[str]  # Minimum indicators to calculate
    optional_indicators: List[str]  # Additional indicators if time permits
    ai_context_length: int  # Maximum AI context length
    detail_level: str  # "basic", "comprehensive", "exhaustive"
    confidence_threshold: float  # Minimum confidence for recommendations


class DepthController:
    """Controls AI analysis depth and manages analysis constraints."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.depth_configs = self._initialize_depth_configs()
        self.logger.info("DepthController initialized with depth configurations")
    
    def _initialize_depth_configs(self) -> Dict[AnalysisDepth, DepthConfiguration]:
        """Initialize depth configurations with predefined settings."""
        return {
            AnalysisDepth.QUICK: DepthConfiguration(
                depth=AnalysisDepth.QUICK,
                max_analysis_time=300,  # 5 minutes
                required_indicators=[
                    "rsi", "macd", "sma_20", "ema_20", "bollinger_bands",
                    "volume", "price_change", "trend"
                ],
                optional_indicators=[
                    "stochastic", "williams_r", "cci", "atr"
                ],
                ai_context_length=1000,  # 1000 tokens
                detail_level="basic",
                confidence_threshold=0.6
            ),
            
            AnalysisDepth.STANDARD: DepthConfiguration(
                depth=AnalysisDepth.STANDARD,
                max_analysis_time=900,  # 15 minutes
                required_indicators=[
                    "rsi", "macd", "sma_20", "ema_20", "bollinger_bands",
                    "stochastic", "williams_r", "cci", "atr", "vwap",
                    "fibonacci", "support_resistance", "volume_profile",
                    "trend", "momentum", "volatility"
                ],
                optional_indicators=[
                    "ichimoku", "elliott_wave", "volume_divergence",
                    "pattern_recognition", "multi_timeframe"
                ],
                ai_context_length=2500,  # 2500 tokens
                detail_level="comprehensive",
                confidence_threshold=0.75
            ),
            
            AnalysisDepth.DEEP: DepthConfiguration(
                depth=AnalysisDepth.DEEP,
                max_analysis_time=1800,  # 30 minutes
                required_indicators=[
                    "rsi", "macd", "sma_20", "ema_20", "bollinger_bands",
                    "stochastic", "williams_r", "cci", "atr", "vwap",
                    "fibonacci", "ichimoku", "elliott_wave",
                    "support_resistance", "volume_profile", "volume_divergence",
                    "pattern_recognition", "multi_timeframe", "trend",
                    "momentum", "volatility", "risk_assessment"
                ],
                optional_indicators=[
                    "advanced_patterns", "correlation_analysis",
                    "sector_comparison", "market_breadth", "sentiment_analysis"
                ],
                ai_context_length=5000,  # 5000 tokens
                detail_level="exhaustive",
                confidence_threshold=0.85
            )
        }
    
    def get_depth_config(self, depth: AnalysisDepth) -> DepthConfiguration:
        """Get configuration for a specific analysis depth."""
        return self.depth_configs.get(depth, self.depth_configs[AnalysisDepth.STANDARD])
    
    def validate_analysis_time(self, depth: AnalysisDepth, elapsed_time: float) -> bool:
        """Check if analysis is within time constraints for the given depth."""
        config = self.get_depth_config(depth)
        return elapsed_time <= config.max_analysis_time
    
    def get_required_indicators(self, depth: AnalysisDepth) -> List[str]:
        """Get list of required indicators for the given depth."""
        config = self.get_depth_config(depth)
        return config.required_indicators.copy()
    
    def get_optional_indicators(self, depth: AnalysisDepth) -> List[str]:
        """Get list of optional indicators for the given depth."""
        config = self.get_depth_config(depth)
        return config.optional_indicators.copy()
    
    def get_ai_context_length(self, depth: AnalysisDepth) -> int:
        """Get maximum AI context length for the given depth."""
        config = self.get_depth_config(depth)
        return config.ai_context_length
    
    def get_confidence_threshold(self, depth: AnalysisDepth) -> float:
        """Get confidence threshold for the given depth."""
        config = self.get_depth_config(depth)
        return config.confidence_threshold
    
    def should_continue_analysis(self, depth: AnalysisDepth, elapsed_time: float, 
                                indicators_completed: List[str], 
                                required_indicators: List[str]) -> bool:
        """Determine if analysis should continue based on depth constraints."""
        config = self.get_depth_config(depth)
        
        # Check time constraint
        if elapsed_time >= config.max_analysis_time:
            self.logger.info(f"Analysis time limit reached for {depth.value} depth")
            return False
        
        # Check if required indicators are complete
        required_complete = all(indicator in indicators_completed for indicator in required_indicators)
        if not required_complete:
            return True
        
        # For deep analysis, continue with optional indicators if time permits
        if depth == AnalysisDepth.DEEP and elapsed_time < config.max_analysis_time * 0.8:
            return True
        
        return False
    
    def get_analysis_summary(self, depth: AnalysisDepth, elapsed_time: float,
                           indicators_completed: List[str], 
                           ai_confidence: float) -> Dict[str, Any]:
        """Generate analysis summary based on depth and results."""
        config = self.get_depth_config(depth)
        
        # Calculate completion percentage
        total_indicators = len(config.required_indicators) + len(config.optional_indicators)
        completed_count = len(indicators_completed)
        completion_percentage = (completed_count / total_indicators) * 100
        
        # Determine quality rating
        if ai_confidence >= config.confidence_threshold and completion_percentage >= 80:
            quality = "excellent"
        elif ai_confidence >= config.confidence_threshold * 0.8 and completion_percentage >= 60:
            quality = "good"
        elif ai_confidence >= config.confidence_threshold * 0.6 and completion_percentage >= 40:
            quality = "fair"
        else:
            quality = "poor"
        
        return {
            "depth": depth.value,
            "elapsed_time_seconds": elapsed_time,
            "max_time_allowed": config.max_analysis_time,
            "indicators_completed": indicators_completed,
            "completion_percentage": round(completion_percentage, 1),
            "ai_confidence": ai_confidence,
            "confidence_threshold": config.confidence_threshold,
            "quality_rating": quality,
            "detail_level": config.detail_level,
            "time_efficiency": round((elapsed_time / config.max_analysis_time) * 100, 1)
        }
    
    def recommend_depth_upgrade(self, current_depth: AnalysisDepth, 
                              user_priority: str, market_volatility: float) -> Optional[AnalysisDepth]:
        """Recommend depth upgrade based on user priority and market conditions."""
        if current_depth == AnalysisDepth.DEEP:
            return None  # Already at maximum depth
        
        # High priority users get deeper analysis
        if user_priority == "high" and current_depth == AnalysisDepth.QUICK:
            return AnalysisDepth.STANDARD
        
        if user_priority == "high" and current_depth == AnalysisDepth.STANDARD:
            return AnalysisDepth.DEEP
        
        # High volatility markets benefit from deeper analysis
        if market_volatility > 0.05 and current_depth == AnalysisDepth.QUICK:  # 5% volatility
            return AnalysisDepth.STANDARD
        
        if market_volatility > 0.10 and current_depth == AnalysisDepth.STANDARD:  # 10% volatility
            return AnalysisDepth.DEEP
        
        return None
    
    def get_depth_description(self, depth: AnalysisDepth) -> str:
        """Get human-readable description of analysis depth."""
        descriptions = {
            AnalysisDepth.QUICK: "Quick analysis (<5 min) - Basic indicators and surface-level insights for fast decision making",
            AnalysisDepth.STANDARD: "Standard analysis (5-15 min) - Comprehensive indicators and detailed analysis for informed decisions",
            AnalysisDepth.DEEP: "Deep analysis (15-30 min) - Exhaustive analysis with advanced indicators and deep market insights"
        }
        return descriptions.get(depth, "Unknown analysis depth")
    
    def estimate_completion_time(self, depth: AnalysisDepth, 
                               market_complexity: float = 1.0) -> timedelta:
        """Estimate completion time for analysis based on depth and market complexity."""
        base_times = {
            AnalysisDepth.QUICK: 180,      # 3 minutes base
            AnalysisDepth.STANDARD: 600,   # 10 minutes base
            AnalysisDepth.DEEP: 1200       # 20 minutes base
        }
        
        base_time = base_times.get(depth, 600)
        estimated_seconds = base_time * market_complexity
        
        return timedelta(seconds=int(estimated_seconds)) 