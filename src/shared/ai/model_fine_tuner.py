"""
AI Model Fine-tuning Module

This module handles fine-tuning AI models for financial analysis,
including context optimization, prompt engineering, and domain-specific training.
"""

import logging
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)


@dataclass
class PromptTemplate:
    """Template for AI prompts with placeholders and metadata."""
    
    name: str
    template: str
    placeholders: List[str]
    context_length: int
    expected_response_format: str
    confidence_threshold: float
    tags: List[str]


@dataclass
class FinancialContext:
    """Financial analysis context for AI models."""
    
    market_conditions: str
    sector_trends: str
    economic_indicators: str
    risk_sentiment: str
    technical_context: str
    fundamental_context: str
    timestamp: datetime


class ModelFineTuner:
    """Handles AI model fine-tuning and financial domain optimization."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.prompt_templates = self._initialize_prompt_templates()
        self.financial_contexts = self._initialize_financial_contexts()
        self.model_configs = self._initialize_model_configs()
        self.logger.info("ModelFineTuner initialized with financial domain templates")
    
    def _initialize_prompt_templates(self) -> Dict[str, PromptTemplate]:
        """Initialize financial analysis prompt templates."""
        return {
            "technical_analysis": PromptTemplate(
                name="technical_analysis",
                template="""Analyze the technical indicators for {symbol}:

Current Price: ${current_price}
RSI: {rsi}
MACD: {macd}
Moving Averages: {moving_averages}
Bollinger Bands: {bollinger_bands}
Volume: {volume_analysis}
Support/Resistance: {support_resistance}

Market Context: {market_context}
Timeframe: {timeframe}

Provide a comprehensive technical analysis including:
1. Trend direction and strength
2. Key support and resistance levels
3. Volume analysis and unusual activity
4. Risk assessment and confidence level
5. Actionable recommendations

Format your response as JSON with keys: trend, support_resistance, volume_analysis, risk_assessment, recommendations, confidence_score""",
                placeholders=["symbol", "current_price", "rsi", "macd", "moving_averages", 
                            "bollinger_bands", "volume_analysis", "support_resistance", 
                            "market_context", "timeframe"],
                context_length=2000,
                expected_response_format="json",
                confidence_threshold=0.75,
                tags=["technical", "analysis", "indicators"]
            ),
            
            "pattern_recognition": PromptTemplate(
                name="pattern_recognition",
                template="""Identify chart patterns for {symbol} based on price action:

Price Data: {price_data}
Volume Data: {volume_data}
Timeframe: {timeframe}

Look for the following patterns:
- Head and Shoulders (regular and inverse)
- Double/Triple tops and bottoms
- Triangles (ascending, descending, symmetrical)
- Flags and pennants
- Gaps and breakouts
- Fibonacci retracements

For each pattern found, provide:
1. Pattern type and confidence level
2. Entry and exit points
3. Price targets
4. Risk/reward ratio
5. Timeframe for completion

Format as JSON with keys: patterns, entry_points, price_targets, risk_reward, timeframe, confidence""",
                placeholders=["symbol", "price_data", "volume_data", "timeframe"],
                context_length=3000,
                expected_response_format="json",
                confidence_threshold=0.8,
                tags=["patterns", "chart_analysis", "fibonacci"]
            ),
            
            "risk_assessment": PromptTemplate(
                name="risk_assessment",
                template="""Assess the risk profile for {symbol}:

Technical Indicators: {technical_indicators}
Market Conditions: {market_conditions}
Volatility: {volatility_metrics}
Volume Profile: {volume_profile}
Sector Performance: {sector_performance}

Evaluate the following risk factors:
1. Technical risk (breakdown levels, trend weakness)
2. Market risk (sector rotation, market sentiment)
3. Volatility risk (price swings, ATR analysis)
4. Liquidity risk (volume analysis, bid-ask spreads)
5. Event risk (earnings, news, economic data)

Provide risk assessment with:
- Overall risk score (1-10)
- Key risk factors and their impact
- Risk mitigation strategies
- Position sizing recommendations
- Stop loss levels

Format as JSON with keys: risk_score, risk_factors, mitigation_strategies, position_sizing, stop_loss, confidence""",
                placeholders=["symbol", "technical_indicators", "market_conditions", 
                            "volatility_metrics", "volume_profile", "sector_performance"],
                context_length=2500,
                expected_response_format="json",
                confidence_threshold=0.8,
                tags=["risk", "assessment", "management"]
            ),
            
            "sentiment_analysis": PromptTemplate(
                name="sentiment_analysis",
                template="""Analyze market sentiment for {symbol}:

News Sentiment: {news_sentiment}
Social Media: {social_sentiment}
Options Flow: {options_flow}
Institutional Activity: {institutional_data}
Market Breadth: {market_breadth}

Evaluate sentiment across multiple dimensions:
1. News sentiment (positive/negative/neutral)
2. Social media sentiment (Reddit, Twitter, forums)
3. Options sentiment (put/call ratios, flow)
4. Institutional sentiment (13F filings, insider trading)
5. Technical sentiment (momentum, volume)

Provide sentiment analysis with:
- Overall sentiment score (-100 to +100)
- Sentiment breakdown by category
- Sentiment trends and changes
- Contrarian indicators
- Sentiment-based trading signals

Format as JSON with keys: overall_sentiment, category_breakdown, trends, contrarian_signals, trading_signals, confidence""",
                placeholders=["symbol", "news_sentiment", "social_sentiment", 
                            "options_flow", "institutional_data", "market_breadth"],
                context_length=3000,
                expected_response_format="json",
                confidence_threshold=0.7,
                tags=["sentiment", "news", "social_media", "options"]
            )
        }
    
    def _initialize_financial_contexts(self) -> Dict[str, FinancialContext]:
        """Initialize financial market contexts for AI analysis."""
        return {
            "bull_market": FinancialContext(
                market_conditions="Strong uptrend with increasing volume and broad participation",
                sector_trends="Technology and growth sectors leading, defensive sectors lagging",
                economic_indicators="Low unemployment, strong GDP growth, accommodative monetary policy",
                risk_sentiment="Low risk aversion, high risk tolerance, momentum-driven trading",
                technical_context="Higher highs and higher lows, strong momentum indicators",
                fundamental_context="Strong earnings growth, positive forward guidance, low interest rates",
                timestamp=datetime.now()
            ),
            
            "bear_market": FinancialContext(
                market_conditions="Strong downtrend with increasing volume and broad selling",
                sector_trends="Defensive sectors outperforming, cyclical sectors underperforming",
                economic_indicators="Rising unemployment, weak GDP growth, restrictive monetary policy",
                risk_sentiment="High risk aversion, low risk tolerance, defensive positioning",
                technical_context="Lower highs and lower lows, weak momentum indicators",
                fundamental_context="Weak earnings growth, negative forward guidance, high interest rates",
                timestamp=datetime.now()
            ),
            
            "sideways_market": FinancialContext(
                market_conditions="Range-bound trading with low volatility and mixed signals",
                sector_trends="Sector rotation, no clear leadership, defensive positioning",
                economic_indicators="Mixed economic data, uncertain monetary policy direction",
                risk_sentiment="Moderate risk aversion, wait-and-see approach, selective positioning",
                technical_context="Sideways channels, mixed momentum indicators, low volatility",
                fundamental_context="Mixed earnings results, cautious forward guidance, stable rates",
                timestamp=datetime.now()
            ),
            
            "volatile_market": FinancialContext(
                market_conditions="High volatility with sharp price swings and increased uncertainty",
                sector_trends="Rapid sector rotation, momentum-driven moves, increased correlation",
                economic_indicators="Economic uncertainty, policy changes, geopolitical events",
                risk_sentiment="High uncertainty, increased hedging, defensive strategies",
                technical_context="Wide price ranges, high ATR, volatile momentum indicators",
                fundamental_context="Earnings uncertainty, policy changes, event-driven moves",
                timestamp=datetime.now()
            )
        }
    
    def _initialize_model_configs(self) -> Dict[str, Dict[str, Any]]:
        """Initialize AI model configurations for different analysis types."""
        return {
            "gpt4o_mini": {
                "max_tokens": 4000,
                "temperature": 0.3,
                "top_p": 0.9,
                "frequency_penalty": 0.1,
                "presence_penalty": 0.1,
                "context_window": 128000
            },
            "gpt4o": {
                "max_tokens": 8000,
                "temperature": 0.2,
                "top_p": 0.9,
                "frequency_penalty": 0.1,
                "presence_penalty": 0.1,
                "context_window": 128000
            },
            "claude_3_5_sonnet": {
                "max_tokens": 4000,
                "temperature": 0.3,
                "top_p": 0.9,
                "context_window": 200000
            },
            "mixtral_8x7b": {
                "max_tokens": 2000,
                "temperature": 0.4,
                "top_p": 0.9,
                "context_window": 32000
            }
        }
    
    def get_prompt_template(self, template_name: str) -> Optional[PromptTemplate]:
        """Get a specific prompt template by name."""
        return self.prompt_templates.get(template_name)
    
    def customize_prompt(self, template_name: str, **kwargs) -> str:
        """Customize a prompt template with specific values."""
        template = self.get_prompt_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
        
        # Validate all required placeholders are provided
        missing_placeholders = [p for p in template.placeholders if p not in kwargs]
        if missing_placeholders:
            raise ValueError(f"Missing required placeholders: {missing_placeholders}")
        
        # Replace placeholders in template
        customized_prompt = template.template
        for placeholder, value in kwargs.items():
            if placeholder in template.placeholders:
                customized_prompt = customized_prompt.replace(f"{{{placeholder}}}", str(value))
        
        return customized_prompt
    
    def get_financial_context(self, market_type: str) -> Optional[FinancialContext]:
        """Get financial market context for analysis."""
        return self.financial_contexts.get(market_type)
    
    def build_analysis_context(self, symbol: str, market_data: Dict[str, Any], 
                             market_type: str = "sideways_market") -> str:
        """Build comprehensive analysis context for AI models."""
        context = self.get_financial_context(market_type)
        if not context:
            context = self.financial_contexts["sideways_market"]
        
        # Build market context string
        market_context = f"""
Market Context for {symbol}:
- Overall Market: {context.market_conditions}
- Sector Trends: {context.sector_trends}
- Economic Indicators: {context.economic_indicators}
- Risk Sentiment: {context.risk_sentiment}
- Technical Context: {context.technical_context}
- Fundamental Context: {context.fundamental_context}

Current Market Data:
- Price: ${market_data.get('current_price', 'N/A')}
- Volume: {market_data.get('volume', 'N/A'):,}
- Change: {market_data.get('price_change', 'N/A')}
- Volatility: {market_data.get('volatility', 'N/A')}
        """.strip()
        
        return market_context
    
    def optimize_model_config(self, model_name: str, analysis_type: str, 
                            complexity: str = "standard") -> Dict[str, Any]:
        """Optimize model configuration for specific analysis type and complexity."""
        base_config = self.model_configs.get(model_name, {})
        if not base_config:
            return {}
        
        # Create optimized config based on analysis type and complexity
        optimized_config = base_config.copy()
        
        if complexity == "quick":
            optimized_config["max_tokens"] = min(optimized_config.get("max_tokens", 4000), 2000)
            optimized_config["temperature"] = min(optimized_config.get("temperature", 0.3) + 0.1, 0.5)
        elif complexity == "deep":
            optimized_config["max_tokens"] = optimized_config.get("max_tokens", 4000)
            optimized_config["temperature"] = max(optimized_config.get("temperature", 0.3) - 0.1, 0.1)
        
        # Adjust for specific analysis types
        if analysis_type == "risk_assessment":
            optimized_config["temperature"] = max(optimized_config.get("temperature", 0.3) - 0.1, 0.1)
        elif analysis_type == "sentiment_analysis":
            optimized_config["temperature"] = min(optimized_config.get("temperature", 0.3) + 0.1, 0.5)
        
        return optimized_config
    
    def validate_response_format(self, response: str, expected_format: str) -> bool:
        """Validate AI response format."""
        if expected_format == "json":
            try:
                json.loads(response)
                return True
            except json.JSONDecodeError:
                return False
        elif expected_format == "text":
            return len(response.strip()) > 0
        else:
            return True
    
    def extract_confidence_score(self, response: Dict[str, Any]) -> float:
        """Extract confidence score from AI response."""
        confidence_fields = ["confidence_score", "confidence", "confidence_level", "certainty"]
        
        for field in confidence_fields:
            if field in response:
                value = response[field]
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, str):
                    # Try to extract numeric value from string
                    try:
                        return float(value.replace("%", "").replace("percent", ""))
                    except ValueError:
                        continue
        
        # Default confidence based on response quality
        if len(response) > 5:
            return 0.7  # Moderate confidence for detailed responses
        else:
            return 0.5  # Low confidence for brief responses
    
    def get_analysis_quality_score(self, response: Dict[str, Any], 
                                 template: PromptTemplate) -> float:
        """Calculate quality score for AI analysis response."""
        quality_score = 0.0
        
        # Check response completeness
        if len(response) >= 3:
            quality_score += 0.3
        
        # Check for required fields based on template
        if "confidence_score" in response:
            quality_score += 0.2
        
        # Check response length (proxy for detail)
        response_text = str(response)
        if len(response_text) > 500:
            quality_score += 0.2
        elif len(response_text) > 200:
            quality_score += 0.1
        
        # Check for actionable content
        action_words = ["buy", "sell", "hold", "entry", "exit", "stop", "target"]
        if any(word in response_text.lower() for word in action_words):
            quality_score += 0.2
        
        # Check confidence threshold
        confidence = self.extract_confidence_score(response)
        if confidence >= template.confidence_threshold:
            quality_score += 0.1
        
        return min(quality_score, 1.0)
    
    async def fine_tune_prompt(self, template_name: str, user_feedback: Dict[str, Any]) -> PromptTemplate:
        """Fine-tune prompt template based on user feedback."""
        template = self.get_prompt_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
        
        # Update template based on feedback
        if "quality_score" in user_feedback:
            # Adjust confidence threshold based on quality
            quality = user_feedback["quality_score"]
            if quality < 0.5:
                template.confidence_threshold = max(0.5, template.confidence_threshold - 0.1)
            elif quality > 0.8:
                template.confidence_threshold = min(0.95, template.confidence_threshold + 0.05)
        
        if "response_format" in user_feedback:
            template.expected_response_format = user_feedback["response_format"]
        
        if "context_length" in user_feedback:
            template.context_length = user_feedback["context_length"]
        
        # Log fine-tuning changes
        self.logger.info(f"Fine-tuned template '{template_name}' based on user feedback")
        
        return template
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """Get list of available prompt templates with metadata."""
        return [
            {
                "name": template.name,
                "tags": template.tags,
                "context_length": template.context_length,
                "expected_format": template.expected_response_format,
                "confidence_threshold": template.confidence_threshold
            }
            for template in self.prompt_templates.values()
        ] 