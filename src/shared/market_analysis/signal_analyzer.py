"""
Core market signal analyzer with intelligent pattern detection.

This module provides the main analysis engine that detects market signals
using technical indicators and applies confidence scoring.

This is a backward compatibility module that imports from the canonical location.
"""

import warnings
import logging
import inspect
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import pandas as pd

# Import from canonical location
from .unified_signal_analyzer import (
    UnifiedSignalAnalyzer,
    unified_signal_analyzer,
    analyze_market_data
)

# Import for backward compatibility
from .signals import SignalType, SignalDirection, MarketSignal, SIGNAL_DESCRIPTIONS
from .confidence_scorer import ConfidenceScorer

# Import deprecation monitoring
from src.core.deprecation_monitor import record_deprecation

# Get caller information
caller_frame = inspect.currentframe()
if caller_frame and caller_frame.f_back:
    caller_info = f"{caller_frame.f_back.f_code.co_filename}:{caller_frame.f_back.f_lineno}"
else:
    caller_info = "unknown"

# Record deprecation usage
record_deprecation("src.shared.market_analysis.signal_analyzer", caller_info)

# Show deprecation warning
warnings.warn(
    "This module is deprecated. Import from src.shared.market_analysis.unified_signal_analyzer instead.",
    DeprecationWarning,
    stacklevel=2
)

logger = logging.getLogger(__name__)


class SignalAnalyzer:
    """
    Main signal analysis engine that detects market patterns and generates signals
    with confidence scoring.
    
    This is a backward compatibility wrapper around UnifiedSignalAnalyzer.
    """
    
    def __init__(self):
        """Initialize the signal analyzer with the unified implementation."""
        self._analyzer = unified_signal_analyzer
        self.confidence_scorer = self._analyzer.confidence_scorer
        self.supported_timeframes = self._analyzer.supported_timeframes
    
    def analyze_market_data(self, symbol: str, timeframe: str, 
                          market_data: pd.DataFrame) -> List[MarketSignal]:
        """
        Analyze market data and generate potential signals with confidence scores.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'BTCUSD')
            timeframe: Timeframe for analysis (e.g., '1h', '1d')
            market_data: DataFrame with OHLCV data and timestamps
            
        Returns:
            List of MarketSignal objects with confidence scores
        """
        return self._analyzer.analyze_market_data(symbol, timeframe, market_data)
    
    def get_signal_summary(self, signals: List[MarketSignal]) -> Dict[str, Any]:
        """
        Generate a summary of signals for easy consumption.
        
        Args:
            signals: List of MarketSignal objects
            
        Returns:
            Dictionary with signal summary including highest confidence signals
        """
        return self._analyzer.get_signal_summary(signals)

# For backward compatibility, expose the methods from the unified implementation
analyze_market_data_async = analyze_market_data