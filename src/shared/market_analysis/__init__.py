"""
Market Analysis Module

This module provides intelligent market setup analysis with confidence scoring
for various signal types including reversals, bounces, and momentum patterns.
"""

from .signals import SignalType, SignalDirection, MarketSignal, SIGNAL_DESCRIPTIONS, DEFAULT_CONFIDENCE_THRESHOLDS
from .confidence_scorer import ConfidenceScorer, ConfidenceFactor
from .signal_analyzer import SignalAnalyzer

__version__ = "0.1.0"

__all__ = [
    'SignalType',
    'SignalDirection',
    'MarketSignal',
    'SIGNAL_DESCRIPTIONS',
    'DEFAULT_CONFIDENCE_THRESHOLDS',
    'ConfidenceScorer',
    'ConfidenceFactor',
    'SignalAnalyzer'
]