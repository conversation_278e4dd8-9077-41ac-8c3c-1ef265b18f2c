"""
Unified Signal Analyzer

This module provides a unified implementation of market signal analysis
that can be used by both the AI chat processor and other components.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from src.core.logger import get_logger
from .signals import SignalType, SignalDirection, MarketSignal, SIGNAL_DESCRIPTIONS
from .confidence_scorer import ConfidenceScorer

logger = get_logger(__name__)


class UnifiedSignalAnalyzer:
    """
    Unified signal analysis engine that detects market patterns and generates signals
    with confidence scoring.
    
    This class provides a unified implementation that can be used by both the
    AI chat processor and other components.
    """
    
    def __init__(self):
        """Initialize the signal analyzer with confidence scorer."""
        self.confidence_scorer = ConfidenceScorer()
        self.supported_timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w']
    
    def analyze_market_data(self, symbol: str, timeframe: str, 
                          market_data: Union[pd.DataFrame, Dict[str, Any]]) -> List[MarketSignal]:
        """
        Analyze market data and generate potential signals with confidence scores.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'BTCUSD')
            timeframe: Timeframe for analysis (e.g., '1h', '1d')
            market_data: DataFrame with OHLCV data or dictionary with market data
            
        Returns:
            List of MarketSignal objects with confidence scores
        """
        # Normalize market data to DataFrame if it's a dictionary
        if isinstance(market_data, dict):
            df = self._normalize_provider_data_to_df(market_data)
        else:
            df = market_data
            
        if timeframe not in self.supported_timeframes:
            logger.warning(f"Unsupported timeframe: {timeframe}. Using default analysis.")
        
        if df.empty:
            logger.warning(f"No market data provided for {symbol}")
            return []
        
        # Calculate technical indicators
        indicators = self._calculate_indicators(df)
        
        # Generate potential signals
        signals = self._generate_signals(symbol, timeframe, indicators, df)
        
        # Filter signals by minimum confidence
        min_confidence = self.confidence_scorer.thresholds['min_confidence_for_signal']
        filtered_signals = [
            signal for signal in signals 
            if signal.confidence_score >= min_confidence
        ]
        
        logger.info(f"Generated {len(filtered_signals)} signals for {symbol} "
                   f"on {timeframe} timeframe")
        
        return filtered_signals
    
    def _normalize_provider_data_to_df(self, data: Dict[str, Any]) -> pd.DataFrame:
        """
        Normalize provider data to a pandas DataFrame with standardized columns.
        
        This method handles various data formats from different providers and
        normalizes them to a standard DataFrame format with OHLCV columns.
        
        Args:
            data: Dictionary containing market data from a provider
            
        Returns:
            Normalized pandas DataFrame with OHLCV data
        """
        # Handle different data formats
        if isinstance(data, dict):
            if 'data' in data and isinstance(data['data'], (list, tuple)):
                df = pd.DataFrame(data['data'])
            elif 'Time Series (Daily)' in data:
                raw = data['Time Series (Daily)']
                df = pd.DataFrame.from_dict(raw, orient='index').reset_index().rename(columns={'index': 'date'})
            elif 'results' in data:
                df = pd.DataFrame(data['results'])
            else:
                sample_vals = [v for v in data.values() if isinstance(v, (list, tuple))]
                if sample_vals:
                    df = pd.DataFrame(data)
                else:
                    df = pd.DataFrame(data.get('data', []))
        elif isinstance(data, list):
            df = pd.DataFrame(data)
        else:
            df = pd.DataFrame(data)

        if df.empty:
            logger.warning('No data available to normalize')
            return pd.DataFrame()

        # Date handling
        if 'timestamp' in df.columns:
            df['date'] = pd.to_datetime(df['timestamp'])
        elif 'Date' in df.columns:
            df['date'] = pd.to_datetime(df['Date'])
        elif 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        if 'date' in df.columns:
            df.set_index('date', inplace=True)

        # Column mapping
        column_mapping = {
            'open': ['open', 'Open', '1. open', 'o'],
            'high': ['high', 'High', '2. high', 'h'],
            'low': ['low', 'Low', '3. low', 'l'],
            'close': ['close', 'Close', '4. close', 'c', 'adjclose', 'Adj Close'],
            'volume': ['volume', 'Volume', '5. volume', 'v', 'adjvolume']
        }
        for std_name, alt_names in column_mapping.items():
            for alt in alt_names:
                if alt in df.columns:
                    df[std_name] = pd.to_numeric(df[alt], errors='coerce')
                    break

        df = df.apply(pd.to_numeric, errors='coerce').dropna()
        df = df[~df.index.duplicated(keep='first')]
        if not df.index.is_monotonic_increasing:
            df = df.sort_index()

        return df
    
    def _calculate_indicators(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate all required technical indicators from market data."""
        try:
            close_prices = market_data['close']
            high_prices = market_data['high']
            low_prices = market_data['low']
            volume_data = market_data['volume']
            
            # Import technical analysis functions
            from src.shared.technical_analysis import (
                calculate_rsi,
                calculate_macd,
                calculate_bollinger_bands,
                calculate_volume_analysis,
                calculate_sma,
                calculate_ema
            )
            
            indicators = {
                'rsi': calculate_rsi(close_prices),
                'macd': calculate_macd(close_prices),
                'bollinger_bands': calculate_bollinger_bands(close_prices),
                'volume_analysis': calculate_volume_analysis(volume_data),
                'sma_20': calculate_sma(close_prices, 20),
                'sma_50': calculate_sma(close_prices, 50),
                'ema_20': calculate_ema(close_prices, 20),
                'close_price': float(close_prices.iloc[-1]) if not close_prices.empty else None,
                'volume': float(volume_data.iloc[-1]) if not volume_data.empty else None
            }
            
            return indicators
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return {}
    
    def _generate_signals(self, symbol: str, timeframe: str, 
                         indicators: Dict[str, Any],
                         market_data: pd.DataFrame) -> List[MarketSignal]:
        """
        Generate all potential market signals based on technical indicators.
        """
        signals = []
        
        # Check for reversal signals
        signals.extend(self._check_reversal_signals(symbol, timeframe, indicators, market_data))
        
        # Check for bounce signals
        signals.extend(self._check_bounce_signals(symbol, timeframe, indicators, market_data))
        
        # Check for momentum continuation signals
        signals.extend(self._check_momentum_signals(symbol, timeframe, indicators, market_data))
        
        return signals
    
    def _check_reversal_signals(self, symbol: str, timeframe: str,
                               indicators: Dict[str, Any],
                               market_data: pd.DataFrame) -> List[MarketSignal]:
        """Check for bullish and bearish reversal patterns."""
        signals = []
        current_price = indicators.get('close_price')
        
        if current_price is None:
            return signals
        
        # Bullish reversal checks
        bullish_reversal = self._detect_bullish_reversal(indicators, market_data)
        if bullish_reversal:
            confidence = self.confidence_scorer.calculate_confidence(
                indicators, SignalType.BULLISH_REVERSAL, SignalDirection.BULLISH
            )
            signals.append(self._create_signal(
                symbol, timeframe, SignalType.BULLISH_REVERSAL,
                SignalDirection.BULLISH, confidence, indicators
            ))
        
        # Bearish reversal checks
        bearish_reversal = self._detect_bearish_reversal(indicators, market_data)
        if bearish_reversal:
            confidence = self.confidence_scorer.calculate_confidence(
                indicators, SignalType.BEARISH_REVERSAL, SignalDirection.BEARISH
            )
            signals.append(self._create_signal(
                symbol, timeframe, SignalType.BEARISH_REVERSAL,
                SignalDirection.BEARISH, confidence, indicators
            ))
        
        return signals
    
    def _check_bounce_signals(self, symbol: str, timeframe: str,
                             indicators: Dict[str, Any],
                             market_data: pd.DataFrame) -> List[MarketSignal]:
        """Check for oversold/overbought bounce signals."""
        signals = []
        rsi = indicators.get('rsi')
        
        if rsi is None:
            return signals
        
        # Oversold bounce (bullish)
        if rsi < self.confidence_scorer.thresholds['oversold_rsi']:
            confidence = self.confidence_scorer.calculate_confidence(
                indicators, SignalType.OVERSOLD_BOUNCE, SignalDirection.BULLISH
            )
            signals.append(self._create_signal(
                symbol, timeframe, SignalType.OVERSOLD_BOUNCE,
                SignalDirection.BULLISH, confidence, indicators
            ))
        
        # Overbought bounce (bearish)
        if rsi > self.confidence_scorer.thresholds['overbought_rsi']:
            confidence = self.confidence_scorer.calculate_confidence(
                indicators, SignalType.OVERBOUGHT_BOUNCE, SignalDirection.BEARISH
            )
            signals.append(self._create_signal(
                symbol, timeframe, SignalType.OVERBOUGHT_BOUNCE,
                SignalDirection.BEARISH, confidence, indicators
            ))
        
        return signals
    
    def _check_momentum_signals(self, symbol: str, timeframe: str,
                               indicators: Dict[str, Any],
                               market_data: pd.DataFrame) -> List[MarketSignal]:
        """Check for momentum continuation signals."""
        signals = []
        
        # Check MACD for momentum continuation
        macd_data = indicators.get('macd', {})
        if (macd_data.get('macd') is not None and 
            macd_data.get('signal') is not None and
            macd_data.get('histogram') is not None):
            
            macd_line = macd_data['macd']
            signal_line = macd_data['signal']
            histogram = macd_data['histogram']
            
            # Bullish momentum (MACD above signal, positive histogram)
            if macd_line > signal_line and histogram > 0:
                confidence = self.confidence_scorer.calculate_confidence(
                    indicators, SignalType.MOMENTUM_CONTINUATION, SignalDirection.BULLISH
                )
                signals.append(self._create_signal(
                    symbol, timeframe, SignalType.MOMENTUM_CONTINUATION,
                    SignalDirection.BULLISH, confidence, indicators
                ))
            
            # Bearish momentum (MACD below signal, negative histogram)
            elif macd_line < signal_line and histogram < 0:
                confidence = self.confidence_scorer.calculate_confidence(
                    indicators, SignalType.MOMENTUM_CONTINUATION, SignalDirection.BEARISH
                )
                signals.append(self._create_signal(
                    symbol, timeframe, SignalType.MOMENTUM_CONTINUATION,
                    SignalDirection.BEARISH, confidence, indicators
                ))
        
        return signals
    
    def _detect_bullish_reversal(self, indicators: Dict[str, Any],
                                market_data: pd.DataFrame) -> bool:
        """Detect potential bullish reversal patterns."""
        # Check for RSI divergence (price makes lower low, RSI makes higher low)
        rsi = indicators.get('rsi')
        bb_data = indicators.get('bollinger_bands', {})
        
        if (rsi is not None and rsi < 35 and
            bb_data.get('lower') is not None and
            indicators.get('close_price') is not None and
            indicators['close_price'] <= bb_data['lower'] * 1.02):
            return True
        
        return False
    
    def _detect_bearish_reversal(self, indicators: Dict[str, Any],
                                market_data: pd.DataFrame) -> bool:
        """Detect potential bearish reversal patterns."""
        # Check for RSI divergence (price makes higher high, RSI makes lower high)
        rsi = indicators.get('rsi')
        bb_data = indicators.get('bollinger_bands', {})
        
        if (rsi is not None and rsi > 65 and
            bb_data.get('upper') is not None and
            indicators.get('close_price') is not None and
            indicators['close_price'] >= bb_data['upper'] * 0.98):
            return True
        
        return False
    
    def _create_signal(self, symbol: str, timeframe: str, signal_type: SignalType,
                      direction: SignalDirection, confidence: float,
                      indicators: Dict[str, Any]) -> MarketSignal:
        """Create a MarketSignal object with proper formatting."""
        description = SIGNAL_DESCRIPTIONS.get(signal_type, "Market signal detected")
        
        return MarketSignal(
            signal_type=signal_type,
            direction=direction,
            confidence_score=confidence,
            symbol=symbol,
            timeframe=timeframe,
            timestamp=datetime.now(),
            indicators_used={k: v for k, v in indicators.items() if v is not None},
            price_level=indicators.get('close_price'),
            volume=indicators.get('volume'),
            description=description
        )
    
    def get_signal_summary(self, signals: List[MarketSignal]) -> Dict[str, Any]:
        """
        Generate a summary of signals for easy consumption.
        
        Args:
            signals: List of MarketSignal objects
            
        Returns:
            Dictionary with signal summary including highest confidence signals
        """
        if not signals:
            return {"total_signals": 0, "signals": []}
        
        # Sort by confidence (highest first)
        sorted_signals = sorted(signals, key=lambda x: x.confidence_score, reverse=True)
        
        summary = {
            "total_signals": len(signals),
            "highest_confidence": sorted_signals[0].confidence_score if signals else 0,
            "signals": [signal.to_dict() for signal in sorted_signals]
        }
        
        return summary


# Create a singleton instance for easy access
unified_signal_analyzer = UnifiedSignalAnalyzer()


# Helper function to normalize data and analyze signals
async def analyze_market_data(symbol: str, timeframe: str, market_data: Any) -> List[Dict[str, Any]]:
    """
    Analyze market data and generate signals.
    
    Args:
        symbol: Trading symbol (e.g., 'AAPL', 'BTCUSD')
        timeframe: Timeframe for analysis (e.g., '1h', '1d')
        market_data: Market data in any supported format
        
    Returns:
        List of signal dictionaries
    """
    signals = unified_signal_analyzer.analyze_market_data(symbol, timeframe, market_data)
    return [signal.to_dict() for signal in signals]
