"""
Confidence scoring system for market signals.

This module provides a configurable confidence scoring system that evaluates
the strength and reliability of market signals based on technical indicators
and market conditions.
"""

import os
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from .signals import SignalType, SignalDirection

logger = logging.getLogger(__name__)


class ConfidenceFactor(Enum):
    """Enumeration of factors that contribute to confidence scoring."""
    RSI_STRENGTH = "rsi_strength"
    MACD_CONVERGENCE = "macd_convergence" 
    BOLLINGER_POSITION = "bollinger_position"
    VOLUME_CONFIRMATION = "volume_confirmation"
    TREND_ALIGNMENT = "trend_alignment"
    SUPPORT_RESISTANCE = "support_resistance"
    VOLATILITY = "volatility"
    TIME_FRAME_CONFIRMATION = "time_frame_confirmation"


@dataclass
class ConfidenceWeight:
    """Data class representing weight configuration for confidence factors."""
    factor: ConfidenceFactor
    weight: float  # 0.0 to 1.0
    description: str


class ConfidenceScorer:
    """
    Configurable confidence scoring system for market signals.
    
    Calculates confidence scores (0-100%) based on technical indicators
    and market conditions using weighted factors.
    """
    
    def __init__(self):
        """Initialize confidence scorer with default weights and thresholds."""
        self.weights = self._get_default_weights()
        self.thresholds = self._get_default_thresholds()
        
        # Load configuration from environment variables
        self._load_config_from_env()
    
    def _get_default_weights(self) -> Dict[ConfidenceFactor, float]:
        """Get default weights for confidence factors."""
        return {
            ConfidenceFactor.RSI_STRENGTH: 0.20,
            ConfidenceFactor.MACD_CONVERGENCE: 0.18,
            ConfidenceFactor.BOLLINGER_POSITION: 0.15,
            ConfidenceFactor.VOLUME_CONFIRMATION: 0.12,
            ConfidenceFactor.TREND_ALIGNMENT: 0.15,
            ConfidenceFactor.SUPPORT_RESISTANCE: 0.10,
            ConfidenceFactor.VOLATILITY: 0.05,
            ConfidenceFactor.TIME_FRAME_CONFIRMATION: 0.05
        }
    
    def _get_default_thresholds(self) -> Dict[str, float]:
        """Get default confidence thresholds."""
        return {
            'oversold_rsi': 30.0,
            'overbought_rsi': 70.0,
            'strong_trend_threshold': 0.7,
            'volume_spike_multiplier': 2.0,
            'bollinger_extreme': 2.5,  # Standard deviations from mean
            'min_confidence_for_signal': 25.0
        }
    
    def _load_config_from_env(self):
        """Load configuration from environment variables."""
        # RSI thresholds
        self.thresholds['oversold_rsi'] = float(os.getenv(
            'MARKET_ANALYSIS_OVERSOLD_RSI', 
            self.thresholds['oversold_rsi']
        ))
        self.thresholds['overbought_rsi'] = float(os.getenv(
            'MARKET_ANALYSIS_OVERBOUGHT_RSI',
            self.thresholds['overbought_rsi']
        ))
        
        # Volume spike threshold
        self.thresholds['volume_spike_multiplier'] = float(os.getenv(
            'MARKET_ANALYSIS_VOLUME_SPIKE_MULTIPLIER',
            self.thresholds['volume_spike_multiplier']
        ))
        
        # Minimum confidence threshold
        self.thresholds['min_confidence_for_signal'] = float(os.getenv(
            'MARKET_ANALYSIS_MIN_CONFIDENCE',
            self.thresholds['min_confidence_for_signal']
        ))
    
    def calculate_confidence(self, indicators: Dict[str, Any], 
                           signal_type: SignalType, 
                           direction: SignalDirection) -> float:
        """
        Calculate confidence score for a market signal.
        
        Args:
            indicators: Dictionary of technical indicator values
            signal_type: Type of signal being evaluated
            direction: Direction of the signal (bullish/bearish)
            
        Returns:
            float: Confidence score between 0-100%
        """
        total_score = 0.0
        total_weight = 0.0
        
        # Calculate individual factor scores
        factor_scores = {}
        
        # RSI Strength
        rsi_score = self._calculate_rsi_strength(indicators.get('rsi'), signal_type, direction)
        factor_scores[ConfidenceFactor.RSI_STRENGTH] = rsi_score
        total_score += rsi_score * self.weights[ConfidenceFactor.RSI_STRENGTH]
        total_weight += self.weights[ConfidenceFactor.RSI_STRENGTH]
        
        # MACD Convergence
        macd_score = self._calculate_macd_convergence(indicators.get('macd'), direction)
        factor_scores[ConfidenceFactor.MACD_CONVERGENCE] = macd_score
        total_score += macd_score * self.weights[ConfidenceFactor.MACD_CONVERGENCE]
        total_weight += self.weights[ConfidenceFactor.MACD_CONVERGENCE]
        
        # Bollinger Position
        bb_score = self._calculate_bollinger_position(indicators.get('bollinger_bands'), 
                                                    indicators.get('close_price'), direction)
        factor_scores[ConfidenceFactor.BOLLINGER_POSITION] = bb_score
        total_score += bb_score * self.weights[ConfidenceFactor.BOLLINGER_POSITION]
        total_weight += self.weights[ConfidenceFactor.BOLLINGER_POSITION]
        
        # Volume Confirmation
        volume_score = self._calculate_volume_confirmation(indicators.get('volume_analysis'))
        factor_scores[ConfidenceFactor.VOLUME_CONFIRMATION] = volume_score
        total_score += volume_score * self.weights[ConfidenceFactor.VOLUME_CONFIRMATION]
        total_weight += self.weights[ConfidenceFactor.VOLUME_CONFIRMATION]
        
        # Normalize score to 0-100 range
        if total_weight > 0:
            confidence = (total_score / total_weight) * 100
        else:
            confidence = 0.0
        
        # Apply minimum confidence threshold
        confidence = max(confidence, 0.0)
        confidence = min(confidence, 100.0)
        
        logger.debug(f"Confidence calculation - Type: {signal_type}, "
                    f"Direction: {direction}, Score: {confidence:.1f}%")
        
        return round(confidence, 1)
    
    def _calculate_rsi_strength(self, rsi: Optional[float], 
                              signal_type: SignalType, 
                              direction: SignalDirection) -> float:
        """Calculate RSI strength factor score (0-1)."""
        if rsi is None:
            return 0.0
        
        if signal_type in [SignalType.BULLISH_REVERSAL, SignalType.OVERSOLD_BOUNCE]:
            # For bullish signals, lower RSI is better
            if rsi <= self.thresholds['oversold_rsi']:
                return 1.0 - (rsi / self.thresholds['oversold_rsi'])
            else:
                return max(0.0, 1.0 - (rsi - self.thresholds['oversold_rsi']) / 30.0)
        
        elif signal_type in [SignalType.BEARISH_REVERSAL, SignalType.OVERBOUGHT_BOUNCE]:
            # For bearish signals, higher RSI is better
            if rsi >= self.thresholds['overbought_rsi']:
                return (rsi - self.thresholds['overbought_rsi']) / (100 - self.thresholds['overbought_rsi'])
            else:
                return max(0.0, (self.thresholds['overbought_rsi'] - rsi) / 30.0)
        
        elif signal_type == SignalType.MOMENTUM_CONTINUATION:
            # For momentum, RSI should be in the middle range
            if 40 <= rsi <= 60:
                return 1.0
            else:
                distance = min(abs(rsi - 40), abs(rsi - 60))
                return max(0.0, 1.0 - (distance / 20.0))
        
        return 0.0
    
    def _calculate_macd_convergence(self, macd_data: Optional[Dict[str, float]], 
                                  direction: SignalDirection) -> float:
        """Calculate MACD convergence factor score (0-1)."""
        if not macd_data or macd_data.get('macd') is None or macd_data.get('signal') is None:
            return 0.0
        
        macd_line = macd_data['macd']
        signal_line = macd_data['signal']
        histogram = macd_data.get('histogram', 0)
        
        # Check for crossover and momentum
        if direction == SignalDirection.BULLISH:
            # Bullish: MACD above signal, positive histogram
            if macd_line > signal_line and histogram > 0:
                strength = abs(histogram) / (abs(macd_line) + 0.001)
                return min(1.0, strength * 2.0)
        
        elif direction == SignalDirection.BEARISH:
            # Bearish: MACD below signal, negative histogram
            if macd_line < signal_line and histogram < 0:
                strength = abs(histogram) / (abs(macd_line) + 0.001)
                return min(1.0, strength * 2.0)
        
        return 0.0
    
    def _calculate_bollinger_position(self, bb_data: Optional[Dict[str, float]], 
                                    current_price: Optional[float], 
                                    direction: SignalDirection) -> float:
        """Calculate Bollinger Band position factor score (0-1)."""
        if not bb_data or current_price is None:
            return 0.0
        
        middle = bb_data.get('middle')
        upper = bb_data.get('upper')
        lower = bb_data.get('lower')
        
        if None in (middle, upper, lower):
            return 0.0
        
        # Calculate position relative to bands
        band_width = upper - lower
        if band_width == 0:
            return 0.0
        
        position = (current_price - lower) / band_width
        
        if direction == SignalDirection.BULLISH:
            # For bullish signals, being near lower band is good
            return max(0.0, 1.0 - position)
        elif direction == SignalDirection.BEARISH:
            # For bearish signals, being near upper band is good
            return max(0.0, position)
        
        return 0.0
    
    def _calculate_volume_confirmation(self, volume_data: Optional[Dict[str, Any]]) -> float:
        """Calculate volume confirmation factor score (0-1)."""
        if not volume_data:
            return 0.0
        
        volume_ratio = volume_data.get('volume_ratio')
        volume_spike = volume_data.get('volume_spike')
        
        if volume_ratio is None:
            return 0.0
        
        # Higher volume ratio increases confidence
        score = min(1.0, volume_ratio / 2.0)
        
        # Volume spike provides additional confirmation
        if volume_spike:
            score = min(1.0, score * 1.2)
        
        return score
    
    def get_confidence_level(self, score: float) -> str:
        """Get descriptive confidence level based on score."""
        if score >= 70.0:
            return "High"
        elif score >= 50.0:
            return "Medium"
        elif score >= 30.0:
            return "Low"
        else:
            return "Very Low"