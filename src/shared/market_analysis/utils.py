"""
Utility functions for market analysis integration across different commands.

This module provides easy-to-use functions for integrating market analysis
with confidence scoring into various bot commands and pipelines.
"""

import logging
from typing import Dict, List, Any, Optional
import pandas as pd

from .signal_analyzer import SignalAnalyzer
from .signals import MarketSignal

logger = logging.getLogger(__name__)


def analyze_symbol_with_confidence(symbol: str, timeframe: str, 
                                 market_data: pd.DataFrame) -> List[Dict[str, Any]]:
    """
    Analyze a single symbol and return signals with confidence scores.
    
    Args:
        symbol: Trading symbol to analyze
        timeframe: Timeframe for analysis (e.g., '1h', '1d')
        market_data: DataFrame with OHLCV data
        
    Returns:
        List of signal dictionaries with confidence scores
    """
    analyzer = SignalAnalyzer()
    signals = analyzer.analyze_market_data(symbol, timeframe, market_data)
    return [signal.to_dict() for signal in signals]


def get_highest_confidence_signal(signals: List[MarketSignal]) -> Optional[Dict[str, Any]]:
    """
    Get the signal with the highest confidence score.
    
    Args:
        signals: List of MarketSignal objects
        
    Returns:
        Dictionary of the highest confidence signal or None if no signals
    """
    if not signals:
        return None
    
    highest_signal = max(signals, key=lambda x: x.confidence_score)
    return highest_signal.to_dict()


def format_signal_for_display(signal: Dict[str, Any]) -> str:
    """
    Format a signal dictionary for display in chat or UI.
    
    Args:
        signal: Signal dictionary from to_dict()
        
    Returns:
        Formatted string for display
    """
    if not signal:
        return "No signals detected"
    
    signal_type = signal.get('signal_type', 'UNKNOWN')
    direction = signal.get('direction', 'NEUTRAL')
    confidence = signal.get('confidence_score', 0)
    symbol = signal.get('symbol', 'UNKNOWN')
    timeframe = signal.get('timeframe', 'UNKNOWN')
    
    return (f"**{symbol}** ({timeframe}) - {signal_type.replace('_', ' ').title()} "
            f"({direction.title()}) | Confidence: {confidence}%")


def analyze_multiple_symbols(symbols: List[str], timeframe: str,
                            market_data_provider: callable) -> Dict[str, List[Dict[str, Any]]]:
    """
    Analyze multiple symbols and return signals for each.
    
    Args:
        symbols: List of trading symbols to analyze
        timeframe: Timeframe for analysis
        market_data_provider: Function that returns market data for a symbol
        
    Returns:
        Dictionary mapping symbols to their signals
    """
    results = {}
    analyzer = SignalAnalyzer()
    
    for symbol in symbols:
        try:
            market_data = market_data_provider(symbol, timeframe)
            if market_data is not None and not market_data.empty:
                signals = analyzer.analyze_market_data(symbol, timeframe, market_data)
                results[symbol] = [signal.to_dict() for signal in signals]
            else:
                results[symbol] = []
                logger.warning(f"No market data available for {symbol}")
        except Exception as e:
            results[symbol] = []
            logger.error(f"Error analyzing {symbol}: {e}")
    
    return results


def filter_signals_by_confidence(signals: List[Dict[str, Any]], 
                                min_confidence: float = 50.0) -> List[Dict[str, Any]]:
    """
    Filter signals by minimum confidence threshold.
    
    Args:
        signals: List of signal dictionaries
        min_confidence: Minimum confidence score (0-100)
        
    Returns:
        Filtered list of signals
    """
    return [signal for signal in signals if signal.get('confidence_score', 0) >= min_confidence]