"""
AI Tool Registry for Unified Bot Capabilities

Provides a centralized registry of tools/abilities that the AI can invoke
to fulfill user requests. This ensures consistent interfaces and prevents
duplication across different AI processors.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.core.logger import get_logger
from src.api.data.market_data_service import MarketDataService
from src.shared.technical_analysis.calculator import technical_analysis_calculator
from src.shared.technical_analysis.zones import enhanced_zone_analyzer
from src.shared.market_analysis.unified_signal_analyzer import analyze_market_data
from src.core.risk_management.atr_calculator import atr_stop_loss_calculator, StopLossConfig, TradeDirection
from src.core.market_calendar import get_market_context

logger = get_logger(__name__)


class ToolRegistry:
    """
    Centralized registry of AI tools and capabilities.
    
    This class provides a unified interface for AI processors to access
    market data, technical analysis, risk management, and other bot capabilities.
    """
    
    def __init__(self):
        """Initialize the tool registry with service instances."""
        self.market_service = MarketDataService()
        self._cache = {}  # Simple in-memory cache for expensive operations
        self._cache_ttl = 300  # 5 minutes
    
    async def price_check(self, symbol: str) -> Dict[str, Any]:
        """
        Get current price and basic market data for a symbol.
        
        Args:
            symbol: Stock symbol to check
            
        Returns:
            Dict containing price, change, volume, and other basic data
        """
        try:
            logger.info(f"🔍 Tool: price_check for {symbol}")
            data = await self.market_service.get_comprehensive_stock_data(symbol)
            
            # Ensure we have a consistent response format
            return {
                'tool': 'price_check',
                'symbol': symbol,
                'success': data.get('status') == 'success',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Tool price_check failed for {symbol}: {e}")
            return {
                'tool': 'price_check',
                'symbol': symbol,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def technical_analysis(self, symbol: str, timeframe: str = "1d") -> Dict[str, Any]:
        """
        Get comprehensive technical analysis for a symbol.
        
        Args:
            symbol: Stock symbol to analyze
            timeframe: Analysis timeframe (1d, 4h, 1h)
            
        Returns:
            Dict containing indicators, zones, and analysis results
        """
        try:
            logger.info(f"📊 Tool: technical_analysis for {symbol} ({timeframe})")
            
            # Get historical data for analysis
            historical_data = await self.market_service.get_historical_data(symbol, days=30)
            
            if not historical_data:
                return {
                    'tool': 'technical_analysis',
                    'symbol': symbol,
                    'success': False,
                    'error': 'No historical data available',
                    'timestamp': datetime.now().isoformat()
                }
            
            # Convert to DataFrame for technical analysis
            import pandas as pd
            data_dicts = []
            for item in historical_data:
                data_dicts.append({
                    'date': item.timestamp,
                    'open': item.open,
                    'high': item.high,
                    'low': item.low,
                    'close': item.close,
                    'volume': item.volume
                })
            
            df = pd.DataFrame(data_dicts)
            if not df.empty:
                df.set_index('date', inplace=True)
            
            # Calculate technical indicators
            indicators = technical_analysis_calculator.calculate_all_indicators(df, symbol)
            
            # Detect supply/demand zones
            zones = enhanced_zone_analyzer.detect_enhanced_zones(df, symbol)
            
            return {
                'tool': 'technical_analysis',
                'symbol': symbol,
                'timeframe': timeframe,
                'success': True,
                'indicators': indicators,
                'zones': zones,
                'data_points': len(df),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Tool technical_analysis failed for {symbol}: {e}")
            return {
                'tool': 'technical_analysis',
                'symbol': symbol,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def trading_signals(self, symbol: str, timeframes: List[str] = None) -> Dict[str, Any]:
        """
        Generate trading signals for a symbol across multiple timeframes.
        
        Args:
            symbol: Stock symbol to analyze
            timeframes: List of timeframes to analyze (default: ['1d', '4h', '1h'])
            
        Returns:
            Dict containing signals and recommendations
        """
        try:
            if timeframes is None:
                timeframes = ['1d', '4h', '1h']
            
            logger.info(f"📈 Tool: trading_signals for {symbol} ({timeframes})")
            
            # Get market data
            historical_data = await self.market_service.get_historical_data(symbol, days=30)
            
            if not historical_data:
                return {
                    'tool': 'trading_signals',
                    'symbol': symbol,
                    'success': False,
                    'error': 'No market data available for signal generation',
                    'timestamp': datetime.now().isoformat()
                }
            
            # Convert to DataFrame
            import pandas as pd
            data_dicts = []
            for item in historical_data:
                data_dicts.append({
                    'date': item.timestamp,
                    'open': item.open,
                    'high': item.high,
                    'low': item.low,
                    'close': item.close,
                    'volume': item.volume
                })
            
            df = pd.DataFrame(data_dicts)
            if not df.empty:
                df.set_index('date', inplace=True)
            
            # Generate signals for each timeframe
            all_signals = []
            for timeframe in timeframes:
                try:
                    signals = await analyze_market_data(symbol, timeframe, df)
                    all_signals.extend(signals)
                except Exception as e:
                    logger.warning(f"Signal generation failed for {timeframe}: {e}")
                    continue
            
            return {
                'tool': 'trading_signals',
                'symbol': symbol,
                'timeframes': timeframes,
                'success': True,
                'signals': all_signals,
                'signal_count': len(all_signals),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Tool trading_signals failed for {symbol}: {e}")
            return {
                'tool': 'trading_signals',
                'symbol': symbol,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def risk_analysis(self, symbol: str, entry_price: float = None, direction: str = "long") -> Dict[str, Any]:
        """
        Calculate risk management parameters including stop-loss levels.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price (uses current price if None)
            direction: Trade direction ("long" or "short")
            
        Returns:
            Dict containing stop-loss levels and risk parameters
        """
        try:
            logger.info(f"⚠️ Tool: risk_analysis for {symbol} ({direction})")
            
            # Get current price if entry_price not provided
            if entry_price is None:
                price_data = await self.price_check(symbol)
                if not price_data['success']:
                    return {
                        'tool': 'risk_analysis',
                        'symbol': symbol,
                        'success': False,
                        'error': 'Could not get current price for risk analysis',
                        'timestamp': datetime.now().isoformat()
                    }
                entry_price = price_data['data'].get('current_price')
                
                if entry_price is None:
                    return {
                        'tool': 'risk_analysis',
                        'symbol': symbol,
                        'success': False,
                        'error': 'No valid entry price available',
                        'timestamp': datetime.now().isoformat()
                    }
            
            # Get historical data for ATR calculation
            historical_data = await self.market_service.get_historical_data(symbol, days=30)
            
            if not historical_data:
                return {
                    'tool': 'risk_analysis',
                    'symbol': symbol,
                    'success': False,
                    'error': 'No historical data for ATR calculation',
                    'timestamp': datetime.now().isoformat()
                }
            
            # Convert to DataFrame
            import pandas as pd
            data_dicts = []
            for item in historical_data:
                data_dicts.append({
                    'date': item.timestamp,
                    'open': item.open,
                    'high': item.high,
                    'low': item.low,
                    'close': item.close,
                    'volume': item.volume
                })
            
            df = pd.DataFrame(data_dicts)
            if not df.empty:
                df.set_index('date', inplace=True)
            
            # Calculate ATR-based stop-loss
            trade_direction = TradeDirection.LONG if direction.lower() == "long" else TradeDirection.SHORT
            stop_loss_result = atr_stop_loss_calculator.calculate_atr_stop_loss(
                df, symbol, trade_direction, entry_price
            )
            
            if stop_loss_result:
                return {
                    'tool': 'risk_analysis',
                    'symbol': symbol,
                    'success': True,
                    'entry_price': entry_price,
                    'direction': direction,
                    'stop_loss_price': stop_loss_result.stop_loss_price,
                    'risk_percentage': stop_loss_result.risk_percentage,
                    'position_size': stop_loss_result.position_size,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'tool': 'risk_analysis',
                    'symbol': symbol,
                    'success': False,
                    'error': 'Could not calculate stop-loss levels',
                    'timestamp': datetime.now().isoformat()
                }
            
        except Exception as e:
            logger.error(f"❌ Tool risk_analysis failed for {symbol}: {e}")
            return {
                'tool': 'risk_analysis',
                'symbol': symbol,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def market_context(self) -> Dict[str, Any]:
        """
        Get current market context including hours, status, and calendar info.
        
        Returns:
            Dict containing market status and context information
        """
        try:
            logger.info(f"🕐 Tool: market_context")
            
            # Get market context (sync function, no await needed)
            context = get_market_context(datetime.now())
            
            return {
                'tool': 'market_context',
                'success': True,
                'context': context,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Tool market_context failed: {e}")
            return {
                'tool': 'market_context',
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


# Create singleton instance
tool_registry = ToolRegistry()
