"""
Canonical AI Chat Processor Implementation

This module provides a unified implementation of the AI Chat Processor
that can be used by both the pipeline and command-based implementations.
"""

import logging
import json
import os
import asyncio
import time
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Tuple

# Import logger from unified logging system
from src.core.logger import get_logger
logger = get_logger(__name__)

# Import configuration from unified config system
try:
    from src.core.config_manager import get_config
    config = get_config()
except ImportError:
    # Fallback config if main config is not available - use environment variables
    import os
    config = {
        'pipeline': {
            'model': os.getenv('MODEL_GLOBAL_FALLBACK', 'moonshotai/kimi-k2:free'),
            'temperature': float(os.getenv('AI_DEFAULT_TEMPERATURE', '0.7')),
            'max_tokens': int(os.getenv('AI_DEFAULT_MAX_TOKENS', '2000')),
            'timeout': float(os.getenv('AI_DEFAULT_TIMEOUT_MS', '30000')) / 1000.0  # Convert ms to seconds
        },
        'technical': {
            'rsi_period': 14,
            'sma_short': 20,
            'sma_long': 50,
            'ema_short': 12,
            'ema_long': 26,
            'lookback_days': 90,
            'recent_prices_days': 20,
            'volume_sma_period': 20,
            'bollinger_period': 20,
            'bollinger_std': 2,
            'decimal_places': 2
        }
    }

# Import market data service and tool registry
try:
    from src.api.data.market_data_service import MarketDataService
    from src.shared.ai_services.tool_registry import tool_registry
    TOOLS_AVAILABLE = True
except ImportError:
    # Fallback implementation if the main service is not available
    class MarketDataService:
        def __init__(self):
            self.cache = {}
            self.logger = logging.getLogger(__name__)

        async def get_comprehensive_stock_data(self, symbol: str):
            # Basic fallback implementation
            return {
                'symbol': symbol,
                'current_price': 100.0,
                'change_percent': 0.0,
                'volume': 1000000,
                'status': 'fallback'
            }

        async def get_technical_indicators(self, symbol: str):
            return []

        async def get_historical_data(self, symbol: str, days: int = 7):
            return []

        async def get_current_price(self, symbol: str):
            return {'price': 100.0, 'change': 0.0}

    tool_registry = None
    TOOLS_AVAILABLE = False

# Import SignalAnalyzer with fallback
try:
    from src.shared.market_analysis.signal_analyzer import SignalAnalyzer
except ImportError:
    # Fallback implementation if SignalAnalyzer is not available
    class SignalAnalyzer:
        def __init__(self):
            self.logger = logging.getLogger(__name__)
            
        def analyze_market_data(self, symbol: str, timeframe: str, market_data):
            # Basic fallback implementation
            return []

# Default system prompt and fallback responses
SYSTEM_PROMPT = """You are a financial analysis assistant. Analyze the user's query and respond with JSON.
Your response must be valid JSON with these fields:
- intent: One of "stock_analysis", "market_overview", "price_check", "general_question"
- symbols: List of stock symbols mentioned (e.g., ["AAPL", "MSFT"])
- needs_data: Boolean indicating if you need market data
- response: Your response to the user's query

Example: {"intent": "stock_analysis", "symbols": ["AAPL"], "needs_data": true, "response": "Apple stock analysis..."}"""

FALLBACK_RESPONSES = {
    "no_ai_config": "I'm sorry, but I'm not able to process your request at the moment due to a configuration issue. Please try again later.",
    "ai_error": "I apologize, but I encountered an error while processing your request. Please try again with a different query.",
    "no_data": "I couldn't retrieve the market data needed to answer your question. Please try again later."
}

class AIAskResult:
    """Model for AI response validation and normalization"""
    
    def __init__(self, intent: str = "general_question", symbols: List[str] = None, 
                 needs_data: bool = False, response: str = ""):
        self.intent = intent
        self.symbols = symbols or []
        self.needs_data = needs_data
        self.response = response
    
    def dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "intent": self.intent,
            "symbols": self.symbols,
            "needs_data": self.needs_data,
            "response": self.response
        }
    
    @classmethod
    def from_ai_response(cls, response_dict: Dict[str, Any]) -> 'AIAskResult':
        """Create from AI response dictionary with validation"""
        # Validate and normalize intent
        valid_intents = ["stock_analysis", "market_overview", "price_check", "general_question"]
        intent = response_dict.get("intent", "general_question")
        if intent not in valid_intents:
            intent = "general_question"
        
        # Validate and normalize symbols
        symbols = response_dict.get("symbols", [])
        if not isinstance(symbols, list):
            symbols = []
        # Ensure all symbols are strings and uppercase
        symbols = [str(s).upper() for s in symbols if s]
        
        # Validate needs_data
        needs_data = bool(response_dict.get("needs_data", False))
        
        # Validate response
        response = response_dict.get("response", "")
        if not isinstance(response, str):
            response = str(response)
        
        return cls(intent=intent, symbols=symbols, needs_data=needs_data, response=response)


class AIChatProcessor:
    """
    Unified AI Chat Processor
    
    This class provides a unified implementation of the AI Chat Processor
    that can be used by both the pipeline and command-based implementations.
    """
    
    def __init__(self, context: Optional[Any] = None, data_providers: Optional[List[str]] = None):
        """
        Initialize the AI Chat Processor
        
        Args:
            context: Optional context object for tracking and logging
            data_providers: Optional list of data provider names to use
        """
        # Store context for logging and pipeline tracking
        self.context = context
        self.pipeline_id = getattr(context, 'pipeline_id', 'unknown') if context else 'unknown'
        
        # Use centralized configuration with environment variable fallbacks
        import os
        self.model = config.get('pipeline', 'model', os.getenv('MODEL_GLOBAL_FALLBACK', 'moonshotai/kimi-k2:free'))
        self.temperature = config.get('pipeline', 'temperature', float(os.getenv('AI_DEFAULT_TEMPERATURE', '0.7')))
        self.max_tokens = config.get('pipeline', 'max_tokens', int(os.getenv('AI_DEFAULT_MAX_TOKENS', '2000')))
        self.ai_timeout = config.get('pipeline', 'timeout', float(os.getenv('AI_DEFAULT_TIMEOUT_MS', '30000')) / 1000.0)

        # Configure OpenRouter client - read directly from environment to avoid config placeholder issues
        api_key = os.getenv('OPENROUTER_API_KEY', '')
        base_url = os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1')

        logger.info(f"🔑 OpenRouter API Key: {'*****' if api_key else 'NOT SET'}")
        logger.info(f"🌐 OpenRouter Base URL: {base_url}")
        
        # OpenRouter uses OpenAI-compatible API but with different base URL
        if api_key and base_url:
            try:
                # Validate API key
                if not api_key.startswith('sk-'):
                    logger.error(f"❌ Invalid API key format: {api_key}")
                    self.client = None
                    return
                
                # Import OpenAI with detailed error handling
                try:
                    from openai import OpenAI
                except ImportError as import_e:
                    logger.error(f"❌ Failed to import OpenAI: {import_e}")
                    self.client = None
                    return
                
                # Create OpenAI client
                try:
                    self.client = OpenAI(
                        api_key=api_key,
                        base_url=base_url,
                        timeout=self.ai_timeout
                    )
                except Exception as client_e:
                    logger.error(f"❌ Failed to create OpenAI client: {type(client_e).__name__}: {str(client_e)}")
                    self.client = None
                    return
                
                logger.info(f"✅ [{self.pipeline_id}] OpenRouter client configured with model: {self.model}")
            except Exception as e:
                logger.error(f"❌ [{self.pipeline_id}] Failed to configure OpenRouter client: {e}")
                self.client = None
        else:
            logger.warning(f"❌ [{self.pipeline_id}] OpenRouter not configured - missing API key or base URL")
            self.client = None

        # Use unified market data service (async)
        self.market_data = MarketDataService()
        
        # Initialize signal analyzer
        self.signal_analyzer = SignalAnalyzer()
    
    async def process(self, query: str) -> Dict[str, Any]:
        """
        Process a user query via AI client and optional data fetching
        
        Args:
            query: The user's query string
            
        Returns:
            Dict containing response, data, intent, symbols, and needs_data
        """
        # Check cache first if available
        cached_response = await self._check_cache(query)
        if cached_response:
            logger.info(f"📦 [{self.pipeline_id}] Cache hit for query: {query[:50]}...")
            return cached_response
        
        # Step 1: Use AI to determine intent and if data is needed
        ai_result = await self._call_ai_model(query)

        # Normalize and validate AI outputs with safe defaults
        try:
            validated_result = AIAskResult.from_ai_response(ai_result)
        except Exception as e:
            logger.error(f"❌ [{self.pipeline_id}] AI response validation failed: {e}")

            # Fallback: Use symbol extractor to find symbols in the query
            from src.shared.utils.symbol_extraction import UnifiedSymbolExtractor
            extractor = UnifiedSymbolExtractor()
            extracted_symbols = extractor.extract_symbols(query, require_dollar_prefix=True)
            fallback_symbols = [s.symbol for s in extracted_symbols]

            # Determine intent based on symbols found
            fallback_intent = "price_check" if fallback_symbols else "general_question"
            fallback_needs_data = bool(fallback_symbols)

            logger.info(f"🔄 [{self.pipeline_id}] Fallback symbol extraction found: {fallback_symbols}")

            # Fallback to a safe default result with extracted symbols
            validated_result = AIAskResult(
                intent=fallback_intent,
                symbols=fallback_symbols,
                needs_data=fallback_needs_data,
                response="I encountered an issue processing your request. Please try again with a different query."
            )
        
        # Extract validated values
        intent = validated_result.intent
        symbols = validated_result.symbols
        needs_data = validated_result.needs_data
        base_response = validated_result.response

        # Ensure base_response is not empty
        if not base_response or base_response.strip() == "":
            base_response = "I couldn't generate a response for your query. Please try asking about stocks or trading topics."

        # Step 2: Use tool registry for enhanced data fetching if available
        data: Dict[str, Any] = {}
        tool_results: Dict[str, Any] = {}

        if needs_data and symbols and TOOLS_AVAILABLE and tool_registry:
            # Use tool registry for enhanced capabilities
            data, tool_results = await self._fetch_data_via_tools(symbols, intent)
        elif needs_data and symbols:
            # Fallback to basic market data fetching
            data = await self._fetch_market_data(symbols)

        # Step 3: Generate the final response with tool results
        final_response = await self._generate_final_response(base_response, data, tool_results)

        # Ensure final response is never empty
        if not final_response or final_response.strip() == "":
            final_response = "I apologize, but I couldn't generate a response for your query. Please try again or ask about something else."

        result = {
            'response': final_response,
            'data': data,
            'tool_results': tool_results,
            'intent': intent,
            'symbols': symbols,
            'needs_data': needs_data,
        }
        
        # Cache the result if possible
        await self._cache_result(query, result)
        
        return result
    
    async def _check_cache(self, query: str) -> Optional[Dict[str, Any]]:
        """Check if the query is in cache"""
        # This is a stub - implementations should override this
        return None
    
    async def _cache_result(self, query: str, result: Dict[str, Any]) -> None:
        """Cache the result for future use"""
        # This is a stub - implementations should override this
        pass
    
    async def _fetch_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """Fetch market data for the given symbols"""
        data: Dict[str, Any] = {}
        
        async def fetch_one(sym: str) -> Optional[Dict[str, Any]]:
            try:
                # Use timeout for individual symbol data fetching
                return await asyncio.wait_for(
                    self.market_data.get_comprehensive_stock_data(sym),
                    timeout=10.0  # 10 second timeout per symbol
                )
            except asyncio.TimeoutError:
                logger.warning(f"⏰ [{self.pipeline_id}] Data fetch timeout for symbol {sym}")
                return None
            except Exception as e:
                logger.error(f"❌ [{self.pipeline_id}] Error fetching data for {sym}: {e}")
                return None

        # Fetch all symbols concurrently with graceful error handling
        tasks = [fetch_one(sym) for sym in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results with detailed error logging
        for sym, result in zip(symbols, results):
            if isinstance(result, Exception):
                logger.error(f"❌ [{self.pipeline_id}] Failed to fetch data for {sym}: {result}")
                continue
            if result is not None:
                data[sym] = result
            else:
                logger.warning(f"⚠️ [{self.pipeline_id}] No data retrieved for symbol {sym}")
        
        return data
    
    async def _fetch_data_via_tools(self, symbols: List[str], intent: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Fetch data using the tool registry based on intent"""
        data = {}
        tool_results = {}

        try:
            # Determine which tools to use based on intent
            if intent == "stock_analysis":
                # Use comprehensive analysis tools
                for symbol in symbols[:3]:  # Limit to 3 symbols to avoid rate limits
                    # Get basic price data
                    price_result = await tool_registry.price_check(symbol)
                    if price_result['success']:
                        data[symbol] = price_result['data']

                    # Get technical analysis
                    tech_result = await tool_registry.technical_analysis(symbol)
                    if tech_result['success']:
                        tool_results[f"{symbol}_technical"] = tech_result

                    # Get trading signals
                    signals_result = await tool_registry.trading_signals(symbol)
                    if signals_result['success']:
                        tool_results[f"{symbol}_signals"] = signals_result

            elif intent == "price_check":
                # Just get current prices
                for symbol in symbols[:5]:  # Allow more symbols for simple price checks
                    price_result = await tool_registry.price_check(symbol)
                    if price_result['success']:
                        data[symbol] = price_result['data']

            else:
                # Default to basic price data
                for symbol in symbols[:3]:
                    price_result = await tool_registry.price_check(symbol)
                    if price_result['success']:
                        data[symbol] = price_result['data']

            # Always get market context for any request with symbols
            if symbols:
                context_result = await tool_registry.market_context()
                if context_result['success']:
                    tool_results['market_context'] = context_result

        except Exception as e:
            logger.error(f"❌ Tool registry data fetching failed: {e}")
            # Fallback to basic market data
            data = await self._fetch_market_data(symbols)

        return data, tool_results

    async def _generate_final_response(self, base_response: str, data: Dict[str, Any], tool_results: Dict[str, Any] = None) -> str:
        """Generate the final response based on the base response, data, and tool results"""
        if tool_results is None:
            tool_results = {}

        # If we have tool results, create an enhanced response
        if tool_results:
            return await self._generate_enhanced_response(base_response, data, tool_results)

        # Otherwise, use the simple data integration
        if not data:
            return base_response
        
        # Basic data integration into response with safe numeric formatting
        data_summary = []
        for symbol, symbol_data in data.items():
            price = symbol_data.get('current_price', symbol_data.get('price'))
            change = symbol_data.get('change_percent', symbol_data.get('change'))

            # Safe numeric conversion
            try:
                p = float(price) if price is not None else None
            except (TypeError, ValueError):
                p = None

            try:
                c = float(change) if change is not None else None
            except (TypeError, ValueError):
                c = None

            # Format with safe defaults
            if p is not None and c is not None:
                change_sign = "+" if c >= 0 else ""
                data_summary.append(f"{symbol}: ${p:.2f} ({change_sign}{c:.2f}%)")
            else:
                data_summary.append(f"{symbol}: Data unavailable")

        data_str = "\n".join(data_summary)
        return f"{base_response}\n\nLatest data:\n{data_str}"

    async def _generate_enhanced_response(self, base_response: str, data: Dict[str, Any], tool_results: Dict[str, Any]) -> str:
        """Generate an enhanced response using tool results"""
        response_parts = [base_response]

        # Add market context if available
        if 'market_context' in tool_results:
            context = tool_results['market_context'].get('context', {})
            if isinstance(context, dict):
                status = context.get('status', 'unknown')
                response_parts.append(f"\n📊 Market Status: {status}")

        # Add price data
        if data:
            response_parts.append("\n💰 Current Prices:")
            for symbol, symbol_data in data.items():
                price = symbol_data.get('current_price', symbol_data.get('price'))
                change = symbol_data.get('change_percent', symbol_data.get('change'))

                # Safe numeric conversion
                try:
                    p = float(price) if price is not None else None
                except (TypeError, ValueError):
                    p = None

                try:
                    c = float(change) if change is not None else None
                except (TypeError, ValueError):
                    c = None

                # Format with safe defaults
                if p is not None and c is not None:
                    change_sign = "+" if c >= 0 else ""
                    response_parts.append(f"• {symbol}: ${p:.2f} ({change_sign}{c:.2f}%)")
                else:
                    response_parts.append(f"• {symbol}: Data unavailable")

        # Add technical analysis summary if available
        tech_symbols = [k.replace('_technical', '') for k in tool_results.keys() if k.endswith('_technical')]
        if tech_symbols:
            response_parts.append("\n📈 Technical Analysis:")
            for symbol in tech_symbols:
                tech_key = f"{symbol}_technical"
                if tech_key in tool_results:
                    tech_data = tool_results[tech_key]
                    if tech_data.get('success'):
                        indicators = tech_data.get('indicators', {})
                        zones = tech_data.get('zones', {})

                        # Add key indicators
                        rsi = indicators.get('rsi')
                        if rsi:
                            rsi_status = "Overbought" if rsi > 70 else "Oversold" if rsi < 30 else "Neutral"
                            response_parts.append(f"• {symbol} RSI: {rsi:.1f} ({rsi_status})")

                        # Add zone information
                        if zones and zones.get('analysis'):
                            zone_summary = zones['analysis'].get('summary', 'No zones detected')
                            response_parts.append(f"• {symbol} Zones: {zone_summary}")

        # Add trading signals summary if available
        signal_symbols = [k.replace('_signals', '') for k in tool_results.keys() if k.endswith('_signals')]
        if signal_symbols:
            response_parts.append("\n🎯 Trading Signals:")
            for symbol in signal_symbols:
                signals_key = f"{symbol}_signals"
                if signals_key in tool_results:
                    signals_data = tool_results[signals_key]
                    if signals_data.get('success'):
                        signals = signals_data.get('signals', [])
                        if signals:
                            signal_count = len(signals)
                            response_parts.append(f"• {symbol}: {signal_count} signals detected")
                        else:
                            response_parts.append(f"• {symbol}: No signals detected")

        return "\n".join(response_parts)
    
    async def _call_ai_model(self, query: str) -> Dict[str, Any]:
        """Call the AI model to process the query"""
        max_attempts = 3
        last_error = None

        for attempt in range(1, max_attempts + 1):
            try:
                if self.client is None:
                    logger.warning(f"[{self.pipeline_id}] AI client is not configured (missing API key). Returning fallback response.")
                    return AIAskResult(
                        intent="general_question",
                        symbols=[],
                        needs_data=False,
                        response=FALLBACK_RESPONSES["no_ai_config"]
                    ).dict()

                messages = [
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": query},
                ]

                logger.info(f"🤖 [{self.pipeline_id}] Calling AI model {self.model} with query: {query[:50]}...")
                completion = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                )

                content = ""
                if completion and getattr(completion, 'choices', None):
                    try:
                        content = completion.choices[0].message.content
                    except Exception:
                        # Fallback for any structural differences
                        first = completion.choices[0]
                        content = getattr(getattr(first, 'message', {}), 'content', "") or getattr(first, 'text', "")

                # Attempt to parse JSON; tolerate code fences and sanitize input
                parsed: Dict[str, Any] = {}
                try:
                    text = content.strip()
                    # Sanitize the text to remove control characters
                    text = self._sanitize_json_string(text)
                    if text.startswith("```"):
                        # Strip triple backticks with optional language tag
                        text = text.strip('`')
                        # Heuristic: find first '{' onwards
                        brace = text.find('{')
                        if brace != -1:
                            text = text[brace:]
                    parsed = json.loads(text) if text else {}
                except Exception as e:
                    logger.warning(f"⚠️ [{self.pipeline_id}] Failed to parse AI JSON; using defaults. Error: {e}")
                    # Try to extract JSON-like content with more lenient parsing
                    try:
                        # Look for JSON object in the text
                        import re
                        json_match = re.search(r'\{.*\}', text, re.DOTALL)
                        if json_match:
                            json_str = json_match.group(0)
                            json_str = self._sanitize_json_string(json_str)
                            parsed = json.loads(json_str)
                        else:
                            parsed = {}
                    except Exception as parse_error:
                        logger.error(f"❌ [{self.pipeline_id}] Even lenient JSON parsing failed: {parse_error}")
                        parsed = {}

                # Use Pydantic model for validation
                validated_result = AIAskResult.from_ai_response(parsed)
                
                # Check for placeholder usage in the response
                if self._contains_placeholders(validated_result.response):
                    logger.warning(f"⚠️ [{self.pipeline_id}] AI response contains template placeholders - generating fallback response")
                    return AIAskResult(
                        intent=validated_result.intent,
                        symbols=validated_result.symbols,
                        needs_data=validated_result.needs_data,
                        response="I need to fetch current market data to provide accurate analysis. Please try again in a moment."
                    ).dict()
                
                logger.info(f"✅ [{self.pipeline_id}] AI call successful. Intent: {validated_result.intent}, Symbols: {validated_result.symbols}")
                return validated_result.dict()

            except Exception as e:
                last_error = e
                logger.warning(f"[{self.pipeline_id}] AI call attempt {attempt} failed: {e}")
                
                # Wait with exponential backoff
                await asyncio.sleep(2 ** attempt)  # 2, 4, 8 seconds between retries

        # If all attempts fail
        logger.error(f"❌ [{self.pipeline_id}] AI call failed after {max_attempts} attempts. Last error: {last_error}")
        return AIAskResult(
            intent="general_question",
            symbols=[],
            needs_data=False,
            response=FALLBACK_RESPONSES["ai_error"]
        ).dict()
    
    def _contains_placeholders(self, text: str) -> bool:
        """Check if text contains template placeholders like {symbol1}, {price1}, etc."""
        import re
        if not isinstance(text, str):
            return False
        # Pattern to match common placeholder formats: {word} or {word_number}
        placeholder_pattern = r'\{[a-zA-Z_][a-zA-Z0-9_]*\}'
        return bool(re.search(placeholder_pattern, text))

    def _sanitize_json_string(self, text: str) -> str:
        """Remove control characters and other invalid JSON characters from string"""
        import re
        if not isinstance(text, str):
            return ""
        # Remove control characters (except tab, newline, carriage return)
        sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        # Remove any other potentially problematic characters
        sanitized = sanitized.strip()
        return sanitized


# Factory function to create an AIChatProcessor instance
def create_processor(context: Optional[Any] = None, data_providers: Optional[List[str]] = None) -> AIChatProcessor:
    """
    Create an AIChatProcessor instance
    
    Args:
        context: Optional context object for tracking and logging
        data_providers: Optional list of data provider names to use
        
    Returns:
        AIChatProcessor: An instance of the AIChatProcessor
    """
    return AIChatProcessor(context=context, data_providers=data_providers)


# Async helper function for processing queries
async def process_query(query: str, context: Optional[Any] = None, data_providers: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Process a query using the AIChatProcessor
    
    Args:
        query: The user's query string
        context: Optional context object for tracking and logging
        data_providers: Optional list of data provider names to use
        
    Returns:
        Dict containing response, data, intent, symbols, and needs_data
    """
    processor = create_processor(context=context, data_providers=data_providers)
    return await processor.process(query)
