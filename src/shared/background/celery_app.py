"""
Celery application configuration for background tasks.
Provides distributed task processing for market data collection and analysis.
"""

import os
from celery import Celery
from src.core.config_manager import config

# Create Celery app instance
app = Celery('trading_bot')

# Configure Celery with <PERSON>is as broker and backend
# Get Redis URL from environment or config with proper fallback
redis_url = os.getenv('REDIS_URL')
if not redis_url:
    redis_url = config.get('app', 'redis_url')
if not redis_url:
    # Log warning and use default, but this may cause issues in production
    import logging
    logger = logging.getLogger(__name__)
    logger.warning("No Redis URL configured for Celery, using environment default. This may cause issues in production.")
    redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')

app.conf.update(
    broker_url=redis_url,
    result_backend=redis_url,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=config.get('app', 'celery_task_time_limit', 300),  # 5 minutes
    task_soft_time_limit=config.get('app', 'celery_task_soft_time_limit', 240),  # 4 minutes
    worker_prefetch_multiplier=config.get('app', 'celery_worker_prefetch_multiplier', 1),
    task_acks_late=True,
    worker_disable_rate_limits=False,
    task_compression='gzip',
    result_compression='gzip',
)

# Eagerly import modules that aren't named "tasks.py" so Celery registers them
try:  # Defensive import; ignore if module already imported
    import importlib
    importlib.import_module("src.shared.background.tasks.market_intelligence")
    importlib.import_module("src.shared.background.tasks.indicators")
except Exception:  # pragma: no cover
    pass

# Celery configuration from centralized config
app.conf.beat_schedule = {
    # Proactive market scan every 15 minutes
    'proactive-market-scan': {
        'task': 'src.shared.background.tasks.market_intelligence.proactive_market_scan',
        'schedule': 900.0,  # 15 minutes
    },
    # Update indicator cache every hour
    'update-indicator-cache': {
        'task': 'src.shared.background.tasks.indicators.update_indicator_cache',
        'schedule': 3600.0,  # 60 minutes
    },
    # Update market cache every 30 minutes
    'update-market-cache': {
        'task': 'src.shared.background.tasks.market_intelligence.update_market_cache',
        'schedule': 1800.0,  # 30 minutes
    },
}

if __name__ == "__main__":
    app.start()