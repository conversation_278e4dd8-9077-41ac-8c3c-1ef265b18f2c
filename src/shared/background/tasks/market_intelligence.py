\"\"\"\nCelery Tasks for Automated Market Intelligence\nBackground processing for market scanning, alerts, and data caching\n\"\"\"\n\nfrom datetime import datetime\nfrom typing import Dict, Any, cast\nimport logging\nimport httpx\nimport pandas as pd\nimport os\nimport yfinance as yf\n\nfrom src.shared.background.celery_app import celery_app\nfrom src.core.config_manager import get_config\n\nlogger = logging.getLogger(__name__)\n\n# Get configuration from central config manager\nconfig = get_config()\ntechnical_config = config.get_technical_analysis_config()\n\n# Get timeout configuration from environment (with fallback to data provider config)\nHTTP_TIMEOUT = float(os.getenv(\"HTTP_TIMEOUT\", str(config.get('api', 'request_timeout', 10.0))))\n\n# Alpha Vantage API key\nALPHA_VANTANCE_API_KEY = os.getenv(\"ALPHA_VANTAGE_API_KEY\")\n\n# Configuration from environment variables (with fallback to central config)\nDEFAULT_SCAN_SYMBOLS = os.getenv(\"DEFAULT_SCAN_SYMBOLS\", \"AAPL,MSFT,GOOGL,AMZN,TSLA,NVDA,SPY,QQQ\")\nDEFAULT_CACHE_SYMBOLS = os.getenv(\"DEFAULT_CACHE_SYMBOLS\", \"SPY,QQQ,AAPL,MSFT,GOOGL,AMZN,TSLA,NVDA\")\nALERT_THRESHOLD = float(os.getenv(\"ALERT_THRESHOLD\", \"75.0\"))\nYFINANCE_PERIOD = os.getenv(\"YFINANCE_PERIOD\", \"60d\")\n\n# Technical indicator parameters (using central config with environment overrides)\nSMA_SHORT_WINDOW = int(os.getenv(\"SMA_SHORT_WINDOW\", str(technical_config.sma_window)))\nSMA_LONG_WINDOW = int(os.getenv(\"SMA_LONG_WINDOW\", \"50\"))\nRSI_WINDOW = int(os.getenv(\"RSI_WINDOW\", str(technical_config.rsi_period)))\nBB_WINDOW = int(os.getenv(\"BB_WINDOW\", str(technical_config.bb_window)))\nVOLUME_WINDOW = int(os.getenv(\"VOLUME_WINDOW\", str(technical_config.volume_window)))\nMACD_FAST = int(os.getenv(\"MACD_FAST\", str(technical_config.macd_fast)))\nMACD_SLOW = int(os.getenv(\"MACD_SLOW\", str(technical_config.macd_slow)))\nMACD_SIGNAL = int(os.getenv(\"MACD_SIGNAL\", str(technical_config.macd_signal)))\n\n# Analysis thresholds\nMIN_DATA_DAYS = int(os.getenv(\"MIN_DATA_DAYS\", \"50\"))\nPRICE_CHANGE_DAYS = int(os.getenv(\"PRICE_CHANGE_DAYS\", \"5\"))\nRSI_OVERSOLD = float(os.getenv(\"RSI_OVERSOLD\", \"30.0\"))\nRSI_OVERBOUGHT = float(os.getenv(\"RSI_OVERBOUGHT\", \"70.0\"))\nVOLUME_HIGH_THRESHOLD = float(os.getenv(\"VOLUME_HIGH_THRESHOLD\", \"1.5\"))\nVOLUME_LOW_THRESHOLD = float(os.getenv(\"VOLUME_LOW_THRESHOLD\", \"0.5\"))\nPRICE_MOMENTUM_THRESHOLD = float(os.getenv(\"PRICE_MOMENTUM_THRESHOLD\", \"5.0\"))\nHIGH_CONFIDENCE_THRESHOLD = float(os.getenv(\"HIGH_CONFIDENCE_THRESHOLD\", \"70.0\"))\nEXTREME_MOMENTUM_THRESHOLD = float(os.getenv(\"EXTREME_MOMENTUM_THRESHOLD\", \"8.0\"))\n

def fetch_real_market_data_alpha_vantage(symbol: str) -> Dict[str, Any] | None:
    """Fetch real market data from Alpha Vantage API"""
    if not ALPHA_VANTAGE_API_KEY:
        logger.warning("Alpha Vantage API key not configured")
        return None
        
    try:
        logger.info(f"Fetching real market data for {symbol} from Alpha Vantage")
        
        # Use Alpha Vantage API to get daily time series data
        url = f"https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol={symbol}&outputsize=compact&apikey={ALPHA_VANTAGE_API_KEY}"
        response = httpx.get(url, timeout=HTTP_TIMEOUT)
        data = cast(Dict[str, Any], response.json())
        
        # Check if we got valid data
        if "Time Series (Daily)" not in data:
            error_msg = data.get('Information', data.get('Note', 'Unknown error'))
            logger.warning(f"No valid data returned for {symbol}: {error_msg}")
            return None
            
        # Extract time series data
        time_series = cast(Dict[str, Dict[str, str]], data["Time Series (Daily)"])
        
        # Convert to DataFrame
        df: pd.DataFrame = pd.DataFrame.from_dict(time_series, orient="index", dtype="float")
        
        # Convert string columns to numeric
        for col in df.columns:
            df[col] = pd.to_numeric(df[col])
            
        # Rename columns
        df.rename(columns={
            "1. open": "open",
            "2. high": "high",
            "3. low": "low", 
            "4. close": "close",
            "5. volume": "volume"
        }, inplace=True)
        
        # Sort by date (ascending)
        df.index = pd.to_datetime(df.index)
        df.sort_index(inplace=True)
        
        if len(df) < MIN_DATA_DAYS:
            logger.warning(f"Insufficient data for {symbol}: only {len(df)} days")
            return None
        
        # Calculate technical indicators from real data
        close_prices = df['close']
        
        # Simple Moving Averages
        sma_20 = float(close_prices.rolling(window=SMA_SHORT_WINDOW).mean().iloc[-1])
        sma_50 = float(close_prices.rolling(window=SMA_LONG_WINDOW).mean().iloc[-1]) if len(close_prices) >= SMA_LONG_WINDOW else sma_20
        
        # RSI calculation
        delta = close_prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=RSI_WINDOW).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=RSI_WINDOW).mean()
        rs = gain / loss
        rsi = float(100 - (100 / (1 + rs)).iloc[-1])
        
        # MACD
        ema_12 = close_prices.ewm(span=MACD_FAST).mean()
        ema_26 = close_prices.ewm(span=MACD_SLOW).mean()
        macd = float(ema_12.iloc[-1] - ema_26.iloc[-1])
        signal = float((ema_12 - ema_26).ewm(span=MACD_SIGNAL).mean().iloc[-1])
        
        # Bollinger Bands
        bb_middle = float(close_prices.rolling(window=BB_WINDOW).mean().iloc[-1])
        bb_std = float(close_prices.rolling(window=BB_WINDOW).std().iloc[-1])
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)
        
        # Volume analysis
        avg_volume = float(df['volume'].rolling(window=VOLUME_WINDOW).mean().iloc[-1])
        current_volume = float(df['volume'].iloc[-1])
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # Price momentum
        price_change = float((df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5] * 100)
        
        # Create comprehensive market data
        market_data: Dict[str, Any] = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "price": {
                "current": float(df['close'].iloc[-1]),
                "open": float(df['open'].iloc[-1]),
                "high": float(df['high'].iloc[-1]),
                "low": float(df['low'].iloc[-1]),
                "change_5d": price_change
            },
            "volume": {
                "current": int(current_volume),
                "average_20d": int(avg_volume),
                "ratio": volume_ratio
            },
            "indicators": {
                "sma_20": sma_20,
                "sma_50": sma_50,
                "rsi": rsi,
                "macd": macd,
                "macd_signal": signal,
                "bb_upper": bb_upper,
                "bb_middle": bb_middle,
                "bb_lower": bb_lower
            },
            "source": "alpha_vantage"
        }
        
        logger.info(f"Successfully fetched market data for {symbol}")
        return market_data
        
    except Exception as e:
        logger.error(f"Error fetching market data for {symbol}: {e}")
        return None

def fetch_real_market_data_yfinance(symbol: str) -> Dict[str, Any] | None:
    """Fetch real market data from Yahoo Finance"""
    try:
        logger.info(f"Fetching real market data for {symbol} from Yahoo Finance")
        
        # Use yfinance to get data
        ticker = yf.Ticker(symbol)
        hist = ticker.history(period=YFINANCE_PERIOD)
        
        if len(hist) < MIN_DATA_DAYS:
            logger.warning(f"Insufficient data for {symbol}: only {len(hist)} days")
            return None
        
        # Calculate technical indicators
        close_prices = hist['Close']
        
        # Simple Moving Averages
        sma_20 = float(close_prices.rolling(window=SMA_SHORT_WINDOW).mean().iloc[-1])
        sma_50 = float(close_prices.rolling(window=SMA_LONG_WINDOW).mean().iloc[-1])
        
        # RSI calculation
        delta = close_prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=RSI_WINDOW).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=RSI_WINDOW).mean()
        rs = gain / loss
        rsi = float(100 - (100 / (1 + rs)).iloc[-1])
        
        # MACD
        ema_12 = close_prices.ewm(span=MACD_FAST).mean()
        ema_26 = close_prices.ewm(span=MACD_SLOW).mean()
        macd = float(ema_12.iloc[-1] - ema_26.iloc[-1])
        signal = float((ema_12 - ema_26).ewm(span=MACD_SIGNAL).mean().iloc[-1])
        
        # Bollinger Bands
        bb_middle = float(close_prices.rolling(window=BB_WINDOW).mean().iloc[-1])
        bb_std = float(close_prices.rolling(window=BB_WINDOW).std().iloc[-1])
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)
        
        # Volume analysis
        avg_volume = float(hist['Volume'].rolling(window=VOLUME_WINDOW).mean().iloc[-1])
        current_volume = float(hist['Volume'].iloc[-1])
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # Price momentum
        price_change = float((hist['Close'].iloc[-1] - hist['Close'].iloc[-5]) / hist['Close'].iloc[-5] * 100)
        
        # Create comprehensive market data
        market_data: Dict[str, Any] = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "price": {
                "current": float(hist['Close'].iloc[-1]),
                "open": float(hist['Open'].iloc[-1]),
                "high": float(hist['High'].iloc[-1]),
                "low": float(hist['Low'].iloc[-1]),
                "change_5d": price_change
            },
            "volume": {
                "current": int(current_volume),
                "average_20d": int(avg_volume),
                "ratio": volume_ratio
            },
            "indicators": {
                "sma_20": sma_20,
                "sma_50": sma_50,
                "rsi": rsi,
                "macd": macd,
                "macd_signal": signal,
                "bb_upper": bb_upper,
                "bb_middle": bb_middle,
                "bb_lower": bb_lower
            },
            "source": "yfinance"
        }
        
        logger.info(f"Successfully fetched market data for {symbol}")
        return market_data
        
    except Exception as e:
        logger.error(f"Error fetching market data for {symbol}: {e}")
        return None

def analyze_market_setup(market_data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze market data to identify trading setups"""
    try:
        symbol = market_data["symbol"]
        price = market_data["price"]["current"]
        rsi = market_data["indicators"]["rsi"]
        macd = market_data["indicators"]["macd"]
        macd_signal = market_data["indicators"]["macd_signal"]
        bb_upper = market_data["indicators"]["bb_upper"]
        bb_lower = market_data["indicators"]["bb_lower"]
        volume_ratio = market_data["volume"]["ratio"]
        price_change = market_data["price"]["change_5d"]
        
        # Initialize analysis
        setup_type = "neutral"
        confidence = 50.0
        signals: list[str] = []
        
        # RSI Analysis
        if rsi < 30:
            signals.append("RSI oversold (< 30)")
            confidence += 15
        elif rsi > 70:
            signals.append("RSI overbought (> 70)")
            confidence += 15
        
        # MACD Analysis
        if macd > macd_signal and macd > 0:
            signals.append("MACD bullish crossover above zero")
            confidence += 20
        elif macd < macd_signal and macd < 0:
            signals.append("MACD bearish crossover below zero")
            confidence += 20
        
        # Bollinger Bands Analysis
        if price <= bb_lower:
            signals.append("Price at or below lower Bollinger Band")
            confidence += 15
        elif price >= bb_upper:
            signals.append("Price at or above upper Bollinger Band")
            confidence += 15
        
        # Volume Analysis
        if volume_ratio > 1.5:
            signals.append("High volume (1.5x average)")
            confidence += 10
        elif volume_ratio < 0.5:
            signals.append("Low volume (0.5x average)")
            confidence += 5
        
        # Price Momentum
        if abs(price_change) > 5:
            signals.append(f"Strong price momentum ({price_change:.1f}% 5-day)")
            confidence += 10
        
        # Determine setup type
        if confidence >= 70:
            if rsi < 30 and macd > macd_signal:
                setup_type = "bullish_reversal"
            elif rsi > 70 and macd < macd_signal:
                setup_type = "bearish_reversal"
            elif price <= bb_lower and volume_ratio > 1.2:
                setup_type = "oversold_bounce"
            elif price >= bb_upper and volume_ratio > 1.2:
                setup_type = "overbought_pullback"
            elif abs(price_change) > 8:
                setup_type = "momentum_continuation"
        
        # Cap confidence at 100
        confidence = min(confidence, 100.0)
        
        analysis: Dict[str, Any] = {
            "symbol": symbol,
            "setup_type": setup_type,
            "confidence": confidence,
            "signals": signals,
            "timestamp": datetime.now().isoformat(),
            "price": price,
            "key_levels": {
                "resistance": bb_upper,
                "support": bb_lower,
                "sma_20": market_data["indicators"]["sma_20"],
                "sma_50": market_data["indicators"]["sma_50"]
            }
        }
        
        logger.info(f"Analysis for {symbol}: {setup_type} (confidence: {confidence:.1f}%)")
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing market setup for {market_data.get('symbol', 'unknown')}: {e}")
        return {
            "symbol": market_data.get("symbol", "unknown"),
            "setup_type": "error",
            "confidence": 0.0,
            "signals": [f"Analysis error: {str(e)}"],
            "timestamp": datetime.now().isoformat()
        }

@celery_app.task(bind=True)
def proactive_market_scan(self) -> Dict[str, Any]:
    """
    Periodically scans a list of symbols, generates signals, and sends high-confidence alerts.
    This is the real-time market scanning functionality.
    """
    logger.info("Starting proactive market scan...")
    
    # In a real application, this would come from a dynamic source (e.g., watchlist service)
    symbols_to_scan = DEFAULT_SCAN_SYMBOLS.split(",")
    alert_threshold = ALERT_THRESHOLD

    try:
        scan_results: list[Dict[str, Any]] = []
        high_confidence_signals: list[Dict[str, Any]] = []
        
        for symbol in symbols_to_scan:
            logger.info(f"Scanning {symbol}...")
            
            # Try Alpha Vantage first, fallback to Yahoo Finance
            market_data = fetch_real_market_data_alpha_vantage(symbol)
            if not market_data:
                market_data = fetch_real_market_data_yfinance(symbol)
            
            if market_data:
                # Analyze the market data
                analysis = analyze_market_setup(market_data)
                scan_results.append(analysis)
                
                # Check if this is a high-confidence signal
                if analysis.get("confidence", 0) >= alert_threshold:
                    high_confidence_signals.append(analysis)
                    logger.info(f"🚨 High-confidence signal for {symbol}: {analysis['setup_type']} (confidence: {analysis['confidence']:.1f}%)")
        
        # Log scan summary
        logger.info(f"Scan complete: {len(scan_results)} symbols analyzed, {len(high_confidence_signals)} high-confidence signals found")
        
        # Here you would add logic to:
        # 1. Cache the full results to Redis or Supabase
        # 2. Send Discord alerts for high-confidence signals
        # 3. Update internal database with latest market data
        
        return {
            "status": "success",
            "symbols_scanned": len(symbols_to_scan),
            "signals_found": len(scan_results),
            "high_confidence_signals": len(high_confidence_signals),
            "timestamp": datetime.now().isoformat(),
            "scan_results": scan_results
        }

    except Exception as e:
        logger.error(f"Proactive market scan failed: {e}")
        # Consider raising a retryable exception for Celery
        # raise self.retry(exc=e, countdown=300) # Retry in 5 minutes
        return {"status": "error", "error": str(e)}

@celery_app.task(bind=True)
def scan_single_symbol(self, symbol: str) -> Dict[str, Any]:
    """
    Scan a single symbol for trading opportunities
    """
    logger.info(f"Starting single symbol scan for {symbol}")
    
    try:
        # Try Alpha Vantage first, fallback to Yahoo Finance
        market_data = fetch_real_market_data_alpha_vantage(symbol)
        if not market_data:
            market_data = fetch_real_market_data_yfinance(symbol)
        
        if market_data:
            analysis = analyze_market_setup(market_data)
            return {
                "status": "success",
                "symbol": symbol,
                "analysis": analysis,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "status": "error",
                "symbol": symbol,
                "error": "Failed to fetch market data",
                "timestamp": datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Single symbol scan failed for {symbol}: {e}")
        return {"status": "error", "symbol": symbol, "error": str(e)}

@celery_app.task(bind=True)
def update_market_cache(self) -> Dict[str, Any]:
    """
    Update market data cache for frequently accessed symbols
    """
    logger.info("Starting market cache update...")
    
    # Cache frequently accessed symbols
    cache_symbols = DEFAULT_CACHE_SYMBOLS.split(",")
    
    try:
        updated_count = 0
        for symbol in cache_symbols:
            market_data = fetch_real_market_data_yfinance(symbol)
            if market_data:
                # Here you would store to Redis cache
                # redis_client.setex(f"market_data:{symbol}", 300, json.dumps(market_data))
                updated_count += 1
                logger.info(f"Cached market data for {symbol}")
        
        logger.info(f"Cache update complete: {updated_count}/{len(cache_symbols)} symbols updated")
        return {
            "status": "success",
            "symbols_updated": updated_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Market cache update failed: {e}")
        return {"status": "error", "error": str(e)}