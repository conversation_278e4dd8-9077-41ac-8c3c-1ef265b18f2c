"""
Multi-Source Sentiment Analysis Module

This module provides comprehensive sentiment analysis by integrating
multiple data sources including news, social media, options flow, and market data.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import json

logger = logging.getLogger(__name__)


class SentimentSource(Enum):
    """Sources of sentiment data."""
    
    NEWS = "news"
    SOCIAL_MEDIA = "social_media"
    OPTIONS_FLOW = "options_flow"
    INSTITUTIONAL = "institutional"
    MARKET_BREADTH = "market_breadth"
    TECHNICAL = "technical"


class SentimentType(Enum):
    """Types of sentiment analysis."""
    
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    MIXED = "mixed"


@dataclass
class SentimentData:
    """Individual sentiment data point."""
    
    source: SentimentSource
    sentiment_type: SentimentType
    confidence: float  # 0.0 to 1.0
    magnitude: float   # 0.0 to 1.0 (how strong the sentiment is)
    text: Optional[str] = None
    timestamp: datetime = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class SentimentAnalysis:
    """Comprehensive sentiment analysis result."""
    
    symbol: str
    overall_sentiment: float  # -100 to +100
    sentiment_breakdown: Dict[SentimentSource, SentimentData]
    sentiment_trends: Dict[str, float]
    contrarian_signals: List[str]
    trading_signals: List[str]
    confidence_score: float
    last_updated: datetime
    data_sources: List[SentimentSource]
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now()


class NewsSentimentAnalyzer:
    """Analyzes sentiment from financial news sources."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.positive_keywords = [
            "bullish", "surge", "rally", "jump", "climb", "gain", "rise", "soar",
            "strong", "positive", "beat", "exceed", "growth", "profit", "earnings",
            "upgrade", "buy", "outperform", "positive outlook", "strong fundamentals"
        ]
        self.negative_keywords = [
            "bearish", "plunge", "crash", "drop", "fall", "decline", "slump", "tumble",
            "weak", "negative", "miss", "disappoint", "loss", "decline", "downgrade",
            "sell", "underperform", "negative outlook", "weak fundamentals"
        ]
        self.neutral_keywords = [
            "stable", "steady", "maintain", "hold", "unchanged", "flat", "sideways",
            "consolidate", "range-bound", "mixed", "uncertain", "volatile"
        ]
    
    def analyze_news_sentiment(self, news_articles: List[Dict[str, Any]]) -> List[SentimentData]:
        """Analyze sentiment from news articles."""
        sentiment_data = []
        
        for article in news_articles:
            title = article.get("title", "")
            content = article.get("content", "")
            full_text = f"{title} {content}".lower()
            
            # Count keyword occurrences
            positive_count = sum(1 for keyword in self.positive_keywords if keyword in full_text)
            negative_count = sum(1 for keyword in self.negative_keywords if keyword in full_text)
            neutral_count = sum(1 for keyword in self.neutral_keywords if keyword in full_text)
            
            # Determine sentiment type and confidence
            if positive_count > negative_count and positive_count > neutral_count:
                sentiment_type = SentimentType.POSITIVE
                confidence = min(0.5 + (positive_count * 0.1), 1.0)
                magnitude = min(positive_count * 0.2, 1.0)
            elif negative_count > positive_count and negative_count > neutral_count:
                sentiment_type = SentimentType.NEGATIVE
                confidence = min(0.5 + (negative_count * 0.1), 1.0)
                magnitude = min(negative_count * 0.2, 1.0)
            elif neutral_count > positive_count and neutral_count > negative_count:
                sentiment_type = SentimentType.NEUTRAL
                confidence = min(0.5 + (neutral_count * 0.1), 1.0)
                magnitude = min(neutral_count * 0.1, 1.0)
            else:
                sentiment_type = SentimentType.MIXED
                confidence = 0.6
                magnitude = 0.5
            
            sentiment_data.append(SentimentData(
                source=SentimentSource.NEWS,
                sentiment_type=sentiment_type,
                confidence=confidence,
                magnitude=magnitude,
                text=title[:200] + "..." if len(title) > 200 else title,
                timestamp=datetime.fromisoformat(article.get("published_at", datetime.now().isoformat())),
                metadata={
                    "source": article.get("source", "unknown"),
                    "url": article.get("url", ""),
                    "positive_keywords": positive_count,
                    "negative_keywords": negative_count,
                    "neutral_keywords": neutral_count
                }
            ))
        
        return sentiment_data


class SocialMediaSentimentAnalyzer:
    """Analyzes sentiment from social media platforms."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.reddit_keywords = {
            "positive": ["moon", "rocket", "diamond hands", "hodl", "bullish", "to the moon"],
            "negative": ["dump", "crash", "bearish", "paper hands", "sell", "short"],
            "neutral": ["discussion", "analysis", "news", "update", "question"]
        }
        self.twitter_keywords = {
            "positive": ["bullish", "buy", "long", "strong", "growth", "profit"],
            "negative": ["bearish", "sell", "short", "weak", "decline", "loss"],
            "neutral": ["analysis", "news", "update", "discussion", "market"]
        }
    
    def analyze_reddit_sentiment(self, reddit_posts: List[Dict[str, Any]]) -> List[SentimentData]:
        """Analyze sentiment from Reddit posts and comments."""
        sentiment_data = []
        
        for post in reddit_posts:
            title = post.get("title", "").lower()
            content = post.get("content", "").lower()
            score = post.get("score", 0)
            comments = post.get("comments", [])
            
            # Analyze title and content
            positive_count = sum(1 for keyword in self.reddit_keywords["positive"] if keyword in title or keyword in content)
            negative_count = sum(1 for keyword in self.reddit_keywords["negative"] if keyword in title or keyword in content)
            neutral_count = sum(1 for keyword in self.reddit_keywords["neutral"] if keyword in title or keyword in content)
            
            # Consider post score and comment sentiment
            score_sentiment = 0.5 + (score / 1000)  # Normalize score to 0-1 range
            
            # Determine overall sentiment
            if positive_count > negative_count:
                sentiment_type = SentimentType.POSITIVE
                confidence = min(0.6 + (positive_count * 0.1) + (score_sentiment * 0.2), 1.0)
                magnitude = min(positive_count * 0.3, 1.0)
            elif negative_count > positive_count:
                sentiment_type = SentimentType.NEGATIVE
                confidence = min(0.6 + (negative_count * 0.1) + (score_sentiment * 0.2), 1.0)
                magnitude = min(negative_count * 0.3, 1.0)
            else:
                sentiment_type = SentimentType.NEUTRAL
                confidence = 0.6 + (score_sentiment * 0.2)
                magnitude = 0.5
            
            sentiment_data.append(SentimentData(
                source=SentimentSource.SOCIAL_MEDIA,
                sentiment_type=sentiment_type,
                confidence=confidence,
                magnitude=magnitude,
                text=title[:200] + "..." if len(title) > 200 else title,
                timestamp=datetime.fromtimestamp(post.get("created_utc", datetime.now().timestamp())),
                metadata={
                    "platform": "reddit",
                    "subreddit": post.get("subreddit", ""),
                    "score": score,
                    "comments_count": len(comments),
                    "positive_keywords": positive_count,
                    "negative_keywords": negative_count,
                    "neutral_keywords": neutral_count
                }
            ))
        
        return sentiment_data
    
    def analyze_twitter_sentiment(self, tweets: List[Dict[str, Any]]) -> List[SentimentData]:
        """Analyze sentiment from Twitter posts."""
        sentiment_data = []
        
        for tweet in tweets:
            text = tweet.get("text", "").lower()
            likes = tweet.get("likes", 0)
            retweets = tweet.get("retweets", 0)
            replies = tweet.get("replies", 0)
            
            # Analyze tweet content
            positive_count = sum(1 for keyword in self.twitter_keywords["positive"] if keyword in text)
            negative_count = sum(1 for keyword in self.twitter_keywords["negative"] if keyword in text)
            neutral_count = sum(1 for keyword in self.twitter_keywords["neutral"] if keyword in text)
            
            # Consider engagement metrics
            engagement_score = (likes + retweets * 2 + replies * 3) / 1000  # Normalize engagement
            
            # Determine sentiment
            if positive_count > negative_count:
                sentiment_type = SentimentType.POSITIVE
                confidence = min(0.6 + (positive_count * 0.1) + (engagement_score * 0.2), 1.0)
                magnitude = min(positive_count * 0.3, 1.0)
            elif negative_count > positive_count:
                sentiment_type = SentimentType.NEGATIVE
                confidence = min(0.6 + (negative_count * 0.1) + (engagement_score * 0.2), 1.0)
                magnitude = min(negative_count * 0.3, 1.0)
            else:
                sentiment_type = SentimentType.NEUTRAL
                confidence = 0.6 + (engagement_score * 0.2)
                magnitude = 0.5
            
            sentiment_data.append(SentimentData(
                source=SentimentSource.SOCIAL_MEDIA,
                sentiment_type=sentiment_type,
                confidence=confidence,
                magnitude=magnitude,
                text=text[:200] + "..." if len(text) > 200 else text,
                timestamp=datetime.fromisoformat(tweet.get("created_at", datetime.now().isoformat())),
                metadata={
                    "platform": "twitter",
                    "likes": likes,
                    "retweets": retweets,
                    "replies": replies,
                    "engagement_score": engagement_score,
                    "positive_keywords": positive_count,
                    "negative_keywords": negative_count,
                    "neutral_keywords": neutral_count
                }
            ))
        
        return sentiment_data


class OptionsFlowSentimentAnalyzer:
    """Analyzes sentiment from options flow data."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_options_sentiment(self, options_data: Dict[str, Any]) -> SentimentData:
        """Analyze sentiment from options flow data."""
        put_volume = options_data.get("put_volume", 0)
        call_volume = options_data.get("call_volume", 0)
        put_call_ratio = options_data.get("put_call_ratio", 1.0)
        unusual_activity = options_data.get("unusual_activity", [])
        
        # Calculate sentiment based on put/call ratio
        if put_call_ratio > 1.5:  # High put activity (bearish)
            sentiment_type = SentimentType.NEGATIVE
            confidence = min(0.7 + (put_call_ratio - 1.5) * 0.2, 1.0)
            magnitude = min((put_call_ratio - 1.0) * 0.5, 1.0)
        elif put_call_ratio < 0.7:  # High call activity (bullish)
            sentiment_type = SentimentType.POSITIVE
            confidence = min(0.7 + (1.0 - put_call_ratio) * 0.2, 1.0)
            magnitude = min((1.0 - put_call_ratio) * 0.5, 1.0)
        else:  # Balanced activity
            sentiment_type = SentimentType.NEUTRAL
            confidence = 0.6
            magnitude = 0.5
        
        # Adjust for unusual activity
        if unusual_activity:
            unusual_bullish = sum(1 for activity in unusual_activity if activity.get("type") == "call" and activity.get("size", 0) > 100)
            unusual_bearish = sum(1 for activity in unusual_activity if activity.get("type") == "put" and activity.get("size", 0) > 100)
            
            if unusual_bullish > unusual_bearish:
                sentiment_type = SentimentType.POSITIVE
                confidence = min(confidence + 0.1, 1.0)
                magnitude = min(magnitude + 0.2, 1.0)
            elif unusual_bearish > unusual_bullish:
                sentiment_type = SentimentType.NEGATIVE
                confidence = min(confidence + 0.1, 1.0)
                magnitude = min(magnitude + 0.2, 1.0)
        
        return SentimentData(
            source=SentimentSource.OPTIONS_FLOW,
            sentiment_type=sentiment_type,
            confidence=confidence,
            magnitude=magnitude,
            text=f"Put/Call Ratio: {put_call_ratio:.2f}, Unusual Activity: {len(unusual_activity)}",
            timestamp=datetime.now(),
            metadata={
                "put_volume": put_volume,
                "call_volume": call_volume,
                "put_call_ratio": put_call_ratio,
                "unusual_activity_count": len(unusual_activity),
                "total_volume": put_volume + call_volume
            }
        )


class MarketBreadthSentimentAnalyzer:
    """Analyzes sentiment from market breadth indicators."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_market_breadth(self, breadth_data: Dict[str, Any]) -> SentimentData:
        """Analyze sentiment from market breadth data."""
        advancing = breadth_data.get("advancing", 0)
        declining = breadth_data.get("declining", 0)
        unchanged = breadth_data.get("unchanged", 0)
        new_highs = breadth_data.get("new_highs", 0)
        new_lows = breadth_data.get("new_lows", 0)
        
        total_stocks = advancing + declining + unchanged
        
        if total_stocks == 0:
            return SentimentData(
                source=SentimentSource.MARKET_BREADTH,
                sentiment_type=SentimentType.NEUTRAL,
                confidence=0.5,
                magnitude=0.5,
                text="Insufficient market breadth data",
                timestamp=datetime.now()
            )
        
        # Calculate breadth ratios
        advance_decline_ratio = advancing / declining if declining > 0 else float('inf')
        new_highs_lows_ratio = new_highs / new_lows if new_lows > 0 else float('inf')
        
        # Determine sentiment based on breadth
        if advance_decline_ratio > 2.0 and new_highs_lows_ratio > 3.0:
            sentiment_type = SentimentType.POSITIVE
            confidence = min(0.8 + (advance_decline_ratio - 2.0) * 0.1, 1.0)
            magnitude = min((advance_decline_ratio - 1.0) * 0.3, 1.0)
        elif advance_decline_ratio < 0.5 and new_highs_lows_ratio < 0.33:
            sentiment_type = SentimentType.NEGATIVE
            confidence = min(0.8 + (1.0 - advance_decline_ratio) * 0.1, 1.0)
            magnitude = min((1.0 - advance_decline_ratio) * 0.3, 1.0)
        else:
            sentiment_type = SentimentType.NEUTRAL
            confidence = 0.6
            magnitude = 0.5
        
        return SentimentData(
            source=SentimentSource.MARKET_BREADTH,
            sentiment_type=sentiment_type,
            confidence=confidence,
            magnitude=magnitude,
            text=f"Advance/Decline: {advance_decline_ratio:.2f}, New Highs/Lows: {new_highs_lows_ratio:.2f}",
            timestamp=datetime.now(),
            metadata={
                "advancing": advancing,
                "declining": declining,
                "unchanged": unchanged,
                "new_highs": new_highs,
                "new_lows": new_lows,
                "advance_decline_ratio": advance_decline_ratio,
                "new_highs_lows_ratio": new_highs_lows_ratio,
                "total_stocks": total_stocks
            }
        )


class MultiSourceSentimentAnalyzer:
    """Main sentiment analyzer that combines multiple data sources."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.news_analyzer = NewsSentimentAnalyzer()
        self.social_analyzer = SocialMediaSentimentAnalyzer()
        self.options_analyzer = OptionsFlowSentimentAnalyzer()
        self.breadth_analyzer = MarketBreadthSentimentAnalyzer()
        self.logger.info("MultiSourceSentimentAnalyzer initialized")
    
    def analyze_sentiment(self, symbol: str, data_sources: Dict[str, Any]) -> SentimentAnalysis:
        """Analyze sentiment from multiple sources."""
        sentiment_data = {}
        
        # Analyze news sentiment
        if "news" in data_sources:
            try:
                news_sentiments = self.news_analyzer.analyze_news_sentiment(data_sources["news"])
                if news_sentiments:
                    sentiment_data[SentimentSource.NEWS] = news_sentiments[0]  # Take first for now
            except Exception as e:
                self.logger.error(f"Error analyzing news sentiment: {e}")
        
        # Analyze social media sentiment
        if "social_media" in data_sources:
            try:
                reddit_sentiments = self.social_analyzer.analyze_reddit_sentiment(
                    data_sources["social_media"].get("reddit", [])
                )
                twitter_sentiments = self.social_analyzer.analyze_twitter_sentiment(
                    data_sources["social_media"].get("twitter", [])
                )
                
                # Combine social media sentiments
                all_social = reddit_sentiments + twitter_sentiments
                if all_social:
                    # Calculate weighted average
                    total_weight = sum(sent.confidence for sent in all_social)
                    if total_weight > 0:
                        weighted_sentiment = sum(
                            sent.confidence * (1.0 if sent.sentiment_type == SentimentType.POSITIVE else -1.0 if sent.sentiment_type == SentimentType.NEGATIVE else 0.0)
                            for sent in all_social
                        ) / total_weight
                        
                        sentiment_type = SentimentType.POSITIVE if weighted_sentiment > 0.1 else SentimentType.NEGATIVE if weighted_sentiment < -0.1 else SentimentType.NEUTRAL
                        confidence = sum(sent.confidence for sent in all_social) / len(all_social)
                        magnitude = sum(sent.magnitude for sent in all_social) / len(all_social)
                        
                        sentiment_data[SentimentSource.SOCIAL_MEDIA] = SentimentData(
                            source=SentimentSource.SOCIAL_MEDIA,
                            sentiment_type=sentiment_type,
                            confidence=confidence,
                            magnitude=magnitude,
                            text=f"Combined social media sentiment from {len(all_social)} sources",
                            timestamp=datetime.now(),
                            metadata={"source_count": len(all_social), "weighted_sentiment": weighted_sentiment}
                        )
            except Exception as e:
                self.logger.error(f"Error analyzing social media sentiment: {e}")
        
        # Analyze options flow sentiment
        if "options_flow" in data_sources:
            try:
                options_sentiment = self.options_analyzer.analyze_options_sentiment(data_sources["options_flow"])
                sentiment_data[SentimentSource.OPTIONS_FLOW] = options_sentiment
            except Exception as e:
                self.logger.error(f"Error analyzing options sentiment: {e}")
        
        # Analyze market breadth sentiment
        if "market_breadth" in data_sources:
            try:
                breadth_sentiment = self.breadth_analyzer.analyze_market_breadth(data_sources["market_breadth"])
                sentiment_data[SentimentSource.MARKET_BREADTH] = breadth_sentiment
            except Exception as e:
                self.logger.error(f"Error analyzing market breadth sentiment: {e}")
        
        # Calculate overall sentiment
        overall_sentiment = self._calculate_overall_sentiment(sentiment_data)
        
        # Generate sentiment trends and signals
        sentiment_trends = self._calculate_sentiment_trends(sentiment_data)
        contrarian_signals = self._identify_contrarian_signals(sentiment_data)
        trading_signals = self._generate_trading_signals(sentiment_data, overall_sentiment)
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(sentiment_data)
        
        return SentimentAnalysis(
            symbol=symbol,
            overall_sentiment=overall_sentiment,
            sentiment_breakdown=sentiment_data,
            sentiment_trends=sentiment_trends,
            contrarian_signals=contrarian_signals,
            trading_signals=trading_signals,
            confidence_score=confidence_score,
            last_updated=datetime.now(),
            data_sources=list(sentiment_data.keys())
        )
    
    def _calculate_overall_sentiment(self, sentiment_data: Dict[SentimentSource, SentimentData]) -> float:
        """Calculate overall sentiment score from -100 to +100."""
        if not sentiment_data:
            return 0.0
        
        total_weight = 0.0
        weighted_sentiment = 0.0
        
        # Weight different sources
        source_weights = {
            SentimentSource.NEWS: 0.3,
            SentimentSource.SOCIAL_MEDIA: 0.25,
            SentimentSource.OPTIONS_FLOW: 0.25,
            SentimentSource.MARKET_BREADTH: 0.2
        }
        
        for source, data in sentiment_data.items():
            weight = source_weights.get(source, 0.1) * data.confidence
            total_weight += weight
            
            # Convert sentiment type to numeric value
            if data.sentiment_type == SentimentType.POSITIVE:
                sentiment_value = data.magnitude * 100
            elif data.sentiment_type == SentimentType.NEGATIVE:
                sentiment_value = -data.magnitude * 100
            else:
                sentiment_value = 0
            
            weighted_sentiment += sentiment_value * weight
        
        if total_weight == 0:
            return 0.0
        
        return weighted_sentiment / total_weight
    
    def _calculate_sentiment_trends(self, sentiment_data: Dict[SentimentSource, SentimentData]) -> Dict[str, float]:
        """Calculate sentiment trends across different dimensions."""
        trends = {}
        
        # Calculate trend by source
        for source, data in sentiment_data.items():
            if data.metadata and "weighted_sentiment" in data.metadata:
                trends[f"{source.value}_trend"] = data.metadata["weighted_sentiment"]
        
        # Calculate overall trend strength
        sentiment_values = []
        for data in sentiment_data.values():
            if data.sentiment_type == SentimentType.POSITIVE:
                sentiment_values.append(data.magnitude)
            elif data.sentiment_type == SentimentType.NEGATIVE:
                sentiment_values.append(-data.magnitude)
            else:
                sentiment_values.append(0)
        
        if sentiment_values:
            trends["overall_trend_strength"] = sum(sentiment_values) / len(sentiment_values)
        
        return trends
    
    def _identify_contrarian_signals(self, sentiment_data: Dict[SentimentSource, SentimentData]) -> List[str]:
        """Identify contrarian trading signals."""
        signals = []
        
        # Check for extreme sentiment (potential reversal)
        for source, data in sentiment_data.items():
            if data.magnitude > 0.8:  # Very strong sentiment
                if data.sentiment_type == SentimentType.POSITIVE:
                    signals.append(f"Extreme bullish sentiment in {source.value} - potential reversal signal")
                elif data.sentiment_type == SentimentType.NEGATIVE:
                    signals.append(f"Extreme bearish sentiment in {source.value} - potential reversal signal")
        
        # Check for sentiment divergence
        positive_sources = [s for s, d in sentiment_data.items() if d.sentiment_type == SentimentType.POSITIVE]
        negative_sources = [s for s, d in sentiment_data.items() if d.sentiment_type == SentimentType.NEGATIVE]
        
        if positive_sources and negative_sources:
            signals.append(f"Mixed sentiment across sources - {len(positive_sources)} bullish vs {len(negative_sources)} bearish")
        
        return signals
    
    def _generate_trading_signals(self, sentiment_data: Dict[SentimentSource, SentimentData], 
                                overall_sentiment: float) -> List[str]:
        """Generate actionable trading signals based on sentiment."""
        signals = []
        
        # Strong positive sentiment
        if overall_sentiment > 50:
            signals.append("Strong bullish sentiment - consider long positions with tight stops")
        elif overall_sentiment > 20:
            signals.append("Moderate bullish sentiment - favorable for long positions")
        
        # Strong negative sentiment
        if overall_sentiment < -50:
            signals.append("Strong bearish sentiment - consider short positions with tight stops")
        elif overall_sentiment < -20:
            signals.append("Moderate bearish sentiment - favorable for short positions")
        
        # Neutral sentiment
        if -20 <= overall_sentiment <= 20:
            signals.append("Neutral sentiment - range-bound trading or wait for clearer signals")
        
        # Options flow signals
        if SentimentSource.OPTIONS_FLOW in sentiment_data:
            options_data = sentiment_data[SentimentSource.OPTIONS_FLOW]
            if options_data.sentiment_type == SentimentType.POSITIVE and options_data.magnitude > 0.7:
                signals.append("Strong options call flow - bullish momentum building")
            elif options_data.sentiment_type == SentimentType.NEGATIVE and options_data.magnitude > 0.7:
                signals.append("Strong options put flow - bearish momentum building")
        
        return signals
    
    def _calculate_confidence_score(self, sentiment_data: Dict[SentimentSource, SentimentData]) -> float:
        """Calculate overall confidence score for the sentiment analysis."""
        if not sentiment_data:
            return 0.0
        
        # Weight confidence by source importance
        source_weights = {
            SentimentSource.NEWS: 0.3,
            SentimentSource.SOCIAL_MEDIA: 0.25,
            SentimentSource.OPTIONS_FLOW: 0.25,
            SentimentSource.MARKET_BREADTH: 0.2
        }
        
        total_weight = 0.0
        weighted_confidence = 0.0
        
        for source, data in sentiment_data.items():
            weight = source_weights.get(source, 0.1)
            total_weight += weight
            weighted_confidence += data.confidence * weight
        
        if total_weight == 0:
            return 0.0
        
        return weighted_confidence / total_weight 