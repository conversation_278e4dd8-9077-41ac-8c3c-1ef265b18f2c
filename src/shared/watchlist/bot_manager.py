"""
Bot-specific watchlist manager implementation
"""

import logging
from typing import Optional

from .base_manager import BaseWatchlistManager

# Metrics tracking (simplified without prometheus dependency)
class MetricsTracker:
    def __init__(self):
        self.operations = {}
        self.durations = []
    
    def inc_operation(self, operation: str, status: str):
        key = f"{operation}_{status}"
        self.operations[key] = self.operations.get(key, 0) + 1
        
    def observe_duration(self, duration: float):
        self.durations.append(duration)

metrics = MetricsTracker()

class BotWatchlistManager(BaseWatchlistManager):
    """
    Bot-specific implementation of watchlist manager
    Uses standard logging and simplified metrics
    """
    
    def __init__(self, db_pool):
        super().__init__(db_pool)
        self.logger = logging.getLogger(__name__)
    
    def log_info(self, message: str, **kwargs):
        """Log information using standard logging"""
        self.logger.info(message)
        
    def log_error(self, message: str, **kwargs):
        """Log error using standard logging"""
        self.logger.error(message)
        
    def record_metric(self, operation: str, status: str, duration: Optional[float] = None):
        """Record metrics using simplified metrics tracker"""
        if status == 'duration' and duration is not None:
            metrics.observe_duration(duration)
        else:
            metrics.inc_operation(operation, status)
