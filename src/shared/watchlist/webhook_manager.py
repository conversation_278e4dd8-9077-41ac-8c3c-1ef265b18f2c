"""
Webhook-specific watchlist manager implementation
"""

import structlog
from typing import Optional
from prometheus_client import Counter, Histogram

from .base_manager import BaseWatchlistManager

# Prometheus metrics
watchlist_operations_total = Counter('watchlist_operations_total', 'Total watchlist operations', ['operation', 'status'])
watchlist_operation_duration = Histogram('watchlist_operation_duration_seconds', 'Watchlist operation duration')

class WebhookWatchlistManager(BaseWatchlistManager):
    """
    Webhook-specific implementation of watchlist manager
    Uses structlog and Prometheus metrics
    """
    
    def __init__(self, db_pool):
        super().__init__(db_pool)
        self.logger = structlog.get_logger()
    
    def log_info(self, message: str, **kwargs):
        """Log information using structlog"""
        self.logger.info(message, **kwargs)
        
    def log_error(self, message: str, **kwargs):
        """Log error using structlog"""
        self.logger.error(message, **kwargs)
        
    def record_metric(self, operation: str, status: str, duration: Optional[float] = None):
        """Record metrics using Prometheus"""
        if status == 'duration' and duration is not None:
            watchlist_operation_duration.observe(duration)
        else:
            watchlist_operations_total.labels(operation=operation, status=status).inc()
