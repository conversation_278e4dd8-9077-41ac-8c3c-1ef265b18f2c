"""
Base watchlist manager for TradingView Automation
"""

import time
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional

from .models import WatchlistSymbol, UserWatchlist

class BaseWatchlistManager(ABC):
    """
    Abstract base class for watchlist management
    Provides common functionality while allowing for different implementations
    """
    
    def __init__(self, db_pool):
        """Initialize with database pool"""
        if db_pool is None:
            raise ValueError("Database pool cannot be None")
        self.db_pool = db_pool
        self._initialized = True
    
    def _check_initialization(self):
        """Check if manager is properly initialized"""
        if not hasattr(self, '_initialized') or not self._initialized:
            raise RuntimeError("WatchlistManager not properly initialized")
        if self.db_pool is None:
            raise RuntimeError("Database pool is None")
    
    @abstractmethod
    def log_info(self, message: str, **kwargs):
        """Log information - to be implemented by subclasses"""
        pass
        
    @abstractmethod
    def log_error(self, message: str, **kwargs):
        """Log error - to be implemented by subclasses"""
        pass
        
    @abstractmethod
    def record_metric(self, operation: str, status: str, duration: Optional[float] = None):
        """Record metrics - to be implemented by subclasses"""
        pass
    
    async def create_watchlist(self, discord_user_id: str, watchlist_name: str = "Default") -> Optional[int]:
        """Create a new watchlist for a user"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                result = await conn.fetchrow("""
                    INSERT INTO user_watchlists (discord_user_id, watchlist_name)
                    VALUES ($1, $2)
                    RETURNING id
                """, discord_user_id, watchlist_name)
                
                if result:
                    watchlist_id = result['id']
                    self.record_metric('create', 'success')
                    self.log_info(f"Created watchlist {watchlist_name} for user {discord_user_id} (ID: {watchlist_id})")
                    return watchlist_id
                else:
                    self.record_metric('create', 'error')
                    self.log_error(f"Failed to create watchlist for user {discord_user_id}")
                    return None
                    
        except Exception as e:
            self.record_metric('create', 'error')
            self.log_error(f"Failed to create watchlist for user {discord_user_id}: {str(e)}")
            return None
        finally:
            duration = time.time() - start_time
            self.record_metric('create', 'duration', duration)
    
    async def get_user_watchlists(self, discord_user_id: str) -> List[UserWatchlist]:
        """Get all watchlists for a user"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                # Get watchlists
                watchlist_rows = await conn.fetch("""
                    SELECT id, watchlist_name, is_active, created_at, updated_at
                    FROM user_watchlists
                    WHERE discord_user_id = $1 AND is_active = TRUE
                    ORDER BY created_at DESC
                """, discord_user_id)
                
                watchlists = []
                for row in watchlist_rows:
                    # Get symbols for this watchlist
                    symbol_rows = await conn.fetch("""
                        SELECT id, symbol, notes, alert_threshold, alert_type, is_active, added_at
                        FROM watchlist_symbols
                        WHERE watchlist_id = $1 AND is_active = TRUE
                        ORDER BY added_at ASC
                    """, row['id'])
                    
                    symbols = [
                        WatchlistSymbol(
                            id=s['id'],
                            symbol=s['symbol'],
                            notes=s['notes'],
                            alert_threshold=s['alert_threshold'],
                            alert_type=s['alert_type'],
                            is_active=s['is_active'],
                            added_at=s['added_at'].timestamp() if s['added_at'] else time.time()
                        )
                        for s in symbol_rows
                    ]
                    
                    watchlist = UserWatchlist(
                        id=row['id'],
                        discord_user_id=discord_user_id,
                        watchlist_name=row['watchlist_name'],
                        is_active=row['is_active'],
                        created_at=row['created_at'].timestamp() if row['created_at'] else time.time(),
                        updated_at=row['updated_at'].timestamp() if row['updated_at'] else time.time(),
                        symbols=symbols
                    )
                    watchlists.append(watchlist)
                
                self.record_metric('get', 'success')
                self.log_info(f"Retrieved {len(watchlists)} watchlists for user {discord_user_id}")
                return watchlists
                
        except Exception as e:
            self.record_metric('get', 'error')
            self.log_error(f"Failed to get watchlists for user {discord_user_id}: {str(e)}")
            return []
        finally:
            duration = time.time() - start_time
            self.record_metric('get', 'duration', duration)
    
    async def add_symbol_to_watchlist(self, watchlist_id: int, symbol: str, notes: Optional[str] = None, 
                                     alert_threshold: Optional[float] = None, alert_type: str = "price_change") -> bool:
        """Add a symbol to a watchlist"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO watchlist_symbols (watchlist_id, symbol, notes, alert_threshold, alert_type)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (watchlist_id, symbol) DO UPDATE SET
                        notes = EXCLUDED.notes,
                        alert_threshold = EXCLUDED.alert_threshold,
                        alert_type = EXCLUDED.alert_type,
                        is_active = TRUE
                """, watchlist_id, symbol, notes, alert_threshold, alert_type)
                
                self.record_metric('add_symbol', 'success')
                self.log_info(f"Added symbol {symbol} to watchlist {watchlist_id}")
                return True
                
        except Exception as e:
            self.record_metric('add_symbol', 'error')
            self.log_error(f"Failed to add symbol {symbol} to watchlist {watchlist_id}: {str(e)}")
            return False
        finally:
            duration = time.time() - start_time
            self.record_metric('add_symbol', 'duration', duration)
    
    async def remove_symbol_from_watchlist(self, watchlist_id: int, symbol: str) -> bool:
        """Remove a symbol from a watchlist"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE watchlist_symbols 
                    SET is_active = FALSE 
                    WHERE watchlist_id = $1 AND symbol = $2
                """, watchlist_id, symbol)
                
                self.record_metric('remove_symbol', 'success')
                self.log_info(f"Removed symbol {symbol} from watchlist {watchlist_id}")
                return True
                
        except Exception as e:
            self.record_metric('remove_symbol', 'error')
            self.log_error(f"Failed to remove symbol {symbol} from watchlist {watchlist_id}: {str(e)}")
            return False
        finally:
            duration = time.time() - start_time
            self.record_metric('remove_symbol', 'duration', duration)
    
    async def delete_watchlist(self, watchlist_id: int) -> bool:
        """Delete a watchlist (soft delete)"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE user_watchlists 
                    SET is_active = FALSE 
                    WHERE id = $1
                """, watchlist_id)
                
                self.record_metric('delete', 'success')
                self.log_info(f"Deleted watchlist {watchlist_id}")
                return True
                
        except Exception as e:
            self.record_metric('delete', 'error')
            self.log_error(f"Failed to delete watchlist {watchlist_id}: {str(e)}")
            return False
        finally:
            duration = time.time() - start_time
            self.record_metric('delete', 'duration', duration)
    
    async def get_watchlist_summary(self, discord_user_id: str) -> Dict[str, Any]:
        """Get a summary of user's watchlists"""
        try:
            watchlists = await self.get_user_watchlists(discord_user_id)
            
            total_symbols = sum(len(w.symbols) for w in watchlists)
            active_watchlists = len(watchlists)
            
            # Get most watched symbols
            symbol_counts = {}
            for watchlist in watchlists:
                for symbol in watchlist.symbols:
                    symbol_counts[symbol.symbol] = symbol_counts.get(symbol.symbol, 0) + 1
            
            top_symbols = sorted(symbol_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            return {
                'total_watchlists': active_watchlists,
                'total_symbols': total_symbols,
                'top_symbols': [{'symbol': s[0], 'count': s[1]} for s in top_symbols],
                'watchlists': [
                    {
                        'name': w.watchlist_name,
                        'symbol_count': len(w.symbols),
                        'created': w.created_at
                    }
                    for w in watchlists
                ]
            }
            
        except Exception as e:
            self.log_error(f"Failed to get watchlist summary for user {discord_user_id}: {str(e)}")
            return {}
            
    async def get_all_users(self) -> List[str]:
        """Get all users with watchlists"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT DISTINCT discord_user_id
                    FROM user_watchlists
                    WHERE is_active = TRUE
                """)
                
                user_ids = [row['discord_user_id'] for row in rows]
                
                self.record_metric('get_all_users', 'success')
                self.log_info(f"Retrieved {len(user_ids)} users with watchlists")
                return user_ids
                
        except Exception as e:
            self.record_metric('get_all_users', 'error')
            self.log_error(f"Failed to get all users: {str(e)}")
            return []
        finally:
            duration = time.time() - start_time
            self.record_metric('get_all_users', 'duration', duration)
    
    async def update_symbol_notes(self, watchlist_id: int, symbol: str, notes: str) -> bool:
        """Update notes for a symbol in a watchlist"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE watchlist_symbols
                    SET notes = $3
                    WHERE watchlist_id = $1 AND symbol = $2
                """, watchlist_id, symbol, notes)
                
                self.record_metric('update_notes', 'success')
                self.log_info(f"Updated notes for symbol {symbol} in watchlist {watchlist_id}")
                return True
                
        except Exception as e:
            self.record_metric('update_notes', 'error')
            self.log_error(f"Failed to update notes for symbol {symbol} in watchlist {watchlist_id}: {str(e)}")
            return False
        finally:
            duration = time.time() - start_time
            self.record_metric('update_notes', 'duration', duration)
    
    async def update_symbol_alerts(self, watchlist_id: int, symbol: str, 
                                  alert_threshold: float, alert_type: str) -> bool:
        """Update alert settings for a symbol in a watchlist"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE watchlist_symbols
                    SET alert_threshold = $3, alert_type = $4
                    WHERE watchlist_id = $1 AND symbol = $2
                """, watchlist_id, symbol, alert_threshold, alert_type)
                
                self.record_metric('update_alerts', 'success')
                self.log_info(f"Updated alerts for symbol {symbol} in watchlist {watchlist_id}")
                return True
                
        except Exception as e:
            self.record_metric('update_alerts', 'error')
            self.log_error(f"Failed to update alerts for symbol {symbol} in watchlist {watchlist_id}: {str(e)}")
            return False
        finally:
            duration = time.time() - start_time
            self.record_metric('update_alerts', 'duration', duration)
    
    async def get_user_alert_preferences(self, discord_user_id: str) -> Dict[str, Dict[str, Any]]:
        """Get alert preferences for all symbols for a user"""
        start_time = time.time()
        
        try:
            # Get all watchlists for the user
            watchlists = await self.get_user_watchlists(discord_user_id)
            
            # Collect alert preferences for all symbols
            alert_preferences = {}
            
            for watchlist in watchlists:
                for symbol in watchlist.symbols:
                    if symbol.symbol not in alert_preferences:
                        alert_preferences[symbol.symbol] = {
                            'alert_threshold': symbol.alert_threshold,
                            'alert_type': symbol.alert_type,
                            'notes': symbol.notes
                        }
            
            self.record_metric('get_alert_prefs', 'success')
            return alert_preferences
            
        except Exception as e:
            self.record_metric('get_alert_prefs', 'error')
            self.log_error(f"Failed to get alert preferences for user {discord_user_id}: {str(e)}")
            return {}
        finally:
            duration = time.time() - start_time
            self.record_metric('get_alert_prefs', 'duration', duration)
