"""
Shared watchlist models for TradingView Automation
"""

import time
from typing import List, Optional
from dataclasses import dataclass

@dataclass
class WatchlistSymbol:
    """Represents a symbol in a user's watchlist"""
    id: int
    symbol: str
    notes: Optional[str]
    alert_threshold: Optional[float]
    alert_type: str
    is_active: bool
    added_at: float

@dataclass
class UserWatchlist:
    """Represents a user's watchlist"""
    id: int
    discord_user_id: str
    watchlist_name: str
    is_active: bool
    created_at: float
    updated_at: float
    symbols: List[WatchlistSymbol]
