"""
Alpaca Market Data Provider

Provides high-quality real-time and historical market data from Alpaca.
Excellent for US stocks, options, crypto, and forex data.
Updated to use the unified base class for consistency.
"""

import os
import logging
import httpx
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional

from .unified_base import (
    UnifiedDataProvider, 
    ProviderType, 
    MarketDataResponse, 
    HistoricalData,
    ProviderError,
    ProviderTimeoutError
)

logger = logging.getLogger(__name__)


class AlpacaProvider(UnifiedDataProvider):
    """
    Provider for fetching market data from Alpaca API.
    
    Advantages:
    - High-quality real-time data
    - Generous free tier (1000 API calls/month)
    - Covers stocks, options, crypto, forex
    - Reliable and professional-grade
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        # Alpaca API configuration
        api_key = os.getenv("ALPACA_API_KEY", "")
        api_secret = os.getenv("ALPACA_API_SECRET", "")
        base_url = os.getenv("ALPACA_API_URL", "https://data.alpaca.markets/v2")
        
        # Check if configured
        is_configured = bool(api_key and api_secret)
        
        if not is_configured:
            logger.warning("Alpaca provider not configured: missing API credentials")
            self.is_available = False
            self.status = ProviderStatus.UNAVAILABLE
        else:
            logger.info("Alpaca provider initialized successfully")
        
        # Initialize unified base class
        super().__init__(
            provider_name="alpaca",
            provider_type=ProviderType.STOCK_DATA,
            config=config or {}
        )
        
        # Store API configuration
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = base_url
        self.is_configured = is_configured
    
    async def get_current_price(self, symbol: str) -> MarketDataResponse:
        """Get current price for a symbol (required by UnifiedDataProvider)."""
        if not self.is_configured:
            raise ProviderError("Alpaca provider not configured", self.provider_name)
        
        try:
            # Use the existing get_ticker method
            ticker_data = await self.get_ticker(symbol)
            
            if "error" in ticker_data:
                raise ProviderError(ticker_data["error"], self.provider_name)
            
            # Extract price data
            price = ticker_data.get("price", 0.0)
            timestamp = ticker_data.get("timestamp", datetime.now(timezone.utc))
            
            # Create metadata
            metadata = self._create_metadata()
            
            # Create response
            return self._create_response(
                symbol=symbol,
                price=price,
                timestamp=timestamp,
                volume=ticker_data.get("volume"),
                change=ticker_data.get("change"),
                change_percent=ticker_data.get("change_percent"),
                open=ticker_data.get("open"),
                high=ticker_data.get("high"),
                low=ticker_data.get("low"),
                previous_close=ticker_data.get("previous_close"),
                metadata=metadata
            )
            
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            raise ProviderError(f"Failed to get current price: {str(e)}", self.provider_name)
    
    async def get_historical_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> HistoricalData:
        """Get historical data for a symbol (required by UnifiedDataProvider)."""
        if not self.is_configured:
            raise ProviderError("Alpaca provider not configured", self.provider_name)
        
        try:
            # Use the existing get_historical_data method
            hist_data = await self.get_historical_data_legacy(symbol, start_date, end_date, days)
            
            if "error" in hist_data:
                raise ProviderError(hist_data["error"], self.provider_name)
            
            # Create metadata
            metadata = self._create_metadata(
                data_window_start=start_date,
                data_window_end=end_date
            )
            
            # Create historical data response
            return HistoricalData(
                symbol=symbol,
                dates=hist_data.get("dates", []),
                opens=hist_data.get("opens", []),
                closes=hist_data.get("closes", []),
                highs=hist_data.get("highs", []),
                lows=hist_data.get("lows", []),
                volumes=hist_data.get("volumes", []),
                metadata=metadata,
                additional_fields=hist_data.get("additional_fields")
            )
            
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            raise ProviderError(f"Failed to get historical data: {str(e)}", self.provider_name)
    
    async def get_historical_data_legacy(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> Dict[str, Any]:
        """Legacy method for historical data (renamed to avoid conflict)."""
        # This will be implemented using the existing logic
        # For now, return a placeholder
        return {
            "error": "Historical data method not yet implemented"
        }
    
    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol for Alpaca API."""
        # Remove common prefixes and normalize
        sym = (symbol or "").strip().upper()
        
        # Handle common symbol formats
        if ":" in sym:
            sym = sym.split(":", 1)[1]  # Remove exchange prefix
        if "." in sym:
            sym = sym.split(".", 1)[0]  # Remove suffix
        
        return sym.replace(" ", "")
    
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get current ticker data for a symbol."""
        if not self.is_configured:
            return {"error": "Alpaca provider not configured"}
        
        try:
            
            # Normalize symbol
            normalized_symbol = self._normalize_symbol(symbol)
            
            # Fetch latest quote
            url = f"{self.base_url}/stocks/{normalized_symbol}/quotes/latest"
            headers = {
                "APCA-API-KEY-ID": self.api_key,
                "APCA-API-SECRET-KEY": self.api_secret
            }
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    quote = data.get("quote", {})
                    
                    # Extract price data
                    ask = quote.get("ap")  # Ask price
                    bid = quote.get("bp")  # Bid price
                    current_price = None
                    
                    if ask is not None and bid is not None:
                        # Use midpoint price
                        current_price = (ask + bid) / 2
                    elif ask is not None:
                        current_price = ask
                    elif bid is not None:
                        current_price = bid
                    
                    if current_price is None:
                        return {"error": "No valid price data available"}
                    
                    # Get additional data
                    volume = quote.get("s", 0)  # Size
                    timestamp = quote.get("t", datetime.now(timezone.utc).isoformat())
                    
                    # Calculate change (would need previous close for accurate change)
                    change = 0.0
                    change_percent = 0.0
                    
                    # Build response
                    ticker_data = {
                        "symbol": symbol,
                        "current_price": current_price,
                        "change": change,
                        "change_percent": change_percent,
                        "volume": volume,
                        "ask": ask,
                        "bid": bid,
                        "timestamp": timestamp,
                        "source": "alpaca"
                    }
                    

                    
                    logger.debug(f"✅ Alpaca data for {symbol}: ${current_price}")
                    return ticker_data
                
                elif response.status_code == 404:
                    return {"error": f"Symbol {symbol} not found on Alpaca"}
                elif response.status_code == 429:
                    return {"error": "Rate limit exceeded"}
                else:
                    return {"error": f"Alpaca API error: {response.status_code}"}
                    
        except Exception as e:
            logger.error(f"Error fetching Alpaca data for {symbol}: {e}")
            return {"error": f"Alpaca provider error: {str(e)}"}
    
    async def get_multiple_tickers(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Get data for multiple symbols efficiently."""
        if not self.is_configured:
            return [{"error": "Alpaca provider not configured"} for _ in symbols]
        
        try:
            # Alpaca supports batch quotes
            normalized_symbols = [self._normalize_symbol(sym) for sym in symbols]
            symbols_str = ",".join(normalized_symbols)
            
            url = f"{self.base_url}/stocks/quotes/latest?symbols={symbols_str}"
            headers = {
                "APCA-API-KEY-ID": self.api_key,
                "APCA-API-SECRET-KEY": self.api_secret
            }
            
            async with httpx.AsyncClient(timeout=15.0) as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    quotes = data.get("quotes", {})
                    
                    results = []
                    for symbol in symbols:
                        normalized = self._normalize_symbol(symbol)
                        quote = quotes.get(normalized, {})
                        
                        if quote:
                            # Process quote data similar to get_ticker
                            ask = quote.get("ap")
                            bid = quote.get("bp")
                            current_price = None
                            
                            if ask is not None and bid is not None:
                                current_price = (ask + bid) / 2
                            elif ask is not None:
                                current_price = ask
                            elif bid is not None:
                                current_price = bid
                            
                            if current_price is not None:
                                ticker_data = {
                                    "symbol": symbol,
                                    "current_price": current_price,
                                    "change": 0.0,  # Will be calculated by report engine
                                    "change_percent": 0.0,  # Will be calculated by report engine
                                    "volume": quote.get("s", 0),
                                    "ask": ask,
                                    "bid": bid,
                                    "timestamp": quote.get("t", datetime.now(timezone.utc).isoformat()),
                                    "source": "alpaca"
                                }
                                results.append(ticker_data)
                            else:
                                results.append({"error": f"No valid price for {symbol}"})
                        else:
                            results.append({"error": f"No data for {symbol}"})
                    
                    logger.info(f"✅ Alpaca batch data: {len([r for r in results if 'error' not in r])}/{len(symbols)} symbols")
                    return results
                
                else:
                    # Fallback to individual requests
                    logger.warning("Alpaca batch request failed, falling back to individual requests")
                    return await self._fetch_individual_tickers(symbols)
                    
        except Exception as e:
            logger.error(f"Error in Alpaca batch request: {e}")
            # Fallback to individual requests
            return await self._fetch_individual_tickers(symbols)
    
    async def get_tickers_batch(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Alias for get_multiple_tickers for consistency."""
        return await self.get_multiple_tickers(symbols)
    
    async def _fetch_individual_tickers(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Fallback: fetch individual tickers when batch fails."""
        results = []
        for symbol in symbols:
            try:
                ticker_data = await self.get_ticker(symbol)
                results.append(ticker_data)
                
                # Rate limiting between requests
                await self._rate_limit()
                
            except Exception as e:
                logger.error(f"Error fetching individual ticker {symbol}: {e}")
                results.append({"error": f"Failed to fetch {symbol}: {str(e)}"})
        
        return results
    
    async def _rate_limit(self):
        """Apply rate limiting for Alpaca API."""
        # Alpaca free tier: 1000 requests/month
        # Conservative approach: 1 request per second
        import asyncio
        await asyncio.sleep(1.0)
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about this provider."""
        return {
            "name": "Alpaca",
            "type": "market_data",
            "quality": "high",
            "rate_limit": "1000/month (free tier)",
            "coverage": ["US stocks", "options", "crypto", "forex"],
            "configured": self.is_configured,
            "base_url": self.base_url
        } 