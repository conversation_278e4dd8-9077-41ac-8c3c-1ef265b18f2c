"""
Data providers package for shared market data access.
Provides abstract base classes and concrete implementations for various market data sources.
Updated to use unified base classes for consistency.
"""

from .unified_base import UnifiedDataProvider, HistoricalData
from .alpha_vantage import AlphaVantageProvider
from .yfinance_provider import YFinanceProvider
from .aggregator import DataProviderAggregator

__all__ = [
    'UnifiedDataProvider',
    'HistoricalData',
    'AlphaVantageProvider',
    'YFinanceProvider',
    'DataProviderAggregator'
]