import logging
logger = logging.getLogger(__name__)
"""
Authoritative YFinance Provider (v2)
Consolidated implementation combining all yfinance functionality
Updated to use unified base class for consistency.
"""
from .unified_base import (
    UnifiedDataProvider,
    ProviderType,
    MarketDataResponse,
    HistoricalData,
    ProviderError
)
import yfinance as yf
from datetime import datetime, timedelta, timezone
import asyncio
import time
import pandas as pd
from typing import List, Dict, Any, Optional

class YFinanceProvider(UnifiedDataProvider):
    """Authoritative yfinance implementation with all methods"""

    def __init__(self, cache_expiry: int = 300, config: Optional[Dict[str, Any]] = None):
        # Initialize unified base class
        provider_config = config or {}
        provider_config['rate_limit'] = 120  # 2 requests per second
        provider_config['timeout'] = provider_config.get('timeout', 30.0)
        provider_config['max_retries'] = provider_config.get('max_retries', 3)
        
        super().__init__(
            provider_name="yfinance",
            provider_type=ProviderType.STOCK_DATA,
            config=provider_config
        )
        
        # YFinance specific configuration
        self.cache_expiry = cache_expiry
        self.base_url = "https://api.yfinance.com"
        self._cache = {}
        self.rate_limit_delay = 0.5  # Delay between API calls
    
    async def get_current_price(self, symbol: str) -> MarketDataResponse:
        """Get current price for a symbol (required by UnifiedDataProvider)."""
        try:
            # Use the existing get_ticker method
            ticker_data = await self.get_ticker(symbol)
            
            if "error" in ticker_data:
                raise ProviderError(ticker_data["error"], self.provider_name)
            
            # Create metadata
            metadata = self._create_metadata()
            
            # Create response
            return self._create_response(
                symbol=symbol,
                price=ticker_data.get("current_price", 0.0),
                timestamp=datetime.fromisoformat(ticker_data.get("timestamp", datetime.now().isoformat())),
                volume=ticker_data.get("volume", 0),
                change=ticker_data.get("change", 0),
                change_percent=ticker_data.get("change_percent", 0),
                open=ticker_data.get("open", 0),
                high=ticker_data.get("high", 0),
                low=ticker_data.get("low", 0),
                previous_close=ticker_data.get("previous_close", 0),
                metadata=metadata
            )
            
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            raise ProviderError(f"Failed to get current price: {str(e)}", self.provider_name)
    
    async def get_historical_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> HistoricalData:
        """Get historical data for a symbol (required by UnifiedDataProvider)."""
        try:
            # Use the existing get_history method
            hist_data = await self.get_history(symbol, "1mo" if days is None else f"{days}d")
            
            if "error" in hist_data:
                raise ProviderError(hist_data["error"], self.provider_name)
            
            # Create metadata
            metadata = self._create_metadata(
                data_window_start=start_date,
                data_window_end=end_date
            )
            
            # Convert string dates back to datetime objects
            dates = [datetime.fromisoformat(d) for d in hist_data.get("dates", [])]
            
            # Create historical data response
            return HistoricalData(
                symbol=symbol,
                dates=dates,
                opens=hist_data.get("opens", []),
                closes=hist_data.get("closes", []),
                highs=hist_data.get("highs", []),
                lows=hist_data.get("lows", []),
                volumes=hist_data.get("volumes", []),
                metadata=metadata,
                additional_fields=hist_data.get("additional_fields", {})
            )
            
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            raise ProviderError(f"Failed to get historical data: {str(e)}", self.provider_name)
    
    async def get_stock_data(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive stock data (compatibility method)."""
        try:
            current_price = await self.get_current_price(symbol)
            return {
                'symbol': symbol,
                'current_price': current_price.price,
                'timestamp': current_price.timestamp.isoformat(),
                'volume': current_price.volume,
                'change': current_price.change,
                'change_percent': current_price.change_percent,
                'provider': self.provider_name,
                'metadata': current_price.metadata
            }
        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            raise

    def _cache_get(self, key: str):
        """Get cached data if not expired"""
        if key in self._cache:
            data, timestamp = self._cache[key]
            if time.time() - timestamp < self.cache_expiry:
                return data
            else:
                # Remove expired cache entry
                del self._cache[key]
        return None

    def _cache_set(self, key: str, data):
        """Cache data with current timestamp"""
        self._cache[key] = (data, time.time())

    async def _rate_limit(self):
        """Simple rate limiting - wait a bit between requests"""
        await asyncio.sleep(self.rate_limit_delay)

    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol for yfinance"""
        return symbol.upper().strip()

    def is_configured(self) -> bool:
        """Check if provider is properly configured"""
        try:
            # Just check if yfinance can be imported and basic functionality works
            # Don't make actual API calls during configuration check
            return True
        except Exception:
            return False

    async def get_ticker(self, symbol: str) -> dict:
        """Get current ticker data"""
        try:
            cache_key = f"yf_{symbol}"
            cached = self._cache_get(cache_key)
            if cached is not None:
                return cached

            await self._rate_limit()
            
            ticker = yf.Ticker(symbol)
            
            # Try to get fast_info first (more efficient)
            price: float = None
            info: dict = {}
            
            try:
                info = getattr(ticker, "fast_info", {}) or {}
                price = info.get("lastPrice") or info.get("regularMarketPrice")
                if not price:
                    # Fall back to regular info
                    info = getattr(ticker, "info", {}) or {}
                    price = info.get("regularMarketPrice")
                if not price:
                    # Fall back to history as last resort
                    hist = ticker.history(period="2d")
                    if not hist.empty:
                        price = float(hist["Close"].iloc[-1])
            except Exception as e:
                logger.warning(f"Error getting fast_info for {symbol}: {e}")
                # Fall back to history
                hist = ticker.history(period="2d")
                if not hist.empty:
                    price = float(hist["Close"].iloc[-1])
                else:
                    raise ProviderError(f"No data available for {symbol}", "yfinance")
            
            if price is None:
                raise ProviderError(f"No price data available for {symbol}", "yfinance")
            
            # Get additional data from history
            hist = ticker.history(period="2d")
            current_price = price
            previous_close = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
            
            change = current_price - previous_close
            change_percent = (change / previous_close * 100) if previous_close else 0
            
            result = {
                "symbol": symbol.upper(),
                "current_price": round(current_price, 2),
                "previous_close": round(previous_close, 2),
                "change": round(change, 2),
                "change_percent": round(change_percent, 2),
                "volume": hist['Volume'].iloc[-1] if len(hist) > 0 else 0,
                "market_cap": info.get('marketCap', 0),
                "pe_ratio": info.get('trailingPE', 0),
                "timestamp": datetime.now().isoformat(),
                "source": "yfinance"
            }
            
            self._cache_set(cache_key, result)
            return result
        except Exception as e:
            logger.error(f"Error fetching from Yahoo Finance for {symbol}: {e}")
            raise ProviderError(str(e), "yfinance")

    async def get_history(self, symbol: str, period: str = "1mo", interval: str = "1d") -> dict:
        """Get historical data"""
        try:
            cache_key = f"yf_history_{symbol}_{period}_{interval}"
            cached = self._cache_get(cache_key)
            if cached is not None:
                return cached

            await self._rate_limit()
            
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                raise ProviderError("No historical data available", "yfinance")
            
            result = {
                "symbol": symbol.upper(),
                "dates": [x.date().isoformat() for x in data.index],
                "opens": data['Open'].values.tolist(),
                "closes": data['Close'].values.tolist(),
                "volumes": data['Volume'].values.tolist(),
                "highs": data['High'].values.tolist(),
                "lows": data['Low'].values.tolist(),
                "source": "yfinance"
            }
            
            self._cache_set(cache_key, result)
            return result
        except Exception as e:
            logger.error(f"Error fetching history from Yahoo Finance for {symbol}: {e}")
            raise ProviderError(str(e), "yfinance")

    def get_historical_data(self, symbol: str, days: int = 30) -> HistoricalData:
        """Get historical data in HistoricalData format"""
        try:
            data = yf.Ticker(symbol).history(period=f"{days}d")  
            
            if data is None or len(data) == 0:
                raise ProviderError("No data available", "yfinance")
            
            return HistoricalData(
                symbol=symbol,
                dates=[x.date() for x in data.index],
                opens=data['Open'].values.tolist(),
                closes=data['Close'].values.tolist(),
                volumes=data['Volume'].values.tolist()
            )
        except Exception as e:
            logger.error(f"YFinance failed for {symbol}: {str(e)}")
            raise ProviderError(str(e), "yfinance")
    
    async def get_market_data(self, request) -> 'MarketDataResponse':
        """Get market data in the format expected by DataProviderManager"""
        try:
            # Get ticker data
            ticker_data = await self.get_ticker(request.symbol)
            
            # Create MarketDataResponse
            from src.data.providers.base import MarketDataResponse
            
            return MarketDataResponse(
                symbol=request.symbol,
                price=ticker_data['current_price'],
                volume=ticker_data['volume'],
                timestamp=datetime.now(),
                change=ticker_data['change'],
                change_percent=ticker_data['change_percent'],
                open=ticker_data.get('previous_close'),
                high=ticker_data['current_price'],  # Use current price as fallback
                low=ticker_data['current_price'],   # Use current price as fallback
                close=ticker_data['current_price'],
                market_cap=ticker_data.get('market_cap'),
                pe_ratio=ticker_data.get('pe_ratio')
            )
        except Exception as e:
            logger.error(f"Error getting market data for {request.symbol}: {e}")
            raise ProviderError(str(e), "yfinance") 

    async def get_multiple_tickers(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Get current ticker data for multiple symbols in batch."""
        try:
            results = []
            
            for symbol in symbols:
                try:
                    ticker_data = await self.get_ticker(symbol)
                    if ticker_data and not ticker_data.get('error'):
                        results.append(ticker_data)
                    else:
                        # Add error entry for failed symbols
                        results.append({
                            "symbol": symbol,
                            "error": ticker_data.get('error', 'Unknown error') if ticker_data else 'No data'
                        })
                        
                except Exception as e:
                    logger.debug(f"Failed to fetch {symbol}: {e}")
                    results.append({
                        "symbol": symbol,
                        "error": str(e)
                    })
                
                # Small delay between requests to be respectful
                await asyncio.sleep(0.1)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in batch ticker fetch: {e}")
            return []
    
    async def get_tickers_batch(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Alternative method name for batch ticker fetching."""
        return await self.get_multiple_tickers(symbols) 