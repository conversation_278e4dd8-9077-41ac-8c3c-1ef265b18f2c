"""
Data provider aggregator for fallback logic.
Manages multiple data providers with graceful fallback between them.
Uses centralized configuration for provider order and settings.
Updated to use unified base classes for consistency.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .unified_base import UnifiedDataProvider
from .alpha_vantage import AlphaVantageProvider
from .yfinance_provider import YFinanceProvider
from src.core.config_manager import config

logger = logging.getLogger(__name__)

class DataProviderAggregator:
    """
    Aggregator that manages multiple data providers with fallback logic.
    Tries providers in configured order until one succeeds.
    Uses centralized configuration for settings.
    Updated to use unified base classes for consistency.
    """
    
    def __init__(self, provider_order: Optional[List[str]] = None):
        """
        Initialize the data provider aggregator.
        
        Args:
            provider_order: List of provider names in preferred order.
                           If None, uses default order from centralized configuration.
        """
        self.providers: Dict[str, UnifiedDataProvider] = {}
        self.provider_order = provider_order or self._get_default_provider_order()
        self._initialize_providers()
    
    def _get_default_provider_order(self) -> List[str]:
        """
        Get default provider order from centralized configuration.
        
        Returns:
            List of provider names in preferred order
        """
        # Use config.get() with default provider order
        return config.get('data_providers', 'provider_order', ['alpha_vantage', 'yfinance'])
    
    def _initialize_providers(self):
        """Initialize all configured providers using centralized configuration"""
        provider_map = {
            "alpha_vantage": AlphaVantageProvider,
            "yfinance": YFinanceProvider
        }
        
        for provider_name in self.provider_order:
            if provider_name in provider_map:
                try:
                    # Check if provider is enabled in configuration
                    provider_config = config.get('data_providers', provider_name, {})
                    if not provider_config.get('enabled', True):
                        logger.info(f"Provider {provider_name} is disabled in configuration, skipping")
                        continue
                        
                    provider_instance = provider_map[provider_name]()
                    if provider_instance.is_configured():
                        self.providers[provider_name] = provider_instance
                        logger.info(f"Initialized data provider: {provider_name}")
                    else:
                        logger.warning(f"Provider {provider_name} is not configured, skipping")
                except Exception as e:
                    logger.error(f"Failed to initialize provider {provider_name}: {e}")
            else:
                logger.warning(f"Unknown provider: {provider_name}")
    
    def get_available_providers(self) -> List[str]:
        """
        Get list of available and configured providers.
        
        Returns:
            List of provider names that are configured and ready
        """
        return list(self.providers.keys())
    
    async def get_ticker(self, symbol: str, preferred_provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Get ticker data, trying providers in order until one succeeds.
        
        Args:
            symbol: Stock symbol to fetch data for
            preferred_provider: Specific provider to try first (optional)
            
        Returns:
            Dictionary with ticker data or error information from last provider
        """
        providers_to_try = self._get_providers_to_try(preferred_provider)
        
        last_error = None
        for provider_name in providers_to_try:
            provider = self.providers.get(provider_name)
            if not provider:
                continue
                
            try:
                result = await provider.get_ticker(symbol)
                if "error" not in result:
                    logger.debug(f"Successfully fetched {symbol} from {provider_name}")
                    return result
                else:
                    last_error = result["error"]
                    logger.warning(f"Provider {provider_name} failed for {symbol}: {last_error}")
            except Exception as e:
                last_error = str(e)
                logger.error(f"Provider {provider_name} error for {symbol}: {e}")
        
        # All providers failed
        error_msg = f"All data providers failed for {symbol}"
        if last_error:
            error_msg += f". Last error: {last_error}"
        
        logger.error(error_msg)
        return {"error": error_msg}
    
    async def get_history(self, symbol: str, period: str = "1mo", interval: str = "1d", 
                         preferred_provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Get historical data, trying providers in order until one succeeds.
        
        Args:
            symbol: Stock symbol to fetch data for
            period: Time period (e.g., "1d", "1mo", "1y")
            interval: Data interval (e.g., "1d", "1h", "15m")
            preferred_provider: Specific provider to try first (optional)
            
        Returns:
            Dictionary with historical data or error information from last provider
        """
        providers_to_try = self._get_providers_to_try(preferred_provider)
        
        last_error = None
        for provider_name in providers_to_try:
            provider = self.providers.get(provider_name)
            if not provider:
                continue
                
            try:
                result = await provider.get_history(symbol, period, interval)
                if "error" not in result:
                    logger.debug(f"Successfully fetched history for {symbol} from {provider_name}")
                    return result
                else:
                    last_error = result["error"]
                    logger.warning(f"Provider {provider_name} failed history for {symbol}: {last_error}")
            except Exception as e:
                last_error = str(e)
                logger.error(f"Provider {provider_name} history error for {symbol}: {e}")
        
        # All providers failed
        error_msg = f"All data providers failed for historical data of {symbol}"
        if last_error:
            error_msg += f". Last error: {last_error}"
        
        logger.error(error_msg)
        return {"error": error_msg}

    async def get_historical_data(self, symbol: str, start_date=None, end_date=None, days: int = 30, interval: str = "1d", preferred_provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Backward-compatible wrapper for historical data. Accepts start_date/end_date or days and delegates to get_history.

        Args:
            symbol: Stock symbol
            start_date: Optional start datetime
            end_date: Optional end datetime
            days: Number of days to fetch if start_date/end_date not provided
            interval: Data interval string (e.g., '1d')
            preferred_provider: Preferred provider name (optional)

        Returns:
            Dictionary with historical data or error information
        """
        # Determine period string from days or start/end
        try:
            if start_date and end_date:
                # Compute days difference and use days as period (simple approach)
                delta = (end_date - start_date).days if hasattr(end_date, '__sub__') else days
                period = f"{max(1, int(delta))}d"
            else:
                period = f"{max(1, int(days))}d"

            return await self.get_history(symbol, period=period, interval=interval, preferred_provider=preferred_provider)
        except Exception as e:
            logger.error(f"get_historical_data wrapper failed for {symbol}: {e}")
            return {"error": str(e)}
    
    def _get_providers_to_try(self, preferred_provider: Optional[str] = None) -> List[str]:
        """
        Get the list of providers to try in order.
        
        Args:
            preferred_provider: Specific provider to try first (optional)
            
        Returns:
            List of provider names in try order
        """
        if preferred_provider and preferred_provider in self.providers:
            # Try preferred provider first, then others in order
            other_providers = [p for p in self.provider_order if p != preferred_provider and p in self.providers]
            return [preferred_provider] + other_providers
        else:
            # Use configured order
            return [p for p in self.provider_order if p in self.providers]

# Create singleton instance for easy access
data_provider_aggregator = DataProviderAggregator()