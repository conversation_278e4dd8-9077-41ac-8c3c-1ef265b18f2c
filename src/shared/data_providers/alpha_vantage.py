"""
Alpha Vantage provider for market data.
Primary provider with rate limiting and error handling.
Uses centralized configuration for API keys and settings.
Enhanced with comprehensive error handling and retry mechanisms.
Updated to use unified base class for consistency.
"""

import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
import httpx
import asyncio

from .unified_base import (
    UnifiedDataProvider,
    ProviderType,
    MarketDataResponse,
    HistoricalData,
    ProviderError
)
from src.core.config_manager import config
from src.shared.error_handling import retry_with_backoff, with_error_logging

logger = logging.getLogger(__name__)

class AlphaVantageProvider(UnifiedDataProvider):
    """
    Alpha Vantage market data provider.
    Implements the primary data source with proper rate limiting.
    Uses centralized configuration for settings.
    Updated to use unified base class for consistency.
    """
    
    def __init__(self, cache_ttl: Optional[int] = None, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Alpha Vantage provider.
        
        Args:
            cache_ttl: Cache time-to-live in seconds. If None, uses configured value.
            config: Additional configuration dictionary.
        """
        # Use config.get() with proper nested access and default values
        provider_config = config.get('data_providers', {}).get('alpha_vantage', {}) if config else {}
        
        # Use provided cache_ttl or fall back to configured value
        effective_cache_ttl = cache_ttl or provider_config.get('cache_ttl', 300)
        
        # Safely get API key with default empty string
        api_key = provider_config.get('api_key', '')
        base_url = "https://www.alphavantage.co/query"
        
        # Use get() with default values for rate limit and timeout
        rate_limit = provider_config.get('rate_limit', 5)
        timeout = provider_config.get('timeout', 10.0)
        
        # Initialize unified base class
        unified_config = {
            'rate_limit': rate_limit,
            'timeout': timeout,
            'max_retries': provider_config.get('max_retries', 3),
            'cache_ttl': effective_cache_ttl
        }
        
        super().__init__(
            provider_name="alpha_vantage",
            provider_type=ProviderType.STOCK_DATA,
            config=unified_config
        )
        
        # Store Alpha Vantage specific configuration
        self.api_key = api_key
        self.base_url = base_url
        self.rate_limit_delay = 60.0 / rate_limit  # Convert RPM to delay between requests
        self.timeout = timeout
        
        # Initialize cache
        self._cache = {}
        self.cache_expiry = effective_cache_ttl
    
    async def get_current_price(self, symbol: str) -> MarketDataResponse:
        """Get current price for a symbol (required by UnifiedDataProvider)."""
        try:
            # Use the existing get_ticker method
            ticker_data = await self.get_ticker(symbol)
            
            if "error" in ticker_data:
                raise ProviderError(ticker_data["error"], self.provider_name)
            
            # Create metadata
            metadata = self._create_metadata()
            
            # Create response
            return self._create_response(
                symbol=symbol,
                price=ticker_data.get("current_price", 0.0),
                timestamp=datetime.fromisoformat(ticker_data.get("timestamp", datetime.now().isoformat())),
                volume=ticker_data.get("volume", 0),
                change=ticker_data.get("change", 0),
                change_percent=ticker_data.get("change_percent", 0),
                open=ticker_data.get("open", 0),
                high=ticker_data.get("high", 0),
                low=ticker_data.get("low", 0),
                previous_close=ticker_data.get("previous_close", 0),
                metadata=metadata
            )
            
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            raise ProviderError(f"Failed to get current price: {str(e)}", self.provider_name)
    
    async def get_historical_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> HistoricalData:
        """Get historical data for a symbol (required by UnifiedDataProvider)."""
        try:
            # Use the existing get_history method
            hist_data = await self.get_history(symbol, "1mo" if days is None else f"{days}d")
            
            if "error" in hist_data:
                raise ProviderError(hist_data["error"], self.provider_name)
            
            # Create metadata
            metadata = self._create_metadata(
                data_window_start=start_date,
                data_window_end=end_date
            )
            
            # Convert string dates back to datetime objects
            dates = [datetime.fromisoformat(d) for d in hist_data.get("dates", [])]
            
            # Create historical data response
            return HistoricalData(
                symbol=symbol,
                dates=dates,
                opens=hist_data.get("opens", []),
                closes=hist_data.get("closes", []),
                highs=hist_data.get("highs", []),
                lows=hist_data.get("lows", []),
                volumes=hist_data.get("volumes", []),
                metadata=metadata,
                additional_fields=hist_data.get("additional_fields", {})
            )
            
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            raise ProviderError(f"Failed to get historical data: {str(e)}", self.provider_name)
    
    async def get_stock_data(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive stock data (compatibility method)."""
        try:
            current_price = await self.get_current_price(symbol)
            return {
                'symbol': symbol,
                'current_price': current_price.price,
                'timestamp': current_price.timestamp.isoformat(),
                'volume': current_price.volume,
                'change': current_price.change,
                'change_percent': current_price.change_percent,
                'provider': self.provider_name,
                'metadata': current_price.metadata
            }
        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            raise

    def _cache_get(self, key: str):
        """Get cached data if not expired"""
        if key in self._cache:
            data, timestamp = self._cache[key]
            if time.time() - timestamp < self.cache_expiry:
                return data
            else:
                # Remove expired cache entry
                del self._cache[key]
        return None

    def _cache_set(self, key: str, data):
        """Cache data with current timestamp"""
        self._cache[key] = (data, time.time())

    async def _rate_limit(self):
        """Simple rate limiting - wait between requests"""
        await asyncio.sleep(self.rate_limit_delay)

    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol for Alpha Vantage"""
        return symbol.upper().strip()
    
    def is_configured(self) -> bool:
        """Check if Alpha Vantage is properly configured with API key"""
        return bool(self.api_key)
    
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """
        Get current ticker data from Alpha Vantage.
        
        Args:
            symbol: Stock symbol to fetch data for
            
        Returns:
            Dictionary with ticker data or error information
        """
        if not self.is_configured():
            return {"error": "Alpha Vantage provider not configured: missing API key"}
        
        try:
            cache_key = f"av_{symbol}"
            cached = self._cache_get(cache_key)
            if cached is not None:
                return cached
            
            await self._rate_limit()
            
            normalized_symbol = self._normalize_symbol(symbol)
            
            params = {
                "function": "GLOBAL_QUOTE",
                "symbol": normalized_symbol,
                "apikey": self.api_key
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.base_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # Check for API errors
                if "Error Message" in data:
                    error_msg = data.get("Error Message", "Unknown Alpha Vantage error")
                    return {"error": f"Alpha Vantage API error: {error_msg}"}
                
                if "Note" in data:  # Rate limit message
                    return {"error": "Alpha Vantage rate limit exceeded"}
                
                quote_data = data.get("Global Quote", {})
                if not quote_data:
                    return {"error": f"No data available for {symbol} from Alpha Vantage"}
                
                # Extract price data
                price_str = quote_data.get("05. price")
                if not price_str:
                    return {"error": f"No price data for {symbol} from Alpha Vantage"}
                
                try:
                    price = float(price_str)
                except (ValueError, TypeError):
                    return {"error": f"Invalid price data for {symbol} from Alpha Vantage"}
                
                result = {
                    "symbol": symbol,
                    "current_price": price,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "source": "alpha_vantage",
                    "volume": self._parse_float(quote_data.get("06. volume")),
                    "open": self._parse_float(quote_data.get("02. open")),
                    "high": self._parse_float(quote_data.get("03. high")),
                    "low": self._parse_float(quote_data.get("04. low")),
                    "close": self._parse_float(quote_data.get("05. price")),
                    "change_percent": self._parse_float(quote_data.get("10. change percent"))
                }
                
                self._cache_set(cache_key, result)
                return result
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error fetching from Alpha Vantage for {symbol}: {e}")
            return {"error": f"Alpha Vantage HTTP error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error fetching from Alpha Vantage for {symbol}: {e}")
            return {"error": f"Alpha Vantage provider error: {str(e)}"}
    
    async def get_history(self, symbol: str, period: str = "1mo", interval: str = "1d") -> Dict[str, Any]:
        """
        Get historical data from Alpha Vantage.
        
        Args:
            symbol: Stock symbol to fetch data for
            period: Time period (e.g., "1mo", "3mo", "1y")
            interval: Data interval (e.g., "1d", "60min", "30min")
            
        Returns:
            Dictionary with historical data or error information
        """
        if not self.is_configured():
            return {"error": "Alpha Vantage provider not configured: missing API key"}
        
        try:
            cache_key = f"av_history_{symbol}_{period}_{interval}"
            cached = self._cache_get(cache_key)
            if cached is not None:
                return cached
            
            await self._rate_limit()
            
            normalized_symbol = self._normalize_symbol(symbol)
            
            # Map period to Alpha Vantage function
            function_map = {
                "1d": "TIME_SERIES_INTRADAY",
                "1mo": "TIME_SERIES_DAILY",
                "3mo": "TIME_SERIES_DAILY",
                "1y": "TIME_SERIES_DAILY",
                "max": "TIME_SERIES_DAILY_ADJUSTED"
            }
            
            function = function_map.get(period, "TIME_SERIES_DAILY")
            
            params = {
                "function": function,
                "symbol": normalized_symbol,
                "apikey": self.api_key
            }
            
            if function == "TIME_SERIES_INTRADAY":
                params["interval"] = interval
                params["outputsize"] = "compact"
            else:
                params["outputsize"] = "full" if period == "max" else "compact"
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.base_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # Check for API errors
                if "Error Message" in data:
                    error_msg = data.get("Error Message", "Unknown Alpha Vantage error")
                    return {"error": f"Alpha Vantage API error: {error_msg}"}
                
                if "Note" in data:  # Rate limit message
                    return {"error": "Alpha Vantage rate limit exceeded"}
                
                # Extract time series data based on function
                time_series_key = None
                if function == "TIME_SERIES_INTRADAY":
                    time_series_key = f"Time Series ({interval})"
                elif function == "TIME_SERIES_DAILY":
                    time_series_key = "Time Series (Daily)"
                elif function == "TIME_SERIES_DAILY_ADJUSTED":
                    time_series_key = "Time Series (Daily)"
                
                time_series = data.get(time_series_key, {})
                if not time_series:
                    return {"error": f"No historical data available for {symbol} from Alpha Vantage"}
                
                # Format historical data
                historical_data = []
                for timestamp_str, values in time_series.items():
                    try:
                        historical_data.append({
                            "timestamp": timestamp_str,
                            "open": self._parse_float(values.get("1. open")),
                            "high": self._parse_float(values.get("2. high")),
                            "low": self._parse_float(values.get("3. low")),
                            "close": self._parse_float(values.get("4. close")),
                            "volume": self._parse_float(values.get("5. volume")),
                            "adjusted_close": self._parse_float(values.get("5. adjusted close"))
                        })
                    except (ValueError, TypeError):
                        continue
                
                result = {
                    "symbol": symbol,
                    "data": historical_data,
                    "period": period,
                    "interval": interval,
                    "count": len(historical_data),
                    "source": "alpha_vantage"
                }
                
                self._cache_set(cache_key, result)
                return result
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error fetching history from Alpha Vantage for {symbol}: {e}")
            return {"error": f"Alpha Vantage HTTP error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error fetching history from Alpha Vantage for {symbol}: {e}")
            return {"error": f"Alpha Vantage provider error: {str(e)}"}
    
    async def get_multiple_tickers(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Get current ticker data for multiple symbols in batch with rate limiting."""
        try:
            results = []
            
            for i, symbol in enumerate(symbols):
                try:
                    ticker_data = await self.get_ticker(symbol)
                    if ticker_data and not ticker_data.get('error'):
                        results.append(ticker_data)
                    else:
                        # Add error entry for failed symbols
                        results.append({
                            "symbol": symbol,
                            "error": ticker_data.get('error', 'Unknown error') if ticker_data else 'No data'
                        })
                        
                except Exception as e:
                    logger.debug(f"Failed to fetch {symbol}: {e}")
                    results.append({
                        "symbol": symbol,
                        "error": str(e)
                    })
                
                # Rate limiting between requests (Alpha Vantage has strict limits)
                if i < len(symbols) - 1:  # Don't sleep after the last request
                    await asyncio.sleep(self.rate_limit_delay)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in batch ticker fetch: {e}")
            return []
    
    async def get_tickers_batch(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Alternative method name for batch ticker fetching."""
        return await self.get_multiple_tickers(symbols)
    
    def _parse_float(self, value: Any) -> Optional[float]:
        """Safely parse float values from API response"""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None

# Create singleton instance for easy access
alpha_vantage_provider = AlphaVantageProvider()