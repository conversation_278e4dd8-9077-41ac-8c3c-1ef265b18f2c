"""
Supabase SDK client implementation for TradingView Automation
"""

import os
import logging
from typing import Dict, Any, Optional, List
from supabase import create_client, Client
from pydantic import BaseModel, validator, Field

from .supabase_base import BaseSupabaseClient

class SupabaseConfig(BaseModel):
    """
    Secure Supabase configuration model with validation.
    """
    url: str = Field(..., min_length=10, description="Supabase project URL")
    key: str = Field(..., min_length=20, description="Supabase API key")
    
    @validator('url')
    def validate_url(cls, url):
        """
        Validate Supabase URL format.
        """
        if not url.startswith(('https://', 'http://')):
            raise ValueError("Supabase URL must start with https:// or http://")
        return url

class SupabaseSDKClient(BaseSupabaseClient):
    """
    Supabase client implementation using the official Python SDK
    """
    
    _instance = None
    
    def __new__(cls):
        """
        Singleton pattern implementation.
        """
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """
        Initialize Supabase client with secure configuration.
        """
        if not hasattr(self, '_initialized'):
            self.logger = logging.getLogger(__name__)
            self._client = None
            self._initialized = False
    
    def _create_supabase_client(self) -> Optional[Client]:
        """
        Create a secure Supabase client.
        
        Returns:
            Supabase Client or None if configuration is invalid
        """
        try:
            # Try to load from environment variables
            url = os.getenv('SUPABASE_URL')
            key = os.getenv('SUPABASE_KEY')
            
            if not url or not key:
                self.log_error("Supabase credentials not found. Client not initialized.")
                return None
            
            # Validate configuration
            config = SupabaseConfig(url=url, key=key)
            
            # Create and return Supabase client
            client = create_client(config.url, config.key)
            self.log_info("Supabase client initialized successfully")
            return client
        
        except Exception as e:
            self.log_error(f"Failed to initialize Supabase client: {e}")
            return None
    
    async def initialize(self) -> bool:
        """Initialize connections and test connectivity"""
        self._client = self._create_supabase_client()
        self._initialized = self._client is not None
        return self._initialized
    
    async def query_data(self, table: str, query: Dict[str, Any], correlation_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Query data from a Supabase table"""
        if not self._client:
            self.log_error("Supabase client not initialized")
            return []
        
        try:
            # Perform query with logging
            result = self._client.table(table).select("*").match(query).execute()
            
            self.log_info(
                f"Supabase query executed successfully",
                extra={
                    "table": table,
                    "query_params": query,
                    "result_count": len(result.data),
                    "correlation_id": correlation_id
                }
            )
            
            return result.data
        
        except Exception as e:
            self.log_error(
                f"Supabase query failed",
                extra={
                    "table": table,
                    "query_params": query,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            return []
    
    async def insert_data(self, table: str, data: Dict[str, Any], correlation_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Insert data into a Supabase table"""
        if not self._client:
            self.log_error("Supabase client not initialized")
            return None
        
        try:
            result = self._client.table(table).insert(data).execute()
            
            self.log_info(
                f"Data inserted successfully into {table}",
                extra={
                    "inserted_data": data,
                    "correlation_id": correlation_id
                }
            )
            
            return result.data
        
        except Exception as e:
            self.log_error(
                f"Failed to insert data into {table}",
                extra={
                    "data": data,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            return None
    
    async def update_data(self, table: str, match_criteria: Dict[str, Any], update_data: Dict[str, Any], correlation_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Update data in a Supabase table"""
        if not self._client:
            self.log_error("Supabase client not initialized")
            return None
        
        try:
            result = (
                self._client.table(table)
                .update(update_data)
                .match(match_criteria)
                .execute()
            )
            
            self.log_info(
                f"Data updated successfully in {table}",
                extra={
                    "match_criteria": match_criteria,
                    "updated_data": update_data,
                    "correlation_id": correlation_id
                }
            )
            
            return result.data
        
        except Exception as e:
            self.log_error(
                f"Failed to update data in {table}",
                extra={
                    "match_criteria": match_criteria,
                    "update_data": update_data,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            return None
    
    async def close(self):
        """Close connections"""
        # The Python SDK doesn't require explicit closing
        self.log_info("Supabase client closed")
    
    def log_info(self, message: str, **kwargs):
        """Log information"""
        extra = kwargs.get('extra', {})
        self.logger.info(message, extra=extra)
    
    def log_error(self, message: str, **kwargs):
        """Log error"""
        extra = kwargs.get('extra', {})
        self.logger.error(message, extra=extra)

# Singleton instance for easy import and use
supabase_sdk_client = SupabaseSDKClient()
