"""
Example of using the unified database connection manager
"""

import asyncio
from typing import Dict, Any, List, Optional

# Import the unified database manager
from src.shared.database import (
    db_manager,
    query_data,
    insert_data,
    update_data
)

async def example_database_operations():
    """Example of using the unified database manager"""
    
    # Initialize the database manager
    await db_manager.initialize()
    
    try:
        # Example 1: Query data
        webhooks = await query_data("webhooks", {"limit": 10})
        print(f"Found {len(webhooks)} webhooks")
        
        # Example 2: Insert data
        new_webhook = {
            "webhook_id": "example-webhook-123",
            "timestamp": "2025-09-07T12:00:00Z",
            "client_ip": "127.0.0.1",
            "raw_data": {"symbol": "AAPL", "price": 150.25},
            "status": "received"
        }
        result = await insert_data("webhooks", new_webhook)
        if result:
            print(f"Inserted webhook with ID: {result.get('id')}")
        
        # Example 3: Update data
        updated = await update_data(
            "webhooks",
            {"webhook_id": "example-webhook-123"},
            {"status": "processed"}
        )
        if updated:
            print(f"Updated webhook status to: {updated.get('status')}")
        
        # Example 4: Using the Supabase client directly
        supabase = await db_manager.get_supabase_client()
        if supabase:
            tickers = await supabase.query_data("tickers", {"symbol": "AAPL"})
            print(f"Found {len(tickers)} AAPL tickers")
        
        # Example 5: Using PostgreSQL session (if enabled)
        if db_manager._use_postgres:
            try:
                async with db_manager.postgres_session() as session:
                    result = await session.execute("SELECT COUNT(*) FROM webhooks")
                    count = result.scalar()
                    print(f"Total webhooks in PostgreSQL: {count}")
            except Exception as e:
                print(f"PostgreSQL error: {e}")
    
    finally:
        # Always close connections when done
        await db_manager.close()

if __name__ == "__main__":
    asyncio.run(example_database_operations())
