"""
Unified Database Connection Manager

This module provides a centralized way to manage database connections
for Supabase databases only.
"""

import os
import asyncio
import logging
from typing import Dict, Any, Optional, List, Union, Tuple
from contextlib import asynccontextmanager

# Import Supabase clients
from .supabase_base import BaseSupabaseClient
from .supabase_sdk_client import supabase_sdk_client
from .supabase_http_client import supabase_http_client

class DatabaseConnectionManager:
    """
    Unified database connection manager that provides a centralized way to
    access databases throughout the application.
    
    Features:
    - Singleton pattern ensures only one connection manager is created
    - Connection pooling for efficient resource usage
    - Support for Supabase databases only
    - Consistent environment variable handling
    - Proper error handling and logging
    """
    
    _instance = None
    _initialized = False
    _connection_lock = asyncio.Lock()
    _health_check_task = None
    
    # Database clients
    _supabase_client: Optional[BaseSupabaseClient] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseConnectionManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._initialized = True
            self._connection_attempts = 0
            self._max_retries = 5
            self._retry_delay = 2  # seconds
            
            # Configure logging
            self.logger = logging.getLogger(__name__)
            
            # Read configuration from environment variables
            from src.core.config_manager import get_config
            config = get_config()
            self._environment = config.get('app', 'environment', 'development')
            self._use_supabase = os.getenv("USE_SUPABASE", "true").lower() == "true"
            self._supabase_client_type = os.getenv("SUPABASE_CLIENT_TYPE", "sdk").lower()
            
            # Database connection strings
            from src.core.config_manager import get_config
            config = get_config()
            db_config = config.get_database_config()
            self._supabase_url = config.get('database', 'supabase_url', '')
            self._supabase_key = config.get('security', 'jwt_secret', '')
    
    async def initialize(self) -> bool:
        """
        Initialize database connections.
        
        Returns:
            bool: True if at least one connection was successful, False otherwise
        """
        async with self._connection_lock:
            success = False
            
            # Initialize Supabase if enabled
            if self._use_supabase:
                supabase_success = await self._initialize_supabase()
                success = success or supabase_success
            
            # Start health check task
            if success:
                self._start_health_check()
            
            return success
    
    async def _initialize_supabase(self) -> bool:
        """
        Initialize Supabase client based on configuration.
        
        Returns:
            bool: True if connection was successful, False otherwise
        """
        try:
            self.logger.info("Initializing Supabase connection")
            
            # Select client implementation based on configuration
            if self._supabase_client_type == "http":
                self._supabase_client = supabase_http_client
            else:
                self._supabase_client = supabase_sdk_client
            
            # Initialize the client
            success = await self._supabase_client.initialize()
            
            if success:
                self.logger.info("Supabase connection established successfully")
            else:
                self.logger.error("Failed to initialize Supabase connection")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error initializing Supabase: {e}")
            return False
    
    def _start_health_check(self):
        """Start background task for periodic health checks"""
        if self._health_check_task is None or self._health_check_task.done():
            self._health_check_task = asyncio.create_task(self._health_check_loop())
    
    async def _health_check_loop(self):
        """Periodically check database connection health"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                # Check Supabase connection
                if self._use_supabase and self._supabase_client:
                    try:
                        # Simple query to test connection
                        await self._supabase_client.query_data("webhooks", {"limit": 1})
                    except Exception as e:
                        self.logger.warning(f"Supabase health check failed: {e}")
                        # Try to reconnect
                        await self._initialize_supabase()
                        
            except Exception as e:
                self.logger.error(f"Error in database health check: {e}")
    
    async def get_supabase_client(self) -> Optional[BaseSupabaseClient]:
        """
        Get the Supabase client instance, initializing if necessary.
        
        Returns:
            Optional[BaseSupabaseClient]: Supabase client or None if not available
        """
        if not self._use_supabase:
            return None
            
        if self._supabase_client is None:
            await self.initialize()
        
        return self._supabase_client
    
    async def query_data(self, table: str, query: Dict[str, Any], use_supabase: bool = True) -> List[Dict[str, Any]]:
        """
        Query data from a database table.
        
        Args:
            table: Table name
            query: Query parameters
            use_supabase: Whether to use Supabase (True) or PostgreSQL (False)
            
        Returns:
            List of matching records
        """
        if use_supabase and self._use_supabase:
            client = await self.get_supabase_client()
            if client:
                return await client.query_data(table, query)
        
        return []
    
    async def insert_data(self, table: str, data: Dict[str, Any], use_supabase: bool = True) -> Optional[Dict[str, Any]]:
        """
        Insert data into a database table.
        
        Args:
            table: Table name
            data: Data to insert
            use_supabase: Whether to use Supabase (True) or PostgreSQL (False)
            
        Returns:
            Inserted record or None if failed
        """
        if use_supabase and self._use_supabase:
            client = await self.get_supabase_client()
            if client:
                return await client.insert_data(table, data)
        
        return None
    
    async def update_data(self, table: str, match_criteria: Dict[str, Any], update_data: Dict[str, Any], use_supabase: bool = True) -> Optional[Dict[str, Any]]:
        """
        Update data in a database table.
        
        Args:
            table: Table name
            match_criteria: Criteria to match records
            update_data: Data to update
            use_supabase: Whether to use Supabase (True) or PostgreSQL (False)
            
        Returns:
            Updated record or None if failed
        """
        if use_supabase and self._use_supabase:
            client = await self.get_supabase_client()
            if client:
                return await client.update_data(table, match_criteria, update_data)
        
        return None
    
    async def close(self):
        """Close all database connections"""
        # Close Supabase connection
        if self._supabase_client:
            try:
                await self._supabase_client.close()
                self.logger.info("Supabase connection closed")
            except Exception as e:
                self.logger.error(f"Error closing Supabase connection: {e}")
        
        # Cancel health check task
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None

# Singleton instance for easy import and use
db_manager = DatabaseConnectionManager()

# Convenience functions
async def get_supabase_client() -> Optional[BaseSupabaseClient]:
    """Get the Supabase client instance"""
    return await db_manager.get_supabase_client()

async def query_data(table: str, query: Dict[str, Any], use_supabase: bool = True) -> List[Dict[str, Any]]:
    """Query data from a database table"""
    return await db_manager.query_data(table, query, use_supabase)

async def insert_data(table: str, data: Dict[str, Any], use_supabase: bool = True) -> Optional[Dict[str, Any]]:
    """Insert data into a database table"""
    return await db_manager.insert_data(table, data, use_supabase)

async def update_data(table: str, match_criteria: Dict[str, Any], update_data: Dict[str, Any], use_supabase: bool = True) -> Optional[Dict[str, Any]]:
    """Update data in a database table"""
    return await db_manager.update_data(table, match_criteria, update_data, use_supabase)
