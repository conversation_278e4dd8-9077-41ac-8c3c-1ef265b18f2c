"""
Base Supabase client interface for TradingView Automation
"""

import os
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

class BaseSupabaseClient(ABC):
    """
    Abstract base class for Supabase client implementations
    Provides common interface for different implementations
    """
    
    @abstractmethod
    def __init__(self):
        """Initialize the Supabase client"""
        pass
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize connections and test connectivity"""
        pass
    
    @abstractmethod
    async def query_data(self, table: str, query: Dict[str, Any], correlation_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Query data from a Supabase table"""
        pass
    
    @abstractmethod
    async def insert_data(self, table: str, data: Dict[str, Any], correlation_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Insert data into a Supabase table"""
        pass
    
    @abstractmethod
    async def update_data(self, table: str, match_criteria: Dict[str, Any], update_data: Dict[str, Any], correlation_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Update data in a Supabase table"""
        pass
    
    @abstractmethod
    async def close(self):
        """Close connections"""
        pass
    
    @abstractmethod
    def log_info(self, message: str, **kwargs):
        """Log information"""
        pass
    
    @abstractmethod
    def log_error(self, message: str, **kwargs):
        """Log error"""
        pass
