"""
HTTP-based Supabase client implementation for TradingView Automation
"""

import os
import json
import structlog
from typing import Dict, Any, Optional, List
import httpx

from .supabase_base import BaseSupabaseClient

class SupabaseHTTPClient(BaseSupabaseClient):
    """
    Supabase client implementation using direct HTTP requests via httpx
    """
    
    _instance = None
    
    def __new__(cls):
        """
        Singleton pattern implementation.
        """
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """
        Initialize Supabase client with configuration from environment variables.
        """
        if not hasattr(self, '_initialized'):
            self.logger = structlog.get_logger()
            from src.core.config_manager import get_config
            config = get_config()
            self.supabase_url = config.get('database', 'supabase_url', '')
            self.supabase_key = config.get('security', 'jwt_secret', '')
            self.supabase_db_url = os.getenv("SUPABASE_DB_URL")
            self.use_supabase = os.getenv("USE_SUPABASE", "true").lower() == "true"
            self.client = None
            self._initialized = False
            
            # Check if Supabase is properly configured
            if not self.use_supabase:
                self.log_info("Supabase integration is disabled")
                return
                
            if not self.supabase_url or not self.supabase_key:
                self.log_warning("Supabase URL or API key not provided, integration disabled")
                self.use_supabase = False
                return
    
    async def initialize(self) -> bool:
        """Initialize connections and test connectivity"""
        if not self.use_supabase:
            return True
            
        try:
            # Initialize HTTP client for Supabase REST API
            self.client = httpx.AsyncClient(
                base_url=self.supabase_url,
                headers={
                    "apikey": self.supabase_key,
                    "Authorization": f"Bearer {self.supabase_key}",
                    "Content-Type": "application/json"
                },
                timeout=30.0
            )
            
            # Test connection with a simple query
            response = await self.client.get("/rest/v1/webhooks?limit=1")
            response.raise_for_status()
            self.log_info("Supabase connection successful")
            
            # Ensure required tables exist
            await self._ensure_tables_exist()
            
            self._initialized = True
            return True
        except Exception as e:
            self.log_error("Failed to connect to Supabase", error=str(e))
            return False
    
    async def _ensure_tables_exist(self) -> bool:
        """Ensure that required tables exist in Supabase"""
        try:
            if self.client is None:
                self.log_error("Supabase client is None")
                return False
                
            # Check if webhooks table exists
            response = await self.client.get("/rest/v1/webhooks?limit=0")
            
            if response.status_code == 404:
                self.log_warning("Webhooks table does not exist")
            
            # Check if tickers table exists
            response = await self.client.get("/rest/v1/tickers?limit=0")
            
            if response.status_code == 404:
                self.log_warning("Tickers table does not exist")
                
            return True
        except Exception as e:
            self.log_error("Failed to ensure tables exist", error=str(e))
            return False
    
    async def query_data(self, table: str, query: Dict[str, Any], correlation_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Query data from a Supabase table"""
        if not self.use_supabase or not self._initialized or self.client is None:
            return []
            
        try:
            # Build query parameters
            params = "&".join([f"{k}=eq.{v}" for k, v in query.items()])
            
            # Execute query
            response = await self.client.get(f"/rest/v1/{table}?{params}")
            response.raise_for_status()
            
            self.log_info(f"Query executed successfully on table {table}", 
                         query_params=query, 
                         correlation_id=correlation_id)
            
            return response.json()
        except Exception as e:
            self.log_error(f"Failed to query table {table}", 
                          error=str(e), 
                          query_params=query,
                          correlation_id=correlation_id)
            return []
    
    async def insert_data(self, table: str, data: Dict[str, Any], correlation_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Insert data into a Supabase table"""
        if not self.use_supabase or not self._initialized or self.client is None:
            return None
            
        try:
            # Insert data
            response = await self.client.post(
                f"/rest/v1/{table}",
                json=data
            )
            response.raise_for_status()
            
            self.log_info(f"Data inserted successfully into {table}", 
                         correlation_id=correlation_id)
            
            return response.json()
        except Exception as e:
            self.log_error(f"Failed to insert data into {table}", 
                          error=str(e),
                          correlation_id=correlation_id)
            return None
    
    async def update_data(self, table: str, match_criteria: Dict[str, Any], update_data: Dict[str, Any], correlation_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Update data in a Supabase table"""
        if not self.use_supabase or not self._initialized or self.client is None:
            return None
            
        try:
            # Build query parameters for matching
            params = "&".join([f"{k}=eq.{v}" for k, v in match_criteria.items()])
            
            # Update data
            response = await self.client.patch(
                f"/rest/v1/{table}?{params}",
                json=update_data
            )
            response.raise_for_status()
            
            self.log_info(f"Data updated successfully in {table}", 
                         match_criteria=match_criteria,
                         correlation_id=correlation_id)
            
            return response.json()
        except Exception as e:
            self.log_error(f"Failed to update data in {table}", 
                          error=str(e),
                          match_criteria=match_criteria,
                          correlation_id=correlation_id)
            return None
    
    async def close(self):
        """Close connections"""
        if self.client:
            await self.client.aclose()
            self.log_info("Supabase client closed")
    
    def log_info(self, message: str, **kwargs):
        """Log information using structlog"""
        self.logger.info(message, **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """Log error using structlog"""
        self.logger.error(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs):
        """Log warning using structlog"""
        self.logger.warning(message, **kwargs)

# Singleton instance for easy import and use
supabase_http_client = SupabaseHTTPClient()
