"""
Unified Symbol Extraction Utilities

This module provides a centralized, consistent approach to extracting stock symbols
from user queries across all commands (/ask, /analyze, etc.).

Key principles:
- Only extract symbols with explicit $ prefix to avoid false positives
- Validate extracted symbols against known patterns
- Provide consistent behavior across all bot commands
"""

import re
import logging
from typing import List, Optional, Tuple, Set
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ExtractedSymbol:
    """Represents an extracted symbol with metadata"""
    symbol: str
    confidence: float
    context: str  # How it was found (e.g., "dollar_prefix", "exchange_notation")
    original_text: str  # Original text that matched


class UnifiedSymbolExtractor:
    """Unified symbol extraction for all bot commands"""
    
    # Known non-ticker words to filter out
    NON_TICKERS = {
        'USD', 'CAD', 'EUR', 'GBP', 'JPY', 'CNY', 'AUD', 'CHF',  # Currencies
        'THE', 'AND', 'FOR', 'YOU', 'ARE', 'BUT', 'NOT', 'ALL',  # Common words
        'BUY', 'SELL', 'HOLD', 'LONG', 'SHORT', 'CALL', 'PUT',   # Trading terms
        'PRICE', 'STOCK', 'MARKET', 'TRADE', 'INVEST', 'MONEY'   # Finance terms
    }
    
    def __init__(self):
        """Initialize the extractor with default settings"""
        self.symbol_pattern = re.compile(r'^[A-Z]{1,10}$')
        
    def extract_symbols(self, text: str, require_dollar_prefix: bool = True) -> List[ExtractedSymbol]:
        """
        Extract stock symbols from text with high precision.
        
        Args:
            text: Text to extract symbols from
            require_dollar_prefix: If True, only extract symbols with $ prefix
            
        Returns:
            List of ExtractedSymbol objects
        """
        if not text:
            return []
            
        extracted = []
        
        # Method 1: Dollar prefix symbols ($AAPL)
        dollar_symbols = self._extract_dollar_prefix_symbols(text)
        extracted.extend(dollar_symbols)
        
        # Method 2: Exchange notation (AAPL.NASDAQ) - only if not requiring dollar prefix
        if not require_dollar_prefix:
            exchange_symbols = self._extract_exchange_notation_symbols(text)
            extracted.extend(exchange_symbols)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_extracted = []
        for symbol in extracted:
            if symbol.symbol not in seen:
                seen.add(symbol.symbol)
                unique_extracted.append(symbol)
                
        return unique_extracted
    
    def _extract_dollar_prefix_symbols(self, text: str) -> List[ExtractedSymbol]:
        """Extract symbols with $ prefix (e.g., $AAPL)"""
        symbols = []
        
        # Pattern: $SYMBOL (1-10 uppercase letters after $)
        pattern = r'\$([A-Z]{1,10})\b'
        matches = re.finditer(pattern, text)
        
        for match in matches:
            symbol = match.group(1)
            if self._is_valid_symbol(symbol):
                symbols.append(ExtractedSymbol(
                    symbol=symbol,
                    confidence=0.95,  # High confidence for $ prefix
                    context="dollar_prefix",
                    original_text=match.group(0)
                ))
                
        return symbols
    
    def _extract_exchange_notation_symbols(self, text: str) -> List[ExtractedSymbol]:
        """Extract symbols with exchange notation (e.g., AAPL.NASDAQ)"""
        symbols = []
        
        # Pattern: SYMBOL.EXCHANGE
        pattern = r'\b([A-Z]{1,10})\.([A-Z]{1,10})\b'
        matches = re.finditer(pattern, text)
        
        for match in matches:
            symbol = match.group(1)
            exchange = match.group(2)
            
            # Validate both symbol and exchange
            if self._is_valid_symbol(symbol) and self._is_valid_exchange(exchange):
                symbols.append(ExtractedSymbol(
                    symbol=symbol,
                    confidence=0.85,  # Good confidence for exchange notation
                    context=f"exchange_notation_{exchange}",
                    original_text=match.group(0)
                ))
                
        return symbols
    
    def _is_valid_symbol(self, symbol: str) -> bool:
        """Validate if a string is a valid stock symbol"""
        if not symbol or len(symbol) < 1 or len(symbol) > 10:
            return False
            
        # Must be all uppercase letters
        if not self.symbol_pattern.match(symbol):
            return False
            
        # Filter out known non-tickers
        if symbol in self.NON_TICKERS:
            return False
            
        return True
    
    def _is_valid_exchange(self, exchange: str) -> bool:
        """Validate if a string is a valid exchange identifier"""
        valid_exchanges = {
            'NYSE', 'NASDAQ', 'AMEX', 'TSX', 'LSE', 'HKEX', 'ASX', 'BSE', 'NSE'
        }
        return exchange in valid_exchanges
    
    def extract_symbols_simple(self, text: str) -> List[str]:
        """
        Simple extraction that returns just symbol strings (for backward compatibility).
        
        Args:
            text: Text to extract symbols from
            
        Returns:
            List of symbol strings
        """
        extracted = self.extract_symbols(text, require_dollar_prefix=True)
        return [symbol.symbol for symbol in extracted]


# Global instance for easy access
unified_symbol_extractor = UnifiedSymbolExtractor()


# Convenience functions for backward compatibility
def extract_symbols_from_query(query: str) -> List[str]:
    """
    Extract symbols from query - unified implementation.
    
    This replaces the various extract_symbols_from_query functions
    scattered across the codebase.
    """
    return unified_symbol_extractor.extract_symbols_simple(query)


def extract_symbols_with_metadata(query: str) -> List[ExtractedSymbol]:
    """
    Extract symbols with full metadata.
    
    Use this when you need confidence scores and context information.
    """
    return unified_symbol_extractor.extract_symbols(query)


def validate_symbol_format(symbol: str) -> Tuple[str, bool, str]:
    """
    Validate and sanitize a symbol format.
    
    Args:
        symbol: Symbol to validate
        
    Returns:
        Tuple of (sanitized_symbol, is_valid, error_message)
    """
    if not symbol:
        return "", False, "Symbol cannot be empty"
    
    # Basic sanitization
    sanitized = symbol.upper().strip()
    
    # Remove $ prefix if present
    if sanitized.startswith('$'):
        sanitized = sanitized[1:]
    
    # Use the unified validator
    extractor = UnifiedSymbolExtractor()
    if extractor._is_valid_symbol(sanitized):
        return sanitized, True, ""
    else:
        return sanitized, False, "Invalid symbol format or known non-ticker"
