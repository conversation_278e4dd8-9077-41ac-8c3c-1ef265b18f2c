"""
Data validation and gap detection for market data quality assessment.

This module provides comprehensive data quality validation including:
- Gap detection in time series data
- Data completeness analysis
- Quality scoring and reporting
"""

import logging
from datetime import datetime, timedelta, time
from typing import List, Dict, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
import pytz

# Import market calendar for proper trading day detection
from src.core.market_calendar import MarketCalendar, Exchange

logger = logging.getLogger(__name__)


class GapSeverity(Enum):
    """Severity levels for data gaps."""
    MINOR = "minor"      # ≤5 minutes missing
    MODERATE = "moderate"  # 5-30 minutes missing
    MAJOR = "major"      # 30+ minutes missing
    CRITICAL = "critical"  # 2+ hours missing


@dataclass
class DataGap:
    """Represents a detected data gap in time series data."""
    symbol: str
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    severity: GapSeverity
    interval_type: str  # '1m', '5m', '1h', '1d', etc.
    expected_count: int
    actual_count: int
    missing_count: int
    gap_percentage: float
    
    def __post_init__(self):
        """Calculate derived fields after initialization."""
        if not hasattr(self, 'duration_seconds'):
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()
        
        if not hasattr(self, 'gap_percentage'):
            self.gap_percentage = (self.missing_count / self.expected_count * 100) if self.expected_count > 0 else 0
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return {
            'symbol': self.symbol,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'duration_seconds': self.duration_seconds,
            'severity': self.severity.value,
            'interval_type': self.interval_type,
            'expected_count': self.expected_count,
            'actual_count': self.actual_count,
            'missing_count': self.missing_count,
            'gap_percentage': self.gap_percentage
        }


class DataGapDetector:
    """Detect and analyze gaps in financial time series data."""
    
    def __init__(self):
        self.intervals_per_timeframe = {
            '1m': 60,
            '5m': 300,
            '15m': 900,
            '30m': 1800,
            '1h': 3600,
            '4h': 14400,
            '1d': 86400
        }
        # Initialize market calendar for trading day detection
        self.market_calendar = MarketCalendar()
    
    def detect_gaps(
        self, 
        data: pd.DataFrame, 
        symbol: str, 
        interval_type: str = '1d'
    ) -> List[DataGap]:
        """
        Detect gaps in time series data, accounting for weekends and holidays.
        
        Args:
            data: DataFrame with datetime index
            symbol: Stock symbol for context
            interval_type: Time interval type (1m, 5m, 1h, 1d, etc.)
            
        Returns:
            List of DataGap objects
        """
        try:
            if data.empty:
                return []
            
            # Get data time range
            start_time = data.index.min()
            end_time = data.index.max()
            
            # For daily data, only expect trading days
            if interval_type == '1d':
                expected_intervals = self._calculate_expected_trading_days(start_time, end_time)
            else:
                expected_intervals = self._calculate_expected_intervals(
                    start_time, end_time, interval_type
                )
            
            # Find gaps
            gaps = self._find_gaps_in_data(
                data, symbol, interval_type, start_time, end_time, expected_intervals
            )
            
            logger.info(f"Detected {len(gaps)} gaps for {symbol} ({interval_type})")
            return gaps
            
        except Exception as e:
            logger.error(f"Error detecting gaps for {symbol}: {e}")
            return []
    
    def _calculate_expected_trading_days(
        self, 
        start_time: datetime, 
        end_time: datetime
    ) -> int:
        """Calculate expected number of trading days in the time range."""
        try:
            # Use market calendar to get actual trading days
            trading_days = self.market_calendar.get_trading_days(
                start_time.date(), 
                end_time.date(), 
                Exchange.NYSE
            )
            return len(trading_days)
        except Exception as e:
            logger.warning(f"Failed to get trading days from calendar: {e}")
            # Fallback: estimate trading days (exclude weekends)
            duration = end_time - start_time
            total_days = duration.days + 1
            # Rough estimate: exclude ~2/7 days for weekends
            estimated_trading_days = int(total_days * 5/7)
            return max(estimated_trading_days, 1)
    
    def _calculate_expected_intervals(
        self, 
        start_time: datetime, 
        end_time: datetime, 
        interval_type: str
    ) -> int:
        """Calculate expected number of intervals in the time range."""
        duration = end_time - start_time
        interval_seconds = self.intervals_per_timeframe.get(interval_type, 86400)
        
        # Add 1 to include both start and end boundaries
        expected_count = int(duration.total_seconds() / interval_seconds) + 1
        
        return max(expected_count, 1)
    
    def _find_gaps_in_data(
        self,
        data: pd.DataFrame,
        symbol: str,
        interval_type: str,
        start_time: datetime,
        end_time: datetime,
        expected_intervals: int
    ) -> List[DataGap]:
        """Find actual gaps in the data, accounting for trading days."""
        gaps = []
        
        try:
            # Sort data by timestamp
            sorted_data = data.sort_index()
            
            # Get the expected timestamps
            if interval_type == '1d':
                expected_timestamps = self._generate_expected_trading_timestamps(
                    start_time, end_time
                )
            else:
                expected_timestamps = self._generate_expected_timestamps(
                    start_time, end_time, interval_type
                )
            
            # Find missing timestamps
            actual_timestamps = set(sorted_data.index)
            missing_timestamps = set(expected_timestamps) - actual_timestamps
            
            if not missing_timestamps:
                return []
            
            # Group consecutive missing timestamps into gaps
            gaps = self._group_missing_timestamps_into_gaps(
                symbol, interval_type, missing_timestamps, expected_intervals
            )
            
        except Exception as e:
            logger.error(f"Error finding gaps in data for {symbol}: {e}")
        
        return gaps
    
    def _generate_expected_trading_timestamps(
        self, 
        start_time: datetime, 
        end_time: datetime
    ) -> List[datetime]:
        """Generate expected trading day timestamps."""
        try:
            # Get actual trading days from market calendar
            trading_dates = self.market_calendar.get_trading_days(
                start_time.date(), 
                end_time.date(), 
                Exchange.NYSE
            )
            
            # Convert to datetime timestamps (using 4:00 AM UTC as standard to match data)
            timestamps = []
            for trading_date in trading_dates:
                # Use 4:00 AM UTC to match the data format from Polygon
                timestamp = datetime.combine(
                    trading_date, 
                    time(4, 0), 
                    tzinfo=pytz.UTC
                )
                timestamps.append(timestamp)
            
            return timestamps
        except Exception as e:
            logger.warning(f"Failed to get trading timestamps from calendar: {e}")
            # Fallback: generate daily timestamps excluding weekends
            timestamps = []
            current = start_time
            while current <= end_time:
                # Only include weekdays (Monday = 0, Friday = 4)
                if current.weekday() < 5:  # Monday to Friday
                    timestamps.append(current)
                current += timedelta(days=1)
            return timestamps
    
    def _generate_expected_timestamps(
        self, 
        start_time: datetime, 
        end_time: datetime, 
        interval_type: str
    ) -> List[datetime]:
        """Generate expected timestamps for the given interval type."""
        timestamps = []
        current = start_time
        interval_seconds = self.intervals_per_timeframe.get(interval_type, 86400)
        
        while current <= end_time:
            timestamps.append(current)
            current += timedelta(seconds=interval_seconds)
        
        return timestamps
    
    def _group_missing_timestamps_into_gaps(
        self,
        symbol: str,
        interval_type: str,
        missing_timestamps: set,
        expected_intervals: int
    ) -> List[DataGap]:
        """Group consecutive missing timestamps into DataGap objects."""
        if not missing_timestamps:
            return []
        
        gaps = []
        sorted_missing = sorted(missing_timestamps)
        
        # Find consecutive ranges
        gap_start = sorted_missing[0]
        prev_timestamp = gap_start
        
        for timestamp in sorted_missing[1:]:
            # Check if this timestamp is consecutive
            expected_next = prev_timestamp + timedelta(
                seconds=self.intervals_per_timeframe.get(interval_type, 86400)
            )
            
            if timestamp != expected_next:
                # End of current gap, start new one
                gap = self._create_gap_object(
                    symbol, interval_type, gap_start, prev_timestamp, 
                    expected_intervals, len(sorted_missing)
                )
                gaps.append(gap)
                gap_start = timestamp
            
            prev_timestamp = timestamp
        
        # Add the last gap
        gap = self._create_gap_object(
            symbol, interval_type, gap_start, prev_timestamp,
            expected_intervals, len(sorted_missing)
        )
        gaps.append(gap)
        
        return gaps
    
    def _create_gap_object(
        self,
        symbol: str,
        interval_type: str,
        start_time: datetime,
        end_time: datetime,
        expected_intervals: int,
        total_missing: int
    ) -> DataGap:
        """Create a DataGap object with calculated severity."""
        duration = (end_time - start_time).total_seconds()
        missing_count = int(duration / self.intervals_per_timeframe.get(interval_type, 86400)) + 1
        
        # Determine severity based on duration
        severity = self._determine_gap_severity(duration)
        
        return DataGap(
            symbol=symbol,
            start_time=start_time,
            end_time=end_time,
            duration_seconds=duration,
            severity=severity,
            interval_type=interval_type,
            expected_count=expected_intervals,
            actual_count=expected_intervals - total_missing,
            missing_count=missing_count,
            gap_percentage=(missing_count / expected_intervals * 100) if expected_intervals > 0 else 0
        )
    
    def _determine_gap_severity(self, duration_seconds: float) -> GapSeverity:
        """Determine the severity of a gap based on its duration."""
        if duration_seconds <= 300:  # ≤5 minutes
            return GapSeverity.MINOR
        elif duration_seconds <= 1800:  # ≤30 minutes
            return GapSeverity.MODERATE
        elif duration_seconds <= 7200:  # ≤2 hours
            return GapSeverity.MAJOR
        else:  # >2 hours
            return GapSeverity.CRITICAL
    
    def assess_data_quality(
        self, 
        data: pd.DataFrame, 
        symbol: str, 
        interval_type: str = '1d'
    ) -> Dict[str, Union[float, str, List[DataGap]]]:
        """
        Assess overall data quality including gaps and completeness.
        
        Returns:
            Dictionary with quality metrics and gap information
        """
        try:
            if data.empty:
                return {
                    'quality_score': 0.0,
                    'completeness': 0.0,
                    'gap_count': 0,
                    'gaps': [],
                    'status': 'no_data',
                    'recommendations': ['No data available for analysis']
                }
            
            # Detect gaps
            gaps = self.detect_gaps(data, symbol, interval_type)
            
            # Calculate quality metrics
            total_expected = len(data) + sum(gap.missing_count for gap in gaps)
            actual_count = len(data)
            completeness = (actual_count / total_expected * 100) if total_expected > 0 else 0
            
            # Calculate quality score (0-100)
            quality_score = self._calculate_quality_score(completeness, gaps)
            
            # Generate recommendations
            recommendations = self._generate_quality_recommendations(gaps, completeness)
            
            return {
                'quality_score': quality_score,
                'completeness': completeness,
                'gap_count': len(gaps),
                'gaps': [gap.to_dict() for gap in gaps],
                'total_expected': total_expected,
                'actual_count': actual_count,
                'status': self._get_quality_status(quality_score),
                'recommendations': recommendations,
                'severity_breakdown': self._get_severity_breakdown(gaps)
            }
            
        except Exception as e:
            logger.error(f"Error assessing data quality for {symbol}: {e}")
            return {
                'quality_score': 0.0,
                'completeness': 0.0,
                'gap_count': 0,
                'gaps': [],
                'status': 'error',
                'recommendations': [f'Error assessing quality: {str(e)}']
            }
    
    def _calculate_quality_score(self, completeness: float, gaps: List[DataGap]) -> float:
        """Calculate overall quality score (0-100)."""
        base_score = completeness
        
        # Penalize for gaps based on severity
        penalty = 0
        for gap in gaps:
            if gap.severity == GapSeverity.CRITICAL:
                penalty += 20
            elif gap.severity == GapSeverity.MAJOR:
                penalty += 10
            elif gap.severity == GapSeverity.MODERATE:
                penalty += 5
            elif gap.severity == GapSeverity.MINOR:
                penalty += 2
        
        # Apply penalty and ensure score is between 0-100
        final_score = max(0, min(100, base_score - penalty))
        return round(final_score, 1)
    
    def _get_quality_status(self, quality_score: float) -> str:
        """Get human-readable quality status."""
        if quality_score >= 90:
            return 'excellent'
        elif quality_score >= 80:
            return 'good'
        elif quality_score >= 70:
            return 'fair'
        elif quality_score >= 50:
            return 'poor'
        else:
            return 'very_poor'
    
    def _get_severity_breakdown(self, gaps: List[DataGap]) -> Dict[str, int]:
        """Get count of gaps by severity level."""
        breakdown = {severity.value: 0 for severity in GapSeverity}
        for gap in gaps:
            breakdown[gap.severity.value] += 1
        return breakdown
    
    def _generate_quality_recommendations(
        self, 
        gaps: List[DataGap], 
        completeness: float
    ) -> List[str]:
        """Generate recommendations for improving data quality."""
        recommendations = []
        
        if completeness < 80:
            recommendations.append("Data completeness is below 80% - consider refreshing from alternative sources")
        
        critical_gaps = [g for g in gaps if g.severity == GapSeverity.CRITICAL]
        if critical_gaps:
            recommendations.append(f"Critical gaps detected: {len(critical_gaps)} periods with >2 hours missing data")
        
        major_gaps = [g for g in gaps if g.severity == GapSeverity.MAJOR]
        if major_gaps:
            recommendations.append(f"Major gaps detected: {len(major_gaps)} periods with 30min-2hr missing data")
        
        if not gaps and completeness >= 95:
            recommendations.append("Data quality is excellent - no gaps detected")
        
        return recommendations


# Global gap detector instance
gap_detector = DataGapDetector()

# Backwards-compatible alias expected by tests and other modules
GapDetector = DataGapDetector


def detect_data_gaps(
    data: pd.DataFrame, 
    symbol: str, 
    interval_type: str = '1d'
) -> List[DataGap]:
    """Convenience function to detect gaps in data."""
    return gap_detector.detect_gaps(data, symbol, interval_type)


def assess_data_quality(
    data: pd.DataFrame, 
    symbol: str, 
    interval_type: str = '1d'
) -> Dict[str, Union[float, str, List[DataGap]]]:
    """Convenience function to assess data quality."""
    return gap_detector.assess_data_quality(data, symbol, interval_type) 