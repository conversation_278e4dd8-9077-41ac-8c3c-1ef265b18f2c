"""
Error handling utilities for the trading automation system.
Provides comprehensive error handling, fallback mechanisms, retry logic, and logging.
"""

from .fallback import FallbackManager, with_fallback
from .retry import retry_with_backoff, RetryConfig, RetryManager
from .logging import setup_error_logging, get_error_logger, with_error_logging

__all__ = [
    'FallbackManager',
    'with_fallback',
    'retry_with_backoff',
    'RetryConfig',
    'RetryManager',
    'setup_error_logging',
    'get_error_logger',
    'with_error_logging'
]