"""
Comprehensive error logging setup with configurable levels and integration with centralized configuration.
Provides structured logging with error context, correlation tracking, and specialized error handlers.
"""

import logging
import os
import json
import time
import uuid
from typing import Optional, Dict, Any, Callable
from datetime import datetime
from functools import wraps

# Import the new unified configuration system
try:
    from src.core.config_manager import config
    def get_config():
        return config
except ImportError:
    # Fallback to environment-based configuration
    def get_config():
        class FallbackConfig:
            def get(self, key, default=None):
                return os.environ.get(key, default)
        return FallbackConfig()

class ErrorLogFormatter(logging.Formatter):
    """
    Custom formatter for error logs with structured JSON output.
    Includes error context, stack traces, and correlation information.
    """
    
    def format(self, record: logging.LogRecord) -> str:
        log_record: Dict[str, Any] = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'process_id': record.process,
            'thread_id': record.thread,
        }
        
        # Add error-specific context
        if hasattr(record, 'error_type'):
            log_record['error_type'] = getattr(record, 'error_type')
        if hasattr(record, 'error_message'):
            log_record['error_message'] = getattr(record, 'error_message')
        if hasattr(record, 'stack_trace'):
            log_record['stack_trace'] = getattr(record, 'stack_trace')
        
        # Add correlation and request context
        if hasattr(record, 'correlation_id'):
            log_record['correlation_id'] = getattr(record, 'correlation_id')
        if hasattr(record, 'request_id'):
            log_record['request_id'] = getattr(record, 'request_id')
        if hasattr(record, 'user_id'):
            log_record['user_id'] = getattr(record, 'user_id')
        
        # Add any extra context
        if hasattr(record, 'extra_context'):
            extra_context = getattr(record, 'extra_context', {})
            log_record.update(extra_context)
        
        return json.dumps(log_record)

def setup_error_logging(
    log_level: Optional[str] = None,
    log_file: Optional[str] = None,
    enable_console: bool = True
) -> logging.Logger:
    """
    Set up comprehensive error logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to error log file (optional)
        enable_console: Whether to enable console logging
    
    Returns:
        Configured root logger
    """
    config = get_config()
    
    # Get configuration with defaults using new access pattern
    level_name = log_level or config.get('app', 'log_level', 'ERROR')
    log_file_path = log_file or config.get('app', 'log_file', 'logs/error.log')
    max_file_size = int(config.get('app', 'log_max_size', 10 * 1024 * 1024))  # 10MB default
    backup_count = int(config.get('app', 'log_backup_count', 5))
    
    # Parse log level
    numeric_level = getattr(logging, level_name.upper(), logging.ERROR)
    
    # Ensure log directory exists
    if log_file_path:
        log_dir = os.path.dirname(log_file_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Clear existing handlers to avoid duplicates
    root_logger.handlers.clear()
    
    # File handler for error logs
    if log_file_path:
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            filename=log_file_path,
            maxBytes=max_file_size,
            backupCount=backup_count
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(ErrorLogFormatter())
        root_logger.addHandler(file_handler)
    
    # Console handler for development
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(ErrorLogFormatter())
        root_logger.addHandler(console_handler)
    
    # Suppress overly verbose loggers from external libraries
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('httpcore').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    
    return root_logger

def get_error_logger(name: str) -> logging.Logger:
    """
    Get a logger instance configured for error logging.
    
    Args:
        name: Logger name (typically __name__)
    
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)

def log_error_with_context(
    logger: logging.Logger,
    level: int,
    message: str,
    error: Optional[Exception] = None,
    correlation_id: Optional[str] = None,
    extra_context: Optional[Dict[str, Any]] = None,
    **kwargs
) -> None:
    """
    Log an error with comprehensive context information.
    
    Args:
        logger: Logger instance
        level: Logging level
        message: Log message
        error: Exception object (optional)
        correlation_id: Correlation ID for tracing
        extra_context: Additional context data
        kwargs: Additional key-value pairs to include in log
    """
    extra = extra_context or {}
    extra.update(kwargs)
    
    if correlation_id:
        extra['correlation_id'] = correlation_id
    
    if error:
        import traceback
        extra['error_type'] = type(error).__name__
        extra['error_message'] = str(error)
        extra['stack_trace'] = traceback.format_exc()
    
    logger.log(level, message, extra=extra)

def generate_correlation_id() -> str:
    """Generate a unique correlation ID for error tracing"""
    return str(uuid.uuid4())

class ErrorContextLogger:
    """
    Context manager for logging operations with error context and timing.
    """
    
    def __init__(self, logger: logging.Logger, operation_name: str, **context):
        self.logger = logger
        self.operation_name = operation_name
        self.context = context
        self.correlation_id = generate_correlation_id()
        self.start_time = time.time()
    
    def __enter__(self):
        self.logger.info(
            f"Operation started: {self.operation_name}",
            extra={
                'correlation_id': self.correlation_id,
                'operation': self.operation_name,
                'event_type': 'operation_start',
                'timestamp': self.start_time,
                **self.context
            }
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = time.time()
        execution_time = end_time - self.start_time
        
        if exc_type:
            log_error_with_context(
                self.logger,
                logging.ERROR,
                f"Operation failed: {self.operation_name}",
                error=exc_val,
                correlation_id=self.correlation_id,
                operation=self.operation_name,
                execution_time=execution_time,
                event_type='operation_error',
                timestamp=end_time,
                **self.context
            )
        else:
            self.logger.info(
                f"Operation completed: {self.operation_name}",
                extra={
                    'correlation_id': self.correlation_id,
                    'operation': self.operation_name,
                    'execution_time': execution_time,
                    'event_type': 'operation_success',
                    'timestamp': end_time,
                    **self.context
                }
            )
        
        # Don't suppress exceptions
        return False

def with_error_logging(operation_name: Optional[str] = None):
    """
    Decorator to add comprehensive error logging to functions.
    
    Args:
        operation_name: Name of the operation (defaults to function name)
    """
    def decorator(func):
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            nonlocal operation_name
            if operation_name is None:
                operation_name = func.__name__
            
            logger = get_error_logger(func.__module__)
            
            with ErrorContextLogger(logger, operation_name, 
                                  function=func.__name__,
                                  args_count=len(args),
                                  kwargs_keys=list(kwargs.keys())):
                return func(*args, **kwargs)
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            nonlocal operation_name
            if operation_name is None:
                operation_name = func.__name__
            
            logger = get_error_logger(func.__module__)
            
            with ErrorContextLogger(logger, operation_name,
                                  function=func.__name__,
                                  args_count=len(args),
                                  kwargs_keys=list(kwargs.keys())):
                return await func(*args, **kwargs)
        
        # Return the appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# Initialize error logging when module is imported
try:
    config_obj = get_config()
    error_logger = setup_error_logging(
        log_level=config_obj.get('app', 'log_level', 'ERROR'),
        log_file=config_obj.get('app', 'log_file', 'logs/error.log'),
        enable_console=config_obj.get('app', 'log_file_enabled', True)
    )
except Exception as e:
    # Fallback initialization if configuration fails
    try:
        error_logger = setup_error_logging(
            log_level='ERROR',
            log_file=None,  # Don't try to create log files
            enable_console=True
        )
    except Exception:
        # Ultimate fallback - just create a basic logger
        error_logger = logging.getLogger('error_logger')
        error_logger.setLevel(logging.ERROR)
        if not error_logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
            error_logger.addHandler(handler)