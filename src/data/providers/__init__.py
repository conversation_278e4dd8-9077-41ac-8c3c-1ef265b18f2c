"""
Unified Data Provider System

Consolidates all data provider implementations into a single, coherent system
with standardized interfaces, failover, and monitoring.
Updated to use consolidated providers.
"""

from typing import List, Dict, Any

# Base classes
from .base import BaseDataProvider, MarketDataResponse

# Market Data Providers - Using consolidated implementations
from src.api.data.providers.finnhub import FinnhubProvider
from src.api.data.providers.polygon import PolygonProvider
from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
from src.shared.data_providers.yfinance_provider import YFinanceProvider

__all__ = [
    'BaseDataProvider',
    'MarketDataResponse',
    'FinnhubProvider',
    'PolygonProvider',
    'AlphaVantageProvider',
    'YFinanceProvider'
]