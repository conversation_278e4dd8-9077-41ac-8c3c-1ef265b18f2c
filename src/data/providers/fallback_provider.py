"""
Fallback Data Provider

Provides graceful degradation when primary data providers fail.
Includes cached data, mock data, and error handling mechanisms.
"""

import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class FallbackData:
    """Fallback data structure"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: datetime
    source: str
    is_stale: bool = False
    age_minutes: int = 0

class FallbackDataProvider:
    """Provides fallback data when primary providers fail"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 3600  # 1 hour cache for fallback data
        self.mock_data_enabled = True
        
        # Sample fallback data for common symbols
        self.sample_data = {
            "AAPL": {"price": 175.00, "change": 2.50, "change_percent": 1.45, "volume": 50000000},
            "MSFT": {"price": 380.00, "change": -1.20, "change_percent": -0.32, "volume": 25000000},
            "GOOGL": {"price": 140.00, "change": 0.80, "change_percent": 0.57, "volume": 30000000},
            "TSLA": {"price": 250.00, "change": 5.00, "change_percent": 2.04, "volume": 75000000},
            "NVDA": {"price": 450.00, "change": 10.00, "change_percent": 2.27, "volume": 40000000},
            "AMZN": {"price": 145.00, "change": -0.50, "change_percent": -0.34, "volume": 35000000},
            "META": {"price": 320.00, "change": 3.00, "change_percent": 0.95, "volume": 20000000},
            "NFLX": {"price": 420.00, "change": -2.00, "change_percent": -0.47, "volume": 15000000}
        }
    
    def get_cached_data(self, symbol: str) -> Optional[FallbackData]:
        """Get cached data for a symbol"""
        cache_key = f"fallback_{symbol}"
        
        if cache_key in self.cache:
            cached_item = self.cache[cache_key]
            age = time.time() - cached_item['timestamp']
            
            if age < self.cache_ttl:
                data = cached_item['data']
                age_minutes = int(age / 60)
                
                return FallbackData(
                    symbol=symbol,
                    price=data['price'],
                    change=data['change'],
                    change_percent=data['change_percent'],
                    volume=data['volume'],
                    timestamp=datetime.fromtimestamp(cached_item['timestamp']),
                    source="cached_fallback",
                    is_stale=age_minutes > 15,
                    age_minutes=age_minutes
                )
        
        return None
    
    def cache_data(self, symbol: str, data: Dict[str, Any]):
        """Cache data for fallback use"""
        cache_key = f"fallback_{symbol}"
        self.cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
    
    def get_mock_data(self, symbol: str) -> FallbackData:
        """Generate mock data for a symbol"""
        if not self.mock_data_enabled:
            raise Exception("Mock data is disabled")
        
        # Use sample data if available, otherwise generate
        if symbol in self.sample_data:
            base_data = self.sample_data[symbol].copy()
        else:
            # Generate reasonable mock data
            base_data = {
                "price": 100.00,
                "change": 0.00,
                "change_percent": 0.00,
                "volume": 1000000
            }
        
        # Add some realistic variation
        import random
        price_variation = random.uniform(-0.05, 0.05)  # ±5% variation
        base_data["price"] *= (1 + price_variation)
        base_data["change"] = base_data["price"] * price_variation
        base_data["change_percent"] = price_variation * 100
        
        return FallbackData(
            symbol=symbol,
            price=round(base_data["price"], 2),
            change=round(base_data["change"], 2),
            change_percent=round(base_data["change_percent"], 2),
            volume=base_data["volume"],
            timestamp=datetime.now(),
            source="mock_fallback",
            is_stale=False,
            age_minutes=0
        )
    
    def get_fallback_data(self, symbol: str, prefer_cached: bool = True) -> FallbackData:
        """Get fallback data for a symbol"""
        logger.warning(f"🔄 Using fallback data for {symbol}")
        
        # Try cached data first if preferred
        if prefer_cached:
            cached = self.get_cached_data(symbol)
            if cached:
                logger.info(f"📦 Using cached fallback data for {symbol} (age: {cached.age_minutes}m)")
                return cached
        
        # Fall back to mock data
        if self.mock_data_enabled:
            mock_data = self.get_mock_data(symbol)
            logger.warning(f"🎭 Using mock data for {symbol} - price: ${mock_data.price}")
            return mock_data
        
        # Last resort: minimal data
        logger.error(f"❌ No fallback data available for {symbol}")
        return FallbackData(
            symbol=symbol,
            price=0.0,
            change=0.0,
            change_percent=0.0,
            volume=0,
            timestamp=datetime.now(),
            source="minimal_fallback",
            is_stale=True,
            age_minutes=999
        )
    
    def to_dict(self, fallback_data: FallbackData) -> Dict[str, Any]:
        """Convert FallbackData to dictionary format"""
        return {
            "symbol": fallback_data.symbol,
            "current_price": fallback_data.price,
            "price": fallback_data.price,
            "change": fallback_data.change,
            "change_percent": fallback_data.change_percent,
            "volume": fallback_data.volume,
            "timestamp": fallback_data.timestamp.isoformat(),
            "source": fallback_data.source,
            "provider": "fallback",
            "status": "fallback",
            "data_stale": fallback_data.is_stale,
            "data_age_minutes": fallback_data.age_minutes,
            "data_suspicious": True,  # Mark fallback data as suspicious
            "suspicious_reasons": [f"Using {fallback_data.source} data"],
            "high": fallback_data.price,
            "low": fallback_data.price,
            "open": fallback_data.price,
            "close": fallback_data.price,
            "previous_close": fallback_data.price - fallback_data.change
        }
    
    def get_market_indices_fallback(self) -> Dict[str, Any]:
        """Get fallback market indices data"""
        logger.warning("🔄 Using fallback market indices data")
        
        indices_data = {
            '^GSPC': {'current_price': 4500.00, 'change_percent': 0.5},
            '^IXIC': {'current_price': 14000.00, 'change_percent': 0.3},
            '^RUT': {'current_price': 2000.00, 'change_percent': -0.2}
        }
        
        return {
            'market_indices': indices_data,
            'timestamp': datetime.now().isoformat(),
            'source': 'fallback',
            'status': 'fallback',
            'data_suspicious': True,
            'suspicious_reasons': ['Using fallback market data']
        }
    
    def disable_mock_data(self):
        """Disable mock data generation"""
        self.mock_data_enabled = False
        logger.info("🚫 Mock data disabled")
    
    def enable_mock_data(self):
        """Enable mock data generation"""
        self.mock_data_enabled = True
        logger.info("✅ Mock data enabled")
    
    def clear_cache(self):
        """Clear all cached fallback data"""
        self.cache.clear()
        logger.info("🗑️ Fallback cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_items = len(self.cache)
        current_time = time.time()
        
        fresh_items = 0
        stale_items = 0
        
        for item in self.cache.values():
            age = current_time - item['timestamp']
            if age < 900:  # 15 minutes
                fresh_items += 1
            else:
                stale_items += 1
        
        return {
            "total_cached_items": total_items,
            "fresh_items": fresh_items,
            "stale_items": stale_items,
            "cache_ttl_seconds": self.cache_ttl,
            "mock_data_enabled": self.mock_data_enabled
        }

# Global fallback provider instance
fallback_provider = FallbackDataProvider()

def get_fallback_provider() -> FallbackDataProvider:
    """Get the global fallback provider instance"""
    return fallback_provider

def get_fallback_data_for_symbol(symbol: str) -> Dict[str, Any]:
    """Get fallback data for a symbol in dictionary format"""
    fallback_data = fallback_provider.get_fallback_data(symbol)
    return fallback_provider.to_dict(fallback_data)

def cache_successful_data(symbol: str, data: Dict[str, Any]):
    """Cache successful data for future fallback use"""
    fallback_provider.cache_data(symbol, {
        'price': data.get('current_price', data.get('price', 0)),
        'change': data.get('change', 0),
        'change_percent': data.get('change_percent', 0),
        'volume': data.get('volume', 0)
    })
