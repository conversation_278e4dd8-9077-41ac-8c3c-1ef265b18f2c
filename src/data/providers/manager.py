"""
Data Provider Manager

Manages multiple data providers with intelligent failover, load balancing,
and health monitoring. This consolidates the scattered provider management
logic into a single, authoritative system.
"""

import asyncio
from typing import Dict, List, Optional, Any, Type
from datetime import datetime, timedelta


from src.core.logger import get_logger
from src.core.config_manager import get_config
from src.core.exceptions import DataProviderError

from .base import (
    BaseDataProvider,
    DataProviderConfig,
    MarketDataRequest,
    MarketDataResponse,
    ProviderStatus
)

from src.api.data.providers.finnhub import FinnhubProvider
from src.api.data.providers.polygon import PolygonProvider
from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
from src.shared.data_providers.yfinance_provider import YFinanceProvider

logger = get_logger(__name__)
config = get_config()


class DataProviderManager:
    """
    Centralized manager for market data providers.
    Handles provider selection, initialization, and fallback mechanisms.
    """
    
    def __init__(self, providers: Optional[List[Type]] = None):
        """
        Initialize data provider manager.
        
        Args:
            providers: Optional list of provider classes to use. 
                       Defaults to standard set of providers.
        """
        self.providers = providers or [
            FinnhubProvider,
            PolygonProvider,
            AlphaVantageProvider,
            YFinanceProvider
        ]
        
        self._initialized_providers = []
        self.request_history = {}
    
    def initialize_providers(self):
        """
        Initialize all configured providers.
        Handles potential initialization failures gracefully.
        """
        for provider_cls in self.providers:
            try:
                provider = provider_cls()
                if provider.is_configured():
                    self._initialized_providers.append(provider)
            except Exception as e:
                print(f"Failed to initialize {provider_cls.__name__}: {e}")
    
    async def _test_provider_health(self, provider: BaseDataProvider) -> bool:
        """Test if a provider is healthy and responsive"""
        try:
            # Test with a common symbol
            test_request = MarketDataRequest(symbol="AAPL", data_types=["price"])
            response = await asyncio.wait_for(
                provider._fetch_data(test_request), 
                timeout=5.0
            )
            return response.is_valid
        except Exception as e:
            logger.debug(f"Provider {provider.name} health check failed: {e}")
            return False
    
    def add_provider(self, provider: BaseDataProvider):
        """Add a data provider"""
        self.providers[provider.name] = provider
        logger.info(f"➕ Added data provider: {provider.name}")
    
    def remove_provider(self, provider_name: str):
        """Remove a data provider"""
        if provider_name in self.providers:
            del self.providers[provider_name]
            logger.info(f"➖ Removed data provider: {provider_name}")
    
    def get_provider(self, provider_name: str) -> Optional[BaseDataProvider]:
        """Get a specific provider by name"""
        return self.providers.get(provider_name)
    
    def get_healthy_providers(self) -> List[BaseDataProvider]:
        """Get list of healthy providers sorted by priority"""
        healthy = []
        for provider in self.providers:
            # For now, consider all providers healthy if they exist
            if provider is not None:
                healthy.append(provider)
        return healthy
    
    def get_best_provider(self, symbol: str) -> Optional[BaseDataProvider]:
        """Get the best provider for a specific symbol"""
        healthy_providers = self.get_healthy_providers()
        
        if not healthy_providers:
            return None
        
        # Simple strategy: return highest priority healthy provider
        # Could be enhanced with symbol-specific provider preferences
        return healthy_providers[0]
    
    async def get_market_data(
        self,
        symbol: str,
        data_types: Optional[List[str]] = None,
        preferred_provider: Optional[str] = None
    ) -> MarketDataResponse:
        """
        Get market data with automatic failover
        
        Args:
            symbol: Stock symbol
            data_types: Types of data to fetch
            preferred_provider: Preferred provider name
            
        Returns:
            MarketDataResponse: Market data response
            
        Raises:
            DataProviderError: If all providers fail
        """
        # Normalize symbol
        symbol = BaseDataProvider.normalize_symbol(symbol)
        
        # Create request
        request = MarketDataRequest(
            symbol=symbol,
            data_types=data_types or ["price", "volume"]
        )
        
        # Track request
        if symbol not in self.request_history:
            self.request_history[symbol] = []
        self.request_history[symbol].append(datetime.now())
        # Skip metrics for now as it's not implemented
        # metrics_collector.increment_counter("data_requests", labels={"symbol": symbol})
        
        # Get provider list
        if preferred_provider and preferred_provider in self.providers:
            providers_to_try = [self.providers[preferred_provider]]
            providers_to_try.extend([p for p in self.get_healthy_providers() if p.name != preferred_provider])
        else:
            providers_to_try = self.get_healthy_providers()
        
        if not providers_to_try:
            raise DataProviderError("No healthy data providers available")
        
        # Try providers in order
        last_error = None
        for provider in providers_to_try:
            try:
                logger.debug(f"🔄 Trying provider {provider.name} for {symbol}")
                
                response = await provider.get_market_data(request)
                
                # Record successful request
                # Skip metrics for now as it's not implemented
                # metrics_collector.increment_counter(
                #     "data_requests_success",
                #     labels={"provider": provider.name, "symbol": symbol}
                # )
                
                logger.debug(f"✅ Successfully got data for {symbol} from {provider.name}")
                return response
                
            except Exception as e:
                last_error = e
                logger.warning(f"⚠️ Provider {provider.name} failed for {symbol}: {e}")
                
                # Record failed request
                # Skip metrics for now as it's not implemented
                # metrics_collector.increment_counter(
                #     "data_requests_failed",
                #     labels={"provider": provider.name, "symbol": symbol}
                # )
                
                # Continue to next provider
                continue
        
        # All providers failed
        error_msg = f"All data providers failed for symbol {symbol}"
        if last_error:
            error_msg += f". Last error: {last_error}"
        
        # Skip metrics for now as it's not implemented
        # metrics_collector.increment_counter("data_requests_all_failed", labels={"symbol": symbol})
        raise DataProviderError(error_msg, symbol=symbol)
    
    async def get_multiple_symbols(
        self,
        symbols: List[str],
        data_types: Optional[List[str]] = None,
        max_concurrent: int = 5
    ) -> Dict[str, MarketDataResponse]:
        """
        Get market data for multiple symbols concurrently
        
        Args:
            symbols: List of stock symbols
            data_types: Types of data to fetch
            max_concurrent: Maximum concurrent requests
            
        Returns:
            Dict mapping symbols to their market data responses
        """
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def fetch_symbol(symbol: str) -> tuple[str, Optional[MarketDataResponse]]:
            async with semaphore:
                try:
                    response = await self.get_market_data(symbol, data_types)
                    return symbol, response
                except Exception as e:
                    logger.error(f"Failed to fetch data for {symbol}: {e}")
                    return symbol, None
        
        # Execute requests concurrently
        tasks = [fetch_symbol(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        data = {}
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Task failed: {result}")
                continue
            
            symbol, response = result
            if response:
                data[symbol] = response
        
        logger.info(f"📊 Fetched data for {len(data)}/{len(symbols)} symbols")
        return data
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all providers"""
        self.last_health_check = datetime.now()
        
        health_data = {
            "timestamp": self.last_health_check.isoformat(),
            "total_providers": len(self.providers),
            "providers": {}
        }
        
        healthy_count = 0
        for provider in self.providers.values():
            provider_health = provider.get_health_status()
            health_data["providers"][provider.name] = provider_health
            
            if provider.status == ProviderStatus.HEALTHY:
                healthy_count += 1
        
        health_data["healthy_providers"] = healthy_count
        health_data["health_percentage"] = (healthy_count / len(self.providers)) * 100 if self.providers else 0
        
        # Log health status
        if healthy_count == 0:
            logger.error("❌ No healthy data providers available")
        elif healthy_count < len(self.providers):
            logger.warning(f"⚠️ Only {healthy_count}/{len(self.providers)} providers are healthy")
        else:
            logger.info(f"✅ All {healthy_count} data providers are healthy")
        
        return health_data
    
    def get_provider_statistics(self) -> Dict[str, Any]:
        """Get comprehensive provider statistics"""
        stats = {
            "total_providers": len(self.providers),
            "enabled_providers": len([p for p in self.providers.values() if p.config.enabled]),
            "healthy_providers": len(self.get_healthy_providers()),
            "provider_details": {},
            "request_history": {}
        }
        
        # Provider details
        for provider in self.providers.values():
            stats["provider_details"][provider.name] = provider.get_health_status()
        
        # Request history summary
        for symbol, requests in self.request_history.items():
            # Keep only recent requests (last 24 hours)
            recent_requests = [
                req for req in requests
                if datetime.now() - req < timedelta(hours=24)
            ]
            self.request_history[symbol] = recent_requests
            
            if recent_requests:
                stats["request_history"][symbol] = {
                    "count": len(recent_requests),
                    "last_request": max(recent_requests).isoformat()
                }
        
        return stats
    
    def reset_provider_errors(self):
        """Reset error counts for all providers"""
        for provider in self.providers.values():
            provider.reset_error_count()
        logger.info("🔄 Reset error counts for all providers")
    
    async def shutdown(self):
        """Shutdown all providers gracefully"""
        logger.info("🛑 Shutting down data provider manager")
        
        # Could add cleanup logic for providers if needed
        for provider in self.providers.values():
            logger.debug(f"Shutting down provider: {provider.name}")
        
        self.providers.clear()
        logger.info("✅ Data provider manager shutdown complete")


# Global data provider manager instance
data_provider_manager = DataProviderManager()