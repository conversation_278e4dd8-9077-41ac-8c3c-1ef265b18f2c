"""
Enhanced Data Provider Configuration Manager

Provides robust configuration management with dependency checking,
fallback mechanisms, and graceful degradation for data providers.
"""

import logging
import importlib
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ProviderStatus(Enum):
    """Provider availability status"""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    DEPENDENCY_MISSING = "dependency_missing"
    CONFIGURATION_ERROR = "configuration_error"
    RATE_LIMITED = "rate_limited"

@dataclass
class ProviderInfo:
    """Information about a data provider"""
    name: str
    status: ProviderStatus
    dependencies: List[str]
    config: Dict[str, Any]
    error_message: Optional[str] = None
    last_check: Optional[float] = None

class EnhancedDataProviderConfig:
    """Enhanced data provider configuration with dependency checking"""
    
    def __init__(self):
        self.provider_info: Dict[str, ProviderInfo] = {}
        self._dependency_cache: Dict[str, bool] = {}
        
        # Define provider dependencies
        self.provider_dependencies = {
            "yfinance": ["yfinance", "pandas", "numpy"],
            "alpha_vantage": ["httpx", "aiohttp"],
            "polygon": ["httpx", "aiohttp"],
            "finnhub": ["httpx", "aiohttp"]
        }
        
        # Default configurations
        self.default_configs = {
            "yfinance": {
                "enabled": True,
                "rate_limit": 5,
                "timeout": 30.0,
                "max_retries": 3,
                "cache_ttl": 300,
                "priority": 3  # Lower priority (fallback)
            },
            "alpha_vantage": {
                "enabled": True,
                "rate_limit": 5,
                "timeout": 10.0,
                "max_retries": 3,
                "cache_ttl": 300,
                "api_key": "",
                "priority": 2
            },
            "polygon": {
                "enabled": True,
                "rate_limit": 5,
                "timeout": 8.0,
                "max_retries": 3,
                "cache_ttl": 300,
                "api_key": "",
                "priority": 1  # Highest priority
            },
            "finnhub": {
                "enabled": True,
                "rate_limit": 30,
                "timeout": 5.0,
                "max_retries": 3,
                "cache_ttl": 300,
                "api_key": "",
                "priority": 2
            }
        }
        
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize all providers and check their status"""
        for provider_name in self.default_configs.keys():
            self._check_provider_status(provider_name)
    
    def _check_dependency(self, dependency: str) -> bool:
        """Check if a dependency is available"""
        if dependency in self._dependency_cache:
            return self._dependency_cache[dependency]
        
        try:
            importlib.import_module(dependency)
            self._dependency_cache[dependency] = True
            return True
        except ImportError:
            self._dependency_cache[dependency] = False
            return False
    
    def _check_provider_status(self, provider_name: str) -> ProviderInfo:
        """Check the status of a specific provider"""
        config = self.default_configs.get(provider_name, {})
        dependencies = self.provider_dependencies.get(provider_name, [])
        
        # Check dependencies
        missing_deps = []
        for dep in dependencies:
            if not self._check_dependency(dep):
                missing_deps.append(dep)
        
        if missing_deps:
            status = ProviderStatus.DEPENDENCY_MISSING
            error_msg = f"Missing dependencies: {', '.join(missing_deps)}"
        elif not config.get("enabled", True):
            status = ProviderStatus.UNAVAILABLE
            error_msg = "Provider disabled in configuration"
        elif provider_name != "yfinance" and not config.get("api_key"):
            status = ProviderStatus.CONFIGURATION_ERROR
            error_msg = "API key not configured"
        else:
            status = ProviderStatus.AVAILABLE
            error_msg = None
        
        provider_info = ProviderInfo(
            name=provider_name,
            status=status,
            dependencies=dependencies,
            config=config.copy(),
            error_message=error_msg
        )
        
        self.provider_info[provider_name] = provider_info
        return provider_info
    
    def get_available_providers(self) -> List[str]:
        """Get list of available provider names"""
        available = []
        for name, info in self.provider_info.items():
            if info.status == ProviderStatus.AVAILABLE:
                available.append(name)
        return available
    
    def get_providers_by_priority(self) -> List[str]:
        """Get providers sorted by priority (highest first)"""
        available = self.get_available_providers()
        return sorted(available, key=lambda x: self.provider_info[x].config.get("priority", 999))
    
    def get_fallback_chain(self) -> List[str]:
        """Get fallback chain for data providers"""
        return self.get_providers_by_priority()
    
    def get_provider_config(self, provider_name: str) -> Dict[str, Any]:
        """Get configuration for a specific provider"""
        if provider_name in self.provider_info:
            return self.provider_info[provider_name].config.copy()
        return self.default_configs.get(provider_name, {}).copy()
    
    def is_provider_available(self, provider_name: str) -> bool:
        """Check if a provider is available"""
        if provider_name not in self.provider_info:
            self._check_provider_status(provider_name)
        return self.provider_info[provider_name].status == ProviderStatus.AVAILABLE
    
    def get_provider_status_report(self) -> Dict[str, Any]:
        """Get comprehensive status report for all providers"""
        report = {
            "total_providers": len(self.provider_info),
            "available_providers": len(self.get_available_providers()),
            "fallback_chain": self.get_fallback_chain(),
            "provider_details": {}
        }
        
        for name, info in self.provider_info.items():
            report["provider_details"][name] = {
                "status": info.status.value,
                "dependencies": info.dependencies,
                "error_message": info.error_message,
                "priority": info.config.get("priority", 999),
                "enabled": info.config.get("enabled", False)
            }
        
        return report
    
    def refresh_provider_status(self, provider_name: Optional[str] = None):
        """Refresh status for one or all providers"""
        if provider_name:
            self._check_provider_status(provider_name)
        else:
            self._dependency_cache.clear()
            for name in self.default_configs.keys():
                self._check_provider_status(name)
    
    def get_yfinance_fallback_config(self) -> Dict[str, Any]:
        """Get special configuration for yfinance as fallback provider"""
        config = self.get_provider_config("yfinance")
        
        # Enhanced fallback configuration
        config.update({
            "is_fallback": True,
            "graceful_degradation": True,
            "error_tolerance": "high",
            "retry_strategy": "conservative",
            "cache_aggressive": True
        })
        
        return config

# Global instance
enhanced_config = EnhancedDataProviderConfig()

def get_enhanced_provider_config() -> EnhancedDataProviderConfig:
    """Get the global enhanced provider configuration instance"""
    return enhanced_config

def get_available_providers() -> List[str]:
    """Get list of available providers"""
    return enhanced_config.get_available_providers()

def get_fallback_chain() -> List[str]:
    """Get provider fallback chain"""
    return enhanced_config.get_fallback_chain()

def is_yfinance_available() -> bool:
    """Check if yfinance is available and working"""
    return enhanced_config.is_provider_available("yfinance")

def get_provider_status_summary() -> str:
    """Get a human-readable provider status summary"""
    report = enhanced_config.get_provider_status_report()
    available = report["available_providers"]
    total = report["total_providers"]
    chain = report["fallback_chain"]
    
    summary = f"Data Providers: {available}/{total} available"
    if chain:
        summary += f" | Fallback chain: {' → '.join(chain)}"
    else:
        summary += " | No providers available"
    
    return summary
