"""
Data Caching System

Unified caching system supporting multiple backends (memory, Redis)
with intelligent cache invalidation and warming strategies.
"""

import logging
import json
import time
import hashlib
import asyncio
from typing import Any, Dict, Optional, Union, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

# Import the new unified configuration and logging system
from src.core.config_manager import config
from src.core.logger import get_logger

logger = get_logger(__name__)


class CacheBackend(Enum):
    """Cache backend types"""
    MEMORY = "memory"
    REDIS = "redis"


@dataclass
class CacheConfig:
    """Cache configuration"""
    enabled: bool = True
    default_ttl: int = 300  # 5 minutes
    max_size: int = 1000
    backend: CacheBackend = CacheBackend.MEMORY
    redis_url: Optional[str] = None
    key_prefix: str = "trading_bot"


@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: datetime = field(default_factory=datetime.now)
    ttl: int = 300
    access_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.now)
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry is expired"""
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    @property
    def age_seconds(self) -> float:
        """Get age of cache entry in seconds"""
        return (datetime.now() - self.created_at).total_seconds()
    
    def access(self):
        """Record cache access"""
        self.access_count += 1
        self.last_accessed = datetime.now()


class MemoryCache:
    """In-memory cache implementation"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: Dict[str, CacheEntry] = {}
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        async with self._lock:
            entry = self.cache.get(key)
            if entry is None:
                return None
            
            if entry.is_expired:
                del self.cache[key]
                return None
            
            entry.access()
            return entry.value
    
    async def set(self, key: str, value: Any, ttl: int = 300):
        """Set value in cache"""
        async with self._lock:
            # Remove expired entries if cache is full
            if len(self.cache) >= self.max_size:
                await self._evict_expired()
                
                # If still full, evict least recently used
                if len(self.cache) >= self.max_size:
                    await self._evict_lru()
            
            self.cache[key] = CacheEntry(key=key, value=value, ttl=ttl)
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        async with self._lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    async def clear(self):
        """Clear all cache entries"""
        async with self._lock:
            self.cache.clear()
    
    async def _evict_expired(self):
        """Remove expired entries"""
        expired_keys = [
            key for key, entry in self.cache.items()
            if entry.is_expired
        ]
        for key in expired_keys:
            del self.cache[key]
    
    async def _evict_lru(self):
        """Remove least recently used entries"""
        if not self.cache:
            return
        
        # Sort by last accessed time and remove oldest
        sorted_entries = sorted(
            self.cache.items(),
            key=lambda x: x[1].last_accessed
        )
        
        # Remove 10% of entries
        num_to_remove = max(1, len(sorted_entries) // 10)
        for key, _ in sorted_entries[:num_to_remove]:
            del self.cache[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_entries = len(self.cache)
        expired_entries = sum(1 for entry in self.cache.values() if entry.is_expired)
        
        return {
            "backend": "memory",
            "total_entries": total_entries,
            "expired_entries": expired_entries,
            "max_size": self.max_size,
            "utilization": (total_entries / self.max_size) * 100 if self.max_size > 0 else 0
        }


class RedisCache:
    """Redis cache implementation (placeholder)"""
    
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.redis_client = None
        logger.warning("Redis cache not implemented yet, falling back to memory cache")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        # Placeholder - would implement Redis operations
        return None
    
    async def set(self, key: str, value: Any, ttl: int = 300):
        """Set value in Redis cache"""
        # Placeholder - would implement Redis operations
        pass
    
    async def delete(self, key: str) -> bool:
        """Delete value from Redis cache"""
        # Placeholder - would implement Redis operations
        return False
    
    async def clear(self):
        """Clear Redis cache"""
        # Placeholder - would implement Redis operations
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics"""
        return {
            "backend": "redis",
            "status": "not_implemented"
        }


class CacheManager:
    """
    Unified cache manager supporting multiple backends
    """
    
    def __init__(self, cache_config: Optional[CacheConfig] = None):
        # Get Redis URL from environment or config with proper fallback
        redis_url = None
        if config.get('app', 'redis_enabled', False):
            redis_url = config.get('app', 'cache_redis_url')
            if not redis_url:
                import os
                redis_url = os.getenv('REDIS_URL')
            if not redis_url:
                # Log warning and use default, but this may cause issues in production
                logger.warning("No Redis URL configured for cache, using environment default. This may cause issues in production.")
                redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
        
        self.config = cache_config or CacheConfig(
            enabled=config.get('app', 'cache_enabled', True),
            default_ttl=config.get('app', 'cache_ttl', 300),
            max_size=config.get('app', 'cache_max_size', 1000),
            redis_url=redis_url
        )
        
        # Initialize cache backend
        if self.config.backend == CacheBackend.REDIS and self.config.redis_url:
            self.cache = RedisCache(self.config.redis_url)
        else:
            self.cache = MemoryCache(self.config.max_size)
        
        # Cache statistics
        self.hit_count = 0
        self.miss_count = 0
        self.set_count = 0
        
        logger.info(f"🗄️ Cache manager initialized with {self.config.backend.value} backend")
    
    def _make_key(self, key: str) -> str:
        """Create cache key with prefix"""
        return f"{self.config.key_prefix}:{key}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if not self.config.enabled:
            return None
        
        cache_key = self._make_key(key)
        
        try:
            value = await self.cache.get(cache_key)
            
            if value is not None:
                self.hit_count += 1
                metrics_collector.increment_counter("cache_hits", labels={"key": key})
                logger.debug(f"🎯 Cache hit for key: {key}")
                return value
            else:
                self.miss_count += 1
                metrics_collector.increment_counter("cache_misses", labels={"key": key})
                logger.debug(f"❌ Cache miss for key: {key}")
                return None
                
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self.miss_count += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        if not self.config.enabled:
            return False
        
        cache_key = self._make_key(key)
        cache_ttl = ttl or self.config.default_ttl
        
        try:
            await self.cache.set(cache_key, value, cache_ttl)
            self.set_count += 1
            metrics_collector.increment_counter("cache_sets", labels={"key": key})
            logger.debug(f"💾 Cached value for key: {key} (TTL: {cache_ttl}s)")
            return True
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        if not self.config.enabled:
            return False
        
        cache_key = self._make_key(key)
        
        try:
            result = await self.cache.delete(cache_key)
            if result:
                metrics_collector.increment_counter("cache_deletes", labels={"key": key})
                logger.debug(f"🗑️ Deleted cache key: {key}")
            return result
            
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    async def clear(self):
        """Clear all cache entries"""
        if not self.config.enabled:
            return
        
        try:
            await self.cache.clear()
            self.hit_count = 0
            self.miss_count = 0
            self.set_count = 0
            logger.info("🧹 Cache cleared")
            
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
    
    def get_hit_rate(self) -> float:
        """Get cache hit rate"""
        total_requests = self.hit_count + self.miss_count
        return (self.hit_count / total_requests) * 100 if total_requests > 0 else 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        cache_stats = self.cache.get_stats()
        
        return {
            **cache_stats,
            "enabled": self.config.enabled,
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "set_count": self.set_count,
            "hit_rate": self.get_hit_rate(),
            "total_requests": self.hit_count + self.miss_count,
            "default_ttl": self.config.default_ttl
        }
    
    # Convenience methods for common cache patterns
    async def get_or_set(self, key: str, factory_func, ttl: Optional[int] = None) -> Any:
        """Get value from cache or set it using factory function"""
        value = await self.get(key)
        if value is not None:
            return value
        
        # Generate value using factory function
        if asyncio.iscoroutinefunction(factory_func):
            value = await factory_func()
        else:
            value = factory_func()
        
        if value is not None:
            await self.set(key, value, ttl)
        
        return value
    
    async def invalidate_pattern(self, pattern: str):
        """Invalidate cache entries matching pattern (memory cache only)"""
        if isinstance(self.cache, MemoryCache):
            keys_to_delete = []
            async with self.cache._lock:
                for key in self.cache.cache.keys():
                    if pattern in key:
                        keys_to_delete.append(key)
            
            for key in keys_to_delete:
                await self.cache.delete(key)
            
            logger.info(f"🔄 Invalidated {len(keys_to_delete)} cache entries matching pattern: {pattern}")
    
    # Enhanced caching strategies
    async def warm_cache(self, symbols: List[str], data_types: List[str] = None):
        """
        Warm cache for frequently requested symbols
        
        Args:
            symbols: List of symbols to pre-cache
            data_types: Types of data to cache (price, volume, etc.)
        """
        if not self.config.enabled:
            return
        
        data_types = data_types or ["price", "volume"]
        logger.info(f"🔥 Warming cache for {len(symbols)} symbols with {len(data_types)} data types")
        
        # Import here to avoid circular imports
        from ..providers.manager import data_provider_manager
        
        # Warm cache concurrently but with rate limiting
        semaphore = asyncio.Semaphore(3)  # Limit concurrent requests
        
        async def warm_symbol(symbol: str):
            async with semaphore:
                try:
                    # Get data which will automatically cache it
                    await data_provider_manager.get_market_data(symbol, data_types)
                    logger.debug(f"🔥 Warmed cache for {symbol}")
                except Exception as e:
                    logger.warning(f"Failed to warm cache for {symbol}: {e}")
        
        # Execute warming tasks
        tasks = [warm_symbol(symbol) for symbol in symbols]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"✅ Cache warming completed for {len(symbols)} symbols")
    
    async def get_cache_analytics(self) -> Dict[str, Any]:
        """Get detailed cache analytics and performance metrics"""
        stats = self.get_stats()
        
        # Add advanced analytics
        analytics = {
            **stats,
            "performance": {
                "hit_rate_percentage": self.get_hit_rate(),
                "miss_rate_percentage": 100 - self.get_hit_rate(),
                "efficiency_score": min(100, self.get_hit_rate() * 1.2),  # Bonus for high hit rates
            },
            "recommendations": []
        }
        
        # Generate recommendations based on performance
        hit_rate = self.get_hit_rate()
        if hit_rate < 30:
            analytics["recommendations"].append("Consider increasing cache TTL or warming cache for popular symbols")
        elif hit_rate > 80:
            analytics["recommendations"].append("Excellent cache performance! Consider expanding cache size")
        
        if isinstance(self.cache, MemoryCache):
            utilization = (len(self.cache.cache) / self.cache.max_size) * 100
            analytics["utilization_percentage"] = utilization
            
            if utilization > 90:
                analytics["recommendations"].append("Cache is near capacity, consider increasing max_size")
            elif utilization < 20:
                analytics["recommendations"].append("Cache is underutilized, could reduce max_size")
        
        return analytics
    
    async def optimize_cache(self):
        """
        Automatically optimize cache based on usage patterns
        """
        if not isinstance(self.cache, MemoryCache):
            logger.info("Cache optimization only available for memory cache")
            return
        
        logger.info("🔧 Starting cache optimization...")
        
        # Analyze access patterns
        async with self.cache._lock:
            entries = list(self.cache.cache.values())
        
        if not entries:
            logger.info("No cache entries to optimize")
            return
        
        # Find frequently accessed entries
        frequent_entries = [e for e in entries if e.access_count > 5]
        stale_entries = [e for e in entries if e.age_seconds > 3600]  # 1 hour old
        
        logger.info(f"📊 Cache analysis: {len(frequent_entries)} frequent, {len(stale_entries)} stale entries")
        
        # Remove stale entries that haven't been accessed recently
        removed_count = 0
        for entry in stale_entries:
            if entry.access_count < 2:  # Rarely accessed
                await self.delete(entry.key.replace(f"{self.config.key_prefix}:", ""))
                removed_count += 1
        
        if removed_count > 0:
            logger.info(f"🧹 Removed {removed_count} stale cache entries")
        
        logger.info("✅ Cache optimization completed")
    
    async def schedule_cache_maintenance(self):
        """Schedule periodic cache maintenance tasks"""
        if not self.config.enabled:
            return
        
        # This would typically be called by a background task scheduler
        try:
            await self.optimize_cache()
            
            # Get popular symbols for cache warming (this would come from usage analytics)
            popular_symbols = ["AAPL", "TSLA", "NVDA", "MSFT", "GOOGL"]  # Example
            await self.warm_cache(popular_symbols)
            
            logger.info("🔄 Scheduled cache maintenance completed")
            
        except Exception as e:
            logger.error(f"Cache maintenance failed: {e}")


# Global cache manager instance
cache_manager = CacheManager()