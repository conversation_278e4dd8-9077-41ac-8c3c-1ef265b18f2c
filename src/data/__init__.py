"""
Unified Data Management Layer

Consolidates all data-related functionality including providers, caching,
storage, and validation into a single, coherent system.
"""

# Core data interfaces
from .providers import (
    BaseDataProvider,
    MarketDataResponse
)

# Specific provider implementations
from .providers import (
    AlphaVantageProvider,
    YFinanceProvider,
    FinnhubProvider,
    PolygonProvider
)

# Caching system
from .cache import (
    CacheManager,
    CacheConfig,
    CacheEntry,
    cache_manager
)

__version__ = "2.0.0"

# Export all components
__all__ = [
    # Providers
    "BaseDataProvider", "MarketDataResponse",
    "AlphaVantageProvider", "YFinanceProvider", "FinnhubProvider", 
    "PolygonProvider",
    
    # Caching
    "CacheManager", "CacheConfig", "CacheEntry", "cache_manager"
]