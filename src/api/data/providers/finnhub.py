import os
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional, Union

import httpx

# Import unified base classes
from src.shared.data_providers.unified_base import (
    UnifiedDataProvider,
    ProviderType,
    MarketDataResponse,
    HistoricalData,
    ProviderError,
    ProviderRateLimitError
)

from src.core.logger import get_logger
from src.core.exceptions import MarketDataError

logger = get_logger(__name__)

class FinnhubProvider(UnifiedDataProvider):
    """
    Market data provider for Finnhub API.
    
    Supports current price and historical data retrieval.
    Updated to use unified base class for consistency.
    """
    
    def __init__(
        self, 
        api_key: str = None, 
        base_url: str = "https://finnhub.io/api/v1",
        rate_limit: int = 60,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize Finnhub provider.
        
        Args:
            api_key (str, optional): Finnhub API key. Defaults to env var.
            base_url (str, optional): Base API URL. Defaults to Finnhub endpoint.
            rate_limit (int, optional): Requests per minute. Defaults to 60.
            config (dict, optional): Additional configuration.
        """
        api_key = api_key or os.getenv('FINNHUB_API_KEY')
        if not api_key:
            raise ValueError("Finnhub API key is required")
        
        # Initialize unified base class
        provider_config = config or {}
        provider_config['rate_limit'] = rate_limit
        provider_config['timeout'] = provider_config.get('timeout', 30.0)
        provider_config['max_retries'] = provider_config.get('max_retries', 3)
        
        super().__init__(
            provider_name="finnhub",
            provider_type=ProviderType.STOCK_DATA,
            config=provider_config
        )
        
        # Store API configuration
        self.api_key = api_key
        self.base_url = base_url
    
    async def get_current_price(self, symbol: str) -> MarketDataResponse:
        """
        Fetch current price for a given symbol from Finnhub.
        
        Args:
            symbol (str): Stock symbol
        
        Returns:
            MarketDataResponse: Current market data
        """
        try:
            validated_symbol = self.validate_symbol(symbol)
            
            url = f"{self.base_url}/quote"
            params = {
                'symbol': validated_symbol,
                'token': self.api_key
            }
            
            # Use rate limiter from unified base
            if not await self.rate_limiter.acquire():
                wait_time = self.rate_limiter.get_wait_time()
                raise ProviderRateLimitError(f"Rate limit exceeded. Wait {wait_time:.1f} seconds", self.provider_name)
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                response_data = response.json()
            
            # Create metadata
            metadata = self._create_metadata()
            
            # Create response using unified base method
            return self._create_response(
                symbol=validated_symbol,
                price=float(response_data.get('c', 0)),
                timestamp=datetime.now(timezone.utc),
                volume=float(response_data.get('v', 0)),
                change_percent=float(response_data.get('dp', 0)),
                high=float(response_data.get('h', 0)),
                low=float(response_data.get('l', 0)),
                open=float(response_data.get('o', 0)),
                previous_close=float(response_data.get('pc', 0)),
                metadata=metadata
            )
        
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            logger.error(f"Finnhub price fetch error for {symbol}: {e}")
            raise ProviderError(f"Failed to fetch price for {symbol}", self.provider_name)
    
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: Optional[datetime] = None, 
        end_date: Optional[datetime] = None,
        days: Optional[Union[int, str]] = None
    ) -> HistoricalData:
        """
        Fetch historical market data for a symbol from Finnhub.
        
        Args:
            symbol (str): Stock symbol
            start_date (datetime, optional): Start of historical data range
            end_date (datetime, optional): End of historical data range
            days (int or str, optional): Number of days to look back (alternative to start_date/end_date)
        
        Returns:
            HistoricalData: Historical market data
        """
        try:
            validated_symbol = self.validate_symbol(symbol)
            
            # Handle date parameters
            if start_date is not None and end_date is not None and days is None:
                # Called with (symbol, start_date, end_date) - use as is
                pass
            elif days is not None:
                # Called with days parameter - convert to int if it's a string
                if isinstance(days, str):
                    try:
                        days = int(days)
                    except ValueError:
                        # If it's not a number, treat it as a resolution string
                        # For now, default to 1 day
                        days = 1
                
                end_date = datetime.now(timezone.utc)
                start_date = end_date - timedelta(days=days)
            else:
                # Default to last 30 days if no dates provided
                end_date = end_date or datetime.now(timezone.utc)
                start_date = start_date or (end_date - timedelta(days=30))
            
            # Use rate limiter from unified base
            if not await self.rate_limiter.acquire():
                wait_time = self.rate_limiter.get_wait_time()
                raise ProviderRateLimitError(f"Rate limit exceeded. Wait {wait_time:.1f} seconds", self.provider_name)
            
            async with httpx.AsyncClient() as client:
                # Finnhub candlestick endpoint
                url = f"{self.base_url}/stock/candle"
                params = {
                    'symbol': validated_symbol,
                    'resolution': 'D',  # Daily resolution
                    'from': int(start_date.timestamp()),
                    'to': int(end_date.timestamp()),
                    'token': self.api_key
                }
                
                response = await client.get(url, params=params)
                response.raise_for_status()
                response_data = response.json()
                
                if response_data.get('s') != 'ok':
                    raise ProviderError(f"Finnhub API error: {response_data.get('s')}", self.provider_name)
                
                # Extract data arrays
                timestamps = response_data.get('t', [])
                opens = response_data.get('o', [])
                highs = response_data.get('h', [])
                lows = response_data.get('l', [])
                closes = response_data.get('c', [])
                volumes = response_data.get('v', [])
                
                # Convert timestamps to datetime objects
                dates = [datetime.fromtimestamp(ts, tz=timezone.utc) for ts in timestamps]
                
                # Create metadata
                metadata = self._create_metadata(
                    data_window_start=start_date,
                    data_window_end=end_date
                )
                
                # Create historical data response
                return HistoricalData(
                    symbol=validated_symbol,
                    dates=dates,
                    opens=opens,
                    closes=closes,
                    highs=highs,
                    lows=lows,
                    volumes=volumes,
                    metadata=metadata,
                    additional_fields={}
                )
        
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            logger.error(f"Finnhub historical data fetch error for {symbol}: {e}")
            raise ProviderError(f"Failed to fetch historical data for {symbol}", self.provider_name)
    
    async def get_stock_data(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive stock data (compatibility method)."""
        try:
            current_price = await self.get_current_price(symbol)
            return {
                'symbol': symbol,
                'current_price': current_price.price,
                'timestamp': current_price.timestamp.isoformat(),
                'volume': current_price.volume,
                'change': 0,  # Not available in Finnhub quote endpoint
                'change_percent': current_price.change_percent,
                'provider': self.provider_name,
                'metadata': current_price.metadata
            }
        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            raise
    
    def validate_symbol(self, symbol: str) -> str:
        """
        Validate and normalize stock symbol.
        
        Args:
            symbol (str): Stock symbol to validate
        
        Returns:
            str: Normalized symbol
        
        Raises:
            ValueError: If symbol is invalid
        """
        if not symbol or not isinstance(symbol, str):
            raise ValueError("Symbol must be a non-empty string")
        
        # Normalize symbol (uppercase, remove whitespace)
        normalized = symbol.strip().upper()
        
        if not normalized:
            raise ValueError("Symbol cannot be empty after normalization")
        
        # Basic validation - symbols should be 1-10 characters, alphanumeric
        if not normalized.isalnum() or len(normalized) > 10:
            raise ValueError(f"Invalid symbol format: {normalized}")
        
        return normalized 