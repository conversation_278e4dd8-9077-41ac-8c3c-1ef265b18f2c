"""
Base classes for market data providers with enhanced metadata and attribution.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from src.core.exceptions import MarketDataError, ProviderUnavailableError, RateLimitError

logger = logging.getLogger(__name__)


class ProviderType(Enum):
    """Enumeration of supported data provider types."""
    POLYGON = "polygon"
    ALPHA_VANTAGE = "alpha_vantage"
    FINNHUB = "finnhub"
    YAHOO = "yahoo"
    YFINANCE = "yfinance"
    INTERNAL = "internal"
    CACHE = "cache"


@dataclass
class ProviderMetadata:
    """Metadata about data source and freshness."""
    provider_name: str
    provider_type: ProviderType
    fetched_at: datetime
    data_window_start: Optional[datetime] = None
    data_window_end: Optional[datetime] = None
    is_fallback: bool = False
    fallback_reason: Optional[str] = None
    response_time_ms: Optional[float] = None
    cache_hit: bool = False
    cache_ttl_remaining: Optional[int] = None
    
    def __post_init__(self):
        """Ensure fetched_at is timezone-aware."""
        if self.fetched_at and self.fetched_at.tzinfo is None:
            self.fetched_at = self.fetched_at.replace(tzinfo=timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'provider_name': self.provider_name,
            'provider_type': self.provider_type.value,
            'fetched_at': self.fetched_at.isoformat(),
            'data_window_start': self.data_window_start.isoformat() if self.data_window_start else None,
            'data_window_end': self.data_window_end.isoformat() if self.data_window_end else None,
            'is_fallback': self.is_fallback,
            'fallback_reason': self.fallback_reason,
            'response_time_ms': self.response_time_ms,
            'cache_hit': self.cache_hit,
            'cache_ttl_remaining': self.cache_ttl_remaining
        }
    
    def get_freshness_minutes(self) -> Optional[int]:
        """Get data freshness in minutes."""
        if not self.fetched_at:
            return None
        
        now = datetime.now(timezone.utc)
        age = now - self.fetched_at
        return int(age.total_seconds() / 60)
    
    def get_freshness_status(self) -> str:
        """Get human-readable freshness status."""
        minutes = self.get_freshness_minutes()
        if minutes is None:
            return "unknown"
        elif minutes < 1:
            return "fresh"
        elif minutes < 5:
            return "very_recent"
        elif minutes < 15:
            return "recent"
        elif minutes < 60:
            return "moderate"
        else:
            return "stale"


@dataclass
class MarketDataResponse:
    """Enhanced market data response with provider attribution."""
    symbol: str
    price: float
    timestamp: datetime
    volume: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None
    close: Optional[float] = None
    change_percent: Optional[float] = None
    provider: Optional[str] = None
    metadata: Optional[ProviderMetadata] = None
    
    def __post_init__(self):
        """Ensure timestamp is timezone-aware."""
        if self.timestamp and self.timestamp.tzinfo is None:
            self.timestamp = self.timestamp.replace(tzinfo=timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        result = {
            'symbol': self.symbol,
            'price': self.price,
            'timestamp': self.timestamp.isoformat(),
            'volume': self.volume,
            'high': self.high,
            'low': self.low,
            'open': self.open,
            'close': self.close,
            'change_percent': self.change_percent,
            'provider': self.provider
        }
        
        if self.metadata:
            result['metadata'] = self.metadata.to_dict()
        
        return result
    
    def get_attribution_text(self) -> str:
        """Generate human-readable attribution text."""
        if not self.metadata:
            return f"Data from {self.provider or 'unknown source'}"
        
        meta = self.metadata
        freshness = meta.get_freshness_status()
        
        # Base attribution
        if meta.cache_hit:
            attribution = f"📊 Cached data from {meta.provider_name}"
        else:
            attribution = f"📊 Data from {meta.provider_name}"
        
        # Add freshness info
        minutes = meta.get_freshness_minutes()
        if minutes is not None:
            if minutes < 1:
                attribution += " (just updated)"
            elif minutes < 60:
                attribution += f" (updated {minutes} min ago)"
            else:
                hours = minutes // 60
                attribution += f" (updated {hours}h {minutes % 60}m ago)"
        
        # Add fallback info
        if meta.is_fallback:
            attribution += f" (fallback: {meta.fallback_reason})"
        
        # Add data window if available
        if meta.data_window_start and meta.data_window_end:
            start_str = meta.data_window_start.strftime("%Y-%m-%d %H:%M")
            end_str = meta.data_window_end.strftime("%Y-%m-%d %H:%M")
            attribution += f" covering {start_str} → {end_str}"
        
        return attribution


class BaseMarketDataProvider(ABC):
    """Base class for market data providers with enhanced metadata."""
    
    def __init__(self, provider_name: str, provider_type: ProviderType):
        self.provider_name = provider_name
        self.provider_type = provider_type
        self.is_available = True
        self.last_error = None
        self.error_count = 0
        self.success_count = 0
        self.avg_response_time = 0.0
    
    @abstractmethod
    async def get_current_price(self, symbol: str) -> MarketDataResponse:
        """Get current price for a symbol."""
        pass
    
    @abstractmethod
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> List[MarketDataResponse]:
        """Get historical data for a symbol."""
        pass
    
    def _create_metadata(
        self,
        is_fallback: bool = False,
        fallback_reason: Optional[str] = None,
        response_time_ms: Optional[float] = None,
        data_window_start: Optional[datetime] = None,
        data_window_end: Optional[datetime] = None
    ) -> ProviderMetadata:
        """Create provider metadata for responses."""
        return ProviderMetadata(
            provider_name=self.provider_name,
            provider_type=self.provider_type,
            fetched_at=datetime.now(timezone.utc),
            data_window_start=data_window_start,
            data_window_end=data_window_end,
            is_fallback=is_fallback,
            fallback_reason=fallback_reason,
            response_time_ms=response_time_ms
        )
    
    def _create_response(
        self,
        symbol: str,
        price: float,
        timestamp: datetime,
        **kwargs
    ) -> MarketDataResponse:
        """Create a market data response with metadata."""
        metadata = kwargs.pop('metadata', None)
        if not metadata:
            metadata = self._create_metadata()
        
        return MarketDataResponse(
            symbol=symbol,
            price=price,
            timestamp=timestamp,
            metadata=metadata,
            **kwargs
        )
    
    async def _measure_response_time(self, func, *args, **kwargs):
        """Measure response time for provider operations."""
        start_time = asyncio.get_event_loop().time()
        try:
            result = await func(*args, **kwargs)
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000  # Convert to ms
            
            # Update provider statistics
            self.success_count += 1
            self.avg_response_time = (
                (self.avg_response_time * (self.success_count - 1) + response_time) / self.success_count
            )
            
            # Add response time to metadata if result has metadata
            if hasattr(result, 'metadata') and result.metadata:
                result.metadata.response_time_ms = response_time
            elif isinstance(result, list) and result and hasattr(result[0], 'metadata'):
                for item in result:
                    if hasattr(item, 'metadata') and item.metadata:
                        item.metadata.response_time_ms = response_time
            
            return result
            
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            raise
    
    def get_provider_stats(self) -> Dict[str, Any]:
        """Get provider performance statistics."""
        return {
            'provider_name': self.provider_name,
            'provider_type': self.provider_type.value,
            'is_available': self.is_available,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'avg_response_time_ms': self.avg_response_time,
            'last_error': self.last_error,
            'success_rate': (
                self.success_count / (self.success_count + self.error_count) * 100
                if (self.success_count + self.error_count) > 0 else 0
            )
        } 