import os
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from src.api.data.providers.data_source_manager import DataSourceManager
from src.core.logger import get_logger
from src.core.exceptions import MarketDataError
from src.core.validation.financial_validator import FinancialDataValidator
from src.api.data.providers.base import MarketDataResponse

logger = get_logger(__name__)

class MarketDataService:
    """
    Advanced market data retrieval service with multiple data sources
    and comprehensive error handling
    """
    
    def __init__(self, data_source_manager: Optional[DataSourceManager] = None):
        """
        Initialize market data service
        
        Args:
            data_source_manager (Optional[DataSourceManager]): Custom data source manager
        """
        self.data_source_manager = data_source_manager or DataSourceManager()
    
    async def get_current_price(self, symbol: str) -> MarketDataResponse:
        """
        Retrieve current price for a stock symbol
        
        Args:
            symbol (str): Stock symbol
        
        Returns:
            MarketDataResponse: Current price data
        """
        try:
            # Validate symbol
            if not symbol or len(symbol) > 10 or not symbol.isalpha():
                raise ValueError(f"Invalid symbol: {symbol}")
            
            # Fetch data using data source manager
            stock_data_dict = await self.data_source_manager.fetch_current_price(symbol)
            
            # Convert dictionary to MarketDataResponse
            stock_data = MarketDataResponse(
                symbol=symbol,
                price=stock_data_dict.get('current_price', 0),
                change_percent=stock_data_dict.get('change_percent', 0),
                volume=stock_data_dict.get('volume', 0),
                high=stock_data_dict.get('high', 0),
                low=stock_data_dict.get('low', 0),
                open=stock_data_dict.get('open', 0),
                close=stock_data_dict.get('close', 0),
                timestamp=datetime.fromisoformat(stock_data_dict.get('timestamp', datetime.now().isoformat())),
                provider=stock_data_dict.get('provider', 'unknown')
            )
            
            return stock_data
        
        except Exception as e:
            logger.error(f"Error retrieving current price for {symbol}: {e}")
            raise MarketDataError(f"Failed to get current price for {symbol}")

    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: Optional[datetime] = None, 
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> List[MarketDataResponse]:
        """
        Retrieve historical stock data
        
        Args:
            symbol (str): Stock symbol
            start_date (Optional[datetime]): Start date for historical data
            end_date (Optional[datetime]): End date for historical data
            days (Optional[int]): Number of days of historical data to retrieve
        
        Returns:
            List[MarketDataResponse]: Historical stock data
        """
        try:
            # Validate symbol
            if not symbol or len(symbol) > 10 or not symbol.isalpha():
                raise ValueError(f"Invalid symbol: {symbol}")
            
            # If days is provided, calculate start and end dates
            if days is not None:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)
            
            # Use the new fetch_historical_data method from DataSourceManager
            historical_data_dicts = await self.data_source_manager.fetch_historical_data(
                symbol, start_date, end_date, days
            )
            
            # Convert to MarketDataResponse objects
            historical_data = []
            for data_dict in historical_data_dicts:
                historical_item = MarketDataResponse(
                    symbol=symbol,
                    price=data_dict.get('close', 0),
                    change_percent=0,  # Historical data doesn't have change
                    volume=data_dict.get('volume', 0),
                    high=data_dict.get('high', 0),
                    low=data_dict.get('low', 0),
                    open=data_dict.get('open', 0),
                    close=data_dict.get('close', 0),
                    timestamp=datetime.fromisoformat(data_dict.get('date', datetime.now().isoformat())),
                    provider=data_dict.get('provider', 'unknown')
                )
                historical_data.append(historical_item)
            
            return historical_data
        
        except Exception as e:
            logger.error(f"Error retrieving historical data for {symbol}: {e}")
            raise MarketDataError(f"Failed to get historical data for {symbol}")

    async def get_comprehensive_stock_data(self, symbol: str) -> Dict[str, Any]:
        """
        Get comprehensive stock data including current price and additional metadata.
        This method provides backward compatibility for older code that expects
        a dictionary format with specific field names.
        
        Args:
            symbol (str): Stock symbol
            
        Returns:
            Dict[str, Any]: Comprehensive stock data in dictionary format
        """
        try:
            # Get current price data
            current_data = await self.get_current_price(symbol)
            
            # Convert MarketDataResponse to the expected dictionary format
            comprehensive_data = {
                'symbol': current_data.symbol,
                'current_price': current_data.price,
                'price': current_data.price,
                'change': current_data.change_percent if current_data.change_percent is not None else 0,
                'change_percent': current_data.change_percent if current_data.change_percent is not None else 0,
                'volume': current_data.volume,
                'high': current_data.high,
                'low': current_data.low,
                'open': current_data.open,
                'close': current_data.close if current_data.close is not None else current_data.price,
                'timestamp': current_data.timestamp.isoformat(),
                'provider': current_data.provider or 'unknown',
                'status': 'success'
            }
            
            # Try to get additional data if available (market cap, name, etc.)
            # This would need to be implemented based on the data source
            try:
                # Placeholder for additional data that might be available
                # from the data source manager
                comprehensive_data['market_cap'] = None  # Would need to be populated from actual data
                comprehensive_data['name'] = symbol  # Would need to be populated from actual data
            except Exception:
                # If additional data fetching fails, continue with basic data
                pass
                
            return comprehensive_data
            
        except Exception as e:
            logger.error(f"Error retrieving comprehensive data for {symbol}: {e}")
            # Return error response in the expected format
            return {
                'symbol': symbol,
                'status': 'error',
                'error': str(e),
                'current_price': None,
                'price': None,
                'change': None,
                'change_percent': None,
                'volume': None
            }

    async def get_technical_indicators(self, symbol: str, timeframe: str = "1d") -> Dict[str, Any]:
        """
        Retrieve technical indicators for a stock symbol
        
        Args:
            symbol (str): Stock symbol
            timeframe (str): Timeframe for indicators (1d, 1w, 1m)
        
        Returns:
            Dict[str, Any]: Technical indicators data
        """
        try:
            # Validate symbol
            if not symbol or len(symbol) > 10 or not symbol.isalpha():
                raise ValueError(f"Invalid symbol: {symbol}")
            
            # Fetch technical indicators using data source manager
            indicators_data = await self.data_source_manager.fetch_technical_indicators(symbol, timeframe)
            
            return indicators_data
        
        except Exception as e:
            logger.error(f"Error retrieving technical indicators for {symbol}: {e}")
            raise MarketDataError(f"Failed to get technical indicators for {symbol}")

# Create and export service instance
market_data_service = MarketDataService()
