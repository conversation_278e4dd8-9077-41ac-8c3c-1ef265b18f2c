"""
Canonical Ask pipeline templates module.
Copied from src/bot/pipeline/commands/ask/stages/response_templates.py
"""

# The full content below is sourced from the previous location to preserve behavior.
# Any updates should be made here going forward.

"""
Response Template Engine

Deterministic response generation with template-based routing.
"""

import logging
import re
import string
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)

class ResponseDepth(Enum):
    """Response depth options"""
    CASUAL = "casual"
    GENERAL = "general"
    DETAILED = "detailed"
    TECHNICAL = "technical"
    ACADEMIC = "academic"

class ResponseStyle(Enum):
    """Response style options"""
    SIMPLE = "simple"
    DETAILED = "detailed"
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    ACADEMIC = "academic"
    TRADING = "trading"
    PROFESSIONAL = "detailed"  # Alias for 'detailed' style

class ConfidenceLevel(Enum):
    """Confidence levels for recommendations"""
    VERY_LOW = (0, 20, "🔴", "Very Low")
    LOW = (21, 40, "🟠", "Low")
    MEDIUM = (41, 60, "🟡", "Medium")
    HIGH = (61, 80, "🟢", "High")
    VERY_HIGH = (81, 100, "🟢", "Very High")
    
    def __init__(self, min_val: int, max_val: int, emoji: str, label: str):
        self.min_val = min_val
        self.max_val = max_val
        self.emoji = emoji
        self.label = label
    
    @classmethod
    def from_score(cls, score: float) -> 'ConfidenceLevel':
        """Get confidence level from numeric score"""
        for level in cls:
            if level.min_val <= score <= level.max_val:
                return level
        return cls.MEDIUM

class PatternType(Enum):
    """Technical analysis pattern types"""
    HEAD_AND_SHOULDERS = "Head & Shoulders"
    INVERSE_HEAD_AND_SHOULDERS = "Inverse Head & Shoulders"
    ASCENDING_TRIANGLE = "Ascending Triangle"
    DESCENDING_TRIANGLE = "Descending Triangle"
    SYMMETRICAL_TRIANGLE = "Symmetrical Triangle"
    BULL_FLAG = "Bull Flag"
    BEAR_FLAG = "Bear Flag"
    DOUBLE_TOP = "Double Top"
    DOUBLE_BOTTOM = "Double Bottom"
    CUP_AND_HANDLE = "Cup & Handle"

@dataclass
class TechnicalPattern:
    """Technical analysis pattern detection result"""
    pattern_type: PatternType
    confidence: float
    direction: str  # "bullish", "bearish", "neutral"
    strength: str   # "weak", "moderate", "strong"
    price_targets: Dict[str, float]
    stop_loss: Optional[float]
    description: str

@dataclass
class SentimentAnalysis:
    """Market sentiment analysis result"""
    overall_sentiment: float  # -1.0 to 1.0
    sentiment_label: str      # "Very Bearish" to "Very Bullish"
    confidence: float         # 0.0 to 1.0
    news_count: int
    recent_headlines: List[str]
    market_fear_greed: Optional[float]

@dataclass
class Recommendation:
    """Trading recommendation with confidence scoring"""
    action: str  # "BUY", "SELL", "HOLD", "WAIT"
    confidence: float  # 0.0 to 100.0
    reasoning: str
    risk_level: str  # "Low", "Medium", "High"
    price_targets: Dict[str, float]
    stop_loss: Optional[float]
    technical_patterns: List[TechnicalPattern]
    sentiment: SentimentAnalysis
    data_quality: float  # 0.0 to 100.0

class ResponseTemplate:
    """Response template structure"""
    
    def __init__(self, template_type: str, style: ResponseStyle, content: str):
        self.template_type = template_type
        self.style = style
        self.content = content
    
    def __str__(self):
        return f"ResponseTemplate({self.template_type}, {self.style.value})"

class SafeDict(dict):
    """Dictionary that safely handles missing keys and format specifiers for string formatting"""
    def __missing__(self, key):
        return f"{{{key}}}"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Convert all values to SafeValue objects immediately
        for key, value in list(self.items()):
            super().__setitem__(key, self._safe_value(value))

    def __setitem__(self, key, value):
        super().__setitem__(key, self._safe_value(value))

    def _safe_value(self, value):
        """Return a safe value that can handle format specifiers."""
        if isinstance(value, SafeValue):
            return value  # Already wrapped
        elif value is None:
            return SafeValue(0.0)
        elif isinstance(value, str):
            # Try to convert string to number
            try:
                return SafeValue(float(value))
            except (ValueError, TypeError):
                # Return a SafeValue that handles format specs gracefully
                return SafeValue(value)
        else:
            return SafeValue(value)

class SafeValue:
    """A wrapper that safely handles format specifiers for template values."""

    def __init__(self, value):
        self.value = value
        self.is_numeric = isinstance(value, (int, float))
        if not self.is_numeric and isinstance(value, str):
            try:
                self.numeric_value = float(value)
                self.is_numeric = True
            except (ValueError, TypeError):
                self.numeric_value = 0.0
        else:
            self.numeric_value = float(value) if isinstance(value, (int, float)) else 0.0

    def __format__(self, format_spec):
        """Handle format specifications safely."""
        if not format_spec:
            return str(self.value)

        # Check if format spec is for numbers (contains 'f', 'd', etc.)
        if any(char in format_spec for char in 'fFeEgGdoxX'):
            if self.is_numeric:
                return f"{self.numeric_value:{format_spec}}"
            else:
                # For non-numeric strings, return appropriate defaults or the string itself
                if str(self.value).lower() in ['n/a', 'unknown', 'unavailable', 'none', '', 'null']:
                    return f"{0.0:{format_spec}}"
                else:
                    # For descriptive strings like "high", "low", return as-is
                    return str(self.value)
        else:
            # Non-numeric format spec, just return the string
            return f"{self.value:{format_spec}}"

    def __str__(self):
        return str(self.value)

    def __repr__(self):
        return f"SafeValue({self.value!r})"

class ResponseTemplateEngine:
    """Enhanced response template engine with confidence scoring and pattern recognition"""
    
    def __init__(self):
        self.templates = self._load_templates()
        self.pattern_detector = PatternDetector()
        self.sentiment_analyzer = SentimentAnalyzer()
    
    def _load_templates(self) -> Dict[str, Any]:
        """Load response templates"""
        return {
            "stock_analysis": {
                "simple": """**{symbol} Analysis - {confidence_emoji} {confidence_level}**

💰 **Current Price:** ${price}
📈 **Change:** {change_sign}{change}% ({change_sign}${abs_change})
📊 **Volume:** {volume}

🎯 **Recommendation:** {action} ({confidence}% confidence)
⚠️ **Risk Level:** {risk_level}

{technical_analysis}
{sentiment_summary}

*Data Quality: {data_quality}% • Updated: {timestamp}*""",
                
                "detailed": """**📊 {symbol} Comprehensive Analysis**

💰 **Price Action:**
• Current: ${price}
• Change: {change_sign}{change}% ({change_sign}${abs_change})
• Volume: {volume}
• Market Cap: {market_cap_formatted}

🎯 **Trading Recommendation:**
• Action: **{action}** {confidence_emoji}
• Confidence: **{confidence}%**
• Risk Level: {risk_level}

📈 **Technical Analysis:**
{technical_analysis}

💭 **Market Sentiment:**
{sentiment_summary}

🎯 **Price Targets:**
{price_targets}

⚠️ **Risk Management:**
• Stop Loss: {stop_loss_formatted}
• Position Size: {position_size}

*Analysis Quality: {data_quality}% • {timestamp}*""",
                
                "technical": """**🔬 {symbol} Technical Analysis**

📊 **Price Data:**
• Current: ${price}
• Change: {change_sign}{change}%
• Volume: {volume}

🎯 **Pattern Recognition:**
{pattern_analysis}

📈 **Technical Indicators:**
{technical_indicators}

🎯 **Recommendation:** {action} ({confidence}% confidence)
⚠️ **Risk:** {risk_level}

*Technical Analysis Quality: {data_quality}%*"""
            },
            
            "technical_analysis": {
                "simple": """**📊 Technical Analysis - {symbol}**

📈 **Price Action:**
• Current: ${price}
• Change: {change_sign}{change}%
• Volume: {volume}

🎯 **Technical Indicators:**
{technical_indicators}

📊 **Pattern Analysis:**
{pattern_analysis}

*Technical Analysis Quality: {data_quality}%*""",
                
                "detailed": """**📊 Comprehensive Technical Analysis - {symbol}**

📈 **Price Action:**
• Current: ${price}
• Change: {change_sign}{change}%
• Volume: {volume}
• Market Cap: {market_cap_formatted}

🎯 **Technical Indicators:**
{technical_indicators}

📊 **Pattern Analysis:**
{pattern_analysis}

🔍 **Support/Resistance:**
• Support: ${support}
• Resistance: ${resistance}

*Technical Analysis Quality: {data_quality}%*"""
            },
            
            "market_overview": {
                "simple": """**🌍 Market Overview**

📊 **Major Indices:**
{indices_summary}

💭 **Market Sentiment:** {overall_sentiment}
🎯 **Trend:** {market_trend}

*Updated: {timestamp}*""",
                
                "detailed": """**🌍 Comprehensive Market Overview**

📊 **Index Performance:**
{indices_detailed}

💭 **Sentiment Analysis:**
• Overall: {overall_sentiment}
• Fear/Greed Index: {fear_greed}
• News Sentiment: {news_sentiment}

📈 **Market Trends:**
{market_trends}

🎯 **Key Levels:**
{key_levels}

*Market Data Quality: {data_quality}% • {timestamp}*"""
            },
            
            "error_fallback": """**⚠️ Analysis Unavailable - Fallback Mode**

📊 **{symbol} Basic Data:**
• Price: ${price}
• Change: {change_sign}{change}%
• Volume: {volume}

ℹ️ **Note:** Advanced analysis unavailable. Using fallback data source.
🔄 **Recommendation:** Try again later for full analysis.

*Fallback Mode • {timestamp}*"""
        }
    
    def generate_response(self, 
                        template_type: Any, 
                        style: Any, 
                        data: Dict[str, Any],
                        query_analysis: Dict[str, Any]) -> str:
        """Generate enhanced response with confidence scoring"""
        try:
            # Normalize inputs
            if isinstance(template_type, Enum):
                template_type_key = template_type.value
            else:
                template_type_key = str(template_type)
            
            if isinstance(style, Enum):
                style_key = style.value
            else:
                style_key = str(style)
            
            # Get base template
            if template_type_key not in self.templates:
                template_type_key = "stock_analysis"
            
            if not isinstance(self.templates[template_type_key], dict):
                # Safety: ensure we are not on error_fallback (string)
                template_type_key = "stock_analysis"
            if style_key not in self.templates[template_type_key]:
                style_key = ResponseStyle.SIMPLE.value
            
            template = self.templates[template_type_key][style_key]
            
            logger.info(f"Using template: {template_type_key}/{style_key}")
            logger.info(f"Template content length: {len(template)}")
            logger.info(f"Input data keys: {list(data.keys())}")
            
            # Enhance data with confidence scoring and pattern recognition
            enhanced_data = self._enhance_data_with_analysis(data, query_analysis)
            
            logger.info(f"Enhanced data keys: {list(enhanced_data.keys())}")
            logger.info(f"Enhanced data values: {enhanced_data}")
            logger.info(f"Confidence emoji: {enhanced_data.get('confidence_emoji', 'NOT_SET')}")
            logger.info(f"Action: {enhanced_data.get('action', 'NOT_SET')}")
            
            # Fill template with enhanced data
            response = self._fill_template(template, enhanced_data)
            
            logger.info(f"Generated {template_type} response with {style} style")
            logger.info(f"Response length: {len(response) if response else 0}")
            return response
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return self._generate_error_response(data)
    
    def _enhance_data_with_analysis(self, data: Dict[str, Any], query_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance data with technical analysis, sentiment, and confidence scoring"""
        enhanced = data.copy()
        
        # Add confidence scoring
        enhanced.update(self._calculate_confidence_scores(data, query_analysis))
        
        # Add technical pattern recognition
        if 'price_data' in data:
            enhanced['technical_patterns'] = self.pattern_detector.detect_patterns(data['price_data'])
        
        # Add sentiment analysis
        enhanced['sentiment'] = self.sentiment_analyzer.analyze_sentiment(data)
        
        # Add recommendation with confidence
        recommendation = self._generate_recommendation(enhanced)
        enhanced['recommendation'] = recommendation
        enhanced['action'] = recommendation.action  # Extract action for template
        
        return enhanced
    
    def _calculate_confidence_scores(self, data: Dict[str, Any], query_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate confidence scores for different aspects of the analysis"""
        confidence_data = {}
        
        # Data quality confidence (based on data completeness and freshness)
        data_quality = self._assess_data_quality(data)
        confidence_data['data_quality'] = data_quality
        
        # Analysis confidence (based on data quality and query complexity)
        analysis_confidence = min(data_quality * 0.8 + 20, 100)  # Base 20% + up to 80% from data quality
        confidence_data['confidence'] = analysis_confidence
        
        # Confidence level and emoji
        confidence_level = ConfidenceLevel.from_score(analysis_confidence)
        confidence_data['confidence_level'] = confidence_level.label
        confidence_data['confidence_emoji'] = confidence_level.emoji
        
        # Risk level based on confidence
        if analysis_confidence >= 80:
            confidence_data['risk_level'] = "Low"
        elif analysis_confidence >= 60:
            confidence_data['risk_level'] = "Medium"
        else:
            confidence_data['risk_level'] = "High"
        
        return confidence_data
    
    def _assess_data_quality(self, data: Dict[str, Any]) -> float:
        """Assess the quality of available data"""
        quality_score = 0.0
        max_score = 100.0
        
        # Check data completeness - look for fields at top level or normalized equivalents
        required_fields = ['price', 'change', 'volume']
        available_fields = 0
        
        for field in required_fields:
            if field in data and data[field] is not None:
                available_fields += 1
            elif field == 'price' and 'current_price' in data and data['current_price'] is not None:
                available_fields += 1
            elif field == 'change' and 'change_percent' in data and data['change_percent'] is not None:
                available_fields += 1
            elif field == 'volume' and 'volume_traded' in data and data['volume_traded'] is not None:
                available_fields += 1
        
        quality_score += (available_fields / len(required_fields)) * 40
        
        # Check data freshness
        if 'timestamp' in data:
            quality_score += 30  # Assume recent data
        
        # Check data source reliability
        if 'status' in data and data['status'] in ['success', 'fallback']:
            quality_score += 30
        
        return min(quality_score, max_score)
    
    def _generate_recommendation(self, enhanced_data: Dict[str, Any]) -> Recommendation:
        """Generate trading recommendation with confidence scoring"""
        # Simple recommendation logic based on price change and confidence
        price_change = enhanced_data.get('change', 0)
        confidence = enhanced_data.get('confidence', 50)
        
        if price_change > 2 and confidence > 60:
            action = "BUY"
        elif price_change < -2 and confidence > 60:
            action = "SELL"
        else:
            action = "HOLD"
        
        return Recommendation(
            action=action,
            confidence=confidence,
            reasoning=f"Based on {abs(price_change)}% price movement and {confidence}% confidence",
            risk_level=enhanced_data.get('risk_level', 'Medium'),
            price_targets={},
            stop_loss=None,
            technical_patterns=enhanced_data.get('technical_patterns', []),
            sentiment=enhanced_data.get('sentiment', SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)),
            data_quality=enhanced_data.get('data_quality', 50)
        )
    
    def _fill_template(self, template: str, data: Dict[str, Any]) -> str:
        """Fill template with data using enhanced formatting"""
        try:
            # Create safe dictionary for missing keys
            safe_data = SafeDict(data)
            
            # Normalize field names to handle common variations
            if 'current_price' in data and 'price' not in data:
                safe_data['price'] = data['current_price']
            if 'change_percent' in data and 'change' not in data:
                safe_data['change'] = data['change_percent']
            if 'volume_traded' in data and 'volume' not in data:
                safe_data['volume'] = data['volume_traded']
            
            logger.info(f"Original data keys: {list(data.keys())}")
            logger.info(f"Normalized data keys: {list(safe_data.keys())}")
            logger.info(f"Original data values: {data}")
            
            # Add computed fields that don't conflict with template format specifiers
            if 'change' in data and data['change'] is not None:
                safe_data['change_sign'] = "+" if data['change'] >= 0 else "-"
                safe_data['abs_change'] = abs(data['change'])
            
            # Pre-format conditional fields
            if 'market_cap' in data and data['market_cap']:
                safe_data['market_cap_formatted'] = f"${data['market_cap']}"
            else:
                safe_data['market_cap_formatted'] = "N/A"
            
            if 'stop_loss' in data and data['stop_loss']:
                safe_data['stop_loss_formatted'] = f"${data['stop_loss']}"
            else:
                safe_data['stop_loss_formatted'] = "Not Set"
            
            # Provide default values for missing keys
            default_values = self._calculate_dynamic_defaults(safe_data)
            
            for key, default_val in default_values.items():
                if key not in safe_data:
                    safe_data[key] = default_val
            
            # Ensure numeric fields are properly formatted for template
            numeric_fields = [
                'data_quality', 'support', 'resistance', 'price', 'change', 'volume', 
                'abs_change', 'confidence', 'market_cap', 'stop_loss'
            ]
            for field in numeric_fields:
                if field in safe_data:
                    try:
                        if safe_data[field] is None:
                            safe_data[field] = 0.0
                        elif isinstance(safe_data[field], str):
                            # Try to convert string to float, fallback to 0.0
                            try:
                                safe_data[field] = float(safe_data[field])
                            except (ValueError, TypeError):
                                safe_data[field] = 0.0
                        else:
                            safe_data[field] = float(safe_data[field])
                    except (ValueError, TypeError):
                        safe_data[field] = 0.0
            
            # Validate required fields before template filling
            required_template_fields = ['symbol', 'price', 'change', 'volume', 'confidence', 'data_quality']
            missing_fields = [field for field in required_template_fields if field not in safe_data or safe_data[field] is None]
            
            if missing_fields:
                logger.warning(f"Missing required fields for template: {missing_fields}")
                logger.warning(f"Available fields: {list(safe_data.keys())}")
            
            # Fill template using string formatting
            try:
                result = template.format(**safe_data)
                logger.info(f"Template filled successfully with {len(safe_data)} data fields")
                return result
            except ValueError as e:
                # More specific error handling for format issues
                logger.error(f"Template format error: {e}")
                logger.error(f"Problematic data: {safe_data}")
                # Log all string values that might cause issues
                for key, value in safe_data.items():
                    if isinstance(value, str):
                        logger.error(f"String field: {key} = {value} (type: {type(value)})")
                raise
            
        except Exception as e:
            logger.error(f"Error filling template: {e}")
            # Return fallback response
            return f"""**⚠️ Analysis Error**

Unable to generate detailed analysis at this time.

**Basic Data Available:**
• Symbol: {data.get('symbol', 'Unknown')}
• Price: ${data.get('price', data.get('current_price', 0))}
• Change: {data.get('change', data.get('change_percent', 0))}%

**Recommendation:** Try again later or use fallback mode.

*Error occurred during response generation: {str(e)}*"""

    def _calculate_dynamic_defaults(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate dynamic default values based on available data."""
        try:
            defaults = {}
            
            # Symbol - use available symbol or generate descriptive fallback
            if 'symbol' not in data:
                if 'current_price' in data and data['current_price']:
                    defaults['symbol'] = 'Stock'
                else:
                    defaults['symbol'] = 'Symbol'
            
            # Price - use available price or indicate unavailable
            if 'price' not in data:
                if 'current_price' in data and data['current_price']:
                    defaults['price'] = data['current_price']
                else:
                    defaults['price'] = 'Price unavailable'
            
            # Change - calculate from available data or indicate unavailable
            if 'change' not in data:
                if 'change_percent' in data and data['change_percent'] is not None:
                    defaults['change'] = data['change_percent']
                else:
                    defaults['change'] = 'Change unavailable'
            
            # Volume - use available volume or indicate unavailable
            if 'volume' not in data:
                if 'volume_traded' in data and data['volume_traded']:
                    defaults['volume'] = data['volume_traded']
                else:
                    defaults['volume'] = 'Volume unavailable'
            
            # Confidence - calculate based on data quality with validation
            if 'confidence' not in data:
                try:
                    from ..core.data_quality_validator import DataQualityValidator
                    
                    data_quality = data.get('data_quality', 0)
                    completeness = data.get('completeness', 0.5)
                    technical_signals = data.get('technical_signals', {})
                    
                    # Use validated confidence calculation
                    defaults['confidence'] = DataQualityValidator.calculate_confidence(
                        data_quality=data_quality,
                        completeness=completeness,
                        technical_signals=technical_signals
                    )
                    
                    # Log if fallback confidence is used
                    if data_quality < 50:
                        logger.warning(f"Low data quality for confidence: {data_quality}, using validated calculation")
                        
                except ImportError:
                    # Fallback to old logic if validator not available
                    data_quality = data.get('data_quality', 0)
                    if data_quality > 80:
                        defaults['confidence'] = 85
                    elif data_quality > 60:
                        defaults['confidence'] = 70
                    elif data_quality > 40:
                        defaults['confidence'] = 55
                    else:
                        defaults['confidence'] = 40
            
            # Data quality - use available or calculate
            if 'data_quality' not in data:
                defaults['data_quality'] = self._assess_data_quality_from_available(data)
            
            # Timestamp - use current time
            if 'timestamp' not in data:
                defaults['timestamp'] = datetime.now().isoformat()
            
            # Action - determine based on available data with validation
            if 'action' not in data:
                try:
                    from ..core.data_quality_validator import ActionValidator
                    
                    change = data.get('change', data.get('change_percent', 0))
                    technical_signals = data.get('technical_signals', {})
                    data_quality = data.get('data_quality', 0)
                    volatility = data.get('volatility', 0.02)  # Default 2% volatility
                    
                    # Use validated action determination
                    defaults['action'] = ActionValidator.determine_action(
                        change=change,
                        technical_signals=technical_signals,
                        data_quality=data_quality,
                        volatility=volatility
                    )
                    
                    # Log if fallback action is used
                    if data_quality < 60:
                        logger.warning(f"Low data quality for action determination: {data_quality}, using validated logic")
                        
                except ImportError:
                    # Fallback to old logic if validator not available
                    change = data.get('change', data.get('change_percent', 0))
                    if isinstance(change, (int, float)) and change > 2:
                        defaults['action'] = 'BUY'
                    elif isinstance(change, (int, float)) and change < -2:
                        defaults['action'] = 'SELL'
                    else:
                        defaults['action'] = 'HOLD'
            
            # Risk level - assess based on data quality and volatility
            if 'risk_level' not in data:
                data_quality = data.get('data_quality', 0)
                if data_quality < 30:
                    defaults['risk_level'] = 'High'
                elif data_quality < 60:
                    defaults['risk_level'] = 'Medium'
                else:
                    defaults['risk_level'] = 'Low'
            
            # Technical analysis - provide context-aware message
            if 'technical_analysis' not in data:
                if 'rsi' in data or 'macd' in data:
                    defaults['technical_analysis'] = 'Technical indicators available and being analyzed.'
                else:
                    defaults['technical_analysis'] = 'Technical analysis data is being processed.'
            
            # Sentiment summary - provide context-aware message
            if 'sentiment_summary' not in data:
                if 'sentiment' in data:
                    defaults['sentiment_summary'] = 'Sentiment analysis completed.'
                else:
                    defaults['sentiment_summary'] = 'Sentiment analysis in progress.'
            
            # Market cap - use available or indicate unavailable
            if 'market_cap' not in data:
                defaults['market_cap'] = 'Market cap unavailable'
            
            # Price targets - provide context-aware message
            if 'price_targets' not in data:
                if data.get('data_quality', 0) > 70:
                    defaults['price_targets'] = 'Price targets being calculated from technical analysis.'
                else:
                    defaults['price_targets'] = 'Price targets unavailable due to insufficient data quality.'
            
            # Support and resistance - calculate from available data with validation
            if 'support' not in data or 'resistance' not in data:
                try:
                    from ..core.data_quality_validator import DataQualityValidator
                    
                    symbol = data.get('symbol', 'UNKNOWN')
                    timeframe = data.get('timeframe', '1D')
                    data_quality = data.get('data_quality', 0)
                    
                    # Use validated support/resistance calculation
                    support, resistance = DataQualityValidator.validate_support_resistance(
                        symbol=symbol,
                        timeframe=timeframe,
                        data_quality=data_quality
                    )
                    
                    if support is not None:
                        defaults['support'] = support
                    else:
                        defaults['support'] = 'Support level unavailable (insufficient data)'
                    
                    if resistance is not None:
                        defaults['resistance'] = resistance
                    else:
                        defaults['resistance'] = 'Resistance level unavailable (insufficient data)'
                    
                    # Log if fallback S/R is used
                    if data_quality < 70:
                        logger.warning(f"Low data quality for S/R calculation: {data_quality}, using validated logic")
                        
                except ImportError:
                    # Fallback to old logic if validator not available
                    current_price = data.get('price', data.get('current_price', 0))
                    if isinstance(current_price, (int, float)) and current_price > 0:
                        defaults['support'] = round(current_price * 0.95, 2)  # 5% below current
                        defaults['resistance'] = round(current_price * 1.05, 2)  # 5% above current
                    else:
                        defaults['support'] = 'Support level unavailable'
                        defaults['resistance'] = 'Resistance level unavailable'
            
            # Confidence emoji and level - calculate from confidence score
            if 'confidence' in defaults:
                confidence = defaults['confidence']
                if confidence >= 80:
                    defaults['confidence_emoji'] = '🟢'
                    defaults['confidence_level'] = 'High'
                elif confidence >= 60:
                    defaults['confidence_emoji'] = '🟡'
                    defaults['confidence_level'] = 'Medium'
                else:
                    defaults['confidence_emoji'] = '🔴'
                    defaults['confidence_level'] = 'Low'
            else:
                defaults['confidence_emoji'] = '🟡'
                defaults['confidence_level'] = 'Medium'
            
            # Fill remaining fields with informative defaults
            remaining_defaults = {
                'stop_loss': None,
                'position_size': 'Not specified',
                'pattern_analysis': 'Pattern analysis in progress',
                'technical_indicators': 'Technical indicators being processed',
                'indices_summary': 'Market indices data being collected',
                'overall_sentiment': 'Sentiment analysis in progress',
                'market_trend': 'Trend analysis in progress',
                'indices_detailed': 'Detailed market data being collected',
                'fear_greed': 'Fear/greed index being calculated',
                'news_sentiment': 'News sentiment analysis in progress',
                'market_trends': 'Market trends being analyzed',
                'key_levels': 'Key levels being identified',
                'abs_change': 0.0
            }
            
            for key, default_val in remaining_defaults.items():
                if key not in defaults:
                    defaults[key] = default_val
            
            return defaults
            
        except Exception as e:
            logger.warning(f"Error calculating dynamic defaults: {e}")
            # Return basic fallbacks if dynamic calculation fails
            return {
                'symbol': 'Symbol',
                'price': 'Price unavailable',
                'change': 'Change unavailable',
                'volume': 'Volume unavailable',
                'confidence': 50,
                'data_quality': 30,
                'timestamp': datetime.now().isoformat(),
                'action': 'HOLD',
                'risk_level': 'Medium',
                'confidence_emoji': '🟡',
                'confidence_level': 'Medium'
            }

    def _assess_data_quality_from_available(self, data: Dict[str, Any]) -> int:
        """Assess data quality from available fields."""
        try:
            quality_score = 0
            
            # Check for price data
            if data.get('price') or data.get('current_price'):
                quality_score += 30
            
            # Check for change data
            if data.get('change') or data.get('change_percent'):
                quality_score += 25
            
            # Check for volume data
            if data.get('volume') or data.get('volume_traded'):
                quality_score += 20
            
            # Check for timestamp
            if data.get('timestamp'):
                quality_score += 15
            
            # Check for technical indicators
            if data.get('rsi') or data.get('macd'):
                quality_score += 10
            
            return min(quality_score, 100)
            
        except Exception as e:
            logger.warning(f"Error assessing data quality: {e}")
            return 30
    
    def _generate_error_response(self, data: Dict[str, Any]) -> str:
        """Generate error response when template generation fails"""
        return f"""**⚠️ Analysis Error**

Unable to generate detailed analysis at this time.

**Basic Data Available:**
• Symbol: {data.get('symbol', 'Unknown')}
• Price: ${data.get('price', data.get('current_price', 0))}
• Change: {data.get('change', data.get('change_percent', 0))}%

**Recommendation:** Try again later or use fallback mode.

*Error occurred during response generation*"""

    def generate_market_overview_response(self, data: Dict[str, Any], style: ResponseStyle = ResponseStyle.DETAILED) -> str:
        """
        Generate a comprehensive market overview response with multiple sections.
        
        Args:
            data (Dict[str, Any]): Market data and analysis information
            style (ResponseStyle): Response verbosity level
        
        Returns:
            str: Formatted market overview response
        """
        # Ensure we have default values for all potential keys
        indices = data.get('indices', [
            {'symbol': 'NVDA', 'price': 0, 'change_percent': 0},
            {'symbol': 'TSLA', 'price': 0, 'change_percent': 0},
            {'symbol': 'AMD', 'price': 0, 'change_percent': 0}
        ])
        
        # Format indices section
        indices_section = "📊 Major Indices:\n" + "\n".join([
            f"• {idx['symbol']}: ${idx['price']:.2f} ({'+' if idx['change_percent'] >= 0 else ''}{idx['change_percent']:.2f}%)"
            for idx in indices
        ])
        
        # Market sentiment and trend
        sentiment = data.get('sentiment', 'Neutral')
        trend = data.get('trend', 'Mixed')
        
        # Sector performance
        sectors = data.get('sectors', {
            'Tech': 'Neutral',
            'Semiconductors': 'Bullish',
            'AI': 'Strong'
        })
        
        sectors_section = "🏭 Sector Performance:\n" + "\n".join([
            f"• {sector}: {performance}"
            for sector, performance in sectors.items()
        ])
        
        # Key market insights
        insights = data.get('insights', [
            "AI stocks continue to drive market momentum",
            "Tech sector showing resilience amid economic uncertainty",
            "Semiconductor stocks remain attractive"
        ])
        
        insights_section = "💡 Market Insights:\n" + "\n".join([
            f"• {insight}" for insight in insights
        ])
        
        # Risk factors
        risk_factors = data.get('risk_factors', [
            "Fed policy uncertainty",
            "Global trade tensions",
            "Tech sector valuation concerns"
        ])
        
        risk_section = "⚠️ Risk Factors:\n" + "\n".join([
            f"• {factor}" for factor in risk_factors
        ])
        
        # Trading strategy
        strategy = data.get('strategy', {
            'focus': 'Quality tech with strong balance sheets',
            'approach': 'Defensive with selective growth exposure'
        })
        
        strategy_section = f"🎯 Trading Strategy:\n• Focus: {strategy['focus']}\n• Approach: {strategy['approach']}"
        
        # Combine all sections
        full_response = f"""🌍 Market Overview

{indices_section}

💭 Market Sentiment: {sentiment}
🎯 Market Trend: {trend}

{sectors_section}

{insights_section}

{risk_section}

{strategy_section}

*Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"""
        
        return full_response

class PatternDetector:
    """Basic technical pattern detection"""
    
    def detect_patterns(self, price_data: Dict[str, Any]) -> List[TechnicalPattern]:
        """Detect basic technical patterns in price data"""
        patterns = []
        
        try:
            # Simple pattern detection logic
            if 'current_price' in price_data and 'change' in price_data:
                price = price_data['current_price']
                change = price_data['change']
                
                # Very basic pattern detection (placeholder for more sophisticated logic)
                if change > 5:  # Strong upward movement
                    patterns.append(TechnicalPattern(
                        pattern_type=PatternType.BULL_FLAG,
                        confidence=70.0,
                        direction="bullish",
                        strength="moderate",
                        price_targets={"resistance": price * 1.05},
                        stop_loss=price * 0.95,
                        description="Potential bull flag pattern forming"
                    ))
                elif change < -5:  # Strong downward movement
                    patterns.append(TechnicalPattern(
                        pattern_type=PatternType.BEAR_FLAG,
                        confidence=70.0,
                        direction="bearish",
                        strength="moderate",
                        price_targets={"support": price * 0.95},
                        stop_loss=price * 1.05,
                        description="Potential bear flag pattern forming"
                    ))
            
        except Exception as e:
            logger.warning(f"Pattern detection failed: {e}")
        
        return patterns

class SentimentAnalyzer:
    """Basic market sentiment analysis"""
    
    def analyze_sentiment(self, data: Dict[str, Any]) -> SentimentAnalysis:
        """Analyze market sentiment from available data"""
        try:
            # Simple sentiment analysis based on price movement
            change = data.get('change', 0)
            
            if change > 3:
                sentiment = 0.7  # Bullish
                label = "Bullish"
            elif change > 1:
                sentiment = 0.3  # Slightly Bullish
                label = "Slightly Bullish"
            elif change < -3:
                sentiment = -0.7  # Bearish
                label = "Bearish"
            elif change < -1:
                sentiment = -0.3  # Slightly Bearish
                label = "Slightly Bearish"
            else:
                sentiment = 0.0  # Neutral
                label = "Neutral"
            
            return SentimentAnalysis(
                overall_sentiment=sentiment,
                sentiment_label=label,
                confidence=0.6,  # Basic confidence for simple analysis
                news_count=0,
                recent_headlines=[],
                market_fear_greed=None
            )
            
        except Exception as e:
            logger.warning(f"Sentiment analysis failed: {e}")
            return SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)
