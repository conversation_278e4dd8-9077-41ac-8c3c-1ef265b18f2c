import logging
from typing import Dict, List
import discord

from src.data.models.stock_data import AnalysisResult
from src.analysis.fundamental.metrics import FundamentalMetricsCalculator

logger = logging.getLogger(__name__)

class AnalysisResponseFormatter:
    """Format analysis results into Discord embeds"""
    
    def __init__(self):
        self.fundamental_calc = FundamentalMetricsCalculator()
    
    def create_analysis_embed(self, analysis: AnalysisResult) -> discord.Embed:
        """Create a comprehensive analysis embed"""
        try:
            # Determine embed color based on recommendation
            color = self._get_recommendation_color(analysis.recommendation)
            
            embed = discord.Embed(
                title=f"📊 Stock Analysis: {analysis.symbol}",
                color=color,
                timestamp=analysis.timestamp
            )
            
            # Current Status Section
            embed.add_field(
                name="📈 Current Status",
                value=self._format_current_status(analysis),
                inline=False
            )
            
            # Technical Indicators Section
            if analysis.technical:
                embed.add_field(
                    name="🎯 Technical Indicators",
                    value=self._format_technical_indicators(analysis),
                    inline=False
                )
            
            # Fundamental Metrics Section
            if analysis.fundamental:
                embed.add_field(
                    name="📊 Fundamental Metrics",
                    value=self._format_fundamental_metrics(analysis),
                    inline=False
                )
            
            # AI Recommendation Section
            embed.add_field(
                name="🔮 AI Recommendation",
                value=self._format_ai_recommendation(analysis),
                inline=False
            )
            
            # Key Insights Section
            if analysis.key_insights:
                embed.add_field(
                    name="💡 Key Insights",
                    value=self._format_key_insights(analysis.key_insights),
                    inline=False
                )
            
            # Risk Warnings Section
            if analysis.risk and analysis.risk.risk_warnings:
                embed.add_field(
                    name="⚠️ Risk Warnings",
                    value=self._format_risk_warnings(analysis.risk.risk_warnings),
                    inline=False
                )
            
            # Footer with metadata
            embed.set_footer(text=self._generate_footer(analysis))
            
            return embed
            
        except Exception as e:
            logger.error(f"Error creating analysis embed: {e}")
            return self._create_error_embed(analysis.symbol)
    
    def _get_recommendation_color(self, recommendation: str) -> discord.Color:
        """Get embed color based on recommendation"""
        color_map = {
            "BUY": discord.Color.green(),
            "SELL": discord.Color.red(),
            "HOLD": discord.Color.gold()
        }
        return color_map.get(recommendation.upper(), discord.Color.blue())
    
    def _format_current_status(self, analysis: AnalysisResult) -> str:
        """Format current status information"""
        if not analysis.quote:
            return "📊 Current price data unavailable"
            
        price = analysis.quote.price
        change = analysis.quote.change or 0
        change_percent = analysis.quote.change_percent or 0
        volume = analysis.quote.volume or 0
        market_cap = analysis.quote.market_cap
        
        # Format change direction
        change_direction = "📈" if change >= 0 else "📉"
        change_str = f"{change_direction} ${abs(change):.2f} ({change_percent:+.1f}%)"
        
        status_lines = [
            f"💰 **Price:** ${price:.2f}",
            f"📊 **Change:** {change_str}",
            f"📊 **Volume:** {volume:,.0f}" if volume else "📊 **Volume:** N/A"
        ]
        
        if market_cap:
            status_lines.append(f"🏢 **Market Cap:** ${market_cap:,.0f}")
            
        return "\n".join(status_lines)
    
    def _format_technical_indicators(self, analysis: AnalysisResult) -> str:
        """Format technical indicators"""
        if not analysis.technical:
            return "⚠️ Technical indicators unavailable"
            
        tech = analysis.technical
        indicators = []
        
        # RSI
        if tech.rsi is not None:
            rsi_status = "Oversold" if tech.rsi < 30 else "Overbought" if tech.rsi > 70 else "Neutral"
            indicators.append(f"**RSI:** {tech.rsi:.1f} ({rsi_status})")
        
        # MACD
        if tech.macd:
            histogram = tech.macd.get('histogram', 0)
            trend = "Bullish" if histogram > 0 else "Bearish"
            indicators.append(f"**MACD:** {histogram:+.3f} ({trend})")
        
        # Moving Averages
        if tech.sma_50 is not None and tech.sma_200 is not None:
            ma_status = "Golden Cross" if tech.sma_50 > tech.sma_200 else "Death Cross"
            indicators.append(f"**MA Status:** {ma_status}")
        
        # Support/Resistance
        if tech.support_level is not None and tech.resistance_level is not None:
            indicators.append(f"**Support:** ${tech.support_level:.2f}")
            indicators.append(f"**Resistance:** ${tech.resistance_level:.2f}")
        
        return "\n".join(indicators) if indicators else "⚠️ Limited technical data available"
    
    def _format_fundamental_metrics(self, analysis: AnalysisResult) -> str:
        """Format fundamental metrics"""
        if not analysis.fundamental:
            return "⚠️ Fundamental data unavailable"
            
        fund = analysis.fundamental
        metrics = []
        
        # P/E Ratio
        if fund.pe_ratio is not None:
            pe_status = self.fundamental_calc.get_pe_status(fund.pe_ratio)
            metrics.append(f"**P/E:** {fund.pe_ratio:.1f} ({pe_status})")
        
        # EPS
        if fund.eps is not None:
            metrics.append(f"**EPS:** ${fund.eps:.2f}")
        
        # Growth
        if fund.revenue_growth is not None:
            growth_status = self.fundamental_calc.get_growth_status(fund.revenue_growth)
            growth_percent = fund.revenue_growth * 100
            metrics.append(f"**Revenue Growth:** {growth_percent:.1f}% ({growth_status})")
        
        # Margin
        if fund.profit_margin is not None:
            margin_status = self.fundamental_calc.get_margin_status(fund.profit_margin)
            margin_percent = fund.profit_margin * 100
            metrics.append(f"**Profit Margin:** {margin_percent:.1f}% ({margin_status})")
        
        # Debt
        if fund.debt_to_equity is not None:
            debt_status = self.fundamental_calc.get_debt_status(fund.debt_to_equity)
            metrics.append(f"**Debt/Equity:** {fund.debt_to_equity:.2f} ({debt_status})")
        
        return "\n".join(metrics) if metrics else "⚠️ Limited fundamental data available"
    
    def _format_ai_recommendation(self, analysis: AnalysisResult) -> str:
        """Format AI recommendation"""
        action_emoji = {
            "BUY": "🟢",
            "SELL": "🔴",
            "HOLD": "🟡"
        }.get(analysis.recommendation.upper(), "⚪")
        
        horizon_map = {
            "SHORT": "1-2 weeks",
            "MEDIUM": "1-3 months",
            "LONG": "3-6 months"
        }
        
        horizon_desc = horizon_map.get(analysis.time_horizon, analysis.time_horizon)
        
        return (
            f"**Action:** {action_emoji} {analysis.recommendation}\n"
            f"**Confidence:** {analysis.confidence}%\n"
            f"**Time Horizon:** {horizon_desc}\n"
            f"**Risk Level:** {getattr(analysis.risk, 'risk_level', 'Unknown') if analysis.risk else 'Unknown'}"
        )
    
    def _format_key_insights(self, insights: List[str]) -> str:
        """Format key insights"""
        if not insights:
            return "No specific insights available"
            
        formatted_insights = [f"• {insight}" for insight in insights[:5]]
        return "\n".join(formatted_insights)
    
    def _format_risk_warnings(self, warnings: List[str]) -> str:
        """Format risk warnings"""
        if not warnings:
            return "No significant risk warnings"
            
        formatted_warnings = [f"⚠️ {warning}" for warning in warnings[:3]]
        return "\n".join(formatted_warnings)
    
    def _generate_footer(self, analysis: AnalysisResult) -> str:
        """Generate footer with metadata"""
        data_sources = ", ".join(analysis.data_sources) if analysis.data_sources else "Limited data"
        quality = analysis.analysis_quality
        
        return f"Data Sources: {data_sources} | Analysis Quality: {quality} | Powered by AI"
    
    def _create_error_embed(self, symbol: str) -> discord.Embed:
        """Create error embed for failed analysis"""
        embed = discord.Embed(
            title=f"❌ Analysis Failed: {symbol}",
            description="Unable to complete analysis at this time.",
            color=discord.Color.red()
        )
        
        embed.add_field(
            name="Possible Issues",
            value="• Market data unavailable\n• Network connectivity issues\n• Symbol not found or delisted",
            inline=False
        )
        
        embed.set_footer(text="Please try again later or check the symbol")
        
        return embed
