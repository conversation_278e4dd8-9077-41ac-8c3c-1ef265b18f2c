"""
Real-time Watchlist Updates

This module provides real-time updates for watchlists, including:
- Live price updates for watchlist symbols
- Real-time notifications for price changes
- WebSocket integration for streaming market data
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime, timedelta

import discord
from discord.ext import tasks

from src.core.logger import get_logger
from src.shared.watchlist.models import WatchlistSymbol, UserWatchlist
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.bot.watchlist_manager import WatchlistManager
from src.bot.watchlist_alerts import AlertType

logger = get_logger(__name__)

class WatchlistRealtimeManager:
    """
    Manages real-time updates for watchlists
    
    Features:
    - Periodic price updates for watchlist symbols
    - Real-time notifications for price changes
    - Caching of market data for efficient updates
    """
    
    def __init__(self, watchlist_manager: WatchlistManager, bot=None):
        """
        Initialize the real-time watchlist manager
        
        Args:
            watchlist_manager: The watchlist manager instance
            bot: The Discord bot instance for sending notifications
        """
        self.watchlist_manager = watchlist_manager
        self.bot = bot
        
        # Cache for market data
        self.market_data_cache: Dict[str, Dict[str, Any]] = {}
        
        # Cache for user watchlists
        self.user_watchlists_cache: Dict[str, List[UserWatchlist]] = {}
        
        # Cache for active symbols
        self.active_symbols: Set[str] = set()
        
        # Data provider for market data
        self.data_provider = DataProviderAggregator()
        
        # Update intervals
        self.price_update_interval = 60  # seconds
        self.watchlist_refresh_interval = 300  # seconds
        
        # Last update timestamps
        self.last_price_update = 0
        self.last_watchlist_refresh = 0
        
        # Flag to indicate if the manager is running
        self.is_running = False
        
        # User notification preferences
        self.notification_preferences: Dict[str, Dict[str, Any]] = {}
        
        # Initialize tasks
        self.price_update_task = None
        self.watchlist_refresh_task = None
    
    async def start(self):
        """Start the real-time watchlist manager"""
        if self.is_running:
            logger.warning("Real-time watchlist manager is already running")
            return
        
        logger.info("Starting real-time watchlist manager")
        self.is_running = True
        
        # Start background tasks
        self.price_update_task = asyncio.create_task(self._price_update_loop())
        self.watchlist_refresh_task = asyncio.create_task(self._watchlist_refresh_loop())
        
        logger.info("Real-time watchlist manager started")
    
    async def stop(self):
        """Stop the real-time watchlist manager"""
        if not self.is_running:
            logger.warning("Real-time watchlist manager is not running")
            return
        
        logger.info("Stopping real-time watchlist manager")
        self.is_running = False
        
        # Cancel background tasks
        if self.price_update_task:
            self.price_update_task.cancel()
            
        if self.watchlist_refresh_task:
            self.watchlist_refresh_task.cancel()
        
        logger.info("Real-time watchlist manager stopped")
    
    async def _price_update_loop(self):
        """Background task for updating prices"""
        logger.info("Starting price update loop")
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # Check if it's time to update prices
                if current_time - self.last_price_update >= self.price_update_interval:
                    await self._update_prices()
                    self.last_price_update = current_time
                
                # Sleep for a short time
                await asyncio.sleep(1)
                
            except asyncio.CancelledError:
                logger.info("Price update loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in price update loop: {e}")
                await asyncio.sleep(5)  # Sleep longer on error
    
    async def _watchlist_refresh_loop(self):
        """Background task for refreshing watchlists"""
        logger.info("Starting watchlist refresh loop")
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # Check if it's time to refresh watchlists
                if current_time - self.last_watchlist_refresh >= self.watchlist_refresh_interval:
                    await self._refresh_watchlists()
                    self.last_watchlist_refresh = current_time
                
                # Sleep for a short time
                await asyncio.sleep(1)
                
            except asyncio.CancelledError:
                logger.info("Watchlist refresh loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in watchlist refresh loop: {e}")
                await asyncio.sleep(5)  # Sleep longer on error
    
    async def _update_prices(self):
        """Update prices for all active symbols"""
        if not self.active_symbols:
            logger.debug("No active symbols to update")
            return
        
        logger.info(f"Updating prices for {len(self.active_symbols)} symbols")
        
        # Get market data for all active symbols
        for symbol in self.active_symbols:
            try:
                # Get market data
                market_data = await self.data_provider.get_ticker(symbol)
                
                if market_data and not isinstance(market_data, str) and not market_data.get("error"):
                    # Check if we have previous data for this symbol
                    previous_data = self.market_data_cache.get(symbol)
                    
                    # Update cache
                    self.market_data_cache[symbol] = market_data
                    
                    # Check for significant price changes
                    if previous_data:
                        await self._check_price_changes(symbol, previous_data, market_data)
                        
            except Exception as e:
                logger.error(f"Error updating price for {symbol}: {e}")
    
    async def _refresh_watchlists(self):
        """Refresh watchlists for all users"""
        logger.info("Refreshing watchlists")
        
        try:
            # Get all users with watchlists
            user_ids = await self.watchlist_manager.get_all_users()
            
            # Clear active symbols
            self.active_symbols.clear()
            
            # Refresh watchlists for each user
            for user_id in user_ids:
                try:
                    # Get user watchlists
                    watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
                    
                    # Update cache
                    self.user_watchlists_cache[user_id] = watchlists
                    
                    # Add symbols to active symbols
                    for watchlist in watchlists:
                        for symbol in watchlist.symbols:
                            self.active_symbols.add(symbol.symbol)
                            
                except Exception as e:
                    logger.error(f"Error refreshing watchlists for user {user_id}: {e}")
            
            # Get notification preferences
            await self._refresh_notification_preferences()
            
            logger.info(f"Refreshed watchlists for {len(user_ids)} users, {len(self.active_symbols)} active symbols")
            
        except Exception as e:
            logger.error(f"Error refreshing watchlists: {e}")
    
    async def _refresh_notification_preferences(self):
        """Refresh notification preferences for all users"""
        try:
            # Get all users with watchlists
            user_ids = await self.watchlist_manager.get_all_users()
            
            # Get notification preferences for each user
            for user_id in user_ids:
                try:
                    # Get user alert preferences
                    alert_prefs = await self.watchlist_manager.get_user_alert_preferences(user_id)
                    
                    # Update cache
                    self.notification_preferences[user_id] = alert_prefs
                    
                except Exception as e:
                    logger.error(f"Error refreshing notification preferences for user {user_id}: {e}")
            
        except Exception as e:
            logger.error(f"Error refreshing notification preferences: {e}")
    
    async def _check_price_changes(self, symbol: str, previous_data: Dict[str, Any], current_data: Dict[str, Any]):
        """
        Check for significant price changes and notify users
        
        Args:
            symbol: The symbol to check
            previous_data: Previous market data
            current_data: Current market data
        """
        try:
            # Get previous and current prices
            previous_price = previous_data.get("current_price", 0)
            current_price = current_data.get("current_price", 0)
            
            if previous_price <= 0 or current_price <= 0:
                return
            
            # Calculate price change
            price_change = (current_price - previous_price) / previous_price * 100
            
            # Find users who have this symbol in their watchlist
            users_to_notify = []
            
            for user_id, alert_prefs in self.notification_preferences.items():
                if symbol in alert_prefs:
                    symbol_prefs = alert_prefs[symbol]
                    
                    # Check if alert is enabled
                    alert_threshold = symbol_prefs.get("alert_threshold")
                    alert_type = symbol_prefs.get("alert_type")
                    
                    if alert_threshold is not None and alert_type == AlertType.PRICE_CHANGE.value:
                        # Check if price change exceeds threshold
                        if abs(price_change) >= alert_threshold:
                            users_to_notify.append((user_id, symbol_prefs))
            
            # Notify users
            if users_to_notify:
                await self._notify_users(symbol, users_to_notify, current_price, previous_price, price_change)
                
        except Exception as e:
            logger.error(f"Error checking price changes for {symbol}: {e}")
    
    async def _notify_users(
        self, 
        symbol: str, 
        users_to_notify: List[Tuple[str, Dict[str, Any]]], 
        current_price: float,
        previous_price: float,
        price_change: float
    ):
        """
        Notify users about price changes
        
        Args:
            symbol: The symbol with price change
            users_to_notify: List of (user_id, preferences) tuples
            current_price: Current price
            previous_price: Previous price
            price_change: Price change percentage
        """
        if not self.bot:
            logger.warning("Cannot send notifications: bot is not available")
            return
        
        logger.info(f"Notifying {len(users_to_notify)} users about price change for {symbol}")
        
        # Create notification embed
        embed = discord.Embed(
            title=f"🔔 Price Alert: ${symbol}",
            description=f"Significant price movement detected for ${symbol}",
            color=discord.Color.gold() if price_change > 0 else discord.Color.red(),
            timestamp=discord.utils.utcnow()
        )
        
        # Add price information
        embed.add_field(
            name="Current Price",
            value=f"${current_price:.2f}",
            inline=True
        )
        
        embed.add_field(
            name="Previous Price",
            value=f"${previous_price:.2f}",
            inline=True
        )
        
        embed.add_field(
            name="Change",
            value=f"{'+' if price_change > 0 else ''}{price_change:.2f}%",
            inline=True
        )
        
        # Add footer
        embed.set_footer(text="Use /watchlist to manage your watchlist alerts")
        
        # Send notifications to users
        for user_id, prefs in users_to_notify:
            try:
                # Get user
                user = await self.bot.fetch_user(int(user_id))
                
                if user:
                    # Add user-specific notes if available
                    notes = prefs.get("notes")
                    if notes:
                        embed.add_field(
                            name="Your Notes",
                            value=notes,
                            inline=False
                        )
                    
                    # Send DM
                    await user.send(embed=embed)
                    logger.info(f"Sent price alert for {symbol} to user {user_id}")
                    
            except Exception as e:
                logger.error(f"Error sending notification to user {user_id}: {e}")
    
    async def get_real_time_data(self, symbol: str) -> Dict[str, Any]:
        """
        Get real-time data for a symbol
        
        Args:
            symbol: The symbol to get data for
            
        Returns:
            Dictionary with real-time market data
        """
        # Check if we have cached data
        if symbol in self.market_data_cache:
            return self.market_data_cache[symbol]
        
        # If not, fetch it
        try:
            market_data = await self.data_provider.get_ticker(symbol)
            
            if market_data and not isinstance(market_data, str) and not market_data.get("error"):
                # Update cache
                self.market_data_cache[symbol] = market_data
                return market_data
            
        except Exception as e:
            logger.error(f"Error getting real-time data for {symbol}: {e}")
        
        return {}
    
    async def get_user_watchlist_with_prices(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get user watchlists with real-time prices
        
        Args:
            user_id: The user ID
            
        Returns:
            List of watchlists with real-time prices
        """
        try:
            # Get user watchlists
            watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
            
            # Prepare result
            result = []
            
            for watchlist in watchlists:
                watchlist_data = {
                    "id": watchlist.id,
                    "name": watchlist.watchlist_name,
                    "symbols": []
                }
                
                # Get real-time data for each symbol
                for symbol in watchlist.symbols:
                    symbol_data = {
                        "symbol": symbol.symbol,
                        "notes": symbol.notes,
                        "alert_threshold": symbol.alert_threshold,
                        "alert_type": symbol.alert_type
                    }
                    
                    # Get real-time data
                    market_data = await self.get_real_time_data(symbol.symbol)
                    
                    if market_data:
                        symbol_data.update({
                            "current_price": market_data.get("current_price", "N/A"),
                            "change_percent": market_data.get("change_percent", "N/A"),
                            "volume": market_data.get("volume", "N/A"),
                            "last_updated": datetime.now().isoformat()
                        })
                    
                    watchlist_data["symbols"].append(symbol_data)
                
                result.append(watchlist_data)
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting user watchlist with prices for user {user_id}: {e}")
            return []


async def initialize_watchlist_realtime(watchlist_manager: WatchlistManager, bot=None) -> WatchlistRealtimeManager:
    """
    Initialize the watchlist real-time manager
    
    Args:
        watchlist_manager: The watchlist manager instance
        bot: The Discord bot instance for sending notifications
        
    Returns:
        Initialized watchlist real-time manager
    """
    try:
        # Create manager
        manager = WatchlistRealtimeManager(watchlist_manager, bot)
        
        # Start manager
        await manager.start()
        
        return manager
        
    except Exception as e:
        logger.error(f"Error initializing watchlist real-time manager: {e}")
        raise
