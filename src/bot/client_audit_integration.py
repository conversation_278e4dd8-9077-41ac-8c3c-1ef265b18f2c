"""
Bot Client Audit Integration

Integrates the request visualization and audit system with the main bot client.
This module adds hooks to automatically visualize all requests and send them to a developer channel.
"""

import discord
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

from .audit.request_visualizer import request_visualizer, AuditLevel
from .setup_audit import setup_audit_system, DEFAULT_WEBHOOK_URL
from src.core.logger import get_logger, generate_correlation_id

logger = get_logger(__name__)

class AuditIntegration:
    """
    Integrates request visualization and audit logging with the Discord bot
    
    This class adds hooks to automatically visualize all requests and
    send them to a developer channel for auditing.
    """
    
    def __init__(self, bot, dev_channel_id: Optional[int] = None, webhook_url: Optional[str] = None):
        """
        Initialize the audit integration
        
        Args:
            bot: The Discord bot instance
            dev_channel_id: The ID of the developer channel to send audit logs to
            webhook_url: The webhook URL to send audit logs to
        """
        self.bot = bot
        self.dev_channel_id = dev_channel_id
        self.webhook_url = webhook_url or DEFAULT_WEBHOOK_URL
        self.visualizer = None
        self.original_dispatch = None
        self.enabled = True
        
        # Register bot event handlers for proper session management
        if hasattr(bot, 'event'):
            @bot.event
            async def on_ready():
                logger.info("Bot is ready, initializing audit system...")
                if self.visualizer:
                    # Ensure session is created
                    if hasattr(self.visualizer, '_get_session'):
                        await self.visualizer._get_session()
                        
            @bot.event
            async def on_close():
                logger.info("Bot is shutting down, cleaning up audit system...")
                if self.visualizer and hasattr(self.visualizer, 'close'):
                    await self.visualizer.close()
        
    async def setup(self):
        """Set up the audit integration"""
        # Set up the audit system
        self.visualizer = setup_audit_system(
            bot=self.bot, 
            dev_channel_id=self.dev_channel_id,
            webhook_url=self.webhook_url,
            enable_logging=self.enabled
        )
        
        # Initialize the session if bot is already ready
        if hasattr(self.bot, 'is_ready') and self.bot.is_ready() and self.visualizer:
            if hasattr(self.visualizer, '_get_session'):
                await self.visualizer._get_session()
        
        # Monkey patch the bot's dispatch method to intercept interactions
        if hasattr(self.bot, 'dispatch') and callable(self.bot.dispatch):
            self.original_dispatch = self.bot.dispatch
            self.bot.dispatch = self._audit_dispatch
            logger.info("✅ Audit integration: Bot dispatch method patched for request visualization")
        else:
            logger.warning("❌ Audit integration: Failed to patch bot dispatch method")
        
        logger.info(f"✅ Audit integration set up with developer channel ID: {self.dev_channel_id}")
        
        return self.visualizer
    
    def _audit_dispatch(self, event_name, *args, **kwargs):
        """
        Patched dispatch method to intercept interactions
        
        This method intercepts Discord interactions and sends them to the
        request visualizer before passing them to the original dispatch method.
        """
        # Call the original dispatch method first
        if self.original_dispatch:
            self.original_dispatch(event_name, *args, **kwargs)
        
        # Intercept interaction create events
        if event_name == 'interaction_create' and args:
            interaction = args[0]
            
            # Only process application command interactions
            if hasattr(interaction, 'type') and interaction.type == discord.InteractionType.application_command:
                # Get command name and options
                command_name = interaction.data.get('name', 'unknown')
                options = interaction.data.get('options', [])
                
                # Extract command arguments
                args = {}
                for option in options:
                    args[option.get('name', 'unknown')] = option.get('value', None)
                
                # Generate correlation ID
                correlation_id = generate_correlation_id()
                
                # Visualize the request asynchronously
                async def visualize():
                    try:
                        await request_visualizer.visualize_request(
                            interaction=interaction,
                            command_name=command_name,
                            args=args,
                            correlation_id=correlation_id
                        )
                    except Exception as e:
                        logger.error(f"Error visualizing request: {e}")
                
                # Schedule the visualization task
                import asyncio
                asyncio.create_task(visualize())

async def add_audit_hooks(bot, dev_channel_id: Optional[int] = None, webhook_url: Optional[str] = None, enabled: bool = True):
    """
    Add audit hooks to the Discord bot
    
    This function adds hooks to automatically visualize all requests and
    send them to a developer channel or webhook for auditing.
    
    Args:
        bot: The Discord bot instance
        dev_channel_id: The ID of the developer channel to send audit logs to
        webhook_url: The webhook URL to send audit logs to
        enabled: Whether to enable audit logging
        
    Returns:
        The audit integration instance
    """
    integration = AuditIntegration(bot, dev_channel_id, webhook_url)
    integration.enabled = enabled
    await integration.setup()
    return integration


def add_audit_hooks_sync(bot, dev_channel_id: Optional[int] = None, webhook_url: Optional[str] = None, enabled: bool = True):
    """
    Synchronous version of add_audit_hooks for compatibility
    
    This function adds hooks to automatically visualize all requests and
    send them to a developer channel or webhook for auditing.
    
    Args:
        bot: The Discord bot instance
        dev_channel_id: The ID of the developer channel to send audit logs to
        webhook_url: The webhook URL to send audit logs to
        enabled: Whether to enable audit logging
        
    Returns:
        The audit integration instance
    """
    import asyncio
    
    integration = AuditIntegration(bot, dev_channel_id, webhook_url)
    integration.enabled = enabled
    
    # Create event loop if needed
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    # Schedule setup using create_task instead of blocking with run_until_complete
    setup_task = asyncio.create_task(integration.setup())
    
    # Add callback to log completion
    def on_setup_done(task):
        try:
            # Get the result to handle any exceptions
            task.result()
            logger.info("Audit system setup completed successfully")
        except Exception as e:
            logger.error(f"Error during audit system setup: {e}")
    
    # Add the callback to the task
    setup_task.add_done_callback(on_setup_done)
    
    logger.info("Scheduled audit system setup for asynchronous execution")
    return integration
