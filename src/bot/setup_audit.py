"""
Audit System Setup

Sets up the request visualization and audit system for the Discord bot.
This automatically sends all command requests to a developer channel for auditing.
"""

import discord
import logging
import os
from typing import Optional, Union, Any
from discord.ext import commands
from src.core.config_manager import get_config
from .audit.request_visualizer import setup_request_visualizer, request_visualizer, AuditLevel

# Get webhook URL from config instead of hardcoding
def get_default_webhook_url() -> str:
    """Get the default webhook URL from config or environment variables"""
    # Try to get from config manager first
    config = get_config()
    webhook_url = config.get('audit', 'webhook_url', '')
    
    # Fall back to environment variable if not in config
    if not webhook_url:
        webhook_url = os.environ.get('AUDIT_WEBHOOK_URL', '')
        
    if not webhook_url:
        logger.warning("No audit webhook URL configured. Audit logs will not be sent to webhook.")
        
    return webhook_url

logger = logging.getLogger(__name__)

# Default developer channel ID - replace with your actual developer channel ID
DEFAULT_DEV_CHANNEL_ID = None  # Set to None to use webhook by default

def setup_audit_system(bot: Union[discord.Client, commands.Bot], dev_channel_id: Optional[int] = None, webhook_url: Optional[str] = None, enable_logging: bool = True):
    """
    Set up the audit system for the Discord bot
    
    Args:
        bot: The Discord bot instance
        dev_channel_id: The ID of the developer channel to send audit logs to
        webhook_url: The webhook URL to send audit logs to
        enable_logging: Whether to enable audit logging
    """
    # Use provided values or defaults
    channel_id = dev_channel_id or DEFAULT_DEV_CHANNEL_ID
    webhook = webhook_url or get_default_webhook_url()
    
    # Set up the request visualizer
    visualizer = setup_request_visualizer(
        bot=bot, 
        dev_channel_id=channel_id,
        webhook_url=webhook
    )
    
    # Configure based on enable_logging flag
    visualizer.configure(
        enabled=enable_logging,
        log_to_console=enable_logging,
        log_to_channel=enable_logging and channel_id is not None,
        log_to_webhook=enable_logging and webhook is not None
    )
    
    # Register commands to visualize
    visualizer.register_commands([
        "analyze", "ask", "price", "watchlist", "portfolio", 
        "alerts", "settings", "search", "toggle_debug"
    ])
    
    # Log setup
    if enable_logging:
        if channel_id:
            logger.info(f"Audit system set up with developer channel ID: {channel_id}")
        if webhook:
            logger.info("Audit system set up with webhook URL for dev logs")
    else:
        logger.info("Audit system is disabled")
    
    # Add audit command for admins
    @bot.tree.command(
        name="audit_config",
        description="Configure the audit system (Admin only)"
    )
    async def audit_config_command(
        interaction: discord.Interaction,
        enabled: Optional[bool] = None,
        channel_id: Optional[str] = None,
        use_webhook: Optional[bool] = None
    ):
        """Configure the audit system"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
        
        # Update configuration
        if enabled is not None:
            request_visualizer.enabled = enabled
            request_visualizer.log_to_console = enabled
            request_visualizer.log_to_channel = enabled and request_visualizer.dev_channel_id is not None
            request_visualizer.log_to_webhook = enabled and request_visualizer.webhook_url is not None
        
        if channel_id:
            try:
                new_channel_id = int(channel_id)
                request_visualizer.dev_channel_id = new_channel_id
                request_visualizer.log_to_channel = request_visualizer.enabled and new_channel_id is not None
            except ValueError:
                await interaction.response.send_message(
                    "❌ Invalid channel ID. Please provide a valid integer.",
                    ephemeral=True
                )
                return
                
        # Toggle webhook usage
        if use_webhook is not None:
            request_visualizer.log_to_webhook = use_webhook and request_visualizer.enabled
            
            # Initialize session if needed
            if request_visualizer.log_to_webhook and not request_visualizer.session:
                import aiohttp
                request_visualizer.session = aiohttp.ClientSession()
        
        # Send confirmation
        await interaction.response.send_message(
            f"✅ Audit system configuration updated:\n"
            f"• Enabled: {request_visualizer.enabled}\n"
            f"• Developer Channel ID: {request_visualizer.dev_channel_id or 'Not set'}\n"
            f"• Webhook Logging: {'Enabled' if request_visualizer.log_to_webhook else 'Disabled'}",
            ephemeral=True
        )
        
        # Log configuration change
        await request_visualizer.log_audit_event(
            level=AuditLevel.INFO,
            message=f"Audit system configuration updated by {interaction.user.name}",
            data={
                "enabled": request_visualizer.enabled,
                "dev_channel_id": request_visualizer.dev_channel_id
            }
        )
    
    # Add command to toggle webhook logging
    @bot.tree.command(
        name="toggle_webhook",
        description="Toggle webhook logging for dev logs (Admin only)"
    )
    async def toggle_webhook_command(interaction: discord.Interaction):
        """Toggle webhook logging for dev logs"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
            
        # Toggle webhook logging
        request_visualizer.log_to_webhook = not request_visualizer.log_to_webhook
        
        # Initialize session if needed
        if request_visualizer.log_to_webhook and not request_visualizer.session:
            import aiohttp
            request_visualizer.session = aiohttp.ClientSession()
        
        # Send confirmation
        status = "enabled" if request_visualizer.log_to_webhook else "disabled"
        await interaction.response.send_message(
            f"✅ Webhook logging is now {status}.",
            ephemeral=True
        )
        
        # Log configuration change
        await request_visualizer.log_audit_event(
            level=AuditLevel.INFO,
            message=f"Webhook logging {status} by {interaction.user.name}",
            data={
                "webhook_enabled": request_visualizer.log_to_webhook
            }
        )
    
    # Add command to test audit system
    @bot.tree.command(
        name="test_audit",
        description="Test the audit system (Admin only)"
    )
    async def test_audit_command(interaction: discord.Interaction):
        """Test the audit system"""
        # Check if user is an admin
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You don't have permission to use this command.",
                ephemeral=True
            )
            return
        
        # Send test message
        await interaction.response.send_message(
            "🧪 Testing audit system...",
            ephemeral=True
        )
        
        # Log test events at different levels
        for level in AuditLevel:
            await request_visualizer.log_audit_event(
                level=level,
                message=f"Test audit event at {level.value} level",
                command_name="test_audit",
                data={
                    "test": True,
                    "level": level.value,
                    "timestamp": discord.utils.utcnow().isoformat()
                }
            )
        
        # Send test request visualization
        await request_visualizer.visualize_request(
            interaction=interaction,
            command_name="test_audit",
            args={"test": True},
            correlation_id="test-correlation-id"
        )
        
        # Send test response visualization
        await request_visualizer.visualize_response(
            interaction=interaction,
            command_name="test_audit",
            response_data="Test response data",
            execution_time=0.5,
            correlation_id="test-correlation-id",
            success=True
        )
        
        # Send test pipeline visualization
        pipeline_data = {
            "status": "completed",
            "execution_time": 1.5,
            "stages": [
                {
                    "name": "initialization",
                    "status": "completed",
                    "duration": 0.2
                },
                {
                    "name": "processing",
                    "status": "completed",
                    "duration": 0.8
                },
                {
                    "name": "completion",
                    "status": "completed",
                    "duration": 0.5
                }
            ],
            "errors": []
        }
        
        await request_visualizer.visualize_pipeline(
            interaction=interaction,
            command_name="test_audit",
            pipeline_data=pipeline_data,
            correlation_id="test-correlation-id"
        )
        
        # Send confirmation
        await interaction.followup.send(
            "✅ Audit system test completed. Check the developer channel for results.",
            ephemeral=True
        )
    
    return visualizer
