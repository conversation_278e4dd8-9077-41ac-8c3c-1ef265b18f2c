"""
Request Visualizer Patch

This module patches the RequestVisualizer class to use the session manager
for HTTP requests instead of creating a new session for each request.
"""

import asyncio
import logging
from typing import Optional, Dict, Any

from .request_visualizer import request_visualizer
from .session_manager import get_session, close_session

logger = logging.getLogger(__name__)

# Patch the _get_session method
async def _get_session(self):
    """
    Get a shared aiohttp.ClientSession from the session manager.
    
    Returns:
        aiohttp.ClientSession: The shared client session
    """
    return await get_session()

# Patch the close method
async def close(self):
    """
    Close the shared aiohttp.ClientSession.
    
    This should be called during application shutdown.
    """
    await close_session()

# Patch the send_webhook method
async def send_webhook(self, content: Dict[str, Any]):
    """
    Send a message to the webhook URL.
    
    Args:
        content: The content to send
    """
    if not self.webhook_url or not self.log_to_webhook:
        return
    
    try:
        session = await self._get_session()
        async with session.post(
            self.webhook_url,
            json=content,
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status >= 400:
                logger.warning(f"Failed to send webhook: {response.status} {await response.text()}")
    except Exception as e:
        logger.error(f"Error sending webhook: {e}")

# Apply the patches
request_visualizer._get_session = _get_session.__get__(request_visualizer)
request_visualizer.close = close.__get__(request_visualizer)
request_visualizer.send_webhook = send_webhook.__get__(request_visualizer)

# Log that the patches have been applied
logger.info("Applied session manager patches to RequestVisualizer")
