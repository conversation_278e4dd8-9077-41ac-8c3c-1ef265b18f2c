"""
Enhanced Zones Command Module

Implements enhanced support/resistance zones analysis with multi-timeframe capabilities
and probability scoring for more accurate trading decisions.
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from src.core.logger import get_logger
from src.bot.permissions import PermissionLevel, require_permission
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.pipeline.commands.analyze.parallel_pipeline import execute_parallel_analyze_pipeline
from src.bot.utils.disclaimer_manager import add_disclaimer

logger = get_logger(__name__)

# Define available timeframes for analysis
TIMEFRAMES = {
    "1d": {"name": "1 Day", "description": "Short-term analysis (1 day)"},
    "1w": {"name": "1 Week", "description": "Medium-term analysis (1 week)"},
    "1m": {"name": "1 Month", "description": "Long-term analysis (1 month)"},
    "3m": {"name": "3 Months", "description": "Extended analysis (3 months)"},
    "6m": {"name": "6 Months", "description": "Long-range analysis (6 months)"},
    "1y": {"name": "1 Year", "description": "Annual analysis (1 year)"}
}

class EnhancedZonesCommands(commands.Cog):
    """Enhanced zones commands with multi-timeframe analysis"""
    
    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        
        # Maximum number of concurrent analyses
        self.max_concurrent_analyses = 3
        self.semaphore = asyncio.Semaphore(self.max_concurrent_analyses)
    
    @app_commands.command(name="zones", description="Get detailed support and resistance zones with multi-timeframe analysis")
    @app_commands.describe(
        symbol="Stock symbol to analyze (e.g., AAPL, MSFT, TSLA)",
        timeframe="Timeframe for analysis (e.g., 1d, 1w, 1m)",
        include_probability="Include probability scoring for zone strength"
    )
    @app_commands.choices(
        timeframe=[
            app_commands.Choice(name="1 Day", value="1d"),
            app_commands.Choice(name="1 Week", value="1w"),
            app_commands.Choice(name="1 Month", value="1m"),
            app_commands.Choice(name="3 Months", value="3m"),
            app_commands.Choice(name="6 Months", value="6m"),
            app_commands.Choice(name="1 Year", value="1y")
        ]
    )
    async def zones_command(
        self, 
        interaction: discord.Interaction, 
        symbol: str,
        timeframe: Optional[str] = "1d",
        include_probability: Optional[bool] = True
    ):
        """
        Get detailed support and resistance zones with multi-timeframe analysis
        
        Parameters:
        -----------
        symbol: str
            Stock symbol to analyze
        timeframe: str, optional
            Timeframe for analysis (e.g., 1d, 1w, 1m)
        include_probability: bool, optional
            Include probability scoring for zone strength
        """
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        # Validate timeframe
        if timeframe not in TIMEFRAMES:
            await interaction.response.send_message(
                f"❌ Invalid timeframe: {timeframe}. Please use one of: {', '.join(TIMEFRAMES.keys())}"
            )
            return
        
        # Defer response to allow for processing time
        await interaction.response.defer(thinking=True)
        
        try:
            # Send initial message
            await interaction.followup.send(
                f"🔍 Analyzing support and resistance zones for {sanitized_symbol} with {TIMEFRAMES[timeframe]['name']} timeframe..."
            )
            
            # Execute analysis with semaphore to limit concurrency
            async with self.semaphore:
                # Execute parallel analyze pipeline
                context = await execute_parallel_analyze_pipeline(
                    ticker=sanitized_symbol,
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=f"zones_{sanitized_symbol}_{timeframe}_{datetime.now().timestamp()}"
                )
                
                # Ensure include_probability is a boolean
                include_prob_bool = bool(include_probability) if include_probability is not None else True
                # Extract zones data
                zones_data = self._extract_zones_data(context, timeframe, include_prob_bool)
                
                # Create embed
                embed = self._create_zones_embed(sanitized_symbol, zones_data, interaction.user, timeframe)
                
                # Add disclaimer
                embed.set_footer(text="Support/resistance zones are based on technical analysis and not guaranteed. Not financial advice.")
                
                # Send the response
                await interaction.followup.send(embed=embed)
                
        except asyncio.TimeoutError:
            await interaction.followup.send(
                "⏰ The zones analysis took longer than expected. Please try again later."
            )
        except Exception as e:
            logger.error(f"Error in zones command: {e}")
            await interaction.followup.send(
                f"❌ I encountered an error while analyzing zones for {sanitized_symbol}. Please try again later."
            )
    
    @app_commands.command(name="multizones", description="Compare support/resistance zones across multiple timeframes")
    @app_commands.describe(
        symbol="Stock symbol to analyze (e.g., AAPL, MSFT, TSLA)",
        include_probability="Include probability scoring for zone strength"
    )
    async def multizones_command(
        self, 
        interaction: discord.Interaction, 
        symbol: str,
        include_probability: Optional[bool] = True
    ):
        """
        Compare support/resistance zones across multiple timeframes
        
        Parameters:
        -----------
        symbol: str
            Stock symbol to analyze
        include_probability: bool, optional
            Include probability scoring for zone strength
        """
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        # Defer response to allow for processing time
        await interaction.response.defer(thinking=True)
        
        try:
            # Send initial message
            await interaction.followup.send(
                f"🔍 Analyzing support and resistance zones for {sanitized_symbol} across multiple timeframes..."
            )
            
            # Select timeframes to analyze
            timeframes_to_analyze = ["1d", "1w", "1m"]
            
            # Execute analysis for each timeframe
            results = {}
            for timeframe in timeframes_to_analyze:
                # Execute analysis with semaphore to limit concurrency
                async with self.semaphore:
                    # Execute parallel analyze pipeline
                    context = await execute_parallel_analyze_pipeline(
                        ticker=sanitized_symbol,
                        user_id=str(interaction.user.id),
                        guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                        correlation_id=f"multizones_{sanitized_symbol}_{timeframe}_{datetime.now().timestamp()}"
                    )
                    
                    # Ensure include_probability is a boolean
                    include_prob_bool = bool(include_probability) if include_probability is not None else True
                    # Extract zones data
                    zones_data = self._extract_zones_data(context, timeframe, include_prob_bool)
                    results[timeframe] = zones_data
            
            # Create multi-timeframe comparison embed
            embed = self._create_multizones_embed(sanitized_symbol, results, interaction.user)
            
            # Add disclaimer
            embed.set_footer(text="Support/resistance zones are based on technical analysis and not guaranteed. Not financial advice.")
            
            # Send the response
            await interaction.followup.send(embed=embed)
                
        except asyncio.TimeoutError:
            await interaction.followup.send(
                "⏰ The multi-timeframe zones analysis took longer than expected. Please try again later."
            )
        except Exception as e:
            logger.error(f"Error in multizones command: {e}")
            await interaction.followup.send(
                f"❌ I encountered an error while analyzing zones for {sanitized_symbol}. Please try again later."
            )
    
    def _extract_zones_data(self, context, timeframe: str, include_probability: bool) -> Dict[str, Any]:
        """
        Extract zones data from analysis context
        
        Args:
            context: Analysis context
            timeframe: Timeframe for analysis
            include_probability: Whether to include probability scoring
            
        Returns:
            Dictionary with zones data
        """
        # Default empty data
        zones_data = {
            "support_levels": [],
            "resistance_levels": [],
            "current_price": 0,
            "timeframe": timeframe,
            "probability_scores": {}
        }
        
        try:
            # Extract data from context
            if hasattr(context, "processing_results"):
                # Get market data
                market_data = context.processing_results.get("market_data", {})
                if market_data:
                    zones_data["current_price"] = market_data.get("current_price", 0)
                
                # Get technical analysis data
                ta_data = context.processing_results.get("technical_analysis", {})
                if ta_data:
                    # Extract support and resistance levels
                    zones_data["support_levels"] = ta_data.get("support_levels", [])
                    zones_data["resistance_levels"] = ta_data.get("resistance_levels", [])
                    
                    # Extract trend
                    zones_data["trend"] = ta_data.get("trend", "unknown")
                    
                    # Calculate probability scores if requested
                    if include_probability:
                        zones_data["probability_scores"] = self._calculate_probability_scores(
                            zones_data["support_levels"],
                            zones_data["resistance_levels"],
                            zones_data["current_price"],
                            ta_data
                        )
        except Exception as e:
            logger.error(f"Error extracting zones data: {e}")
        
        return zones_data
    
    def _calculate_probability_scores(
        self,
        support_levels: List[float],
        resistance_levels: List[float],
        current_price: float,
        ta_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Calculate probability scores for support and resistance levels
        
        Args:
            support_levels: List of support levels
            resistance_levels: List of resistance levels
            current_price: Current price
            ta_data: Technical analysis data
            
        Returns:
            Dictionary with probability scores
        """
        probability_scores = {
            "support": {},
            "resistance": {}
        }
        
        try:
            # Get additional technical indicators
            rsi = ta_data.get("rsi", 50)
            trend = ta_data.get("trend", "sideways")
            volatility = ta_data.get("volatility", "medium")
            
            # Calculate base strength factors
            trend_factor = 1.2 if trend == "uptrend" else 0.8 if trend == "downtrend" else 1.0
            volatility_factor = 0.8 if volatility == "high" else 1.2 if volatility == "low" else 1.0
            
            # Calculate support level probabilities
            for i, level in enumerate(support_levels):
                if level <= 0:
                    continue
                    
                # Calculate distance from current price
                distance = abs(current_price - level) / current_price
                
                # Calculate strength based on multiple factors
                strength = 0.9 - (distance * 2)  # Base strength decreases with distance
                
                # Adjust for trend (support is stronger in uptrends)
                strength *= trend_factor
                
                # Adjust for volatility (support is weaker in high volatility)
                strength *= volatility_factor
                
                # Adjust for RSI (support is stronger when RSI is low)
                if rsi < 30:
                    strength *= 1.2
                elif rsi > 70:
                    strength *= 0.8
                
                # Adjust for position in the list (earlier supports are stronger)
                strength *= (1.0 - (i * 0.1))
                
                # Ensure strength is between 0 and 1
                strength = max(0.1, min(0.95, strength))
                
                # Store probability
                probability_scores["support"][f"{level:.2f}"] = round(strength * 100)
            
            # Calculate resistance level probabilities
            for i, level in enumerate(resistance_levels):
                if level <= 0:
                    continue
                    
                # Calculate distance from current price
                distance = abs(current_price - level) / current_price
                
                # Calculate strength based on multiple factors
                strength = 0.9 - (distance * 2)  # Base strength decreases with distance
                
                # Adjust for trend (resistance is stronger in downtrends)
                strength *= (1.0 / trend_factor)  # Inverse of trend factor
                
                # Adjust for volatility (resistance is weaker in high volatility)
                strength *= volatility_factor
                
                # Adjust for RSI (resistance is stronger when RSI is high)
                if rsi > 70:
                    strength *= 1.2
                elif rsi < 30:
                    strength *= 0.8
                
                # Adjust for position in the list (earlier resistances are stronger)
                strength *= (1.0 - (i * 0.1))
                
                # Ensure strength is between 0 and 1
                strength = max(0.1, min(0.95, strength))
                
                # Store probability
                probability_scores["resistance"][f"{level:.2f}"] = round(strength * 100)
        except Exception as e:
            logger.error(f"Error calculating probability scores: {e}")
        
        return probability_scores
    
    def _create_zones_embed(
        self,
        symbol: str,
        zones_data: Dict[str, Any],
        user: discord.User,
        timeframe: str
    ) -> discord.Embed:
        """
        Create embed for zones analysis
        
        Args:
            symbol: Symbol being analyzed
            zones_data: Zones data
            user: User who requested the analysis
            timeframe: Timeframe for analysis
            
        Returns:
            Discord embed with zones analysis
        """
        # Get data
        current_price = zones_data.get("current_price", 0)
        support_levels = zones_data.get("support_levels", [])
        resistance_levels = zones_data.get("resistance_levels", [])
        trend = zones_data.get("trend", "unknown")
        probability_scores = zones_data.get("probability_scores", {})
        
        # Create embed
        embed = discord.Embed(
            title=f"Support & Resistance Zones: ${symbol}",
            description=f"Analysis for {TIMEFRAMES[timeframe]['name']} timeframe",
            color=discord.Color.blue()
        )
        
        # Add current price
        if current_price > 0:
            embed.add_field(
                name="Current Price",
                value=f"${current_price:.2f}",
                inline=True
            )
        
        # Add trend
        embed.add_field(
            name="Trend",
            value=trend.capitalize(),
            inline=True
        )
        
        # Add timeframe
        embed.add_field(
            name="Timeframe",
            value=TIMEFRAMES[timeframe]["name"],
            inline=True
        )
        
        # Add support levels with probability scores
        if support_levels:
            support_text = ""
            for i, level in enumerate(support_levels[:5]):  # Limit to top 5
                if level <= 0:
                    continue
                    
                # Add probability score if available
                prob_score = probability_scores.get("support", {}).get(f"{level:.2f}")
                if prob_score is not None:
                    support_text += f"S{i+1}: ${level:.2f} (Strength: {prob_score}%)\n"
                else:
                    support_text += f"S{i+1}: ${level:.2f}\n"
            
            if support_text:
                embed.add_field(
                    name="Support Levels",
                    value=support_text,
                    inline=True
                )
        
        # Add resistance levels with probability scores
        if resistance_levels:
            resistance_text = ""
            for i, level in enumerate(resistance_levels[:5]):  # Limit to top 5
                if level <= 0:
                    continue
                    
                # Add probability score if available
                prob_score = probability_scores.get("resistance", {}).get(f"{level:.2f}")
                if prob_score is not None:
                    resistance_text += f"R{i+1}: ${level:.2f} (Strength: {prob_score}%)\n"
                else:
                    resistance_text += f"R{i+1}: ${level:.2f}\n"
            
            if resistance_text:
                embed.add_field(
                    name="Resistance Levels",
                    value=resistance_text,
                    inline=True
                )
        
        # Add key levels analysis
        if current_price > 0 and (support_levels or resistance_levels):
            # Find closest support and resistance
            closest_support = None
            closest_support_distance = float('inf')
            for level in support_levels:
                if level < current_price:
                    distance = current_price - level
                    if distance < closest_support_distance:
                        closest_support = level
                        closest_support_distance = distance
            
            closest_resistance = None
            closest_resistance_distance = float('inf')
            for level in resistance_levels:
                if level > current_price:
                    distance = level - current_price
                    if distance < closest_resistance_distance:
                        closest_resistance = level
                        closest_resistance_distance = distance
            
            # Calculate risk/reward
            if closest_support and closest_resistance:
                risk = current_price - closest_support
                reward = closest_resistance - current_price
                
                if risk > 0:
                    risk_reward_ratio = reward / risk
                    
                    embed.add_field(
                        name="Key Levels Analysis",
                        value=f"Closest Support: ${closest_support:.2f}\n"
                              f"Closest Resistance: ${closest_resistance:.2f}\n"
                              f"Risk/Reward Ratio: {risk_reward_ratio:.2f}",
                        inline=False
                    )
        
        # Add timestamp
        embed.timestamp = discord.utils.utcnow()
        
        # Add author
        embed.set_author(
            name=f"Requested by {user.display_name}",
            icon_url=user.display_avatar.url if hasattr(user, 'display_avatar') else None
        )
        
        return embed
    
    def _create_multizones_embed(
        self,
        symbol: str,
        results: Dict[str, Dict[str, Any]],
        user: discord.User
    ) -> discord.Embed:
        """
        Create embed for multi-timeframe zones analysis
        
        Args:
            symbol: Symbol being analyzed
            results: Results for each timeframe
            user: User who requested the analysis
            
        Returns:
            Discord embed with multi-timeframe zones analysis
        """
        # Get current price from any timeframe
        current_price = 0
        for timeframe, data in results.items():
            if data.get("current_price", 0) > 0:
                current_price = data.get("current_price")
                break
        
        # Create embed
        embed = discord.Embed(
            title=f"Multi-Timeframe Zones Analysis: ${symbol}",
            description=f"Support and resistance zones across multiple timeframes",
            color=discord.Color.gold()
        )
        
        # Add current price
        if current_price > 0:
            embed.add_field(
                name="Current Price",
                value=f"${current_price:.2f}",
                inline=True
            )
        
        # Add analysis for each timeframe
        for timeframe, data in results.items():
            support_levels = data.get("support_levels", [])
            resistance_levels = data.get("resistance_levels", [])
            trend = data.get("trend", "unknown")
            probability_scores = data.get("probability_scores", {})
            
            # Create field value
            field_value = f"Trend: {trend.capitalize()}\n\n"
            
            # Add support levels
            if support_levels:
                field_value += "**Support Levels:**\n"
                for i, level in enumerate(support_levels[:3]):  # Limit to top 3
                    if level <= 0:
                        continue
                        
                    # Add probability score if available
                    prob_score = probability_scores.get("support", {}).get(f"{level:.2f}")
                    if prob_score is not None:
                        field_value += f"S{i+1}: ${level:.2f} ({prob_score}%)\n"
                    else:
                        field_value += f"S{i+1}: ${level:.2f}\n"
                field_value += "\n"
            
            # Add resistance levels
            if resistance_levels:
                field_value += "**Resistance Levels:**\n"
                for i, level in enumerate(resistance_levels[:3]):  # Limit to top 3
                    if level <= 0:
                        continue
                        
                    # Add probability score if available
                    prob_score = probability_scores.get("resistance", {}).get(f"{level:.2f}")
                    if prob_score is not None:
                        field_value += f"R{i+1}: ${level:.2f} ({prob_score}%)\n"
                    else:
                        field_value += f"R{i+1}: ${level:.2f}\n"
            
            # Add field
            embed.add_field(
                name=f"{TIMEFRAMES[timeframe]['name']} Analysis",
                value=field_value,
                inline=False
            )
        
        # Add confluence analysis
        embed.add_field(
            name="Zone Confluence Analysis",
            value=self._generate_confluence_analysis(results, current_price),
            inline=False
        )
        
        # Add timestamp
        embed.timestamp = discord.utils.utcnow()
        
        # Add author
        embed.set_author(
            name=f"Requested by {user.display_name}",
            icon_url=user.display_avatar.url if hasattr(user, 'display_avatar') else None
        )
        
        return embed
    
    def _generate_confluence_analysis(
        self,
        results: Dict[str, Dict[str, Any]],
        current_price: float
    ) -> str:
        """
        Generate confluence analysis for multi-timeframe zones
        
        Args:
            results: Results for each timeframe
            current_price: Current price
            
        Returns:
            Confluence analysis text
        """
        # Collect all support and resistance levels
        all_supports = []
        all_resistances = []
        
        for timeframe, data in results.items():
            supports = data.get("support_levels", [])
            resistances = data.get("resistance_levels", [])
            
            for level in supports:
                if level > 0:
                    all_supports.append((level, timeframe))
            
            for level in resistances:
                if level > 0:
                    all_resistances.append((level, timeframe))
        
        # Find confluence zones (levels that are close to each other)
        support_zones = self._find_confluence_zones(all_supports, current_price * 0.01)
        resistance_zones = self._find_confluence_zones(all_resistances, current_price * 0.01)
        
        # Generate analysis text
        analysis = ""
        
        # Add strong support zones
        if support_zones:
            analysis += "**Strong Support Zones:**\n"
            for zone in support_zones[:3]:  # Limit to top 3
                center = zone["center"]
                timeframes = zone["timeframes"]
                strength = zone["strength"]
                
                analysis += f"${center:.2f} (Strength: {strength}/10, Timeframes: {', '.join(timeframes)})\n"
            analysis += "\n"
        
        # Add strong resistance zones
        if resistance_zones:
            analysis += "**Strong Resistance Zones:**\n"
            for zone in resistance_zones[:3]:  # Limit to top 3
                center = zone["center"]
                timeframes = zone["timeframes"]
                strength = zone["strength"]
                
                analysis += f"${center:.2f} (Strength: {strength}/10, Timeframes: {', '.join(timeframes)})\n"
            analysis += "\n"
        
        # Add trading recommendation based on zones
        if support_zones and resistance_zones and current_price > 0:
            # Find closest support and resistance
            closest_support = None
            closest_support_distance = float('inf')
            for zone in support_zones:
                center = zone["center"]
                if center < current_price:
                    distance = current_price - center
                    if distance < closest_support_distance:
                        closest_support = zone
                        closest_support_distance = distance
            
            closest_resistance = None
            closest_resistance_distance = float('inf')
            for zone in resistance_zones:
                center = zone["center"]
                if center > current_price:
                    distance = center - current_price
                    if distance < closest_resistance_distance:
                        closest_resistance = zone
                        closest_resistance_distance = distance
            
            if closest_support and closest_resistance:
                # Calculate risk/reward
                risk = current_price - closest_support["center"]
                reward = closest_resistance["center"] - current_price
                
                if risk > 0:
                    risk_reward_ratio = reward / risk
                    
                    analysis += "**Trading Zones:**\n"
                    analysis += f"Support Zone: ${closest_support['center']:.2f} (Strength: {closest_support['strength']}/10)\n"
                    analysis += f"Resistance Zone: ${closest_resistance['center']:.2f} (Strength: {closest_resistance['strength']}/10)\n"
                    analysis += f"Risk/Reward Ratio: {risk_reward_ratio:.2f}\n"
        
        if not analysis:
            analysis = "No strong confluence zones detected across timeframes."
        
        return analysis
    
    def _find_confluence_zones(self, levels: List[tuple], tolerance: float) -> List[Dict[str, Any]]:
        """
        Find confluence zones among levels
        
        Args:
            levels: List of (level, timeframe) tuples
            tolerance: Tolerance for grouping levels
            
        Returns:
            List of confluence zones
        """
        if not levels:
            return []
        
        # Sort levels
        sorted_levels = sorted(levels, key=lambda x: x[0])
        
        # Group levels that are close to each other
        zones = []
        current_zone = {
            "levels": [sorted_levels[0][0]],
            "timeframes": [sorted_levels[0][1]]
        }
        
        for i in range(1, len(sorted_levels)):
            level, timeframe = sorted_levels[i]
            prev_level = sorted_levels[i-1][0]
            
            if abs(level - prev_level) <= tolerance:
                # Add to current zone
                current_zone["levels"].append(level)
                current_zone["timeframes"].append(timeframe)
            else:
                # Finalize current zone
                if current_zone["levels"]:
                    center = sum(current_zone["levels"]) / len(current_zone["levels"])
                    strength = min(10, len(set(current_zone["timeframes"])) * 2 + len(current_zone["levels"]) - 1)
                    
                    zones.append({
                        "center": center,
                        "levels": current_zone["levels"],
                        "timeframes": list(set(current_zone["timeframes"])),
                        "strength": strength
                    })
                
                # Start new zone
                current_zone = {
                    "levels": [level],
                    "timeframes": [timeframe]
                }
        
        # Add last zone
        if current_zone["levels"]:
            center = sum(current_zone["levels"]) / len(current_zone["levels"])
            strength = min(10, len(set(current_zone["timeframes"])) * 2 + len(current_zone["levels"]) - 1)
            
            zones.append({
                "center": center,
                "levels": current_zone["levels"],
                "timeframes": list(set(current_zone["timeframes"])),
                "strength": strength
            })
        
        # Sort zones by strength
        return sorted(zones, key=lambda x: x["strength"], reverse=True)


async def setup(bot):
    """Add the enhanced zones commands to the bot"""
    await bot.add_cog(EnhancedZonesCommands(bot))
