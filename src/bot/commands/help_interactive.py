"""
Interactive Help Command Module

Implements an interactive help command with tutorials and rich UI components.
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

from src.core.logger import get_logger
from src.bot.permissions import PermissionLevel, require_permission

logger = get_logger(__name__)

# Command categories and descriptions
COMMAND_CATEGORIES = {
    "analysis": {
        "name": "Analysis Commands",
        "description": "Commands for technical and fundamental analysis",
        "emoji": "📊",
        "commands": [
            {
                "name": "/analyze",
                "description": "Get detailed technical analysis for a stock",
                "usage": "/analyze symbol:AAPL timeframe:1d",
                "example": "Provides RSI, MACD, support/resistance levels, and more for AAPL"
            },
            {
                "name": "/compare",
                "description": "Compare technical analysis for multiple stocks",
                "usage": "/compare symbols:AAPL,MSFT,GOOGL timeframe:1d",
                "example": "Side-by-side comparison of key metrics for multiple symbols"
            },
            {
                "name": "/zones",
                "description": "Get support and resistance zones with probability scoring",
                "usage": "/zones symbol:AAPL timeframe:1d include_probability:True",
                "example": "Shows key price levels with strength indicators"
            },
            {
                "name": "/multizones",
                "description": "Compare support/resistance zones across multiple timeframes",
                "usage": "/multizones symbol:AAPL include_probability:True",
                "example": "Shows zone confluence across daily, weekly, and monthly timeframes"
            }
        ]
    },
    "ai": {
        "name": "AI-Powered Commands",
        "description": "Commands that leverage AI for insights and recommendations",
        "emoji": "🤖",
        "commands": [
            {
                "name": "/ask",
                "description": "Ask the AI about trading and markets",
                "usage": "/ask query:\"What is the outlook for AAPL?\"",
                "example": "Get AI-powered insights about specific stocks or general market questions"
            },
            {
                "name": "/recommendations",
                "description": "Get personalized trading recommendations",
                "usage": "/recommendations symbol:AAPL include_backtesting:True",
                "example": "Provides entry/exit points and risk management based on your risk profile"
            },
            {
                "name": "/riskprofile",
                "description": "View or update your risk profile",
                "usage": "/riskprofile risk_level:moderate",
                "example": "Set your risk tolerance for personalized recommendations"
            }
        ]
    },
    "watchlist": {
        "name": "Watchlist Commands",
        "description": "Commands for managing your stock watchlist",
        "emoji": "👀",
        "commands": [
            {
                "name": "/watchlist view",
                "description": "View your watchlist",
                "usage": "/watchlist action:view",
                "example": "Shows all symbols in your watchlist with current prices"
            },
            {
                "name": "/watchlist add",
                "description": "Add a symbol to your watchlist",
                "usage": "/watchlist action:add symbol:AAPL notes:\"Apple Inc.\"",
                "example": "Adds AAPL to your watchlist with optional notes"
            },
            {
                "name": "/watchlist remove",
                "description": "Remove a symbol from your watchlist",
                "usage": "/watchlist action:remove symbol:AAPL",
                "example": "Removes AAPL from your watchlist"
            },
            {
                "name": "/watchlist live",
                "description": "View real-time updates for your watchlist",
                "usage": "/watchlist action:live",
                "example": "Shows watchlist with real-time price updates"
            }
        ]
    },
    "alerts": {
        "name": "Alert Commands",
        "description": "Commands for setting up and managing alerts",
        "emoji": "🔔",
        "commands": [
            {
                "name": "/alerts view",
                "description": "View your active alerts",
                "usage": "/alerts action:view",
                "example": "Shows all your active price and technical alerts"
            },
            {
                "name": "/alerts create",
                "description": "Create a new alert",
                "usage": "/alerts action:create symbol:AAPL type:price target:150 direction:above",
                "example": "Creates an alert for when AAPL goes above $150"
            },
            {
                "name": "/alerts delete",
                "description": "Delete an alert",
                "usage": "/alerts action:delete alert_id:12345",
                "example": "Deletes the alert with ID 12345"
            }
        ]
    },
    "portfolio": {
        "name": "Portfolio Commands",
        "description": "Commands for managing your portfolio",
        "emoji": "💼",
        "commands": [
            {
                "name": "/portfolio view",
                "description": "View your portfolio",
                "usage": "/portfolio action:view",
                "example": "Shows your portfolio with current values and performance"
            },
            {
                "name": "/portfolio add",
                "description": "Add a position to your portfolio",
                "usage": "/portfolio action:add symbol:AAPL shares:10 price:150",
                "example": "Adds 10 shares of AAPL at $150 to your portfolio"
            },
            {
                "name": "/portfolio remove",
                "description": "Remove a position from your portfolio",
                "usage": "/portfolio action:remove position_id:12345",
                "example": "Removes the position with ID 12345 from your portfolio"
            }
        ]
    },
    "utility": {
        "name": "Utility Commands",
        "description": "Utility and help commands",
        "emoji": "🔧",
        "commands": [
            {
                "name": "/help",
                "description": "Show interactive help and tutorials",
                "usage": "/help category:analysis",
                "example": "Shows help for analysis commands with interactive UI"
            },
            {
                "name": "/feedback",
                "description": "Provide feedback on bot responses",
                "usage": "/feedback rating:positive comment:\"Great analysis!\"",
                "example": "Sends feedback to improve the bot's responses"
            },
            {
                "name": "/status",
                "description": "Check bot status and system health",
                "usage": "/status",
                "example": "Shows the current status of all bot components"
            }
        ]
    }
}

class HelpView(discord.ui.View):
    """Interactive help view with buttons and select menus"""
    
    def __init__(self, author_id: int, timeout: int = 300):
        super().__init__(timeout=timeout)
        self.author_id = author_id
        self.current_category = "analysis"
        self.current_page = 0
        self.add_category_select()
    
    def add_category_select(self):
        """Add category select menu"""
        select = discord.ui.Select(
            placeholder="Select a command category",
            options=[
                discord.SelectOption(
                    label=data["name"],
                    value=category,
                    emoji=data["emoji"],
                    description=data["description"][:100]  # Discord limits description to 100 chars
                )
                for category, data in COMMAND_CATEGORIES.items()
            ]
        )
        
        select.callback = self.category_select_callback
        self.add_item(select)
    
    async def category_select_callback(self, interaction: discord.Interaction):
        """Handle category selection"""
        # Check if the interaction user is the author
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This help menu is not for you.", ephemeral=True)
            return
        
        # Get selected category
        self.current_category = interaction.data["values"][0]
        self.current_page = 0
        
        # Update the embed
        embed = self.create_category_embed()
        
        # Update the message
        await interaction.response.edit_message(embed=embed, view=self)
    
    @discord.ui.button(label="Previous", style=discord.ButtonStyle.secondary, emoji="⬅️")
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle previous button click"""
        # Check if the interaction user is the author
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This help menu is not for you.", ephemeral=True)
            return
        
        # Get commands for current category
        commands = COMMAND_CATEGORIES[self.current_category]["commands"]
        
        # Update page
        self.current_page = (self.current_page - 1) % len(commands)
        
        # Update the embed
        embed = self.create_command_embed()
        
        # Update the message
        await interaction.response.edit_message(embed=embed, view=self)
    
    @discord.ui.button(label="Overview", style=discord.ButtonStyle.primary, emoji="📋")
    async def overview_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle overview button click"""
        # Check if the interaction user is the author
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This help menu is not for you.", ephemeral=True)
            return
        
        # Update the embed
        embed = self.create_category_embed()
        
        # Update the message
        await interaction.response.edit_message(embed=embed, view=self)
    
    @discord.ui.button(label="Next", style=discord.ButtonStyle.secondary, emoji="➡️")
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle next button click"""
        # Check if the interaction user is the author
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This help menu is not for you.", ephemeral=True)
            return
        
        # Get commands for current category
        commands = COMMAND_CATEGORIES[self.current_category]["commands"]
        
        # Update page
        self.current_page = (self.current_page + 1) % len(commands)
        
        # Update the embed
        embed = self.create_command_embed()
        
        # Update the message
        await interaction.response.edit_message(embed=embed, view=self)
    
    @discord.ui.button(label="Tutorial", style=discord.ButtonStyle.success, emoji="📚")
    async def tutorial_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle tutorial button click"""
        # Check if the interaction user is the author
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This help menu is not for you.", ephemeral=True)
            return
        
        # Get commands for current category
        commands = COMMAND_CATEGORIES[self.current_category]["commands"]
        command = commands[self.current_page]
        
        # Create tutorial embed
        embed = discord.Embed(
            title=f"Tutorial: {command['name']}",
            description=f"Step-by-step guide for using {command['name']}",
            color=discord.Color.green()
        )
        
        # Add tutorial steps
        embed.add_field(
            name="Step 1: Invoke the Command",
            value=f"Type `{command['usage']}` in any channel where the bot is available.",
            inline=False
        )
        
        embed.add_field(
            name="Step 2: Provide Parameters",
            value=f"The command requires specific parameters. Example: `{command['usage']}`",
            inline=False
        )
        
        embed.add_field(
            name="Step 3: Review Results",
            value=f"The bot will process your request and provide results. {command['example']}",
            inline=False
        )
        
        embed.add_field(
            name="Tips",
            value="• Use tab completion to see available parameters\n"
                  "• Required parameters are marked with *\n"
                  "• Optional parameters can be omitted",
            inline=False
        )
        
        # Add footer
        embed.set_footer(text="Click 'Back to Command' to return to the command details")
        
        # Create back button
        back_view = TutorialBackView(self, self.author_id)
        
        # Update the message
        await interaction.response.edit_message(embed=embed, view=back_view)
    
    def create_category_embed(self):
        """Create embed for category overview"""
        category_data = COMMAND_CATEGORIES[self.current_category]
        
        embed = discord.Embed(
            title=f"{category_data['emoji']} {category_data['name']}",
            description=category_data['description'],
            color=discord.Color.blue()
        )
        
        # Add commands
        for command in category_data['commands']:
            embed.add_field(
                name=command['name'],
                value=command['description'],
                inline=True
            )
        
        # Add instructions
        embed.add_field(
            name="Navigation",
            value="• Use the dropdown to select a category\n"
                  "• Click on 'Next' or 'Previous' to navigate through commands\n"
                  "• Click on 'Tutorial' for step-by-step guides",
            inline=False
        )
        
        # Add footer
        embed.set_footer(text="Click on 'Next' to see detailed command information")
        
        return embed
    
    def create_command_embed(self):
        """Create embed for command details"""
        category_data = COMMAND_CATEGORIES[self.current_category]
        command = category_data['commands'][self.current_page]
        
        embed = discord.Embed(
            title=f"{category_data['emoji']} {command['name']}",
            description=command['description'],
            color=discord.Color.blue()
        )
        
        # Add usage
        embed.add_field(
            name="Usage",
            value=f"`{command['usage']}`",
            inline=False
        )
        
        # Add example
        embed.add_field(
            name="Example",
            value=command['example'],
            inline=False
        )
        
        # Add navigation info
        embed.add_field(
            name="Navigation",
            value=f"Command {self.current_page + 1} of {len(category_data['commands'])}",
            inline=False
        )
        
        # Add footer
        embed.set_footer(text="Click on 'Tutorial' for a step-by-step guide")
        
        return embed


class TutorialBackView(discord.ui.View):
    """View with back button for tutorials"""
    
    def __init__(self, help_view: HelpView, author_id: int):
        super().__init__(timeout=300)
        self.help_view = help_view
        self.author_id = author_id
    
    @discord.ui.button(label="Back to Command", style=discord.ButtonStyle.primary)
    async def back_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle back button click"""
        # Check if the interaction user is the author
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This help menu is not for you.", ephemeral=True)
            return
        
        # Update the embed
        embed = self.help_view.create_command_embed()
        
        # Update the message
        await interaction.response.edit_message(embed=embed, view=self.help_view)


class FeedbackView(discord.ui.View):
    """Interactive feedback view with buttons and text input"""
    
    def __init__(self, author_id: int, command: str, timeout: int = 300):
        super().__init__(timeout=timeout)
        self.author_id = author_id
        self.command = command
        self.rating = None
    
    @discord.ui.button(label="👍 Helpful", style=discord.ButtonStyle.success)
    async def helpful_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle helpful button click"""
        # Check if the interaction user is the author
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This feedback form is not for you.", ephemeral=True)
            return
        
        self.rating = "positive"
        
        # Show modal for additional comments
        modal = FeedbackModal(self.command, self.rating)
        await interaction.response.send_modal(modal)
    
    @discord.ui.button(label="👎 Not Helpful", style=discord.ButtonStyle.danger)
    async def not_helpful_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle not helpful button click"""
        # Check if the interaction user is the author
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This feedback form is not for you.", ephemeral=True)
            return
        
        self.rating = "negative"
        
        # Show modal for additional comments
        modal = FeedbackModal(self.command, self.rating)
        await interaction.response.send_modal(modal)
    
    @discord.ui.button(label="🤔 Needs Improvement", style=discord.ButtonStyle.secondary)
    async def needs_improvement_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle needs improvement button click"""
        # Check if the interaction user is the author
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This feedback form is not for you.", ephemeral=True)
            return
        
        self.rating = "neutral"
        
        # Show modal for additional comments
        modal = FeedbackModal(self.command, self.rating)
        await interaction.response.send_modal(modal)


class FeedbackModal(discord.ui.Modal, title="Provide Feedback"):
    """Modal for collecting feedback comments"""
    
    def __init__(self, command: str, rating: str):
        super().__init__()
        self.command = command
        self.rating = rating
    
    comment = discord.ui.TextInput(
        label="Additional Comments (Optional)",
        placeholder="Please share any specific feedback or suggestions...",
        required=False,
        style=discord.TextStyle.paragraph,
        max_length=1000
    )
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle modal submission"""
        # Save feedback to database or metrics collector
        from src.bot.metrics_collector import record_feedback
        
        await record_feedback(
            user_id=str(interaction.user.id),
            command=self.command,
            rating=self.rating,
            comment=self.comment.value,
            timestamp=datetime.now()
        )
        
        # Thank the user
        await interaction.response.send_message(
            "Thank you for your feedback! We'll use it to improve the bot.",
            ephemeral=True
        )


class InteractiveHelpCommands(commands.Cog):
    """Interactive help commands with tutorials and UI components"""
    
    def __init__(self, bot):
        self.bot = bot
    
    @app_commands.command(name="help", description="Show interactive help and tutorials")
    @app_commands.describe(
        category="Command category to show help for"
    )
    @app_commands.choices(
        category=[
            app_commands.Choice(name="Analysis Commands", value="analysis"),
            app_commands.Choice(name="AI-Powered Commands", value="ai"),
            app_commands.Choice(name="Watchlist Commands", value="watchlist"),
            app_commands.Choice(name="Alert Commands", value="alerts"),
            app_commands.Choice(name="Portfolio Commands", value="portfolio"),
            app_commands.Choice(name="Utility Commands", value="utility")
        ]
    )
    async def help_command(
        self, 
        interaction: discord.Interaction, 
        category: Optional[str] = "analysis"
    ):
        """
        Show interactive help and tutorials
        
        Parameters:
        -----------
        category: str, optional
            Command category to show help for
        """
        # Create help view
        view = HelpView(interaction.user.id)
        view.current_category = category
        
        # Create initial embed
        embed = view.create_category_embed()
        
        # Send response
        await interaction.response.send_message(embed=embed, view=view)
    
    @app_commands.command(name="feedback", description="Provide feedback on bot responses")
    @app_commands.describe(
        command="Command you're providing feedback for",
        rating="Your rating of the command",
        comment="Additional comments or suggestions"
    )
    @app_commands.choices(
        command=[
            app_commands.Choice(name="/analyze", value="analyze"),
            app_commands.Choice(name="/ask", value="ask"),
            app_commands.Choice(name="/zones", value="zones"),
            app_commands.Choice(name="/recommendations", value="recommendations"),
            app_commands.Choice(name="/watchlist", value="watchlist"),
            app_commands.Choice(name="/alerts", value="alerts"),
            app_commands.Choice(name="/portfolio", value="portfolio"),
            app_commands.Choice(name="/help", value="help")
        ],
        rating=[
            app_commands.Choice(name="Helpful", value="positive"),
            app_commands.Choice(name="Not Helpful", value="negative"),
            app_commands.Choice(name="Needs Improvement", value="neutral")
        ]
    )
    async def feedback_command(
        self, 
        interaction: discord.Interaction, 
        command: Optional[str] = None,
        rating: Optional[str] = None,
        comment: Optional[str] = None
    ):
        """
        Provide feedback on bot responses
        
        Parameters:
        -----------
        command: str, optional
            Command you're providing feedback for
        rating: str, optional
            Your rating of the command
        comment: str, optional
            Additional comments or suggestions
        """
        # If command and rating are provided, record feedback directly
        if command and rating:
            from src.bot.metrics_collector import record_feedback
            
            await record_feedback(
                user_id=str(interaction.user.id),
                command=command,
                rating=rating,
                comment=comment or "",
                timestamp=datetime.now()
            )
            
            # Thank the user
            await interaction.response.send_message(
                "Thank you for your feedback! We'll use it to improve the bot.",
                ephemeral=True
            )
            return
        
        # Otherwise, show interactive feedback form
        embed = discord.Embed(
            title="Provide Feedback",
            description="Please rate your experience with the bot and provide any additional comments.",
            color=discord.Color.blue()
        )
        
        # Add command selection field
        if not command:
            embed.add_field(
                name="Step 1: Select Command",
                value="Use the dropdown below to select the command you're providing feedback for.",
                inline=False
            )
        else:
            embed.add_field(
                name="Command",
                value=f"/{command}",
                inline=False
            )
        
        # Add rating field
        embed.add_field(
            name="Step 2: Rate Your Experience",
            value="Click one of the buttons below to rate your experience.",
            inline=False
        )
        
        # Create view
        view = FeedbackView(interaction.user.id, command or "general")
        
        # Send response
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)


async def setup(bot):
    """Add the interactive help commands to the bot"""
    await bot.add_cog(InteractiveHelpCommands(bot))
