"""
Enhanced Watchlist Commands

Implements enhanced watchlist commands with real-time updates and improved user experience.
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from src.core.logger import get_logger
from src.bot.permissions import PermissionLevel, require_permission
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.watchlist_manager import WatchlistManager
from src.bot.watchlist_realtime import WatchlistRealtimeManager
from src.bot.watchlist_alerts import AlertType

logger = get_logger(__name__)

class EnhancedWatchlistCommands(commands.Cog):
    """Enhanced watchlist commands with real-time updates"""
    
    def __init__(self, bot):
        self.bot = bot
        self.permission_checker = getattr(bot, 'permission_checker', None)
        self.watchlist_manager = getattr(bot, 'watchlist_manager', None)
        self.watchlist_realtime = getattr(bot, 'watchlist_realtime', None)
    
    @app_commands.command(name="watchlist", description="Manage your stock watchlist with real-time updates")
    @app_commands.describe(
        action="Action to perform (view, add, remove, create)",
        symbol="Stock symbol to add or remove",
        watchlist_name="Name of the watchlist to create or use",
        notes="Optional notes about the symbol",
        alert_threshold="Alert threshold for price changes (e.g., 5 for 5%)",
        alert_type="Type of alert to set"
    )
    @app_commands.choices(
        action=[
            app_commands.Choice(name="view", value="view"),
            app_commands.Choice(name="add", value="add"),
            app_commands.Choice(name="remove", value="remove"),
            app_commands.Choice(name="create", value="create"),
            app_commands.Choice(name="summary", value="summary"),
            app_commands.Choice(name="live", value="live")
        ],
        alert_type=[
            app_commands.Choice(name="Price Change", value="price_change"),
            app_commands.Choice(name="Volume Spike", value="volume_spike"),
            app_commands.Choice(name="Technical Signal", value="technical_signal"),
            app_commands.Choice(name="Support/Resistance", value="support_resistance"),
            app_commands.Choice(name="Volatility", value="volatility")
        ]
    )
    async def watchlist_command(
        self, 
        interaction: discord.Interaction, 
        action: str,
        symbol: Optional[str] = None,
        watchlist_name: Optional[str] = None,
        notes: Optional[str] = None,
        alert_threshold: Optional[float] = None,
        alert_type: Optional[str] = "price_change"
    ):
        """
        Manage your stock watchlist with real-time updates
        
        Parameters:
        -----------
        action: str
            Action to perform (view, add, remove, create, summary, live)
        symbol: str, optional
            Stock symbol to add or remove
        watchlist_name: str, optional
            Name of the watchlist to create or use
        notes: str, optional
            Optional notes about the symbol
        alert_threshold: float, optional
            Alert threshold for price changes (e.g., 5 for 5%)
        alert_type: str, optional
            Type of alert to set
        """
        # Check if permission checker is available
        if self.permission_checker is None:
            await interaction.response.send_message(
                "⚠️ Permission system is not available. Please try again later."
            )
            return
            
        # Check if user has paid access
        has_permission, reason = self.permission_checker.has_permission(
            interaction.user, PermissionLevel.PAID, None, str(interaction.guild_id) if interaction.guild_id else None
        )
        
        if not has_permission:
            await interaction.response.send_message(
                f"❌ This command requires paid tier access: {reason}"
            )
            return
        
        # Check if watchlist manager is available
        if not self.watchlist_manager:
            await interaction.response.send_message(
                "❌ Watchlist system is not available. Please try again later."
            )
            return
        
        # Handle different actions
        if action == "view":
            await self._handle_view_watchlist(interaction)
        elif action == "add":
            await self._handle_add_symbol(interaction, symbol, watchlist_name, notes, alert_threshold, alert_type)
        elif action == "remove":
            await self._handle_remove_symbol(interaction, symbol, watchlist_name)
        elif action == "create":
            await self._handle_create_watchlist(interaction, watchlist_name)
        elif action == "summary":
            await self._handle_watchlist_summary(interaction)
        elif action == "live":
            await self._handle_live_watchlist(interaction)
        else:
            await interaction.response.send_message(
                "❌ Invalid action. Please use view, add, remove, create, summary, or live."
            )
    
    async def _handle_view_watchlist(self, interaction: discord.Interaction):
        """Handle viewing user's watchlist"""
        # Check if watchlist manager is available
        if self.watchlist_manager is None:
            await interaction.response.send_message(
                "⚠️ Watchlist service is not available. Please try again later."
            )
            return
            
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get user's watchlists
            watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
            
            if not watchlists:
                await interaction.followup.send(
                    "You don't have any watchlists yet. Use `/watchlist create` to create one."
                )
                return
            
            # Create embed for each watchlist
            for watchlist in watchlists:
                embed = discord.Embed(
                    title=f"📋 {watchlist.watchlist_name} Watchlist",
                    description=f"Your watchlist with {len(watchlist.symbols)} symbols",
                    color=discord.Color.blue()
                )
                
                # Add symbols to embed
                if watchlist.symbols:
                    # Get real-time data if available
                    if self.watchlist_realtime:
                        for symbol in watchlist.symbols:
                            # Get real-time data
                            market_data = await self.watchlist_realtime.get_real_time_data(symbol.symbol)
                            
                            if market_data:
                                current_price = market_data.get("current_price", "N/A")
                                change_percent = market_data.get("change_percent", "N/A")
                                
                                # Format change percent
                                if isinstance(change_percent, (int, float)):
                                    if change_percent > 0:
                                        change_str = f"🟢 +{change_percent:.2f}%"
                                    elif change_percent < 0:
                                        change_str = f"🔴 {change_percent:.2f}%"
                                    else:
                                        change_str = f"⚪ {change_percent:.2f}%"
                                else:
                                    change_str = "N/A"
                                
                                # Format price
                                if isinstance(current_price, (int, float)):
                                    price_str = f"${current_price:.2f}"
                                else:
                                    price_str = "N/A"
                                
                                # Add field
                                embed.add_field(
                                    name=f"${symbol.symbol}",
                                    value=f"Price: {price_str}\nChange: {change_str}\nNotes: {symbol.notes or 'None'}\nAlert: {symbol.alert_threshold or 'None'} ({symbol.alert_type or 'None'})",
                                    inline=True
                                )
                            else:
                                # No real-time data
                                embed.add_field(
                                    name=f"${symbol.symbol}",
                                    value=f"Price: N/A\nNotes: {symbol.notes or 'None'}\nAlert: {symbol.alert_threshold or 'None'} ({symbol.alert_type or 'None'})",
                                    inline=True
                                )
                    else:
                        # No real-time manager
                        for symbol in watchlist.symbols:
                            embed.add_field(
                                name=f"${symbol.symbol}",
                                value=f"Notes: {symbol.notes or 'None'}\nAlert: {symbol.alert_threshold or 'None'} ({symbol.alert_type or 'None'})",
                                inline=True
                            )
                else:
                    embed.add_field(
                        name="No symbols",
                        value="Use `/watchlist add` to add symbols to this watchlist.",
                        inline=False
                    )
                
                # Add footer with timestamp
                embed.set_footer(text=f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Send embed
                await interaction.followup.send(embed=embed)
        except Exception as e:
            logger.error(f"Error viewing watchlist: {e}")
            await interaction.followup.send(
                "❌ An error occurred while retrieving your watchlist. Please try again later."
            )
    
    async def _handle_add_symbol(
        self, 
        interaction: discord.Interaction,
        symbol: Optional[str],
        watchlist_name: Optional[str],
        notes: Optional[str],
        alert_threshold: Optional[float],
        alert_type: Optional[str]
    ):
        """Handle adding a symbol to watchlist"""
        # Check if watchlist manager is available
        if self.watchlist_manager is None:
            await interaction.response.send_message(
                "⚠️ Watchlist service is not available. Please try again later."
            )
            return
            
        # Check required parameters
        if not symbol:
            await interaction.response.send_message(
                "❌ Missing required parameter. Please provide a symbol."
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get user's watchlists
            watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
            
            if not watchlists:
                # Create default watchlist if none exists
                watchlist_id = await self.watchlist_manager.create_watchlist(user_id, "Default")
                if not watchlist_id:
                    await interaction.followup.send(
                        "❌ Failed to create watchlist. Please try again later."
                    )
                    return
                
                # Get watchlists again
                watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
            
            # Find the watchlist to add to
            target_watchlist = None
            
            if watchlist_name:
                # Find watchlist by name
                for watchlist in watchlists:
                    if watchlist.watchlist_name.lower() == watchlist_name.lower():
                        target_watchlist = watchlist
                        break
            else:
                # Use first watchlist
                target_watchlist = watchlists[0]
            
            if not target_watchlist:
                await interaction.followup.send(
                    f"❌ Watchlist '{watchlist_name}' not found. Use `/watchlist create` to create it."
                )
                return
            
            # Add symbol to watchlist
            success = await self.watchlist_manager.add_symbol_to_watchlist(
                target_watchlist.id, sanitized_symbol, notes, alert_threshold, alert_type
            )
            
            if success:
                # Create success embed
                embed = discord.Embed(
                    title="✅ Symbol Added",
                    description=f"Added ${sanitized_symbol} to {target_watchlist.watchlist_name} watchlist",
                    color=discord.Color.green()
                )
                
                # Add details
                details = []
                if notes:
                    details.append(f"Notes: {notes}")
                if alert_threshold:
                    details.append(f"Alert Threshold: {alert_threshold}%")
                if alert_type:
                    details.append(f"Alert Type: {alert_type}")
                
                if details:
                    embed.add_field(
                        name="Details",
                        value="\n".join(details),
                        inline=False
                    )
                
                # Add real-time data if available
                if self.watchlist_realtime:
                    market_data = await self.watchlist_realtime.get_real_time_data(sanitized_symbol)
                    
                    if market_data:
                        current_price = market_data.get("current_price", "N/A")
                        change_percent = market_data.get("change_percent", "N/A")
                        
                        if isinstance(current_price, (int, float)):
                            embed.add_field(
                                name="Current Price",
                                value=f"${current_price:.2f}",
                                inline=True
                            )
                        
                        if isinstance(change_percent, (int, float)):
                            embed.add_field(
                                name="Change",
                                value=f"{'+' if change_percent > 0 else ''}{change_percent:.2f}%",
                                inline=True
                            )
                
                embed.set_footer(text="Use /watchlist view to see your full watchlist")
                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(
                    "❌ Failed to add symbol to watchlist. Please try again later."
                )
                
        except Exception as e:
            logger.error(f"Error adding symbol to watchlist: {e}")
            await interaction.followup.send(
                "❌ An error occurred while adding the symbol to your watchlist. Please try again later."
            )
    
    async def _handle_remove_symbol(
        self, 
        interaction: discord.Interaction,
        symbol: Optional[str],
        watchlist_name: Optional[str]
    ):
        """Handle removing a symbol from watchlist"""
        # Check if watchlist manager is available
        if self.watchlist_manager is None:
            await interaction.response.send_message(
                "⚠️ Watchlist service is not available. Please try again later."
            )
            return
            
        # Check required parameters
        if not symbol:
            await interaction.response.send_message(
                "❌ Missing required parameter. Please provide a symbol."
            )
            return
        
        # Sanitize symbol
        sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)
        if not is_valid:
            await interaction.response.send_message(f"❌ Invalid symbol: {error_message}")
            return
        
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get user's watchlists
            watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
            
            if not watchlists:
                await interaction.followup.send(
                    "You don't have any watchlists yet. Use `/watchlist create` to create one."
                )
                return
            
            # Find the watchlist to remove from
            target_watchlist = None
            
            if watchlist_name:
                # Find watchlist by name
                for watchlist in watchlists:
                    if watchlist.watchlist_name.lower() == watchlist_name.lower():
                        target_watchlist = watchlist
                        break
            else:
                # Find watchlist containing the symbol
                for watchlist in watchlists:
                    for watchlist_symbol in watchlist.symbols:
                        if watchlist_symbol.symbol.upper() == sanitized_symbol.upper():
                            target_watchlist = watchlist
                            break
                    if target_watchlist:
                        break
            
            if not target_watchlist:
                await interaction.followup.send(
                    f"❌ Symbol {sanitized_symbol} not found in your watchlists."
                )
                return
            
            # Remove symbol from watchlist
            success = await self.watchlist_manager.remove_symbol_from_watchlist(
                target_watchlist.id, sanitized_symbol
            )
            
            if success:
                # Create success embed
                embed = discord.Embed(
                    title="🗑️ Symbol Removed",
                    description=f"Removed ${sanitized_symbol} from {target_watchlist.watchlist_name} watchlist",
                    color=discord.Color.red()
                )
                
                embed.set_footer(text="Use /watchlist view to see your full watchlist")
                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(
                    "❌ Failed to remove symbol from watchlist. Please try again later."
                )
                
        except Exception as e:
            logger.error(f"Error removing symbol from watchlist: {e}")
            await interaction.followup.send(
                "❌ An error occurred while removing the symbol from your watchlist. Please try again later."
            )
    
    async def _handle_create_watchlist(
        self, 
        interaction: discord.Interaction,
        watchlist_name: Optional[str]
    ):
        """Handle creating a new watchlist"""
        # Check if watchlist manager is available
        if self.watchlist_manager is None:
            await interaction.response.send_message(
                "⚠️ Watchlist service is not available. Please try again later."
            )
            return
            
        # Check required parameters
        if not watchlist_name:
            await interaction.response.send_message(
                "❌ Missing required parameter. Please provide a watchlist name."
            )
            return
        
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Create watchlist
            watchlist_id = await self.watchlist_manager.create_watchlist(user_id, watchlist_name)
            
            if watchlist_id:
                # Create success embed
                embed = discord.Embed(
                    title="✅ Watchlist Created",
                    description=f"Created watchlist '{watchlist_name}'",
                    color=discord.Color.green()
                )
                
                embed.add_field(
                    name="Next Steps",
                    value="Use `/watchlist add` to add symbols to your new watchlist.",
                    inline=False
                )
                
                embed.set_footer(text="Use /watchlist view to see your watchlists")
                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(
                    "❌ Failed to create watchlist. Please try again later."
                )
                
        except Exception as e:
            logger.error(f"Error creating watchlist: {e}")
            await interaction.followup.send(
                "❌ An error occurred while creating your watchlist. Please try again later."
            )
    
    async def _handle_watchlist_summary(self, interaction: discord.Interaction):
        """Handle getting watchlist summary"""
        # Check if watchlist manager is available
        if self.watchlist_manager is None:
            await interaction.response.send_message(
                "⚠️ Watchlist service is not available. Please try again later."
            )
            return
            
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Get watchlist summary
            summary = await self.watchlist_manager.get_watchlist_summary(user_id)
            
            if not summary:
                await interaction.followup.send(
                    "You don't have any watchlists yet. Use `/watchlist create` to create one."
                )
                return
            
            # Create summary embed
            embed = discord.Embed(
                title="📊 Watchlist Summary",
                description=f"You have {summary.get('total_watchlists', 0)} watchlists with {summary.get('total_symbols', 0)} symbols",
                color=discord.Color.blue()
            )
            
            # Add watchlists
            watchlists = summary.get('watchlists', [])
            if watchlists:
                watchlist_text = "\n".join([f"• {w.get('name')}: {w.get('symbol_count')} symbols" for w in watchlists])
                embed.add_field(
                    name="Your Watchlists",
                    value=watchlist_text,
                    inline=False
                )
            
            # Add top symbols
            top_symbols = summary.get('top_symbols', [])
            if top_symbols:
                top_symbols_text = "\n".join([f"• ${s.get('symbol')}" for s in top_symbols])
                embed.add_field(
                    name="Top Symbols",
                    value=top_symbols_text,
                    inline=False
                )
            
            # Add real-time data if available
            if self.watchlist_realtime and top_symbols:
                # Get real-time data for top symbols
                for symbol_data in top_symbols:
                    symbol = symbol_data.get('symbol')
                    if symbol:
                        market_data = await self.watchlist_realtime.get_real_time_data(symbol)
                        
                        if market_data:
                            current_price = market_data.get("current_price", "N/A")
                            change_percent = market_data.get("change_percent", "N/A")
                            
                            if isinstance(current_price, (int, float)) and isinstance(change_percent, (int, float)):
                                change_str = f"{'+' if change_percent > 0 else ''}{change_percent:.2f}%"
                                embed.add_field(
                                    name=f"${symbol}",
                                    value=f"${current_price:.2f} ({change_str})",
                                    inline=True
                                )
            
            embed.set_footer(text=f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error getting watchlist summary: {e}")
            await interaction.followup.send(
                "❌ An error occurred while retrieving your watchlist summary. Please try again later."
            )
    
    async def _handle_live_watchlist(self, interaction: discord.Interaction):
        """Handle viewing live watchlist with real-time updates"""
        await interaction.response.defer(thinking=True)
        
        user_id = str(interaction.user.id)
        
        try:
            # Check if real-time manager is available
            if not self.watchlist_realtime:
                await interaction.followup.send(
                    "❌ Real-time updates are not available. Please use `/watchlist view` instead."
                )
                return
            
            # Get watchlists with real-time prices
            watchlists_data = await self.watchlist_realtime.get_user_watchlist_with_prices(user_id)
            
            if not watchlists_data:
                await interaction.followup.send(
                    "You don't have any watchlists yet. Use `/watchlist create` to create one."
                )
                return
            
            # Create embed for each watchlist
            for watchlist in watchlists_data:
                embed = discord.Embed(
                    title=f"🔴 LIVE: {watchlist.get('name')} Watchlist",
                    description=f"Real-time data for {len(watchlist.get('symbols', []))} symbols",
                    color=discord.Color.gold(),
                    timestamp=discord.utils.utcnow()
                )
                
                # Add symbols to embed
                symbols = watchlist.get('symbols', [])
                if symbols:
                    for symbol_data in symbols:
                        symbol = symbol_data.get('symbol')
                        current_price = symbol_data.get('current_price', 'N/A')
                        change_percent = symbol_data.get('change_percent', 'N/A')
                        volume = symbol_data.get('volume', 'N/A')
                        
                        # Format change percent
                        if isinstance(change_percent, (int, float)):
                            if change_percent > 0:
                                change_str = f"🟢 +{change_percent:.2f}%"
                            elif change_percent < 0:
                                change_str = f"🔴 {change_percent:.2f}%"
                            else:
                                change_str = f"⚪ {change_percent:.2f}%"
                        else:
                            change_str = "N/A"
                        
                        # Format price
                        if isinstance(current_price, (int, float)):
                            price_str = f"${current_price:.2f}"
                        else:
                            price_str = "N/A"
                        
                        # Format volume
                        if isinstance(volume, (int, float)):
                            volume_str = f"{volume:,.0f}"
                        else:
                            volume_str = "N/A"
                        
                        # Add field
                        embed.add_field(
                            name=f"${symbol}",
                            value=f"Price: {price_str}\nChange: {change_str}\nVolume: {volume_str}",
                            inline=True
                        )
                else:
                    embed.add_field(
                        name="No symbols",
                        value="Use `/watchlist add` to add symbols to this watchlist.",
                        inline=False
                    )
                
                # Add footer with auto-refresh note
                embed.set_footer(text="Live data - prices update automatically")
                
                # Send embed
                await interaction.followup.send(embed=embed)
        except Exception as e:
            logger.error(f"Error viewing live watchlist: {e}")
            await interaction.followup.send(
                "❌ An error occurred while retrieving your live watchlist. Please try again later."
            )


async def setup(bot):
    """Add the enhanced watchlist commands to the bot"""
    await bot.add_cog(EnhancedWatchlistCommands(bot))
