"""
Enhanced Recommendations Command Module

Implements enhanced trading recommendations with user risk profiles
and personalized investment strategies.
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime
import json

from src.core.logger import get_logger
from src.bot.permissions import PermissionLevel, require_permission
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.pipeline.commands.analyze.parallel_pipeline import execute_parallel_analyze_pipeline
from src.bot.utils.disclaimer_manager import add_disclaimer

logger = get_logger(__name__)

# Define risk profile levels
RISK_PROFILES = {
    "conservative": {
        "description": "Low-risk approach focused on capital preservation with modest returns",
        "max_position_size": 2.0,  # % of portfolio
        "stop_loss": 5.0,  # % from entry
        "take_profit": 10.0,  # % from entry
        "time_horizon": "long-term",
        "preferred_assets": ["blue_chip", "dividend", "value", "etf"]
    },
    "moderate": {
        "description": "Balanced approach with moderate risk for steady growth",
        "max_position_size": 5.0,  # % of portfolio
        "stop_loss": 10.0,  # % from entry
        "take_profit": 20.0,  # % from entry
        "time_horizon": "medium-term",
        "preferred_assets": ["growth", "value", "momentum", "dividend"]
    },
    "aggressive": {
        "description": "Higher-risk approach seeking significant returns with higher volatility",
        "max_position_size": 10.0,  # % of portfolio
        "stop_loss": 15.0,  # % from entry
        "take_profit": 30.0,  # % from entry
        "time_horizon": "short-term",
        "preferred_assets": ["growth", "momentum", "small_cap", "emerging"]
    }
}

class UserRiskProfile:
    """User risk profile for personalized recommendations"""
    
    def __init__(
        self,
        user_id: str,
        risk_level: str = "moderate",
        max_position_size: float = None,
        stop_loss: float = None,
        take_profit: float = None,
        time_horizon: str = None,
        preferred_assets: List[str] = None
    ):
        self.user_id = user_id
        self.risk_level = risk_level
        
        # Use default values based on risk level if not provided
        profile_defaults = RISK_PROFILES.get(risk_level, RISK_PROFILES["moderate"])
        self.max_position_size = max_position_size or profile_defaults["max_position_size"]
        self.stop_loss = stop_loss or profile_defaults["stop_loss"]
        self.take_profit = take_profit or profile_defaults["take_profit"]
        self.time_horizon = time_horizon or profile_defaults["time_horizon"]
        self.preferred_assets = preferred_assets or profile_defaults["preferred_assets"]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return {
            "user_id": self.user_id,
            "risk_level": self.risk_level,
            "max_position_size": self.max_position_size,
            "stop_loss": self.stop_loss,
            "take_profit": self.take_profit,
            "time_horizon": self.time_horizon,
            "preferred_assets": self.preferred_assets
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserRiskProfile':
        """Create from dictionary"""
        return cls(
            user_id=data["user_id"],
            risk_level=data.get("risk_level", "moderate"),
            max_position_size=data.get("max_position_size"),
            stop_loss=data.get("stop_loss"),
            take_profit=data.get("take_profit"),
            time_horizon=data.get("time_horizon"),
            preferred_assets=data.get("preferred_assets")
        )


class UserRiskProfileManager:
    """Manages user risk profiles for personalized recommendations"""
    
    def __init__(self, db_pool=None):
        """Initialize with database pool"""
        self.db_pool = db_pool
        self.profiles_cache: Dict[str, UserRiskProfile] = {}
    
    async def get_user_profile(self, user_id: str) -> UserRiskProfile:
        """Get user risk profile, creating default if none exists"""
        # Check cache first
        if user_id in self.profiles_cache:
            return self.profiles_cache[user_id]
        
        try:
            if self.db_pool:
                async with self.db_pool.acquire() as conn:
                    # Check if profile exists
                    result = await conn.fetchrow("""
                        SELECT data FROM user_risk_profiles
                        WHERE user_id = $1
                    """, user_id)
                    
                    if result:
                        # Parse profile data
                        profile_data = json.loads(result["data"])
                        profile = UserRiskProfile.from_dict(profile_data)
                        
                        # Update cache
                        self.profiles_cache[user_id] = profile
                        return profile
            
            # Create default profile if not found
            default_profile = UserRiskProfile(user_id=user_id)
            
            # Save to database
            await self.save_user_profile(default_profile)
            
            # Update cache
            self.profiles_cache[user_id] = default_profile
            return default_profile
            
        except Exception as e:
            logger.error(f"Error getting user risk profile: {e}")
            # Return default profile on error
            return UserRiskProfile(user_id=user_id)
    
    async def save_user_profile(self, profile: UserRiskProfile) -> bool:
        """Save user risk profile to database"""
        try:
            if self.db_pool:
                async with self.db_pool.acquire() as conn:
                    # Convert profile to JSON
                    profile_data = json.dumps(profile.to_dict())
                    
                    # Upsert profile
                    await conn.execute("""
                        INSERT INTO user_risk_profiles (user_id, data)
                        VALUES ($1, $2)
                        ON CONFLICT (user_id) DO UPDATE
                        SET data = $2, updated_at = NOW()
                    """, profile.user_id, profile_data)
                    
                    # Update cache
                    self.profiles_cache[profile.user_id] = profile
                    return True
            
            return False
                
        except Exception as e:
            logger.error(f"Error saving user risk profile: {e}")
            return False
    
    async def update_user_profile(
        self,
        user_id: str,
        risk_level: Optional[str] = None,
        max_position_size: Optional[float] = None,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        time_horizon: Optional[str] = None,
        preferred_assets: Optional[List[str]] = None
    ) -> bool:
        """Update user risk profile with new values"""
        try:
            # Get current profile
            profile = await self.get_user_profile(user_id)
            
            # Update values if provided
            if risk_level is not None:
                profile.risk_level = risk_level
                
                # Reset to defaults for new risk level if not explicitly provided
                if max_position_size is None and stop_loss is None and take_profit is None:
                    defaults = RISK_PROFILES.get(risk_level, RISK_PROFILES["moderate"])
                    profile.max_position_size = defaults["max_position_size"]
                    profile.stop_loss = defaults["stop_loss"]
                    profile.take_profit = defaults["take_profit"]
                    profile.time_horizon = defaults["time_horizon"]
                    profile.preferred_assets = defaults["preferred_assets"]
            
            # Update individual values if provided
            if max_position_size is not None:
                profile.max_position_size = max_position_size
            if stop_loss is not None:
                profile.stop_loss = stop_loss
            if take_profit is not None:
                profile.take_profit = take_profit
            if time_horizon is not None:
                profile.time_horizon = time_horizon
            if preferred_assets is not None:
                profile.preferred_assets = preferred_assets
            
            # Save updated profile
            return await self.save_user_profile(profile)
                
        except Exception as e:
            logger.error(f"Error updating user risk profile: {e}")
            return False
