"""
Pipeline Visualizer
Provides real-time visualization of pipeline execution for debugging
"""

import discord
import asyncio
import time
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging
from enum import Enum
import traceback

logger = logging.getLogger(__name__)

class StageStatus(Enum):
    """Status of a pipeline stage"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class PipelineVisualizer:
    """
    Visualizes pipeline execution flow for debugging purposes
    Tracks stage execution, data flow, and performance metrics
    """
    
    def __init__(self, interaction: Optional[discord.Interaction] = None, 
                 command_name: str = "", correlation_id: str = "",
                 log_to_console: bool = True, log_to_discord: bool = True):
        self.interaction = interaction
        self.command_name = command_name
        self.correlation_id = correlation_id
        self.log_to_console = log_to_console
        self.log_to_discord = log_to_discord and interaction is not None
        
        self.stages: Dict[str, Dict[str, Any]] = {}
        self.start_time = time.time()
        self.message: Optional[discord.Message] = None
        self.update_task: Optional[asyncio.Task] = None
        self.data_snapshots: Dict[str, Dict[str, Any]] = {}
        self.errors: List[Dict[str, Any]] = []
        self.is_active = False
        self.last_update_time = 0
        self.update_interval = 1.0  # seconds
        
    async def start(self):
        """Start visualization tracking"""
        self.is_active = True
        self.start_time = time.time()
        
        if self.log_to_discord and self.interaction is not None:
            try:
                embed = self._create_initial_embed()
                
                if self.interaction.response.is_done():
                    self.message = await self.interaction.followup.send(
                        embed=embed, 
                        ephemeral=True  # Make debug info private
                    )
                else:
                    await self.interaction.response.send_message(
                        embed=embed,
                        ephemeral=True
                    )
                    self.message = await self.interaction.original_response()
                
                # Start background update task
                self.update_task = asyncio.create_task(self._update_loop())
            except Exception as e:
                logger.error(f"Failed to start pipeline visualization: {e}")
                # Continue without Discord visualization
        
        if self.log_to_console:
            logger.info(f"[PIPELINE-VIZ] Started pipeline visualization for {self.command_name} (ID: {self.correlation_id})")
    
    async def stop(self):
        """Stop visualization tracking"""
        self.is_active = False
        
        # Final update
        await self._update_visualization()
        
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        
        total_time = time.time() - self.start_time
        
        if self.log_to_console:
            logger.info(f"[PIPELINE-VIZ] Completed pipeline visualization for {self.command_name} in {total_time:.2f}s")
            
            # Log summary to console
            completed_stages = sum(1 for s in self.stages.values() if s["status"] == StageStatus.COMPLETED)
            failed_stages = sum(1 for s in self.stages.values() if s["status"] == StageStatus.FAILED)
            
            logger.info(f"[PIPELINE-VIZ] Summary: {completed_stages} stages completed, {failed_stages} failed")
            if self.errors:
                logger.error(f"[PIPELINE-VIZ] {len(self.errors)} errors occurred during pipeline execution")
    
    async def register_stage(self, stage_name: str, description: str = ""):
        """Register a new pipeline stage"""
        self.stages[stage_name] = {
            "name": stage_name,
            "description": description,
            "status": StageStatus.PENDING,
            "start_time": None,
            "end_time": None,
            "duration": None,
            "input_data_size": None,
            "output_data_size": None,
            "order": len(self.stages) + 1
        }
        
        await self._update_visualization()
    
    async def start_stage(self, stage_name: str, input_data: Any = None):
        """Mark a stage as started and capture input data"""
        if stage_name not in self.stages:
            await self.register_stage(stage_name)
        
        self.stages[stage_name]["status"] = StageStatus.RUNNING
        self.stages[stage_name]["start_time"] = time.time()
        
        # Capture input data snapshot
        if input_data is not None:
            try:
                # Try to get size estimation
                if isinstance(input_data, (dict, list)):
                    size = len(json.dumps(input_data))
                elif isinstance(input_data, str):
                    size = len(input_data)
                else:
                    size = str(input_data.__sizeof__())
                
                self.stages[stage_name]["input_data_size"] = size
                
                # Store data snapshot (limited size)
                self.data_snapshots[f"{stage_name}_input"] = self._create_data_snapshot(input_data)
            except Exception as e:
                logger.warning(f"Failed to capture input data for stage {stage_name}: {e}")
        
        await self._update_visualization()
        
        if self.log_to_console:
            logger.info(f"[PIPELINE-VIZ] Started stage: {stage_name}")
    
    async def complete_stage(self, stage_name: str, output_data: Any = None):
        """Mark a stage as completed and capture output data"""
        if stage_name not in self.stages:
            logger.warning(f"Completing unregistered stage: {stage_name}")
            await self.register_stage(stage_name)
            await self.start_stage(stage_name)
        
        self.stages[stage_name]["status"] = StageStatus.COMPLETED
        self.stages[stage_name]["end_time"] = time.time()
        
        if self.stages[stage_name]["start_time"]:
            duration = self.stages[stage_name]["end_time"] - self.stages[stage_name]["start_time"]
            self.stages[stage_name]["duration"] = duration
        
        # Capture output data snapshot
        if output_data is not None:
            try:
                # Try to get size estimation
                if isinstance(output_data, (dict, list)):
                    size = len(json.dumps(output_data))
                elif isinstance(output_data, str):
                    size = len(output_data)
                else:
                    size = str(output_data.__sizeof__())
                
                self.stages[stage_name]["output_data_size"] = size
                
                # Store data snapshot (limited size)
                self.data_snapshots[f"{stage_name}_output"] = self._create_data_snapshot(output_data)
            except Exception as e:
                logger.warning(f"Failed to capture output data for stage {stage_name}: {e}")
        
        await self._update_visualization()
        
        if self.log_to_console:
            duration_str = f"{self.stages[stage_name].get('duration', 0):.2f}s" if self.stages[stage_name].get('duration') else "unknown"
            logger.info(f"[PIPELINE-VIZ] Completed stage: {stage_name} in {duration_str}")
    
    async def fail_stage(self, stage_name: str, error: Exception):
        """Mark a stage as failed and capture error information"""
        if stage_name not in self.stages:
            logger.warning(f"Failing unregistered stage: {stage_name}")
            await self.register_stage(stage_name)
            await self.start_stage(stage_name)
        
        self.stages[stage_name]["status"] = StageStatus.FAILED
        self.stages[stage_name]["end_time"] = time.time()
        
        if self.stages[stage_name]["start_time"]:
            duration = self.stages[stage_name]["end_time"] - self.stages[stage_name]["start_time"]
            self.stages[stage_name]["duration"] = duration
        
        # Capture error information
        error_info = {
            "stage": stage_name,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc(),
            "timestamp": time.time()
        }
        
        self.errors.append(error_info)
        
        await self._update_visualization()
        
        if self.log_to_console:
            logger.error(f"[PIPELINE-VIZ] Stage failed: {stage_name} - {type(error).__name__}: {error}")
    
    async def skip_stage(self, stage_name: str, reason: str = ""):
        """Mark a stage as skipped"""
        if stage_name not in self.stages:
            await self.register_stage(stage_name)
        
        self.stages[stage_name]["status"] = StageStatus.SKIPPED
        self.stages[stage_name]["skip_reason"] = reason
        
        await self._update_visualization()
        
        if self.log_to_console:
            logger.info(f"[PIPELINE-VIZ] Skipped stage: {stage_name} - {reason}")
    
    async def log_data_flow(self, key: str, data: Any):
        """Log arbitrary data flow between stages"""
        try:
            self.data_snapshots[key] = self._create_data_snapshot(data)
            
            if self.log_to_console:
                logger.debug(f"[PIPELINE-VIZ] Data flow: {key}")
                
            await self._update_visualization()
        except Exception as e:
            logger.warning(f"Failed to log data flow for {key}: {e}")
    
    def _create_data_snapshot(self, data: Any) -> Dict[str, Any]:
        """Create a limited snapshot of data for visualization"""
        try:
            snapshot = {
                "timestamp": time.time(),
                "type": type(data).__name__
            }
            
            # Handle different data types
            if isinstance(data, dict):
                # Limit size for visualization
                if len(data) > 10:
                    keys = list(data.keys())[:10]
                    snapshot["data"] = {k: data[k] for k in keys}
                    snapshot["truncated"] = True
                    snapshot["total_keys"] = len(data)
                else:
                    snapshot["data"] = data
                    snapshot["truncated"] = False
            elif isinstance(data, list):
                # Limit size for visualization
                if len(data) > 10:
                    snapshot["data"] = data[:10]
                    snapshot["truncated"] = True
                    snapshot["total_items"] = len(data)
                else:
                    snapshot["data"] = data
                    snapshot["truncated"] = False
            elif isinstance(data, str):
                # Limit string length
                if len(data) > 500:
                    snapshot["data"] = data[:500] + "..."
                    snapshot["truncated"] = True
                    snapshot["total_length"] = len(data)
                else:
                    snapshot["data"] = data
                    snapshot["truncated"] = False
            else:
                # For other types, use string representation
                snapshot["data"] = str(data)
                
            return snapshot
        except Exception as e:
            logger.warning(f"Failed to create data snapshot: {e}")
            return {
                "timestamp": time.time(),
                "type": type(data).__name__,
                "error": f"Failed to capture: {e}"
            }
    
    async def _update_loop(self):
        """Background task to update visualization periodically"""
        try:
            while self.is_active:
                current_time = time.time()
                
                # Only update at specified interval
                if current_time - self.last_update_time >= self.update_interval:
                    await self._update_visualization()
                    self.last_update_time = current_time
                
                await asyncio.sleep(0.5)
        except asyncio.CancelledError:
            # Task was cancelled, clean up
            pass
        except Exception as e:
            logger.error(f"Error in pipeline visualization update loop: {e}")
    
    async def _update_visualization(self):
        """Update the visualization in Discord"""
        if not self.log_to_discord or not self.message:
            return
        
        try:
            embed = self._create_visualization_embed()
            await self.message.edit(embed=embed)
        except discord.NotFound:
            # Message was deleted
            self.message = None
        except Exception as e:
            logger.error(f"Failed to update pipeline visualization: {e}")
    
    def _create_initial_embed(self) -> discord.Embed:
        """Create initial visualization embed"""
        embed = discord.Embed(
            title=f"🔍 Pipeline Debug: {self.command_name}",
            description=f"Visualizing pipeline execution flow\nID: `{self.correlation_id}`",
            color=discord.Color.blue(),
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(
            name="Status",
            value="Initializing...",
            inline=False
        )
        
        embed.set_footer(text="Pipeline visualization for debugging")
        
        return embed
    
    def _create_visualization_embed(self) -> discord.Embed:
        """Create detailed visualization embed"""
        # Determine overall status
        if any(s["status"] == StageStatus.FAILED for s in self.stages.values()):
            status = "Failed"
            color = discord.Color.red()
        elif all(s["status"] in (StageStatus.COMPLETED, StageStatus.SKIPPED) for s in self.stages.values()):
            status = "Completed"
            color = discord.Color.green()
        elif any(s["status"] == StageStatus.RUNNING for s in self.stages.values()):
            status = "Running"
            color = discord.Color.blue()
        else:
            status = "Pending"
            color = discord.Color.light_grey()
        
        # Calculate elapsed time
        elapsed = time.time() - self.start_time
        
        embed = discord.Embed(
            title=f"🔍 Pipeline Debug: {self.command_name}",
            description=f"Status: **{status}**\nElapsed: **{elapsed:.2f}s**\nID: `{self.correlation_id}`",
            color=color,
            timestamp=datetime.utcnow()
        )
        
        # Add stage information
        stages_text = []
        for stage in sorted(self.stages.values(), key=lambda s: s["order"]):
            status_emoji = {
                StageStatus.PENDING: "⏳",
                StageStatus.RUNNING: "▶️",
                StageStatus.COMPLETED: "✅",
                StageStatus.FAILED: "❌",
                StageStatus.SKIPPED: "⏭️"
            }.get(stage["status"], "⏺️")
            
            duration = ""
            if stage["duration"] is not None:
                duration = f" ({stage['duration']:.2f}s)"
            
            stages_text.append(f"{status_emoji} **{stage['name']}**{duration}")
            
            # Add data flow indicators if available
            input_key = f"{stage['name']}_input"
            output_key = f"{stage['name']}_output"
            
            if input_key in self.data_snapshots:
                input_data = self.data_snapshots[input_key]
                input_type = input_data.get("type", "unknown")
                input_size = stage.get("input_data_size", "unknown")
                stages_text.append(f"  ↳ Input: {input_type} ({input_size} bytes)")
            
            if output_key in self.data_snapshots:
                output_data = self.data_snapshots[output_key]
                output_type = output_data.get("type", "unknown")
                output_size = stage.get("output_data_size", "unknown")
                stages_text.append(f"  ↳ Output: {output_type} ({output_size} bytes)")
        
        if stages_text:
            embed.add_field(
                name="Pipeline Stages",
                value="\n".join(stages_text),
                inline=False
            )
        
        # Add errors if any
        if self.errors:
            error_text = []
            for i, error in enumerate(self.errors[:3]):  # Limit to 3 errors
                error_text.append(f"{i+1}. **{error['stage']}**: {error['error_type']} - {error['error_message']}")
            
            if len(self.errors) > 3:
                error_text.append(f"... and {len(self.errors) - 3} more errors")
            
            embed.add_field(
                name=f"Errors ({len(self.errors)})",
                value="\n".join(error_text),
                inline=False
            )
        
        embed.set_footer(text="Pipeline visualization for debugging")
        
        return embed

def create_visualizer(interaction: Optional[discord.Interaction] = None, 
                     command_name: str = "", correlation_id: str = "",
                     log_to_console: bool = True, log_to_discord: bool = True) -> PipelineVisualizer:
    """Factory function to create a pipeline visualizer"""
    return PipelineVisualizer(
        interaction=interaction,
        command_name=command_name,
        correlation_id=correlation_id,
        log_to_console=log_to_console,
        log_to_discord=log_to_discord
    )
