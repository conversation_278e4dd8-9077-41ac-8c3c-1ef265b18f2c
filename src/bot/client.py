"""
Discord Bot Client
Enhanced trading bot with modular pipeline system
"""

from discord.app_commands import AppCommandError

import discord
import asyncio
import logging
import time
import os
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from discord.ext import commands
from datetime import datetime, timedelta

class BotRateLimiter:
    """Simple rate limiter for user requests"""

    def __init__(self, max_requests: int = 50, time_window: int = 3600):
        self.max_requests = max_requests
        self.time_window = time_window  # in seconds
        self.user_requests = {}

    def can_make_request(self, user_id: str) -> bool:
        """Check if user can make a request"""
        current_time = datetime.now()

        if user_id not in self.user_requests:
            return True

        # Remove old requests outside the time window
        self.user_requests[user_id] = [
            t for t in self.user_requests[user_id]
            if (current_time - t).seconds < self.time_window
        ]

        return len(self.user_requests[user_id]) < self.max_requests

    def record_user_query(self, user_id: str):
        """Record a user's query"""
        if user_id not in self.user_requests:
            self.user_requests[user_id] = []
        self.user_requests[user_id].append(datetime.now())

    def get_remaining_time(self, user_id: str) -> float:
        """Get remaining time in hours until user can make another request"""
        if user_id not in self.user_requests or not self.user_requests[user_id]:
            return 0.0

        # Get the oldest request
        oldest_request = min(self.user_requests[user_id])
        time_elapsed = (datetime.now() - oldest_request).seconds

        if time_elapsed >= self.time_window:
            return 0.0

        return (self.time_window - time_elapsed) / 3600.0  # Convert to hours

# Import pipeline components
from .pipeline.commands.ask.pipeline import execute_ask_pipeline
from .pipeline.commands.ask.batch_processor import execute_batch_ask_pipeline, extract_symbols_from_query
from .pipeline.commands.ask.stages.response_audit import response_auditor
from .pipeline.commands.ask.stages.ask_sections import AskPipelineSections


from .commands.analyze_async import setup as setup_async_analyze
from .commands.alerts import setup as setup_alerts_command
from .commands.portfolio import setup as setup_portfolio_command
from .commands.batch_analyze import setup as setup_batch_analyze_command
from .commands.watchlist_enhanced import setup as setup_enhanced_watchlist_command
from .commands.zones_enhanced import setup as setup_enhanced_zones_command
from .commands.recommendations_command import setup as setup_enhanced_recommendations_command
from .commands.help_interactive import setup as setup_interactive_help_command
from .permissions import DiscordPermissionChecker, PermissionLevel, require_permission
from .watchlist_manager import WatchlistManager
from .watchlist_alerts import initialize_watchlist_alerts
from .database_manager import db_manager
from .utils.component_checker import ComponentChecker
from .utils.input_sanitizer import InputSanitizer, sanitize_symbol, sanitize_query, is_valid_symbol
from .utils.disclaimer_manager import DisclaimerManager, add_disclaimer
from .rate_limiter import EnhancedRateLimiter, RateLimitTier, rate_limiter
from .watchlist_realtime import initialize_watchlist_realtime
import asyncio
import json
from datetime import datetime
from src.core.logger import get_logger, generate_correlation_id, get_trading_logger
from src.core.formatting.response_templates import ResponseGenerator

# Import AI query analysis for quick price detection
from .pipeline.commands.ask.stages.query_analyzer import AIQueryAnalyzer, QueryIntent, ProcessingRoute
from .pipeline.commands.ask.stages.symbol_validator import SymbolValidator

# AI service (LLM) factory
from src.shared.ai_services.ai_chat_processor import create_processor as create_ai_service

# Configure logging for the bot
logger = get_logger(__name__)

class TradingBot:
    """Enhanced trading bot with modular pipeline system"""

    def __init__(self, token: Optional[str] = None, bot_owner_id: Optional[int] = None):
        """Initialize the trading bot with necessary intents"""
        # Define minimal required intents
        intents = discord.Intents.default()
        intents.messages = True  # For receiving message events
        intents.message_content = True  # For message content access
        intents.guilds = True  # For server information

        # Initialize the bot first
        self.bot = commands.Bot(command_prefix='!', intents=intents)

        # Store token for later use
        self.token = token
        self.bot_owner_id = bot_owner_id

        # Use role IDs instead of names for better security
        # These should be configured from environment variables in production
        self.paid_role_ids = [
            int(id_str) for id_str in os.getenv('PAID_ROLE_IDS', '').split(',') if id_str
        ] or [1408844110642544836]  # Default test role ID

        self.admin_role_ids = [
            int(id_str) for id_str in os.getenv('ADMIN_ROLE_IDS', '').split(',') if id_str
        ] or [1304548446090301448]  # Default test role ID

        # Fallback to role names only if no IDs are configured
        self.paid_role_names = ["paid", "premium", "vip", "pro"]
        self.admin_role_names = ["admin", "moderator", "mod"]

        # Track permission checks for security auditing
        self.permission_checks = {}
        self.last_audit_time = time.time()

        # Initialize interaction logger
        self.interaction_logger = logging.getLogger('discord.interaction')

        # Initialize permission checker
        self.permission_checker = DiscordPermissionChecker(bot_owner_id=bot_owner_id)

        # Use enhanced rate limiter
        self.rate_limiter = rate_limiter
        self.watchlist_manager = None  # Will be initialized when database connection is available
        self.watchlist_alert_manager = None  # Will be initialized when database connection is available
        self.watchlist_realtime = None  # Will be initialized when database connection is available
        self.response_generator = ResponseGenerator()
        self.rate_limit_cache = {}
        self.last_command_time = {}
        self.component_status = {}
        self.initialization_complete = False
        # Initialize AI service used by /ask (required component)
        try:
            self.ai_service = create_ai_service(context=self)
            self.component_status['ai_service'] = True
            logger.info("✅ AI service initialized for /ask")
        except Exception as e:
            self.ai_service = None
            self.component_status['ai_service'] = False
            logger.warning(f"⚠️ AI service not initialized: {e}")


        # Initialize AI query analyzer for quick price detection
        self.symbol_validator = SymbolValidator()
        self.query_analyzer = AIQueryAnalyzer()
        self.query_analyzer.symbol_validator = self.symbol_validator

        # Setup event listeners
        @self.bot.event
        async def on_ready():
            """Called when the bot is ready to start receiving events"""
            try:
                # Log basic bot information
                if self.bot.user is not None:
                    logger.info(f'🤖 Logged in as {self.bot.user} (ID: {self.bot.user.id})')
                    logger.info(f'🔗 Bot URL: https://discord.com/oauth2/authorize?client_id={self.bot.user.id}&scope=bot%20applications.commands')
                else:
                    logger.warning('⚠️ Bot is ready but user information is not available yet')

                logger.info('------')

                # Setup commands asynchronously after ready
                try:
                    await self.async_setup_commands()
                    logger.info("✅ All commands setup completed")
                except Exception as e:
                    logger.error(f"❌ Failed to setup commands: {e}", exc_info=True)
                    # Continue running even if command setup fails

                # Initialize database connection with better error handling
                try:
                    logger.info("🔄 Initializing database connection...")
                    db_success = await db_manager.initialize()
                    if db_success:
                        logger.info("✅ Database connection established")
                        self.component_status['database'] = True

                        # Initialize watchlist manager with database connection
                        try:
                            self.watchlist_manager = WatchlistManager(db_manager.get_pool())
                            self.component_status['watchlist_manager'] = True
                            logger.info("✅ Watchlist manager initialized")
                        except Exception as e:
                            logger.error(f"❌ Watchlist manager initialization error: {e}", exc_info=True)
                            self.component_status['watchlist_manager'] = False

                        # Initialize watchlist alert manager only if watchlist_manager is available
                        if self.watchlist_manager is not None:
                            try:
                                self.watchlist_alert_manager = await initialize_watchlist_alerts(self.watchlist_manager)
                                self.component_status['watchlist_alert_manager'] = True
                                logger.info("✅ Watchlist alert manager initialized")
                            except Exception as e:
                                logger.error(f"❌ Watchlist alert manager initialization error: {e}", exc_info=True)
                                self.component_status['watchlist_alert_manager'] = False
                        else:
                            logger.error("❌ Cannot initialize watchlist alert manager: watchlist_manager is None")
                            self.component_status['watchlist_alert_manager'] = False

                        # Initialize watchlist real-time manager only if watchlist_manager is available
                        if self.watchlist_manager is not None:
                            try:
                                self.watchlist_realtime = await initialize_watchlist_realtime(self.watchlist_manager, self.bot)
                                self.component_status['watchlist_realtime'] = True
                                logger.info("✅ Watchlist real-time manager initialized")
                            except Exception as e:
                                logger.error(f"❌ Watchlist real-time manager initialization error: {e}", exc_info=True)
                                self.component_status['watchlist_realtime'] = False
                        else:
                            logger.error("❌ Cannot initialize watchlist real-time manager: watchlist_manager is None")
                            self.component_status['watchlist_realtime'] = False
                    else:
                        logger.error("❌ Failed to initialize database connection")
                        self.component_status['database'] = False
                except ImportError as e:
                    logger.warning(f"Database module not available (likely asyncpg not installed): {e}")
                    logger.info("Continuing without database functionality")
                    self.component_status['database'] = False
                except Exception as e:
                    logger.error(f"❌ Database initialization error: {e}", exc_info=True)
                    self.component_status['database'] = False

                # Sync slash commands with Discord with better error handling
                try:
                    logger.info("🔄 Syncing slash commands with Discord...")
                    await self.bot.tree.sync()
                    logger.info("✅ Slash commands synced successfully!")
                    self.component_status['commands_synced'] = True
                except Exception as e:
                    logger.error(f"❌ Failed to sync slash commands: {e}", exc_info=True)
                    self.component_status['commands_synced'] = False

                # Mark initialization as complete
                self.initialization_complete = True

                # Log available commands with better formatting
                try:
                    commands = [cmd.name for cmd in self.bot.tree.get_commands()]
                    logger.info(f"📋 Available slash commands ({len(commands)}): {', '.join(commands)}")

                    # Log server information
                    guild_count = len(self.bot.guilds)
                    user_count = sum(guild.member_count for guild in self.bot.guilds if guild.member_count is not None)
                    logger.info(f"🌐 Connected to {guild_count} servers and approximately {user_count} users")
                except Exception as e:
                    logger.error(f"❌ Error logging command information: {e}", exc_info=True)

                logger.info("🎉 Bot initialization completed successfully!")

            except Exception as e:
                logger.error(f"❌ Critical error in on_ready: {e}", exc_info=True)
                # Continue running even if initialization fails

            # Setup event listeners
            @self.bot.event
            async def on_command_error(ctx, error):
                if isinstance(error, commands.CommandNotFound):
                    return
                logger.error(f'Error in {ctx.command}: {error}')

        logger.info("TradingBot instance created")

        # Diagnostic: log registered slash commands after setup (useful to confirm /analyze was registered)
        try:
            registered_commands = [c.name for c in self.bot.tree.walk_commands()]
            logger.info(f"Registered slash commands during init: {registered_commands}")
        except Exception as e:
            logger.warning(f"Failed to list registered slash commands during init: {e}")

    async def run_in_degraded_mode(self):
        """Run the bot in degraded mode without Discord connection"""
        logger.info("🚨 Running in DEGRADED MODE - Discord connection unavailable")
        logger.info("✅ Pipeline services are still available for testing and development")
        logger.info("✅ AI analysis, market data, and technical indicators are functional")
        logger.info("✅ Use the API endpoints for testing instead of Discord commands")

        # Keep the process running for development/testing
        try:
            while True:
                await asyncio.sleep(60)  # Sleep for 1 minute
                logger.info("🔄 Degraded mode: Pipeline services are running...")
        except KeyboardInterrupt:
            logger.info("🛑 Degraded mode shutdown requested")
        except Exception as e:
            logger.error(f"❌ Error in degraded mode: {e}")
            raise

    async def _check_required_components(self, interaction: discord.Interaction, required_components: List[str]) -> bool:
        """Check if required components are initialized"""
        # If initialization is not complete, inform the user
        if not self.initialization_complete:
            await interaction.response.send_message(
                "⏳ Bot is still initializing. Please try again in a few moments."
            )
            return False

        # Use the component checker to verify all required components
        return await ComponentChecker.check_components(self, interaction, required_components)

    async def async_setup_commands(self):
        """Asynchronously setup all bot commands"""
        # Setup the analyze command (this registers /analyze)
        # Use async analyze command instead of the original one for better performance
        # setup_analyze_command(self.bot)  # Original analyze command (commented out)
        await setup_async_analyze(self.bot)  # Enhanced async analyze command

        # Setup the alerts command (this registers /alerts)
        await setup_alerts_command(self.bot)

        # Setup the portfolio command (this registers /portfolio)
        await setup_portfolio_command(self.bot)

        # Setup the batch_analyze command (this registers /batch_analyze)
        await setup_batch_analyze_command(self.bot)

        # Setup the enhanced watchlist command (this registers /watchlist)
        await setup_enhanced_watchlist_command(self.bot)

        # Setup the enhanced zones command (this registers /zones and /multizones)
        await setup_enhanced_zones_command(self.bot)

        # Setup the enhanced recommendations command (this registers /recommendations and /riskprofile)
        await setup_enhanced_recommendations_command(self.bot)

        # Setup the interactive help command (this registers /help and /feedback)
        await setup_interactive_help_command(self.bot)

        # Enhanced slash commands with rich embeds
        @self.bot.tree.command(name="ask", description="Ask the AI about trading and markets")
        @discord.app_commands.describe(
            query="Your question about trading and markets",
            attachment="Voice message attachment (optional)"
        )
        async def ask_command(interaction: discord.Interaction, query: Optional[str] = None, attachment: Optional[discord.Attachment] = None):
            """Ask the AI about trading and markets"""
            try:
                print(f"🚨 ASK COMMAND CALLED: query='{query}', attachment={attachment}")
                await self.handle_ask_command(interaction, query, attachment)
                print(f"✅ ASK COMMAND COMPLETED SUCCESSFULLY")
            except Exception as e:
                print(f"🚨 ERROR IN ASK COMMAND: {e}")
                import traceback
                traceback.print_exc()
                # Re-raise to trigger the error handler
                raise

        # Zones command is now handled by the enhanced zones command module
        # @self.bot.tree.command(name="zones", description="Get support and resistance zones for a stock")
        # async def zones_command(interaction: discord.Interaction, symbol: str):
        #     """Get support and resistance zones analysis"""
        #     await self.handle_zones_command(interaction, symbol)

        # Recommendations command is now handled by the enhanced recommendations command module
        # @self.bot.tree.command(name="recommendations", description="Get AI-powered trading recommendations")
        # async def recommendations_command(interaction: discord.Interaction, symbol: str):
        #     """Get AI-powered trading recommendations"""
        #     await self.handle_recommendations_command(interaction, symbol)

        # Admin commands
        @self.bot.tree.command(name="status", description="Check bot status and system health")
        async def status_command(interaction: discord.Interaction):
            """Check bot status and system health"""
            await self.handle_status_command(interaction)

        # Help command is now handled by the interactive help command module
        # @self.bot.tree.command(name="help", description="Show available commands and usage")
        # async def help_command(interaction: discord.Interaction):
        #     """Show available commands and usage"""
        #     await self.handle_help_command(interaction)

        # Paid tier commands
        @self.bot.tree.command(name="watchlist", description="Manage your personal watchlist")
        async def watchlist_command(interaction: discord.Interaction, action: str = "show", symbol: Optional[str] = None):
            """Manage your personal watchlist"""
            await self.handle_watchlist_command(interaction, action, symbol)

        @self.bot.tree.command(name="ping", description="Check bot latency")
        async def ping_command(interaction: discord.Interaction):
            """Check bot latency"""
            latency = round(self.bot.latency * 1000)
            await interaction.response.send_message(f"🏓 Pong! Latency: {latency}ms")

        @self.bot.tree.command(name="test", description="Test bot functionality")
        async def test_command(interaction: discord.Interaction):
            """Test command to verify bot is working"""
            await interaction.response.send_message("✅ Bot is working! All systems operational.")

        # Register the error handler
        self.bot.tree.error(self.on_app_command_error)

    async def on_app_command_error(self, interaction: discord.Interaction, error: discord.app_commands.AppCommandError):
        """Handle app command errors with full traceback logging"""
        import traceback
        command_name = interaction.command.name if interaction.command else 'unknown command'

        # Log with maximum detail
        logger.error(f"🚨 APP COMMAND ERROR HANDLER CALLED for {command_name}")
        logger.error(f"Error type: {type(error)}")
        logger.error(f"Error message: {error}")
        logger.error(f"Error args: {error.args}")

        # Get full traceback
        tb_str = ''.join(traceback.format_exception(type(error), error, error.__traceback__))
        logger.error(f"Full traceback for {command_name}:\n{tb_str}")

        # Also print to stdout for immediate visibility
        print(f"🚨 ERROR in /{command_name}: {error}")
        print(f"Traceback:\n{tb_str}")

        # Send a user-friendly error message (ephemeral to avoid spam)
        try:
            if interaction.response.is_done():
                await interaction.followup.send(
                    f"❌ An error occurred while processing the `/{command_name}` command. Please try again later.",
                    ephemeral=True
                )
            else:
                await interaction.response.send_message(
                    f"❌ An error occurred while processing the `/{command_name}` command. Please try again later.",
                    ephemeral=True
                )
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")
            print(f"Failed to send error message: {e}")

    async def handle_ask_command(self, interaction: discord.Interaction, query: Optional[str] = None, attachment: Optional[discord.Attachment] = None):
        """Handle the /ask command with AI-powered responses, batch query support, and voice input"""
        # Check if required components are ready
        if not await self._check_required_components(interaction, ['ai_service']):
            return

        # Handle voice input if attachment is provided
        query_text = query
        if attachment is not None:
            # Process voice attachment
            try:
                from .pipeline.commands.ask.stages.voice_processor import voice_processor
                voice_result = await voice_processor.process_voice_attachment(attachment)

                if voice_result['success']:
                    query_text = voice_result['text']
                    # Send a message indicating voice was processed
                    await interaction.response.send_message(
                        f"🎤 Voice message processed: \"{query_text[:100]}{'...' if len(query_text) > 100 else ''}\"",
                        ephemeral=True
                    )
                else:
                    await interaction.response.send_message(
                        f"❌ Failed to process voice message: {voice_result.get('error', 'Unknown error')}",
                        ephemeral=True
                    )
                    return
            except Exception as e:
                self.interaction_logger.error(f"Error processing voice attachment: {e}", exc_info=True)
                await interaction.response.send_message(
                    "❌ Failed to process voice message. Please try again or use text input.",
                    ephemeral=True
                )
                return
        elif query is None:
            # No query or attachment provided
            await interaction.response.send_message(
                "❌ Please provide either a text query or a voice message attachment.",
                ephemeral=True
            )
            return

        # Sanitize the query
        sanitized_query, is_valid, error_message = InputSanitizer.sanitize_query(query_text)
        if not is_valid:
            # If we already sent a response for voice, use followup
            if attachment is not None:
                await interaction.followup.send(f"❌ {error_message}")
            else:
                await interaction.response.send_message(f"❌ {error_message}", ephemeral=True)
            return

        # Check for sensitive information
        if InputSanitizer.contains_sensitive_info(sanitized_query):
            # If we already sent a response for voice, use followup
            if attachment is not None:
                await interaction.followup.send(
                    "❌ Your query appears to contain sensitive information like API keys, passwords, or personal data. "
                    "Please remove this information and try again."
                )
            else:
                await interaction.response.send_message(
                    "❌ Your query appears to contain sensitive information like API keys, passwords, or personal data. "
                    "Please remove this information and try again.",
                    ephemeral=True
                )
            return

        # Check rate limits
        user_id = str(interaction.user.id)
        if not self.rate_limiter.can_make_request(user_id):
            remaining_time = self.rate_limiter.get_remaining_time(user_id)
            # If we already sent a response for voice, use followup
            if attachment is not None:
                await interaction.followup.send(
                    f"⏱️ You've reached the rate limit for AI queries. Please try again in {remaining_time:.1f} hours."
                )
            else:
                await interaction.response.send_message(
                    f"⏱️ You've reached the rate limit for AI queries. Please try again in {remaining_time:.1f} hours.",
                    ephemeral=True
                )
            return

        # Record the query
        self.rate_limiter.record_user_query(user_id)

        # If we haven't responded yet (no voice attachment), defer response
        if attachment is None:
            await interaction.response.defer(thinking=True)

        try:
            # Generate correlation ID for tracking
            correlation_id = generate_correlation_id()

            # Check if this is a batch query (contains multiple stock symbols)
            symbols = extract_symbols_from_query(sanitized_query)
            is_batch_query = len(symbols) > 1

            # Detect language for multi-language support
            from .pipeline.commands.ask.stages.language_detector import detect_query_language
            language_info = detect_query_language(sanitized_query)

            # Log language detection
            if language_info['success']:
                logger.info(f"Detected language: {language_info['language_name']} (confidence: {language_info['confidence']:.2f})")

            if is_batch_query:
                # Execute batch ask pipeline for multiple symbols
                await interaction.followup.send(
                    f"🔍 Processing batch query for {len(symbols)} symbols: {', '.join(symbols)}..."
                )

                context = await execute_batch_ask_pipeline(
                    query=sanitized_query,
                    symbols=symbols,
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id
                )
            else:
                # Execute regular ask pipeline
                context = await execute_ask_pipeline(
                    query=sanitized_query,
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id
                )

            # Get the response from the context
            response = context.processing_results.get('response', '')

            # Add language context to response only if language is detected and not English
            if language_info['success'] and language_info['detected_language'] != 'en':
                if not language_info['is_supported']:
                    # Add note about language support for unsupported languages
                    language_note = f"\n\n*Note: Your query was detected as {language_info['language_name']}, but I can only provide full support in English, Spanish, French, German, Italian, Portuguese, Russian, Chinese, Japanese, and Korean.*"
                    response += language_note
                # For supported non-English languages, we could translate the response
                # but for now we'll just indicate the language was detected
                elif language_info['is_supported']:
                    # Add a subtle note that the query was in another language
                    # This is mostly for logging/awareness, as we respond in English by default
                    logger.info(f"Query detected as {language_info['language_name']} but responding in English")

            # Add disclaimer if needed
            response = add_disclaimer(response, {
                'command': 'batch_ask' if is_batch_query else 'ask',
                'query': sanitized_query,
                'symbols': symbols if is_batch_query else None,
                'language': language_info if language_info['success'] else None
            })

            # Send the response with length enforcement
            from src.shared.utils.discord_helpers import DiscordMessageHelper
            await DiscordMessageHelper.safe_send_message(interaction, response, use_followup=True)

        except asyncio.TimeoutError:
            await interaction.followup.send(
                "⏰ The request took longer than expected. Please try again with a more specific question."
            )
        except Exception as e:
            self.interaction_logger.error(f"Error in ask command: {e}", exc_info=True)
            await interaction.followup.send(
                "❌ I encountered an error while processing your request. Please try again later."
            )

    # The handle_zones_command method has been moved to a more complete implementation below

    # Removed duplicate handle_recommendations_command method to fix redeclaration error

    async def handle_status_command(self, interaction: discord.Interaction):
        """Handle the /status command"""
        try:
            # Check if user has admin access
            if not self.permission_checker.has_permission(interaction.user, PermissionLevel.ADMIN):
                await interaction.response.send_message(
                    "❌ This command requires admin access."
                )
                return

            # Get bot status
            latency = round(self.bot.latency * 1000)
            guild_count = len(self.bot.guilds)
            user_count = sum(guild.member_count for guild in self.bot.guilds if guild.member_count is not None)

            embed = discord.Embed(
                title="🤖 Bot Status",
                color=discord.Color.green(),
                timestamp=discord.utils.utcnow()
            )
            embed.add_field(name="Latency", value=f"{latency}ms", inline=True)
            embed.add_field(name="Servers", value=guild_count, inline=True)
            embed.add_field(name="Users", value=user_count, inline=True)
            embed.add_field(name="Status", value="🟢 Online", inline=True)

            await interaction.response.send_message(embed=embed)

        except Exception as e:
            logger.error(f"Error in status command: {e}", exc_info=True)
            await interaction.response.send_message("❌ An error occurred while checking status.")

    async def handle_help_command(self, interaction: discord.Interaction):
        """Handle the /help command"""
        try:
            user_tier = self.permission_checker.get_user_tier(interaction.user)

            embed = discord.Embed(
                title="📚 Trading Bot Commands",
                description="Available commands based on your access level",
                color=discord.Color.blue()
            )

            # Public commands
            embed.add_field(
                name="📡 Public Commands",
                value="• `/ask` - Ask AI about trading and markets\n• `/help` - Show this help message",
                inline=False
            )

            # Paid tier commands
            if user_tier in ["paid", "admin"]:
                embed.add_field(
                    name="💎 Paid Tier Commands",
                    value="• `/zones` - Get support/resistance zones\n• `/recommendations` - AI trading recommendations\n• `/watchlist` - Manage personal watchlist",
                    inline=False
                )

            # Admin commands
            if user_tier == "admin":
                embed.add_field(
                    name="⚡ Admin Commands",
                    value="• `/status` - Check bot status and health\n• `/ping` - Check bot latency",
                    inline=False
                )

            embed.add_field(
                name="Your Tier",
                value=f"🎯 **{user_tier.title()}**",
                inline=False
            )

            await interaction.response.send_message(embed=embed)

        except Exception as e:
            logger.error(f"Error in help command: {e}", exc_info=True)
            await interaction.response.send_message("❌ An error occurred while showing help.")

    async def handle_watchlist_command(self, interaction: discord.Interaction, action: str = "show", symbol: Optional[str] = None):
        """Handle the /watchlist command"""
        try:
            # Check if user has paid access
            if not self.permission_checker.has_permission(interaction.user, PermissionLevel.PAID):
                await interaction.response.send_message(
                    "❌ This command requires paid tier access. Upgrade to manage your watchlist."
                )
                return

            user_id = str(interaction.user.id)

            # Check if components are properly initialized
            if not await self._check_required_components(interaction, ['watchlist_manager']):
                return

            # Sanitize and validate the action
            sanitized_action, action_valid, action_error = InputSanitizer.sanitize_watchlist_action(action)

            if not action_valid:
                await interaction.response.send_message(
                    f"❌ Invalid action: {action_error}\nPlease use a valid action."
                )
                return

            # If symbol is provided, sanitize and validate it
            sanitized_symbol = None
            if symbol is not None and sanitized_action in ["add", "remove"]:
                sanitized_symbol, symbol_valid, symbol_error = InputSanitizer.sanitize_symbol(symbol)

                if not symbol_valid:
                    await interaction.response.send_message(
                        f"❌ Invalid symbol: {symbol_error}\nPlease try again with a valid stock symbol."
                    )
                    return

            if sanitized_action == "show":
                await interaction.response.defer(thinking=True)

                # Check if watchlist manager is initialized
                if self.watchlist_manager is None:
                    await interaction.followup.send("⚠️ Watchlist service is still initializing. Please try again in a moment.")
                    return

                # Get user's watchlists
                watchlists = await self.watchlist_manager.get_user_watchlists(user_id)

                if not watchlists:
                    # Create default watchlist if none exists
                    watchlist_id = await self.watchlist_manager.create_watchlist(user_id, "Default")
                    if watchlist_id:
                        watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
                    else:
                        await interaction.followup.send("❌ Failed to create default watchlist.")
                        return

                # Create watchlist summary
                summary = await self.watchlist_manager.get_watchlist_summary(user_id)

                embed = discord.Embed(
                    title="📋 Your Watchlists",
                    description=f"Managing {summary.get('total_symbols', 0)} symbols across {summary.get('total_watchlists', 0)} watchlists",
                    color=0x00ff00
                )

                for watchlist in watchlists:
                    symbol_list = ", ".join([s.symbol for s in watchlist.symbols]) if watchlist.symbols else "No symbols"
                    embed.add_field(
                        name=f"📁 {watchlist.watchlist_name}",
                        value=f"Symbols: {symbol_list}\nCreated: {datetime.fromtimestamp(watchlist.created_at).strftime('%Y-%m-%d')}",
                        inline=False
                    )

                if summary.get('top_symbols'):
                    top_symbols = ", ".join([f"{s['symbol']} ({s['count']})" for s in summary['top_symbols']])
                    embed.add_field(
                        name="🔥 Most Watched",
                        value=top_symbols,
                        inline=False
                    )

                embed.set_footer(text="Use /watchlist add SYMBOL to add new symbols")
                await interaction.followup.send(embed=embed)

            elif sanitized_action == "add" and sanitized_symbol:
                await interaction.response.defer(thinking=True)

                # Sanitize symbol
                if symbol is not None:
                    symbol = symbol.upper().strip()

                # Get or create default watchlist
                watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
                if not watchlists:
                    watchlist_id = await self.watchlist_manager.create_watchlist(user_id, "Default")
                else:
                    watchlist_id = watchlists[0].id  # Use first watchlist

                # Add symbol to watchlist
                success = await self.watchlist_manager.add_symbol_to_watchlist(
                    watchlist_id, sanitized_symbol, notes=f"Added via Discord command on {datetime.now().strftime('%Y-%m-%d')}"
                )

                if success:
                    embed = discord.Embed(
                        title="✅ Symbol Added",
                        description=f"**{sanitized_symbol}** has been added to your watchlist",
                        color=0x00ff00
                    )
                    embed.add_field(name="Watchlist", value="Default", inline=True)
                    embed.add_field(name="Added", value=datetime.now().strftime('%Y-%m-%d %H:%M'), inline=True)
                    embed.set_footer(text="You'll receive alerts when this symbol meets your criteria")
                else:
                    embed = discord.Embed(
                        title="❌ Failed to Add Symbol",
                        description=f"Could not add **{sanitized_symbol}** to your watchlist",
                        color=0xff0000
                    )

                await interaction.followup.send(embed=embed)

            elif sanitized_action == "remove" and sanitized_symbol:
                await interaction.response.defer(thinking=True)

                # Sanitize symbol
                if symbol is not None:
                    symbol = symbol.upper().strip()

                # Find watchlist containing this symbol
                watchlists = await self.watchlist_manager.get_user_watchlists(user_id)
                removed = False

                for watchlist in watchlists:
                    if any(s.symbol == sanitized_symbol for s in watchlist.symbols):
                        success = await self.watchlist_manager.remove_symbol_from_watchlist(watchlist.id, sanitized_symbol)
                        if success:
                            removed = True
                            break

                if removed:
                    embed = discord.Embed(
                        title="🗑️ Symbol Removed",
                        description=f"**{sanitized_symbol}** has been removed from your watchlist",
                        color=0xff8800
                    )
                else:
                    embed = discord.Embed(
                        title="❌ Symbol Not Found",
                        description=f"**{sanitized_symbol}** was not found in any of your watchlists",
                        color=0xff0000
                    )

                await interaction.followup.send(embed=embed)

            else:
                embed = discord.Embed(
                    title="📋 Watchlist Commands",
                    description="Manage your personal watchlist of trading symbols",
                    color=0x0099ff
                )
                embed.add_field(
                    name="Available Actions",
                    value="• `/watchlist show` - View your watchlists\n• `/watchlist add SYMBOL` - Add a symbol\n• `/watchlist remove SYMBOL` - Remove a symbol",
                    inline=False
                )
                embed.set_footer(text="Requires paid tier access")
                await interaction.response.send_message(embed=embed)

        except Exception as e:
            logger.error(f"Error in watchlist command: {e}", exc_info=True)
            await interaction.response.send_message("❌ An error occurred while managing your watchlist.")

    async def handle_enhanced_analyze_command(self, interaction: discord.Interaction, symbol: str):
        """Handle enhanced analyze command with rich embeds"""
        await interaction.response.defer(thinking=True)

        try:
            # Sanitize and validate the symbol
            sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)

            if not is_valid:
                await interaction.followup.send(
                    f"❌ Invalid symbol: {error_message}\nPlease try again with a valid stock symbol."
                )
                return

            # Generate correlation ID
            correlation_id = generate_correlation_id()
            logger.set_correlation_id(correlation_id)

            # Execute analysis pipeline
            result = await asyncio.wait_for(
                execute_ask_pipeline(
                    query=f"analyze ${sanitized_symbol} with comprehensive technical analysis",
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id,
                    strict_mode=True  # /analyze is strict
                ),
                timeout=45.0
            )

            if hasattr(result, 'processing_results'):
                results = result.processing_results

                # Create rich embed with actual technical analysis data
                embed = self._create_analysis_embed(symbol, results, interaction.user)

                # Add performance metrics
                embed.add_field(
                    name="📊 Performance Metrics",
                    value=f"• Pipeline: ✅ Success\n• Execution: {getattr(result, 'execution_time', 'N/A')}\n• Quality: {results.get('data_quality', {}).get('status', 'N/A')}",
                    inline=False
                )

                # Add disclaimer footer to embed
                embed.set_footer(text="This analysis is for educational purposes only and does not constitute financial advice.")

                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(f"❌ Analysis failed for {symbol}")

        except Exception as e:
            logger.error(f"Enhanced analyze command failed: {e}", exc_info=True)
            await interaction.followup.send(f"❌ Analysis failed: {str(e)}")

    async def handle_zones_command(self, interaction: discord.Interaction, symbol: str):
        """Handle zones command for support/resistance analysis"""
        await interaction.response.defer(thinking=True)

        try:
            # Sanitize and validate the symbol
            sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)

            if not is_valid:
                await interaction.followup.send(
                    f"❌ Invalid symbol: {error_message}\nPlease try again with a valid stock symbol."
                )
                return

            correlation_id = generate_correlation_id()

            result = await asyncio.wait_for(
                execute_ask_pipeline(
                    query=f"show me the support and resistance zones for ${sanitized_symbol}",
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id,
                    strict_mode=False
                ),
                timeout=45.0
            )

            # Normalize result to a dict if possible
            results = None
            if hasattr(result, 'processing_results'):
                results = result.processing_results
            elif isinstance(result, dict):
                results = result
            elif isinstance(result, str):
                # Raw string from pipeline — add disclaimer and send to user
                response_with_disclaimer = add_disclaimer(result, {
                    'command': 'zones',
                    'symbol': sanitized_symbol
                })
                await interaction.followup.send(f"📡 Zones analysis response for {sanitized_symbol}:\n\n{response_with_disclaimer}")
                return
            else:
                await interaction.followup.send(f"❌ Zones analysis failed for {sanitized_symbol}")
                return

            if not isinstance(results, dict):
                await interaction.followup.send(f"❌ Zones analysis returned unexpected format for {symbol}")
                return

            # Prefer technical_analysis.zones, fall back to top-level zones
            ta = results.get('technical_analysis') if isinstance(results.get('technical_analysis'), dict) else results

            embed = self._create_zones_embed(sanitized_symbol, ta, interaction.user)

            # Add disclaimer footer to embed
            embed.set_footer(text="Support/resistance zones are based on technical analysis and not guaranteed. Not financial advice.")

            await interaction.followup.send(embed=embed)

        except asyncio.TimeoutError:
            await interaction.followup.send(
                "⏰ The zones analysis took longer than expected. Please try again later."
            )
        except Exception as e:
            logger.error(f"Zones command failed: {e}", exc_info=True)
            await interaction.followup.send(f"❌ Zones analysis failed: {str(e)}")

    async def handle_recommendations_command(self, interaction: discord.Interaction, symbol: str):
        """Handle recommendations command for AI-powered insights"""
        await interaction.response.defer(thinking=True)

        try:
            # Sanitize and validate the symbol
            sanitized_symbol, is_valid, error_message = InputSanitizer.sanitize_symbol(symbol)

            if not is_valid:
                await interaction.followup.send(
                    f"❌ Invalid symbol: {error_message}\nPlease try again with a valid stock symbol."
                )
                return

            correlation_id = generate_correlation_id()

            result = await asyncio.wait_for(
                execute_ask_pipeline(
                    query=f"give me trading recommendations for ${sanitized_symbol} with risk management",
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id,
                    strict_mode=False
                ),
                timeout=45.0
            )

            if hasattr(result, 'processing_results'):
                results = result.processing_results
                embed = self._create_recommendations_embed(sanitized_symbol, results, interaction.user)

                # Add strong disclaimer footer to embed for recommendations
                embed.set_footer(text="DISCLAIMER: These are algorithmic suggestions based on technical analysis only, not personalized investment advice. Always conduct your own research before making investment decisions.")

                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(f"❌ Recommendations failed for {sanitized_symbol}")

        except Exception as e:
            logger.error(f"Recommendations command failed: {e}", exc_info=True)
            await interaction.followup.send(f"❌ Recommendations failed: {str(e)}")

    def _create_analysis_embed(self, symbol: str, results: dict, user: discord.User) -> discord.Embed:
        """Create rich embed for analysis results"""
        embed = discord.Embed(
            title=f"📊 Technical Analysis: {symbol}",
            description="Comprehensive market analysis powered by AI",
            color=discord.Color.blue(),
            timestamp=discord.utils.utcnow()
        )

        # Add technical indicators
        if 'technical_analysis' in results:
            ta = results['technical_analysis']
            indicators = ta.get('indicators', {})

            if indicators:
                embed.add_field(
                    name="📈 Key Indicators",
                    value=f"• **Price**: ${indicators.get('current_price', 'N/A'):.2f}\n"
                          f"• **RSI**: {indicators.get('rsi', 'N/A'):.1f}\n"
                          f"• **MACD**: {indicators.get('macd', 'N/A'):.3f}\n"
                          f"• **Volume**: {indicators.get('volume', 'N/A'):,.0f}",
                    inline=True
                )

        # Add data quality
        if 'data_quality' in results:
            dq = results['data_quality']
            quality_color = discord.Color.green() if dq.get('quality_score', 0) >= 80 else discord.Color.orange()
            embed.add_field(
                name="🔍 Data Quality",
                value=f"• **Score**: {dq.get('quality_score', 'N/A')}/100\n"
                      f"• **Completeness**: {dq.get('completeness', 'N/A'):.1f}%\n"
                      f"• **Status**: {dq.get('status', 'N/A').title()}",
                inline=True
            )

        # Add user attribution
        embed.set_footer(text=f"Requested by {user.display_name}", icon_url=user.display_avatar.url)

        return embed

    def _create_zones_embed(self, symbol: str, results: dict, user: discord.User) -> discord.Embed:
        """Create rich embed for zones analysis"""
        embed = discord.Embed(
            title=f"🎯 Support & Resistance Zones: {symbol}",
            description="AI-detected supply and demand zones",
            color=discord.Color.gold(),
            timestamp=discord.utils.utcnow()
        )

        # Extract zones list from either top-level or nested 'technical_analysis'
        zones = []
        try:
            if isinstance(results, dict):
                if 'zones' in results and isinstance(results.get('zones'), list):
                    zones = results.get('zones', [])
                elif 'technical_analysis' in results and isinstance(results.get('technical_analysis'), dict):
                    ta = results.get('technical_analysis')
                    if isinstance(ta.get('zones'), list):
                        if ta:
                            zones = ta.get('zones', [])
        except Exception:
            zones = []

        # Normalize zone type keys and count support/resistance flexibly
        def _zone_type(z: dict) -> str:
            t = None
            if not isinstance(z, dict):
                return ''
            t = z.get('zone_type') or z.get('type') or z.get('method')
            if isinstance(t, str):
                return t.lower()
            return ''

        support_zones = [z for z in zones if _zone_type(z) in ('demand', 'support')]
        resistance_zones = [z for z in zones if _zone_type(z) in ('supply', 'resistance')]

        if zones:
            # Add support zones with price levels
            if support_zones:
                support_text = ""
                for i, zone in enumerate(support_zones[:5]):  # Limit to top 5
                    level = zone.get('level') or zone.get('price')
                    if level:
                        support_text += f"S{i+1}: ${level:.2f}\n"
                if support_text:
                    embed.add_field(
                        name="🟢 Support Zones",
                        value=support_text,
                        inline=True
                    )

            # Add resistance zones with price levels
            if resistance_zones:
                resistance_text = ""
                for i, zone in enumerate(resistance_zones[:5]):  # Limit to top 5
                    level = zone.get('level') or zone.get('price')
                    if level:
                        resistance_text += f"R{i+1}: ${level:.2f}\n"
                if resistance_text:
                    embed.add_field(
                        name="🔴 Resistance Zones",
                        value=resistance_text,
                        inline=True
                    )

            # If we have both support and resistance, show count summary
            if support_zones or resistance_zones:
                summary = f"• Support Levels: {len(support_zones)}\n• Resistance Levels: {len(resistance_zones)}"
                embed.add_field(
                    name="📊 Zone Summary",
                    value=summary,
                    inline=False
                )
        else:
            embed.add_field(
                name="⚠️ No Zones Detected",
                value="Insufficient data for zone analysis",
                inline=False
            )

        # Add user attribution
        embed.set_footer(text=f"Requested by {user.display_name}", icon_url=user.display_avatar.url)
        return embed

    def _create_recommendations_embed(self, symbol: str, results: dict, user: discord.User) -> discord.Embed:
        """Create rich embed for trading recommendations"""
        embed = discord.Embed(
            title=f"🤖 AI Recommendations: {symbol}",
            description="AI-powered trading insights and risk management",
            color=discord.Color.purple(),
            timestamp=discord.utils.utcnow()
        )

        # Add recommendations if available
        if 'trading_signals' in results:
            signals = results['trading_signals']
            if signals:
                embed.add_field(
                    name="📊 Trading Signals",
                    value=f"Generated **{len(signals)}** signals",
                    inline=True
                )
            else:
                embed.add_field(
                    name="📊 Trading Signals",
                    value="No clear signals at this time",
                    inline=True
                )

        # Add key technical levels (zones) if available
        zones_info = self._extract_zones_info(results)
        if zones_info:
            embed.add_field(
                name="🎯 Key Levels",
                value=zones_info,
                inline=False
            )

        # Add risk management
        embed.add_field(
            name="⚠️ Risk Disclaimer",
            value="This is educational content only. Always do your own research and consider consulting a financial advisor.",
            inline=False
        )

        embed.set_footer(text=f"Requested by {user.display_name}", icon_url=user.display_avatar.url)
        return embed

    def _extract_zones_info(self, results: dict) -> str:
        """Extract and format zones information for recommendations"""
        zones_info = ""

        try:
            # Extract zones from technical analysis
            ta_data = results.get('technical_analysis', {})
            if ta_data:
                # Try to get zones from different possible locations
                zones = []
                if 'zones' in ta_data and isinstance(ta_data['zones'], list):
                    zones = ta_data['zones']
                elif 'support_resistance' in ta_data:
                    sr_data = ta_data['support_resistance']
                    if isinstance(sr_data, dict):
                        # Extract support levels
                        if 'support' in sr_data and isinstance(sr_data['support'], list):
                            for level in sr_data['support'][:3]:  # Top 3 support levels
                                zones.append({'level': level, 'type': 'support'})
                        # Extract resistance levels
                        if 'resistance' in sr_data and isinstance(sr_data['resistance'], list):
                            for level in sr_data['resistance'][:3]:  # Top 3 resistance levels
                                zones.append({'level': level, 'type': 'resistance'})

                # Format zones info
                support_levels = [z for z in zones if z.get('type') == 'support']
                resistance_levels = [z for z in zones if z.get('type') == 'resistance']

                if support_levels or resistance_levels:
                    if support_levels:
                        support_text = ", ".join([f"${z['level']:.2f}" for z in support_levels[:3]])
                        zones_info += f"**Support**: {support_text}\n"
                    if resistance_levels:
                        resistance_text = ", ".join([f"${z['level']:.2f}" for z in resistance_levels[:3]])
                        zones_info += f"**Resistance**: {resistance_text}\n"
        except Exception as e:
            # If there's an error extracting zones, just return empty string
            pass

        return zones_info.strip() if zones_info else ""

    async def handle_enhanced_status_command(self, interaction: discord.Interaction):
        """Handle enhanced status command with performance metrics"""
        embed = discord.Embed(
            title="🤖 NHX.ai Bot Status",
            description="Professional trading automation bot",
            color=discord.Color.green(),
            timestamp=discord.utils.utcnow()
        )

        # System status
        embed.add_field(
            name="🟢 System Status",
            value="All systems operational",
            inline=True
        )

        # Performance metrics
        embed.add_field(
            name="⚡ Performance",
            value=f"Latency: {round(self.bot.latency * 1000)}ms\nConnected to {len(self.bot.guilds)} servers",
            inline=True
        )

        # Data quality status
        embed.add_field(
            name="📊 Data Quality",
            value="✅ 100% data validation\n✅ Market calendar integrated\n✅ Free-tier optimized",
            inline=False
        )

        embed.set_footer(text="Production-ready infrastructure", icon_url=self.bot.user.display_avatar.url)
        await interaction.response.send_message(embed=embed)

    async def handle_enhanced_help_command(self, interaction: discord.Interaction):
        """Handle enhanced help command with professional presentation"""
        embed = discord.Embed(
            title="🤖 NHX.ai Trading Bot Help",
            description="Professional AI-powered trading assistant with hedge-fund grade infrastructure",
            color=discord.Color.blue(),
            timestamp=discord.utils.utcnow()
        )

        # Core commands
        embed.add_field(
            name="📊 Analysis Commands",
            value="• `/analyze [ticker]` - Comprehensive technical analysis\n"
                  "• `/zones <symbol>` - Support & resistance zones\n"
                  "• `/recommendations <symbol>` - AI trading insights",
            inline=False
        )

        # Utility commands
        embed.add_field(
            name="🛠️ Utility Commands",
            value="• `/ask <query>` - Freeform AI assistance\n"
                  "• `/watchlist` - Manage your watchlist\n"
                  "• `/status` - Bot performance metrics",
            inline=False
        )

        # Features
        embed.add_field(
            name="🚀 Key Features",
            value="• **100% Data Quality** - No false gaps\n"
                  "• **Market Calendar** - Trading day aware\n"
                  "• **Free-Tier Optimized** - 5 calls/minute\n"
                  "• **AI-Powered** - Technical + sentiment analysis",
            inline=False
        )

        embed.set_footer(text="Built on production-ready infrastructure", icon_url=self.bot.user.display_avatar.url)
        await interaction.followup.send(embed=embed)

    def _sanitize_query(self, query: str) -> str:
        """Sanitize and normalize the user query"""
        # Basic sanitization - implement as needed
        return query.strip()

    async def _is_simple_price_query(self, query: str):
        """Check if the query is a simple price lookup using AI classification"""
        try:
            # Analyze the query with AI
            analysis = await self.query_analyzer.analyze_query(query, context=None)

            # Check if it's a simple price query: stock analysis intent with high confidence and quick response route
            if (analysis.intent == QueryIntent.STOCK_ANALYSIS and
                analysis.confidence > 0.7 and
                analysis.processing_route == ProcessingRoute.QUICK_RESPONSE and
                analysis.symbols):

                # Find the highest confidence symbol
                best_symbol = None
                best_confidence = 0
                for symbol in analysis.symbols:
                    if symbol.confidence > best_confidence:
                        best_confidence = symbol.confidence
                        best_symbol = symbol.text

                if best_confidence > 0.8:
                    return best_symbol
            return None
        except Exception as e:
            logger.warning(f"AI query analysis failed for quick price check: {e}")
            return None

    def _contains_intermediate_language(self, text: str) -> bool:
        """Check if text contains intermediate language that should be filtered out"""
        intermediate_phrases = [
            'as an AI', 'as a language model', 'I am an AI', 'I am a language model',
            'I don\'t have access', 'I do not have access', 'I cannot provide',
            'I don\'t have the ability', 'I do not have the ability',
            'my knowledge is limited', 'my knowledge cutoff', 'my training data',
            'I was trained', 'my training only includes', 'I don\'t have real-time',
            'I don\'t have live', 'I don\'t have current', 'I don\'t have up-to-date'
        ]

        text_lower = text.lower()
        return any(phrase in text_lower for phrase in intermediate_phrases)

    async def _validate_symbol(self, symbol: str) -> bool:
        """Validate if a symbol is valid"""
        # Simple validation for now
        if not symbol or len(symbol) < 1 or len(symbol) > 10:
            return False

        # Check if it's alphanumeric or contains dots (for crypto pairs like BTC.USD)
        return all(c.isalnum() or c == '.' for c in symbol)
        """Check if text contains intermediate language that should not be sent to user."""
        import re
        # Patterns for intermediate language that indicates processing is not complete
        intermediate_patterns = [
            r'let me (?:fetch|get|find|retrieve)',
            r'i\'ll (?:fetch|get|find|retrieve|analyze|check)',
            r'fetching (?:data|price|information)',
            r'getting (?:data|price|information)',
            r'retrieving (?:data|price|information)',
            r'analyzing (?:data|price|information)',
            r'checking (?:data|price|information)',
            r'please wait (?:while|until)',
            r'stand by',
            r'one moment',
            r'loading',
            r'processing'
        ]

        text_lower = text.lower()
        for pattern in intermediate_patterns:
            if re.search(pattern, text_lower):
                return True
        return False

    def add_listener(self, func, event: str):
        """Add event listener to bot"""
        self.bot.add_listener(func, event)

    async def on_ready(self):
        """Called when bot is ready"""
        logger.info(f"🤖 Bot is ready! Logged in as {self.bot.user}")
        logger.info(f"📊 Connected to {len(self.bot.guilds)} guilds")

    async def on_command_error(self, ctx, error):
        """Handle command errors"""
        if isinstance(error, commands.CommandNotFound):
            return

        logger.error(f"Command error: {error}")
        await ctx.send(f"❌ An error occurred: {str(error)}")

    async def start_bot(self, token: str):
        """Start the bot asynchronously"""
        # Debug logging for token (mask sensitive value)
        logger.info(f"Starting bot with provided token (length: {len(token)})")
        logger.info(f"Token type: {type(token)}")
        logger.info(f"Token is None: {token is None}")

        await self.bot.start(token)

    def run(self, token: str):
        """Run the bot with proper event loop handling"""
        try:
            if not self.bot.is_closed():
                return  # Already running

            # Check if we're already in an event loop
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If loop is running, schedule the bot start
                    loop.create_task(self.start_bot(token))
                    return
            except RuntimeError:
                # No event loop, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Run the bot in the event loop
            try:
                loop.run_until_complete(self.start_bot(token))
            finally:
                if not loop.is_closed():
                    loop.close()

        except Exception as e:
            logger.error(f"Error in bot run: {e}", exc_info=True)
            raise

# RateLimiter class has been renamed to BotRateLimiter and moved to the top of the file

def create_bot() -> 'TradingBot':
    """Create and return a new bot instance"""
    # Get token from environment variable
    token = os.getenv('DISCORD_BOT_TOKEN')
    return TradingBot(token=token)


async def run_bot_async():
    """Run the bot asynchronously with proper error handling"""
    bot = create_bot()

    # Debug logging (avoid printing sensitive token)
    logger.info(f"Discord token loaded: {'SET' if bot.token else 'NOT SET'}")
    if bot.token:
        logger.info(f"Token length: {len(bot.token)} characters")

    if not bot.token:
        logger.warning("DISCORD_BOT_TOKEN not provided - running in degraded mode")
        # Run bot in degraded mode without Discord
        await bot.run_in_degraded_mode()
        return

    try:
        logger.info("Attempting to start Discord bot...")
        await bot.start_bot(bot.token)
    except discord.errors.LoginFailure as e:
        logger.warning(f"Discord authentication failed: {e} - running in degraded mode")
        # Run bot in degraded mode when Discord auth fails
        await bot.run_in_degraded_mode()
    except Exception as e:
        logger.error(f"Bot failed to start: {e}", exc_info=True)
        raise
    finally:
        if hasattr(bot, 'bot') and not bot.bot.is_closed():
            await bot.bot.close()

def run_bot():
    """Run the bot with proper error handling"""
    try:
        asyncio.run(run_bot_async())
    except KeyboardInterrupt:
        logger.info("Bot shutdown requested by user")
    except Exception as e:
        logger.error(f"Bot failed to start: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    # Logging is already configured in setup_bot_logging()
    logger.info("Starting Discord bot...")
    run_bot()