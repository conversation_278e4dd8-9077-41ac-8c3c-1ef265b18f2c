"""
AI Service Wrapper - DEPRECATED

This module is deprecated. Import from src.shared.ai_services.ai_service_wrapper instead.

This is a backward compatibility module that imports from the canonical location.
"""

import warnings
import logging
import inspect
from typing import Any, Dict, List

# Import from canonical location
from src.shared.ai_services.ai_service_wrapper import (
    AIChatProcessor,
    logger
)

# Import deprecation monitoring
from src.core.deprecation_monitor import record_deprecation

# Get caller information
caller_frame = inspect.currentframe()
if caller_frame and caller_frame.f_back:
    caller_info = f"{caller_frame.f_back.f_code.co_filename}:{caller_frame.f_back.f_lineno}"
else:
    caller_info = "unknown"

# Record deprecation usage
record_deprecation("src.bot.pipeline.ask.stages.ai_service_wrapper", caller_info)

# Show deprecation warning
warnings.warn(
    "This module is deprecated. Import from src.shared.ai_services.ai_service_wrapper instead.",
    DeprecationWarning,
    stacklevel=2
)

# For backward compatibility, create a wrapper class that inherits from AIChatProcessor
class LegacyAIChatProcessor(AIChatProcessor):
    """Legacy wrapper for backward compatibility"""
    pass

# Create an instance of the processor for backward compatibility
ai_processor = None

# Function to get or create the processor instance
def get_processor(config=None):
    global ai_processor
    if ai_processor is None:
        config = config or {}
        ai_processor = LegacyAIChatProcessor(config)
    return ai_processor
