"""
AI Chat Processor - DEPRECATED

This module is deprecated. Import from src.shared.ai_services.ai_chat_processor instead.

This is a backward compatibility module that imports from the canonical location.
"""

import warnings
import logging
import inspect
import os
import asyncio
import json
from typing import Dict, Any, List, Optional

# Import from canonical location
from src.shared.ai_services.ai_chat_processor import (
    AIChatProcessor as CanonicalAIChatProcessor,
    AIAskResult,
    create_processor,
    process_query,
    SYSTEM_PROMPT,
    FALLBACK_RESPONSES
)

# Import deprecation monitoring
from src.core.deprecation_monitor import record_deprecation

# Get caller information
caller_frame = inspect.currentframe()
if caller_frame and caller_frame.f_back:
    caller_info = f"{caller_frame.f_back.f_code.co_filename}:{caller_frame.f_back.f_lineno}"
else:
    caller_info = "unknown"

# Record deprecation usage
record_deprecation("src.bot.pipeline.ask.stages.ai_chat_processor", caller_info)

# Show deprecation warning
warnings.warn(
    "This module is deprecated. Import from src.shared.ai_services.ai_chat_processor instead.",
    DeprecationWarning,
    stacklevel=2
)

# Define response style enum for backward compatibility
class ResponseStyle:
    DETAILED = "detailed"
    SIMPLE = "simple"

# Define template engine class for backward compatibility
class ResponseTemplateEngine:
    def generate_response(self, template_type, style, data, query_analysis):
        return f"Analysis for {data.get('symbol', 'stock')}: ${data.get('price', 'N/A')}"
    
    def generate_market_overview_response(self, market_data):
        return f"Market overview with {len(market_data.get('indices', []))} indices"

# Define feature flags for backward compatibility
TEMPLATE_ENGINE_AVAILABLE = True
ENHANCED_TECHNICAL_ANALYSIS_AVAILABLE = False
ENHANCED_MARKET_CONTEXT_AVAILABLE = False
ENHANCED_AI_CLIENT_AVAILABLE = False
ENHANCED_RESPONSE_GENERATOR_AVAILABLE = False
ENHANCED_CACHE_AVAILABLE = False
ENHANCED_PREPROCESSING_AVAILABLE = False
PIPELINE_CONFIG_AVAILABLE = False
CACHE_AVAILABLE = False

# Backward compatibility class
class AIChatProcessor(CanonicalAIChatProcessor):
    """Backward compatibility wrapper for AIChatProcessor"""
    
    def __init__(self, context: Optional[Any] = None, data_providers: Optional[List[str]] = None):
        super().__init__(context=context, data_providers=data_providers)
        self.pipeline_id = getattr(context, 'pipeline_id', 'unknown') if context else 'unknown'

# Backward compatibility function
async def processor(context: Any, results: Dict[str, Any]) -> Dict[str, Any]:
    """Async helper used by tests: instantiate AIChatProcessor and run `process`."""
    # Ensure original query exists
    if not getattr(context, 'original_query', None):
        return {'error': True, 'response': 'Please provide a question to analyze.'}

    proc = AIChatProcessor(context=context)
    # Allow tests to patch attributes on AIChatProcessor after instantiation
    return await proc.process(context.original_query)
