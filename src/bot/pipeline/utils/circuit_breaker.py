"""
Circuit Breaker Implementation

Provides circuit breaker pattern for pipeline sections to prevent cascading failures.
Implements state machine: Closed → Open → Half-Open with configurable cooldown.
"""

import time
import logging
from typing import Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing recovery

@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    name: str
    threshold: int = 3           # Failures before opening
    cooldown: int = 60          # Seconds before half-open
    timeout: int = 30           # Timeout for operations
    enable_metrics: bool = True

class CircuitBreaker:
    """
    Circuit Breaker implementation with state machine logic.
    
    States:
    - CLOSED: Normal operation, all requests pass through
    - OPEN: Circuit is open, all requests are rejected
    - HALF_OPEN: Testing recovery, limited requests allowed
    """
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.name = config.name
        
        # State management
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_failure_time: Optional[float] = None
        self._last_success_time: Optional[float] = None
        
        # Metrics
        self._total_requests = 0
        self._total_failures = 0
        self._total_successes = 0
        self._total_rejected = 0
        
        logger.info(f"Circuit breaker '{self.name}' initialized with threshold={config.threshold}, cooldown={config.cooldown}s")
    
    @property
    def state(self) -> CircuitState:
        """Get current circuit state"""
        return self._state
    
    @property
    def is_open(self) -> bool:
        """Check if circuit is open (rejecting requests)"""
        return self._state == CircuitState.OPEN
    
    @property
    def is_half_open(self) -> bool:
        """Check if circuit is half-open (testing recovery)"""
        return self._state == CircuitState.HALF_OPEN
    
    @property
    def is_closed(self) -> bool:
        """Check if circuit is closed (normal operation)"""
        return self._state == CircuitState.CLOSED
    
    def can_execute(self) -> bool:
        """
        Check if request can be executed based on current state.
        
        Returns:
            True if request can proceed, False if circuit is open
        """
        if self._state == CircuitState.CLOSED:
            return True
        
        if self._state == CircuitState.OPEN:
            # Check if cooldown period has passed
            if self._should_attempt_half_open():
                self._transition_to_half_open()
                return True
            return False
        
        if self._state == CircuitState.HALF_OPEN:
            # Allow limited requests in half-open state
            return True
        
        return False
    
    def on_success(self):
        """Handle successful execution"""
        self._total_requests += 1
        self._total_successes += 1
        self._last_success_time = time.time()
        
        if self._state == CircuitState.HALF_OPEN:
            # Success in half-open state, close the circuit
            self._close_circuit()
            logger.info(f"Circuit breaker '{self.name}' closed after successful recovery")
        elif self._state == CircuitState.CLOSED:
            # Reset failure count on success
            self._failure_count = 0
        
        logger.debug(f"Circuit breaker '{self.name}' success (state={self._state.value}, failures={self._failure_count})")
    
    def on_failure(self, error: Optional[str] = None):
        """Handle execution failure"""
        self._total_requests += 1
        self._total_failures += 1
        self._failure_count += 1
        self._last_failure_time = time.time()
        
        if self._state == CircuitState.CLOSED and self._failure_count >= self.config.threshold:
            # Threshold reached, open the circuit
            self._open_circuit()
            logger.warning(f"Circuit breaker '{self.name}' opened after {self._failure_count} failures")
        elif self._state == CircuitState.HALF_OPEN:
            # Failure in half-open state, open the circuit again
            self._open_circuit()
            logger.warning(f"Circuit breaker '{self.name}' reopened after failure in half-open state")
        
        logger.debug(f"Circuit breaker '{self.name}' failure (state={self._state.value}, failures={self._failure_count}, error={error})")
    
    def on_rejection(self):
        """Handle request rejection (circuit open)"""
        self._total_rejected += 1
        logger.debug(f"Circuit breaker '{self.name}' rejected request (state={self._state.value})")
    
    def _should_attempt_half_open(self) -> bool:
        """Check if enough time has passed to attempt half-open state"""
        if not self._last_failure_time:
            return False
        
        elapsed = time.time() - self._last_failure_time
        return elapsed >= self.config.cooldown
    
    def _open_circuit(self):
        """Open the circuit (start rejecting requests)"""
        self._state = CircuitState.OPEN
        logger.info(f"Circuit breaker '{self.name}' opened (failures={self._failure_count})")
    
    def _close_circuit(self):
        """Close the circuit (resume normal operation)"""
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        logger.info(f"Circuit breaker '{self.name}' closed")
    
    def _transition_to_half_open(self):
        """Transition to half-open state (testing recovery)"""
        self._state = CircuitState.HALF_OPEN
        logger.info(f"Circuit breaker '{self.name}' transitioning to half-open state")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get circuit breaker metrics"""
        return {
            "name": self.name,
            "state": self._state.value,
            "failure_count": self._failure_count,
            "total_requests": self._total_requests,
            "total_failures": self._total_failures,
            "total_successes": self._total_successes,
            "total_rejected": self._total_rejected,
            "success_rate": (self._total_successes / max(self._total_requests, 1)) * 100,
            "last_failure_time": self._last_failure_time,
            "last_success_time": self._last_success_time,
            "config": {
                "threshold": self.config.threshold,
                "cooldown": self.config.cooldown,
                "timeout": self.config.timeout
            }
        }
    
    def reset(self):
        """Reset circuit breaker to initial state"""
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_failure_time = None
        self._last_success_time = None
        self._total_requests = 0
        self._total_failures = 0
        self._total_successes = 0
        self._total_rejected = 0
        logger.info(f"Circuit breaker '{self.name}' reset to initial state")
    
    def __str__(self) -> str:
        return f"CircuitBreaker(name='{self.name}', state={self._state.value}, failures={self._failure_count})"
    
    def __repr__(self) -> str:
        return self.__str__() 