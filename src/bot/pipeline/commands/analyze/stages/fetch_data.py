"""Stage: fetch_data

Fetch market data for the requested ticker and attach it to the PipelineContext.

This version mirrors the comprehensive fetch flow used by the /ask pipeline: it prefers
an enhanced MarketDataService if available, and falls back to the project's
DataProviderAggregator. It collects current price, technical indicators and
historical data and stores them under context.processing_results['market_data'].
"""

from typing import Any, Dict
from src.core.logger import get_trading_logger
from ..pipeline import PipelineContext
import asyncio
import logging


async def run(context: PipelineContext) -> PipelineContext:
    """Fetch market data and attach to context.processing_results['market_data'].

    The function attempts to use the centralized MarketDataService (used by /ask).
    If that's not importable, it falls back to DataProviderAggregator. It collects
    comprehensive stock data, technical indicators and historical data where available.
    """
    logger = get_trading_logger("fetch_data_stage")
    ticker = context.ticker
    logger.info(f"[fetch_data] Fetching comprehensive market data for {ticker}")

    try:
        # Prefer centralized MarketDataService if present (same service /ask uses)
        try:
            from src.api.data.market_data_service import MarketDataService as EnhancedMarketDataService
            market_service = EnhancedMarketDataService()
            logger.debug("[fetch_data] Using EnhancedMarketDataService")
        except Exception:
            market_service = None
            logger.debug("[fetch_data] EnhancedMarketDataService not available; will fallback to aggregator")

        market_data: Dict[str, Any] = {}

        if market_service and hasattr(market_service, 'get_comprehensive_stock_data'):
            # Use the single-call comprehensive fetch when available
            try:
                comprehensive = await asyncio.wait_for(
                    market_service.get_comprehensive_stock_data(ticker),
                    timeout=12.0
                )
                logger.info(f"[fetch_data] Retrieved comprehensive data for {ticker} from MarketDataService")
                # Merge returned comprehensive structure into market_data
                if isinstance(comprehensive, dict):
                    market_data.update(comprehensive)
                    market_data.setdefault('provider', 'market_data_service')
                    market_data.setdefault('status', 'success')
            except Exception as exc:
                logger.warning(f"[fetch_data] MarketDataService comprehensive fetch failed for {ticker}: {exc}")

        # If we don't yet have key pieces, fallback to aggregator
        if not market_data or market_data.get('status') != 'success':
            try:
                from src.shared.data_providers.aggregator import DataProviderAggregator
                aggregator = DataProviderAggregator()
                logger.info(f"[fetch_data] Falling back to DataProviderAggregator for {ticker}")

                agg_data = await asyncio.wait_for(aggregator.get_ticker(ticker), timeout=12.0)
                if isinstance(agg_data, dict):
                    market_data.update(agg_data)
                    market_data.setdefault('provider', market_data.get('provider', 'aggregator'))
                    market_data.setdefault('status', market_data.get('status', 'success' if agg_data else 'error'))
                else:
                    logger.warning(f"[fetch_data] Aggregator returned non-dict for {ticker}")
            except Exception as exc:
                logger.error(f"[fetch_data] Aggregator fetch failed for {ticker}: {exc}")
                context.error_log.append({
                    'stage': 'fetch_data',
                    'error_message': str(exc),
                    'error_type': type(exc).__name__,
                })

        # Ensure we have current price fields normalized
        # Normalize common variations used across codebase
        if 'current_price' in market_data and 'price' not in market_data:
            market_data['price'] = market_data['current_price']
        if 'change_percent' in market_data and 'change' not in market_data:
            market_data['change'] = market_data['change_percent']

        # Fetch technical indicators if not included
        try:
            if 'technical_indicators' not in market_data or not market_data.get('technical_indicators'):
                if market_service and hasattr(market_service, 'get_technical_indicators'):
                    ti = await asyncio.wait_for(market_service.get_technical_indicators(ticker), timeout=10.0)
                    market_data['technical_indicators'] = ti
                    logger.info(f"[fetch_data] Fetched technical indicators for {ticker} from MarketDataService")
                else:
                    # Aggregator may expose fetch_technical_indicators or fetch_technical_indicators
                    try:
                        if 'aggregator' in locals():
                            ti = await asyncio.wait_for(aggregator.get_technical_indicators(ticker), timeout=10.0)
                            market_data['technical_indicators'] = ti
                            logger.info(f"[fetch_data] Fetched technical indicators for {ticker} from Aggregator")
                    except Exception:
                        logger.debug(f"[fetch_data] Technical indicators not available from aggregator for {ticker}")
        except Exception as exc:
            logger.warning(f"[fetch_data] Technical indicators fetch failed: {exc}")

        # Fetch historical data if not present
        try:
            if 'historical' not in market_data or not market_data.get('historical'):
                if market_service and hasattr(market_service, 'get_historical_data'):
                    hist = await asyncio.wait_for(market_service.get_historical_data(ticker, days=30), timeout=12.0)
                    market_data['historical'] = hist
                    logger.info(f"[fetch_data] Retrieved historical data for {ticker} from MarketDataService")
                else:
                    if 'aggregator' in locals():
                        hist = await asyncio.wait_for(aggregator.get_history(ticker, period='1mo', interval='1d'), timeout=12.0)
                        market_data['historical'] = hist
                        logger.info(f"[fetch_data] Retrieved historical data for {ticker} from Aggregator")
        except Exception as exc:
            logger.warning(f"[fetch_data] Historical data fetch failed: {exc}")

        # Normalize market cap: accept various key names and compute if possible
        try:
            # Accept variations from different providers
            if 'market_cap' not in market_data or not market_data.get('market_cap'):
                for key in ('market_cap', 'marketCap', 'marketCapUSD', 'marketcap'):
                    if key in market_data and market_data.get(key):
                        market_data['market_cap'] = market_data.get(key)
                        break

            # If still missing or zero, try to compute from shares outstanding and current price
            if (not market_data.get('market_cap') or float(market_data.get('market_cap', 0)) == 0) and market_data.get('shares_outstanding'):
                try:
                    shares = float(market_data.get('shares_outstanding'))
                    price = float(market_data.get('current_price') or market_data.get('price') or 0)
                    if shares > 0 and price > 0:
                        market_data['market_cap'] = round(shares * price, 2)
                        logger.info(f"[fetch_data] Computed market_cap from shares_outstanding for {ticker}")
                except Exception:
                    # ignore compute failures
                    pass
        except Exception as exc:
            logger.debug(f"[fetch_data] Market cap normalization error: {exc}")

        from datetime import datetime
        market_data.setdefault('timestamp', datetime.utcnow().isoformat())

        # Final sanity defaults
        market_data.setdefault('symbol', ticker)
        market_data.setdefault('status', market_data.get('status', 'success'))

        context.processing_results['market_data'] = market_data
        logger.info(f"[fetch_data] Completed comprehensive data collection for {ticker}")

    except Exception as exc:
        logger.error(f"[fetch_data] Unexpected error while fetching data for {ticker}: {exc}")
        context.error_log.append({
            'stage': 'fetch_data',
            'error_message': str(exc),
            'error_type': type(exc).__name__,
        })

    return context 