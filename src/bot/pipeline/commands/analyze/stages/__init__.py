"""Analyze pipeline stages package

This package exposes stage runnables for the analyze pipeline. Stages are intentionally
lightweight and receive a PipelineContext object defined in the parent pipeline.
"""

from typing import List

# Exported stage modules (import lazily by pipeline when needed)
__all__: List[str] = [
    "fetch_data",
    "technical_analysis",
    "price_targets",
    "report_generator",
    "enhanced_analysis",
]
