"""Stage: price_targets

Compute actionable price targets (take-profits and stop-loss) based on technical analysis
and attach the structured targets to PipelineContext.processing_results['price_targets'].
"""

from typing import Dict, Any
from src.core.logger import get_trading_logger
from ..pipeline import PipelineContext


def _compute_targets_from_trend(current_price: float, trend: str, volatility_level: str) -> Dict[str, float]:
    # Basic heuristic targets - you can replace with more advanced formulas later
    if trend == 'uptrend':
        tp1 = current_price * 1.03
        tp2 = current_price * 1.07
        tp3 = current_price * 1.12
        sl = current_price * 0.97
    elif trend == 'downtrend':
        tp1 = current_price * 0.97
        tp2 = current_price * 0.93
        tp3 = current_price * 0.88
        sl = current_price * 1.03
    else:
        tp1 = current_price * 1.02
        tp2 = current_price * 1.05
        tp3 = current_price * 1.09
        sl = current_price * 0.99

    # Adjust for volatility
    multiplier = 1.5 if volatility_level == 'high' else 1.2 if volatility_level == 'medium' else 1.0
    return {
        'tp1': round(tp1 * multiplier, 2),
        'tp2': round(tp2 * multiplier, 2),
        'tp3': round(tp3 * multiplier, 2),
        'sl': round(sl, 2),
    }


async def run(context: PipelineContext) -> PipelineContext:
    """Calculate price targets and attach to context.processing_results['price_targets']."""
    logger = get_trading_logger("price_targets_stage")
    ticker = context.ticker
    logger.info(f"[price_targets] Calculating price targets for {ticker}")

    try:
        market_data = context.processing_results.get('market_data', {})
        technical = context.processing_results.get('technical_analysis', {})

        current_price = market_data.get('current_price') or market_data.get('price') or 0
        trend = technical.get('trend', 'sideways')
        volatility = technical.get('volatility', 'medium')

        targets = _compute_targets_from_trend(float(current_price), trend, volatility)

        context.processing_results['price_targets'] = targets
        logger.info(f"[price_targets] Targets for {ticker}: {targets}")

    except Exception as exc:
        logger.error(f"[price_targets] Error for {ticker}: {exc}")
        context.error_log.append({
            'stage': 'price_targets',
            'error_message': str(exc),
            'error_type': type(exc).__name__,
        })

    return context 