"""
Parallel Analyze Command Pipeline

This module implements an optimized analyze pipeline using the parallel pipeline engine
for generating structured technical analysis reports with improved performance.
"""

import asyncio
from typing import Dict, Any, Optional, List
import time
import logging

from src.core.logger import get_trading_logger
from src.shared.data_providers.unified_base import ProviderError
from src.bot.pipeline.core.parallel_pipeline import (
    FlexiblePipelineBuilder, ParallelPipelineEngine
)
from src.bot.pipeline.core.pipeline_engine import BasePipelineStage, StageResult
from src.bot.pipeline.core.context_manager import PipelineContext, PipelineStatus

logger = get_trading_logger("parallel_analyze_pipeline")


class DataFetchStage(BasePipelineStage):
    """Stage for fetching market data"""
    
    def __init__(self):
        super().__init__("data_fetch")
        self.outputs = ["market_data"]
    
    async def process(self, context: PipelineContext) -> StageResult:
        """Fetch market data for the ticker"""
        start_time = time.time()
        ticker = context.ticker
        
        try:
            # Use real data providers
            from src.shared.data_providers.aggregator import DataProviderAggregator
            
            aggregator = DataProviderAggregator()
            market_data = await aggregator.get_ticker(ticker)
            
            if not market_data or 'error' in market_data:
                raise ProviderError(
                    f"Failed to fetch data for {ticker}: {market_data.get('error', 'Unknown error')}",
                    "aggregator"
                )
            
            return StageResult(
                success=True,
                output_data={"market_data": market_data},
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.error(f"Failed to fetch data for {ticker}: {e}")
            return StageResult(
                success=False,
                output_data={},
                execution_time=time.time() - start_time,
                error_message=f"Data fetch failed: {str(e)}"
            )


class HistoricalDataFetchStage(BasePipelineStage):
    """Stage for fetching historical market data"""
    
    def __init__(self):
        super().__init__("historical_data_fetch")
        self.outputs = ["historical_data"]
    
    async def process(self, context: PipelineContext) -> StageResult:
        """Fetch historical data for the ticker"""
        start_time = time.time()
        ticker = context.ticker
        
        try:
            # Use real data providers
            from src.shared.data_providers.aggregator import DataProviderAggregator
            
            aggregator = DataProviderAggregator()
            historical_data = await aggregator.get_history(ticker, period="1mo", interval="1d")
            
            if not historical_data or 'error' in historical_data:
                raise ProviderError(
                    f"Failed to fetch historical data for {ticker}: {historical_data.get('error', 'Unknown error')}",
                    "aggregator"
                )
            
            return StageResult(
                success=True,
                output_data={"historical_data": historical_data},
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.error(f"Failed to fetch historical data for {ticker}: {e}")
            return StageResult(
                success=False,
                output_data={},
                execution_time=time.time() - start_time,
                error_message=f"Historical data fetch failed: {str(e)}"
            )


class TechnicalAnalysisStage(BasePipelineStage):
    """Stage for generating technical analysis"""
    
    def __init__(self):
        super().__init__("technical_analysis")
        self.required_inputs = ["market_data", "historical_data"]
        self.outputs = ["technical_analysis"]
    
    async def process(self, context: PipelineContext) -> StageResult:
        """Generate technical analysis"""
        start_time = time.time()
        ticker = context.ticker
        
        try:
            # Get data from context
            market_data = context.processing_results.get("market_data", {})
            historical_data = context.processing_results.get("historical_data", {})
            
            import pandas as pd
            from src.shared.technical_analysis.indicators import (
                calculate_rsi, calculate_macd
            )
            
            # Check if we have enough historical data
            closes = historical_data.get('closes', [])
            if len(closes) < 14:
                raise ValueError("Insufficient historical data for technical analysis")
            
            # Convert to pandas Series for indicator calculations
            closes_series = pd.Series(closes)
            
            # Calculate RSI
            rsi = calculate_rsi(closes_series, period=14)
            
            # Calculate MACD
            macd_data = calculate_macd(closes_series)
            macd_signal = "bullish" if macd_data['macd'][-1] > macd_data['signal'][-1] else "bearish"
            
            # Simple support/resistance calculation (pivot points)
            high = historical_data.get('highs', [max(closes)] * len(closes))
            low = historical_data.get('lows', [min(closes)] * len(closes))
            high_series = pd.Series(high)
            low_series = pd.Series(low)
            
            support = low_series.rolling(window=20).min().iloc[-1]
            resistance = high_series.rolling(window=20).max().iloc[-1]
            support_levels = [support]
            resistance_levels = [resistance]
            
            # Determine trend
            trend = "uptrend" if closes[-1] > closes[0] else "downtrend" if closes[-1] < closes[0] else "sideways"
            
            # Calculate volatility (standard deviation of returns)
            returns = closes_series.pct_change().dropna()
            volatility = returns.std()
            volatility_level = "high" if volatility > 0.05 else "medium" if volatility > 0.02 else "low"
            
            technical_analysis = {
                "rsi": float(rsi) if rsi is not None else 50.0,
                "macd": macd_signal,
                "support_levels": support_levels,
                "resistance_levels": resistance_levels,
                "trend": trend,
                "volatility": volatility_level
            }
            
            return StageResult(
                success=True,
                output_data={"technical_analysis": technical_analysis},
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.error(f"Technical analysis failed for {ticker}: {e}")
            
            # Generate fallback technical analysis
            fallback_analysis = {
                "rsi": 50.0,  # Neutral RSI
                "macd": "neutral",
                "support_levels": [market_data.get('current_price', 0) * 0.95],
                "resistance_levels": [market_data.get('current_price', 0) * 1.05],
                "trend": "sideways",
                "volatility": "medium",
                "is_fallback": True,
                "fallback_reason": str(e)
            }
            
            return StageResult(
                success=True,  # Still return success with fallback data
                output_data={"technical_analysis": fallback_analysis},
                execution_time=time.time() - start_time,
                error_message=f"Used fallback technical analysis: {str(e)}"
            )


class PriceTargetsStage(BasePipelineStage):
    """Stage for calculating price targets"""
    
    def __init__(self):
        super().__init__("price_targets")
        self.required_inputs = ["market_data", "technical_analysis"]
        self.outputs = ["price_targets"]
    
    async def process(self, context: PipelineContext) -> StageResult:
        """Calculate price targets"""
        start_time = time.time()
        ticker = context.ticker
        
        try:
            # Get data from context
            market_data = context.processing_results.get("market_data", {})
            technical_analysis = context.processing_results.get("technical_analysis", {})
            
            current_price = market_data.get('current_price', 0)
            if not current_price:
                raise ValueError("No current price available")
            
            trend = technical_analysis.get('trend', 'sideways')
            volatility = technical_analysis.get('volatility', 'medium')
            
            # Calculate targets based on trend and volatility
            if trend == "uptrend":
                # Bullish targets
                short_term = current_price * 1.05  # 5% increase
                medium_term = current_price * 1.15  # 15% increase
                long_term = current_price * 1.30   # 30% increase
            elif trend == "downtrend":
                # Bearish targets
                short_term = current_price * 0.95  # 5% decrease
                medium_term = current_price * 0.85  # 15% decrease
                long_term = current_price * 0.70   # 30% decrease
            else:
                # Sideways targets
                short_term = current_price * 1.02  # 2% increase
                medium_term = current_price * 1.08  # 8% increase
                long_term = current_price * 1.15   # 15% increase
            
            # Adjust for volatility
            volatility_multiplier = 1.5 if volatility == "high" else 1.2 if volatility == "medium" else 1.0
            short_term *= volatility_multiplier
            medium_term *= volatility_multiplier
            long_term *= volatility_multiplier
            
            # Calculate risk/reward ratio
            risk = abs(current_price - short_term)
            reward = abs(long_term - current_price)
            risk_reward_ratio = reward / risk if risk > 0 else 1.0
            
            price_targets = {
                "short_term": round(short_term, 2),
                "medium_term": round(medium_term, 2),
                "long_term": round(long_term, 2),
                "risk_reward_ratio": round(risk_reward_ratio, 2)
            }
            
            return StageResult(
                success=True,
                output_data={"price_targets": price_targets},
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.error(f"Price targets calculation failed for {ticker}: {e}")
            
            # Generate fallback price targets
            current_price = market_data.get('current_price', 0)
            fallback_targets = {
                "short_term": round(current_price * 1.05, 2),
                "medium_term": round(current_price * 1.15, 2),
                "long_term": round(current_price * 1.25, 2),
                "risk_reward_ratio": 2.0,
                "is_fallback": True,
                "fallback_reason": str(e)
            }
            
            return StageResult(
                success=True,  # Still return success with fallback data
                output_data={"price_targets": fallback_targets},
                execution_time=time.time() - start_time,
                error_message=f"Used fallback price targets: {str(e)}"
            )


class EnhancedAnalysisStage(BasePipelineStage):
    """Stage for enhanced analysis"""
    
    def __init__(self):
        super().__init__("enhanced_analysis")
        self.required_inputs = ["market_data", "technical_analysis"]
        self.outputs = ["enhanced_analysis"]
    
    async def process(self, context: PipelineContext) -> StageResult:
        """Generate enhanced analysis"""
        import time
        from src.core.logger import get_trading_logger
        logger = get_trading_logger("enhanced_analysis")
        
        start_time = time.time()
        ticker = context.ticker
        
        try:
            # Get data from context
            market_data = context.processing_results.get("market_data", {})
            technical_analysis = context.processing_results.get("technical_analysis", {})
            
            current_price = market_data.get('current_price', 0)
            if not current_price:
                raise ValueError("No current price available for enhanced analysis")
            
            trend = technical_analysis.get('trend', 'sideways')
            rsi = technical_analysis.get('rsi', 50.0)
            volatility = technical_analysis.get('volatility', 'medium')
            
            # Determine market sentiment based on technical indicators
            if trend == 'uptrend' and rsi < 70:
                sentiment = 'bullish'
                probability = 0.65 + (70 - rsi) / 100 * 0.2  # Adjust based on RSI
            elif trend == 'downtrend' and rsi > 30:
                sentiment = 'bearish'
                probability = 0.65 + (rsi - 30) / 100 * 0.2
            else:
                sentiment = 'neutral'
                probability = 0.5
            
            # Adjust probability based on volatility
            if volatility == 'high':
                probability *= 0.8  # Reduce confidence in high volatility
            elif volatility == 'low':
                probability *= 1.1  # Increase confidence in low volatility
            
            # Calculate confidence score
            confidence = min(0.9, probability * 0.8 + (1 - abs(rsi - 50) / 50) * 0.2)
            
            # Generate key levels summary
            support = technical_analysis.get('support_levels', [current_price * 0.95])
            resistance = technical_analysis.get('resistance_levels', [current_price * 1.05])
            
            key_levels = {
                "nearest_support": min(support),
                "nearest_resistance": min(resistance),
                "distance_to_support": current_price - min(support),
                "distance_to_resistance": min(resistance) - current_price
            }
            
            enhanced_analysis = {
                "sentiment": sentiment,
                "probability": round(probability, 2),
                "confidence": round(confidence, 2),
                "key_levels": key_levels,
                "volatility_assessment": volatility,
                "recommendation_strength": "strong" if confidence > 0.7 else "moderate" if confidence > 0.5 else "weak",
                "trading_bias": f"{sentiment} with {volatility} volatility"
            }
            
            return StageResult(
                success=True,
                output_data={"enhanced_analysis": enhanced_analysis},
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.error(f"Enhanced analysis failed for {ticker}: {e}")
            
            # Create basic sentiment and probability assessment
            trend = technical_analysis.get('trend', 'sideways')
            rsi = technical_analysis.get('rsi', 50.0)
            
            # Determine basic sentiment
            if trend == 'uptrend' and rsi < 70:
                sentiment = 'bullish'
                probability = 0.65
            elif trend == 'downtrend' and rsi > 30:
                sentiment = 'bearish'
                probability = 0.65
            else:
                sentiment = 'neutral'
                probability = 0.5
                
            fallback_enhanced = {
                "sentiment": sentiment,
                "probability": probability,
                "confidence": 0.5,  # Medium confidence
                "key_levels": {
                    "support": technical_analysis.get('support_levels', [market_data.get('current_price', 0) * 0.95]),
                    "resistance": technical_analysis.get('resistance_levels', [market_data.get('current_price', 0) * 1.05])
                },
                "is_fallback": True,
                "fallback_reason": str(e)
            }
            
            return StageResult(
                success=True,  # Still return success with fallback data
                output_data={"enhanced_analysis": fallback_enhanced},
                execution_time=time.time() - start_time,
                error_message=f"Used fallback enhanced analysis: {str(e)}"
            )


class ReportGeneratorStage(BasePipelineStage):
    """Stage for generating the final report"""
    
    def __init__(self):
        super().__init__("report_generator")
        self.required_inputs = ["market_data", "technical_analysis", "price_targets", "enhanced_analysis"]
        self.outputs = ["response"]
    
    async def process(self, context: PipelineContext) -> StageResult:
        """Generate the final report"""
        import time
        from src.core.logger import get_trading_logger
        logger = get_trading_logger("report_generator")
        
        start_time = time.time()
        ticker = context.ticker
        
        try:
            # Get data from all previous stages
            market_data = context.processing_results.get("market_data", {})
            technical_analysis = context.processing_results.get("technical_analysis", {})
            price_targets = context.processing_results.get("price_targets", {})
            enhanced_analysis = context.processing_results.get("enhanced_analysis", {})
            
            # Extract key metrics with fallbacks
            current_price = market_data.get('current_price', 0)
            previous_close = market_data.get('previous_close', current_price)
            change_pct = market_data.get('change_percent', 0)
            volume = market_data.get('volume', 0)
            
            rsi = technical_analysis.get('rsi', 50.0)
            trend = technical_analysis.get('trend', 'sideways')
            support_levels = technical_analysis.get('support_levels', [current_price * 0.95])
            resistance_levels = technical_analysis.get('resistance_levels', [current_price * 1.05])
            volatility = technical_analysis.get('volatility', 'medium')
            
            short_term = price_targets.get('short_term', current_price * 1.05)
            medium_term = price_targets.get('medium_term', current_price * 1.15)
            long_term = price_targets.get('long_term', current_price * 1.25)
            risk_reward = price_targets.get('risk_reward_ratio', 2.0)
            
            sentiment = enhanced_analysis.get('sentiment', 'neutral')
            probability = enhanced_analysis.get('probability', 0.5)
            confidence = enhanced_analysis.get('confidence', 0.5)
            recommendation_strength = enhanced_analysis.get('recommendation_strength', 'moderate')
            
            # Check if using fallback data
            using_fallback = (
                technical_analysis.get('is_fallback', False) or
                price_targets.get('is_fallback', False) or
                enhanced_analysis.get('is_fallback', False)
            )
            
            # Build the report structure
            report = f"""# 📊 Technical Analysis Report: {ticker.upper()}

## 📈 Market Overview
**Current Price:** ${current_price:.2f}
**Previous Close:** ${previous_close:.2f}
**Change:** {change_pct:+.2f}%
**Volume:** {volume:,.0f} shares
**Volatility:** {volatility.capitalize()}

## 🔍 Technical Indicators
- **RSI (14):** {rsi:.1f} {'(Overbought)' if rsi > 70 else '(Oversold)' if rsi < 30 else '(Neutral)'}
- **Trend:** {trend.capitalize()}
- **MACD Signal:** {technical_analysis.get('macd', 'neutral').capitalize()}
- **Support Levels:** ${min(support_levels):.2f}, ${support_levels[1]:.2f if len(support_levels) > 1 else current_price * 0.98:.2f}
- **Resistance Levels:** ${min(resistance_levels):.2f}, ${resistance_levels[1]:.2f if len(resistance_levels) > 1 else current_price * 1.02:.2f}

## 🎯 Price Targets
- **Short-term Target (1-2 weeks):** ${short_term:.2f} ({(short_term - current_price)/current_price*100:+.1f}%)
- **Medium-term Target (1-3 months):** ${medium_term:.2f} ({(medium_term - current_price)/current_price*100:+.1f}%)
- **Long-term Target (3-6 months):** ${long_term:.2f} ({(long_term - current_price)/current_price*100:+.1f}%)
- **Risk/Reward Ratio:** {risk_reward:.1f}:1

## 🤖 AI Enhanced Analysis
**Market Sentiment:** {sentiment.capitalize()}
**Confidence Level:** {confidence*100:.0f}%
**Trading Probability:** {probability*100:.0f}%
**Recommendation Strength:** {recommendation_strength.capitalize()}
**Trading Bias:** {enhanced_analysis.get('trading_bias', 'neutral')}"""

            if using_fallback:
                report += """

⚠️ **Data Quality Notice:** This report contains fallback analysis due to temporary data processing issues. Some metrics may be estimates."""

            # Add trading strategy suggestions based on analysis
            report += """

## 💡 Trading Strategy Suggestions

### Based on Current Technical Setup:
"""
            
            if sentiment == 'bullish' and confidence > 0.6:
                report += "- **Bullish Bias:** Consider long positions near support levels\n"
                report += "- **Entry:** Near ${min(support_levels):.2f} (support zone)\n"
                report += "- **Stop Loss:** Below nearest support (${min(support_levels)*0.98:.2f})\n"
                report += "- **Take Profit:** Short-term target ${short_term:.2f}"
            elif sentiment == 'bearish' and confidence > 0.6:
                report += "- **Bearish Bias:** Consider short positions near resistance levels\n"
                report += "- **Entry:** Near ${min(resistance_levels):.2f} (resistance zone)\n"
                report += "- **Stop Loss:** Above nearest resistance (${min(resistance_levels)*1.02:.2f})\n"
                report += "- **Take Profit:** Short-term target ${short_term:.2f}"
            else:
                report += "- **Neutral Market:** Wait for clearer signals or use range trading\n"
                report += "- **Range Trading:** Buy near support, sell near resistance\n"
                report += "- **Support:** ${min(support_levels):.2f}\n"
                report += "- **Resistance:** ${min(resistance_levels):.2f}"

            report += f"""

## ⚠️ Risk Management
- **Position Size:** Risk no more than 1-2% of capital per trade
- **Risk/Reward:** Target at least {risk_reward:.1f}:1 ratio
- **Volatility Adjustment:** {'Reduce position size' if volatility == 'high' else 'Normal position sizing' if volatility == 'medium' else 'Can increase position size slightly'} due to {volatility} volatility

*Generated on {time.strftime('%Y-%m-%d %H:%M UTC')} | Analysis Confidence: {confidence*100:.0f}%*

---

**DISCLAIMER:** This analysis is for educational and informational purposes only and does not constitute financial, investment, or trading advice. Past performance is not indicative of future results. Always conduct your own research and consider consulting with a licensed financial advisor before making any investment decisions. The information provided is based on technical analysis and may not account for fundamental factors or market conditions. Trading involves substantial risk of loss and is not suitable for all investors."""

            context.processing_results["response"] = report
            context.status = PipelineStatus.COMPLETED
            context.completion_time = time.time()
            
            logger.info(f"Report generated successfully for {ticker}")
            return context
            
        except Exception as e:
            logger.error(f"Report generation failed for {ticker}: {e}")
            
            # Get data from previous stages (with fallback defaults)
            market_data = context.processing_results.get("market_data", {})
            technical_analysis = context.processing_results.get("technical_analysis", {})
            price_targets = context.processing_results.get("price_targets", {})
            enhanced_analysis = context.processing_results.get("enhanced_analysis", {})
            
            # Extract key data points with fallbacks
            current_price = market_data.get('current_price', 0)
            previous_close = market_data.get('previous_close', current_price * 0.99)
            change_pct = market_data.get('change_percent', 0)
            
            # Check if we're using fallback data
            using_fallback = (
                technical_analysis.get('is_fallback', False) or
                price_targets.get('is_fallback', False) or
                enhanced_analysis.get('is_fallback', False)
            )
            
            # Create a basic report template
            report = f"""## {ticker} Technical Analysis

**Current Price:** ${current_price:.2f}
**Previous Close:** ${previous_close:.2f}
**Change:** {change_pct:.2f}%

### Technical Indicators
- **RSI:** {technical_analysis.get('rsi', 50):.1f}
- **Trend:** {technical_analysis.get('trend', 'sideways').capitalize()}
- **Support:** ${technical_analysis.get('support_levels', [current_price * 0.95])[0]:.2f}
- **Resistance:** ${technical_analysis.get('resistance_levels', [current_price * 1.05])[0]:.2f}

### Price Targets
- **Short-term:** ${price_targets.get('short_term', current_price * 1.05):.2f}
- **Medium-term:** ${price_targets.get('medium_term', current_price * 1.15):.2f}
- **Long-term:** ${price_targets.get('long_term', current_price * 1.25):.2f}

### Market Sentiment
- **Outlook:** {enhanced_analysis.get('sentiment', 'neutral').capitalize()}
- **Probability:** {enhanced_analysis.get('probability', 0.5) * 100:.1f}%
"""
            
            # Add fallback notice if using fallback data
            if using_fallback:
                fallback_notice = "\n\n⚠️ **Note:** This analysis contains fallback data due to processing errors. Some information may be less accurate than usual."
                report += fallback_notice
            
            # Add disclaimer
            disclaimer = "\n\n*This analysis is for informational purposes only and does not constitute investment advice. Always do your own research before making investment decisions.*"
            report += disclaimer
            
            return StageResult(
                success=True,  # Still return success with fallback report
                output_data={"response": report},
                execution_time=time.time() - start_time,
                error_message=f"Used fallback report: {str(e)}"
            )


def create_parallel_analyze_pipeline() -> ParallelPipelineEngine:
    """Create and configure the parallel analyze pipeline"""
    builder = FlexiblePipelineBuilder("analyze")
    
    # Add stages in logical order (dependencies will be automatically resolved)
    builder.add_stages([
        DataFetchStage(),
        HistoricalDataFetchStage(),
        TechnicalAnalysisStage(),
        PriceTargetsStage(),
        EnhancedAnalysisStage(),
        ReportGeneratorStage()
    ])
    
    # Configure pipeline
    builder.set_max_execution_time(45.0)
    builder.set_concurrency_limit(3)  # Limit to 3 concurrent stages
    
    # Configure circuit breaker
    builder.configure_circuit_breaker(
        enabled=True,
        failure_threshold=3,
        recovery_timeout=60.0
    )
    
    return builder.build()


async def execute_parallel_analyze_pipeline(
    ticker: str,
    user_id: Optional[str] = None,
    guild_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
    strict_mode: bool = True
) -> PipelineContext:
    """
    Execute the parallel analyze pipeline for a given ticker
    
    Args:
        ticker: Stock symbol to analyze
        user_id: Discord user ID
        guild_id: Discord guild ID
        correlation_id: Request correlation ID
        strict_mode: Whether to use strict formatting
        
    Returns:
        PipelineContext with results
    """
    logger.info(f"Starting parallel analyze pipeline for {ticker}", extra={
        "ticker": ticker,
        "correlation_id": correlation_id,
        "strict_mode": strict_mode
    })
    
    # Create pipeline context
    context = PipelineContext()
    context.ticker = ticker.upper()
    context.user_id = user_id
    context.guild_id = guild_id
    context.correlation_id = correlation_id
    context.strict_mode = strict_mode
    
    # Create and execute pipeline
    pipeline = create_parallel_analyze_pipeline()
    
    try:
        # Execute pipeline
        context = await pipeline.execute(context)
        
        # Log completion
        if context.status == PipelineStatus.COMPLETED:
            logger.info(f"Parallel analyze pipeline completed successfully for {ticker}")
        else:
            logger.warning(f"Parallel analyze pipeline completed with status {context.status} for {ticker}")
            
    except Exception as e:
        logger.error(f"Parallel analyze pipeline failed for {ticker}: {e}")
        context.status = PipelineStatus.FAILED
        context.error_log.append({
            "error_message": str(e),
            "error_type": type(e).__name__,
            "stage": "pipeline_execution"
        })
        
        # Generate error response with more helpful information
        error_response = f"""❌ **ANALYSIS ENCOUNTERED ISSUES FOR {ticker}**

Error: {str(e)}
"""
        
        # Check if we have any partial results to show
        if "market_data" in context.processing_results:
            error_response += f"\n**Current Price:** ${context.processing_results['market_data'].get('current_price', 'N/A')}"
            
        # Add fallback data if available
        if "technical_analysis" in context.processing_results:
            error_response += "\n\n**Basic Technical Analysis:**"
            tech = context.processing_results["technical_analysis"]
            error_response += f"\n• RSI: {tech.get('rsi', 'N/A')}"
            error_response += f"\n• Trend: {tech.get('trend', 'N/A')}"
            
        if "price_targets" in context.processing_results:
            error_response += "\n\n**Estimated Price Targets:**"
            targets = context.processing_results["price_targets"]
            error_response += f"\n• Short-term: ${targets.get('short_term', 'N/A')}"
            error_response += f"\n• Medium-term: ${targets.get('medium_term', 'N/A')}"
            
        # Add disclaimer and support info
        error_response += "\n\n⚠️ **Note:** This is partial data generated from fallback systems due to an error."
        error_response += "\nPlease try again later or contact support if the issue persists."
        error_response += "\n\n*This analysis is for informational purposes only and does not constitute investment advice.*"
            
        context.processing_results["response"] = error_response
    
    return context
