"""
Analyze Command Pipeline

This module implements the analyze pipeline for generating structured technical analysis reports.
"""

import asyncio
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from src.core.logger import get_trading_logger
from src.core.formatting.analysis_template import AnalysisTemplate
from src.shared.data_providers.unified_base import UnifiedDataProvider, ProviderError


class PipelineStatus(Enum):
    """Pipeline execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class PipelineContext:
    """Context for pipeline execution"""
    ticker: str
    user_id: Optional[str] = None
    guild_id: Optional[str] = None
    correlation_id: Optional[str] = None
    strict_mode: bool = True
    status: PipelineStatus = PipelineStatus.PENDING
    processing_results: Dict[str, Any] = None
    error_log: list = None
    
    def __post_init__(self):
        if self.processing_results is None:
            self.processing_results = {}
        if self.error_log is None:
            self.error_log = []


async def execute_analyze_pipeline(
    ticker: str,
    user_id: Optional[str] = None,
    guild_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
    strict_mode: bool = True
) -> PipelineContext:
    """
    Execute the analyze pipeline for a given ticker
    
    Args:
        ticker: Stock symbol to analyze
        user_id: Discord user ID
        guild_id: Discord guild ID
        correlation_id: Request correlation ID
        strict_mode: Whether to use strict formatting
        
        Returns:
        PipelineContext with results
    """
    logger = get_trading_logger("analyze_pipeline")
    
    # Create pipeline context
    context = PipelineContext(
        ticker=ticker.upper(),
        user_id=user_id,
        guild_id=guild_id,
        correlation_id=correlation_id,
        strict_mode=strict_mode
    )
    
    try:
        logger.info(f"Starting analyze pipeline for {ticker}", extra={
            "ticker": ticker,
            "correlation_id": correlation_id,
            "strict_mode": strict_mode
        })
        
        context.status = PipelineStatus.RUNNING
        
        # Stage 1: Validate ticker
        if not ticker or len(ticker) > 10:
            raise ValueError(f"Invalid ticker: {ticker}")
        
        # Stage 2: Fetch market data
        logger.info(f"Fetching data for {ticker}")
        try:
            # Use real data providers instead of mock data
            from src.shared.data_providers.aggregator import DataProviderAggregator
            
            aggregator = DataProviderAggregator()
            market_data = await aggregator.get_ticker(ticker)
            
            if not market_data or 'error' in market_data:
                raise ProviderError(f"Failed to fetch data for {ticker}: {market_data.get('error', 'Unknown error')}", "aggregator")
            
            context.processing_results["market_data"] = market_data
        except Exception as e:
            logger.error(f"Failed to fetch data for {ticker}: {e}")
            raise ProviderError(f"Data fetch failed: {e}", "aggregator")
        
        # Stage 3: Generate technical analysis (using modular stage)
        logger.info(f"Running stage: technical_analysis for {ticker}")
        try:
            from .stages.technical_analysis import run as technical_run
            context = await technical_run(context)
        except Exception as e:
            logger.error(f"technical_analysis stage failed: {e}")
            # Fallback: Generate basic technical analysis
            logger.info(f"Using fallback technical analysis for {ticker}")
            market_data = context.processing_results.get("market_data", {})
            fallback_analysis = {
                "rsi": 50.0,  # Neutral RSI
                "macd": "neutral",
                "support_levels": [market_data.get('current_price', 0) * 0.95],
                "resistance_levels": [market_data.get('current_price', 0) * 1.05],
                "trend": "sideways",
                "volatility": "medium",
                "is_fallback": True,  # Flag to indicate this is fallback data
                "fallback_reason": str(e)
            }
            context.processing_results["technical_analysis"] = fallback_analysis
            context.error_log.append({
                "error_message": str(e),
                "error_type": type(e).__name__,
                "stage": "technical_analysis",
                "fallback_applied": True
            })

        # Stage 4: Calculate price targets (modular)
        logger.info(f"Running stage: price_targets for {ticker}")
        try:
            from .stages.price_targets import run as price_targets_run
            context = await price_targets_run(context)
        except Exception as e:
            logger.error(f"price_targets stage failed: {e}")
            # Fallback: Generate basic price targets
            logger.info(f"Using fallback price targets for {ticker}")
            market_data = context.processing_results.get("market_data", {})
            current_price = market_data.get('current_price', 0)
            fallback_targets = {
                "short_term": round(current_price * 1.05, 2),  # 5% increase
                "medium_term": round(current_price * 1.15, 2),  # 15% increase
                "long_term": round(current_price * 1.25, 2),  # 25% increase
                "risk_reward_ratio": 2.0,
                "is_fallback": True,  # Flag to indicate this is fallback data
                "fallback_reason": str(e)
            }
            context.processing_results["price_targets"] = fallback_targets
            context.error_log.append({
                "error_message": str(e),
                "error_type": type(e).__name__,
                "stage": "price_targets",
                "fallback_applied": True
            })

        # Stage 5: Enhanced Analysis (new enhanced engines)
        logger.info(f"Running stage: enhanced_analysis for {ticker}")
        try:
            from .stages.enhanced_analysis import EnhancedAnalysisStage
            enhanced_stage = EnhancedAnalysisStage()
            context = await enhanced_stage.run(context)
        except Exception as e:
            logger.error(f"enhanced_analysis stage failed: {e}")
            # Fallback: Generate basic enhanced analysis
            logger.info(f"Using fallback enhanced analysis for {ticker}")
            market_data = context.processing_results.get("market_data", {})
            technical_analysis = context.processing_results.get("technical_analysis", {})
            
            # Create basic sentiment and probability assessment
            trend = technical_analysis.get('trend', 'sideways')
            rsi = technical_analysis.get('rsi', 50.0)
            
            # Determine basic sentiment
            if trend == 'uptrend' and rsi < 70:
                sentiment = 'bullish'
                probability = 0.65
            elif trend == 'downtrend' and rsi > 30:
                sentiment = 'bearish'
                probability = 0.65
            else:
                sentiment = 'neutral'
                probability = 0.5
                
            fallback_enhanced = {
                "sentiment": sentiment,
                "probability": probability,
                "confidence": 0.5,  # Medium confidence
                "key_levels": {
                    "support": technical_analysis.get('support_levels', [market_data.get('current_price', 0) * 0.95]),
                    "resistance": technical_analysis.get('resistance_levels', [market_data.get('current_price', 0) * 1.05])
                },
                "is_fallback": True,  # Flag to indicate this is fallback data
                "fallback_reason": str(e)
            }
            context.processing_results["enhanced_analysis"] = fallback_enhanced
            context.error_log.append({
                "error_message": str(e),
                "error_type": type(e).__name__,
                "stage": "enhanced_analysis",
                "fallback_applied": True
            })

        # Stage 6: Generate structured report (modular)
        logger.info(f"Running stage: report_generator for {ticker}")
        try:
            from .stages.report_generator import run as report_run
            context = await report_run(context)
        except Exception as e:
            logger.error(f"report_generator stage failed: {e}")
            # Fallback: Generate basic report
            logger.info(f"Using fallback report generator for {ticker}")
            
            # Get data from previous stages (with fallback defaults)
            market_data = context.processing_results.get("market_data", {})
            technical_analysis = context.processing_results.get("technical_analysis", {})
            price_targets = context.processing_results.get("price_targets", {})
            enhanced_analysis = context.processing_results.get("enhanced_analysis", {})
            
            # Extract key data points with fallbacks
            current_price = market_data.get('current_price', 0)
            previous_close = market_data.get('previous_close', current_price * 0.99)
            change_pct = market_data.get('change_percent', 0)
            
            # Check if we're using fallback data
            using_fallback = (
                technical_analysis.get('is_fallback', False) or
                price_targets.get('is_fallback', False) or
                enhanced_analysis.get('is_fallback', False)
            )
            
            # Create a basic report template
            report = f"""## {ticker} Technical Analysis

**Current Price:** ${current_price:.2f}
**Previous Close:** ${previous_close:.2f}
**Change:** {change_pct:.2f}%

### Technical Indicators
- **RSI:** {technical_analysis.get('rsi', 50):.1f}
- **Trend:** {technical_analysis.get('trend', 'sideways').capitalize()}
- **Support:** ${technical_analysis.get('support_levels', [current_price * 0.95])[0]:.2f}
- **Resistance:** ${technical_analysis.get('resistance_levels', [current_price * 1.05])[0]:.2f}

### Price Targets
- **Short-term:** ${price_targets.get('short_term', current_price * 1.05):.2f}
- **Medium-term:** ${price_targets.get('medium_term', current_price * 1.15):.2f}
- **Long-term:** ${price_targets.get('long_term', current_price * 1.25):.2f}

### Market Sentiment
- **Outlook:** {enhanced_analysis.get('sentiment', 'neutral').capitalize()}
- **Probability:** {enhanced_analysis.get('probability', 0.5) * 100:.1f}%
"""
            
            # Add fallback notice if using fallback data
            if using_fallback:
                fallback_notice = "\n\n⚠️ **Note:** This analysis contains fallback data due to processing errors. Some information may be less accurate than usual."
                report += fallback_notice
            
            # Add disclaimer
            disclaimer = "\n\n*This analysis is for informational purposes only and does not constitute investment advice. Always do your own research before making investment decisions.*"
            report += disclaimer
            
            context.processing_results["response"] = report
            context.error_log.append({
                "error_message": str(e),
                "error_type": type(e).__name__,
                "stage": "report_generator",
                "fallback_applied": True
            })
        
        # Mark pipeline as completed
        context.status = PipelineStatus.COMPLETED
        logger.info(f"Analyze pipeline completed successfully for {ticker}")
        
    except Exception as e:
        logger.error(f"Analyze pipeline failed for {ticker}: {e}")
        context.status = PipelineStatus.FAILED
        context.error_log.append({
            "error_message": str(e),
            "error_type": type(e).__name__,
            "stage": "pipeline_execution"
        })
        
        # Generate error response with more helpful information
        error_response = f"""❌ **ANALYSIS ENCOUNTERED ISSUES FOR {ticker}**

Error: {str(e)}
"""
        
        # Check if we have any partial results to show
        if "market_data" in context.processing_results:
            error_response += f"\n**Current Price:** ${context.processing_results['market_data'].get('current_price', 'N/A')}"
            
        # Add fallback data if available
        if "technical_analysis" in context.processing_results:
            error_response += "\n\n**Basic Technical Analysis:**"
            tech = context.processing_results["technical_analysis"]
            error_response += f"\n• RSI: {tech.get('rsi', 'N/A')}"
            error_response += f"\n• Trend: {tech.get('trend', 'N/A')}"
            
        if "price_targets" in context.processing_results:
            error_response += "\n\n**Estimated Price Targets:**"
            targets = context.processing_results["price_targets"]
            error_response += f"\n• Short-term: ${targets.get('short_term', 'N/A')}"
            error_response += f"\n• Medium-term: ${targets.get('medium_term', 'N/A')}"
            
        # Add disclaimer and support info
        error_response += "\n\n⚠️ **Note:** This is partial data generated from fallback systems due to an error."
        error_response += "\nPlease try again later or contact support if the issue persists."
        error_response += "\n\n*This analysis is for informational purposes only and does not constitute investment advice.*"
            
        context.processing_results["response"] = error_response
    
    return context


async def _generate_real_technical_analysis(ticker: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
    """Generate real technical analysis using actual market data"""
    try:
        # Import technical analysis tools
        from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator
        
        calculator = TechnicalAnalysisCalculator()
        
        # Get historical data for calculations
        from src.shared.data_providers.aggregator import DataProviderAggregator
        aggregator = DataProviderAggregator()
        
        # Get historical data (last 30 days)
        historical_data = await aggregator.get_history(ticker, period="1mo", interval="1d")
        
        if not historical_data or 'error' in historical_data:
            # Fallback to basic analysis if historical data fails
            return {
                "rsi": 50.0,
                "macd": "neutral",
                "support_levels": [market_data.get('current_price', 0) * 0.95],
                "resistance_levels": [market_data.get('current_price', 0) * 1.05],
                "trend": "sideways",
                "volatility": "low"
            }
        
        # Calculate real technical indicators
        closes = historical_data.get('closes', [])
        if len(closes) < 14:
            raise ValueError("Insufficient historical data for technical analysis")
        
        # Calculate RSI
        rsi = calculator.calculate_rsi(closes, period=14)
        
        # Calculate MACD
        macd_data = calculator.calculate_macd(closes)
        macd_signal = "bullish" if macd_data['macd'] > macd_data['signal'] else "bearish"
        
        # Calculate support/resistance levels
        support_levels = calculator.calculate_support_levels(closes)
        resistance_levels = calculator.calculate_resistance_levels(closes)
        
        # Determine trend
        trend = "uptrend" if closes[-1] > closes[0] else "downtrend" if closes[-1] < closes[0] else "sideways"
        
        # Calculate volatility
        volatility = calculator.calculate_volatility(closes)
        volatility_level = "high" if volatility > 0.05 else "medium" if volatility > 0.02 else "low"
        
        return {
            "rsi": rsi,
            "macd": macd_signal,
            "support_levels": support_levels,
            "resistance_levels": resistance_levels,
            "trend": trend,
            "volatility": volatility_level
        }
        
    except Exception as e:
        # Use get_logger instead of undefined logger
        from src.core.logger import get_logger
        logger = get_logger(__name__)
        logger.warning(f"Failed to generate real technical analysis for {ticker}: {e}")
        # Return basic analysis as fallback
        return {
            "rsi": 50.0,
            "macd": "neutral",
            "support_levels": [market_data.get('current_price', 0) * 0.95],
            "resistance_levels": [market_data.get('current_price', 0) * 1.05],
            "trend": "sideways",
            "volatility": "low"
        }


def _calculate_real_price_targets(market_data: Dict[str, Any], technical_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """Calculate real price targets based on technical analysis"""
    try:
        current_price = market_data.get('current_price', 0)
        if not current_price:
            raise ValueError("No current price available")
        
        trend = technical_analysis.get('trend', 'sideways')
        volatility = technical_analysis.get('volatility', 'medium')
        
        # Calculate targets based on trend and volatility
        if trend == "uptrend":
            # Bullish targets
            short_term = current_price * 1.05  # 5% increase
            medium_term = current_price * 1.15  # 15% increase
            long_term = current_price * 1.30   # 30% increase
        elif trend == "downtrend":
            # Bearish targets
            short_term = current_price * 0.95  # 5% decrease
            medium_term = current_price * 0.85  # 15% decrease
            long_term = current_price * 0.70   # 30% decrease
        else:
            # Sideways targets
            short_term = current_price * 1.02  # 2% increase
            medium_term = current_price * 1.08  # 8% increase
            long_term = current_price * 1.15   # 15% increase
        
        # Adjust for volatility
        volatility_multiplier = 1.5 if volatility == "high" else 1.2 if volatility == "medium" else 1.0
        short_term *= volatility_multiplier
        medium_term *= volatility_multiplier
        long_term *= volatility_multiplier
        
        # Calculate risk/reward ratio
        risk = abs(current_price - short_term)
        reward = abs(long_term - current_price)
        risk_reward_ratio = reward / risk if risk > 0 else 1.0
        
        return {
            "short_term": round(short_term, 2),
            "medium_term": round(medium_term, 2),
            "long_term": round(long_term, 2),
            "risk_reward_ratio": round(risk_reward_ratio, 2)
        }
        
    except Exception as e:
        # Use get_logger instead of undefined logger
        from src.core.logger import get_logger
        logger = get_logger(__name__)
        logger.warning(f"Failed to calculate real price targets: {e}")
        # Return basic targets as fallback
        current_price = market_data.get('current_price', 0)
        return {
            "short_term": round(current_price * 1.05, 2),
            "medium_term": round(current_price * 1.15, 2),
            "long_term": round(current_price * 1.25, 2),
            "risk_reward_ratio": 2.0
        }
