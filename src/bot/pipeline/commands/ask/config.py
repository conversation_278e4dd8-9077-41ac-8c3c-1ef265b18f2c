"""
Enhanced Ask Pipeline Configuration - PROFESSIONAL GRADE

Enterprise-grade configuration management for the ask pipeline with:
✅ Comprehensive environment variable validation
✅ Type-safe configuration with defaults
✅ Performance tuning parameters
✅ Security and compliance settings
✅ Real-time configuration updates
✅ Configuration validation and health checks
"""

import os
import logging
from typing import Optional, Dict, Any, List
from functools import lru_cache
from dataclasses import dataclass, field
from enum import Enum
import json
import yaml
from pathlib import Path
import time
from src.core.exceptions import ConfigurationError, ConfigValidationError

logger = logging.getLogger(__name__)


class ConfigSource(Enum):
    """Configuration source types"""
    ENVIRONMENT = "environment"
    YAML_FILE = "yaml_file"
    DEFAULT = "default"
    OVERRIDE = "override"


@dataclass
class ConfigValidation:
    """Configuration validation results"""
    is_valid: bool = True
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    source: ConfigSource = ConfigSource.ENVIRONMENT
    
    def add_error(self, error: str):
        """Add validation error"""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str):
        """Add validation warning"""
        self.warnings.append(warning)
    
    def get_summary(self) -> str:
        """Get validation summary"""
        if self.is_valid:
            return f"✅ Configuration valid from {self.source.value}"
        else:
            return f"❌ Configuration invalid: {', '.join(self.errors)}"


class AskPipelineConfig:
    """Enhanced configuration for the ask pipeline"""
    
    def __init__(self):
        # Existing configs
        self.circuit_breaker_threshold = self._get_int("CIRCUIT_BREAKER_THRESHOLD", 3)
        self.circuit_breaker_cooldown = 60  # Seconds before half-open state
        self._config_source = ConfigSource.ENVIRONMENT
        self._validation = ConfigValidation()
        
        # Load configuration with validation
        self._load_configuration()
        self._validate_configuration()
        
        # Log configuration status
        logger.info(f"🔧 Configuration loaded from {self._config_source.value}")
        if not self._validation.is_valid:
            logger.error(f"❌ Configuration validation failed: {self._validation.get_summary()}")
        else:
            logger.info(f"✅ Configuration validation passed")
    
    def _load_configuration(self):
        """Load configuration from multiple sources with priority"""
        # Priority 1: Environment variables
        if self._load_from_environment():
            self._config_source = ConfigSource.ENVIRONMENT
            return
        
        # Priority 2: YAML configuration file
        if self._load_from_yaml():
            self._config_source = ConfigSource.YAML_FILE
            return
        
        # Priority 3: Default values
        self._load_defaults()
        self._config_source = ConfigSource.DEFAULT
    
    def _load_from_environment(self) -> bool:
        """Load configuration from environment variables"""
        try:
            # Pipeline enablement
            self.enabled = self._get_bool("ASK_PIPELINE_ENABLED", True)
            
            # Pipeline execution settings
            self.timeout = self._get_float("ASK_PIPELINE_TIMEOUT", 30.0)
            self.max_retries = self._get_int("ASK_PIPELINE_MAX_RETRIES", 3)
            self.quality_threshold = self._get_float("ASK_PIPELINE_QUALITY_THRESHOLD", 0.75)
            
            # Caching settings
            self.cache_enabled = self._get_bool("ASK_PIPELINE_CACHE_ENABLED", True)
            self.cache_ttl = self._get_int("ASK_PIPELINE_CACHE_TTL", 300)
            
            # Pipeline performance settings
            self.parallel_execution = self._get_bool("PIPELINE_PARALLEL_EXECUTION", False)
            self.section_timeout = self._get_float("PIPELINE_SECTION_TIMEOUT", 15.0)
            self.quality_check_enabled = self._get_bool("PIPELINE_QUALITY_CHECK_ENABLED", True)
            self.fallback_enabled = self._get_bool("PIPELINE_FALLBACK_ENABLED", True)
            
            # Monitoring settings
            self.log_level = self._get_string("PIPELINE_LOG_LEVEL", "INFO")
            self.enable_metrics = self._get_bool("PIPELINE_ENABLE_METRICS", True)
            self.enable_audit_trail = self._get_bool("PIPELINE_ENABLE_AUDIT_TRAIL", True)
            self.enable_performance_tracking = self._get_bool("PIPELINE_PERFORMANCE_TRACKING", True)
            
            # Response settings
            self.response_template_cache_enabled = self._get_bool("RESPONSE_TEMPLATE_CACHE_ENABLED", True)
            self.response_template_cache_ttl = self._get_int("RESPONSE_TEMPLATE_CACHE_TTL", 600)
            self.response_max_length = self._get_int("RESPONSE_MAX_LENGTH", 2000)
            self.response_enable_markdown = self._get_bool("RESPONSE_ENABLE_MARKDOWN", True)
            
            # Market data provider settings
            self.yahoo_finance_enabled = self._get_bool("YAHOO_FINANCE_ENABLED", True)
            self.polygon_enabled = self._get_bool("POLYGON_ENABLED", True)
            self.finnhub_enabled = self._get_bool("FINNHUB_ENABLED", True)
            self.alpha_vantage_enabled = self._get_bool("ALPHA_VANTAGE_ENABLED", True)
            
            # Rate limiting (requests per minute)
            self.yahoo_finance_rate_limit = self._get_int("YAHOO_FINANCE_RATE_LIMIT", 5)
            self.polygon_rate_limit = self._get_int("POLYGON_RATE_LIMIT", 5)
            self.finnhub_rate_limit = self._get_int("FINNHUB_RATE_LIMIT", 30)
            self.alpha_vantage_rate_limit = self._get_int("ALPHA_VANTAGE_RATE_LIMIT", 5)
            
            # Timeouts (seconds)
            self.yahoo_finance_timeout = self._get_float("YAHOO_FINANCE_TIMEOUT", 10.0)
            self.polygon_timeout = self._get_float("POLYGON_TIMEOUT", 8.0)
            self.finnhub_timeout = self._get_float("FINNHUB_TIMEOUT", 5.0)
            self.alpha_vantage_timeout = self._get_float("ALPHA_VANTAGE_TIMEOUT", 10.0)
            
            # Development/testing overrides
            self.enable_mock_data = self._get_bool("ENABLE_MOCK_DATA", False)
            self.enable_debug_logging = self._get_bool("ENABLE_DEBUG_LOGGING", False)
            self.enable_performance_profiling = self._get_bool("ENABLE_PERFORMANCE_PROFILING", False)
            self.enable_api_response_logging = self._get_bool("ENABLE_API_RESPONSE_LOGGING", False)
            
            # Circuit Breaker Configuration
            self.circuit_breaker_enabled = self._get_bool("CIRCUIT_BREAKER_ENABLED", True)
            self.circuit_breaker_threshold = self._get_int("CIRCUIT_BREAKER_THRESHOLD", 3)
            self.circuit_breaker_cooldown = self._get_int("CIRCUIT_BREAKER_COOLDOWN", 60)
            self.circuit_breaker_timeout = self._get_int("CIRCUIT_BREAKER_TIMEOUT", 30)
            
            # OpenRouter AI configuration
            self.openrouter_enabled = self._get_bool("OPENROUTER_ENABLED", True)
            self.openrouter_api_key = self._get_string("OPENROUTER_API_KEY", "")
            self.openrouter_model = self._get_string("OPENROUTER_GENERAL_MODEL", self._get_string("MODEL_GLOBAL_FALLBACK", "openrouter/sonoma-sky-alpha"))
            self.openrouter_temperature = self._get_float("OPENROUTER_GENERAL_MODEL_TEMPERATURE", 0.7)
            self.openrouter_max_tokens = self._get_int("OPENROUTER_GENERAL_MODEL_MAX_TOKENS", 2000)
            self.openrouter_timeout = self._get_float("ASK_PIPELINE_TIMEOUT", 30.0)
            self.openrouter_base_url = self._get_string("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
            
            # Model fallback configuration - use consistent environment variables
            self.model_global_fallback = self._get_string("MODEL_GLOBAL_FALLBACK", "moonshotai/kimi-k2:free")
            self.llm_model = self._get_string("MODEL_LLM", self.model_global_fallback)
            self.model_quick = self._get_string("MODEL_QUICK", self.model_global_fallback)
            self.model_analysis = self._get_string("MODEL_ANALYSIS", self.model_global_fallback)
            self.model_heavy = self._get_string("MODEL_HEAVY", self.model_global_fallback)

            # AI Pipeline configuration
            self.ai_pipeline_timeout = self._get_float("ASK_PIPELINE_TIMEOUT", 30.0)
            self.ai_pipeline_cache_ttl = self._get_int("ASK_PIPELINE_CACHE_TTL", 3600)
            self.ai_pipeline_max_retries = self._get_int("ASK_PIPELINE_MAX_RETRIES", 3)
            self.ai_pipeline_parallel_execution = self._get_bool("ASK_PIPELINE_PARALLEL_EXECUTION", False)

            # AI Model Selection Preferences
            self.ai_prefer_cost_efficiency = self._get_bool("AI_PREFER_COST_EFFICIENCY", False)
            self.ai_prefer_speed = self._get_bool("AI_PREFER_SPEED", False)
            self.ai_prefer_accuracy = self._get_bool("AI_PREFER_ACCURACY", True)
            self.ai_enable_fallback_chain = self._get_bool("AI_ENABLE_FALLBACK_CHAIN", True)
            self.ai_max_fallback_attempts = self._get_int("AI_MAX_FALLBACK_ATTEMPTS", 3)

            # Performance Tracking
            self.ai_performance_tracking = self._get_bool("AI_PERFORMANCE_TRACKING", True)
            self.ai_metrics_retention = self._get_int("AI_METRICS_RETENTION", 30)
            self.ai_circuit_breaker_threshold = self._get_int("AI_CIRCUIT_BREAKER_THRESHOLD", 5)
            self.ai_circuit_breaker_timeout = self._get_int("AI_CIRCUIT_BREAKER_TIMEOUT", 300)

            return True
            
        except Exception as e:
            logger.warning(f"Failed to load from environment: {e}")
            return False
    
    def _load_from_yaml(self) -> bool:
        """Load configuration from YAML file"""
        try:
            config_path = Path("config.yaml")
            if not config_path.exists():
                return False
            
            with open(config_path, 'r') as f:
                yaml_config = yaml.safe_load(f)
            
            if not yaml_config or 'ask_pipeline' not in yaml_config:
                return False
            
            config_data = yaml_config['ask_pipeline']
            
            # Apply YAML configuration
            for key, value in config_data.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            
            return True
            
        except Exception as e:
            logger.warning(f"Failed to load from YAML: {e}")
            return False
    
    def _load_defaults(self):
        """Load default configuration values"""
        # Set all default values
        self.enabled = True
        self.timeout = 30.0
        self.max_retries = 3
        self.quality_threshold = 0.75
        self.cache_enabled = True
        self.cache_ttl = 300
        self.parallel_execution = False
        self.section_timeout = 15.0
        self.quality_check_enabled = True
        self.fallback_enabled = True
        self.log_level = "INFO"
        self.enable_metrics = True
        self.enable_audit_trail = True
        self.enable_performance_tracking = True
        self.response_template_cache_enabled = True
        self.response_template_cache_ttl = 600
        self.response_max_length = 2000
        self.response_enable_markdown = True
        self.yahoo_finance_enabled = True
        self.polygon_enabled = True
        self.finnhub_enabled = True
        self.alpha_vantage_enabled = True
        self.yahoo_finance_rate_limit = 5
        self.polygon_rate_limit = 5
        self.finnhub_rate_limit = 30
        self.alpha_vantage_rate_limit = 5
        self.yahoo_finance_timeout = 10.0
        self.polygon_timeout = 8.0
        self.finnhub_timeout = 5.0
        self.alpha_vantage_timeout = 10.0
        self.enable_mock_data = False
        self.enable_debug_logging = False
        self.enable_performance_profiling = False
        self.enable_api_response_logging = False
        
        # Circuit Breaker defaults
        self.circuit_breaker_enabled = True
        self.circuit_breaker_threshold = 3
        self.circuit_breaker_cooldown = 60
        self.circuit_breaker_timeout = 30
        
        # OpenRouter AI defaults - use environment variables
        self.openrouter_enabled = True
        self.openrouter_api_key = ""
        self.openrouter_model = os.getenv("OPENROUTER_GENERAL_MODEL", "openrouter/sonoma-sky-alpha")
        self.openrouter_temperature = 0.7
        self.openrouter_max_tokens = 2000  # Increased for comprehensive responses
        self.openrouter_timeout = 30.0
        self.openrouter_base_url = "https://openrouter.ai/api/v1"

        # Model fallback defaults - use environment variables
        self.model_global_fallback = os.getenv("MODEL_GLOBAL_FALLBACK", "moonshotai/kimi-k2:free")
        self.llm_model = os.getenv("MODEL_LLM", os.getenv("MODEL_GLOBAL_FALLBACK", "moonshotai/kimi-k2:free"))
        self.model_quick = os.getenv("MODEL_QUICK", os.getenv("MODEL_GLOBAL_FALLBACK", "moonshotai/kimi-k2:free"))
        self.model_analysis = os.getenv("MODEL_ANALYSIS", os.getenv("MODEL_GLOBAL_FALLBACK", "moonshotai/kimi-k2:free"))
        self.model_heavy = os.getenv("MODEL_HEAVY", os.getenv("MODEL_GLOBAL_FALLBACK", "moonshotai/kimi-k2:free"))

        # AI Pipeline defaults - use environment variables
        self.ai_pipeline_timeout = float(os.getenv("ASK_PIPELINE_TIMEOUT", "30.0"))
        self.ai_pipeline_cache_ttl = int(os.getenv("ASK_PIPELINE_CACHE_TTL", "3600"))
        self.ai_pipeline_max_retries = int(os.getenv("ASK_PIPELINE_MAX_RETRIES", "3"))
        self.ai_pipeline_parallel_execution = os.getenv("ASK_PIPELINE_PARALLEL_EXECUTION", "false").lower() == "true"

        # AI Model Selection Preferences - use environment variables
        self.ai_prefer_cost_efficiency = os.getenv("AI_PREFER_COST_EFFICIENCY", "false").lower() == "true"
        self.ai_prefer_speed = os.getenv("AI_PREFER_SPEED", "false").lower() == "true"
        self.ai_prefer_accuracy = os.getenv("AI_PREFER_ACCURACY", "true").lower() == "true"
        self.ai_enable_fallback_chain = os.getenv("AI_ENABLE_FALLBACK_CHAIN", "true").lower() == "true"
        self.ai_max_fallback_attempts = int(os.getenv("AI_MAX_FALLBACK_ATTEMPTS", "3"))

        # Performance Tracking defaults - use environment variables
        self.ai_performance_tracking = os.getenv("AI_PERFORMANCE_TRACKING", "true").lower() == "true"
        self.ai_metrics_retention = int(os.getenv("AI_METRICS_RETENTION", "30"))
        self.ai_circuit_breaker_threshold = int(os.getenv("AI_CIRCUIT_BREAKER_THRESHOLD", "5"))
        self.ai_circuit_breaker_timeout = int(os.getenv("AI_CIRCUIT_BREAKER_TIMEOUT", "300"))
    
    def _validate_configuration(self):
        """Validate configuration values"""
        validation = ConfigValidation()
        
        # Validate critical settings
        if self.timeout <= 0:
            validation.add_error("Pipeline timeout must be positive")
        
        if self.max_retries < 0:
            validation.add_error("Max retries cannot be negative")
        
        if not 0.0 <= self.quality_threshold <= 1.0:
            validation.add_error("Quality threshold must be between 0.0 and 1.0")
        
        if self.cache_ttl <= 0:
            validation.add_error("Cache TTL must be positive")
        
        # Validate provider settings
        if not any([self.yahoo_finance_enabled, self.polygon_enabled, 
                   self.finnhub_enabled, self.alpha_vantage_enabled]):
            validation.add_warning("No market data providers enabled")
        
        # Validate rate limits
        for provider, rate_limit in [
            ("Yahoo Finance", self.yahoo_finance_rate_limit),
            ("Polygon", self.polygon_rate_limit),
            ("Finnhub", self.finnhub_rate_limit),
            ("Alpha Vantage", self.alpha_vantage_rate_limit)
        ]:
            if rate_limit <= 0:
                validation.add_error(f"{provider} rate limit must be positive")
        
        # Validate timeouts
        for provider, timeout in [
            ("Yahoo Finance", self.yahoo_finance_timeout),
            ("Polygon", self.polygon_timeout),
            ("Finnhub", self.finnhub_timeout),
            ("Alpha Vantage", self.alpha_vantage_timeout)
        ]:
            if timeout <= 0:
                validation.add_error(f"{provider} timeout must be positive")
        
        # Validate OpenRouter settings
        if self.openrouter_enabled and not self.openrouter_api_key:
            validation.add_warning("OpenRouter is enabled but no API key is set")
        
        if self.openrouter_temperature < 0 or self.openrouter_temperature > 2:
            validation.add_error("OpenRouter temperature must be between 0 and 2")
        
        if self.openrouter_max_tokens <= 0:
            validation.add_error("OpenRouter max tokens must be positive")
        
        if self.openrouter_timeout <= 0:
            validation.add_error("OpenRouter timeout must be positive")
        
        # Validate Circuit Breaker settings
        if self.circuit_breaker_enabled:
            if self.circuit_breaker_threshold <= 0:
                validation.add_error("Circuit breaker threshold must be positive")
            if self.circuit_breaker_cooldown <= 0:
                validation.add_error("Circuit breaker cooldown must be positive")
            if self.circuit_breaker_timeout <= 0:
                validation.add_error("Circuit breaker timeout must be positive")
        
        self._validation = validation
    
    def _get_from_central_config(self, section: str, key: str, default: Any) -> Any:
        """Get configuration value from centralized config manager"""
        from src.core.config_manager import get_config
        config = get_config()
        return config.get(section, key, default)
    
    def _get_string(self, key: str, default: str) -> str:
        """Get string environment variable with validation"""
        value = os.getenv(key, default)
        if not isinstance(value, str):
            logger.warning(f"Invalid string value for {key}: {value}, using default: {default}")
            return default
        return value
    
    def _get_int(self, key: str, default: int) -> int:
        """Get integer environment variable with validation"""
        try:
            value = os.getenv(key, str(default))
            int_value = int(value)
            if int_value < 0 and key.endswith(('_rate_limit', '_timeout', '_ttl')):
                logger.warning(f"Negative value for {key}: {int_value}, using default: {default}")
                return default
            return int_value
        except (ValueError, TypeError):
            logger.warning(f"Invalid integer value for {key}: {os.getenv(key)}, using default: {default}")
            return default
    
    def _get_float(self, key: str, default: float) -> float:
        """Get float environment variable with validation"""
        try:
            value = os.getenv(key, str(default))
            float_value = float(value)
            if float_value < 0:
                logger.warning(f"Negative value for {key}: {float_value}, using default: {default}")
                return default
            return float_value
        except (ValueError, TypeError):
            logger.warning(f"Invalid float value for {key}: {os.getenv(key)}, using default: {default}")
            return default
    
    def _get_bool(self, key: str, default: bool) -> bool:
        """Get boolean environment variable with validation"""
        value = os.getenv(key, str(default)).lower()
        if value in ("true", "1", "yes", "on"):
            return True
        elif value in ("false", "0", "no", "off"):
            return False
        else:
            logger.warning(f"Invalid boolean value for {key}: {os.getenv(key)}, using default: {default}")
            return default
    
    def get_section_config(self, section_name: str) -> Dict[str, Any]:
        """Get configuration for a specific pipeline section"""
        section_configs = {
            "query_analysis": {
                "max_retries": 2,
                "timeout": 10.0,
                "critical": True
            },
            "data_collection": {
                "max_retries": 3,
                "timeout": 15.0,
                "critical": True
            },
            "response_generation": {
                "max_retries": 2,
                "timeout": 20.0,
                "critical": True
            },
            "response_formatting": {
                "max_retries": 1,
                "timeout": 5.0,
                "critical": False
            }
        }
        
        return section_configs.get(section_name, {})
    
    def get_provider_config(self, provider_name: str) -> Dict[str, Any]:
        """Get configuration for a specific market data provider"""
        provider_configs = {
            "yahoo_finance": {
                "enabled": self.yahoo_finance_enabled,
                "rate_limit": self.yahoo_finance_rate_limit,
                "timeout": self.yahoo_finance_timeout
            },
            "polygon": {
                "enabled": self.polygon_enabled,
                "rate_limit": self.polygon_rate_limit,
                "timeout": self.polygon_timeout
            },
            "finnhub": {
                "enabled": self.finnhub_enabled,
                "rate_limit": self.finnhub_rate_limit,
                "timeout": self.finnhub_timeout
            },
            "alpha_vantage": {
                "enabled": self.alpha_vantage_enabled,
                "rate_limit": self.alpha_vantage_rate_limit,
                "timeout": self.alpha_vantage_timeout
            }
        }
        
        return provider_configs.get(provider_name, {})
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get configuration for AI/OpenRouter"""
        return {
            "enabled": self.openrouter_enabled,
            "model": self.openrouter_model,
            "temperature": self.openrouter_temperature,
            "max_tokens": self.openrouter_max_tokens,
            "timeout": self.openrouter_timeout,
            "base_url": self.openrouter_base_url,
            "has_api_key": bool(self.openrouter_api_key),
            "model_fallbacks": {
                "global": self.model_global_fallback,
                "llm": self.llm_model,
                "quick": self.model_quick,
                "analysis": self.model_analysis,
                "heavy": self.model_heavy
            }
        }
    
    def is_provider_enabled(self, provider_name: str) -> bool:
        """Check if a specific provider is enabled"""
        provider_config = self.get_provider_config(provider_name)
        return provider_config.get("enabled", False)
    
    def get_enabled_providers(self) -> List[str]:
        """Get list of enabled market data providers"""
        providers = ["yahoo_finance", "polygon", "finnhub", "alpha_vantage"]
        return [p for p in providers if self.is_provider_enabled(p)]
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get comprehensive configuration summary"""
        return {
            "source": self._config_source.value,
            "validation": self._validation.get_summary(),
            "pipeline": {
                "enabled": self.enabled,
                "timeout": self.timeout,
                "max_retries": self.max_retries,
                "quality_threshold": self.quality_threshold
            },
            "providers": {
                "enabled": self.get_enabled_providers(),
                "total": len(self.get_enabled_providers())
            },
            "performance": {
                "parallel_execution": self.parallel_execution,
                "cache_enabled": self.cache_enabled,
                "metrics_enabled": self.enable_metrics
            },
            "ai": self.get_ai_config()
        }
    
    def reload_configuration(self) -> bool:
        """Reload configuration from sources"""
        try:
            logger.info("🔄 Reloading configuration...")
            self._load_configuration()
            self._validate_configuration()
            
            if self._validation.is_valid:
                logger.info("✅ Configuration reloaded successfully")
                return True
            else:
                logger.error(f"❌ Configuration reload failed: {self._validation.get_summary()}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Configuration reload error: {e}")
            return False

    def to_dict(self) -> Dict[str, Any]:
        """Convert the config object to a dictionary."""
        return {
            # Pipeline settings
            'enabled': self.enabled,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'quality_threshold': self.quality_threshold,
            
            # Caching settings
            'cache_enabled': self.cache_enabled,
            'cache_ttl': self.cache_ttl,
            
            # Performance settings
            'parallel_execution': self.parallel_execution,
            'section_timeout': self.section_timeout,
            'quality_check_enabled': self.quality_check_enabled,
            'fallback_enabled': self.fallback_enabled,
            
            # Monitoring settings
            'log_level': self.log_level,
            'enable_metrics': self.enable_metrics,
            'enable_audit_trail': self.enable_audit_trail,
            'enable_performance_tracking': self.enable_performance_tracking,
            
            # Response settings
            'response_template_cache_enabled': self.response_template_cache_enabled,
            'response_template_cache_ttl': self.response_template_cache_ttl,
            'response_max_length': self.response_max_length,
            'response_enable_markdown': self.response_enable_markdown,
            
            # Market data providers
            'yahoo_finance_enabled': self.yahoo_finance_enabled,
            'polygon_enabled': self.polygon_enabled,
            'finnhub_enabled': self.finnhub_enabled,
            'alpha_vantage_enabled': self.alpha_vantage_enabled,
            
            # Rate limits
            'yahoo_finance_rate_limit': self.yahoo_finance_rate_limit,
            'polygon_rate_limit': self.polygon_rate_limit,
            'finnhub_rate_limit': self.finnhub_rate_limit,
            'alpha_vantage_rate_limit': self.alpha_vantage_rate_limit,
            
            # Timeouts
            'yahoo_finance_timeout': self.yahoo_finance_timeout,
            'polygon_timeout': self.polygon_timeout,
            'finnhub_timeout': self.finnhub_timeout,
            'alpha_vantage_timeout': self.alpha_vantage_timeout,
            
            # Development/testing
            'enable_mock_data': self.enable_mock_data,
            'enable_debug_logging': self.enable_debug_logging,
            'enable_performance_profiling': self.enable_performance_profiling,
            'enable_api_response_logging': self.enable_api_response_logging,
            
            # Circuit breaker
            'circuit_breaker_enabled': self.circuit_breaker_enabled,
            'circuit_breaker_threshold': self.circuit_breaker_threshold,
            'circuit_breaker_cooldown': self.circuit_breaker_cooldown,
            'circuit_breaker_timeout': self.circuit_breaker_timeout
        }


@lru_cache()
def get_config() -> AskPipelineConfig:
    """Get cached configuration instance"""
    return AskPipelineConfig()


# Global configuration instance
config = get_config()


def get_config_summary() -> Dict[str, Any]:
    """Get configuration summary for monitoring"""
    return config.get_config_summary()


def reload_config() -> bool:
    """Reload configuration (for runtime updates)"""
    return config.reload_configuration() 