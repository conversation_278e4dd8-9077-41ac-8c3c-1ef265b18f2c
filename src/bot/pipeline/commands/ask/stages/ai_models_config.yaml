# AI Models Configuration
# This file defines available AI models and their capabilities
# Environment variables can be used with ${VAR_NAME} syntax

# Default model configuration
defaults:
  temperature: ${AI_DEFAULT_TEMPERATURE:-0.7}
  max_tokens: ${AI_DEFAULT_MAX_TOKENS:-2000}
  timeout_ms: ${AI_DEFAULT_TIMEOUT_MS:-30000}
  cost_per_1k_tokens: ${AI_DEFAULT_COST:-0.001}
  accuracy_score: ${AI_DEFAULT_ACCURACY:-0.8}
  response_time_ms: ${AI_DEFAULT_RESPONSE_TIME:-2000}

# Model capabilities mapping
capabilities:
  basic_analysis: "BASIC_ANALYSIS"
  technical_analysis: "TECHNICAL_ANALYSIS"
  fundamental_analysis: "FUNDAMENTAL_ANALYSIS"
  sentiment_analysis: "SENTIMENT_ANALYSIS"
  complex_analysis: "COMPLEX_ANALYSIS"
  real_time_trading: "REAL_TIME_TRADING"
  educational: "EDUCATIONAL"
  risk_assessment: "RISK_ASSESSMENT"

# Model selection scoring weights
scoring_weights:
  accuracy: ${AI_SCORING_ACCURACY_WEIGHT:-0.4}
  cost_efficiency: ${AI_SCORING_COST_WEIGHT:-0.25}
  response_time: ${AI_SCORING_TIME_WEIGHT:-0.2}
  token_capacity: ${AI_SCORING_CAPACITY_WEIGHT:-0.1}
  user_preference: ${AI_SCORING_PREFERENCE_WEIGHT:-0.05}

# Complexity multipliers for token estimation
complexity_multipliers:
  simple: ${AI_COMPLEXITY_SIMPLE:-1.0}
  moderate: ${AI_COMPLEXITY_MODERATE:-1.5}
  complex: ${AI_COMPLEXITY_COMPLEX:-2.0}
  expert: ${AI_COMPLEXITY_EXPERT:-2.5}
  real_time: ${AI_COMPLEXITY_REAL_TIME:-3.0}

# Performance tracking settings
performance:
  enable_tracking: ${AI_PERFORMANCE_TRACKING:-true}
  metrics_retention_days: ${AI_METRICS_RETENTION:-30}
  circuit_breaker_threshold: ${AI_CIRCUIT_BREAKER_THRESHOLD:-5}
  circuit_breaker_timeout: ${AI_CIRCUIT_BREAKER_TIMEOUT:-300}
