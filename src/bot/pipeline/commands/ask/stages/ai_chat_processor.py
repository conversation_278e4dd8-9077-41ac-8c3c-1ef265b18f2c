"""
AI Chat Processor - DEPRECATED

This module is deprecated. Import from src.shared.ai_services.ai_chat_processor instead.

This is a backward compatibility module that imports from the canonical location.
"""

import warnings
import logging
import inspect
import os
import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Tuple

# Import from canonical location
from src.shared.ai_services.ai_chat_processor import (
    AIChatProcessor as CanonicalAIChatProcessor,
    AIAskResult,
    create_processor,
    process_query,
    SYSTEM_PROMPT,
    FALLBACK_RESPONSES
)

# Import logger from unified logging system
from src.core.logger import get_logger
logger = get_logger(__name__)

# Import deprecation monitoring
from src.core.deprecation_monitor import record_deprecation

# Get caller information
caller_frame = inspect.currentframe()
if caller_frame and caller_frame.f_back:
    caller_info = f"{caller_frame.f_back.f_code.co_filename}:{caller_frame.f_back.f_lineno}"
else:
    caller_info = "unknown"

# Record deprecation usage
record_deprecation("src.bot.pipeline.commands.ask.stages.ai_chat_processor", caller_info)

# Show deprecation warning
warnings.warn(
    "This module is deprecated. Import from src.shared.ai_services.ai_chat_processor instead.",
    DeprecationWarning,
    stacklevel=2
)

# Define response style enum for backward compatibility
class ResponseStyle:
    DETAILED = "detailed"
    SIMPLE = "simple"

# Define template engine class for backward compatibility
class ResponseTemplateEngine:
    def generate_response(self, template_type, style, data, query_analysis):
        return f"Analysis for {data.get('symbol', 'stock')}: ${data.get('price', 'N/A')}"
    
    def generate_market_overview_response(self, market_data):
        return f"Market overview with {len(market_data.get('indices', []))} indices"

# Define feature flags for backward compatibility
TEMPLATE_ENGINE_AVAILABLE = True
ENHANCED_TECHNICAL_ANALYSIS_AVAILABLE = False
ENHANCED_MARKET_CONTEXT_AVAILABLE = False
ENHANCED_AI_CLIENT_AVAILABLE = False
ENHANCED_RESPONSE_GENERATOR_AVAILABLE = False
ENHANCED_CACHE_MANAGER_AVAILABLE = False
ENHANCED_PREPROCESSING_AVAILABLE = False
PIPELINE_CONFIG_AVAILABLE = False
CACHE_AVAILABLE = False
MARKET_HOURS_AVAILABLE = False
AI_ROUTING_AVAILABLE = False
CONVERSATION_MEMORY_AVAILABLE = False

# Import models for backward compatibility
try:
    from .models import AIAskResult as LocalAIAskResult
except ImportError:
    LocalAIAskResult = AIAskResult

# Backward compatibility class
class AIChatProcessor(CanonicalAIChatProcessor):
    """Lightweight AI chat processor used by the pipeline and unit tests.

    It wraps an AI client and market data service and exposes a simple
    `process` coroutine that returns a dict matching test expectations.
    """

    def __init__(self, context: Any = None):
        super().__init__(context=context)
        self.context = context
        self.logger = logger

    async def process(self, query: str) -> Dict[str, Any]:
        """Process a user query via AI client and optional data fetching.

        Returns a dict with at least: response, data, intent, symbols, needs_data
        """
        # If no AI client configured, return a harmless fallback response
        if not self.client:
            return {
                'response': 'Fallback response: AI client not configured',
                'data': {},
                'intent': 'general_question',
                'symbols': [],
                'needs_data': False
            }

        # Use the canonical implementation
        return await super().process(query)

# Backward compatibility function
async def processor(context: Any, results: Dict[str, Any]) -> Dict[str, Any]:
    """Async helper used by tests: instantiate AIChatProcessor and run `process`.

    If no original query is present, return an error dict as tests expect.
    """
    # Ensure original query exists
    if not getattr(context, 'original_query', None):
        return {'error': True, 'response': 'Please provide a question to analyze.'}

    proc = AIChatProcessor(context=context)
    # Allow tests to patch attributes on AIChatProcessor after instantiation
    # If results already contain a 'processor' instance, tests will patch the class
    return await proc.process(context.original_query)
