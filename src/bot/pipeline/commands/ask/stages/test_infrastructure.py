"""
Test file to verify the pipeline infrastructure is working correctly.

This is a temporary file to ensure our base classes and configuration
are properly set up before we begin the extraction process.
"""

import asyncio
from typing import Dict, Any

from src.bot.pipeline.commands.ask.stages.config import PipelineConfig, PipelineMode, get_config, update_config
from src.bot.pipeline.commands.ask.stages.core.base import <PERSON>ingContext, ProcessingResult, ProcessingStage
from src.bot.pipeline.commands.ask.stages.core.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorType, ErrorSeverity


class TestPipelineInfrastructure:
    """Test class for pipeline infrastructure components."""
    
    def test_pipeline_config_creation(self):
        """Test that pipeline configuration can be created."""
        config = PipelineConfig()
        assert config.mode == PipelineMode.HYBRID
        assert config.use_new_preprocessor is True
        assert config.use_new_core is True
        assert config.use_new_postprocessor is True
        assert config.use_new_utils is True
    
    def test_pipeline_config_modes(self):
        """Test different pipeline modes."""
        # Test legacy mode
        config = PipelineConfig(mode=PipelineMode.LEGACY)
        assert config.is_legacy_mode() is True
        assert config.is_hybrid_mode() is False
        assert config.is_modular_mode() is False
        
        # Test hybrid mode
        config = PipelineConfig(mode=PipelineMode.HYBRID)
        assert config.is_hybrid_mode() is True
        
        # Test modular mode
        config = PipelineConfig(mode=PipelineMode.MODULAR)
        assert config.is_modular_mode() is True
    
    def test_feature_flags(self):
        """Test feature flag functionality."""
        config = PipelineConfig()
        
        # Test component usage
        assert config.should_use_new_component("preprocessor") is True
        assert config.should_use_new_component("core") is True
        
        # Test legacy mode
        config.mode = PipelineMode.LEGACY
        assert config.should_use_new_component("preprocessor") is False
        
        # Test modular mode
        config.mode = PipelineMode.MODULAR
        assert config.should_use_new_component("preprocessor") is True
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Test valid config
        config = PipelineConfig()
        assert config.cache_ttl == 300
        
        # Test invalid config (should raise ValueError)
        try:
            PipelineConfig(cache_ttl=-1)
            assert False, "Should have raised ValueError"
        except ValueError:
            pass  # Expected
    
    def test_processing_context(self):
        """Test processing context creation."""
        context = ProcessingContext(
            user_id="test_user",
            query="test query",
            stage=ProcessingStage.PREPROCESSING,
            metadata={"test": "data"},
            start_time=1234567890.0
        )
        
        assert context.user_id == "test_user"
        assert context.query == "test query"
        assert context.stage == ProcessingStage.PREPROCESSING
        assert context.metadata["test"] == "data"
        assert context.start_time == 1234567890.0
        assert context.stage_data == {}
    
    def test_processing_result(self):
        """Test processing result creation."""
        result = ProcessingResult(
            success=True,
            data={"test": "data"},
            metadata={"test_meta": "value"}
        )
        
        assert result.success is True
        assert result.data["test"] == "data"
        assert result.error is None
        assert result.metadata["test_meta"] == "value"
    
    def test_error_handler_creation(self):
        """Test error handler creation."""
        handler = ErrorHandler()
        assert handler is not None
        assert len(handler.error_counts) == len(ErrorType)
    
    def test_error_classification(self):
        """Test error classification."""
        handler = ErrorHandler()
        
        # Test validation error
        error = ValueError("Invalid input")
        error_type = handler._classify_error(error)
        assert error_type == ErrorType.VALIDATION_ERROR
        
        # Test unknown error
        class CustomError(Exception):
            pass
        
        error = CustomError("Custom error")
        error_type = handler._classify_error(error)
        assert error_type == ErrorType.UNKNOWN_ERROR
    
    def test_error_severity_determination(self):
        """Test error severity determination."""
        handler = ErrorHandler()
        
        # Test validation error severity
        severity = handler._determine_severity(ValueError("test"), ErrorType.VALIDATION_ERROR)
        assert severity == ErrorSeverity.LOW
        
        # Test AI service error severity
        severity = handler._determine_severity(Exception("AI error"), ErrorType.AI_SERVICE_ERROR)
        assert severity == ErrorSeverity.HIGH
    
    def test_config_environment_override(self):
        """Test environment variable configuration override."""
        import os
        
        # Set environment variable
        os.environ["PIPELINE_MODE"] = "legacy"
        os.environ["USE_NEW_PREPROCESSOR"] = "false"
        
        # Create new config (should pick up environment variables)
        config = PipelineConfig()
        assert config.mode == PipelineMode.LEGACY
        assert config.use_new_preprocessor is False
        
        # Clean up
        del os.environ["PIPELINE_MODE"]
        del os.environ["USE_NEW_PREPROCESSOR"]
    
    def test_config_serialization(self):
        """Test configuration serialization to/from dictionary."""
        config = PipelineConfig(
            mode=PipelineMode.MODULAR,
            use_new_preprocessor=False,
            cache_ttl=600
        )
        
        # Convert to dictionary
        config_dict = config.to_dict()
        assert config_dict["mode"] == "modular"
        assert config_dict["feature_flags"]["use_new_preprocessor"] is False
        assert config_dict["performance"]["caching"]["ttl"] == 600
        
        # Create from dictionary
        new_config = PipelineConfig.from_dict(config_dict)
        assert new_config.mode == PipelineMode.MODULAR
        assert new_config.use_new_preprocessor is False
        assert new_config.cache_ttl == 600


if __name__ == "__main__":
    # Run tests
    test_instance = TestPipelineInfrastructure()
    
    # Run all test methods
    test_methods = [method for method in dir(test_instance) if method.startswith('test_')]
    
    print(f"Running {len(test_methods)} infrastructure tests...")
    
    for method_name in test_methods:
        try:
            method = getattr(test_instance, method_name)
            method()
            print(f"✅ {method_name} - PASSED")
        except Exception as e:
            print(f"❌ {method_name} - FAILED: {e}")
    
    print("\nInfrastructure test completed!") 