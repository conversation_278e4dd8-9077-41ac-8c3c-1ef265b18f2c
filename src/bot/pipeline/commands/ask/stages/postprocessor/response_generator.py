"""
Enhanced Response Generator for the /ask pipeline.

This module extracts the response generation logic from ai_chat_processor.py
including template engine integration, data validation, and safe fallback responses.
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from ..config import get_config

logger = logging.getLogger(__name__)


class ResponseGenerator:
    """Enhanced response generator with template engine integration and safety features."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or get_config()
        self._enhancements_enabled = getattr(self.config, 'enable_response_generator_enhancements', True)
        
        # Performance tracking
        self.total_responses_generated = 0
        self.template_engine_successes = 0
        self.fallback_responses_used = 0
        
        # Initialize template engine if available
        self.template_engine = self._initialize_template_engine()
    
    def _initialize_template_engine(self):
        """Initialize the response template engine if available."""
        try:
            # Try to import the template engine
            from ..response_templates import ResponseTemplateEngine
            logger.info("Response template engine initialized successfully")
            return ResponseTemplateEngine()
        except ImportError:
            logger.warning("Response template engine not available - will use fallback generation")
            return None
    
    def generate_final_response(self, initial_response: str, data: Dict[str, Any], 
                              pipeline_id: str = None) -> str:
        """
        Generate final response using enhanced template engine and validation.
        
        This replaces the monolithic _generate_final_response method from ai_chat_processor.py
        
        Args:
            initial_response: Initial AI response
            data: Stock/market data for response generation
            pipeline_id: Optional pipeline ID for logging
            
        Returns:
            Formatted final response
        """
        start_time = time.time()
        self.total_responses_generated += 1
        
        try:
            if not data:
                logger.warning(f"⚠️ [{pipeline_id}] No data provided for response generation")
                return initial_response
            
            # Use enhanced template engine if available and enabled
            if self._enhancements_enabled and self.template_engine:
                return self._generate_enhanced_response(initial_response, data, pipeline_id)
            else:
                return self._generate_legacy_response(initial_response, data, pipeline_id)
                
        except Exception as e:
            logger.error(f"❌ [{pipeline_id}] Response generation failed: {e}")
            # Return safe fallback
            return self._generate_safe_fallback_response(data, initial_response, pipeline_id)
    
    def _generate_enhanced_response(self, initial_response: str, data: Dict[str, Any], 
                                  pipeline_id: str) -> str:
        """Generate response using enhanced template engine."""
        try:
            # Determine template type and style based on data
            template_type, style = self._determine_template_config(data)
            
            # Prepare enhanced data for template processing
            enhanced_data = self._prepare_enhanced_data(data)
            
            # Generate response using template engine
            formatted_response = self.template_engine.generate_response(
                template_type,
                style,
                enhanced_data,
                {}  # query_analysis is empty for now
            )
            
            # Track success
            self.template_engine_successes += 1
            
            logger.info(f"✅ [{pipeline_id}] Generated enhanced template response")
            return formatted_response
            
        except Exception as e:
            logger.error(f"❌ [{pipeline_id}] Enhanced response generation failed: {e}")
            # Fall back to legacy method
            return self._generate_legacy_response(initial_response, data, pipeline_id)
    
    def _determine_template_config(self, data: Dict[str, Any]) -> tuple[str, str]:
        """Determine appropriate template type and style based on data."""
        # Determine template type
        if len(data) > 1:
            template_type = "market_overview"
        else:
            template_type = "stock_analysis"
        
        # Determine style (could be made configurable)
        style = "DETAILED"  # Default to detailed style
        
        return template_type, style
    
    def _prepare_enhanced_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare enhanced data structure for template processing."""
        if len(data) == 1:
            # Single symbol data
            symbol = list(data.keys())[0]
            stock_data = data[symbol]
            
            return {
                'symbol': symbol,
                'price': stock_data.get('current_price'),
                'current_price': stock_data.get('current_price'),
                'change': stock_data.get('change_percent'),
                'change_percent': stock_data.get('change_percent'),
                'volume': stock_data.get('volume'),
                'market_cap': stock_data.get('market_cap'),
                'timestamp': stock_data.get('timestamp', datetime.now().isoformat()),
                'data_quality': stock_data.get('data_quality', 50),
                'data': data  # Include all original data for freshness validation
            }
        else:
            # Multiple symbols data (market overview)
            indices_summary = []
            for symbol, stock_data in data.items():
                price = stock_data.get('current_price', stock_data.get('price', 0))
                change = stock_data.get('change_percent', stock_data.get('change', 0))
                change_sign = "+" if change >= 0 else "-"
                indices_summary.append(f"• **{symbol}**: ${price} ({change_sign}{abs(change)}%)")
            
            return {
                'indices_summary': "\n".join(indices_summary),
                'overall_sentiment': self._calculate_overall_sentiment(data),
                'market_trend': self._analyze_market_trend(data),
                'timestamp': datetime.now().isoformat(),
                'data_quality': self._calculate_data_quality(data),
                'data': data  # Include all original data for freshness validation
            }
    
    def _generate_legacy_response(self, initial_response: str, data: Dict[str, Any], 
                                pipeline_id: str) -> str:
        """Generate response using legacy logic (fallback)."""
        try:
            # Use template engine if available
            if self.template_engine:
                template_type, style = self._determine_template_config(data)
                enhanced_data = self._prepare_enhanced_data(data)
                
                formatted_response = self.template_engine.generate_response(
                    template_type,
                    style,
                    enhanced_data,
                    {}
                )
                
                logger.info(f"✅ [{pipeline_id}] Generated legacy template response")
                return formatted_response
                
            else:
                # No template engine available, use basic formatting
                logger.warning(f"⚠️ [{pipeline_id}] No template engine available, using basic response")
                return self._generate_basic_response(data, initial_response)
                
        except Exception as e:
            logger.error(f"❌ [{pipeline_id}] Legacy response generation failed: {e}")
            return self._generate_safe_fallback_response(data, initial_response, pipeline_id)
    
    def _generate_basic_response(self, data: Dict[str, Any], initial_response: str) -> str:
        """Generate basic response without template engine."""
        if not data:
            return initial_response
        
        # For single symbol
        if len(data) == 1:
            symbol = list(data.keys())[0]
            stock_data = data[symbol]
            
            current_price = stock_data.get('current_price', stock_data.get('price', 'N/A'))
            change = stock_data.get('change_percent', stock_data.get('change', 'N/A'))
            
            return f"**{symbol} Analysis**\n\n{initial_response}\n\n**Current Price**: ${current_price}\n**Change**: {change}%"
        
        # For multiple symbols
        summary = []
        for symbol, stock_data in data.items():
            price = stock_data.get('current_price', stock_data.get('price', 'N/A'))
            change = stock_data.get('change_percent', stock_data.get('change', 'N/A'))
            summary.append(f"• **{symbol}**: ${price} ({change}%)")
        
        return f"**Market Overview**\n\n{initial_response}\n\n**Summary**:\n" + "\n".join(summary)
    
    def _generate_safe_fallback_response(self, data: Dict[str, Any], initial_response: str, 
                                       pipeline_id: str = None) -> str:
        """
        Generate a safe fallback response that doesn't make false recommendations.
        
        This replaces the monolithic _generate_safe_fallback_response method from ai_chat_processor.py
        """
        self.fallback_responses_used += 1
        
        if not data:
            logger.warning(f"⚠️ [{pipeline_id}] No data available for fallback response")
            return "I'm unable to provide analysis at the moment. Please try again later."
        
        # For single symbol
        if len(data) == 1:
            symbol = list(data.keys())[0]
            stock_data = data[symbol]
            
            # Check if data is available and fresh
            if not stock_data.get('data_available', True):
                return (f"**{symbol} Analysis - Data Unavailable**\n\n"
                       f"I'm unable to fetch real-time market data for {symbol} at the moment.\n\n"
                       f"**⚠️ DISCLAIMER**: This is educational content, not financial advice.")
            
            # Check data freshness
            timestamp = stock_data.get('timestamp')
            if timestamp:
                try:
                    data_time = datetime.fromisoformat(str(timestamp).replace('Z', '+00:00'))
                    age_minutes = (datetime.now() - data_time).total_seconds() / 60
                    
                    if age_minutes > 15:
                        return (f"**{symbol} Analysis - Data Too Old**\n\n"
                               f"⚠️ **WARNING**: Market data is {int(age_minutes)} minutes old and may be unreliable.\n\n"
                               f"**DO NOT** use this data for trading decisions.\n\n"
                               f"**⚠️ DISCLAIMER**: This is educational content, not financial advice.")
                except:
                    pass
            
            # Generate safe response without specific recommendations
            current_price = stock_data.get('current_price', stock_data.get('price', 'N/A'))
            change = stock_data.get('change_percent', stock_data.get('change', 'N/A'))
            
            return (f"**{symbol} Analysis**\n\n"
                   f"**Current Price**: ${current_price}\n"
                   f"**Change**: {change}%\n\n"
                   f"**⚠️ DISCLAIMER**: This is educational content, not financial advice.\n"
                   f"Always do your own research before making investment decisions.")
        
        # For multiple symbols
        return (f"**Market Overview**\n\n"
               f"I'm unable to provide detailed analysis at the moment.\n\n"
               f"**⚠️ DISCLAIMER**: This is educational content, not financial advice.\n"
               f"Always do your own research before making investment decisions.")
    
    def _calculate_overall_sentiment(self, data: Dict[str, Any]) -> str:
        """Calculate overall market sentiment from multiple symbols."""
        if not data:
            return "neutral"
        
        positive_count = 0
        total_count = 0
        
        for symbol_data in data.values():
            change = symbol_data.get('change_percent', symbol_data.get('change', 0))
            if isinstance(change, (int, float)):
                if change > 0:
                    positive_count += 1
                total_count += 1
        
        if total_count == 0:
            return "neutral"
        
        positive_ratio = positive_count / total_count
        
        if positive_ratio >= 0.7:
            return "bullish"
        elif positive_ratio <= 0.3:
            return "bearish"
        else:
            return "mixed"
    
    def _analyze_market_trend(self, data: Dict[str, Any]) -> str:
        """Analyze overall market trend from multiple symbols."""
        if not data:
            return "sideways"
        
        total_change = 0
        valid_count = 0
        
        for symbol_data in data.values():
            change = symbol_data.get('change_percent', symbol_data.get('change', 0))
            if isinstance(change, (int, float)):
                total_change += change
                valid_count += 1
        
        if valid_count == 0:
            return "sideways"
        
        avg_change = total_change / valid_count
        
        if avg_change > 1.0:
            return "strongly_up"
        elif avg_change > 0.5:
            return "up"
        elif avg_change < -1.0:
            return "strongly_down"
        elif avg_change < -0.5:
            return "down"
        else:
            return "sideways"
    
    def _calculate_data_quality(self, data: Dict[str, Any]) -> int:
        """Calculate overall data quality score."""
        if not data:
            return 0
        
        total_quality = 0
        valid_count = 0
        
        for symbol_data in data.values():
            quality = symbol_data.get('data_quality', 50)
            if isinstance(quality, (int, float)):
                total_quality += quality
                valid_count += 1
        
        if valid_count == 0:
            return 0
        
        return int(total_quality / valid_count)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring."""
        return {
            'total_responses_generated': self.total_responses_generated,
            'template_engine_successes': self.template_engine_successes,
            'fallback_responses_used': self.fallback_responses_used,
            'enhancements_enabled': self._enhancements_enabled,
            'template_engine_available': self.template_engine is not None,
            'success_rate': (self.template_engine_successes / max(1, self.total_responses_generated)) * 100
        }
    
    def toggle_enhancements(self, enable: bool) -> None:
        """Toggle enhancement features on/off."""
        self._enhancements_enabled = enable
        logger.info(f"Response generator enhancements {'enabled' if enable else 'disabled'}")
    
    def reset_metrics(self) -> None:
        """Reset performance metrics."""
        self.total_responses_generated = 0
        self.template_engine_successes = 0
        self.fallback_responses_used = 0
        logger.info("Response generator metrics reset")


# Global instance for easy access
response_generator = ResponseGenerator() 