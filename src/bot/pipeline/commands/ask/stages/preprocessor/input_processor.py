"""
Input Processor for the /ask pipeline.

This module integrates our enhanced SymbolValidator with the main pipeline
to replace the monolithic validation logic.
"""

import logging
from typing import Tuple, List, Dict, Any
from ..symbol_validator import SymbolValidator
from ..config import get_config

logger = logging.getLogger(__name__)


class InputProcessor:
    """Processes and validates user input using enhanced modules."""
    
    def __init__(self):
        self.config = get_config()
        self.symbol_validator = SymbolValidator()
        self._enhancements_enabled = self.config.enable_symbol_validator_enhancements
    
    def validate_and_sanitize_query(self, query: str) -> Tuple[str, List[str]]:
        """
        Validate and sanitize user query using enhanced SymbolValidator.
        
        This replaces the monolithic _validateAndSanitizeQuery method from ai_chat_processor.py
        
        Args:
            query: User input query
            
        Returns:
            Tuple of (sanitized_query, symbols_list)
            
        Raises:
            ValueError: If query validation fails
        """
        try:
            # Use enhanced validation if enabled
            if self._enhancements_enabled:
                return self._enhanced_validation(query)
            else:
                return self._legacy_validation(query)
                
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            raise ValueError(f"Input validation failed: {e}")
    
    def _enhanced_validation(self, query: str) -> Tuple[str, List[str]]:
        """Use enhanced SymbolValidator for validation."""
        # Basic validation
        if not query or not isinstance(query, str):
            raise ValueError("Query must be a non-empty string")
        
        if len(query) < 3:
            raise ValueError("Query too short (min 3 characters)")
        
        # Use enhanced validation
        enhanced_result = self.symbol_validator.enhanced_validation(
            query, 
            enable_preprocessing=True
        )
        
        if not enhanced_result["success"]:
            raise ValueError(f"Enhanced validation failed: {enhanced_result.get('error', 'Unknown error')}")
        
        # Extract symbols from enhanced result
        suggestions = enhanced_result["suggestions"]
        symbols = [s.text for s in suggestions]
        
        # Apply additional validation rules
        if len(symbols) > 10:
            raise ValueError("Too many symbols in query (max 10)")
        
        # Return sanitized query and symbols
        sanitized_query = enhanced_result.get("sanitized_query", query)
        return sanitized_query, symbols
    
    def _legacy_validation(self, query: str) -> Tuple[str, List[str]]:
        """Fallback to legacy validation logic."""
        # Basic validation
        if not query or not isinstance(query, str):
            raise ValueError("Query must be a non-empty string")
        
        # Remove potential dangerous characters
        sanitized_query = query.strip()
        
        # Length validation
        if len(sanitized_query) > 1000:
            raise ValueError("Query too long (max 1000 characters)")
        
        if len(sanitized_query) < 3:
            raise ValueError("Query too short (min 3 characters)")
        
        # Check for potentially dangerous patterns
        dangerous_patterns = [
            r'<script', r'javascript:', r'data:text/html', r'vbscript:',
            r'<iframe', r'<object', r'<embed', r'<form', r'<input',
            r'<textarea', r'<select', r'<button', r'<link', r'<meta',
            r'<style', r'<title', r'<base', r'<bgsound', r'<marquee',
            r'<applet', r'<xmp', r'<plaintext', r'<listing', r'<nobr',
            r'<wbr', r'<noframes', r'<noscript', r'<noembed', r'<nolegend',
            r'<nolayer', r'<nosmartquotes'
        ]
        
        import re
        for pattern in dangerous_patterns:
            if re.search(pattern, sanitized_query, re.IGNORECASE):
                raise ValueError(f"Query contains potentially dangerous content: {pattern}")
        
        # Extract symbols using legacy method
        symbols = self._extract_symbols_legacy(sanitized_query)
        
        # Validate symbols (max 10 symbols per query)
        if len(symbols) > 10:
            raise ValueError("Too many symbols in query (max 10)")
        
        # Validate individual symbols
        for symbol in symbols:
            if not re.match(r'^[A-Z]{1,5}$', symbol):
                raise ValueError(f"Invalid symbol format: {symbol}")
        
        return sanitized_query, symbols
    
    def _extract_symbols_legacy(self, text: str) -> List[str]:
        """Legacy symbol extraction method."""
        import re
        # Look for $ followed by uppercase letters
        symbol_pattern = r'\$([A-Z]{1,5})'
        matches = re.findall(symbol_pattern, text)
        
        # Remove duplicates and validate
        valid_symbols = []
        for symbol in set(matches):
            if re.match(r'^[A-Z]{1,5}$', symbol):
                valid_symbols.append(symbol)
        
        return valid_symbols
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """Get statistics about validation performance."""
        return {
            "enhancements_enabled": self._enhancements_enabled,
            "symbol_validator_enhanced": self.symbol_validator.is_enhanced_mode(),
            "config": {
                "enable_symbol_validator_enhancements": self.config.enable_symbol_validator_enhancements
            }
        }
    
    def toggle_enhancements(self, enable: bool) -> None:
        """Toggle enhancement features on/off."""
        self._enhancements_enabled = enable
        logger.info(f"Input processor enhancements {'enabled' if enable else 'disabled'}")


# Global instance for easy access
input_processor = InputProcessor() 