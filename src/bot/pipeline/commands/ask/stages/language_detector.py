"""
Language Detection Module for Ask Pipeline

This module provides multi-language detection capabilities for the /ask command,
allowing the bot to identify the language of user queries and respond appropriately.
"""

import logging
from typing import Dict, List, Optional, Tuple
from langdetect import detect, detect_langs, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException

# Set seed for consistent results
DetectorFactory.seed = 0

logger = logging.getLogger(__name__)

# Language codes mapping
LANGUAGE_CODES = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'zh-cn': 'Chinese (Simplified)',
    'zh-tw': 'Chinese (Traditional)',
    'ja': 'Japanese',
    'ko': 'Korean',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'bn': 'Bengali',
    'ur': 'Urdu',
    'fa': 'Persian',
    'tr': 'Turkish',
    'nl': 'Dutch',
    'pl': 'Polish',
    'uk': 'Ukrainian',
    'ro': 'Romanian',
    'cs': 'Czech',
    'hu': 'Hungarian',
    'sv': 'Swedish',
    'da': 'Danish',
    'fi': 'Finnish',
    'no': 'Norwegian',
    'el': 'Greek',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Tagalog',
    'sw': 'Swahili'
}

# Supported languages for trading analysis
SUPPORTED_LANGUAGES = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'zh-cn': 'Chinese (Simplified)',
    'ja': 'Japanese',
    'ko': 'Korean'
}

class LanguageDetector:
    """
    Detects the language of user queries and provides language information
    for the ask pipeline.
    """
    
    def __init__(self):
        """Initialize the language detector."""
        self.supported_languages = SUPPORTED_LANGUAGES
        self.language_codes = LANGUAGE_CODES
    
    def detect_language(self, text: str) -> Dict[str, any]:
        """
        Detect the language of a given text.
        
        Args:
            text: The text to analyze
            
        Returns:
            Dictionary with language detection results
        """
        try:
            # Detect primary language
            primary_lang = detect(text)
            
            # Get probability distribution
            lang_probs = detect_langs(text)
            
            # Convert to list of dictionaries
            probabilities = []
            for lang in lang_probs:
                probabilities.append({
                    'language_code': lang.lang,
                    'language_name': self.language_codes.get(lang.lang, lang.lang),
                    'probability': lang.prob
                })
            
            # Check if language is supported
            is_supported = primary_lang in self.supported_languages
            
            return {
                'detected_language': primary_lang,
                'language_name': self.language_codes.get(primary_lang, primary_lang),
                'is_supported': is_supported,
                'supported_name': self.supported_languages.get(primary_lang, 'Unsupported'),
                'confidence': probabilities[0]['probability'] if probabilities else 0.0,
                'all_probabilities': probabilities,
                'success': True
            }
            
        except LangDetectException as e:
            logger.warning(f"Language detection failed: {e}")
            return {
                'detected_language': 'unknown',
                'language_name': 'Unknown',
                'is_supported': False,
                'supported_name': 'Unsupported',
                'confidence': 0.0,
                'all_probabilities': [],
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Unexpected error in language detection: {e}")
            return {
                'detected_language': 'unknown',
                'language_name': 'Unknown',
                'is_supported': False,
                'supported_name': 'Unsupported',
                'confidence': 0.0,
                'all_probabilities': [],
                'success': False,
                'error': str(e)
            }
    
    def detect_multiple_languages(self, texts: List[str]) -> List[Dict[str, any]]:
        """
        Detect languages for multiple texts.
        
        Args:
            texts: List of texts to analyze
            
        Returns:
            List of language detection results
        """
        results = []
        for text in texts:
            results.append(self.detect_language(text))
        return results
    
    def get_supported_languages(self) -> Dict[str, str]:
        """
        Get list of supported languages.
        
        Returns:
            Dictionary of supported languages
        """
        return self.supported_languages.copy()
    
    def is_language_supported(self, language_code: str) -> bool:
        """
        Check if a language is supported.
        
        Args:
            language_code: Language code to check
            
        Returns:
            True if supported, False otherwise
        """
        return language_code in self.supported_languages
    
    def get_language_response_template(self, language_code: str) -> str:
        """
        Get appropriate response template based on language.
        
        Args:
            language_code: Language code
            
        Returns:
            Response template
        """
        templates = {
            'en': "I've detected your query is in English. I'll provide a response in English.",
            'es': "He detectado que su consulta está en español. Proporcionaré una respuesta en español.",
            'fr': "J'ai détecté que votre requête est en français. Je fournirai une réponse en français.",
            'de': "Ich habe festgestellt, dass Ihre Anfrage auf Deutsch ist. Ich werde eine Antwort auf Deutsch geben.",
            'it': "Ho rilevato che la tua richiesta è in italiano. Fornirò una risposta in italiano.",
            'pt': "Detectei que sua consulta está em português. Vou fornecer uma resposta em português.",
            'ru': "Я обнаружил, что ваш запрос на русском языке. Я дам ответ на русском языке.",
            'zh-cn': "我检测到您的查询是中文。我将提供中文回复。",
            'ja': "あなたのクエリが日本語であることを検出しました。日本語で回答します。",
            'ko': "귀하의 쿼리가 한국어임을 감지했습니다. 한국어로 답변하겠습니다."
        }
        
        return templates.get(language_code, templates['en'])

# Global language detector instance
language_detector = LanguageDetector()

def detect_query_language(query: str) -> Dict[str, any]:
    """
    Convenience function to detect the language of a query.
    
    Args:
        query: The query to analyze
        
    Returns:
        Language detection results
    """
    return language_detector.detect_language(query)

def add_language_context_to_prompt(prompt: str, language_info: Dict[str, any]) -> str:
    """
    Add language context to a prompt based on detected language.
    
    Args:
        prompt: Original prompt
        language_info: Language detection results
        
    Returns:
        Prompt with language context added
    """
    if not language_info.get('success', False):
        return prompt
    
    detected_lang = language_info.get('detected_language', 'en')
    is_supported = language_info.get('is_supported', False)
    
    # If language is supported and not English, add context
    if is_supported and detected_lang != 'en':
        language_name = language_info.get('language_name', 'Unknown')
        context = f"[Language: {language_name}] "
        return context + prompt
    
    return prompt