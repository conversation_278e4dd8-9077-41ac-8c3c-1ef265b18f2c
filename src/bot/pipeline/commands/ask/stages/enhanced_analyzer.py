"""
Enhanced Query Analyzer

This module combines context understanding and advanced classification to provide
comprehensive query analysis for the AI system.
"""

import logging
from typing import Dict, Any, Optional, List
import asyncio

from .enhanced_context import enhanced_context, EnhancedContextUnderstanding
from .advanced_classifier import advanced_classifier, AdvancedQueryClassifier, QueryClassification
from ..stages.query_analyzer import AIQueryAnalyzer, QueryAnalysis

logger = logging.getLogger(__name__)

class EnhancedQueryAnalyzer:
    """Enhanced query analyzer combining context understanding and advanced classification"""
    
    def __init__(self):
        self.context_understanding = enhanced_context
        self.advanced_classifier = advanced_classifier
        self.base_analyzer = AIQueryAnalyzer()
    
    async def analyze_query_enhanced(
        self, 
        query: str, 
        user_id: str,
        guild_id: Optional[str] = None,
        conversation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Perform enhanced query analysis combining context and classification
        
        Args:
            query: User query
            user_id: Discord user ID
            guild_id: Discord guild ID
            conversation_id: Conversation ID for tracking history
            
        Returns:
            Enhanced analysis results
        """
        try:
            # Build enhanced context
            context = await self.context_understanding.build_enhanced_context(
                user_id, query, guild_id, conversation_id
            )
            
            # Perform advanced classification
            classification = await self.advanced_classifier.classify_query(query, context)
            
            # Get base analysis
            base_analysis = await self.base_analyzer.anhanced_query_analysis(query, context)
            
            # Combine results
            enhanced_analysis = {
                "base_analysis": base_analysis,
                "enhanced_context": context,
                "advanced_classification": classification,
                "integration_insights": self._generate_integration_insights(
                    context, classification
                ),
                "processing_strategy": self._determine_processing_strategy(
                    context, classification
                )
            }
            
            return enhanced_analysis
            
        except Exception as e:
            logger.error(f"Error in enhanced query analysis: {e}")
            # Fallback to base analysis
            base_analysis = await self.base_analyzer.anhanced_query_analysis(query, None)
            return {
                "base_analysis": base_analysis,
                "error": str(e),
                "fallback_used": True
            }
    
    def _generate_integration_insights(
        self, 
        context: Dict[str, Any], 
        classification: QueryClassification
    ) -> Dict[str, Any]:
        """Generate insights from integration of context and classification"""
        insights = {}
        
        # User preference insights
        user_context = context.get("user_context", {})
        if user_context:
            insights["user_preferences"] = {
                "tier_level": user_context.get("tier_level"),
                "risk_tolerance": user_context.get("risk_tolerance"),
                "trading_style": user_context.get("trading_style"),
                "preferred_indicators": user_context.get("preferred_indicators", [])
            }
        
        # Market context insights
        market_context = context.get("market_context", {})
        if market_context:
            insights["market_conditions"] = {
                "status": market_context.get("market_status"),
                "volatility": market_context.get("volatility_level"),
                "sentiment": market_context.get("news_sentiment")
            }
        
        # Query complexity insights
        insights["complexity_insights"] = {
            "level": classification.complexity.name,
            "requires_expert_attention": classification.complexity.value >= 4,
            "processing_time_estimate": self._estimate_processing_time(classification.complexity)
        }
        
        # Domain-specific insights
        insights["domain_insights"] = {
            "primary_domain": classification.primary_domain.value,
            "secondary_domains": [d.value for d in classification.secondary_domains],
            "subcategories": classification.subcategories,
            "domain_description": self.advanced_classifier.get_domain_description(
                classification.primary_domain
            )
        }
        
        # Sentiment insights
        insights["sentiment_insights"] = classification.sentiment
        
        return insights
    
    def _determine_processing_strategy(
        self, 
        context: Dict[str, Any], 
        classification: QueryClassification
    ) -> Dict[str, Any]:
        """Determine optimal processing strategy"""
        strategy = {
            "processing_route": "standard",
            "data_requirements": [],
            "ai_model_selection": "default",
            "response_format": "standard",
            "priority": "normal",
            "caching_strategy": "standard"
        }
        
        # Adjust based on complexity
        if classification.complexity.value >= 4:
            strategy["processing_route"] = "enhanced"
            strategy["ai_model_selection"] = "advanced"
            strategy["priority"] = "high"
        elif classification.complexity.value == 1:
            strategy["processing_route"] = "quick"
            strategy["ai_model_selection"] = "fast"
            strategy["priority"] = "low"
        
        # Adjust based on domain
        domain = classification.primary_domain
        if domain in [domain.TECHNICAL_ANALYSIS, domain.FUNDAMENTAL_ANALYSIS]:
            strategy["data_requirements"].extend(["market_data", "historical_data"])
            strategy["caching_strategy"] = "aggressive"
        elif domain == domain.OPTIONS_TRADING:
            strategy["data_requirements"].extend(["options_data", "greeks_data"])
            strategy["ai_model_selection"] = "specialized"
        elif domain == domain.CRYPTO_ANALYSIS:
            strategy["data_requirements"].append("crypto_data")
        
        # Adjust based on urgency
        query_context = context.get("query_context", {})
        if query_context.get("urgency") == "high":
            strategy["priority"] = "high"
            strategy["processing_route"] = "priority"
        
        # Adjust based on user tier
        user_context = context.get("user_context", {})
        if user_context.get("tier_level") in ["premium", "pro"]:
            strategy["priority"] = "high"
            strategy["ai_model_selection"] = "premium"
        
        # Adjust based on context requirements
        if classification.requires_context:
            strategy["processing_route"] = "context_aware"
            strategy["data_requirements"].append("conversation_history")
        
        return strategy
    
    def _estimate_processing_time(self, complexity: 'QueryComplexity') -> str:
        """Estimate processing time based on complexity"""
        base_time = 1  # seconds
        
        if complexity == complexity.SIMPLE:
            return f"{base_time}s"
        elif complexity == complexity.MODERATE:
            return f"{base_time * 2}s"
        elif complexity == complexity.COMPLEX:
            return f"{base_time * 4}s"
        elif complexity == complexity.ADVANCED:
            return f"{base_time * 8}s"
        else:  # EXPERT
            return f"{base_time * 15}s"
    
    async def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]):
        """Update user preferences in context understanding"""
        self.context_understanding.update_user_preferences(user_id, preferences)
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile from context understanding"""
        if user_id in self.context_understanding.user_contexts:
            return self.context_understanding.user_contexts[user_id].__dict__
        return {}

# Global instance
enhanced_analyzer = EnhancedQueryAnalyzer()

# Convenience function
async def analyze_query_with_enhanced_context(
    query: str, 
    user_id: str,
    guild_id: Optional[str] = None,
    conversation_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Convenience function for enhanced query analysis
    
    Args:
        query: User query
        user_id: Discord user ID
        guild_id: Discord guild ID
        conversation_id: Conversation ID for tracking history
        
    Returns:
        Enhanced analysis results
    """
    return await enhanced_analyzer.analyze_query_enhanced(
        query, user_id, guild_id, conversation_id
    )