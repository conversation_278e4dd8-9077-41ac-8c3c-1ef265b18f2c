"""
Voice Input Processor for Ask Pipeline

This module provides voice input parsing capabilities for the /ask command,
allowing users to submit voice messages that are converted to text.
"""

import logging
import io
import asyncio
from typing import Optional, Dict, Any
import discord
from discord import Attachment
import speech_recognition as sr
from pydub import AudioSegment

logger = logging.getLogger(__name__)

class VoiceInputProcessor:
    """
    Processes voice input from Discord attachments and converts to text.
    """
    
    def __init__(self):
        """Initialize the voice input processor."""
        self.recognizer = sr.Recognizer()
        self.supported_formats = ['audio/ogg', 'audio/wav', 'audio/mp3', 'audio/mpeg']
    
    async def process_voice_attachment(self, attachment: Attachment) -> Dict[str, Any]:
        """
        Process a voice attachment and convert to text.
        
        Args:
            attachment: Discord attachment object
            
        Returns:
            Dictionary with processing results
        """
        try:
            # Check if attachment is audio
            if not self._is_audio_attachment(attachment):
                return {
                    'success': False,
                    'error': 'Attachment is not an audio file',
                    'text': None
                }
            
            # Download the attachment
            logger.info(f"Processing voice attachment: {attachment.filename}")
            audio_bytes = await attachment.read()
            
            # Convert to WAV format for processing
            wav_audio = await self._convert_to_wav(audio_bytes, attachment.content_type)
            
            # Convert speech to text
            text = await self._speech_to_text(wav_audio)
            
            return {
                'success': True,
                'text': text,
                'filename': attachment.filename,
                'content_type': attachment.content_type,
                'size': attachment.size
            }
            
        except Exception as e:
            logger.error(f"Error processing voice attachment: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': None
            }
    
    def _is_audio_attachment(self, attachment: Attachment) -> bool:
        """
        Check if attachment is an audio file.
        
        Args:
            attachment: Discord attachment object
            
        Returns:
            True if audio file, False otherwise
        """
        # Check content type
        if attachment.content_type and any(fmt in attachment.content_type.lower() for fmt in ['audio/', 'video/ogg']):
            return True
        
        # Check file extension
        audio_extensions = ['.wav', '.mp3', '.ogg', '.m4a', '.flac']
        return any(attachment.filename.lower().endswith(ext) for ext in audio_extensions)
    
    async def _convert_to_wav(self, audio_bytes: bytes, content_type: Optional[str]) -> bytes:
        """
        Convert audio bytes to WAV format.
        
        Args:
            audio_bytes: Audio data as bytes
            content_type: MIME type of the audio
            
        Returns:
            Audio data in WAV format as bytes
        """
        loop = asyncio.get_event_loop()
        
        def _convert_sync():
            # Load audio with pydub
            audio = AudioSegment.from_file(io.BytesIO(audio_bytes))
            
            # Convert to mono and set sample rate for speech recognition
            audio = audio.set_channels(1).set_frame_rate(16000)
            
            # Export to WAV
            wav_buffer = io.BytesIO()
            audio.export(wav_buffer, format='wav')
            return wav_buffer.getvalue()
        
        return await loop.run_in_executor(None, _convert_sync)
    
    async def _speech_to_text(self, wav_audio: bytes) -> str:
        """
        Convert WAV audio to text using speech recognition.
        
        Args:
            wav_audio: WAV audio data as bytes
            
        Returns:
            Transcribed text
        """
        loop = asyncio.get_event_loop()
        
        def _recognize_sync():
            # Create AudioData object
            audio_data = sr.AudioData(wav_audio, 16000, 2)
            
            # Recognize speech
            try:
                # Using Google Speech Recognition (free tier)
                text = self.recognizer.recognize_google(audio_data)
                return text
            except sr.UnknownValueError:
                logger.warning("Speech recognition could not understand audio")
                return ""
            except sr.RequestError as e:
                logger.error(f"Could not request results from Google Speech Recognition service; {e}")
                # Fallback to sphinx if available
                try:
                    text = self.recognizer.recognize_sphinx(audio_data)
                    return text
                except Exception as sphinx_error:
                    logger.error(f"Sphinx recognition also failed: {sphinx_error}")
                    return ""
        
        return await loop.run_in_executor(None, _recognize_sync)
    
    async def process_multiple_attachments(self, attachments: list[Attachment]) -> Dict[str, Any]:
        """
        Process multiple voice attachments.
        
        Args:
            attachments: List of Discord attachment objects
            
        Returns:
            Dictionary with combined processing results
        """
        results = []
        combined_text = []
        
        for attachment in attachments:
            result = await self.process_voice_attachment(attachment)
            results.append(result)
            
            if result['success'] and result['text']:
                combined_text.append(result['text'])
        
        return {
            'results': results,
            'combined_text': ' '.join(combined_text),
            'success_count': len([r for r in results if r['success']]),
            'total_count': len(results)
        }

# Global voice processor instance
voice_processor = VoiceInputProcessor()

async def process_voice_query(attachment: Attachment) -> Dict[str, Any]:
    """
    Convenience function to process a voice query attachment.
    
    Args:
        attachment: Discord attachment object
        
    Returns:
        Processing results
    """
    return await voice_processor.process_voice_attachment(attachment)