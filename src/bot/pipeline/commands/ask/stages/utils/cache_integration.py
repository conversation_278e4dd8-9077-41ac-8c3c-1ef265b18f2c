"""
Cache Integration for the /ask pipeline.

This module integrates our enhanced cache manager with the existing AI cache system
to provide better caching capabilities while maintaining backward compatibility.
"""

import logging
from typing import Dict, Any, List, Optional
from .enhanced_cache_manager import enhanced_cache_manager
from ..config import get_config

logger = logging.getLogger(__name__)


class CacheIntegration:
    """Integrates enhanced cache manager with existing AI cache system."""
    
    def __init__(self):
        self.config = get_config()
        self._enhancements_enabled = getattr(self.config, 'enable_enhanced_cache_manager', True)
    
    async def get_cached_ai_response(self, query: str) -> Optional[Dict[str, Any]]:
        """
        Enhanced cache retrieval that integrates with existing AI cache.
        
        Args:
            query: User query string
            
        Returns:
            Cached response if available, None otherwise
        """
        if not self._enhancements_enabled:
            # Fall back to existing AI cache
            try:
                from ..ai_cache import get_cached_ai_response
                return await get_cached_ai_response(query)
            except ImportError:
                logger.warning("AI cache module not available")
                return None
        
        try:
            # Use enhanced cache manager with empty symbols list for query-only caching
            cached_response = enhanced_cache_manager.get_cached_response(query, [])
            
            if cached_response:
                logger.info(f"✅ Enhanced cache hit for query: {query[:50]}...")
                # Track cache metrics
                enhanced_cache_manager.record_cache_metrics(True)
                return cached_response
            else:
                logger.debug(f"❌ Enhanced cache miss for query: {query[:50]}...")
                enhanced_cache_manager.record_cache_metrics(False)
                return None
                
        except Exception as e:
            logger.error(f"❌ Enhanced cache retrieval failed: {e}")
            # Fall back to existing AI cache
            try:
                from ..ai_cache import get_cached_ai_response
                return await get_cached_ai_response(query)
            except ImportError:
                return None
    
    async def cache_ai_response(self, query: str, response: Dict[str, Any]) -> None:
        """
        Enhanced cache storage that integrates with existing AI cache.
        
        Args:
            query: User query string
            response: Response data to cache
        """
        if not self._enhancements_enabled:
            # Fall back to existing AI cache
            try:
                from ..ai_cache import cache_ai_response
                await cache_ai_response(query, response)
                return
            except ImportError:
                logger.warning("AI cache module not available")
                return
        
        try:
            # Use enhanced cache manager with empty symbols list for query-only caching
            enhanced_cache_manager.cache_response(query, [], response)
            logger.info(f"✅ Enhanced cache storage successful for query: {query[:50]}...")
            
        except Exception as e:
            logger.error(f"❌ Enhanced cache storage failed: {e}")
            # Fall back to existing AI cache
            try:
                from ..ai_cache import cache_ai_response
                await cache_ai_response(query, response)
            except ImportError:
                pass
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        if not self._enhancements_enabled:
            return {"enhancements_disabled": True}
        
        try:
            return enhanced_cache_manager.get_cache_statistics()
        except Exception as e:
            logger.error(f"❌ Cache statistics retrieval failed: {e}")
            return {"error": str(e)}
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        if not self._enhancements_enabled:
            return {"enhancements_disabled": True}
        
        try:
            return enhanced_cache_manager.get_performance_stats()
        except Exception as e:
            logger.error(f"❌ Performance stats retrieval failed: {e}")
            return {"error": str(e)}
    
    def toggle_enhancements(self, enable: bool) -> None:
        """Toggle enhancement features on/off."""
        self._enhancements_enabled = enable
        logger.info(f"Cache integration enhancements {'enabled' if enable else 'disabled'}")
    
    def reset_metrics(self) -> None:
        """Reset all cache metrics."""
        if self._enhancements_enabled:
            enhanced_cache_manager.reset_metrics()
            logger.info("Cache integration metrics reset")


# Global instance for easy access
cache_integration = CacheIntegration() 