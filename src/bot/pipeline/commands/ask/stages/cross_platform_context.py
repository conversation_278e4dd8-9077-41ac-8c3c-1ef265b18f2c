"""
Cross-Platform Context Sharing Module

This module enables sharing of user context and preferences across different platforms
(Discord, Web, Mobile, etc.) for a consistent user experience.
"""

import logging
import asyncio
import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

# Path for storing cross-platform context data
CONTEXT_STORAGE_PATH = os.path.join('data', 'context')

class CrossPlatformContext:
    """Cross-platform context sharing system"""
    
    def __init__(self):
        """Initialize the cross-platform context system"""
        self.context_cache: Dict[str, Dict[str, Any]] = {}
        self.platform_mappings: Dict[str, Dict[str, str]] = {}
        self._ensure_storage_path()
    
    def _ensure_storage_path(self):
        """Ensure the context storage path exists"""
        os.makedirs(CONTEXT_STORAGE_PATH, exist_ok=True)
    
    async def get_user_context(
        self, 
        user_id: str, 
        platform: str = "discord"
    ) -> Dict[str, Any]:
        """
        Get user context across platforms
        
        Args:
            user_id: User ID on the current platform
            platform: Platform identifier (discord, web, mobile)
            
        Returns:
            User context data
        """
        # Get global user ID
        global_user_id = await self._get_global_user_id(user_id, platform)
        
        # Check cache first
        if global_user_id in self.context_cache:
            return self.context_cache[global_user_id]
        
        # Load from storage
        context = await self._load_user_context(global_user_id)
        
        # Update cache
        self.context_cache[global_user_id] = context
        
        return context
    
    async def update_user_context(
        self, 
        user_id: str, 
        context_data: Dict[str, Any],
        platform: str = "discord"
    ) -> bool:
        """
        Update user context across platforms
        
        Args:
            user_id: User ID on the current platform
            context_data: Context data to update
            platform: Platform identifier (discord, web, mobile)
            
        Returns:
            True if update was successful
        """
        # Get global user ID
        global_user_id = await self._get_global_user_id(user_id, platform)
        
        # Get existing context
        existing_context = await self.get_user_context(user_id, platform)
        
        # Merge context data
        merged_context = {**existing_context, **context_data}
        
        # Update timestamp
        merged_context["last_updated"] = datetime.now().isoformat()
        merged_context["last_platform"] = platform
        
        # Update cache
        self.context_cache[global_user_id] = merged_context
        
        # Save to storage
        return await self._save_user_context(global_user_id, merged_context)
    
    async def link_platform_ids(
        self, 
        primary_id: str, 
        primary_platform: str,
        secondary_id: str,
        secondary_platform: str
    ) -> bool:
        """
        Link user IDs across platforms
        
        Args:
            primary_id: User ID on primary platform
            primary_platform: Primary platform identifier
            secondary_id: User ID on secondary platform
            secondary_platform: Secondary platform identifier
            
        Returns:
            True if linking was successful
        """
        try:
            # Get global user ID for primary
            global_user_id = await self._get_global_user_id(primary_id, primary_platform)
            
            # Update platform mappings
            if secondary_platform not in self.platform_mappings:
                self.platform_mappings[secondary_platform] = {}
            
            self.platform_mappings[secondary_platform][secondary_id] = global_user_id
            
            # Save mappings
            await self._save_platform_mappings()
            
            return True
        except Exception as e:
            logger.error(f"Error linking platform IDs: {e}")
            return False
    
    async def get_conversation_context(
        self,
        conversation_id: str,
        platform: str = "discord"
    ) -> Dict[str, Any]:
        """
        Get conversation context
        
        Args:
            conversation_id: Conversation ID
            platform: Platform identifier
            
        Returns:
            Conversation context data
        """
        # Generate global conversation ID
        global_conv_id = f"{platform}:{conversation_id}"
        
        # Load conversation context
        context_path = os.path.join(CONTEXT_STORAGE_PATH, f"conv_{global_conv_id}.json")
        
        try:
            if os.path.exists(context_path):
                async with asyncio.Lock():
                    with open(context_path, 'r') as f:
                        return json.load(f)
            else:
                return {
                    "conversation_id": global_conv_id,
                    "platform": platform,
                    "history": [],
                    "created_at": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error loading conversation context: {e}")
            return {
                "conversation_id": global_conv_id,
                "platform": platform,
                "history": [],
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "error": str(e)
            }
    
    async def update_conversation_context(
        self,
        conversation_id: str,
        context_data: Dict[str, Any],
        platform: str = "discord"
    ) -> bool:
        """
        Update conversation context
        
        Args:
            conversation_id: Conversation ID
            context_data: Context data to update
            platform: Platform identifier
            
        Returns:
            True if update was successful
        """
        # Generate global conversation ID
        global_conv_id = f"{platform}:{conversation_id}"
        
        # Get existing context
        existing_context = await self.get_conversation_context(conversation_id, platform)
        
        # Merge context data
        merged_context = {**existing_context, **context_data}
        
        # Update timestamp
        merged_context["last_updated"] = datetime.now().isoformat()
        
        # Save conversation context
        context_path = os.path.join(CONTEXT_STORAGE_PATH, f"conv_{global_conv_id}.json")
        
        try:
            async with asyncio.Lock():
                with open(context_path, 'w') as f:
                    json.dump(merged_context, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving conversation context: {e}")
            return False
    
    async def add_conversation_message(
        self,
        conversation_id: str,
        message: Dict[str, Any],
        platform: str = "discord"
    ) -> bool:
        """
        Add message to conversation history
        
        Args:
            conversation_id: Conversation ID
            message: Message data
            platform: Platform identifier
            
        Returns:
            True if message was added successfully
        """
        # Get conversation context
        context = await self.get_conversation_context(conversation_id, platform)
        
        # Add message to history
        if "history" not in context:
            context["history"] = []
        
        # Add timestamp if not present
        if "timestamp" not in message:
            message["timestamp"] = datetime.now().isoformat()
        
        # Add message to history
        context["history"].append(message)
        
        # Limit history size
        max_history = 50
        if len(context["history"]) > max_history:
            context["history"] = context["history"][-max_history:]
        
        # Update conversation context
        return await self.update_conversation_context(conversation_id, context, platform)
    
    async def _get_global_user_id(self, user_id: str, platform: str) -> str:
        """
        Get global user ID across platforms
        
        Args:
            user_id: User ID on the current platform
            platform: Platform identifier
            
        Returns:
            Global user ID
        """
        # Check if mapping exists
        if platform in self.platform_mappings and user_id in self.platform_mappings[platform]:
            return self.platform_mappings[platform][user_id]
        
        # Load mappings if not loaded
        if not self.platform_mappings:
            await self._load_platform_mappings()
            
            # Check again after loading
            if platform in self.platform_mappings and user_id in self.platform_mappings[platform]:
                return self.platform_mappings[platform][user_id]
        
        # Create new global ID if not found
        global_user_id = f"user_{uuid.uuid4().hex}"
        
        # Update mappings
        if platform not in self.platform_mappings:
            self.platform_mappings[platform] = {}
        
        self.platform_mappings[platform][user_id] = global_user_id
        
        # Save mappings
        await self._save_platform_mappings()
        
        return global_user_id
    
    async def _load_user_context(self, global_user_id: str) -> Dict[str, Any]:
        """
        Load user context from storage
        
        Args:
            global_user_id: Global user ID
            
        Returns:
            User context data
        """
        context_path = os.path.join(CONTEXT_STORAGE_PATH, f"{global_user_id}.json")
        
        try:
            if os.path.exists(context_path):
                async with asyncio.Lock():
                    with open(context_path, 'r') as f:
                        return json.load(f)
            else:
                return {
                    "user_id": global_user_id,
                    "created_at": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat(),
                    "preferences": {},
                    "platforms": {}
                }
        except Exception as e:
            logger.error(f"Error loading user context: {e}")
            return {
                "user_id": global_user_id,
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "preferences": {},
                "platforms": {},
                "error": str(e)
            }
    
    async def _save_user_context(self, global_user_id: str, context: Dict[str, Any]) -> bool:
        """
        Save user context to storage
        
        Args:
            global_user_id: Global user ID
            context: Context data to save
            
        Returns:
            True if save was successful
        """
        context_path = os.path.join(CONTEXT_STORAGE_PATH, f"{global_user_id}.json")
        
        try:
            async with asyncio.Lock():
                with open(context_path, 'w') as f:
                    json.dump(context, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving user context: {e}")
            return False
    
    async def _load_platform_mappings(self):
        """Load platform mappings from storage"""
        mappings_path = os.path.join(CONTEXT_STORAGE_PATH, "platform_mappings.json")
        
        try:
            if os.path.exists(mappings_path):
                async with asyncio.Lock():
                    with open(mappings_path, 'r') as f:
                        self.platform_mappings = json.load(f)
            else:
                self.platform_mappings = {}
        except Exception as e:
            logger.error(f"Error loading platform mappings: {e}")
            self.platform_mappings = {}
    
    async def _save_platform_mappings(self) -> bool:
        """
        Save platform mappings to storage
        
        Returns:
            True if save was successful
        """
        mappings_path = os.path.join(CONTEXT_STORAGE_PATH, "platform_mappings.json")
        
        try:
            async with asyncio.Lock():
                with open(mappings_path, 'w') as f:
                    json.dump(self.platform_mappings, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving platform mappings: {e}")
            return False

# Global instance
cross_platform_context = CrossPlatformContext()

# Convenience functions
async def get_user_context(user_id: str, platform: str = "discord") -> Dict[str, Any]:
    """
    Get user context across platforms
    
    Args:
        user_id: User ID on the current platform
        platform: Platform identifier (discord, web, mobile)
        
    Returns:
        User context data
    """
    return await cross_platform_context.get_user_context(user_id, platform)

async def update_user_context(
    user_id: str, 
    context_data: Dict[str, Any],
    platform: str = "discord"
) -> bool:
    """
    Update user context across platforms
    
    Args:
        user_id: User ID on the current platform
        context_data: Context data to update
        platform: Platform identifier (discord, web, mobile)
        
    Returns:
        True if update was successful
    """
    return await cross_platform_context.update_user_context(user_id, context_data, platform)

async def get_conversation_context(
    conversation_id: str,
    platform: str = "discord"
) -> Dict[str, Any]:
    """
    Get conversation context
    
    Args:
        conversation_id: Conversation ID
        platform: Platform identifier
        
    Returns:
        Conversation context data
    """
    return await cross_platform_context.get_conversation_context(conversation_id, platform)

async def add_conversation_message(
    conversation_id: str,
    message: Dict[str, Any],
    platform: str = "discord"
) -> bool:
    """
    Add message to conversation history
    
    Args:
        conversation_id: Conversation ID
        message: Message data
        platform: Platform identifier
        
    Returns:
        True if message was added successfully
    """
    return await cross_platform_context.add_conversation_message(conversation_id, message, platform)
