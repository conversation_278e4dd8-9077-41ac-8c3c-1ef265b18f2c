"""
Enhanced Context Understanding Module

This module enhances the AI's ability to understand user context, including:
- User profile and preferences
- Conversation history
- Market conditions
- Query sentiment
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json

from .ml_sentiment_analyzer import analyze_sentiment_ml
from .cross_platform_context import get_user_context, update_user_context, get_conversation_context, add_conversation_message

logger = logging.getLogger(__name__)

@dataclass
class UserContext:
    """Enhanced user context information"""
    user_id: str
    tier_level: str  # free, basic, premium
    preferred_symbols: List[str]
    risk_tolerance: str  # conservative, moderate, aggressive
    trading_style: str  # day, swing, position, long_term
    last_query_time: datetime
    query_frequency: int  # queries in last 24 hours
    preferred_timeframes: List[str]
    preferred_indicators: List[str]
    language_preference: str = "en"

@dataclass
class MarketContext:
    """Current market context information"""
    market_status: str  # open, closed, pre_market, after_market
    major_indices: Dict[str, Dict[str, Any]]
    sector_performance: Dict[str, float]
    volatility_level: str  # low, moderate, high
    news_sentiment: float  # -1 to 1 scale
    economic_events: List[Dict[str, Any]]
    is_earnings_season: bool

@dataclass
class ConversationContext:
    """Conversation history context"""
    conversation_id: str
    history: List[Dict[str, Any]]
    current_topic: str
    topic_depth: int  # how deep into a topic we are
    last_query_time: datetime
    context_drift_score: float  # 0-1 scale of how much topic has changed

class EnhancedContextUnderstanding:
    """Enhanced context understanding for AI queries"""
    
    def __init__(self):
        self.user_contexts: Dict[str, UserContext] = {}
        self.market_context: Optional[MarketContext] = None
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        
    async def build_enhanced_context(
        self, 
        user_id: str, 
        query: str, 
        guild_id: Optional[str] = None,
        conversation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Build enhanced context for a query
        
        Args:
            user_id: Discord user ID
            query: User query
            guild_id: Discord guild ID
            conversation_id: Conversation ID for tracking history
            
        Returns:
            Enhanced context dictionary
        """
        context = {
            "user_context": await self._get_user_context(user_id),
            "market_context": await self._get_market_context(),
            "conversation_context": await self._get_conversation_context(conversation_id, query),
            "query_context": await self._analyze_query_context(query),
            "temporal_context": self._get_temporal_context(),
            "platform_context": self._get_platform_context(guild_id)
        }
        
        return context
    
    async def _get_user_context(self, user_id: str) -> Dict[str, Any]:
        """Get enhanced user context with cross-platform support"""
        try:
            # Try to get context from cross-platform system first
            cross_platform_data = await get_user_context(user_id)
            
            # Extract preferences if available
            preferences = cross_platform_data.get("preferences", {})
            
            # If we have preferences, use them to update or create user context
            if preferences:
                # Create or update user context
                if user_id not in self.user_contexts:
                    self.user_contexts[user_id] = UserContext(
                        user_id=user_id,
                        tier_level=preferences.get("tier_level", "free"),
                        preferred_symbols=preferences.get("preferred_symbols", []),
                        risk_tolerance=preferences.get("risk_tolerance", "moderate"),
                        trading_style=preferences.get("trading_style", "position"),
                        last_query_time=datetime.now(),
                        query_frequency=1,
                        preferred_timeframes=preferences.get("preferred_timeframes", ["1d"]),
                        preferred_indicators=preferences.get("preferred_indicators", ["rsi", "macd"]),
                        language_preference=preferences.get("language_preference", "en")
                    )
                else:
                    # Update existing context with cross-platform preferences
                    user_context = self.user_contexts[user_id]
                    
                    # Only update fields that are present in preferences
                    if "tier_level" in preferences:
                        user_context.tier_level = preferences["tier_level"]
                    if "preferred_symbols" in preferences:
                        user_context.preferred_symbols = preferences["preferred_symbols"]
                    if "risk_tolerance" in preferences:
                        user_context.risk_tolerance = preferences["risk_tolerance"]
                    if "trading_style" in preferences:
                        user_context.trading_style = preferences["trading_style"]
                    if "preferred_timeframes" in preferences:
                        user_context.preferred_timeframes = preferences["preferred_timeframes"]
                    if "preferred_indicators" in preferences:
                        user_context.preferred_indicators = preferences["preferred_indicators"]
                    if "language_preference" in preferences:
                        user_context.language_preference = preferences["language_preference"]
            else:
                # If no cross-platform data, create a basic context if needed
                if user_id not in self.user_contexts:
                    self.user_contexts[user_id] = UserContext(
                        user_id=user_id,
                        tier_level="free",
                        preferred_symbols=[],
                        risk_tolerance="moderate",
                        trading_style="position",
                        last_query_time=datetime.now(),
                        query_frequency=1,
                        preferred_timeframes=["1d"],
                        preferred_indicators=["rsi", "macd"],
                        language_preference="en"
                    )
            
            user_context = self.user_contexts[user_id]
            
            # Update query frequency
            if datetime.now() - user_context.last_query_time < timedelta(hours=24):
                user_context.query_frequency += 1
            else:
                user_context.query_frequency = 1
                
            user_context.last_query_time = datetime.now()
            
            # Sync back to cross-platform context
            await update_user_context(user_id, {
                "preferences": asdict(user_context)
            })
            
            return asdict(user_context)
            
        except Exception as e:
            logger.error(f"Error getting cross-platform user context: {e}")
            
            # Fallback to local context
            if user_id not in self.user_contexts:
                self.user_contexts[user_id] = UserContext(
                    user_id=user_id,
                    tier_level="free",
                    preferred_symbols=[],
                    risk_tolerance="moderate",
                    trading_style="position",
                    last_query_time=datetime.now(),
                    query_frequency=1,
                    preferred_timeframes=["1d"],
                    preferred_indicators=["rsi", "macd"],
                    language_preference="en"
                )
            
            user_context = self.user_contexts[user_id]
            user_context.last_query_time = datetime.now()
            
            return asdict(user_context)
    
    async def _get_market_context(self) -> Dict[str, Any]:
        """Get current market context"""
        # This would integrate with real market data in production
        # For now, we'll return a basic structure
        if not self.market_context:
            self.market_context = MarketContext(
                market_status="unknown",
                major_indices={
                    "SPY": {"price": 400.0, "change": 0.0, "change_pct": 0.0},
                    "QQQ": {"price": 350.0, "change": 0.0, "change_pct": 0.0}
                },
                sector_performance={
                    "Technology": 0.0,
                    "Financials": 0.0,
                    "Healthcare": 0.0
                },
                volatility_level="moderate",
                news_sentiment=0.0,
                economic_events=[],
                is_earnings_season=False
            )
        
        return asdict(self.market_context)
    
    async def _get_conversation_context(
        self, 
        conversation_id: Optional[str], 
        query: str
    ) -> Dict[str, Any]:
        """Get conversation context with cross-platform support"""
        try:
            if not conversation_id:
                conversation_id = f"conv_{hash(query)}_{datetime.now().timestamp()}"
            
            # Try to get context from cross-platform system first
            cross_platform_data = await get_conversation_context(conversation_id)
            
            # Extract history if available
            history = cross_platform_data.get("history", [])
            
            # If we don't have conversation context locally, create a new one
            if conversation_id not in self.conversation_contexts:
                self.conversation_contexts[conversation_id] = ConversationContext(
                    conversation_id=conversation_id,
                    history=history,  # Use history from cross-platform if available
                    current_topic=cross_platform_data.get("current_topic", "general"),
                    topic_depth=cross_platform_data.get("topic_depth", 0),
                    last_query_time=datetime.now(),
                    context_drift_score=cross_platform_data.get("context_drift_score", 0.0)
                )
            
            conv_context = self.conversation_contexts[conversation_id]
            
            # Update conversation history
            message = {
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "topic": self._infer_topic(query)
            }
            
            conv_context.history.append(message)
            
            # Also add to cross-platform context
            await add_conversation_message(conversation_id, message)
            
            # Update topic and drift
            new_topic = self._infer_topic(query)
            if new_topic != conv_context.current_topic:
                conv_context.context_drift_score = min(1.0, conv_context.context_drift_score + 0.2)
                if conv_context.context_drift_score > 0.5:
                    conv_context.current_topic = new_topic
                    conv_context.topic_depth = 0
            else:
                conv_context.topic_depth += 1
                conv_context.context_drift_score = max(0.0, conv_context.context_drift_score - 0.1)
                
            conv_context.last_query_time = datetime.now()
            
            # Sync back to cross-platform context
            await update_user_context(conversation_id, {
                "current_topic": conv_context.current_topic,
                "topic_depth": conv_context.topic_depth,
                "context_drift_score": conv_context.context_drift_score
            }, "conversation")
            
            return asdict(conv_context)
            
        except Exception as e:
            logger.error(f"Error getting cross-platform conversation context: {e}")
            
            # Fallback to local context
            if not conversation_id:
                conversation_id = f"conv_{hash(query)}_{datetime.now().timestamp()}"
            
            # If we don't have conversation context, create a new one
            if conversation_id not in self.conversation_contexts:
                self.conversation_contexts[conversation_id] = ConversationContext(
                    conversation_id=conversation_id,
                    history=[],
                    current_topic="general",
                    topic_depth=0,
                    last_query_time=datetime.now(),
                    context_drift_score=0.0
                )
            
            conv_context = self.conversation_contexts[conversation_id]
            
            # Update conversation history
            conv_context.history.append({
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "topic": self._infer_topic(query)
            })
            
            # Update topic and drift
            new_topic = self._infer_topic(query)
            if new_topic != conv_context.current_topic:
                conv_context.context_drift_score = min(1.0, conv_context.context_drift_score + 0.2)
                if conv_context.context_drift_score > 0.5:
                    conv_context.current_topic = new_topic
                    conv_context.topic_depth = 0
            else:
                conv_context.topic_depth += 1
                conv_context.context_drift_score = max(0.0, conv_context.context_drift_score - 0.1)
                
            conv_context.last_query_time = datetime.now()
            
            return asdict(conv_context)
    
    async def _analyze_query_context(self, query: str) -> Dict[str, Any]:
        """Analyze the context of a specific query"""
        # Extract symbols from query
        symbols = self._extract_symbols(query)
        
        # Determine query type
        query_type = self._classify_query_type(query)
        
        # Analyze urgency
        urgency = self._assess_urgency(query)
        
        # Analyze sentiment
        sentiment = await self._analyze_sentiment(query)
        
        return {
            "symbols": symbols,
            "query_type": query_type,
            "urgency": urgency,
            "sentiment": sentiment,
            "complexity": self._assess_complexity(query),
            "requires_real_time": self._requires_real_time_data(query),
            "comparison_query": "vs" in query.lower() or "compare" in query.lower()
        }
    
    def _extract_symbols(self, query: str) -> List[str]:
        """Extract stock symbols from query"""
        import re
        # Pattern for $SYMBOL
        symbol_pattern = r'\$([A-Z]{1,5})'
        matches = re.findall(symbol_pattern, query)
        return list(set(matches))  # Remove duplicates
    
    def _classify_query_type(self, query: str) -> str:
        """Classify the type of query"""
        query_lower = query.lower()
        
        # Classification keywords
        classifications = {
            "technical_analysis": ["rsi", "macd", "moving average", "support", "resistance", "indicator"],
            "fundamental_analysis": ["earnings", "pe ratio", "revenue", "profit", "fundamental"],
            "price_query": ["price", "current", "quote", "how much"],
            "comparison": ["vs", "compare", "versus"],
            "strategy": ["strategy", "play", "position", "long", "short"],
            "risk": ["risk", "volatility", "safe", "danger"],
            "education": ["explain", "what is", "how to", "learn"]
        }
        
        # Score each classification
        scores = {}
        for category, keywords in classifications.items():
            scores[category] = sum(1 for keyword in keywords if keyword in query_lower)
        
        # Return the highest scoring category, or general if none score
        if scores:
            best_category = max(scores.items(), key=lambda x: x[1])
            if best_category[1] > 0:
                return best_category[0]
        
        return "general"
    
    def _assess_urgency(self, query: str) -> str:
        """Assess the urgency of a query"""
        query_lower = query.lower()
        
        urgent_keywords = ["now", "immediately", "urgent", "asap", "quick", "fast"]
        if any(keyword in query_lower for keyword in urgent_keywords):
            return "high"
        
        time_sensitive_keywords = ["today", "current", "latest", "this week", "tomorrow"]
        if any(keyword in query_lower for keyword in time_sensitive_keywords):
            return "medium"
        
        return "low"
    
    async def _analyze_sentiment(self, query: str) -> Dict[str, Any]:
        """Analyze sentiment of a query using ML-based approach"""
        try:
            # Use ML-based sentiment analyzer
            sentiment_result = await analyze_sentiment_ml(query)
            return sentiment_result
        except Exception as e:
            logger.error(f"Error in ML sentiment analysis: {e}")
            
            # Fallback to simple sentiment analysis based on keywords
            query_lower = query.lower()
            
            positive_words = ["good", "great", "excellent", "amazing", "awesome", "bullish", "positive"]
            negative_words = ["bad", "terrible", "awful", "horrible", "bearish", "negative", "worst"]
            
            positive_score = sum(1 for word in positive_words if word in query_lower)
            negative_score = sum(1 for word in negative_words if word in query_lower)
            
            # Normalize scores
            total = positive_score + negative_score
            if total > 0:
                sentiment_score = (positive_score - negative_score) / total
            else:
                sentiment_score = 0.0
            
            # Determine sentiment label
            if sentiment_score > 0.3:
                sentiment_label = "positive"
            elif sentiment_score < -0.3:
                sentiment_label = "negative"
            else:
                sentiment_label = "neutral"
            
            return {
                "score": sentiment_score,
                "label": sentiment_label,
                "confidence": min(1.0, total / 5.0),  # Confidence based on number of sentiment words
                "analysis_method": "fallback"
            }
    
    def _assess_complexity(self, query: str) -> int:
        """Assess the complexity of a query (1-10 scale)"""
        # Factors affecting complexity:
        # 1. Length
        # 2. Number of symbols
        # 3. Technical terms
        # 4. Multiple questions
        
        complexity = 1
        
        # Length factor
        if len(query.split()) > 20:
            complexity += 2
        elif len(query.split()) > 10:
            complexity += 1
        
        # Symbol count factor
        symbols = self._extract_symbols(query)
        if len(symbols) > 3:
            complexity += 2
        elif len(symbols) > 1:
            complexity += 1
        
        # Technical terms factor
        technical_terms = ["rsi", "macd", "bollinger", "stochastic", "atr", "ema", "sma", "fibonacci"]
        tech_count = sum(1 for term in technical_terms if term in query.lower())
        complexity += min(3, tech_count)
        
        # Question count factor
        question_marks = query.count('?')
        complexity += min(2, question_marks)
        
        return min(10, complexity)
    
    def _requires_real_time_data(self, query: str) -> bool:
        """Determine if query requires real-time data"""
        real_time_keywords = [
            "current", "now", "latest", "real-time", "live", "today", 
            "price", "quote", "momentum", "volume"
        ]
        return any(keyword in query.lower() for keyword in real_time_keywords)
    
    def _infer_topic(self, query: str) -> str:
        """Infer the topic of a query"""
        query_lower = query.lower()
        
        topics = {
            "technical_analysis": ["rsi", "macd", "indicator", "support", "resistance", "chart"],
            "fundamental_analysis": ["earnings", "revenue", "profit", "pe ratio", "fundamental"],
            "market_outlook": ["market", "outlook", "trend", "sentiment"],
            "risk_management": ["risk", "stop loss", "position size", "volatility"],
            "trading_strategy": ["strategy", "play", "entry", "exit", "position"],
            "education": ["explain", "what is", "how to", "learn", "tutorial"]
        }
        
        # Score each topic
        scores = {}
        for topic, keywords in topics.items():
            scores[topic] = sum(1 for keyword in keywords if keyword in query_lower)
        
        # Return the highest scoring topic, or general if none score
        if scores:
            best_topic = max(scores.items(), key=lambda x: x[1])
            if best_topic[1] > 0:
                return best_topic[0]
        
        return "general"
    
    def _get_temporal_context(self) -> Dict[str, Any]:
        """Get temporal context"""
        now = datetime.now()
        return {
            "current_time": now.isoformat(),
            "day_of_week": now.weekday(),
            "hour_of_day": now.hour,
            "is_market_hours": 9 <= now.hour <= 16,  # Simple market hours check
            "season": self._get_season(now),
            "days_since_last_query": 0  # Would be calculated in real implementation
        }
    
    def _get_season(self, date: datetime) -> str:
        """Get the season for a date"""
        month = date.month
        if month in [12, 1, 2]:
            return "winter"
        elif month in [3, 4, 5]:
            return "spring"
        elif month in [6, 7, 8]:
            return "summer"
        else:
            return "fall"
    
    def _get_platform_context(self, guild_id: Optional[str]) -> Dict[str, Any]:
        """Get platform-specific context"""
        return {
            "platform": "discord",
            "guild_id": guild_id,
            "is_dm": guild_id is None,
            "platform_features": ["rich_embeds", "attachments", "reactions"]
        }
    
    def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]):
        """Update user preferences"""
        if user_id in self.user_contexts:
            user_context = self.user_contexts[user_id]
            
            # Update preferences that are provided
            for key, value in preferences.items():
                if hasattr(user_context, key):
                    setattr(user_context, key, value)
        else:
            logger.warning(f"User context not found for user {user_id}")

# Global instance
enhanced_context = EnhancedContextUnderstanding()