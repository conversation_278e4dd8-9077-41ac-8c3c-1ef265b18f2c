"""
Redis-backed caching for AI analysis results to improve performance and reduce API calls.
Provides intelligent caching with TTL, configurable toggles, and query-based invalidation.
"""

import json
import hashlib
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta, timezone
import os # Added for os.getenv

# Try to import Redis with fallback
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis not available - caching disabled")

from ..config import config

logger = logging.getLogger(__name__)


class AICache:
    """Redis-backed cache for AI analysis results with intelligent TTL management"""
    
    def __init__(self, redis_url: Optional[str] = None, redis_password: Optional[str] = None):
        """Initialize the AI cache with Redis connection."""
        self.logger = logging.getLogger(__name__)
        self.cache_enabled = False
        self.redis_client = None
        
        # Security: Require authentication
        if not redis_url and not redis_password:
            self.logger.error("❌ Redis authentication required - no password or URL provided")
            self.cache_enabled = False
            return
        
        try:
            if redis_url:
                # Use Redis URL with authentication
                self.logger.info(f"🔧 Connecting to Redis via URL")
                self.redis_client = redis.from_url(
                    redis_url,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
            else:
                # Fallback to individual components with password requirement
                redis_host = os.getenv('REDIS_HOST', 'redis')
                redis_port = int(os.getenv('REDIS_PORT', 6379))
                redis_db = int(os.getenv('REDIS_DB', 0))
                
                if not redis_password:
                    self.logger.error("❌ Redis password required for individual component connection")
                    self.cache_enabled = False
                    return
                
                logger.info(f"🔧 Attempting Redis connection to {redis_host}:{redis_port} with authentication")
                self.redis_client = redis.Redis(
                    host=redis_host,
                    port=redis_port,
                    db=redis_db,
                    password=redis_password,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
            
            # Test connection
            self.redis_client.ping()
            self.cache_enabled = True
            logger.info("✅ Redis cache connected successfully with authentication")
            
        except redis.AuthenticationError:
            logger.error("❌ Redis authentication failed - check password")
            self.cache_enabled = False
            self.redis_client = None
        except Exception as e:
            logger.error(f"❌ Failed to connect to Redis: {e}")
            self.cache_enabled = False
            self.redis_client = None
    
    def _generate_cache_key(self, query: str) -> str:
        """Generate a consistent cache key from the query"""
        # Normalize query: lowercase, strip whitespace, and hash
        normalized_query = query.lower().strip()
        query_hash = hashlib.md5(normalized_query.encode()).hexdigest()
        return f"ai:ask:{query_hash}"
    
    def _is_market_data_query(self, query: str) -> bool:
        """Check if query is related to market data/stock prices"""
        query_lower = query.lower()
        
        # Keywords that indicate market data queries
        market_data_keywords = [
            'price', 'stock', 'share', 'ticker', 'market', 'trading',
            'current', 'today', 'now', 'live', 'real-time', '$',
            'gme', 'nvda', 'tsla', 'aapl', 'msft', 'amd', 'spy', 'qqq'
        ]
        
        # Check if query contains market data keywords
        return any(keyword in query_lower for keyword in market_data_keywords)
    
    def _is_market_open(self) -> bool:
        """Check if US stock markets are currently open"""
        current_time = self._get_current_utc_time()
        
        # Convert to US Eastern Time (ET) for market hours
        # US markets are open 9:30 AM - 4:00 PM ET, Monday-Friday
        try:
            import pytz
            et_tz = pytz.timezone('US/Eastern')
            et_time = current_time.astimezone(et_tz)
            
            # Check if it's a weekday (Monday = 0, Sunday = 6)
            if et_time.weekday() >= 5:  # Saturday or Sunday
                return False
            
            # Check if it's during market hours (9:30 AM - 4:00 PM ET)
            market_open = et_time.replace(hour=9, minute=30, second=0, microsecond=0)
            market_close = et_time.replace(hour=16, minute=0, second=0, microsecond=0)
            
            return market_open <= et_time <= market_close
            
        except ImportError:
            # Fallback: assume markets are open during typical US business hours UTC
            # US ET is UTC-5 (EST) or UTC-4 (EDT), so roughly 14:30-21:00 UTC
            utc_hour = current_time.hour
            return 14 <= utc_hour <= 21
    
    def get_default_ttl(self) -> int:
        """Get default cache TTL from central configuration"""
        from src.core.config_manager import get_config
        
        # Get central configuration
        config = get_config()
        base_ttl = config.get('pipeline', 'cache_ttl', 3600)  # Default to 1 hour
        
        # Override with environment variable if set
        return int(os.getenv('ASK_PIPELINE_CACHE_TTL', str(base_ttl)))
    
    def _get_current_utc_time(self) -> datetime:
        """Get current time in UTC for consistent timestamp handling"""
        return datetime.now(timezone.utc)
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """Parse timestamp string with proper timezone handling"""
        try:
            if not timestamp_str:
                return None
            
            # Handle ISO format with timezone
            if 'T' in timestamp_str:
                if timestamp_str.endswith('Z'):
                    # UTC time
                    timestamp_str = timestamp_str[:-1] + '+00:00'
                elif '+' in timestamp_str or timestamp_str.endswith('+00:00'):
                    # Already has timezone info
                    pass
                else:
                    # Assume UTC if no timezone specified
                    timestamp_str = timestamp_str + '+00:00'
                
                return datetime.fromisoformat(timestamp_str)
            else:
                # Try other common formats
                return datetime.fromisoformat(timestamp_str)
                
        except (ValueError, TypeError) as e:
            logger.warning(f"Failed to parse timestamp '{timestamp_str}': {e}")
            return None
    
    def _is_cache_stale(self, cached_time: datetime, query: str) -> tuple[bool, float, str]:
        """
        Check if cache is stale based on query type and age
        
        Returns:
            tuple: (is_stale, age_seconds, warning_message)
        """
        current_time = self._get_current_utc_time()
        
        # Handle timezone-naive timestamps by assuming UTC
        if cached_time.tzinfo is None:
            cached_time = cached_time.replace(tzinfo=timezone.utc)
        
        age_seconds = (current_time - cached_time).total_seconds()
        age_minutes = age_seconds / 60
        
        # Get market-aware TTL
        cache_ttl = self._get_market_aware_ttl(query)
        
        # Check if cache is expired
        if age_seconds > cache_ttl:
            if self._is_market_data_query(query):
                if self._is_market_open():
                    warning = f"Market data cache expired during market hours ({age_minutes:.1f} minutes old)"
                else:
                    warning = f"Market data cache expired during closed markets ({age_minutes:.1f} minutes old)"
            else:
                warning = f"Cache expired ({age_minutes:.1f} minutes old)"
            return True, age_seconds, warning
        
        # Check if cache is getting old but not expired
        if self._is_market_data_query(query):
            if self._is_market_open():
                # During market hours, warn at 15 seconds
                if age_seconds > 15:
                    warning = f"Market data cache is {age_minutes:.1f} minutes old during market hours - consider refreshing"
                    return False, age_seconds, warning
            else:
                # During closed markets, warn at 2 minutes
                if age_seconds > 120:
                    warning = f"Market data cache is {age_minutes:.1f} minutes old during closed markets"
                    return False, age_seconds, warning
        else:
            # For non-market data queries, warn at 80% of TTL
            if age_seconds > (cache_ttl * 0.8):
                warning = f"Cache is {age_minutes:.1f} minutes old - will expire soon"
                return False, age_seconds, warning
        
        return False, age_seconds, ""
    
    async def get_cached_response(self, query: str) -> Optional[Dict[str, Any]]:
        """Get cached AI response for a query if available and valid"""
        if not self.cache_enabled or not self.redis_client:
            return None
        
        try:
            cache_key = self._generate_cache_key(query)
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                result = json.loads(cached_data)
                
                # Parse cached timestamp
                cached_time_str = result.get('cached_at', '')
                cached_time = self._parse_timestamp(cached_time_str)
                
                if cached_time is None:
                    logger.warning(f"Invalid timestamp in cache: {cached_time_str}")
                    # Remove invalid cache entry
                    self.redis_client.delete(cache_key)
                    return None
                
                # Check if cache is stale
                is_stale, age_seconds, warning = self._is_cache_stale(cached_time, query)
                
                if is_stale:
                    logger.debug(f"🕒 {warning} for query: {query[:50]}...")
                    # Remove expired cache
                    self.redis_client.delete(cache_key)
                    return None
                else:
                    # Cache is valid, but add age warning if applicable
                    if warning:
                        logger.info(f"⚠️ {warning} for query: {query[:50]}...")
                        # Add warning to response data
                        if 'response_data' in result:
                            if isinstance(result['response_data'], dict):
                                result['response_data']['cache_warning'] = warning
                                result['response_data']['cache_age_seconds'] = age_seconds
                    
                    logger.info(f"📦 Cache hit for query: {query[:50]}... (age: {age_seconds:.1f}s)")
                    return result['response_data']
            
        except Exception as e:
            logger.warning(f"⚠️ Cache retrieval error: {e}")
        
        return None
    
    async def cache_response(self, query: str, response_data: Dict[str, Any]) -> bool:
        """Cache AI response with timestamp, TTL, and symbol indexing"""
        if not self.cache_enabled or not self.redis_client:
            return False
        
        try:
            cache_key = self._generate_cache_key(query)
            
            # Get market-aware TTL
            cache_ttl = self._get_market_aware_ttl(query)
            
            # Extract symbols from response data for indexing
            symbols = response_data.get('symbols', [])
            
            # Use UTC time for consistent timestamp handling
            current_time = self._get_current_utc_time()
            
            cache_entry = {
                'response_data': response_data,
                'cached_at': current_time.isoformat(),
                'query': query[:100],  # Store first 100 chars for debugging
                'ttl': cache_ttl,
                'symbols': symbols,  # Store symbols for later invalidation
                'cache_version': '2.0',  # Version for cache format compatibility
                'market_open': self._is_market_open()  # Store market status for debugging
            }
            
            # Store with TTL
            success = self.redis_client.setex(
                cache_key,
                cache_ttl,
                json.dumps(cache_entry)
            )
            
            if success:
                market_status = "OPEN" if self._is_market_open() else "CLOSED"
                logger.info(f"💾 Cached response for query: {query[:50]}... (TTL: {cache_ttl}s, Market: {market_status}, UTC: {current_time.strftime('%H:%M:%S')}Z)")
                
                # Index cache key by symbols for efficient invalidation
                for symbol in symbols:
                    symbol_key = f"ai:ask:symbol:{symbol.upper()}"
                    self.redis_client.sadd(symbol_key, cache_key)
                    # Set TTL on symbol index to match cache entry TTL
                    self.redis_client.expire(symbol_key, cache_ttl)
            
            return bool(success)
            
        except Exception as e:
            logger.error(f"❌ Cache storage error: {e}")
            return False
    
    async def invalidate_cache(self, query: str = None, symbol: str = None) -> bool:
        """Invalidate cache for specific query, symbol, or clear entire cache"""
        if not self.cache_enabled or not self.redis_client:
            return False
        
        try:
            if query:
                # Invalidate specific query
                cache_key = self._generate_cache_key(query)
                deleted = self.redis_client.delete(cache_key)
                logger.info(f"🗑️ Invalidated cache for query: {query[:50]}...")
                return bool(deleted)
            elif symbol:
                # Invalidate all cache entries for a specific symbol
                symbol_key = f"ai:ask:symbol:{symbol.upper()}"
                cache_keys = self.redis_client.smembers(symbol_key)
                
                if cache_keys:
                    # Convert to list and delete all cache entries
                    key_list = list(cache_keys)
                    deleted = self.redis_client.delete(*key_list)
                    
                    # Also remove the symbol index
                    self.redis_client.delete(symbol_key)
                    
                    logger.info(f"🗑️ Invalidated {deleted} cache entries for symbol: {symbol}")
                    return bool(deleted)
                else:
                    logger.info(f"ℹ️ No cache entries found for symbol: {symbol}")
                    return True
            else:
                # Clear all AI cache keys and symbol indexes
                cache_keys = self.redis_client.keys("ai:ask:*")
                symbol_keys = self.redis_client.keys("ai:ask:symbol:*")
                
                all_keys = cache_keys + symbol_keys
                if all_keys:
                    deleted = self.redis_client.delete(*all_keys)
                    logger.info(f"🗑️ Cleared {deleted} cache entries and indexes")
                    return bool(deleted)
                return True
                
        except Exception as e:
            logger.error(f"❌ Cache invalidation error: {e}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics and health"""
        if not self.cache_enabled or not self.redis_client:
            return {"enabled": False, "connected": False}
        
        try:
            # Count cache entries
            keys = self.redis_client.keys("ai:ask:*")
            total_entries = len(keys)
            
            # Get memory usage (approximate)
            memory_info = self.redis_client.info('memory')
            
            # Get current market status and TTL info
            current_time = self._get_current_utc_time()
            market_open = self._is_market_open()
            
            # Sample some cache entries to get age statistics
            sample_keys = keys[:10] if len(keys) > 10 else keys
            cache_ages = []
            
            for key in sample_keys:
                try:
                    cached_data = self.redis_client.get(key)
                    if cached_data:
                        result = json.loads(cached_data)
                        cached_time_str = result.get('cached_at', '')
                        cached_time = self._parse_timestamp(cached_time_str)
                        if cached_time:
                            age_seconds = (current_time - cached_time).total_seconds()
                            cache_ages.append(age_seconds)
                except Exception:
                    continue
            
            avg_age = sum(cache_ages) / len(cache_ages) if cache_ages else 0
            oldest_cache = max(cache_ages) if cache_ages else 0
            
            return {
                "enabled": True,
                "connected": True,
                "total_entries": total_entries,
                "memory_used_mb": memory_info.get('used_memory', 0) / 1024 / 1024,
                "current_time_utc": current_time.isoformat(),
                "market_status": "OPEN" if market_open else "CLOSED",
                "market_aware_ttl": {
                    "market_data_open": 30,  # 30 seconds during market hours
                    "market_data_closed": 300,  # 5 minutes during closed markets
                    "general_queries": int(os.getenv('ASK_PIPELINE_CACHE_TTL', 300))
                },
                "cache_age_stats": {
                    "average_age_seconds": avg_age,
                    "oldest_cache_seconds": oldest_cache,
                    "oldest_cache_minutes": oldest_cache / 60 if oldest_cache > 0 else 0
                },
                "cache_hit_rate": "N/A"  # Would need tracking for hit rate
            }
            
        except Exception as e:
            logger.error(f"❌ Cache stats error: {e}")
            return {"enabled": True, "connected": False, "error": str(e)}


# Global cache instance with environment-based configuration
def _create_global_cache():
    """Create global cache instance with proper configuration"""
    try:
        redis_url = os.getenv('REDIS_URL')
        redis_password = os.getenv('REDIS_PASSWORD')
        
        if not redis_url and not redis_password:
            logger.warning("⚠️ No Redis configuration found - cache will be disabled")
            return AICache()  # Will fail gracefully
        
        return AICache(redis_url=redis_url, redis_password=redis_password)
    except Exception as e:
        logger.error(f"❌ Failed to create global cache: {e}")
        # Return a disabled cache instance
        cache = AICache()
        cache.cache_enabled = False
        return cache

ai_cache = _create_global_cache()


# Utility functions for easy access
async def get_cached_ai_response(query: str) -> Optional[Dict[str, Any]]:
    """Get cached AI response if available"""
    return await ai_cache.get_cached_response(query)


async def cache_ai_response(query: str, response_data: Dict[str, Any]) -> bool:
    """Cache AI response for future use"""
    return await ai_cache.cache_response(query, response_data)


async def invalidate_ai_cache(query: str = None, symbol: str = None) -> bool:
    """Invalidate AI cache entries for specific query or symbol"""
    return await ai_cache.invalidate_cache(query, symbol)


def get_ai_cache_stats() -> Dict[str, Any]:
    """Get AI cache statistics"""
    return ai_cache.get_cache_stats()
