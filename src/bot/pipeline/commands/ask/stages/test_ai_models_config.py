#!/usr/bin/env python3
"""
Test script for AI Models Configuration

This script tests the configurable AI model system to ensure it works correctly.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.bot.pipeline.commands.ask.stages.ai_models_config_loader import get_ai_models_config_loader
from src.bot.pipeline.commands.ask.stages.ai_routing_service import AdvancedAIRoutingService, QueryComplexity

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_config_loader():
    """Test the AI models configuration loader."""
    logger.info("Testing AI models configuration loader...")
    
    try:
        config_loader = get_ai_models_config_loader()
        
        # Test loading model configurations
        model_configs = config_loader.get_model_configs()
        logger.info(f"Loaded {len(model_configs)} model configurations")
        
        for model_key, model_config in model_configs.items():
            logger.info(f"  - {model_key}: {model_config.name} ({model_config.provider})")
            logger.info(f"    Model ID: {model_config.model_id}")
            logger.info(f"    Max Tokens: {model_config.max_tokens}")
            logger.info(f"    Cost per 1K tokens: ${model_config.cost_per_1k_tokens}")
            logger.info(f"    Enabled: {model_config.enabled}")
            logger.info(f"    Capabilities: {model_config.capabilities}")
        
        # Test scoring weights
        scoring_weights = config_loader.get_scoring_weights()
        logger.info(f"Scoring weights: {scoring_weights}")
        
        # Test complexity multipliers
        complexity_multipliers = config_loader.get_complexity_multipliers()
        logger.info(f"Complexity multipliers: {complexity_multipliers}")
        
        # Test performance config
        performance_config = config_loader.get_performance_config()
        logger.info(f"Performance config: {performance_config}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing config loader: {e}")
        return False


def test_ai_routing_service():
    """Test the AI routing service with configurable models."""
    logger.info("Testing AI routing service...")
    
    try:
        # Initialize the routing service
        routing_service = AdvancedAIRoutingService()
        
        # Test model initialization
        logger.info(f"Initialized {len(routing_service.models)} AI models")
        
        for model_key, model in routing_service.models.items():
            logger.info(f"  - {model_key}: {model.name} (Available: {model.is_available})")
        
        # Test query analysis
        test_queries = [
            "What is the current price of AAPL?",
            "Analyze the RSI and MACD for Tesla stock",
            "Perform a complex multi-timeframe analysis of SPY",
            "What's the risk assessment for my portfolio with high volatility stocks?",
            "Execute a live trading signal for NVDA now!"
        ]
        
        for query in test_queries:
            logger.info(f"\nAnalyzing query: '{query}'")
            analysis = routing_service.analyze_query_complexity(query)
            logger.info(f"  Complexity: {analysis.complexity.value}")
            logger.info(f"  Required capabilities: {[cap.value for cap in analysis.required_capabilities]}")
            logger.info(f"  Estimated tokens: {analysis.estimated_tokens}")
            logger.info(f"  Urgency: {analysis.urgency}")
            
            # Test model selection
            optimal_model, fallback_models = routing_service.select_optimal_model(analysis)
            logger.info(f"  Selected model: {optimal_model.name}")
            logger.info(f"  Fallback models: {[m.name for m in fallback_models]}")
        
        # Test configuration summary
        config_summary = routing_service.get_configuration_summary()
        logger.info(f"\nConfiguration summary:")
        logger.info(f"  Total models: {config_summary['models']['total']}")
        logger.info(f"  Available models: {config_summary['models']['available']}")
        logger.info(f"  Scoring weights: {config_summary['scoring_weights']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing AI routing service: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_environment_variables():
    """Test environment variable substitution."""
    logger.info("Testing environment variable substitution...")
    
    # Set some test environment variables
    test_env_vars = {
        'GPT_4O_MINI_ENABLED': 'false',
        'AI_SCORING_ACCURACY_WEIGHT': '0.5',
        'AI_COMPLEXITY_SIMPLE': '1.2'
    }
    
    # Save original values
    original_values = {}
    for key, value in test_env_vars.items():
        original_values[key] = os.environ.get(key)
        os.environ[key] = value
    
    try:
        # Create a new config loader to pick up the changes
        from src.bot.pipeline.commands.ask.stages.ai_models_config_loader import AIModelsConfigLoader
        config_loader = AIModelsConfigLoader()
        
        # Test that environment variables are applied
        model_configs = config_loader.get_model_configs()
        gpt_4o_mini = model_configs.get('gpt-4o-mini')
        if gpt_4o_mini:
            logger.info(f"GPT-4o Mini enabled: {gpt_4o_mini.enabled} (should be False)")
            assert not gpt_4o_mini.enabled, "Environment variable override failed"
        
        scoring_weights = config_loader.get_scoring_weights()
        logger.info(f"Accuracy weight: {scoring_weights['accuracy']} (should be 0.5)")
        assert scoring_weights['accuracy'] == 0.5, "Environment variable override failed"
        
        complexity_multipliers = config_loader.get_complexity_multipliers()
        logger.info(f"Simple complexity: {complexity_multipliers['simple']} (should be 1.2)")
        assert complexity_multipliers['simple'] == 1.2, "Environment variable override failed"
        
        logger.info("Environment variable substitution working correctly!")
        return True
        
    except Exception as e:
        logger.error(f"Error testing environment variables: {e}")
        return False
        
    finally:
        # Restore original environment variables
        for key, original_value in original_values.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value


def main():
    """Run all tests."""
    logger.info("Starting AI Models Configuration Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Configuration Loader", test_config_loader),
        ("AI Routing Service", test_ai_routing_service),
        ("Environment Variables", test_environment_variables)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'=' * 20} {test_name} {'=' * 20}")
        try:
            result = test_func()
            results.append((test_name, result))
            logger.info(f"✅ {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'=' * 50}")
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! AI Models Configuration is working correctly.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the configuration.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
