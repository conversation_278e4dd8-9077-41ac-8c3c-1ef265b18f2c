"""
AI Models Configuration Loader

Loads AI model configurations from YAML files with environment variable substitution.
Provides a centralized way to manage AI model settings without hardcoding values.
"""

import os
import yaml
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from dataclasses import dataclass
import re

logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """Configuration for a single AI model."""
    name: str
    provider: str
    model_id: str
    capabilities: List[str]
    max_tokens: int
    cost_per_1k_tokens: float
    response_time_ms: int
    accuracy_score: float
    enabled: bool = True


class AIModelsConfigLoader:
    """Loads and manages AI model configurations."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration loader.
        
        Args:
            config_path: Path to the AI models configuration file
        """
        self.logger = logging.getLogger(__name__)
        
        # Default config path
        if config_path is None:
            current_dir = Path(__file__).parent
            config_path = current_dir / "ai_models_config.yaml"
        
        self.config_path = Path(config_path)
        self._config_cache = None
        self._last_modified = None
        
    def _substitute_env_vars(self, value: Any) -> Any:
        """
        Recursively substitute environment variables in configuration values.
        
        Supports syntax: ${VAR_NAME} and ${VAR_NAME:-default_value}
        """
        if isinstance(value, str):
            # Pattern to match ${VAR_NAME} or ${VAR_NAME:-default}
            pattern = r'\$\{([^}]+)\}'
            
            def replace_var(match):
                var_expr = match.group(1)
                if ':-' in var_expr:
                    var_name, default_value = var_expr.split(':-', 1)
                    return os.getenv(var_name.strip(), default_value)
                else:
                    var_name = var_expr.strip()
                    env_value = os.getenv(var_name)
                    if env_value is None:
                        self.logger.warning(f"Environment variable {var_name} not found")
                        return match.group(0)  # Return original if not found
                    return env_value
            
            return re.sub(pattern, replace_var, value)
        
        elif isinstance(value, dict):
            return {k: self._substitute_env_vars(v) for k, v in value.items()}
        
        elif isinstance(value, list):
            return [self._substitute_env_vars(item) for item in value]
        
        else:
            return value
    
    def _convert_types(self, value: Any, expected_type: type) -> Any:
        """Convert string values to expected types."""
        if isinstance(value, str):
            if expected_type == bool:
                return value.lower() in ('true', '1', 'yes', 'on')
            elif expected_type == int:
                try:
                    return int(value)
                except ValueError:
                    self.logger.warning(f"Could not convert '{value}' to int")
                    return 0
            elif expected_type == float:
                try:
                    return float(value)
                except ValueError:
                    self.logger.warning(f"Could not convert '{value}' to float")
                    return 0.0
        
        return value
    
    def _load_config_file(self) -> Dict[str, Any]:
        """Load and parse the configuration file."""
        try:
            if not self.config_path.exists():
                self.logger.error(f"AI models config file not found: {self.config_path}")
                return {}
            
            # Check if file has been modified
            current_modified = self.config_path.stat().st_mtime
            if (self._config_cache is not None and 
                self._last_modified is not None and 
                current_modified == self._last_modified):
                return self._config_cache
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                raw_config = yaml.safe_load(f)
            
            if not raw_config:
                self.logger.error("Empty or invalid AI models configuration file")
                return {}
            
            # Substitute environment variables
            config = self._substitute_env_vars(raw_config)
            
            # Cache the result
            self._config_cache = config
            self._last_modified = current_modified
            
            self.logger.info(f"Loaded AI models configuration from {self.config_path}")
            return config
            
        except yaml.YAMLError as e:
            self.logger.error(f"Error parsing AI models config YAML: {e}")
            return {}
        except Exception as e:
            self.logger.error(f"Error loading AI models config: {e}")
            return {}
    
    def get_model_configs(self) -> Dict[str, ModelConfig]:
        """
        Get all configured AI models.
        
        Returns:
            Dictionary mapping model keys to ModelConfig objects
        """
        config = self._load_config_file()
        if not config:
            return {}
        
        models_config = config.get('models', {})
        capabilities_map = config.get('capabilities', {})
        defaults = config.get('defaults', {})
        
        model_configs = {}
        
        for model_key, model_data in models_config.items():
            try:
                # Apply defaults
                for key, default_value in defaults.items():
                    if key not in model_data:
                        model_data[key] = default_value
                
                # Convert capability names to enum values
                capabilities = []
                for cap_name in model_data.get('capabilities', []):
                    if cap_name in capabilities_map:
                        capabilities.append(capabilities_map[cap_name])
                    else:
                        capabilities.append(cap_name.upper())
                
                # Create ModelConfig with type conversion
                model_config = ModelConfig(
                    name=model_data.get('name', model_key),
                    provider=model_data.get('provider', 'Unknown'),
                    model_id=model_data.get('model_id', model_key),
                    capabilities=capabilities,
                    max_tokens=self._convert_types(model_data.get('max_tokens', 2000), int),
                    cost_per_1k_tokens=self._convert_types(model_data.get('cost_per_1k_tokens', 0.001), float),
                    response_time_ms=self._convert_types(model_data.get('response_time_ms', 2000), int),
                    accuracy_score=self._convert_types(model_data.get('accuracy_score', 0.8), float),
                    enabled=self._convert_types(model_data.get('enabled', True), bool)
                )
                
                model_configs[model_key] = model_config
                
            except Exception as e:
                self.logger.error(f"Error processing model config for {model_key}: {e}")
                continue
        
        self.logger.info(f"Loaded {len(model_configs)} AI model configurations")
        return model_configs
    
    def get_scoring_weights(self) -> Dict[str, float]:
        """Get model selection scoring weights."""
        config = self._load_config_file()
        weights = config.get('scoring_weights', {})
        
        return {
            'accuracy': self._convert_types(weights.get('accuracy', 0.4), float),
            'cost_efficiency': self._convert_types(weights.get('cost_efficiency', 0.25), float),
            'response_time': self._convert_types(weights.get('response_time', 0.2), float),
            'token_capacity': self._convert_types(weights.get('token_capacity', 0.1), float),
            'user_preference': self._convert_types(weights.get('user_preference', 0.05), float)
        }
    
    def get_complexity_multipliers(self) -> Dict[str, float]:
        """Get complexity multipliers for token estimation."""
        config = self._load_config_file()
        multipliers = config.get('complexity_multipliers', {})
        
        return {
            'simple': self._convert_types(multipliers.get('simple', 1.0), float),
            'moderate': self._convert_types(multipliers.get('moderate', 1.5), float),
            'complex': self._convert_types(multipliers.get('complex', 2.0), float),
            'expert': self._convert_types(multipliers.get('expert', 2.5), float),
            'real_time': self._convert_types(multipliers.get('real_time', 3.0), float)
        }
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance tracking configuration."""
        config = self._load_config_file()
        perf_config = config.get('performance', {})
        
        return {
            'enable_tracking': self._convert_types(perf_config.get('enable_tracking', True), bool),
            'metrics_retention_days': self._convert_types(perf_config.get('metrics_retention_days', 30), int),
            'circuit_breaker_threshold': self._convert_types(perf_config.get('circuit_breaker_threshold', 5), int),
            'circuit_breaker_timeout': self._convert_types(perf_config.get('circuit_breaker_timeout', 300), int)
        }
    
    def reload_config(self) -> bool:
        """Force reload the configuration from file."""
        self._config_cache = None
        self._last_modified = None
        config = self._load_config_file()
        return bool(config)


# Global instance for easy access
_config_loader = None

def get_ai_models_config_loader() -> AIModelsConfigLoader:
    """Get the global AI models configuration loader instance."""
    global _config_loader
    if _config_loader is None:
        _config_loader = AIModelsConfigLoader()
    return _config_loader
