"""
Base classes and interfaces for the /ask pipeline stages.

This module provides abstract base classes and interfaces that ensure
consistency across all pipeline components.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass
from enum import Enum


class ProcessingStage(Enum):
    """Enumeration of pipeline processing stages."""
    PREPROCESSING = "preprocessing"
    CORE_PROCESSING = "core_processing"
    POSTPROCESSING = "postprocessing"


@dataclass
class ProcessingContext:
    """Context object passed between pipeline stages."""
    user_id: str
    query: str
    stage: ProcessingStage
    metadata: Dict[str, Any]
    start_time: float
    stage_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.stage_data is None:
            self.stage_data = {}


@dataclass
class ProcessingResult:
    """Result object returned from pipeline stages."""
    success: bool
    data: Any
    error: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseProcessor(ABC):
    """Abstract base class for all pipeline processors."""
    
    def __init__(self, name: str):
        self.name = name
        self.stage = self._get_stage()
    
    @abstractmethod
    def _get_stage(self) -> ProcessingStage:
        """Return the processing stage this processor belongs to."""
        pass
    
    @abstractmethod
    async def process(self, context: ProcessingContext) -> ProcessingResult:
        """Process the given context and return a result."""
        pass
    
    async def pre_process(self, context: ProcessingContext) -> ProcessingContext:
        """Optional pre-processing hook."""
        return context
    
    async def post_process(self, context: ProcessingContext, result: ProcessingResult) -> ProcessingResult:
        """Optional post-processing hook."""
        return result


class BaseValidator(ABC):
    """Abstract base class for input validators."""
    
    @abstractmethod
    async def validate(self, data: Any) -> ProcessingResult:
        """Validate the given data and return a result."""
        pass
    
    @abstractmethod
    def get_validation_rules(self) -> Dict[str, Any]:
        """Return the validation rules for this validator."""
        pass


class BaseFormatter(ABC):
    """Abstract base class for formatters."""
    
    @abstractmethod
    async def format(self, data: Any, context: ProcessingContext) -> ProcessingResult:
        """Format the given data according to context and return a result."""
        pass


class BaseClient(ABC):
    """Abstract base class for external service clients."""
    
    @abstractmethod
    async def call(self, *args, **kwargs) -> ProcessingResult:
        """Make a call to the external service."""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the service is available."""
        pass


class BaseCache(ABC):
    """Abstract base class for caching implementations."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Retrieve a value from cache."""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Store a value in cache."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete a value from cache."""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cached values."""
        pass


class BaseRateLimiter(ABC):
    """Abstract base class for rate limiting implementations."""
    
    @abstractmethod
    async def is_allowed(self, key: str) -> bool:
        """Check if the request is allowed."""
        pass
    
    @abstractmethod
    async def record_request(self, key: str) -> None:
        """Record a request for rate limiting."""
        pass
    
    @abstractmethod
    def get_limits(self) -> Dict[str, Any]:
        """Get the current rate limiting configuration."""
        pass 