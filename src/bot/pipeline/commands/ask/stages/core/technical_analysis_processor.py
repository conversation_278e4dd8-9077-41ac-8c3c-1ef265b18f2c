"""
Enhanced Technical Analysis Processor for the /ask pipeline.

This module extracts the technical analysis logic from ai_chat_processor.py
including indicator calculations, data processing, quality assessment, and data enhancement.
"""

import logging
import time
from typing import Dict, Any, List, Optional, Union
from ..config import get_config

logger = logging.getLogger(__name__)


class TechnicalAnalysisProcessor:
    """Enhanced technical analysis processor with data quality assessment and enhancement."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or get_config()
        self._enhancements_enabled = getattr(self.config, 'enable_technical_analysis_enhancements', True)
        
        # Performance tracking
        self.total_analyses = 0
        self.successful_analyses = 0
        self.failed_analyses = 0
        self.avg_processing_time = 0.0
        
        # Quality thresholds
        self.quality_thresholds = {
            'excellent': 80,
            'good': 60,
            'fair': 40,
            'poor': 20
        }
        
        logger.info("Enhanced technical analysis processor initialized")
    
    def calculate_technical_indicators_quality(self, indicators: Dict[str, Any]) -> int:
        """
        Calculate quality score for technical indicators.
        
        This replaces the monolithic _calculate_technical_indicators_quality method from ai_chat_processor.py
        
        Args:
            indicators: Dictionary containing technical indicators
            
        Returns:
            Quality score (0-100)
        """
        if not self._enhancements_enabled:
            return self._legacy_calculate_quality(indicators)
        
        try:
            start_time = time.time()
            self.total_analyses += 1
            
            quality_score = self._enhanced_quality_assessment(indicators)
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self._update_performance_metrics(processing_time, True)
            
            logger.debug(f"✅ Technical indicators quality calculated: {quality_score}/100")
            return quality_score
            
        except Exception as e:
            logger.error(f"❌ Enhanced quality calculation failed: {e}")
            self._update_performance_metrics(0, False)
            # Fall back to legacy method
            return self._legacy_calculate_quality(indicators)
    
    def _enhanced_quality_assessment(self, indicators: Dict[str, Any]) -> int:
        """Enhanced quality assessment with multiple factors."""
        if not indicators:
            return 0
        
        total_score = 0
        max_possible_score = 0
        
        # Factor 1: Data completeness (30 points)
        completeness_score = self._assess_data_completeness(indicators)
        total_score += completeness_score * 0.3
        max_possible_score += 30
        
        # Factor 2: Data freshness (25 points)
        freshness_score = self._assess_data_freshness(indicators)
        total_score += freshness_score * 0.25
        max_possible_score += 25
        
        # Factor 3: Data consistency (25 points)
        consistency_score = self._assess_data_consistency(indicators)
        total_score += consistency_score * 0.25
        max_possible_score += 25
        
        # Factor 4: Data accuracy (20 points)
        accuracy_score = self._assess_data_accuracy(indicators)
        total_score += accuracy_score * 0.2
        max_possible_score += 20
        
        # Calculate final score
        final_score = int((total_score / max_possible_score) * 100)
        return min(100, max(0, final_score))
    
    def _assess_data_completeness(self, indicators: Dict[str, Any]) -> int:
        """Assess how complete the technical indicators data is."""
        required_indicators = ['rsi', 'macd', 'sma', 'ema', 'bollinger_bands']
        available_indicators = [key for key in indicators.keys() if key in required_indicators]
        
        completeness_ratio = len(available_indicators) / len(required_indicators)
        return int(completeness_ratio * 100)
    
    def _assess_data_freshness(self, indicators: Dict[str, Any]) -> int:
        """Assess how fresh the technical indicators data is."""
        timestamp = indicators.get('timestamp')
        if not timestamp:
            return 50  # Default score if no timestamp
        
        try:
            # Calculate age in minutes
            data_time = float(timestamp)
            age_minutes = (time.time() - data_time) / 60
            
            if age_minutes < 5:
                return 100  # Very fresh
            elif age_minutes < 15:
                return 80   # Fresh
            elif age_minutes < 30:
                return 60   # Moderately fresh
            elif age_minutes < 60:
                return 40   # Stale
            else:
                return 20   # Very stale
        except:
            return 50  # Default score if timestamp parsing fails
    
    def _assess_data_consistency(self, indicators: Dict[str, Any]) -> int:
        """Assess how consistent the technical indicators data is."""
        # Check for logical consistency in indicator values
        consistency_score = 100
        
        # RSI should be between 0 and 100
        rsi = indicators.get('rsi')
        if rsi is not None and (rsi < 0 or rsi > 100):
            consistency_score -= 20
        
        # MACD components should be reasonable
        macd = indicators.get('macd', {})
        if isinstance(macd, dict):
            macd_line = macd.get('macd_line', 0)
            signal_line = macd.get('signal_line', 0)
            if abs(macd_line) > 1000 or abs(signal_line) > 1000:
                consistency_score -= 15
        
        # Moving averages should be positive
        sma = indicators.get('sma', {})
        if isinstance(sma, dict):
            for period, value in sma.items():
                if value and value <= 0:
                    consistency_score -= 10
                    break
        
        return max(0, consistency_score)
    
    def _assess_data_accuracy(self, indicators: Dict[str, Any]) -> int:
        """Assess the accuracy of technical indicators data."""
        accuracy_score = 100
        
        # Check for missing or invalid values
        for key, value in indicators.items():
            if value is None or value == "":
                accuracy_score -= 5
            elif isinstance(value, (int, float)) and (value < -1000000 or value > 1000000):
                accuracy_score -= 10
        
        return max(0, accuracy_score)
    
    def _legacy_calculate_quality(self, indicators: Dict[str, Any]) -> int:
        """Legacy quality calculation method."""
        if not indicators:
            return 0
        
        # Simple quality assessment based on data availability
        quality_score = 0
        
        # Basic indicators (20 points each)
        if 'rsi' in indicators:
            quality_score += 20
        if 'macd' in indicators:
            quality_score += 20
        if 'sma' in indicators:
            quality_score += 20
        if 'ema' in indicators:
            quality_score += 20
        if 'bollinger_bands' in indicators:
            quality_score += 20
        
        return quality_score
    
    def process_comprehensive_data(self, comprehensive_data: Any, symbol: str, 
                                 tools_required: List[str]) -> Dict[str, Any]:
        """
        Process comprehensive market data for a symbol.
        
        This replaces the monolithic _process_comprehensive_data method from ai_chat_processor.py
        
        Args:
            comprehensive_data: Raw comprehensive data from market service
            symbol: Stock symbol
            tools_required: List of required analysis tools
            
        Returns:
            Processed data dictionary
        """
        if not self._enhancements_enabled:
            return self._legacy_process_comprehensive_data(comprehensive_data, symbol, tools_required)
        
        try:
            start_time = time.time()
            
            processed_data = self._enhanced_data_processing(comprehensive_data, symbol, tools_required)
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self._update_performance_metrics(processing_time, True)
            
            logger.debug(f"✅ Comprehensive data processed for {symbol}")
            return processed_data
            
        except Exception as e:
            logger.error(f"❌ Enhanced data processing failed for {symbol}: {e}")
            self._update_performance_metrics(0, False)
            # Fall back to legacy method
            return self._legacy_process_comprehensive_data(comprehensive_data, symbol, tools_required)
    
    def _enhanced_data_processing(self, comprehensive_data: Any, symbol: str, 
                                tools_required: List[str]) -> Dict[str, Any]:
        """Enhanced data processing with validation and enhancement."""
        processed_data = {
            'symbol': symbol,
            'data_available': True,
            'timestamp': time.time(),
            'tools_required': tools_required,
            'processing_mode': 'enhanced'
        }
        
        # Extract and validate price data
        if hasattr(comprehensive_data, 'get'):
            # Handle dictionary-like objects
            price_data = comprehensive_data.get('current_price') or comprehensive_data.get('price')
            if price_data:
                processed_data['current_price'] = float(price_data)
            
            change_data = comprehensive_data.get('change_percent') or comprehensive_data.get('change')
            if change_data:
                processed_data['change_percent'] = float(change_data)
            
            volume_data = comprehensive_data.get('volume')
            if volume_data:
                processed_data['volume'] = int(volume_data)
        else:
            # Handle object attributes
            try:
                processed_data['current_price'] = getattr(comprehensive_data, 'current_price', None) or getattr(comprehensive_data, 'price', None)
                processed_data['change_percent'] = getattr(comprehensive_data, 'change_percent', None) or getattr(comprehensive_data, 'change', None)
                processed_data['volume'] = getattr(comprehensive_data, 'volume', None)
            except:
                pass
        
        # Add technical indicators if available
        if 'technical_indicators' in tools_required:
            technical_data = self._extract_technical_indicators(comprehensive_data)
            if technical_data:
                processed_data['technical_indicators'] = technical_data
        
        # Add market context if available
        if 'market_context' in tools_required:
            market_data = self._extract_market_context(comprehensive_data)
            if market_data:
                processed_data['market_context'] = market_data
        
        # Calculate data quality score
        quality_score = self.calculate_technical_indicators_quality(processed_data)
        processed_data['data_quality'] = quality_score
        
        return processed_data
    
    def _extract_technical_indicators(self, data: Any) -> Optional[Dict[str, Any]]:
        """Extract technical indicators from data."""
        indicators = {}
        
        # Try to extract RSI
        rsi = getattr(data, 'rsi', None) or (data.get('rsi') if hasattr(data, 'get') else None)
        if rsi is not None:
            indicators['rsi'] = float(rsi)
        
        # Try to extract MACD
        macd = getattr(data, 'macd', None) or (data.get('macd') if hasattr(data, 'get') else None)
        if macd is not None:
            if isinstance(macd, dict):
                indicators['macd'] = macd
            else:
                indicators['macd'] = {'macd_line': float(macd)}
        
        # Try to extract moving averages
        sma = getattr(data, 'sma', None) or (data.get('sma') if hasattr(data, 'get') else None)
        if sma is not None:
            indicators['sma'] = sma
        
        ema = getattr(data, 'ema', None) or (data.get('ema') if hasattr(data, 'get') else None)
        if ema is not None:
            indicators['ema'] = ema
        
        return indicators if indicators else None
    
    def _extract_market_context(self, data: Any) -> Optional[Dict[str, Any]]:
        """Extract market context from data."""
        context = {}
        
        # Try to extract market status
        market_status = getattr(data, 'market_status', None) or (data.get('market_status') if hasattr(data, 'get') else None)
        if market_status is not None:
            context['market_status'] = market_status
        
        # Try to extract sector information
        sector = getattr(data, 'sector', None) or (data.get('sector') if hasattr(data, 'get') else None)
        if sector is not None:
            context['sector'] = sector
        
        return context if context else None
    
    def _legacy_process_comprehensive_data(self, comprehensive_data: Any, symbol: str, 
                                         tools_required: List[str]) -> Dict[str, Any]:
        """Legacy data processing method."""
        processed_data = {
            'symbol': symbol,
            'data_available': True,
            'timestamp': time.time(),
            'tools_required': tools_required,
            'processing_mode': 'legacy'
        }
        
        # Basic data extraction
        if hasattr(comprehensive_data, 'get'):
            processed_data['current_price'] = comprehensive_data.get('current_price')
            processed_data['change_percent'] = comprehensive_data.get('change_percent')
            processed_data['volume'] = comprehensive_data.get('volume')
        else:
            processed_data['current_price'] = getattr(comprehensive_data, 'current_price', None)
            processed_data['change_percent'] = getattr(comprehensive_data, 'change_percent', None)
            processed_data['volume'] = getattr(comprehensive_data, 'volume', None)
        
        # Calculate basic quality score
        quality_score = self._legacy_calculate_quality(processed_data)
        processed_data['data_quality'] = quality_score
        
        return processed_data
    
    def process_price_data_response(self, current_data_response: Any, symbol: str, 
                                  tools_required: List[str]) -> Dict[str, Any]:
        """
        Process price data response for a symbol.
        
        This replaces the monolithic _process_price_data_response method from ai_chat_processor.py
        
        Args:
            current_data_response: Raw price data response
            symbol: Stock symbol
            tools_required: List of required analysis tools
            
        Returns:
            Processed price data dictionary
        """
        if not self._enhancements_enabled:
            return self._legacy_process_price_data_response(current_data_response, symbol, tools_required)
        
        try:
            start_time = time.time()
            
            processed_data = self._enhanced_price_data_processing(current_data_response, symbol, tools_required)
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self._update_performance_metrics(processing_time, True)
            
            logger.debug(f"✅ Price data processed for {symbol}")
            return processed_data
            
        except Exception as e:
            logger.error(f"❌ Enhanced price data processing failed for {symbol}: {e}")
            self._update_performance_metrics(0, False)
            # Fall back to legacy method
            return self._legacy_process_price_data_response(current_data_response, symbol, tools_required)
    
    def _enhanced_price_data_processing(self, current_data_response: Any, symbol: str, 
                                      tools_required: List[str]) -> Dict[str, Any]:
        """Enhanced price data processing with validation."""
        processed_data = {
            'symbol': symbol,
            'data_available': True,
            'timestamp': time.time(),
            'tools_required': tools_required,
            'processing_mode': 'enhanced'
        }
        
        # Extract price information
        if hasattr(current_data_response, 'get'):
            # Handle dictionary-like objects
            price = current_data_response.get('price') or current_data_response.get('current_price')
            if price:
                processed_data['current_price'] = float(price)
            
            change = current_data_response.get('change') or current_data_response.get('change_percent')
            if change:
                processed_data['change_percent'] = float(change)
        else:
            # Handle object attributes
            try:
                processed_data['current_price'] = getattr(current_data_response, 'price', None) or getattr(current_data_response, 'current_price', None)
                processed_data['change_percent'] = getattr(current_data_response, 'change', None) or getattr(current_data_response, 'change_percent', None)
            except:
                pass
        
        # Calculate data quality score
        quality_score = self.calculate_technical_indicators_quality(processed_data)
        processed_data['data_quality'] = quality_score
        
        return processed_data
    
    def _legacy_process_price_data_response(self, current_data_response: Any, symbol: str, 
                                          tools_required: List[str]) -> Dict[str, Any]:
        """Legacy price data processing method."""
        processed_data = {
            'symbol': symbol,
            'data_available': True,
            'timestamp': time.time(),
            'tools_required': tools_required,
            'processing_mode': 'legacy'
        }
        
        # Basic data extraction
        if hasattr(current_data_response, 'get'):
            processed_data['current_price'] = current_data_response.get('price')
            processed_data['change_percent'] = current_data_response.get('change')
        else:
            processed_data['current_price'] = getattr(current_data_response, 'price', None)
            processed_data['change_percent'] = getattr(current_data_response, 'change', None)
        
        # Calculate basic quality score
        quality_score = self._legacy_calculate_quality(processed_data)
        processed_data['data_quality'] = quality_score
        
        return processed_data
    
    def add_technical_indicators_to_data(self, symbol_data: Dict[str, Any], 
                                       technical_indicators: Any) -> None:
        """
        Add technical indicators to symbol data.
        
        This replaces the monolithic _add_technical_indicators_to_data method from ai_chat_processor.py
        
        Args:
            symbol_data: Symbol data dictionary to enhance
            technical_indicators: Technical indicators data
        """
        if not self._enhancements_enabled:
            return self._legacy_add_technical_indicators(symbol_data, technical_indicators)
        
        try:
            self._enhanced_add_technical_indicators(symbol_data, technical_indicators)
            logger.debug("✅ Technical indicators added to symbol data using enhanced method")
        except Exception as e:
            logger.error(f"❌ Enhanced technical indicators addition failed: {e}")
            # Fall back to legacy method
            self._legacy_add_technical_indicators(symbol_data, technical_indicators)
    
    def _enhanced_add_technical_indicators(self, symbol_data: Dict[str, Any], 
                                         technical_indicators: Any) -> None:
        """Enhanced method to add technical indicators with validation."""
        if not technical_indicators:
            return
        
        # Extract indicators with validation
        indicators = {}
        
        # RSI
        rsi = self._safe_extract_indicator(technical_indicators, 'rsi')
        if rsi is not None:
            indicators['rsi'] = rsi
        
        # MACD
        macd = self._safe_extract_indicator(technical_indicators, 'macd')
        if macd is not None:
            indicators['macd'] = macd
        
        # Moving averages
        sma = self._safe_extract_indicator(technical_indicators, 'sma')
        if sma is not None:
            indicators['sma'] = sma
        
        ema = self._safe_extract_indicator(technical_indicators, 'ema')
        if ema is not None:
            indicators['ema'] = ema
        
        # Bollinger Bands
        bb = self._safe_extract_indicator(technical_indicators, 'bollinger_bands')
        if bb is not None:
            indicators['bollinger_bands'] = bb
        
        # Add to symbol data if we have indicators
        if indicators:
            symbol_data['technical_indicators'] = indicators
            symbol_data['indicators_count'] = len(indicators)
    
    def _safe_extract_indicator(self, data: Any, indicator_name: str) -> Any:
        """Safely extract indicator value from data."""
        try:
            if hasattr(data, 'get'):
                return data.get(indicator_name)
            else:
                return getattr(data, indicator_name, None)
        except:
            return None
    
    def _legacy_add_technical_indicators(self, symbol_data: Dict[str, Any], 
                                       technical_indicators: Any) -> None:
        """Legacy method to add technical indicators."""
        if not technical_indicators:
            return
        
        # Basic indicator addition
        if hasattr(technical_indicators, 'get'):
            # Handle dictionary-like objects
            for key, value in technical_indicators.items():
                if value is not None:
                    symbol_data[key] = value
        else:
            # Handle object attributes
            for attr in ['rsi', 'macd', 'sma', 'ema', 'bollinger_bands']:
                try:
                    value = getattr(technical_indicators, attr, None)
                    if value is not None:
                        symbol_data[attr] = value
                except:
                    pass
    
    def _update_performance_metrics(self, processing_time: float, success: bool) -> None:
        """Update performance tracking metrics."""
        if success:
            self.successful_analyses += 1
            # Update average processing time
            if self.successful_analyses == 1:
                self.avg_processing_time = processing_time
            else:
                self.avg_processing_time = (
                    (self.avg_processing_time * (self.successful_analyses - 1) + processing_time) 
                    / self.successful_analyses
                )
        else:
            self.failed_analyses += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring."""
        total_analyses = self.total_analyses
        success_rate = (self.successful_analyses / total_analyses * 100) if total_analyses > 0 else 0
        
        return {
            'total_analyses': total_analyses,
            'successful_analyses': self.successful_analyses,
            'failed_analyses': self.failed_analyses,
            'success_rate': success_rate,
            'avg_processing_time': self.avg_processing_time,
            'enhancements_enabled': self._enhancements_enabled,
            'quality_thresholds': self.quality_thresholds
        }
    
    def toggle_enhancements(self, enable: bool) -> None:
        """Toggle enhancement features on/off."""
        self._enhancements_enabled = enable
        logger.info(f"Technical analysis processor enhancements {'enabled' if enable else 'disabled'}")
    
    def reset_metrics(self) -> None:
        """Reset performance metrics."""
        self.total_analyses = 0
        self.successful_analyses = 0
        self.failed_analyses = 0
        self.avg_processing_time = 0.0
        logger.info("Technical analysis processor metrics reset")
    
    def update_quality_thresholds(self, new_thresholds: Dict[str, int]) -> None:
        """Update quality assessment thresholds."""
        self.quality_thresholds.update(new_thresholds)
        logger.info(f"Quality thresholds updated: {new_thresholds}")


# Global instance for easy access
technical_analysis_processor = TechnicalAnalysisProcessor() 