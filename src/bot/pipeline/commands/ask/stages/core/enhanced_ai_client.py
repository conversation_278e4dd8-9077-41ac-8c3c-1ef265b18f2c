"""
Enhanced AI Client for the /ask pipeline.

This module extracts the complex AI model calling logic from ai_chat_processor.py
including retry logic, timeout handling, token tracking, and fallback strategies.
"""

import logging
import asyncio
import time
from typing import Dict, Any, Optional, List
from openai import OpenAI
from ..config import get_config
import os

logger = logging.getLogger(__name__)


class EnhancedAIClient:
    """Enhanced AI client with robust retry logic and monitoring."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or get_config()
        self.client = None
        # Fix config access - use direct attributes instead of nested pipeline dict
        self.model = getattr(self.config, 'model', 'moonshotai/kimi-k2:free')
        self.temperature = getattr(self.config, 'temperature', 0.7)
        self.max_tokens = getattr(self.config, 'max_tokens', 2000)
        self.ai_timeout = getattr(self.config, 'ai_timeout', 30.0)
        
        # Retry configuration
        self.max_attempts = 3
        self.attempt_timeout_multiplier = 0.5  # Each attempt gets shorter timeout
        
        # Performance tracking
        self.total_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        self.total_tokens_used = 0
        self.avg_response_time = 0.0
        
        # Initialize client if possible
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize the OpenAI client if API key is available."""
        try:
            # Check for API key in environment or config
            api_key = os.getenv('OPENAI_API_KEY') or getattr(self.config, 'api_key', None)
            if api_key:
                self.client = OpenAI(api_key=api_key)
                logger.info("Enhanced AI client initialized successfully")
            else:
                logger.warning("No API key found - enhanced AI client will use fallbacks")
        except Exception as e:
            logger.error(f"Failed to initialize enhanced AI client: {e}")
            self.client = None
    
    async def call_model(self, query: str, system_prompt: str, correlation_id: str = None) -> Dict[str, Any]:
        """
        Enhanced AI model calling with robust retry logic and monitoring.
        
        This replaces the monolithic _call_ai_model method from ai_chat_processor.py
        
        Args:
            query: User query to send to AI
            system_prompt: System prompt for the AI
            correlation_id: Optional correlation ID for tracing
            
        Returns:
            Dictionary with AI response and metadata
        """
        start_time = time.time()
        self.total_calls += 1
        
        try:
            # Check if client is available
            if not self.client:
                logger.warning(f"[{correlation_id}] AI client not configured - returning fallback")
                return self._create_fallback_response("no_ai_config", correlation_id)
            
            # Prepare messages
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": query},
            ]
            
            # Attempt AI call with retries
            result = await self._attempt_ai_call_with_retries(
                messages, correlation_id, start_time
            )
            
            # Update success metrics
            self.successful_calls += 1
            response_time = time.time() - start_time
            self._update_performance_metrics(response_time, result.get('token_usage', {}))
            
            logger.info(f"✅ [{correlation_id}] Enhanced AI call successful in {response_time:.2f}s")
            return result
            
        except Exception as e:
            # Update failure metrics
            self.failed_calls += 1
            response_time = time.time() - start_time
            
            logger.error(f"❌ [{correlation_id}] Enhanced AI call failed after {response_time:.2f}s: {e}")
            
            # Return fallback response
            return self._create_fallback_response("ai_call_failed", correlation_id, error=str(e))
    
    async def _attempt_ai_call_with_retries(self, messages: List[Dict], correlation_id: str, start_time: float) -> Dict[str, Any]:
        """Attempt AI call with exponential backoff and timeout handling."""
        last_error = None
        operation_timeout = self.ai_timeout * 1.5
        
        for attempt in range(1, self.max_attempts + 1):
            try:
                # Calculate timeout for this attempt
                attempt_timeout = min(
                    operation_timeout * (self.attempt_timeout_multiplier ** (attempt - 1)),
                    15.0  # Cap at 15 seconds per attempt
                )
                
                logger.info(f"🤖 [{correlation_id}] AI call attempt {attempt}/{self.max_attempts} "
                          f"with {attempt_timeout:.1f}s timeout")
                
                # Execute AI call with timeout
                completion = await self._execute_ai_call_with_timeout(
                    messages, attempt_timeout, correlation_id
                )
                
                # Parse and validate response
                result = self._parse_ai_response(completion, correlation_id)
                
                # Add metadata
                result['attempts'] = attempt
                result['total_time'] = time.time() - start_time
                result['correlation_id'] = correlation_id
                
                return result
                
            except asyncio.TimeoutError as e:
                last_error = e
                logger.warning(f"⏰ [{correlation_id}] AI call timed out after {attempt_timeout:.1f}s "
                             f"(attempt {attempt}/{self.max_attempts})")
                
                if attempt == self.max_attempts:
                    raise TimeoutError(f"AI call timed out after {self.max_attempts} attempts")
                    
            except Exception as e:
                last_error = e
                logger.warning(f"⚠️ [{correlation_id}] AI call attempt {attempt} failed: {e}")
                
                if attempt == self.max_attempts:
                    raise e
                
                # Brief delay before retry
                await asyncio.sleep(0.5 * attempt)
        
        # Should never reach here, but just in case
        raise last_error or Exception("AI call failed after all attempts")
    
    async def _execute_ai_call_with_timeout(self, messages: List[Dict], timeout: float, correlation_id: str) -> Any:
        """Execute AI call with proper timeout handling."""
        try:
            # Use asyncio to handle the timeout
            completion = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.client.chat.completions.create(
                        model=self.model,
                        messages=messages,
                        temperature=self.temperature,
                        max_tokens=self.max_tokens,
                    )
                ),
                timeout=timeout
            )
            
            return completion
            
        except asyncio.TimeoutError:
            raise TimeoutError(f"AI call timed out after {timeout:.1f}s")
        except Exception as e:
            logger.error(f"❌ [{correlation_id}] AI call execution failed: {e}")
            raise e
    
    def _parse_ai_response(self, completion: Any, correlation_id: str) -> Dict[str, Any]:
        """Parse AI response and extract content and token usage."""
        # Extract token usage if available
        token_usage = {}
        if hasattr(completion, 'usage') and completion.usage:
            token_usage = {
                'prompt_tokens': completion.usage.prompt_tokens,
                'completion_tokens': completion.usage.completion_tokens,
                'total_tokens': completion.usage.total_tokens
            }
            logger.info(f"📊 [{correlation_id}] Token usage: {token_usage}")
        
        # Extract content from response
        content = ""
        if completion and getattr(completion, 'choices', None):
            try:
                # OpenAI v1 SDK style
                content = completion.choices[0].message.content
            except Exception:
                # Fallback for any structural differences
                first = completion.choices[0]
                content = getattr(getattr(first, 'message', {}), 'content', "") or getattr(first, 'text', "")
        
        # Log response content for debugging
        logger.info(f"🔍 [{correlation_id}] Raw AI response: {content[:500]}...")
        logger.debug(f"🔍 [{correlation_id}] Full AI response: {content}")
        
        # Attempt to parse JSON from response
        parsed_data = self._extract_json_from_response(content, correlation_id)
        
        return {
            'content': content,
            'parsed_data': parsed_data,
            'token_usage': token_usage,
            'raw_completion': completion
        }
    
    def _extract_json_from_response(self, content: str, correlation_id: str) -> Dict[str, Any]:
        """Extract JSON from AI response using robust parsing."""
        try:
            import re
            import json
            
            text = content.strip()
            
            # Extract JSON from fenced code blocks using robust regex
            json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
            matches = re.findall(json_pattern, text, re.DOTALL)
            
            if matches:
                # Try to parse the first JSON match
                json_str = matches[0]
                parsed = json.loads(json_str)
                logger.info(f"✅ [{correlation_id}] Successfully extracted JSON from response")
                return parsed
            
            # Fallback: try to find JSON anywhere in the text
            json_pattern_fallback = r'\{[^{}]*\}'
            matches_fallback = re.findall(json_pattern_fallback, text, re.DOTALL)
            
            if matches_fallback:
                # Try the longest match
                longest_match = max(matches_fallback, key=len)
                try:
                    parsed = json.loads(longest_match)
                    logger.info(f"✅ [{correlation_id}] Successfully extracted JSON using fallback pattern")
                    return parsed
                except json.JSONDecodeError:
                    pass
            
            # If no JSON found, return empty dict
            logger.warning(f"⚠️ [{correlation_id}] No valid JSON found in AI response")
            return {}
            
        except Exception as e:
            logger.error(f"❌ [{correlation_id}] JSON extraction failed: {e}")
            return {}
    
    def _create_fallback_response(self, fallback_type: str, correlation_id: str, error: str = None) -> Dict[str, Any]:
        """Create appropriate fallback response based on failure type."""
        fallback_responses = {
            "no_ai_config": {
                "intent": "general_question",
                "symbols": [],
                "tools_required": [],
                "needs_data": False,
                "response": "I'm currently unable to process your request due to configuration issues. Please try again later.",
                "fallback_type": fallback_type
            },
            "ai_call_failed": {
                "intent": "general_question", 
                "symbols": [],
                "tools_required": [],
                "needs_data": False,
                "response": f"I'm experiencing technical difficulties: {error}. Please try again later.",
                "fallback_type": fallback_type,
                "error": error
            }
        }
        
        response = fallback_responses.get(fallback_type, fallback_responses["ai_call_failed"])
        response['correlation_id'] = correlation_id
        response['timestamp'] = time.time()
        
        return response
    
    def _update_performance_metrics(self, response_time: float, token_usage: Dict[str, int]) -> None:
        """Update performance tracking metrics."""
        # Update response time metrics
        if self.successful_calls == 1:
            self.avg_response_time = response_time
        else:
            self.avg_response_time = (
                (self.avg_response_time * (self.successful_calls - 1) + response_time) 
                / self.successful_calls
            )
        
        # Update token usage
        if token_usage:
            self.total_tokens_used += token_usage.get('total_tokens', 0)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring."""
        total_calls = self.total_calls
        success_rate = (self.successful_calls / total_calls * 100) if total_calls > 0 else 0
        
        return {
            'total_calls': total_calls,
            'successful_calls': self.successful_calls,
            'failed_calls': self.failed_calls,
            'success_rate': success_rate,
            'avg_response_time': self.avg_response_time,
            'total_tokens_used': self.total_tokens_used,
            'model': self.model,
            'max_attempts': self.max_attempts,
            'ai_timeout': self.ai_timeout
        }
    
    def reset_metrics(self) -> None:
        """Reset performance metrics."""
        self.total_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        self.total_tokens_used = 0
        self.avg_response_time = 0.0
        logger.info("Enhanced AI client metrics reset")
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update client configuration."""
        if 'model' in new_config:
            self.model = new_config['model']
        if 'temperature' in new_config:
            self.temperature = new_config['temperature']
        if 'max_tokens' in new_config:
            self.max_tokens = new_config['max_tokens']
        if 'timeout' in new_config:
            self.ai_timeout = new_config['timeout']
        
        logger.info(f"Enhanced AI client config updated: {new_config}")


# Global instance for easy access
enhanced_ai_client = EnhancedAIClient() 