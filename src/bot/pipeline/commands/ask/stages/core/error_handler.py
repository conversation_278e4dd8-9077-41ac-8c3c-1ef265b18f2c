"""
Centralized error handling for the /ask pipeline.

This module provides consistent error handling, logging, and fallback mechanisms
across all pipeline stages.
"""

import logging
import traceback
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass
from enum import Enum

from .base import ProcessingContext, ProcessingResult, ProcessingStage


class ErrorSeverity(Enum):
    """Enumeration of error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorType(Enum):
    """Enumeration of error types."""
    VALIDATION_ERROR = "validation_error"
    AI_SERVICE_ERROR = "ai_service_error"
    DATA_ERROR = "data_error"
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"


@dataclass
class PipelineError:
    """Structured error information for pipeline failures."""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    details: Optional[Dict[str, Any]] = None
    original_exception: Optional[Exception] = None
    context: Optional[ProcessingContext] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}


class ErrorHandler:
    """Centralized error handler for the pipeline."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.error_counts: Dict[ErrorType, int] = {error_type: 0 for error_type in ErrorType}
        self.fallback_strategies: Dict[ErrorType, str] = self._get_default_fallback_strategies()
    
    def _get_default_fallback_strategies(self) -> Dict[ErrorType, str]:
        """Get default fallback strategies for each error type."""
        return {
            ErrorType.VALIDATION_ERROR: "return_validation_error",
            ErrorType.AI_SERVICE_ERROR: "use_fallback_ai_service",
            ErrorType.DATA_ERROR: "use_cached_data",
            ErrorType.NETWORK_ERROR: "retry_with_backoff",
            ErrorType.TIMEOUT_ERROR: "use_fallback_service",
            ErrorType.UNKNOWN_ERROR: "return_generic_error"
        }
    
    def handle_error(
        self,
        error: Exception,
        context: ProcessingContext,
        error_type: Optional[ErrorType] = None,
        severity: Optional[ErrorSeverity] = None
    ) -> ProcessingResult:
        """Handle an error and return an appropriate result."""
        # Determine error type if not provided
        if error_type is None:
            error_type = self._classify_error(error)
        
        # Determine severity if not provided
        if severity is None:
            severity = self._determine_severity(error, error_type)
        
        # Create structured error
        pipeline_error = PipelineError(
            error_type=error_type,
            severity=severity,
            message=str(error),
            details=self._extract_error_details(error),
            original_exception=error,
            context=context
        )
        
        # Log the error
        self._log_error(pipeline_error)
        
        # Update error counts
        self.error_counts[error_type] += 1
        
        # Get fallback strategy
        fallback_strategy = self.fallback_strategies.get(error_type, "return_generic_error")
        
        # Execute fallback strategy
        return self._execute_fallback_strategy(fallback_strategy, pipeline_error)
    
    def _classify_error(self, error: Exception) -> ErrorType:
        """Classify the error based on its type."""
        error_class = type(error).__name__
        
        if "ValidationError" in error_class or "ValueError" in error_class:
            return ErrorType.VALIDATION_ERROR
        elif "AI" in error_class or "OpenAI" in error_class or "Anthropic" in error_class:
            return ErrorType.AI_SERVICE_ERROR
        elif "DataError" in error_class or "KeyError" in error_class:
            return ErrorType.DATA_ERROR
        elif "ConnectionError" in error_class or "NetworkError" in error_class:
            return ErrorType.NETWORK_ERROR
        elif "TimeoutError" in error_class or "asyncio.TimeoutError" in error_class:
            return ErrorType.TIMEOUT_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR
    
    def _determine_severity(self, error: Exception, error_type: ErrorType) -> ErrorSeverity:
        """Determine the severity of an error."""
        if error_type == ErrorType.VALIDATION_ERROR:
            return ErrorSeverity.LOW
        elif error_type in [ErrorType.DATA_ERROR, ErrorType.TIMEOUT_ERROR]:
            return ErrorSeverity.MEDIUM
        elif error_type in [ErrorType.AI_SERVICE_ERROR, ErrorType.NETWORK_ERROR]:
            return ErrorSeverity.HIGH
        else:
            return ErrorSeverity.CRITICAL
    
    def _extract_error_details(self, error: Exception) -> Dict[str, Any]:
        """Extract additional details from an error."""
        details = {
            "error_class": type(error).__name__,
            "error_module": type(error).__module__,
            "traceback": traceback.format_exc()
        }
        
        # Add custom attributes if they exist
        if hasattr(error, 'details'):
            details['custom_details'] = error.details
        if hasattr(error, 'code'):
            details['error_code'] = error.code
        
        return details
    
    def _log_error(self, pipeline_error: PipelineError) -> None:
        """Log the error with appropriate level."""
        log_message = f"Pipeline Error [{pipeline_error.error_type.value}]: {pipeline_error.message}"
        
        if pipeline_error.severity == ErrorSeverity.LOW:
            self.logger.warning(log_message, extra=pipeline_error.details)
        elif pipeline_error.severity == ErrorSeverity.MEDIUM:
            self.logger.error(log_message, extra=pipeline_error.details)
        elif pipeline_error.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, extra=pipeline_error.details)
        else:  # CRITICAL
            self.logger.critical(log_message, extra=pipeline_error.details)
    
    def _execute_fallback_strategy(self, strategy: str, pipeline_error: PipelineError) -> ProcessingResult:
        """Execute the appropriate fallback strategy."""
        if strategy == "return_validation_error":
            return self._return_validation_error(pipeline_error)
        elif strategy == "use_fallback_ai_service":
            return self._use_fallback_ai_service(pipeline_error)
        elif strategy == "use_cached_data":
            return self._use_cached_data(pipeline_error)
        elif strategy == "retry_with_backoff":
            return self._retry_with_backoff(pipeline_error)
        elif strategy == "use_fallback_service":
            return self._use_fallback_service(pipeline_error)
        else:
            return self._return_generic_error(pipeline_error)
    
    def _return_validation_error(self, pipeline_error: PipelineError) -> ProcessingResult:
        """Return a validation error response."""
        return ProcessingResult(
            success=False,
            data={
                "error": "validation_error",
                "message": pipeline_error.message,
                "details": pipeline_error.details
            },
            error=pipeline_error.message,
            metadata={"error_type": pipeline_error.error_type.value}
        )
    
    def _use_fallback_ai_service(self, pipeline_error: PipelineError) -> ProcessingResult:
        """Attempt to use a fallback AI service."""
        # This would integrate with the fallback AI service
        return ProcessingResult(
            success=False,
            data={
                "error": "ai_service_unavailable",
                "message": "AI service is currently unavailable. Please try again later.",
                "fallback_attempted": True
            },
            error="AI service unavailable",
            metadata={"error_type": pipeline_error.error_type.value}
        )
    
    def _use_cached_data(self, pipeline_error: PipelineError) -> ProcessingResult:
        """Attempt to use cached data as fallback."""
        return ProcessingResult(
            success=False,
            data={
                "error": "data_unavailable",
                "message": "Requested data is currently unavailable. Please try again later.",
                "fallback_attempted": True
            },
            error="Data unavailable",
            metadata={"error_type": pipeline_error.error_type.value}
        )
    
    def _retry_with_backoff(self, pipeline_error: PipelineError) -> ProcessingResult:
        """Indicate that retry with backoff should be attempted."""
        return ProcessingResult(
            success=False,
            data={
                "error": "retry_required",
                "message": "Service temporarily unavailable. Retrying...",
                "retry_after": 5  # seconds
            },
            error="Retry required",
            metadata={"error_type": pipeline_error.error_type.value}
        )
    
    def _use_fallback_service(self, pipeline_error: PipelineError) -> ProcessingResult:
        """Attempt to use a fallback service."""
        return ProcessingResult(
            success=False,
            data={
                "error": "service_unavailable",
                "message": "Service is currently unavailable. Please try again later.",
                "fallback_attempted": True
            },
            error="Service unavailable",
            metadata={"error_type": pipeline_error.error_type.value}
        )
    
    def _return_generic_error(self, pipeline_error: PipelineError) -> ProcessingResult:
        """Return a generic error response."""
        return ProcessingResult(
            success=False,
            data={
                "error": "internal_error",
                "message": "An unexpected error occurred. Please try again later.",
                "error_id": id(pipeline_error)
            },
            error="Internal error",
            metadata={"error_type": pipeline_error.error_type.value}
        )
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get a summary of all errors encountered."""
        return {
            "total_errors": sum(self.error_counts.values()),
            "error_counts": self.error_counts.copy(),
            "fallback_strategies": self.fallback_strategies.copy()
        }
    
    def reset_error_counts(self) -> None:
        """Reset error counts for monitoring purposes."""
        self.error_counts = {error_type: 0 for error_type in ErrorType}
    
    def set_fallback_strategy(self, error_type: ErrorType, strategy: str) -> None:
        """Set a custom fallback strategy for a specific error type."""
        self.fallback_strategies[error_type] = strategy 