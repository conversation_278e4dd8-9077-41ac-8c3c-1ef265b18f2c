dvice. Market conditions change rapidly - always verify current data before making trading decisions.\"\\n}\\n```\\n\\n### Technical Analysis Query (Comprehensive):\\n```json\\n{\\n  \"intent\": \"technical_analysis\",\\n  \"symbols\": [\"GME\"],\\n  \"tools_required\": [\"price_fetch\", \"historical_data\", \"technical_indicators\"],\\n  \"confidence\": 0.95,\\n  \"timeframe\": \"intraday\",\\n  \"risk_level\": \"high\",\\n  \"response\": \"**GameStop Corp. ($GME) Comprehensive Technical Analysis:**\\n\\n**Current Market Data**: Real-time price analysis with volume confirmation and momentum indicators.\\n\\n**Technical Indicators**: RSI, MACD, moving averages, and support/resistance levels provide entry/exit guidance.\\n\\n**Volume Analysis**: Unusual volume patterns often precede significant price movements in high-volatility stocks.\\n\\n**Risk Assessment**: $GME exhibits extreme volatility requiring strict risk management.\\n\\n**\u26a0\ufe0f RISK DISCLOSURE**: This is educational analysis, not financial advice. $GME involves substantial risk. Past performance doesn\\'t guarantee future results. Only trade with capital you can afford to lose.\"\\n}\\n```\\n\\n## Critical Rules:\\n1. **NEVER fabricate specific data**: No fake prices, RSI values, or financial metrics\\n2. **Complete responses**: Fully answer the query without saying \"I\\'ll analyze later\"  \\n3. **Educational focus**: Teach methodology and risk management principles\\n4. **Compliance first**: Include appropriate disclaimers and risk warnings\\n5. **Practical guidance**: Provide actionable frameworks and decision criteria\\n6. **Quality over quantity**: Focused, high-value insights rather than generic advice\\n7. **Discord optimization**: Responses fit in single message, well-formatted\\n8. **Professional tone**: Avoid hype language, focus on education and risk awareness\\n\\n## Fallback Behavior:\\n- Unknown intent \u2192 \"educational\"\\n- Unclear symbols \u2192 Only extract symbols with $ prefix, otherwise return empty array\\n- No $ symbols \u2192 Return empty symbols array and focus on educational content\\n- Ambiguous data needs \u2192 Default to [\"price_fetch\"] for safety\\n- Low confidence \u2192 Increase educational content, reduce specific recommendations\\n'}, {'role': 'user', 'content': \"What's the technical setup for $TSLA?\"}], 'model': 'moonshotai/kimi-k2:free', 'max_tokens': 2000, 'temperature': 0.7}}", "module": "_base_client", "function": "_build_request", "line": 435, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:38.773214", "level": "DEBUG", "logger": "httpcore.connection", "message": "connect_tcp.started host='openrouter.ai' port=443 local_address=None timeout=30.0 socket_options=None", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:38.847367", "level": "DEBUG", "logger": "httpcore.connection", "message": "connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x758778295660>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:38.847940", "level": "DEBUG", "logger": "httpcore.connection", "message": "start_tls.started ssl_context=<ssl.SSLContext object at 0x75877815c9c0> server_hostname='openrouter.ai' timeout=30.0", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:38.874725", "level": "DEBUG", "logger": "httpcore.connection", "message": "start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x758778295570>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:38.875010", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_headers.started request=<Request [b'POST']>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:38.875336", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_headers.complete", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:38.875462", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_body.started request=<Request [b'POST']>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:38.875742", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_body.complete", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:38.875824", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_headers.started request=<Request [b'POST']>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:39.822080", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 25 Aug 2025 00:15:39 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Content-Encoding', b'gzip'), (b'Access-Control-Allow-Origin', b'*'), (b'Vary', b'Accept-Encoding'), (b'Permissions-Policy', b'payment=(self \"https://checkout.stripe.com\" \"https://connect-js.stripe.com\" \"https://js.stripe.com\" \"https://*.js.stripe.com\" \"https://hooks.stripe.com\")'), (b'Referrer-Policy', b'no-referrer, strict-origin-when-cross-origin'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9746e56c0ec63d85-EWR')])", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:39.823084", "level": "INFO", "logger": "httpx", "message": "HTTP Request: POST https://openrouter.ai/api/v1/chat/completions \"HTTP/1.1 200 OK\"", "module": "_client", "function": "_send_single_request", "line": 1013, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:39.823324", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_body.started request=<Request [b'POST']>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.779917", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u23f0 [93cf14c1-81c0-4ac3-acc1-1050d6cf0d75] AI call timed out after 15.0 seconds (attempt 1/3)", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 880, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.780198", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83e\udd16 [93cf14c1-81c0-4ac3-acc1-1050d6cf0d75] Calling AI model moonshotai/kimi-k2:free with query: What's the technical setup for $TSLA?...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 853, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.785023", "level": "DEBUG", "logger": "openai._base_client", "message": "Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': 'You are a professional trading analysis assistant. Your role is to analyze user queries, extract relevant information, and provide educational trading insights with proper risk disclosures.\\n\\n## Core Responsibilities:\\n1. **Intent Classification**: Accurately categorize user queries into specific trading contexts\\n2. **Symbol Extraction**: Identify and clean stock symbols from user input\\n3. **Data Requirements**: Determine when real-time market data is essential vs. educational content\\n4. **Complete Analysis**: Provide comprehensive, actionable trading education and market insights\\n\\n## Intent Categories:\\n- `price_check`: Current price requests, quotes, basic market data\\n- `technical_analysis`: Chart patterns, indicators, support/resistance analysis, comprehensive technical data requests\\n- `fundamental_analysis`: Financial metrics, earnings, valuation analysis\\n- `options_strategy`: Option plays, strategies, volatility analysis\\n- `market_overview`: Sector analysis, broad market conditions\\n- `risk_management`: Position sizing, stop losses, portfolio protection\\n- `educational`: Trading concepts, strategy explanations, learning content\\n\\n## Intent Recognition Examples:\\n\\n### Technical Analysis Intent (CRITICAL):\\n**Keywords that ALWAYS trigger `technical_analysis` intent:**\\n- \"indicator values\", \"indicators\", \"technical indicators\"\\n- \"RSI\", \"MACD\", \"moving averages\", \"support/resistance\"\\n- \"chart analysis\", \"technical analysis\", \"pattern analysis\"\\n- \"all available data\", \"comprehensive analysis\", \"full analysis\"\\n- \"trend analysis\", \"momentum\", \"volume analysis\"\\n\\n**Example queries that should trigger `technical_analysis`:**\\n- \"What is the price of $GME and all available indicator values?\"\\n- \"Show me $AAPL with technical indicators\"\\n- \"Give me comprehensive analysis of $TSLA including indicators\"\\n- \"What are the current RSI and MACD values for $NVDA?\"\\n\\n### Price Check Intent:\\n**Keywords that trigger `price_check` intent:**\\n- \"price\", \"current price\", \"quote\", \"ticker\"\\n- \"how much is\", \"what\\'s the price of\"\\n- Simple price requests without technical analysis keywords\\n\\n## Symbol Extraction Rules:\\n- **REQUIRED**: Symbols MUST be prefixed with $ (example: $AAPL, $MSFT, $SPY)\\n- Extract symbols only from $ prefixes, convert to UPPERCASE, validate format\\n- For options queries without specific symbols: Suggest high-liquidity, high-volume stocks with $ prefix\\n- Priority symbols for options: $SPY, $QQQ, $AAPL, $NVDA, $TSLA, $AMD, $MSFT, $AMZN\\n- Extract ALL relevant symbols mentioned in the query with $ prefix\\n- For broad queries, suggest 2-4 representative symbols maximum with $ prefix\\n\\n## Data Strategy (CRITICAL):\\n**NO MOCK DATA POLICY**: Never fabricate prices, technical indicators, or financial metrics\\n- Use `tools_required` array to specify which data tools are needed for accurate analysis\\n- Available tools: `[\"price_fetch\", \"historical_data\", \"technical_indicators\", \"fundamental_data\", \"options_data\"]`\\n- Leave empty `[]` for educational content, general strategies, or historical context\\n- Provide qualitative analysis and educational content when data isn\\'t available\\n- Focus on methodology, risk management, and strategic thinking over specific numbers\\n\\n## Response Requirements:\\n**Format**: Valid JSON with exact structure specified below\\n**Length**: 200-400 words maximum (Discord-optimized)\\n**Tone**: Professional, educational, risk-aware\\n**Compliance**: Include appropriate disclaimers and risk warnings\\n\\n## Required JSON Structure:\\n```json\\n{\\n  \"intent\": \"string (one of the 7 categories above)\",\\n  \"symbols\": [\"SYMBOL1\", \"SYMBOL2\"],\\n  \"tools_required\": [\"price_fetch\", \"historical_data\"],\\n  \"confidence\": float (0.0-1.0),\\n  \"timeframe\": \"string (intraday|short_term|medium_term|long_term)\",\\n  \"risk_level\": \"string (low|medium|high|very_high)\",\\n  \"response\": \"Complete educational response with disclaimers\"\\n}\\n```\\n\\n## Response Generation Guidelines:\\n\\n### For Options Strategies (needs_data=true):\\nFocus on:\\n- Methodology and selection criteria\\n- Risk management principles  \\n- Volatility considerations\\n- Liquidity requirements\\n- Educational strategy breakdown\\n\\n### For Educational Content (needs_data=false):\\nFocus on:\\n- Concept explanations with examples\\n- Strategic frameworks\\n- Risk management principles\\n- Historical context and patterns\\n- Step-by-step methodologies\\n\\n### For Technical Analysis (needs_data=true):\\nFocus on:\\n- Pattern recognition methodology\\n- Indicator interpretation principles\\n- Support/resistance concepts\\n- Volume analysis techniques\\n- Risk-reward frameworks\\n\\n## Compliance Requirements:\\nEvery response MUST include appropriate risk disclaimers:\\n- \"This is educational content, not financial advice\"\\n- \"Past performance doesn\\'t guarantee future results\"  \\n- \"Options trading involves substantial risk of loss\"\\n- \"Consult a financial advisor for personalized advice\"\\n- Position sizing recommendations (1-3% risk per trade maximum)\\n\\n## Example Responses:\\n\\n### Options Strategy Query:\\n```json\\n{\\n  \"intent\": \"options_strategy\",\\n  \"symbols\": [\"NVDA\", \"AMD\", \"TSLA\"],\\n  \"tools_required\": [\"price_fetch\", \"historical_data\", \"technical_indicators\"],\\n  \"confidence\": 0.9,\\n  \"timeframe\": \"short_term\",\\n  \"risk_level\": \"high\",\\n  \"response\": \"**Options Strategy Analysis for High-Volatility Plays:**\\n\\n**Methodology**: Focus on stocks with IV > 30%, daily volume > 1M shares, and clear technical setups.\\n\\n**$NVDA**: AI sector leader with consistent institutional flow. Look for weekly calls on any VWAP retest with strong volume confirmation.\\n\\n**$AMD**: Semiconductor correlation play with lower premium costs. Consider 14-21 DTE calls during sector rotation.\\n\\n**$TSLA**: High-beta momentum candidate. Best suited for experienced traders due to extreme volatility.\\n\\n**Entry Framework**: Wait for first 30min range establishment, enter on volume-confirmed breakouts above key resistance.\\n\\n**Risk Management**: Maximum 2% account risk per position. Set mechanical stops at 50% premium loss. Take profits at 100-150% gains.\\n\\n**\u26a0\ufe0f RISK DISCLOSURE**: Options trading involves substantial risk of total loss. This is educational analysis, not financial advice. Past performance doesn\\'t guarantee future results. Only trade with capital you can afford to lose.\"\\n}\\n```\\n\\n### Educational Query:\\n```json\\n{\\n  \"intent\": \"educational\",\\n  \"symbols\": [],\\n  \"tools_required\": [],\\n  \"confidence\": 1.0,\\n  \"timeframe\": \"long_term\",\\n  \"risk_level\": \"medium\",\\n  \"response\": \"**Technical Analysis Fundamentals:**\\n\\n**Core Premise**: Price action reflects all available information through supply/demand dynamics.\\n\\n**Key Components**:\\n1. **Trend Analysis**: Identify direction using moving averages, trendlines, and higher/lower pivots\\n2. **Support/Resistance**: Price levels where buying/selling pressure historically emerges\\n3. **Volume Confirmation**: Trading activity validates or questions price movements\\n4. **Momentum Indicators**: RSI, MACD help identify overbought/oversold conditions\\n\\n**Practical Application**: Combine multiple timeframes - daily for trend, hourly for entry timing. Never rely on single indicators.\\n\\n**Risk Framework**: Technical analysis provides probability, not certainty. Always use stop-losses and position sizing.\\n\\n**Success Metrics**: Focus on risk-adjusted returns, not win rate. Consistent 2:1 reward-to-risk ratios outperform high-frequency wins.\\n\\n**\u26a0\ufe0f EDUCATIONAL DISCLAIMER**: This explains analytical concepts, not specific investment advice. Markets involve substantial risk. Consider professional guidance for personal strategies.\"\\n}\\n```\\n\\n### Price Check Query:\\n```json\\n{\\n  \"intent\": \"price_check\",\\n  \"symbols\": [\"AAPL\"],\\n  \"tools_required\": [\"price_fetch\", \"historical_data\"],\\n  \"confidence\": 1.0,\\n  \"timeframe\": \"intraday\",\\n  \"risk_level\": \"low\",\\n  \"response\": \"**Apple Inc. ($AAPL) Market Analysis:**\\n\\nApple typically trades with strong institutional support around major moving averages and respects technical levels during normal market conditions. Current market data analysis focuses on:\\n\\n- Real-time price momentum and daily change patterns\\n- Volume analysis relative to 20-day averages\\n- Key technical support/resistance level identification\\n- Intraday momentum indicator configurations\\n- Options flow sentiment indicators when relevant\\n\\n**Market Context**: $AAPL represents approximately 7% of S&P 500 weighting, serving as both individual stock opportunity and broad market indicator.\\n\\n**\u26a0\ufe0f DISCLAIMER**: Market data analysis is for informational purposes only, not investment advice. Market conditions change rapidly - always verify current data before making trading decisions.\"\\n}\\n```\\n\\n### Technical Analysis Query (Comprehensive):\\n```json\\n{\\n  \"intent\": \"technical_analysis\",\\n  \"symbols\": [\"GME\"],\\n  \"tools_required\": [\"price_fetch\", \"historical_data\", \"technical_indicators\"],\\n  \"confidence\": 0.95,\\n  \"timeframe\": \"intraday\",\\n  \"risk_level\": \"high\",\\n  \"response\": \"**GameStop Corp. ($GME) Comprehensive Technical Analysis:**\\n\\n**Current Market Data**: Real-time price analysis with volume confirmation and momentum indicators.\\n\\n**Technical Indicators**: RSI, MACD, moving averages, and support/resistance levels provide entry/exit guidance.\\n\\n**Volume Analysis**: Unusual volume patterns often precede significant price movements in high-volatility stocks.\\n\\n**Risk Assessment**: $GME exhibits extreme volatility requiring strict risk management.\\n\\n**\u26a0\ufe0f RISK DISCLOSURE**: This is educational analysis, not financial advice. $GME involves substantial risk. Past performance doesn\\'t guarantee future results. Only trade with capital you can afford to lose.\"\\n}\\n```\\n\\n## Critical Rules:\\n1. **NEVER fabricate specific data**: No fake prices, RSI values, or financial metrics\\n2. **Complete responses**: Fully answer the query without saying \"I\\'ll analyze later\"  \\n3. **Educational focus**: Teach methodology and risk management principles\\n4. **Compliance first**: Include appropriate disclaimers and risk warnings\\n5. **Practical guidance**: Provide actionable frameworks and decision criteria\\n6. **Quality over quantity**: Focused, high-value insights rather than generic advice\\n7. **Discord optimization**: Responses fit in single message, well-formatted\\n8. **Professional tone**: Avoid hype language, focus on education and risk awareness\\n\\n## Fallback Behavior:\\n- Unknown intent \u2192 \"educational\"\\n- Unclear symbols \u2192 Only extract symbols with $ prefix, otherwise return empty array\\n- No $ symbols \u2192 Return empty symbols array and focus on educational content\\n- Ambiguous data needs \u2192 Default to [\"price_fetch\"] for safety\\n- Low confidence \u2192 Increase educational content, reduce specific recommendations\\n'}, {'role': 'user', 'content': \"What's the technical setup for $TSLA?\"}], 'model': 'moonshotai/kimi-k2:free', 'max_tokens': 2000, 'temperature': 0.7}}", "module": "_base_client", "function": "_build_request", "line": 435, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.786135", "level": "DEBUG", "logger": "httpcore.connection", "message": "connect_tcp.started host='openrouter.ai' port=443 local_address=None timeout=30.0 socket_options=None", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.813266", "level": "DEBUG", "logger": "httpcore.connection", "message": "connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7587782ae590>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.813528", "level": "DEBUG", "logger": "httpcore.connection", "message": "start_tls.started ssl_context=<ssl.SSLContext object at 0x75877815c9c0> server_hostname='openrouter.ai' timeout=30.0", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.845663", "level": "DEBUG", "logger": "httpcore.connection", "message": "start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7587782d2740>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.845943", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_headers.started request=<Request [b'POST']>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.846204", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_headers.complete", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.846314", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_body.started request=<Request [b'POST']>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.846761", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_body.complete", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:53.846987", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_headers.started request=<Request [b'POST']>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:55.185424", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 25 Aug 2025 00:15:55 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Content-Encoding', b'gzip'), (b'Access-Control-Allow-Origin', b'*'), (b'Vary', b'Accept-Encoding'), (b'Permissions-Policy', b'payment=(self \"https://checkout.stripe.com\" \"https://connect-js.stripe.com\" \"https://js.stripe.com\" \"https://*.js.stripe.com\" \"https://hooks.stripe.com\")'), (b'Referrer-Policy', b'no-referrer, strict-origin-when-cross-origin'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9746e5c9a9c8438c-EWR')])", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:55.185853", "level": "INFO", "logger": "httpx", "message": "HTTP Request: POST https://openrouter.ai/api/v1/chat/completions \"HTTP/1.1 200 OK\"", "module": "_client", "function": "_send_single_request", "line": 1013, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:55.186026", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_body.started request=<Request [b'POST']>", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:58.206087", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_body.complete", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:58.206332", "level": "DEBUG", "logger": "httpcore.http11", "message": "response_closed.started", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:58.206452", "level": "DEBUG", "logger": "httpcore.http11", "message": "response_closed.complete", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:15:58.206709", "level": "DEBUG", "logger": "openai._base_client", "message": "HTTP Request: POST https://openrouter.ai/api/v1/chat/completions \"200 OK\"", "module": "_base_client", "function": "_request", "line": 887, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.839005", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_body.complete", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.839512", "level": "DEBUG", "logger": "httpcore.http11", "message": "response_closed.started", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.839761", "level": "DEBUG", "logger": "httpcore.http11", "message": "response_closed.complete", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.840005", "level": "DEBUG", "logger": "httpcore.connection", "message": "close.started", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.840416", "level": "DEBUG", "logger": "httpcore.connection", "message": "close.complete", "module": "_trace", "function": "trace", "line": 45, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.840710", "level": "DEBUG", "logger": "openai._base_client", "message": "HTTP Request: POST https://openrouter.ai/api/v1/chat/completions \"200 OK\"", "module": "_base_client", "function": "_request", "line": 887, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.843482", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udcca [93cf14c1-81c0-4ac3-acc1-1050d6cf0d75] Token usage: {'prompt_tokens': 2272, 'completion_tokens': 361, 'total_tokens': 2633}", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 893, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.843746", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [93cf14c1-81c0-4ac3-acc1-1050d6cf0d75] Raw AI response content: ```json\n{\n  \"intent\": \"technical_analysis\",\n  \"symbols\": [\"TSLA\"],\n  \"tools_required\": [\"price_fetch\", \"historical_data\", \"technical_indicators\"],\n  \"confidence\": 0.95,\n  \"timeframe\": \"short_term\",\n  \"risk_level\": \"high\",\n  \"response\": \"**Tesla Inc. ($TSLA) Technical Analysis Framework:**\n\n**Multi-Timeframe Setup**: Daily chart for trend direction, 1-hour for precise entry timing, 15-minute for confirmation.\n\n**Key Technical Levels**:\\n- **Primary Resistance**: Previous swing high on daily chart...", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 906, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.843915", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [93cf14c1-81c0-4ac3-acc1-1050d6cf0d75] Full raw AI response: ```json\n{\n  \"intent\": \"technical_analysis\",\n  \"symbols\": [\"TSLA\"],\n  \"tools_required\": [\"price_fetch\", \"historical_data\", \"technical_indicators\"],\n  \"confidence\": 0.95,\n  \"timeframe\": \"short_term\",\n  \"risk_level\": \"high\",\n  \"response\": \"**Tesla Inc. ($TSLA) Technical Analysis Framework:**\n\n**Multi-Timeframe Setup**: Daily chart for trend direction, 1-hour for precise entry timing, 15-minute for confirmation.\n\n**Key Technical Levels**:\\n- **Primary Resistance**: Previous swing high on daily chart\\n- **Key Support**: 20-day EMA and recent volume-weighted average price\\n- **Momentum Zone**: RSI divergence patterns at 70/30 levels\\n- **Breakout Confirmation**: Volume surge >150% of 20-day average on any move above resistance\n\n**Risk Management Protocol**:\\n- **Position Sizing**: Maximum 3% account risk per trade\\n- **Entry Strategy**: Wait for 30-minute range establishment, enter on volume-confirmed breakouts\\n- **Stop Placement**: 2-3% below key support level or ATR-based stops\\n- **Profit Targets**: Initial target at 2:1 reward-to-risk ratio, trail stops above 10-day EMA\n\n**Volatility Considerations**: $TSLA exhibits extreme intraday swings. Expect 3-6% daily ranges. Consider smaller position sizes during earnings weeks or regulatory announcements.\n\n**\u26a0\ufe0f RISK WARNING**: Tesla demonstrates high-beta characteristics with significant overnight gap risk. This is educational analysis, not financial advice. Past performance doesn't guarantee future results. Always use stop-losses and position sizing appropriate to your risk tolerance.\"\n}\n```", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 907, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.847199", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u26a0\ufe0f [93cf14c1-81c0-4ac3-acc1-1050d6cf0d75] Failed to parse AI JSON; using defaults. Error: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 946, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.849951", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [93cf14c1-81c0-4ac3-acc1-1050d6cf0d75] AI call successful. Intent: technical_analysis, Symbols: ['TSLA']", "module": "ai_chat_processor", "function": "_call_ai_model", "line": 1031, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.850353", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] MarketDataService type: <class 'src.api.data.market_data_service.MarketDataService'>", "module": "ai_chat_processor", "function": "process", "line": 540, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.850636", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] MarketDataService methods: ['data_source_manager', 'get_comprehensive_stock_data', 'get_current_price', 'get_historical_data', 'get_technical_indicators']", "module": "ai_chat_processor", "function": "process", "line": 541, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.850841", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Tools required: ['price_fetch', 'historical_data', 'technical_indicators'] for symbols: ['TSLA']", "module": "ai_chat_processor", "function": "process", "line": 542, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.851028", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd04 [DIAG] Processing symbol TSLA (sequential with rate limiting)", "module": "ai_chat_processor", "function": "process", "line": 689, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.851204", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udce1 [DIAG] Starting comprehensive data fetch for symbol: TSLA", "module": "ai_chat_processor", "function": "fetch_one_symbol_comprehensive", "line": 547, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.851371", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Using comprehensive data fetch for TSLA", "module": "ai_chat_processor", "function": "fetch_one_symbol_comprehensive", "line": 554, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:04.851638", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udd0d Provider priority for TSLA: ['polygon', 'finnhub', 'yahoo']", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1069, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.022300", "level": "DEBUG", "logger": "httpx", "message": "load_ssl_context verify=True cert=None trust_env=True http2=False", "module": "_config", "function": "load_ssl_context", "line": 79, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.022986", "level": "DEBUG", "logger": "httpx", "message": "load_verify_locations cafile='/usr/local/lib/python3.10/site-packages/certifi/cacert.pem'", "module": "_config", "function": "load_ssl_context_verify", "line": 146, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.034178", "level": "DEBUG", "logger": "httpcore.connection", "message": "connect_tcp.started host='api.polygon.io' port=443 local_address=None timeout=5.0 socket_options=None", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.101049", "level": "DEBUG", "logger": "httpcore.connection", "message": "connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7587782ac4c0>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.101314", "level": "DEBUG", "logger": "httpcore.connection", "message": "start_tls.started ssl_context=<ssl.SSLContext object at 0x75878d02d1c0> server_hostname='api.polygon.io' timeout=5.0", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.136134", "level": "DEBUG", "logger": "httpcore.connection", "message": "start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x758778263a90>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.136799", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_headers.started request=<Request [b'GET']>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.137338", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_headers.complete", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.137560", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_body.started request=<Request [b'GET']>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.137805", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_body.complete", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.137877", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_headers.started request=<Request [b'GET']>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.191880", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 25 Aug 2025 00:16:05 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'217'), (b'Connection', b'keep-alive'), (b'Content-Encoding', b'gzip'), (b'Vary', b'Accept-Encoding'), (b'X-Polygon-Cluster-Name', b'polygon-ny5'), (b'X-Request-Id', b'********************************'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.192275", "level": "INFO", "logger": "httpx", "message": "HTTP Request: GET https://api.polygon.io/v2/aggs/ticker/TSLA/prev?apiKey=********************************&adjusted=true \"HTTP/1.1 200 OK\"", "module": "_client", "function": "_send_single_request", "line": 1729, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.192472", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_body.started request=<Request [b'GET']>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.192645", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_body.complete", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.192771", "level": "DEBUG", "logger": "httpcore.http11", "message": "response_closed.started", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.192950", "level": "DEBUG", "logger": "httpcore.http11", "message": "response_closed.complete", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.194360", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_headers.started request=<Request [b'GET']>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.194768", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_headers.complete", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.194921", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_body.started request=<Request [b'GET']>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.195120", "level": "DEBUG", "logger": "httpcore.http11", "message": "send_request_body.complete", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.195219", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_headers.started request=<Request [b'GET']>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.215846", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_headers.complete return_value=(b'HTTP/1.1', 404, b'Not Found', [(b'Date', b'Mon, 25 Aug 2025 00:16:05 GMT'), (b'Content-Type', b'text/plain'), (b'Content-Length', b'18'), (b'Connection', b'keep-alive'), (b'X-Request-Id', b'0bd368dfcaea3327ac1c3a7146e8aeca'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.216335", "level": "INFO", "logger": "httpx", "message": "HTTP Request: GET https://api.polygon.io/v2/reference/tickers/TSLA/details?apiKey=******************************** \"HTTP/1.1 404 Not Found\"", "module": "_client", "function": "_send_single_request", "line": 1729, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.216632", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_body.started request=<Request [b'GET']>", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.216853", "level": "DEBUG", "logger": "httpcore.http11", "message": "receive_response_body.complete", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.217021", "level": "DEBUG", "logger": "httpcore.http11", "message": "response_closed.started", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.217266", "level": "DEBUG", "logger": "httpcore.http11", "message": "response_closed.complete", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.217913", "level": "DEBUG", "logger": "httpcore.connection", "message": "close.started", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.218433", "level": "DEBUG", "logger": "httpcore.connection", "message": "close.complete", "module": "_trace", "function": "atrace", "line": 85, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.218667", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 polygon provided usable data for TSLA", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1109, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.218751", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\ude80 High-priority provider polygon succeeded - returning data immediately", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1113, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.219056", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Fetching technical indicators separately for TSLA (comprehensive data doesn't include them)", "module": "ai_chat_processor", "function": "fetch_one_symbol_comprehensive", "line": 566, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.219223", "level": "DEBUG", "logger": "src.api.data.providers.data_source_manager", "message": "Polygon historical data failed for TSLA: 'RealPolygonProvider' object has no attribute 'get_historical_data'", "module": "data_source_manager", "function": "fetch_historical_data", "line": 770, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.219291", "level": "WARNING", "logger": "src.api.data.providers.data_source_manager", "message": "\u26a0\ufe0f No historical data available for TSLA, creating synthetic data for analysis", "module": "data_source_manager", "function": "fetch_historical_data", "line": 784, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.219359", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udce6 Using cached data for TSLA", "module": "data_source_manager", "function": "fetch_stock_data", "line": 691, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.219588", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Generated 30 synthetic historical data points for TSLA", "module": "data_source_manager", "function": "fetch_historical_data", "line": 820, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.219822", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [DIAG] Technical indicators for unknown: RSI=53.46, MACD=3.45, SMA20=329.9", "module": "ai_chat_processor", "function": "_add_technical_indicators_to_data", "line": 1695, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.219893", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\ud83d\udd0d [DIAG] Fetching historical data separately for TSLA (comprehensive data doesn't include it)", "module": "ai_chat_processor", "function": "fetch_one_symbol_comprehensive", "line": 590, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220012", "level": "DEBUG", "logger": "src.api.data.providers.data_source_manager", "message": "Polygon historical data failed for TSLA: 'RealPolygonProvider' object has no attribute 'get_historical_data'", "module": "data_source_manager", "function": "fetch_historical_data", "line": 770, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220071", "level": "WARNING", "logger": "src.api.data.providers.data_source_manager", "message": "\u26a0\ufe0f No historical data available for TSLA, creating synthetic data for analysis", "module": "data_source_manager", "function": "fetch_historical_data", "line": 784, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220132", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udce6 Using cached data for TSLA", "module": "data_source_manager", "function": "fetch_stock_data", "line": 691, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220315", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Generated 30 synthetic historical data points for TSLA", "module": "data_source_manager", "function": "fetch_historical_data", "line": 820, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220541", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [DIAG] Comprehensive data fetch completed for TSLA", "module": "ai_chat_processor", "function": "fetch_one_symbol_comprehensive", "line": 667, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220609", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_chat_processor", "message": "\u2705 [DIAG] Successfully fetched data for TSLA", "module": "ai_chat_processor", "function": "process", "line": 696, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220689", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "=== RESPONSE GENERATION START ===", "module": "response_templates", "function": "generate_response", "line": 281, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220746", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Template type: stock_analysis", "module": "response_templates", "function": "generate_response", "line": 282, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220809", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Style: ResponseStyle.DETAILED", "module": "response_templates", "function": "generate_response", "line": 283, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220863", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Query analysis: {}", "module": "response_templates", "function": "generate_response", "line": 284, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.220922", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Input data keys: ['symbol', 'price', 'change', 'volume', 'timestamp', 'market_cap', 'rsi', 'macd', 'macd_signal', 'macd_histogram', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'support_levels', 'resistance_levels', 'support', 'resistance', 'bollinger_upper', 'bollinger_middle', 'bollinger_lower', 'atr', 'stochastic', 'williams_r', 'historical', 'technical_indicators_available', 'high', 'low', 'open', 'previous_close']", "module": "response_templates", "function": "generate_response", "line": 285, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221079", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Input data sample: {'symbol': 'TSLA', 'price': 340.01, 'change': 5.7, 'volume': 94016347, 'timestamp': '2025-08-25T00:16:05.220535', 'market_cap': 0, 'rsi': 53.46, 'macd': 3.45, 'macd_signal': None, 'macd_histogram': No...", "module": "response_templates", "function": "generate_response", "line": 286, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221134", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Using template: stock_analysis/detailed", "module": "response_templates", "function": "generate_response", "line": 306, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221184", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Template content length: 603", "module": "response_templates", "function": "generate_response", "line": 307, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221234", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Template preview: **\ud83d\udcca {symbol} Comprehensive Analysis**\n\n\ud83d\udcb0 **Price Action:**\n\u2022 Current: ${price}\n\u2022 Change: {change_sign}{change}% ({change_sign}${abs_change})\n\u2022 Volume: {volume}\n\u2022 Market Cap: {market_cap_formatted}\n\n\ud83c\udfaf ...", "module": "response_templates", "function": "generate_response", "line": 308, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221326", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Enhanced data keys: ['symbol', 'price', 'change', 'volume', 'timestamp', 'market_cap', 'rsi', 'macd', 'macd_signal', 'macd_histogram', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'support_levels', 'resistance_levels', 'support', 'resistance', 'bollinger_upper', 'bollinger_middle', 'bollinger_lower', 'atr', 'stochastic', 'williams_r', 'historical', 'technical_indicators_available', 'high', 'low', 'open', 'previous_close', 'data_quality', 'confidence', 'confidence_level', 'confidence_emoji', 'risk_level', 'sentiment', 'recommendation', 'action']", "module": "response_templates", "function": "generate_response", "line": 313, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221424", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Enhanced data types: [('symbol', 'str'), ('price', 'float'), ('change', 'float'), ('volume', 'int'), ('timestamp', 'str'), ('market_cap', 'int'), ('rsi', 'float'), ('macd', 'float'), ('macd_signal', 'NoneType'), ('macd_histogram', 'NoneType'), ('sma_20', 'float'), ('sma_50', 'NoneType'), ('ema_12', 'float'), ('ema_26', 'float'), ('support_levels', 'list'), ('resistance_levels', 'list'), ('support', 'int'), ('resistance', 'int'), ('bollinger_upper', 'float'), ('bollinger_middle', 'float'), ('bollinger_lower', 'float'), ('atr', 'NoneType'), ('stochastic', 'NoneType'), ('williams_r', 'NoneType'), ('historical', 'list'), ('technical_indicators_available', 'bool'), ('high', 'int'), ('low', 'int'), ('open', 'int'), ('previous_close', 'NoneType'), ('data_quality', 'float'), ('confidence', 'float'), ('confidence_level', 'str'), ('confidence_emoji', 'str'), ('risk_level', 'str'), ('sentiment', 'SentimentAnalysis'), ('recommendation', 'Recommendation'), ('action', 'str')]", "module": "response_templates", "function": "generate_response", "line": 314, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221490", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Confidence emoji: \ud83d\udfe2", "module": "response_templates", "function": "generate_response", "line": 315, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221541", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Action: BUY", "module": "response_templates", "function": "generate_response", "line": 316, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221597", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Original data keys: ['symbol', 'price', 'change', 'volume', 'timestamp', 'market_cap', 'rsi', 'macd', 'macd_signal', 'macd_histogram', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'support_levels', 'resistance_levels', 'support', 'resistance', 'bollinger_upper', 'bollinger_middle', 'bollinger_lower', 'atr', 'stochastic', 'williams_r', 'historical', 'technical_indicators_available', 'high', 'low', 'open', 'previous_close', 'data_quality', 'confidence', 'confidence_level', 'confidence_emoji', 'risk_level', 'sentiment', 'recommendation', 'action']", "module": "response_templates", "function": "_fill_template", "line": 450, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221656", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Normalized data keys: ['symbol', 'price', 'change', 'volume', 'timestamp', 'market_cap', 'rsi', 'macd', 'macd_signal', 'macd_histogram', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'support_levels', 'resistance_levels', 'support', 'resistance', 'bollinger_upper', 'bollinger_middle', 'bollinger_lower', 'atr', 'stochastic', 'williams_r', 'historical', 'technical_indicators_available', 'high', 'low', 'open', 'previous_close', 'data_quality', 'confidence', 'confidence_level', 'confidence_emoji', 'risk_level', 'sentiment', 'recommendation', 'action']", "module": "response_templates", "function": "_fill_template", "line": 451, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221835", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Original data values: {'symbol': 'TSLA', 'price': 340.01, 'change': 5.7, 'volume': 94016347, 'timestamp': '2025-08-25T00:16:05.220535', 'market_cap': 0, 'rsi': 53.46, 'macd': 3.45, 'macd_signal': None, 'macd_histogram': None, 'sma_20': 329.9, 'sma_50': None, 'ema_12': 331.67, 'ema_26': 328.22, 'support_levels': [329.83, 331.53], 'resistance_levels': [334.45, 335.66], 'support': 0, 'resistance': 0, 'bollinger_upper': 341.07, 'bollinger_middle': 329.9, 'bollinger_lower': 318.73, 'atr': None, 'stochastic': None, 'williams_r': None, 'historical': [{'date': '2025-07-26T00:16:05.220189', 'open': 318.91800792541136, 'high': 324.77044510316307, 'low': 311.14572122637856, 'close': 317.55428524552366, 'volume': 84319316, 'symbol': 'TSLA'}, {'date': '2025-07-27T00:16:05.220196', 'open': 316.211013121959, 'high': 322.02692140976893, 'low': 313.1106871742095, 'close': 317.5373475755389, 'volume': 84005287, 'symbol': 'TSLA'}, {'date': '2025-07-28T00:16:05.220199', 'open': 320.26372516461447, 'high': 319.6077050842308, 'low': 316.33552250745333, 'close': 319.1762973903736, 'volume': 83135311, 'symbol': 'TSLA'}, {'date': '2025-07-29T00:16:05.220202', 'open': 316.136767729243, 'high': 320.93252021877777, 'low': 308.22478294543635, 'close': 315.19799549718806, 'volume': 86959784, 'symbol': 'TSLA'}, {'date': '2025-07-30T00:16:05.220208', 'open': 308.7904732267531, 'high': 313.1745846204016, 'low': 302.0593336900438, 'close': 309.69418694074994, 'volume': 75621330, 'symbol': 'TSLA'}, {'date': '2025-07-31T00:16:05.220213', 'open': 309.54821160854783, 'high': 317.58787030614366, 'low': 301.308115681736, 'close': 310.1635017807407, 'volume': 79881751, 'symbol': 'TSLA'}, {'date': '2025-08-01T00:16:05.220216', 'open': 305.1183884044452, 'high': 307.85367153806783, 'low': 302.7045653617551, 'close': 305.06742509977636, 'volume': 103816219, 'symbol': 'TSLA'}, {'date': '2025-08-02T00:16:05.220219', 'open': 309.97226660370853, 'high': 319.5744602214926, 'low': 307.85482382375085, 'close': 310.66088135840647, 'volume': 76126023, 'symbol': 'TSLA'}, {'date': '2025-08-03T00:16:05.220222', 'open': 314.681352567468, 'high': 317.8470616371773, 'low': 313.2975909303473, 'close': 313.9101458728979, 'volume': 107460025, 'symbol': 'TSLA'}, {'date': '2025-08-04T00:16:05.220225', 'open': 310.5325317475636, 'high': 312.3445716781955, 'low': 304.21710451607424, 'close': 309.04908421244096, 'volume': 119465901, 'symbol': 'TSLA'}, {'date': '2025-08-05T00:16:05.220228', 'open': 309.0498624282709, 'high': 314.8686392409301, 'low': 307.43198633658477, 'close': 309.1931602859671, 'volume': 76891942, 'symbol': 'TSLA'}, {'date': '2025-08-06T00:16:05.220231', 'open': 303.99753699195367, 'high': 306.79433900653277, 'low': 303.2870333819177, 'close': 305.21381740969264, 'volume': 85921249, 'symbol': 'TSLA'}, {'date': '2025-08-07T00:16:05.220234', 'open': 299.9877881235291, 'high': 303.2099748337007, 'low': 294.18387253394894, 'close': 299.75599860991383, 'volume': 75681413, 'symbol': 'TSLA'}, {'date': '2025-08-08T00:16:05.220237', 'open': 303.8223487339574, 'high': 309.0096306550249, 'low': 302.1897547245221, 'close': 303.57675562860686, 'volume': 92363973, 'symbol': 'TSLA'}, {'date': '2025-08-09T00:16:05.220240', 'open': 307.12002674130474, 'high': 314.0162671364614, 'low': 299.9423599981848, 'close': 307.64667669507554, 'volume': 86462469, 'symbol': 'TSLA'}, {'date': '2025-08-10T00:16:05.220243', 'open': 312.48415804626836, 'high': 318.96176085601246, 'low': 303.253961008373, 'close': 312.5973522328697, 'volume': 114250609, 'symbol': 'TSLA'}, {'date': '2025-08-11T00:16:05.220246', 'open': 308.23679421624246, 'high': 314.7317187686568, 'low': 308.05378151908513, 'close': 308.75858859024333, 'volume': 81375131, 'symbol': 'TSLA'}, {'date': '2025-08-12T00:16:05.220249', 'open': 304.0599039624081, 'high': 311.0206926442342, 'low': 303.7789823179896, 'close': 304.8907264839667, 'volume': 65865720, 'symbol': 'TSLA'}, {'date': '2025-08-13T00:16:05.220252', 'open': 301.743280070672, 'high': 309.7907650614076, 'low': 295.7777673845937, 'close': 301.8765012560329, 'volume': 79391297, 'symbol': 'TSLA'}, {'date': '2025-08-14T00:16:05.220255', 'open': 302.2811112726118, 'high': 304.35924950566266, 'low': 296.0768883516895, 'close': 303.5667682595295, 'volume': 93869207, 'symbol': 'TSLA'}, {'date': '2025-08-15T00:16:05.220280', 'open': 300.659963383994, 'high': 309.2647832898954, 'low': 298.4158274208482, 'close': 300.79496454108113, 'volume': 75923964, 'symbol': 'TSLA'}, {'date': '2025-08-16T00:16:05.220283', 'open': 296.02855008263464, 'high': 301.0431614345729, 'low': 287.12801287397554, 'close': 294.98987129832835, 'volume': 67630468, 'symbol': 'TSLA'}, {'date': '2025-08-17T00:16:05.220286', 'open': 292.7566281995947, 'high': 294.34845273878847, 'low': 288.7904035949913, 'close': 292.94169696375513, 'volume': 67237002, 'symbol': 'TSLA'}, {'date': '2025-08-18T00:16:05.220289', 'open': 288.277973795352, 'high': 295.7907670502212, 'low': 284.31646985652463, 'close': 288.2934905234244, 'volume': 110166478, 'symbol': 'TSLA'}, {'date': '2025-08-19T00:16:05.220292', 'open': 292.76246377953186, 'high': 299.1336939115898, 'low': 287.853623617899, 'close': 293.10745305407454, 'volume': 74713190, 'symbol': 'TSLA'}, {'date': '2025-08-20T00:16:05.220295', 'open': 293.01478311399967, 'high': 293.28514330998394, 'low': 290.7230666463849, 'close': 292.7590275448633, 'volume': 82963737, 'symbol': 'TSLA'}, {'date': '2025-08-21T00:16:05.220298', 'open': 293.918292318454, 'high': 294.37678338562074, 'low': 287.66940819654945, 'close': 293.2384514822338, 'volume': 83348702, 'symbol': 'TSLA'}, {'date': '2025-08-22T00:16:05.220301', 'open': 295.79537954748037, 'high': 303.2833445717225, 'low': 289.69772152899765, 'close': 295.85584473745524, 'volume': 68248361, 'symbol': 'TSLA'}, {'date': '2025-08-23T00:16:05.220304', 'open': 297.20227891464333, 'high': 301.11551807692473, 'low': 293.03003412693096, 'close': 296.41455793904646, 'volume': 105774197, 'symbol': 'TSLA'}, {'date': '2025-08-24T00:16:05.220307', 'open': 292.1092684840304, 'high': 293.57217641349706, 'low': 286.2839951973386, 'close': 291.1364833681115, 'volume': 74388045, 'symbol': 'TSLA'}], 'technical_indicators_available': True, 'high': 0, 'low': 0, 'open': 0, 'previous_close': None, 'data_quality': 70.0, 'confidence': 76.0, 'confidence_level': 'High', 'confidence_emoji': '\ud83d\udfe2', 'risk_level': 'Medium', 'sentiment': SentimentAnalysis(overall_sentiment=0.7, sentiment_label='Bullish', confidence=0.6, news_count=0, recent_headlines=[], market_fear_greed=None), 'recommendation': Recommendation(action='BUY', confidence=76.0, reasoning='Based on 5.7% price movement and 76.0% confidence', risk_level='Medium', price_targets={}, stop_loss=None, technical_patterns=[], sentiment=SentimentAnalysis(overall_sentiment=0.7, sentiment_label='Bullish', confidence=0.6, news_count=0, recent_headlines=[], market_fear_greed=None), data_quality=70.0), 'action': 'BUY'}", "module": "response_templates", "function": "_fill_template", "line": 452, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.221959", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Generated technical indicators: \u2022 RSI: 53.46\n\u2022 MACD: 3.45\n\u2022 20-Day SMA: 329.9\n\u2022 12-Day EMA: 331.67\n\u2022 26-Day EMA: 328.22\n\u2022 Support: 3...", "module": "response_templates", "function": "_fill_template", "line": 511, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.222017", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Data keys available: ['symbol', 'price', 'change', 'volume', 'timestamp', 'market_cap', 'rsi', 'macd', 'macd_signal', 'macd_histogram', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'support_levels', 'resistance_levels', 'support', 'resistance', 'bollinger_upper', 'bollinger_middle', 'bollinger_lower', 'atr', 'stochastic', 'williams_r', 'historical', 'technical_indicators_available', 'high', 'low', 'open', 'previous_close', 'data_quality', 'confidence', 'confidence_level', 'confidence_emoji', 'risk_level', 'sentiment', 'recommendation', 'action']", "module": "response_templates", "function": "_fill_template", "line": 512, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.222072", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "RSI value: 53.46", "module": "response_templates", "function": "_fill_template", "line": 513, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.222121", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "MACD value: 3.45", "module": "response_templates", "function": "_fill_template", "line": 514, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.222170", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "SMA 20 value: 329.9", "module": "response_templates", "function": "_fill_template", "line": 515, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.222251", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Template filled successfully with 57 data fields", "module": "response_templates", "function": "_fill_template", "line": 618, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.222303", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Generated stock_analysis response with detailed style", "module": "response_templates", "function": "generate_response", "line": 321, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.222352", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "Response length: 887", "module": "response_templates", "function": "generate_response", "line": 322, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.222419", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_templates", "message": "=== RESPONSE GENERATION SUCCESS ===", "module": "response_templates", "function": "generate_response", "line": 323, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.222988", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.ai_cache", "message": "\ud83d\udcbe Cached response for query: What's the technical setup for $TSLA?... (TTL: 300s)", "module": "ai_cache", "function": "cache_response", "line": 172, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.223241", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage ai_chat_processor returned: <class 'dict'>", "module": "pipeline", "function": "_run_stage", "line": 168, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.223330", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor completed, result type: <class 'dict'>", "module": "pipeline", "function": "run", "line": 151, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.223526", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage ai_chat_processor raw result: {'response': \"**\ud83d\udcca TSLA Comprehensive Analysis**\\n\\n\ud83d\udcb0 **Price Action:**\\n\u2022 Current: $340.01\\n\u2022 Change: +5.7% (+$5.7)\\n\u2022 Volume: 94016347.0\\n\u2022 Market Cap: Market cap data not available\\n\\n\ud83c\udfaf **Trading Recommendation:**\\n\u2022 Action: **BUY** \ud83d\udfe2\\n\u2022 Confidence: **76.0%**\\n\u2022 Risk Level: Medium\\n\\n\ud83d\udcc8 **Technical Analysis:**\\n\u2022 RSI: 53.46\\n\u2022 MACD: 3.45\\n\u2022 20-Day SMA: 329.9\\n\u2022 12-Day EMA: 331.67\\n\u2022 26-Day EMA: 328.22\\n\u2022 Support: 329.83\\n\u2022 Resistance: 334.45\\n\u2022 Bollinger Upper: 341.07\\n\u2022 Bollinger Lower: 318.73\\n\\n\ud83d\udcad **Market Sentiment:**\\nMarket sentiment: SentimentAnalysis(overall_sentiment=0.7, sentiment_label='Bullish', confidence=0.6, news_count=0, recent_headlines=[], market_fear_greed=None)\\n\\n\ud83c\udfaf **Price Targets:**\\nSupport: $329.83 \u2022 Resistance: $334.45\\n\\n\u26a0\ufe0f **Risk Management:**\\n\u2022 Stop Loss: Stop loss not configured\\n\u2022 Position Size: Recommended 1-2% of portfolio per trade\\n\\n*Analysis Quality: 70.0% \u2022 2025-08-25T00:16:05.220535*\", 'data': {'TSLA': {'current_price': 340.01, 'change': 5.7, 'change_percent': 5.7, 'volume': 94016347, 'high': 0, 'low': 0, 'open': 0, 'close': 340.01, 'name': 'TSLA', 'currency': 'USD', 'data_available': True, 'rsi': 53.46, 'macd': 3.45, 'macd_signal': None, 'macd_histogram': None, 'sma_20': 329.9, 'sma_50': None, 'ema_12': 331.67, 'ema_26': 328.22, 'support_levels': [329.83, 331.53], 'resistance_levels': [334.45, 335.66], 'bollinger_upper': 341.07, 'bollinger_middle': 329.9, 'bollinger_lower': 318.73, 'volume_sma': 96220786.6, 'technical_indicators_available': True, 'historical': [{'date': '2025-07-26T00:16:05.220189', 'open': 318.91800792541136, 'high': 324.77044510316307, 'low': 311.14572122637856, 'close': 317.55428524552366, 'volume': 84319316, 'symbol': 'TSLA'}, {'date': '2025-07-27T00:16:05.220196', 'open': 316.211013121959, 'high': 322.02692140976893, 'low': 313.1106871742095, 'close': 317.5373475755389, 'volume': 84005287, 'symbol': 'TSLA'}, {'date': '2025-07-28T00:16:05.220199', 'open': 320.26372516461447, 'high': 319.6077050842308, 'low': 316.33552250745333, 'close': 319.1762973903736, 'volume': 83135311, 'symbol': 'TSLA'}, {'date': '2025-07-29T00:16:05.220202', 'open': 316.136767729243, 'high': 320.93252021877777, 'low': 308.22478294543635, 'close': 315.19799549718806, 'volume': 86959784, 'symbol': 'TSLA'}, {'date': '2025-07-30T00:16:05.220208', 'open': 308.7904732267531, 'high': 313.1745846204016, 'low': 302.0593336900438, 'close': 309.69418694074994, 'volume': 75621330, 'symbol': 'TSLA'}, {'date': '2025-07-31T00:16:05.220213', 'open': 309.54821160854783, 'high': 317.58787030614366, 'low': 301.308115681736, 'close': 310.1635017807407, 'volume': 79881751, 'symbol': 'TSLA'}, {'date': '2025-08-01T00:16:05.220216', 'open': 305.1183884044452, 'high': 307.85367153806783, 'low': 302.7045653617551, 'close': 305.06742509977636, 'volume': 103816219, 'symbol': 'TSLA'}, {'date': '2025-08-02T00:16:05.220219', 'open': 309.97226660370853, 'high': 319.5744602214926, 'low': 307.85482382375085, 'close': 310.66088135840647, 'volume': 76126023, 'symbol': 'TSLA'}, {'date': '2025-08-03T00:16:05.220222', 'open': 314.681352567468, 'high': 317.8470616371773, 'low': 313.2975909303473, 'close': 313.9101458728979, 'volume': 107460025, 'symbol': 'TSLA'}, {'date': '2025-08-04T00:16:05.220225', 'open': 310.5325317475636, 'high': 312.3445716781955, 'low': 304.21710451607424, 'close': 309.04908421244096, 'volume': 119465901, 'symbol': 'TSLA'}, {'date': '2025-08-05T00:16:05.220228', 'open': 309.0498624282709, 'high': 314.8686392409301, 'low': 307.43198633658477, 'close': 309.1931602859671, 'volume': 76891942, 'symbol': 'TSLA'}, {'date': '2025-08-06T00:16:05.220231', 'open': 303.99753699195367, 'high': 306.79433900653277, 'low': 303.2870333819177, 'close': 305.21381740969264, 'volume': 85921249, 'symbol': 'TSLA'}, {'date': '2025-08-07T00:16:05.220234', 'open': 299.9877881235291, 'high': 303.2099748337007, 'low': 294.18387253394894, 'close': 299.75599860991383, 'volume': 75681413, 'symbol': 'TSLA'}, {'date': '2025-08-08T00:16:05.220237', 'open': 303.8223487339574, 'high': 309.0096306550249, 'low': 302.1897547245221, 'close': 303.57675562860686, 'volume': 92363973, 'symbol': 'TSLA'}, {'date': '2025-08-09T00:16:05.220240', 'open': 307.12002674130474, 'high': 314.0162671364614, 'low': 299.9423599981848, 'close': 307.64667669507554, 'volume': 86462469, 'symbol': 'TSLA'}, {'date': '2025-08-10T00:16:05.220243', 'open': 312.48415804626836, 'high': 318.96176085601246, 'low': 303.253961008373, 'close': 312.5973522328697, 'volume': 114250609, 'symbol': 'TSLA'}, {'date': '2025-08-11T00:16:05.220246', 'open': 308.23679421624246, 'high': 314.7317187686568, 'low': 308.05378151908513, 'close': 308.75858859024333, 'volume': 81375131, 'symbol': 'TSLA'}, {'date': '2025-08-12T00:16:05.220249', 'open': 304.0599039624081, 'high': 311.0206926442342, 'low': 303.7789823179896, 'close': 304.8907264839667, 'volume': 65865720, 'symbol': 'TSLA'}, {'date': '2025-08-13T00:16:05.220252', 'open': 301.743280070672, 'high': 309.7907650614076, 'low': 295.7777673845937, 'close': 301.8765012560329, 'volume': 79391297, 'symbol': 'TSLA'}, {'date': '2025-08-14T00:16:05.220255', 'open': 302.2811112726118, 'high': 304.35924950566266, 'low': 296.0768883516895, 'close': 303.5667682595295, 'volume': 93869207, 'symbol': 'TSLA'}, {'date': '2025-08-15T00:16:05.220280', 'open': 300.659963383994, 'high': 309.2647832898954, 'low': 298.4158274208482, 'close': 300.79496454108113, 'volume': 75923964, 'symbol': 'TSLA'}, {'date': '2025-08-16T00:16:05.220283', 'open': 296.02855008263464, 'high': 301.0431614345729, 'low': 287.12801287397554, 'close': 294.98987129832835, 'volume': 67630468, 'symbol': 'TSLA'}, {'date': '2025-08-17T00:16:05.220286', 'open': 292.7566281995947, 'high': 294.34845273878847, 'low': 288.7904035949913, 'close': 292.94169696375513, 'volume': 67237002, 'symbol': 'TSLA'}, {'date': '2025-08-18T00:16:05.220289', 'open': 288.277973795352, 'high': 295.7907670502212, 'low': 284.31646985652463, 'close': 288.2934905234244, 'volume': 110166478, 'symbol': 'TSLA'}, {'date': '2025-08-19T00:16:05.220292', 'open': 292.76246377953186, 'high': 299.1336939115898, 'low': 287.853623617899, 'close': 293.10745305407454, 'volume': 74713190, 'symbol': 'TSLA'}, {'date': '2025-08-20T00:16:05.220295', 'open': 293.01478311399967, 'high': 293.28514330998394, 'low': 290.7230666463849, 'close': 292.7590275448633, 'volume': 82963737, 'symbol': 'TSLA'}, {'date': '2025-08-21T00:16:05.220298', 'open': 293.918292318454, 'high': 294.37678338562074, 'low': 287.66940819654945, 'close': 293.2384514822338, 'volume': 83348702, 'symbol': 'TSLA'}, {'date': '2025-08-22T00:16:05.220301', 'open': 295.79537954748037, 'high': 303.2833445717225, 'low': 289.69772152899765, 'close': 295.85584473745524, 'volume': 68248361, 'symbol': 'TSLA'}, {'date': '2025-08-23T00:16:05.220304', 'open': 297.20227891464333, 'high': 301.11551807692473, 'low': 293.03003412693096, 'close': 296.41455793904646, 'volume': 105774197, 'symbol': 'TSLA'}, {'date': '2025-08-24T00:16:05.220307', 'open': 292.1092684840304, 'high': 293.57217641349706, 'low': 286.2839951973386, 'close': 291.1364833681115, 'volume': 74388045, 'symbol': 'TSLA'}], 'symbol': 'TSLA', 'timestamp': '2025-08-25T00:16:05.220535', 'tools_used': ['price_fetch', 'historical_data', 'technical_indicators']}}, 'intent': 'technical_analysis', 'symbols': ['TSLA'], 'tools_required': ['price_fetch', 'historical_data', 'technical_indicators'], 'needs_data': True, 'pipeline_id': '93cf14c1-81c0-4ac3-acc1-1050d6cf0d75', 'processing_timestamp': '2025-08-25T00:16:05.223223'}", "module": "pipeline", "function": "run", "line": 152, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.223646", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u26a1 Executing stage: response_audit", "module": "pipeline", "function": "run", "line": 148, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.223712", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Running stage response_audit with query: What's the technical setup for $TSLA?...", "module": "pipeline", "function": "_run_stage", "line": 160, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.223770", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage response_audit input results: {'query': \"What's the technical setup for $TSLA?\"}", "module": "pipeline", "function": "_run_stage", "line": 165, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.223841", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udd0d Starting response audit...", "module": "response_audit", "function": "audit_response", "line": 54, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.224547", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udd0d Audit completed - Passed: True", "module": "response_audit", "function": "audit_response", "line": 94, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.224633", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udcca Quality Score: 0.90", "module": "response_audit", "function": "audit_response", "line": 95, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.224702", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udd0d Warnings: ['Response is very short (< 50 characters)', 'No market data available for numeric validation']", "module": "response_audit", "function": "audit_response", "line": 99, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.224765", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd27 Stage response_audit returned: <class 'dict'>", "module": "pipeline", "function": "_run_stage", "line": 168, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.224823", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage response_audit completed, result type: <class 'dict'>", "module": "pipeline", "function": "run", "line": 151, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.224885", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Stage response_audit raw result: {'query': \"What's the technical setup for $TSLA?\", 'audit_results': {'passed': True, 'issues': [], 'warnings': ['Response is very short (< 50 characters)', 'No market data available for numeric validation'], 'quality_score': 0.8999999999999999, 'validated_response': '', 'corrections_made': []}, 'response': ''}", "module": "pipeline", "function": "run", "line": 152, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.224954", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Pipeline execution completed, returning 2 results", "module": "pipeline", "function": "run", "line": 155, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225013", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Pipeline execution completed, got 2 results", "module": "pipeline", "function": "execute_ask_pipeline", "line": 262, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225196", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Raw pipeline results: {'ai_chat_processor': {'response': \"**\ud83d\udcca TSLA Comprehensive Analysis**\\n\\n\ud83d\udcb0 **Price Action:**\\n\u2022 Current: $340.01\\n\u2022 Change: +5.7% (+$5.7)\\n\u2022 Volume: 94016347.0\\n\u2022 Market Cap: Market cap data not available\\n\\n\ud83c\udfaf **Trading Recommendation:**\\n\u2022 Action: **BUY** \ud83d\udfe2\\n\u2022 Confidence: **76.0%**\\n\u2022 Risk Level: Medium\\n\\n\ud83d\udcc8 **Technical Analysis:**\\n\u2022 RSI: 53.46\\n\u2022 MACD: 3.45\\n\u2022 20-Day SMA: 329.9\\n\u2022 12-Day EMA: 331.67\\n\u2022 26-Day EMA: 328.22\\n\u2022 Support: 329.83\\n\u2022 Resistance: 334.45\\n\u2022 Bollinger Upper: 341.07\\n\u2022 Bollinger Lower: 318.73\\n\\n\ud83d\udcad **Market Sentiment:**\\nMarket sentiment: SentimentAnalysis(overall_sentiment=0.7, sentiment_label='Bullish', confidence=0.6, news_count=0, recent_headlines=[], market_fear_greed=None)\\n\\n\ud83c\udfaf **Price Targets:**\\nSupport: $329.83 \u2022 Resistance: $334.45\\n\\n\u26a0\ufe0f **Risk Management:**\\n\u2022 Stop Loss: Stop loss not configured\\n\u2022 Position Size: Recommended 1-2% of portfolio per trade\\n\\n*Analysis Quality: 70.0% \u2022 2025-08-25T00:16:05.220535*\", 'data': {'TSLA': {'current_price': 340.01, 'change': 5.7, 'change_percent': 5.7, 'volume': 94016347, 'high': 0, 'low': 0, 'open': 0, 'close': 340.01, 'name': 'TSLA', 'currency': 'USD', 'data_available': True, 'rsi': 53.46, 'macd': 3.45, 'macd_signal': None, 'macd_histogram': None, 'sma_20': 329.9, 'sma_50': None, 'ema_12': 331.67, 'ema_26': 328.22, 'support_levels': [329.83, 331.53], 'resistance_levels': [334.45, 335.66], 'bollinger_upper': 341.07, 'bollinger_middle': 329.9, 'bollinger_lower': 318.73, 'volume_sma': 96220786.6, 'technical_indicators_available': True, 'historical': [{'date': '2025-07-26T00:16:05.220189', 'open': 318.91800792541136, 'high': 324.77044510316307, 'low': 311.14572122637856, 'close': 317.55428524552366, 'volume': 84319316, 'symbol': 'TSLA'}, {'date': '2025-07-27T00:16:05.220196', 'open': 316.211013121959, 'high': 322.02692140976893, 'low': 313.1106871742095, 'close': 317.5373475755389, 'volume': 84005287, 'symbol': 'TSLA'}, {'date': '2025-07-28T00:16:05.220199', 'open': 320.26372516461447, 'high': 319.6077050842308, 'low': 316.33552250745333, 'close': 319.1762973903736, 'volume': 83135311, 'symbol': 'TSLA'}, {'date': '2025-07-29T00:16:05.220202', 'open': 316.136767729243, 'high': 320.93252021877777, 'low': 308.22478294543635, 'close': 315.19799549718806, 'volume': 86959784, 'symbol': 'TSLA'}, {'date': '2025-07-30T00:16:05.220208', 'open': 308.7904732267531, 'high': 313.1745846204016, 'low': 302.0593336900438, 'close': 309.69418694074994, 'volume': 75621330, 'symbol': 'TSLA'}, {'date': '2025-07-31T00:16:05.220213', 'open': 309.54821160854783, 'high': 317.58787030614366, 'low': 301.308115681736, 'close': 310.1635017807407, 'volume': 79881751, 'symbol': 'TSLA'}, {'date': '2025-08-01T00:16:05.220216', 'open': 305.1183884044452, 'high': 307.85367153806783, 'low': 302.7045653617551, 'close': 305.06742509977636, 'volume': 103816219, 'symbol': 'TSLA'}, {'date': '2025-08-02T00:16:05.220219', 'open': 309.97226660370853, 'high': 319.5744602214926, 'low': 307.85482382375085, 'close': 310.66088135840647, 'volume': 76126023, 'symbol': 'TSLA'}, {'date': '2025-08-03T00:16:05.220222', 'open': 314.681352567468, 'high': 317.8470616371773, 'low': 313.2975909303473, 'close': 313.9101458728979, 'volume': 107460025, 'symbol': 'TSLA'}, {'date': '2025-08-04T00:16:05.220225', 'open': 310.5325317475636, 'high': 312.3445716781955, 'low': 304.21710451607424, 'close': 309.04908421244096, 'volume': 119465901, 'symbol': 'TSLA'}, {'date': '2025-08-05T00:16:05.220228', 'open': 309.0498624282709, 'high': 314.8686392409301, 'low': 307.43198633658477, 'close': 309.1931602859671, 'volume': 76891942, 'symbol': 'TSLA'}, {'date': '2025-08-06T00:16:05.220231', 'open': 303.99753699195367, 'high': 306.79433900653277, 'low': 303.2870333819177, 'close': 305.21381740969264, 'volume': 85921249, 'symbol': 'TSLA'}, {'date': '2025-08-07T00:16:05.220234', 'open': 299.9877881235291, 'high': 303.2099748337007, 'low': 294.18387253394894, 'close': 299.75599860991383, 'volume': 75681413, 'symbol': 'TSLA'}, {'date': '2025-08-08T00:16:05.220237', 'open': 303.8223487339574, 'high': 309.0096306550249, 'low': 302.1897547245221, 'close': 303.57675562860686, 'volume': 92363973, 'symbol': 'TSLA'}, {'date': '2025-08-09T00:16:05.220240', 'open': 307.12002674130474, 'high': 314.0162671364614, 'low': 299.9423599981848, 'close': 307.64667669507554, 'volume': 86462469, 'symbol': 'TSLA'}, {'date': '2025-08-10T00:16:05.220243', 'open': 312.48415804626836, 'high': 318.96176085601246, 'low': 303.253961008373, 'close': 312.5973522328697, 'volume': 114250609, 'symbol': 'TSLA'}, {'date': '2025-08-11T00:16:05.220246', 'open': 308.23679421624246, 'high': 314.7317187686568, 'low': 308.05378151908513, 'close': 308.75858859024333, 'volume': 81375131, 'symbol': 'TSLA'}, {'date': '2025-08-12T00:16:05.220249', 'open': 304.0599039624081, 'high': 311.0206926442342, 'low': 303.7789823179896, 'close': 304.8907264839667, 'volume': 65865720, 'symbol': 'TSLA'}, {'date': '2025-08-13T00:16:05.220252', 'open': 301.743280070672, 'high': 309.7907650614076, 'low': 295.7777673845937, 'close': 301.8765012560329, 'volume': 79391297, 'symbol': 'TSLA'}, {'date': '2025-08-14T00:16:05.220255', 'open': 302.2811112726118, 'high': 304.35924950566266, 'low': 296.0768883516895, 'close': 303.5667682595295, 'volume': 93869207, 'symbol': 'TSLA'}, {'date': '2025-08-15T00:16:05.220280', 'open': 300.659963383994, 'high': 309.2647832898954, 'low': 298.4158274208482, 'close': 300.79496454108113, 'volume': 75923964, 'symbol': 'TSLA'}, {'date': '2025-08-16T00:16:05.220283', 'open': 296.02855008263464, 'high': 301.0431614345729, 'low': 287.12801287397554, 'close': 294.98987129832835, 'volume': 67630468, 'symbol': 'TSLA'}, {'date': '2025-08-17T00:16:05.220286', 'open': 292.7566281995947, 'high': 294.34845273878847, 'low': 288.7904035949913, 'close': 292.94169696375513, 'volume': 67237002, 'symbol': 'TSLA'}, {'date': '2025-08-18T00:16:05.220289', 'open': 288.277973795352, 'high': 295.7907670502212, 'low': 284.31646985652463, 'close': 288.2934905234244, 'volume': 110166478, 'symbol': 'TSLA'}, {'date': '2025-08-19T00:16:05.220292', 'open': 292.76246377953186, 'high': 299.1336939115898, 'low': 287.853623617899, 'close': 293.10745305407454, 'volume': 74713190, 'symbol': 'TSLA'}, {'date': '2025-08-20T00:16:05.220295', 'open': 293.01478311399967, 'high': 293.28514330998394, 'low': 290.7230666463849, 'close': 292.7590275448633, 'volume': 82963737, 'symbol': 'TSLA'}, {'date': '2025-08-21T00:16:05.220298', 'open': 293.918292318454, 'high': 294.37678338562074, 'low': 287.66940819654945, 'close': 293.2384514822338, 'volume': 83348702, 'symbol': 'TSLA'}, {'date': '2025-08-22T00:16:05.220301', 'open': 295.79537954748037, 'high': 303.2833445717225, 'low': 289.69772152899765, 'close': 295.85584473745524, 'volume': 68248361, 'symbol': 'TSLA'}, {'date': '2025-08-23T00:16:05.220304', 'open': 297.20227891464333, 'high': 301.11551807692473, 'low': 293.03003412693096, 'close': 296.41455793904646, 'volume': 105774197, 'symbol': 'TSLA'}, {'date': '2025-08-24T00:16:05.220307', 'open': 292.1092684840304, 'high': 293.57217641349706, 'low': 286.2839951973386, 'close': 291.1364833681115, 'volume': 74388045, 'symbol': 'TSLA'}], 'symbol': 'TSLA', 'timestamp': '2025-08-25T00:16:05.220535', 'tools_used': ['price_fetch', 'historical_data', 'technical_indicators']}}, 'intent': 'technical_analysis', 'symbols': ['TSLA'], 'tools_required': ['price_fetch', 'historical_data', 'technical_indicators'], 'needs_data': True, 'pipeline_id': '93cf14c1-81c0-4ac3-acc1-1050d6cf0d75', 'processing_timestamp': '2025-08-25T00:16:05.223223'}, 'response_audit': {'query': \"What's the technical setup for $TSLA?\", 'audit_results': {'passed': True, 'issues': [], 'warnings': ['Response is very short (< 50 characters)', 'No market data available for numeric validation'], 'quality_score': 0.8999999999999999, 'validated_response': '', 'corrections_made': []}, 'response': ''}}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 263, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225313", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udd0d Analyzing pipeline results...", "module": "pipeline", "function": "execute_ask_pipeline", "line": 272, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225371", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb AI chat processor returned: <class 'dict'>", "module": "pipeline", "function": "execute_ask_pipeline", "line": 278, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225542", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb AI chat processor raw result: {'response': \"**\ud83d\udcca TSLA Comprehensive Analysis**\\n\\n\ud83d\udcb0 **Price Action:**\\n\u2022 Current: $340.01\\n\u2022 Change: +5.7% (+$5.7)\\n\u2022 Volume: 94016347.0\\n\u2022 Market Cap: Market cap data not available\\n\\n\ud83c\udfaf **Trading Recommendation:**\\n\u2022 Action: **BUY** \ud83d\udfe2\\n\u2022 Confidence: **76.0%**\\n\u2022 Risk Level: Medium\\n\\n\ud83d\udcc8 **Technical Analysis:**\\n\u2022 RSI: 53.46\\n\u2022 MACD: 3.45\\n\u2022 20-Day SMA: 329.9\\n\u2022 12-Day EMA: 331.67\\n\u2022 26-Day EMA: 328.22\\n\u2022 Support: 329.83\\n\u2022 Resistance: 334.45\\n\u2022 Bollinger Upper: 341.07\\n\u2022 Bollinger Lower: 318.73\\n\\n\ud83d\udcad **Market Sentiment:**\\nMarket sentiment: SentimentAnalysis(overall_sentiment=0.7, sentiment_label='Bullish', confidence=0.6, news_count=0, recent_headlines=[], market_fear_greed=None)\\n\\n\ud83c\udfaf **Price Targets:**\\nSupport: $329.83 \u2022 Resistance: $334.45\\n\\n\u26a0\ufe0f **Risk Management:**\\n\u2022 Stop Loss: Stop loss not configured\\n\u2022 Position Size: Recommended 1-2% of portfolio per trade\\n\\n*Analysis Quality: 70.0% \u2022 2025-08-25T00:16:05.220535*\", 'data': {'TSLA': {'current_price': 340.01, 'change': 5.7, 'change_percent': 5.7, 'volume': 94016347, 'high': 0, 'low': 0, 'open': 0, 'close': 340.01, 'name': 'TSLA', 'currency': 'USD', 'data_available': True, 'rsi': 53.46, 'macd': 3.45, 'macd_signal': None, 'macd_histogram': None, 'sma_20': 329.9, 'sma_50': None, 'ema_12': 331.67, 'ema_26': 328.22, 'support_levels': [329.83, 331.53], 'resistance_levels': [334.45, 335.66], 'bollinger_upper': 341.07, 'bollinger_middle': 329.9, 'bollinger_lower': 318.73, 'volume_sma': 96220786.6, 'technical_indicators_available': True, 'historical': [{'date': '2025-07-26T00:16:05.220189', 'open': 318.91800792541136, 'high': 324.77044510316307, 'low': 311.14572122637856, 'close': 317.55428524552366, 'volume': 84319316, 'symbol': 'TSLA'}, {'date': '2025-07-27T00:16:05.220196', 'open': 316.211013121959, 'high': 322.02692140976893, 'low': 313.1106871742095, 'close': 317.5373475755389, 'volume': 84005287, 'symbol': 'TSLA'}, {'date': '2025-07-28T00:16:05.220199', 'open': 320.26372516461447, 'high': 319.6077050842308, 'low': 316.33552250745333, 'close': 319.1762973903736, 'volume': 83135311, 'symbol': 'TSLA'}, {'date': '2025-07-29T00:16:05.220202', 'open': 316.136767729243, 'high': 320.93252021877777, 'low': 308.22478294543635, 'close': 315.19799549718806, 'volume': 86959784, 'symbol': 'TSLA'}, {'date': '2025-07-30T00:16:05.220208', 'open': 308.7904732267531, 'high': 313.1745846204016, 'low': 302.0593336900438, 'close': 309.69418694074994, 'volume': 75621330, 'symbol': 'TSLA'}, {'date': '2025-07-31T00:16:05.220213', 'open': 309.54821160854783, 'high': 317.58787030614366, 'low': 301.308115681736, 'close': 310.1635017807407, 'volume': 79881751, 'symbol': 'TSLA'}, {'date': '2025-08-01T00:16:05.220216', 'open': 305.1183884044452, 'high': 307.85367153806783, 'low': 302.7045653617551, 'close': 305.06742509977636, 'volume': 103816219, 'symbol': 'TSLA'}, {'date': '2025-08-02T00:16:05.220219', 'open': 309.97226660370853, 'high': 319.5744602214926, 'low': 307.85482382375085, 'close': 310.66088135840647, 'volume': 76126023, 'symbol': 'TSLA'}, {'date': '2025-08-03T00:16:05.220222', 'open': 314.681352567468, 'high': 317.8470616371773, 'low': 313.2975909303473, 'close': 313.9101458728979, 'volume': 107460025, 'symbol': 'TSLA'}, {'date': '2025-08-04T00:16:05.220225', 'open': 310.5325317475636, 'high': 312.3445716781955, 'low': 304.21710451607424, 'close': 309.04908421244096, 'volume': 119465901, 'symbol': 'TSLA'}, {'date': '2025-08-05T00:16:05.220228', 'open': 309.0498624282709, 'high': 314.8686392409301, 'low': 307.43198633658477, 'close': 309.1931602859671, 'volume': 76891942, 'symbol': 'TSLA'}, {'date': '2025-08-06T00:16:05.220231', 'open': 303.99753699195367, 'high': 306.79433900653277, 'low': 303.2870333819177, 'close': 305.21381740969264, 'volume': 85921249, 'symbol': 'TSLA'}, {'date': '2025-08-07T00:16:05.220234', 'open': 299.9877881235291, 'high': 303.2099748337007, 'low': 294.18387253394894, 'close': 299.75599860991383, 'volume': 75681413, 'symbol': 'TSLA'}, {'date': '2025-08-08T00:16:05.220237', 'open': 303.8223487339574, 'high': 309.0096306550249, 'low': 302.1897547245221, 'close': 303.57675562860686, 'volume': 92363973, 'symbol': 'TSLA'}, {'date': '2025-08-09T00:16:05.220240', 'open': 307.12002674130474, 'high': 314.0162671364614, 'low': 299.9423599981848, 'close': 307.64667669507554, 'volume': 86462469, 'symbol': 'TSLA'}, {'date': '2025-08-10T00:16:05.220243', 'open': 312.48415804626836, 'high': 318.96176085601246, 'low': 303.253961008373, 'close': 312.5973522328697, 'volume': 114250609, 'symbol': 'TSLA'}, {'date': '2025-08-11T00:16:05.220246', 'open': 308.23679421624246, 'high': 314.7317187686568, 'low': 308.05378151908513, 'close': 308.75858859024333, 'volume': 81375131, 'symbol': 'TSLA'}, {'date': '2025-08-12T00:16:05.220249', 'open': 304.0599039624081, 'high': 311.0206926442342, 'low': 303.7789823179896, 'close': 304.8907264839667, 'volume': 65865720, 'symbol': 'TSLA'}, {'date': '2025-08-13T00:16:05.220252', 'open': 301.743280070672, 'high': 309.7907650614076, 'low': 295.7777673845937, 'close': 301.8765012560329, 'volume': 79391297, 'symbol': 'TSLA'}, {'date': '2025-08-14T00:16:05.220255', 'open': 302.2811112726118, 'high': 304.35924950566266, 'low': 296.0768883516895, 'close': 303.5667682595295, 'volume': 93869207, 'symbol': 'TSLA'}, {'date': '2025-08-15T00:16:05.220280', 'open': 300.659963383994, 'high': 309.2647832898954, 'low': 298.4158274208482, 'close': 300.79496454108113, 'volume': 75923964, 'symbol': 'TSLA'}, {'date': '2025-08-16T00:16:05.220283', 'open': 296.02855008263464, 'high': 301.0431614345729, 'low': 287.12801287397554, 'close': 294.98987129832835, 'volume': 67630468, 'symbol': 'TSLA'}, {'date': '2025-08-17T00:16:05.220286', 'open': 292.7566281995947, 'high': 294.34845273878847, 'low': 288.7904035949913, 'close': 292.94169696375513, 'volume': 67237002, 'symbol': 'TSLA'}, {'date': '2025-08-18T00:16:05.220289', 'open': 288.277973795352, 'high': 295.7907670502212, 'low': 284.31646985652463, 'close': 288.2934905234244, 'volume': 110166478, 'symbol': 'TSLA'}, {'date': '2025-08-19T00:16:05.220292', 'open': 292.76246377953186, 'high': 299.1336939115898, 'low': 287.853623617899, 'close': 293.10745305407454, 'volume': 74713190, 'symbol': 'TSLA'}, {'date': '2025-08-20T00:16:05.220295', 'open': 293.01478311399967, 'high': 293.28514330998394, 'low': 290.7230666463849, 'close': 292.7590275448633, 'volume': 82963737, 'symbol': 'TSLA'}, {'date': '2025-08-21T00:16:05.220298', 'open': 293.918292318454, 'high': 294.37678338562074, 'low': 287.66940819654945, 'close': 293.2384514822338, 'volume': 83348702, 'symbol': 'TSLA'}, {'date': '2025-08-22T00:16:05.220301', 'open': 295.79537954748037, 'high': 303.2833445717225, 'low': 289.69772152899765, 'close': 295.85584473745524, 'volume': 68248361, 'symbol': 'TSLA'}, {'date': '2025-08-23T00:16:05.220304', 'open': 297.20227891464333, 'high': 301.11551807692473, 'low': 293.03003412693096, 'close': 296.41455793904646, 'volume': 105774197, 'symbol': 'TSLA'}, {'date': '2025-08-24T00:16:05.220307', 'open': 292.1092684840304, 'high': 293.57217641349706, 'low': 286.2839951973386, 'close': 291.1364833681115, 'volume': 74388045, 'symbol': 'TSLA'}], 'symbol': 'TSLA', 'timestamp': '2025-08-25T00:16:05.220535', 'tools_used': ['price_fetch', 'historical_data', 'technical_indicators']}}, 'intent': 'technical_analysis', 'symbols': ['TSLA'], 'tools_required': ['price_fetch', 'historical_data', 'technical_indicators'], 'needs_data': True, 'pipeline_id': '93cf14c1-81c0-4ac3-acc1-1050d6cf0d75', 'processing_timestamp': '2025-08-25T00:16:05.223223'}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 279, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225654", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83d\udccb AI chat processor returned dict with keys: ['response', 'data', 'intent', 'symbols', 'tools_required', 'needs_data', 'pipeline_id', 'processing_timestamp']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 284, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225712", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted response from ai_chat_processor section", "module": "pipeline", "function": "execute_ask_pipeline", "line": 287, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225766", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for response: **\ud83d\udcca TSLA Comprehensive Analysis**\n\n\ud83d\udcb0 **Price Action:**\n\u2022 Current: $340.01\n\u2022 Change: +5.7% (+$5.7)\n\u2022 Volume: 94016347.0\n\u2022 Market Cap: Market cap data not available\n\n\ud83c\udfaf **Trading Recommendation:**\n\u2022 Action: **BUY** \ud83d\udfe2\n\u2022 Confidence: **76.0%**\n\u2022 Risk Level: Medium\n\n\ud83d\udcc8 **Technical Analysis:**\n\u2022 RSI: 53.46\n\u2022 MACD: 3.45\n\u2022 20-Day SMA: 329.9\n\u2022 12-Day EMA: 331.67\n\u2022 26-Day EMA: 328.22\n\u2022 Support: 329.83\n\u2022 Resistance: 334.45\n\u2022 Bollinger Upper: 341.07\n\u2022 Bollinger Lower: 318.73\n\n\ud83d\udcad **Market Sentiment:**\nMarket sentiment: SentimentAnalysis(overall_sentiment=0.7, sentiment_label='Bullish', confidence=0.6, news_count=0, recent_headlines=[], market_fear_greed=None)\n\n\ud83c\udfaf **Price Targets:**\nSupport: $329.83 \u2022 Resistance: $334.45\n\n\u26a0\ufe0f **Risk Management:**\n\u2022 Stop Loss: Stop loss not configured\n\u2022 Position Size: Recommended 1-2% of portfolio per trade\n\n*Analysis Quality: 70.0% \u2022 2025-08-25T00:16:05.220535*", "module": "pipeline", "function": "execute_ask_pipeline", "line": 288, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225829", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted data from ai_chat_processor section", "module": "pipeline", "function": "execute_ask_pipeline", "line": 287, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.225981", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for data: {'TSLA': {'current_price': 340.01, 'change': 5.7, 'change_percent': 5.7, 'volume': 94016347, 'high': 0, 'low': 0, 'open': 0, 'close': 340.01, 'name': 'TSLA', 'currency': 'USD', 'data_available': True, 'rsi': 53.46, 'macd': 3.45, 'macd_signal': None, 'macd_histogram': None, 'sma_20': 329.9, 'sma_50': None, 'ema_12': 331.67, 'ema_26': 328.22, 'support_levels': [329.83, 331.53], 'resistance_levels': [334.45, 335.66], 'bollinger_upper': 341.07, 'bollinger_middle': 329.9, 'bollinger_lower': 318.73, 'volume_sma': 96220786.6, 'technical_indicators_available': True, 'historical': [{'date': '2025-07-26T00:16:05.220189', 'open': 318.91800792541136, 'high': 324.77044510316307, 'low': 311.14572122637856, 'close': 317.55428524552366, 'volume': 84319316, 'symbol': 'TSLA'}, {'date': '2025-07-27T00:16:05.220196', 'open': 316.211013121959, 'high': 322.02692140976893, 'low': 313.1106871742095, 'close': 317.5373475755389, 'volume': 84005287, 'symbol': 'TSLA'}, {'date': '2025-07-28T00:16:05.220199', 'open': 320.26372516461447, 'high': 319.6077050842308, 'low': 316.33552250745333, 'close': 319.1762973903736, 'volume': 83135311, 'symbol': 'TSLA'}, {'date': '2025-07-29T00:16:05.220202', 'open': 316.136767729243, 'high': 320.93252021877777, 'low': 308.22478294543635, 'close': 315.19799549718806, 'volume': 86959784, 'symbol': 'TSLA'}, {'date': '2025-07-30T00:16:05.220208', 'open': 308.7904732267531, 'high': 313.1745846204016, 'low': 302.0593336900438, 'close': 309.69418694074994, 'volume': 75621330, 'symbol': 'TSLA'}, {'date': '2025-07-31T00:16:05.220213', 'open': 309.54821160854783, 'high': 317.58787030614366, 'low': 301.308115681736, 'close': 310.1635017807407, 'volume': 79881751, 'symbol': 'TSLA'}, {'date': '2025-08-01T00:16:05.220216', 'open': 305.1183884044452, 'high': 307.85367153806783, 'low': 302.7045653617551, 'close': 305.06742509977636, 'volume': 103816219, 'symbol': 'TSLA'}, {'date': '2025-08-02T00:16:05.220219', 'open': 309.97226660370853, 'high': 319.5744602214926, 'low': 307.85482382375085, 'close': 310.66088135840647, 'volume': 76126023, 'symbol': 'TSLA'}, {'date': '2025-08-03T00:16:05.220222', 'open': 314.681352567468, 'high': 317.8470616371773, 'low': 313.2975909303473, 'close': 313.9101458728979, 'volume': 107460025, 'symbol': 'TSLA'}, {'date': '2025-08-04T00:16:05.220225', 'open': 310.5325317475636, 'high': 312.3445716781955, 'low': 304.21710451607424, 'close': 309.04908421244096, 'volume': 119465901, 'symbol': 'TSLA'}, {'date': '2025-08-05T00:16:05.220228', 'open': 309.0498624282709, 'high': 314.8686392409301, 'low': 307.43198633658477, 'close': 309.1931602859671, 'volume': 76891942, 'symbol': 'TSLA'}, {'date': '2025-08-06T00:16:05.220231', 'open': 303.99753699195367, 'high': 306.79433900653277, 'low': 303.2870333819177, 'close': 305.21381740969264, 'volume': 85921249, 'symbol': 'TSLA'}, {'date': '2025-08-07T00:16:05.220234', 'open': 299.9877881235291, 'high': 303.2099748337007, 'low': 294.18387253394894, 'close': 299.75599860991383, 'volume': 75681413, 'symbol': 'TSLA'}, {'date': '2025-08-08T00:16:05.220237', 'open': 303.8223487339574, 'high': 309.0096306550249, 'low': 302.1897547245221, 'close': 303.57675562860686, 'volume': 92363973, 'symbol': 'TSLA'}, {'date': '2025-08-09T00:16:05.220240', 'open': 307.12002674130474, 'high': 314.0162671364614, 'low': 299.9423599981848, 'close': 307.64667669507554, 'volume': 86462469, 'symbol': 'TSLA'}, {'date': '2025-08-10T00:16:05.220243', 'open': 312.48415804626836, 'high': 318.96176085601246, 'low': 303.253961008373, 'close': 312.5973522328697, 'volume': 114250609, 'symbol': 'TSLA'}, {'date': '2025-08-11T00:16:05.220246', 'open': 308.23679421624246, 'high': 314.7317187686568, 'low': 308.05378151908513, 'close': 308.75858859024333, 'volume': 81375131, 'symbol': 'TSLA'}, {'date': '2025-08-12T00:16:05.220249', 'open': 304.0599039624081, 'high': 311.0206926442342, 'low': 303.7789823179896, 'close': 304.8907264839667, 'volume': 65865720, 'symbol': 'TSLA'}, {'date': '2025-08-13T00:16:05.220252', 'open': 301.743280070672, 'high': 309.7907650614076, 'low': 295.7777673845937, 'close': 301.8765012560329, 'volume': 79391297, 'symbol': 'TSLA'}, {'date': '2025-08-14T00:16:05.220255', 'open': 302.2811112726118, 'high': 304.35924950566266, 'low': 296.0768883516895, 'close': 303.5667682595295, 'volume': 93869207, 'symbol': 'TSLA'}, {'date': '2025-08-15T00:16:05.220280', 'open': 300.659963383994, 'high': 309.2647832898954, 'low': 298.4158274208482, 'close': 300.79496454108113, 'volume': 75923964, 'symbol': 'TSLA'}, {'date': '2025-08-16T00:16:05.220283', 'open': 296.02855008263464, 'high': 301.0431614345729, 'low': 287.12801287397554, 'close': 294.98987129832835, 'volume': 67630468, 'symbol': 'TSLA'}, {'date': '2025-08-17T00:16:05.220286', 'open': 292.7566281995947, 'high': 294.34845273878847, 'low': 288.7904035949913, 'close': 292.94169696375513, 'volume': 67237002, 'symbol': 'TSLA'}, {'date': '2025-08-18T00:16:05.220289', 'open': 288.277973795352, 'high': 295.7907670502212, 'low': 284.31646985652463, 'close': 288.2934905234244, 'volume': 110166478, 'symbol': 'TSLA'}, {'date': '2025-08-19T00:16:05.220292', 'open': 292.76246377953186, 'high': 299.1336939115898, 'low': 287.853623617899, 'close': 293.10745305407454, 'volume': 74713190, 'symbol': 'TSLA'}, {'date': '2025-08-20T00:16:05.220295', 'open': 293.01478311399967, 'high': 293.28514330998394, 'low': 290.7230666463849, 'close': 292.7590275448633, 'volume': 82963737, 'symbol': 'TSLA'}, {'date': '2025-08-21T00:16:05.220298', 'open': 293.918292318454, 'high': 294.37678338562074, 'low': 287.66940819654945, 'close': 293.2384514822338, 'volume': 83348702, 'symbol': 'TSLA'}, {'date': '2025-08-22T00:16:05.220301', 'open': 295.79537954748037, 'high': 303.2833445717225, 'low': 289.69772152899765, 'close': 295.85584473745524, 'volume': 68248361, 'symbol': 'TSLA'}, {'date': '2025-08-23T00:16:05.220304', 'open': 297.20227891464333, 'high': 301.11551807692473, 'low': 293.03003412693096, 'close': 296.41455793904646, 'volume': 105774197, 'symbol': 'TSLA'}, {'date': '2025-08-24T00:16:05.220307', 'open': 292.1092684840304, 'high': 293.57217641349706, 'low': 286.2839951973386, 'close': 291.1364833681115, 'volume': 74388045, 'symbol': 'TSLA'}], 'symbol': 'TSLA', 'timestamp': '2025-08-25T00:16:05.220535', 'tools_used': ['price_fetch', 'historical_data', 'technical_indicators']}}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 288, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226083", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted intent from ai_chat_processor section", "module": "pipeline", "function": "execute_ask_pipeline", "line": 287, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226137", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for intent: technical_analysis", "module": "pipeline", "function": "execute_ask_pipeline", "line": 288, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226191", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted symbols from ai_chat_processor section", "module": "pipeline", "function": "execute_ask_pipeline", "line": 287, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226244", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for symbols: ['TSLA']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 288, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226297", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted tools_required from ai_chat_processor section", "module": "pipeline", "function": "execute_ask_pipeline", "line": 287, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226352", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for tools_required: ['price_fetch', 'historical_data', 'technical_indicators']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 288, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226413", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted needs_data from ai_chat_processor section", "module": "pipeline", "function": "execute_ask_pipeline", "line": 287, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226468", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for needs_data: True", "module": "pipeline", "function": "execute_ask_pipeline", "line": 288, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226529", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted pipeline_id from ai_chat_processor section", "module": "pipeline", "function": "execute_ask_pipeline", "line": 287, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226595", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for pipeline_id: 93cf14c1-81c0-4ac3-acc1-1050d6cf0d75", "module": "pipeline", "function": "execute_ask_pipeline", "line": 288, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226658", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Extracted processing_timestamp from ai_chat_processor section", "module": "pipeline", "function": "execute_ask_pipeline", "line": 287, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226721", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "Value for processing_timestamp: 2025-08-25T00:16:05.223223", "module": "pipeline", "function": "execute_ask_pipeline", "line": 288, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226791", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted keys: ['response', 'data', 'intent', 'symbols', 'tools_required', 'needs_data', 'pipeline_id', 'processing_timestamp']", "module": "pipeline", "function": "execute_ask_pipeline", "line": 306, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.226985", "level": "DEBUG", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final extracted data: {'response': \"**\ud83d\udcca TSLA Comprehensive Analysis**\\n\\n\ud83d\udcb0 **Price Action:**\\n\u2022 Current: $340.01\\n\u2022 Change: +5.7% (+$5.7)\\n\u2022 Volume: 94016347.0\\n\u2022 Market Cap: Market cap data not available\\n\\n\ud83c\udfaf **Trading Recommendation:**\\n\u2022 Action: **BUY** \ud83d\udfe2\\n\u2022 Confidence: **76.0%**\\n\u2022 Risk Level: Medium\\n\\n\ud83d\udcc8 **Technical Analysis:**\\n\u2022 RSI: 53.46\\n\u2022 MACD: 3.45\\n\u2022 20-Day SMA: 329.9\\n\u2022 12-Day EMA: 331.67\\n\u2022 26-Day EMA: 328.22\\n\u2022 Support: 329.83\\n\u2022 Resistance: 334.45\\n\u2022 Bollinger Upper: 341.07\\n\u2022 Bollinger Lower: 318.73\\n\\n\ud83d\udcad **Market Sentiment:**\\nMarket sentiment: SentimentAnalysis(overall_sentiment=0.7, sentiment_label='Bullish', confidence=0.6, news_count=0, recent_headlines=[], market_fear_greed=None)\\n\\n\ud83c\udfaf **Price Targets:**\\nSupport: $329.83 \u2022 Resistance: $334.45\\n\\n\u26a0\ufe0f **Risk Management:**\\n\u2022 Stop Loss: Stop loss not configured\\n\u2022 Position Size: Recommended 1-2% of portfolio per trade\\n\\n*Analysis Quality: 70.0% \u2022 2025-08-25T00:16:05.220535*\", 'data': {'TSLA': {'current_price': 340.01, 'change': 5.7, 'change_percent': 5.7, 'volume': 94016347, 'high': 0, 'low': 0, 'open': 0, 'close': 340.01, 'name': 'TSLA', 'currency': 'USD', 'data_available': True, 'rsi': 53.46, 'macd': 3.45, 'macd_signal': None, 'macd_histogram': None, 'sma_20': 329.9, 'sma_50': None, 'ema_12': 331.67, 'ema_26': 328.22, 'support_levels': [329.83, 331.53], 'resistance_levels': [334.45, 335.66], 'bollinger_upper': 341.07, 'bollinger_middle': 329.9, 'bollinger_lower': 318.73, 'volume_sma': 96220786.6, 'technical_indicators_available': True, 'historical': [{'date': '2025-07-26T00:16:05.220189', 'open': 318.91800792541136, 'high': 324.77044510316307, 'low': 311.14572122637856, 'close': 317.55428524552366, 'volume': 84319316, 'symbol': 'TSLA'}, {'date': '2025-07-27T00:16:05.220196', 'open': 316.211013121959, 'high': 322.02692140976893, 'low': 313.1106871742095, 'close': 317.5373475755389, 'volume': 84005287, 'symbol': 'TSLA'}, {'date': '2025-07-28T00:16:05.220199', 'open': 320.26372516461447, 'high': 319.6077050842308, 'low': 316.33552250745333, 'close': 319.1762973903736, 'volume': 83135311, 'symbol': 'TSLA'}, {'date': '2025-07-29T00:16:05.220202', 'open': 316.136767729243, 'high': 320.93252021877777, 'low': 308.22478294543635, 'close': 315.19799549718806, 'volume': 86959784, 'symbol': 'TSLA'}, {'date': '2025-07-30T00:16:05.220208', 'open': 308.7904732267531, 'high': 313.1745846204016, 'low': 302.0593336900438, 'close': 309.69418694074994, 'volume': 75621330, 'symbol': 'TSLA'}, {'date': '2025-07-31T00:16:05.220213', 'open': 309.54821160854783, 'high': 317.58787030614366, 'low': 301.308115681736, 'close': 310.1635017807407, 'volume': 79881751, 'symbol': 'TSLA'}, {'date': '2025-08-01T00:16:05.220216', 'open': 305.1183884044452, 'high': 307.85367153806783, 'low': 302.7045653617551, 'close': 305.06742509977636, 'volume': 103816219, 'symbol': 'TSLA'}, {'date': '2025-08-02T00:16:05.220219', 'open': 309.97226660370853, 'high': 319.5744602214926, 'low': 307.85482382375085, 'close': 310.66088135840647, 'volume': 76126023, 'symbol': 'TSLA'}, {'date': '2025-08-03T00:16:05.220222', 'open': 314.681352567468, 'high': 317.8470616371773, 'low': 313.2975909303473, 'close': 313.9101458728979, 'volume': 107460025, 'symbol': 'TSLA'}, {'date': '2025-08-04T00:16:05.220225', 'open': 310.5325317475636, 'high': 312.3445716781955, 'low': 304.21710451607424, 'close': 309.04908421244096, 'volume': 119465901, 'symbol': 'TSLA'}, {'date': '2025-08-05T00:16:05.220228', 'open': 309.0498624282709, 'high': 314.8686392409301, 'low': 307.43198633658477, 'close': 309.1931602859671, 'volume': 76891942, 'symbol': 'TSLA'}, {'date': '2025-08-06T00:16:05.220231', 'open': 303.99753699195367, 'high': 306.79433900653277, 'low': 303.2870333819177, 'close': 305.21381740969264, 'volume': 85921249, 'symbol': 'TSLA'}, {'date': '2025-08-07T00:16:05.220234', 'open': 299.9877881235291, 'high': 303.2099748337007, 'low': 294.18387253394894, 'close': 299.75599860991383, 'volume': 75681413, 'symbol': 'TSLA'}, {'date': '2025-08-08T00:16:05.220237', 'open': 303.8223487339574, 'high': 309.0096306550249, 'low': 302.1897547245221, 'close': 303.57675562860686, 'volume': 92363973, 'symbol': 'TSLA'}, {'date': '2025-08-09T00:16:05.220240', 'open': 307.12002674130474, 'high': 314.0162671364614, 'low': 299.9423599981848, 'close': 307.64667669507554, 'volume': 86462469, 'symbol': 'TSLA'}, {'date': '2025-08-10T00:16:05.220243', 'open': 312.48415804626836, 'high': 318.96176085601246, 'low': 303.253961008373, 'close': 312.5973522328697, 'volume': 114250609, 'symbol': 'TSLA'}, {'date': '2025-08-11T00:16:05.220246', 'open': 308.23679421624246, 'high': 314.7317187686568, 'low': 308.05378151908513, 'close': 308.75858859024333, 'volume': 81375131, 'symbol': 'TSLA'}, {'date': '2025-08-12T00:16:05.220249', 'open': 304.0599039624081, 'high': 311.0206926442342, 'low': 303.7789823179896, 'close': 304.8907264839667, 'volume': 65865720, 'symbol': 'TSLA'}, {'date': '2025-08-13T00:16:05.220252', 'open': 301.743280070672, 'high': 309.7907650614076, 'low': 295.7777673845937, 'close': 301.8765012560329, 'volume': 79391297, 'symbol': 'TSLA'}, {'date': '2025-08-14T00:16:05.220255', 'open': 302.2811112726118, 'high': 304.35924950566266, 'low': 296.0768883516895, 'close': 303.5667682595295, 'volume': 93869207, 'symbol': 'TSLA'}, {'date': '2025-08-15T00:16:05.220280', 'open': 300.659963383994, 'high': 309.2647832898954, 'low': 298.4158274208482, 'close': 300.79496454108113, 'volume': 75923964, 'symbol': 'TSLA'}, {'date': '2025-08-16T00:16:05.220283', 'open': 296.02855008263464, 'high': 301.0431614345729, 'low': 287.12801287397554, 'close': 294.98987129832835, 'volume': 67630468, 'symbol': 'TSLA'}, {'date': '2025-08-17T00:16:05.220286', 'open': 292.7566281995947, 'high': 294.34845273878847, 'low': 288.7904035949913, 'close': 292.94169696375513, 'volume': 67237002, 'symbol': 'TSLA'}, {'date': '2025-08-18T00:16:05.220289', 'open': 288.277973795352, 'high': 295.7907670502212, 'low': 284.31646985652463, 'close': 288.2934905234244, 'volume': 110166478, 'symbol': 'TSLA'}, {'date': '2025-08-19T00:16:05.220292', 'open': 292.76246377953186, 'high': 299.1336939115898, 'low': 287.853623617899, 'close': 293.10745305407454, 'volume': 74713190, 'symbol': 'TSLA'}, {'date': '2025-08-20T00:16:05.220295', 'open': 293.01478311399967, 'high': 293.28514330998394, 'low': 290.7230666463849, 'close': 292.7590275448633, 'volume': 82963737, 'symbol': 'TSLA'}, {'date': '2025-08-21T00:16:05.220298', 'open': 293.918292318454, 'high': 294.37678338562074, 'low': 287.66940819654945, 'close': 293.2384514822338, 'volume': 83348702, 'symbol': 'TSLA'}, {'date': '2025-08-22T00:16:05.220301', 'open': 295.79537954748037, 'high': 303.2833445717225, 'low': 289.69772152899765, 'close': 295.85584473745524, 'volume': 68248361, 'symbol': 'TSLA'}, {'date': '2025-08-23T00:16:05.220304', 'open': 297.20227891464333, 'high': 301.11551807692473, 'low': 293.03003412693096, 'close': 296.41455793904646, 'volume': 105774197, 'symbol': 'TSLA'}, {'date': '2025-08-24T00:16:05.220307', 'open': 292.1092684840304, 'high': 293.57217641349706, 'low': 286.2839951973386, 'close': 291.1364833681115, 'volume': 74388045, 'symbol': 'TSLA'}], 'symbol': 'TSLA', 'timestamp': '2025-08-25T00:16:05.220535', 'tools_used': ['price_fetch', 'historical_data', 'technical_indicators']}}, 'intent': 'technical_analysis', 'symbols': ['TSLA'], 'tools_required': ['price_fetch', 'historical_data', 'technical_indicators'], 'needs_data': True, 'pipeline_id': '93cf14c1-81c0-4ac3-acc1-1050d6cf0d75', 'processing_timestamp': '2025-08-25T00:16:05.223223'}", "module": "pipeline", "function": "execute_ask_pipeline", "line": 307, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.227138", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\ud83c\udfaf Final response length: 887 characters", "module": "pipeline", "function": "execute_ask_pipeline", "line": 334, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.227222", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.pipeline", "message": "\u2705 Pipeline completed successfully in 26.49s", "module": "pipeline", "function": "execute_ask_pipeline", "line": 344, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.227383", "level": "INFO", "logger": "__main__", "message": "Pipeline completed. Available keys in processing_results: ['response', 'data', 'intent', 'symbols', 'tools_required', 'needs_data', 'pipeline_id', 'processing_timestamp']", "module": "client", "function": "handle_ask_command", "line": 209, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.227482", "level": "INFO", "logger": "__main__", "message": "Found response, length: 887", "module": "client", "function": "handle_ask_command", "line": 214, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.227647", "level": "INFO", "logger": "__main__", "message": "\u23f3 Auditing response before sending...", "module": "client", "function": "handle_ask_command", "line": 229, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.227849", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udd0d Starting response audit...", "module": "response_audit", "function": "audit_response", "line": 54, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.228209", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udd22 Performing numeric validation against real market data...", "module": "response_audit", "function": "_check_numeric_accuracy", "line": 254, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.229750", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udd0d Audit completed - Passed: False", "module": "response_audit", "function": "audit_response", "line": 94, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.229851", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udcca Quality Score: 0.65", "module": "response_audit", "function": "audit_response", "line": 95, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.229919", "level": "WARNING", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udd0d Issues found: ['Potential factual issue: unrealistic future year 2053']", "module": "response_audit", "function": "audit_response", "line": 97, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.229984", "level": "INFO", "logger": "src.bot.pipeline.commands.ask.stages.response_audit", "message": "\ud83d\udd0d Warnings: ['Found 21 potentially incomplete sentences or fragments', 'Symbol BUY mentioned but no market data available', 'Symbol lysis mentioned but no market data available']", "module": "response_audit", "function": "audit_response", "line": 99, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.230042", "level": "ERROR", "logger": "__main__", "message": "Response audit failed: ['Potential factual issue: unrealistic future year 2053']", "module": "client", "function": "handle_ask_command", "line": 240, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.421563", "level": "DEBUG", "logger": "discord.gateway", "message": "For Shard ID None: WebSocket Event: {'t': 'MESSAGE_UPDATE', 's': 5, 'op': 0, 'd': {'webhook_id': '1404506961776480316', 'type': 20, 'tts': False, 'timestamp': '2025-08-25T00:15:38.664000+00:00', 'position': 0, 'pinned': False, 'mentions': [], 'mention_roles': [], 'mention_everyone': False, 'member': {'roles': ['1408844110642544836'], 'premium_since': None, 'pending': False, 'nick': None, 'mute': False, 'joined_at': '2025-08-23T16:03:31.143047+00:00', 'flags': 1, 'deaf': False, 'communication_disabled_until': None, 'banner': None, 'avatar': None}, 'interaction_metadata': {'user': {'username': '_discorsebot_55978', 'public_flags': 0, 'primary_guild': None, 'id': '1281632159253397545', 'global_name': '__69_420__', 'display_name_styles': None, 'discriminator': '0', 'collectibles': None, 'clan': None, 'avatar_decoration_data': None, 'avatar': '687d1ecb582f67dc20f5d82b4d5a28aa'}, 'type': 2, 'name': 'ask', 'id': '1409330345408598159', 'command_type': 1, 'authorizing_integration_owners': {'0': '1304548446090301440'}}, 'interaction': {'user': {'username': '_discorsebot_55978', 'public_flags': 0, 'primary_guild': None, 'id': '1281632159253397545', 'global_name': '__69_420__', 'display_name_styles': None, 'discriminator': '0', 'collectibles': None, 'clan': None, 'avatar_decoration_data': None, 'avatar': '687d1ecb582f67dc20f5d82b4d5a28aa'}, 'type': 2, 'name': 'ask', 'member': {'roles': ['1304548446090301448', '1304548446090301444'], 'premium_since': None, 'pending': False, 'nick': None, 'mute': False, 'joined_at': '2024-11-08T20:49:46.901000+00:00', 'flags': 0, 'deaf': False, 'communication_disabled_until': None, 'banner': None, 'avatar': None}, 'id': '1409330345408598159'}, 'id': '1409330346360836208', 'flags': 0, 'embeds': [], 'edited_timestamp': None, 'content': '\u274c Response validation failed. Please try again.', 'components': [], 'channel_type': 0, 'channel_id': '1304548446501601313', 'author': {'username': 'NHX.ai', 'public_flags': 0, 'primary_guild': None, 'id': '1404506961776480316', 'global_name': None, 'display_name_styles': None, 'discriminator': '2574', 'collectibles': None, 'clan': None, 'bot': True, 'avatar_decoration_data': None, 'avatar': '43aaac0b7392e3c274c05127dc373f50'}, 'attachments': [], 'application_id': '1404506961776480316', 'guild_id': '1304548446090301440'}}", "module": "gateway", "function": "received_message", "line": 500, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.421957", "level": "DEBUG", "logger": "discord.client", "message": "Dispatching event socket_event_type", "module": "client", "function": "dispatch", "line": 462, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.422145", "level": "DEBUG", "logger": "discord.client", "message": "Dispatching event raw_message_edit", "module": "client", "function": "dispatch", "line": 462, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.422300", "level": "DEBUG", "logger": "discord.client", "message": "Dispatching event message_edit", "module": "client", "function": "dispatch", "line": 462, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.444308", "level": "DEBUG", "logger": "discord.webhook.async_", "message": "Webhook ID 1404506961776480316 with POST https://discord.com/api/v10/webhooks/1404506961776480316/aW50ZXJhY3Rpb246MTQwOTMzMDM0NTQwODU5ODE1OTo5NThpbDM2Q3RKcUpsekcwODFWTjEydjhVQUhoYkJwTjRLdVpCOXBuRk9zcFVpQzQwdXNWaExXbGg0ZjZBdW1ZZXBrTUxKVFZFS3ZDaTl3SnFkN3VkNjhsaE0xUzk1Rk1KYzY4NTB3SHVKcW5KVk9TRGU3ZjFBaUhqY2l5T05NMw has returned status code 200", "module": "async_", "function": "request", "line": 180, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:05.444986", "level": "DEBUG", "logger": "discord.client", "message": "Dispatching event app_command_completion", "module": "client", "function": "dispatch", "line": 462, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:17.879376", "level": "DEBUG", "logger": "discord.gateway", "message": "Keeping shard ID None websocket alive with sequence 5.", "module": "gateway", "function": "run", "line": 167, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:17.922765", "level": "DEBUG", "logger": "discord.gateway", "message": "For Shard ID None: WebSocket Event: {'t': None, 's': None, 'op': 11, 'd': None}", "module": "gateway", "function": "received_message", "line": 500, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:59.131184", "level": "DEBUG", "logger": "discord.gateway", "message": "Keeping shard ID None websocket alive with sequence 5.", "module": "gateway", "function": "run", "line": 167, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:16:59.169840", "level": "DEBUG", "logger": "discord.gateway", "message": "For Shard ID None: WebSocket Event: {'t': None, 's': None, 'op': 11, 'd': None}", "module": "gateway", "function": "received_message", "line": 500, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:17:40.382916", "level": "DEBUG", "logger": "discord.gateway", "message": "Keeping shard ID None websocket alive with sequence 5.", "module": "gateway", "function": "run", "line": 167, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:17:40.473918", "level": "DEBUG", "logger": "discord.gateway", "message": "For Shard ID None: WebSocket Event: {'t': None, 's': None, 'op': 11, 'd': None}", "module": "gateway", "function": "received_message", "line": 500, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:18:21.635189", "level": "DEBUG", "logger": "discord.gateway", "message": "Keeping shard ID None websocket alive with sequence 5.", "module": "gateway", "function": "run", "line": 167, "correlation_id": null}
bot-1  | {"timestamp": "2025-08-25T00:18:21.681806", "level": "DEBUG", "logger": "discord.gateway", "message": "For Shard ID None: WebSocket Event: {'t': None, 's': None, 'op': 11, 'd': None}", "module": "gateway", "function": "received_message", "line": 500, "correlation_id": null}
ami@fedora-workstation:~/Desktop/tradingview-automation$ 