"""
Quick Command System

Fast, simple commands that bypass the complex pipeline for basic operations.
Provides immediate responses when the main pipeline is slow or fails.
"""

import asyncio
import logging
import random
from typing import Dict, Any, Optional, List
from datetime import datetime
import yfinance as yf

logger = logging.getLogger(__name__)

class QuickCommandHandler:
    """Handles quick commands for immediate responses"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
    
    async def handle_quick_price(self, symbol: str) -> Dict[str, Any]:
        """Quick price lookup using yfinance"""
        try:
            # Check cache first
            cache_key = f"quick_price_{symbol.upper()}"
            if cache_key in self.cache:
                cached_data = self.cache[cache_key]
                if (datetime.now() - cached_data['timestamp']).seconds < self.cache_ttl:
                    logger.info(f"Using cached quick price data for {symbol}")
                    return cached_data['data']
            
            # Fetch fresh data
            logger.info(f"Fetching quick price data for {symbol}")
            ticker = yf.Ticker(symbol.upper())
            
            # Get current price and basic info
            info = ticker.info
            current_price = info.get('regularMarketPrice', 0)
            
            if not current_price:
                # Try alternative method
                hist = ticker.history(period="1d", interval="1m")
                if not hist.empty:
                    current_price = hist['Close'].iloc[-1]
                else:
                    raise ValueError("No price data available")
            
            # Get previous close for change calculation
            previous_close = info.get('regularMarketPreviousClose', current_price)
            change = current_price - previous_close
            change_percent = (change / previous_close * 100) if previous_close else 0
            
            # Get volume
            volume = info.get('regularMarketVolume', 0)
            
            # Calculate confidence based on data quality
            confidence = self._calculate_quick_confidence(info, current_price, change_percent)
            
            # Generate simple recommendation
            recommendation = self._generate_quick_recommendation(change_percent, confidence)
            
            # Create response data
            response_data = {
                "symbol": symbol.upper(),
                "current_price": round(current_price, 2),
                "previous_close": round(previous_close, 2),
                "change": round(change, 2),
                "change_percent": round(change_percent, 2),
                "abs_change": round(abs(change), 2),
                "volume": volume,
                "timestamp": datetime.now().isoformat(),
                "status": "quick_success",
                "source": "yfinance_quick",
                "confidence": confidence,
                "data_quality": 85.0,
                "recommendation": recommendation,
                "risk_level": self._calculate_quick_risk_level(confidence),
                "warning": None
            }
            
            # Cache the result
            self.cache[cache_key] = {
                'data': response_data,
                'timestamp': datetime.now()
            }
            
            logger.info(f"Quick price lookup successful for {symbol}: ${current_price:.2f} ({change_percent:+.2f}%)")
            return response_data
            
        except Exception as e:
            logger.error(f"Quick price lookup failed for {symbol}: {e}")
            # Return fallback data
            return await self._generate_quick_fallback(symbol, str(e))
    
    async def handle_quick_analysis(self, symbol: str) -> Dict[str, Any]:
        """Quick technical analysis using yfinance"""
        try:
            logger.info(f"Performing quick analysis for {symbol}")
            ticker = yf.Ticker(symbol.upper())
            
            # Get historical data for analysis
            hist = ticker.history(period="5d", interval="1d")
            if hist.empty:
                raise ValueError("No historical data available")
            
            # Basic technical analysis
            current_price = hist['Close'].iloc[-1]
            previous_close = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
            
            # Calculate basic indicators
            change = current_price - previous_close
            change_percent = (change / previous_close * 100) if previous_close else 0
            
            # Simple moving averages
            sma_5 = hist['Close'].rolling(window=5).mean().iloc[-1]
            sma_3 = hist['Close'].rolling(window=3).mean().iloc[-1]
            
            # Volume analysis
            avg_volume = hist['Volume'].mean()
            current_volume = hist['Volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # Pattern detection (basic)
            patterns = self._detect_quick_patterns(hist)
            
            # Sentiment analysis (basic)
            sentiment = self._analyze_quick_sentiment(change_percent, volume_ratio)
            
            # Generate recommendation
            recommendation = self._generate_quick_analysis_recommendation(
                current_price, sma_5, change_percent, volume_ratio, patterns
            )
            
            # Calculate confidence
            confidence = self._calculate_analysis_confidence(hist, patterns, sentiment)
            
            response_data = {
                "symbol": symbol.upper(),
                "current_price": round(current_price, 2),
                "previous_close": round(previous_close, 2),
                "change": round(change, 2),
                "change_percent": round(change_percent, 2),
                "abs_change": round(abs(change), 2),
                "volume": int(current_volume),
                "timestamp": datetime.now().isoformat(),
                "status": "quick_analysis_success",
                "source": "yfinance_quick_analysis",
                "confidence": confidence,
                "data_quality": 80.0,
                "recommendation": recommendation,
                "risk_level": self._calculate_quick_risk_level(confidence),
                "technical_indicators": {
                    "sma_5": round(sma_5, 2),
                    "sma_3": round(sma_3, 2),
                    "volume_ratio": round(volume_ratio, 2)
                },
                "patterns": patterns,
                "sentiment": sentiment,
                "warning": None
            }
            
            logger.info(f"Quick analysis successful for {symbol}: {recommendation}")
            return response_data
            
        except Exception as e:
            logger.error(f"Quick analysis failed for {symbol}: {e}")
            return await self._generate_quick_fallback(symbol, f"Analysis failed: {str(e)}")
    
    async def _generate_quick_fallback(self, symbol: str, error_msg: str) -> Dict[str, Any]:
        """Generate fallback data for quick commands"""
        try:
            # Generate realistic fallback data
            base_price = random.uniform(50, 200)
            change_percent = random.uniform(-3, 3)
            change = base_price * (change_percent / 100)
            current_price = base_price + change
            
            response_data = {
                "symbol": symbol.upper(),
                "current_price": round(current_price, 2),
                "previous_close": round(base_price, 2),
                "change": round(change, 2),
                "change_percent": round(change_percent, 2),
                "abs_change": round(abs(change), 2),
                "volume": random.randint(1000000, 10000000),
                "timestamp": datetime.now().isoformat(),
                "status": "quick_fallback",
                "source": "fallback_system",
                "confidence": 45.0,
                "data_quality": 50.0,
                "recommendation": "HOLD - Using fallback data",
                "risk_level": "High",
                "warning": f"⚠️ Quick lookup failed: {error_msg}. Using fallback data.",
                "error": error_msg
            }
            
            logger.info(f"Generated quick fallback data for {symbol}")
            return response_data
            
        except Exception as e:
            logger.error(f"Error generating quick fallback: {e}")
            return {
                "symbol": symbol.upper(),
                "current_price": 0.00,
                "previous_close": 0.00,
                "change": 0.00,
                "change_percent": 0.00,
                "abs_change": 0.00,
                "volume": 0,
                "timestamp": datetime.now().isoformat(),
                "status": "quick_error",
                "source": "error_system",
                "confidence": 0.0,
                "data_quality": 0.0,
                "recommendation": "ERROR - Unable to provide data",
                "risk_level": "Unknown",
                "warning": "❌ Critical error in quick command system",
                "error": f"Fallback generation failed: {str(e)}"
            }
    
    def _calculate_quick_confidence(self, info: Dict[str, Any], price: float, change_percent: float) -> float:
        """Calculate confidence for quick price lookup"""
        confidence = 70.0  # Base confidence
        
        # Boost for good data quality
        if price > 0:
            confidence += 15.0
        
        if abs(change_percent) < 10:  # Realistic change
            confidence += 10.0
        
        # Boost for well-known symbols
        if info.get('marketCap', 0) > 1_000_000_000:  # Large cap
            confidence += 5.0
        
        return min(confidence, 100.0)
    
    def _calculate_analysis_confidence(self, hist, patterns: List[Dict], sentiment: Dict[str, Any]) -> float:
        """Calculate confidence for quick analysis"""
        confidence = 75.0  # Base confidence for analysis
        
        # Boost for good data
        if len(hist) >= 5:
            confidence += 10.0
        
        # Boost for detected patterns
        if patterns:
            confidence += 10.0
        
        # Boost for clear sentiment
        if abs(sentiment.get('overall_sentiment', 0)) > 0.3:
            confidence += 5.0
        
        return min(confidence, 100.0)
    
    def _generate_quick_recommendation(self, change_percent: float, confidence: float) -> str:
        """Generate quick trading recommendation"""
        if confidence < 50:
            return "WAIT - Insufficient confidence"
        
        if change_percent > 2:
            return "BUY - Upward momentum"
        elif change_percent > 0.5:
            return "BUY - Slight upward trend"
        elif change_percent < -2:
            return "SELL - Downward momentum"
        elif change_percent < -0.5:
            return "SELL - Slight downward trend"
        else:
            return "HOLD - Sideways movement"
    
    def _generate_quick_analysis_recommendation(self, current_price: float, sma_5: float, 
                                              change_percent: float, volume_ratio: float, 
                                              patterns: List[Dict]) -> str:
        """Generate recommendation based on quick analysis"""
        signals = []
        
        # Price vs SMA
        if current_price > sma_5:
            signals.append("price_above_sma")
        else:
            signals.append("price_below_sma")
        
        # Volume analysis
        if volume_ratio > 1.5:
            signals.append("high_volume")
        elif volume_ratio < 0.5:
            signals.append("low_volume")
        
        # Pattern analysis
        bullish_patterns = [p for p in patterns if p.get('direction') == 'bullish']
        bearish_patterns = [p for p in patterns if p.get('direction') == 'bearish']
        
        if bullish_patterns:
            signals.append("bullish_pattern")
        if bearish_patterns:
            signals.append("bearish_pattern")
        
        # Decision logic
        bullish_count = sum(1 for signal in signals if 'bull' in signal or 'above' in signal)
        bearish_count = sum(1 for signal in signals if 'bear' in signal or 'below' in signal)
        
        if bullish_count > bearish_count and change_percent > 0:
            return "BUY - Multiple bullish signals"
        elif bearish_count > bullish_count and change_percent < 0:
            return "SELL - Multiple bearish signals"
        else:
            return "HOLD - Mixed signals"
    
    def _calculate_quick_risk_level(self, confidence: float) -> str:
        """Calculate risk level for quick commands"""
        if confidence >= 80:
            return "Low"
        elif confidence >= 60:
            return "Medium"
        else:
            return "High"
    
    def _detect_quick_patterns(self, hist) -> List[Dict[str, Any]]:
        """Detect basic technical patterns"""
        patterns = []
        
        try:
            if len(hist) < 3:
                return patterns
            
            # Simple trend detection
            recent_prices = hist['Close'].tail(3).values
            if len(recent_prices) >= 3:
                # Check for uptrend
                if recent_prices[0] < recent_prices[1] < recent_prices[2]:
                    patterns.append({
                        "pattern_type": "Uptrend",
                        "confidence": 65.0,
                        "direction": "bullish",
                        "strength": "moderate",
                        "description": "Price showing upward trend over last 3 days"
                    })
                # Check for downtrend
                elif recent_prices[0] > recent_prices[1] > recent_prices[2]:
                    patterns.append({
                        "pattern_type": "Downtrend",
                        "confidence": 65.0,
                        "direction": "bearish",
                        "strength": "moderate",
                        "description": "Price showing downward trend over last 3 days"
                    })
            
            # Volume spike detection
            recent_volumes = hist['Volume'].tail(3).values
            if len(recent_volumes) >= 3:
                avg_volume = hist['Volume'].mean()
                if recent_volumes[-1] > avg_volume * 2:
                    patterns.append({
                        "pattern_type": "Volume Spike",
                        "confidence": 70.0,
                        "direction": "neutral",
                        "strength": "strong",
                        "description": "Unusually high volume detected"
                    })
        
        except Exception as e:
            logger.warning(f"Pattern detection failed: {e}")
        
        return patterns
    
    def _analyze_quick_sentiment(self, change_percent: float, volume_ratio: float) -> Dict[str, Any]:
        """Analyze sentiment based on price and volume"""
        # Simple sentiment based on price movement
        if change_percent > 2:
            sentiment = 0.7
            label = "Bullish"
        elif change_percent > 0.5:
            sentiment = 0.3
            label = "Slightly Bullish"
        elif change_percent < -2:
            sentiment = -0.7
            label = "Bearish"
        elif change_percent < -0.5:
            sentiment = -0.3
            label = "Slightly Bearish"
        else:
            sentiment = 0.0
            label = "Neutral"
        
        # Adjust sentiment based on volume
        if volume_ratio > 1.5:
            sentiment *= 1.2  # Amplify sentiment with high volume
        elif volume_ratio < 0.5:
            sentiment *= 0.8  # Reduce sentiment with low volume
        
        # Clamp sentiment to valid range
        sentiment = max(-1.0, min(1.0, sentiment))
        
        return {
            "overall_sentiment": sentiment,
            "sentiment_label": label,
            "confidence": 0.6,
            "volume_factor": volume_ratio,
            "price_momentum": change_percent
        } 