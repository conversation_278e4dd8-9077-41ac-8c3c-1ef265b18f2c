"""
Conversation Memory Service

Provides conversation memory and context management for:
- Follow-up question handling
- Conversation state persistence
- User preference learning
- Context-aware responses
- Memory optimization
"""

import logging
import asyncio
import time
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib

logger = logging.getLogger(__name__)

class ConversationType(Enum):
    """Types of conversations."""
    STOCK_ANALYSIS = "stock_analysis"
    TECHNICAL_EDUCATION = "technical_education"
    RISK_ASSESSMENT = "risk_assessment"
    PORTFOLIO_REVIEW = "portfolio_review"
    MARKET_OVERVIEW = "market_overview"
    GENERAL_QUERY = "general_query"

class MemoryPriority(Enum):
    """Memory priority levels."""
    LOW = "low"           # General information, easily replaceable
    MEDIUM = "medium"     # User preferences, moderate importance
    HIGH = "high"         # Critical context, user-specific data
    CRITICAL = "critical" # Essential information, never delete

@dataclass
class ConversationTurn:
    """A single turn in a conversation."""
    turn_id: str
    timestamp: datetime
    user_query: str
    ai_response: str
    symbols_mentioned: List[str]
    tools_used: List[str]
    response_quality: float  # 0.0 to 1.0
    user_feedback: Optional[str] = None
    metadata: Dict[str, Any] = None

@dataclass
class ConversationSession:
    """A complete conversation session."""
    session_id: str
    user_id: str
    conversation_type: ConversationType
    start_time: datetime
    last_activity: datetime
    turns: List[ConversationTurn]
    symbols_analyzed: List[str]
    user_preferences: Dict[str, Any]
    context_summary: str
    memory_priority: MemoryPriority
    is_active: bool = True

@dataclass
class UserPreference:
    """User preferences and learning data."""
    user_id: str
    experience_level: str  # "beginner", "intermediate", "expert"
    preferred_models: List[str]
    response_style: str  # "detailed", "concise", "educational", "actionable"
    risk_tolerance: str  # "conservative", "moderate", "aggressive"
    favorite_symbols: List[str]
    learning_topics: List[str]
    last_updated: datetime
    usage_patterns: Dict[str, int]

class ConversationMemoryService:
    """Service for managing conversation memory and context."""
    
    def __init__(self, max_sessions: int = 100, max_turns_per_session: int = 50):
        """Initialize the conversation memory service."""
        self.logger = logging.getLogger(__name__)
        
        # Memory configuration with bounds checking
        if max_sessions < 10 or max_sessions > 1000:
            raise ValueError("max_sessions must be between 10 and 1000")
        if max_turns_per_session < 5 or max_turns_per_session > 500:
            raise ValueError("max_turns_per_session must be between 5 and 500")
        
        self.max_sessions = max_sessions
        self.max_turns_per_session = max_turns_per_session
        
        # Active conversations
        self.active_sessions: Dict[str, ConversationSession] = {}
        
        # User preferences
        self.user_preferences: Dict[str, UserPreference] = {}
        
        # Memory optimization settings
        self.memory_cleanup_interval = 3600  # 1 hour
        self.last_cleanup = time.time()
        
        # Context summarization cache
        self.context_cache: Dict[str, str] = {}
        self.cache_ttl = 1800  # 30 minutes
        
        # Background cleanup task management
        self._cleanup_task = None
        self._cleanup_running = False
    
    async def start_background_cleanup(self):
        """Start background memory cleanup task - must be called in async context."""
        if self._cleanup_running:
            return
        
        self._cleanup_running = True
        
        async def cleanup_task():
            while self._cleanup_running:
                try:
                    await self._cleanup_old_memories()
                    await asyncio.sleep(self.memory_cleanup_interval)
                except Exception as e:
                    self.logger.error(f"Error in background cleanup: {e}")
                    await asyncio.sleep(300)  # Wait 5 minutes on error
        
        # Create and store the task reference
        self._cleanup_task = asyncio.create_task(cleanup_task())
        self.logger.info("Background memory cleanup task started")
    
    async def stop_background_cleanup(self):
        """Stop background memory cleanup task."""
        if not self._cleanup_running:
            return
        
        self._cleanup_running = False
        
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
        
        self.logger.info("Background memory cleanup task stopped")
    
    def __del__(self):
        """Cleanup when object is destroyed."""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
    
    def create_conversation_session(self, user_id: str, initial_query: str, 
                                   conversation_type: ConversationType = ConversationType.GENERAL_QUERY) -> str:
        """
        Create a new conversation session.
        
        Args:
            user_id: User identifier
            initial_query: First query in the conversation
            conversation_type: Type of conversation
            
        Returns:
            Session ID for the new conversation
        """
        try:
            # Generate unique session ID
            session_id = self._generate_session_id(user_id, initial_query)
            
            # Create initial turn
            initial_turn = ConversationTurn(
                turn_id=self._generate_turn_id(session_id, 1),
                timestamp=datetime.now(),
                user_query=initial_query,
                ai_response="",  # Will be filled after AI response
                symbols_mentioned=self._extract_symbols(initial_query),
                tools_used=[],
                response_quality=0.0,
                metadata={}
            )
            
            # Create session
            session = ConversationSession(
                session_id=session_id,
                user_id=user_id,
                conversation_type=conversation_type,
                start_time=datetime.now(),
                last_activity=datetime.now(),
                turns=[initial_turn],
                symbols_analyzed=initial_turn.symbols_mentioned,
                user_preferences=self._get_user_preferences(user_id),
                context_summary="",
                memory_priority=self._determine_memory_priority(conversation_type)
            )
            
            # Store session
            self.active_sessions[session_id] = session
            
            # Cleanup old sessions if needed
            if len(self.active_sessions) > self.max_sessions:
                self._cleanup_old_sessions()
            
            self.logger.info(f"Created conversation session {session_id} for user {user_id}")
            return session_id
            
        except Exception as e:
            self.logger.error(f"Error creating conversation session: {e}")
            return ""
    
    def add_conversation_turn(self, session_id: str, user_query: str, ai_response: str,
                             symbols_mentioned: List[str] = None, tools_used: List[str] = None,
                             response_quality: float = 0.5) -> bool:
        """
        Add a new turn to an existing conversation.
        
        Args:
            session_id: Session identifier
            user_query: User's query
            ai_response: AI's response
            symbols_mentioned: Symbols mentioned in this turn
            tools_used: Tools used to generate response
            response_quality: Quality score for the response
            
        Returns:
            True if turn was added successfully
        """
        try:
            if session_id not in self.active_sessions:
                self.logger.warning(f"Session {session_id} not found")
                return False
            
            session = self.active_sessions[session_id]
            
            # Create new turn
            turn = ConversationTurn(
                turn_id=self._generate_turn_id(session_id, len(session.turns) + 1),
                timestamp=datetime.now(),
                user_query=user_query,
                ai_response=ai_response,
                symbols_mentioned=symbols_mentioned or self._extract_symbols(user_query),
                tools_used=tools_used or [],
                response_quality=response_quality,
                metadata={}
            )
            
            # Add turn to session
            session.turns.append(turn)
            session.last_activity = datetime.now()
            
            # Update symbols analyzed
            session.symbols_analyzed.extend(turn.symbols_mentioned)
            session.symbols_analyzed = list(set(session.symbols_analyzed))  # Remove duplicates
            
            # Limit turns per session
            if len(session.turns) > self.max_turns_per_session:
                session.turns = session.turns[-self.max_turns_per_session:]
            
            # Update context summary
            session.context_summary = self._generate_context_summary(session)
            
            self.logger.info(f"Added turn {turn.turn_id} to session {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding conversation turn: {e}")
            return False
    
    def get_conversation_context(self, session_id: str, max_turns: int = 5) -> Dict[str, Any]:
        """
        Get conversation context for follow-up questions.
        
        Args:
            session_id: Session identifier
            max_turns: Maximum number of recent turns to include
            
        Returns:
            Conversation context information
        """
        try:
            if session_id not in self.active_sessions:
                return {}
            
            session = self.active_sessions[session_id]
            
            # Get recent turns
            recent_turns = session.turns[-max_turns:] if len(session.turns) > max_turns else session.turns
            
            # Build context
            context = {
                'session_id': session_id,
                'conversation_type': session.conversation_type.value,
                'symbols_analyzed': session.symbols_analyzed,
                'user_preferences': session.user_preferences,
                'recent_turns': [
                    {
                        'query': turn.user_query,
                        'response': turn.ai_response[:200] + "..." if len(turn.ai_response) > 200 else turn.ai_response,
                        'symbols': turn.symbols_mentioned,
                        'tools': turn.tools_used,
                        'timestamp': turn.timestamp.isoformat()
                    }
                    for turn in recent_turns
                ],
                'context_summary': session.context_summary,
                'session_age_minutes': int((datetime.now() - session.start_time).total_seconds() / 60)
            }
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error getting conversation context: {e}")
            return {}
    
    def is_follow_up_question(self, session_id: str, query: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Determine if a query is a follow-up question.
        
        Args:
            session_id: Session identifier
            query: Current query
            
        Returns:
            Tuple of (is_follow_up, context_info)
        """
        try:
            if session_id not in self.active_sessions:
                return False, {}
            
            session = self.active_sessions[session_id]
            
            # Check for follow-up indicators
            follow_up_indicators = [
                'it', 'that', 'this', 'they', 'them', 'those', 'these',
                'what about', 'how about', 'and', 'also', 'too', 'as well',
                'in addition', 'furthermore', 'moreover', 'besides'
            ]
            
            query_lower = query.lower()
            is_follow_up = any(indicator in query_lower for indicator in follow_up_indicators)
            
            # Check if query references previous symbols
            previous_symbols = set()
            for turn in session.turns:
                previous_symbols.update(turn.symbols_mentioned)
            
            current_symbols = set(self._extract_symbols(query))
            symbol_overlap = len(current_symbols.intersection(previous_symbols)) > 0
            
            # Check session age
            session_age_hours = (datetime.now() - session.start_time).total_seconds() / 3600
            recent_session = session_age_hours < 24  # Within 24 hours
            
            # Determine if it's a follow-up
            is_follow_up = is_follow_up or symbol_overlap or recent_session
            
            context_info = {
                'is_follow_up': is_follow_up,
                'symbol_overlap': symbol_overlap,
                'session_age_hours': session_age_hours,
                'previous_symbols': list(previous_symbols),
                'current_symbols': list(current_symbols)
            }
            
            return is_follow_up, context_info
            
        except Exception as e:
            self.logger.error(f"Error determining follow-up question: {e}")
            return False, {}
    
    def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]):
        """Update user preferences based on conversation patterns."""
        try:
            if user_id not in self.user_preferences:
                self.user_preferences[user_id] = UserPreference(
                    user_id=user_id,
                    experience_level="intermediate",
                    preferred_models=[],
                    response_style="detailed",
                    risk_tolerance="moderate",
                    favorite_symbols=[],
                    learning_topics=[],
                    last_updated=datetime.now(),
                    usage_patterns={}
                )
            
            user_pref = self.user_preferences[user_id]
            
            # Update preferences
            for key, value in preferences.items():
                if hasattr(user_pref, key):
                    setattr(user_pref, key, value)
            
            # Update last modified
            user_pref.last_updated = datetime.now()
            
            self.logger.info(f"Updated preferences for user {user_id}")
            
        except Exception as e:
            self.logger.error(f"Error updating user preferences: {e}")
    
    def _generate_session_id(self, user_id: str, query: str) -> str:
        """Generate a unique session ID."""
        timestamp = datetime.now().isoformat()
        content = f"{user_id}:{query}:{timestamp}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _generate_turn_id(self, session_id: str, turn_number: int) -> str:
        """Generate a unique turn ID."""
        return f"{session_id}_turn_{turn_number}"
    
    def _extract_symbols(self, text: str) -> List[str]:
        """Extract stock symbols from text."""
        import re
        # Look for $ followed by uppercase letters
        symbol_pattern = r'\$([A-Z]+)'
        matches = re.findall(symbol_pattern, text)
        return list(set(matches))
    
    def _get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user preferences for a session."""
        if user_id in self.user_preferences:
            user_pref = self.user_preferences[user_id]
            return asdict(user_pref)
        return {}
    
    def _determine_memory_priority(self, conversation_type: ConversationType) -> MemoryPriority:
        """Determine memory priority for a conversation type."""
        priority_mapping = {
            ConversationType.STOCK_ANALYSIS: MemoryPriority.MEDIUM,
            ConversationType.TECHNICAL_EDUCATION: MemoryPriority.HIGH,
            ConversationType.RISK_ASSESSMENT: MemoryPriority.HIGH,
            ConversationType.PORTFOLIO_REVIEW: MemoryPriority.CRITICAL,
            ConversationType.MARKET_OVERVIEW: MemoryPriority.LOW,
            ConversationType.GENERAL_QUERY: MemoryPriority.LOW
        }
        return priority_mapping.get(conversation_type, MemoryPriority.MEDIUM)
    
    def _generate_context_summary(self, session: ConversationSession) -> str:
        """Generate a summary of conversation context."""
        try:
            if not session.turns:
                return "New conversation started"
            
            # Get key information from recent turns
            recent_turns = session.turns[-3:]  # Last 3 turns
            symbols = list(set(session.symbols_analyzed))
            tools_used = list(set([tool for turn in recent_turns for tool in turn.tools_used]))
            
            summary_parts = []
            
            if symbols:
                summary_parts.append(f"Analyzing: {', '.join(symbols[:3])}")
            
            if tools_used:
                summary_parts.append(f"Tools: {', '.join(tools_used[:3])}")
            
            summary_parts.append(f"Turns: {len(session.turns)}")
            
            return " | ".join(summary_parts)
            
        except Exception as e:
            self.logger.error(f"Error generating context summary: {e}")
            return "Context summary unavailable"
    
    def _cleanup_old_sessions(self):
        """Clean up old sessions to free memory."""
        try:
            if len(self.active_sessions) <= self.max_sessions:
                return
            
            # Sort sessions by last activity and priority
            sessions_to_cleanup = []
            for session_id, session in self.active_sessions.items():
                age_hours = (datetime.now() - session.last_activity).total_seconds() / 3600
                priority_score = {
                    MemoryPriority.LOW: 1,
                    MemoryPriority.MEDIUM: 2,
                    MemoryPriority.HIGH: 3,
                    MemoryPriority.CRITICAL: 4
                }.get(session.memory_priority, 2)
                
                # Calculate cleanup score (lower = more likely to be cleaned up)
                cleanup_score = age_hours / priority_score
                sessions_to_cleanup.append((session_id, cleanup_score))
            
            # Sort by cleanup score (highest first)
            sessions_to_cleanup.sort(key=lambda x: x[1], reverse=True)
            
            # Remove oldest sessions until we're under the limit
            sessions_to_remove = len(self.active_sessions) - self.max_sessions
            for session_id, _ in sessions_to_cleanup[:sessions_to_remove]:
                del self.active_sessions[session_id]
                self.logger.info(f"Cleaned up old session {session_id}")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old sessions: {e}")
    
    async def _cleanup_old_memories(self):
        """Background task to clean up old memories."""
        try:
            self._cleanup_old_sessions()
            self.last_cleanup = time.time()
            
        except Exception as e:
            self.logger.error(f"Error in memory cleanup: {e}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        try:
            total_turns = sum(len(session.turns) for session in self.active_sessions.values())
            total_symbols = len(set([symbol for session in self.active_sessions.values() 
                                   for symbol in session.symbols_analyzed]))
            
            stats = {
                'active_sessions': len(self.active_sessions),
                'total_turns': total_turns,
                'total_symbols': total_symbols,
                'user_preferences': len(self.user_preferences),
                'memory_usage_mb': self._estimate_memory_usage(),
                'last_cleanup': self.last_cleanup,
                'cleanup_interval': self.memory_cleanup_interval
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting memory stats: {e}")
            return {'error': str(e)}
    
    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage in MB."""
        try:
            # Rough estimation
            session_size = len(self.active_sessions) * 1024  # 1KB per session
            turn_size = sum(len(session.turns) for session in self.active_sessions.values()) * 512  # 512B per turn
            preference_size = len(self.user_preferences) * 256  # 256B per preference
            
            total_bytes = session_size + turn_size + preference_size
            return round(total_bytes / (1024 * 1024), 2)  # Convert to MB
            
        except Exception as e:
            self.logger.error(f"Error estimating memory usage: {e}")
            return 0.0 