"""
Advanced AI-Driven Market Analysis Pipeline

This module implements a sophisticated, modular pipeline for processing user queries 
related to market analysis. The pipeline is designed to:
- Analyze user intent through AI-powered query interpretation
- Collect and process market data dynamically
- Generate intelligent, context-aware responses
- Format responses for Discord communication

Key Features:
- Flexible processing routes (knowledge-based, data-driven, hybrid)
- Robust error handling and fallback mechanisms
- Advanced market data collection and analysis
- Configurable response generation strategies

Architecture:
The pipeline consists of four main sections:
1. Query Analysis: Interprets user intent and extracts key symbols
2. Data Collection: Gathers market data based on analysis
3. Response Generation: Creates intelligent responses
4. Response Formatting: Prepares output for Discord

Dependencies:
- Python 3.8+
- Async programming support
- Market data providers (primary and fallback)
"""

import logging
import asyncio
import time
from typing import Dict, Any, List, Optional, Union, TypedDict, Literal, Protocol, ClassVar
from datetime import datetime
from enum import Enum, auto
from dataclasses import dataclass, field

from .pipeline_sections import PipelineSection, SectionResult
from .symbol_validator import SymbolValidator
from .query_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SymbolContext
from .response_templates import ResponseT<PERSON><PERSON><PERSON><PERSON><PERSON>, ResponseDepth, ResponseStyle
from ..config import config
from src.core.logger import get_pipeline_logger
from src.core.exceptions import PipelineError
from ....utils.circuit_breaker import CircuitBreaker, CircuitBreakerConfig
from ....utils.metrics import metrics_collector

# Import fallback market data service
try:
    from src.api.data.market_data_service import MarketDataService
except ImportError:
    # Fallback implementation if the main service is not available
    class FallbackMarketDataService:
        def __init__(self):
            self.cache = {}
            self.logger = logging.getLogger(__name__)
            
        async def get_current_price(self, symbol: str) -> Dict[str, Any]:
            """
            Get current price for a stock symbol using yfinance as fallback
            
            Args:
                symbol: Stock symbol to fetch data for
                
            Returns:
                Dictionary containing stock data or error information
            """
            try:
                import yfinance as yf
                from datetime import datetime, timedelta
                
                self.logger.info(f"Fetching current price for {symbol} using yfinance fallback")
                ticker = yf.Ticker(symbol)
                
                # Get current price and other info
                hist = ticker.history(period="1d")
                if hist.empty:
                    raise ValueError(f"No data found for symbol {symbol}")
                
                # Get additional info
                info = ticker.info
                
                # Get yesterday's close for change calculation
                hist_2d = ticker.history(period="2d")
                prev_close = hist_2d["Close"].iloc[0] if len(hist_2d) > 1 else None
                current_price = hist["Close"].iloc[-1]
                
                # Calculate change if we have previous close
                if prev_close is not None:
                    change = current_price - prev_close
                    change_percent = (change / prev_close) * 100
                else:
                    change = change_percent = None
                
                # Format the response to match expected structure with standardized schema
                return {
                    "status": "success",
                    "symbol": symbol,
                    "current_price": current_price,
                    "change": change,
                    "change_percent": change_percent,
                    "volume": hist["Volume"].iloc[-1],
                    "previous_close": prev_close,
                    "high": hist["High"].iloc[-1],
                    "low": hist["Low"].iloc[-1],
                    "open": hist["Open"].iloc[-1],
                    "market_cap": info.get("marketCap", 0),
                    "name": info.get("shortName", symbol),
                    "currency": info.get("currency", "USD"),
                    "provider": "yfinance"
                }
                
            except Exception as e:
                self.logger.error(f"Error fetching current price for {symbol}: {e}")
                # Return a minimal response with error information and standardized schema
                return {
                    "status": "error",
                    "symbol": symbol,
                    "error": str(e),
                    "current_price": None,
                    "change": None,
                    "change_percent": None,
                    "volume": None,
                    "provider": "fallback"
                }
        
        async def get_historical_data(self, symbol: str, start_date=None, end_date=None, days: int = 30) -> List[Dict[str, Any]]:
            """
            Get historical data for a stock symbol using yfinance as fallback
            
            Args:
                symbol: Stock symbol to fetch data for
                start_date: Start date for historical data
                end_date: End date for historical data
                days: Number of days of historical data to retrieve
                
            Returns:
                List of dictionaries containing historical data
            """
            try:
                import yfinance as yf
                from datetime import datetime, timedelta
                
                self.logger.info(f"Fetching historical data for {symbol} using yfinance fallback")
                ticker = yf.Ticker(symbol)
                
                # If days is provided, calculate start and end dates
                if days is not None:
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=days)
                
                # Fetch historical data
                hist = ticker.history(start=start_date, end=end_date, interval="1d")
                
                if hist.empty:
                    raise ValueError(f"No historical data found for symbol {symbol}")
                
                # Convert to list of dicts with consistent format
                historical_data = []
                for idx, row in hist.iterrows():
                    historical_data.append({
                        "date": idx.to_pydatetime(),
                        "open": row["Open"],
                        "high": row["High"],
                        "low": row["Low"],
                        "close": row["Close"],
                        "volume": row["Volume"],
                        "symbol": symbol
                    })
                
                return historical_data
                
            except Exception as e:
                self.logger.error(f"Error fetching historical data for {symbol}: {e}")
                return []
        
        # Alias for backward compatibility
        get_comprehensive_stock_data = get_current_price

    EnhancedMarketDataService = FallbackMarketDataService

logger = get_pipeline_logger("ask_sections")

@dataclass
class MarketDataConfig:
    """Configuration for market data collection and analysis."""
    primary_provider: str = "MarketDataService"
    fallback_provider: str = "yfinance"
    max_symbols_to_analyze: int = 5
    confidence_threshold: float = 0.6
    data_collection_timeout: float = 15.0

@dataclass
class ResponseGenerationConfig:
    """Configuration for response generation strategies."""
    default_route: Literal["knowledge_based", "data_driven", "hybrid"] = "hybrid"
    max_response_length: int = 1900  # Just under Discord's 2000 character limit
    include_technical_analysis: bool = True
    include_sentiment_analysis: bool = True

class SymbolAnalyzer(Protocol):
    """Protocol for symbol validation and analysis."""
    async def validate_symbol(self, symbol: str) -> bool: ...
    async def get_symbol_context(self, symbol: str) -> Dict[str, Any]: ...

class AnalysisResult(TypedDict):
    """Type definition for analysis results"""
    intent: str
    confidence: float
    symbols: List[SymbolContext]
    processing_route: str
    requires_market_data: bool
    requires_ai_reasoning: bool
    data_requirements: List[str]
    context_clues: List[str]
    response_style: str
    urgency: str
    complexity: str

# Global configuration instances
MARKET_DATA_CONFIG = MarketDataConfig()
RESPONSE_CONFIG = ResponseGenerationConfig()

class AskPipelineSections:
    """Factory for creating ask pipeline sections"""
    
    @staticmethod
    def _create_circuit_breaker(section_name: str) -> CircuitBreaker:
        """Create a circuit breaker for a pipeline section"""
        if not config.circuit_breaker_enabled:
            return None
        
        breaker_config = CircuitBreakerConfig(
            name=section_name,
            threshold=config.circuit_breaker_threshold,
            cooldown=config.circuit_breaker_cooldown,
            timeout=config.circuit_breaker_timeout
        )
        return CircuitBreaker(breaker_config)
    
    @staticmethod
    def create_query_analysis_section() -> PipelineSection:
        """Create the query analysis section"""
        
        async def processor(context: Any, results: Dict[str, SectionResult]) -> Dict[str, Any]:
            """Process query analysis using AI-driven approach"""
            start_time = time.time()
            section_name = "query_analysis"
            
            # Check circuit breaker
            breaker = AskPipelineSections._create_circuit_breaker(section_name)
            if breaker and not breaker.can_execute():
                metrics_collector.record_section_failure(section_name, "circuit_breaker_open")
                breaker.on_rejection()
                raise PipelineError(section_name, f"Circuit breaker is open (failures={breaker._failure_count})")
            
            try:
                logger.info("Starting query analysis section")
                query = context.original_query
                
                # Initialize analyzer with symbol validator
                analyzer = AIQueryAnalyzer()
                if hasattr(context, 'symbol_validator'):
                    analyzer.symbol_validator = context.symbol_validator  # type: ignore
                else:
                    analyzer.symbol_validator = SymbolValidator()  # type: ignore
                
                # Get AI-driven analysis
                analysis = await analyzer.analyze_query(query, context)
                
                # Log what the AI decided
                logger.info("AI Query Analysis Results:")
                logger.info(f"  Intent: {analysis.intent.value}")
                logger.info(f"  Confidence: {analysis.confidence}")
                logger.info(f"  Processing Route: {analysis.processing_route.value}")
                logger.info(f"  Symbols to analyze: {[s.text for s in analysis.symbols if s.should_analyze]}")
                logger.info(f"  Requires market data: {analysis.requires_market_data}")
                logger.info(f"  Requires AI reasoning: {analysis.requires_ai_reasoning}")
                
                logger.info("Query analysis section completed successfully")
                
                # Record success metrics
                execution_time = time.time() - start_time
                metrics_collector.record_section_success(section_name)
                metrics_collector.record_section_latency(section_name, execution_time)
                
                # Update circuit breaker on success
                if breaker:
                    breaker.on_success()
                    metrics_collector.set_circuit_breaker_state(section_name, breaker.state.value)
                
                return {
                    "query_analysis": analysis,
                    "intent": analysis.intent.value,
                    "symbols": [s.text for s in analysis.symbols if s.should_analyze],
                    "symbol_contexts": analysis.symbols,  # Full context for AI decisions
                    "processing_route": analysis.processing_route.value,
                    "confidence": analysis.confidence,
                    "data_requirements": analysis.data_requirements,
                    "context_clues": analysis.context_clues,
                    "response_style": analysis.response_style,
                    "urgency": analysis.urgency,
                    "complexity": analysis.complexity
                }
                
            except Exception as e:
                # Record failure metrics
                execution_time = time.time() - start_time
                error_type = type(e).__name__
                metrics_collector.record_section_failure(section_name, error_type)
                metrics_collector.record_section_latency(section_name, execution_time)
                
                # Update circuit breaker on failure
                if breaker:
                    breaker.on_failure(str(e))
                    metrics_collector.set_circuit_breaker_state(section_name, breaker.state.value)
                
                logger.error(f"Query analysis section failed: {e}")
                raise PipelineError(section_name, f"Processing failed: {str(e)}")
        
        async def quality_checker(output_data: Dict[str, Any], context: Any) -> Dict[str, Any]:
            """Check quality of query analysis"""
            logger.debug("Running quality check for query analysis section")
            quality_score = 0.0
            issues = []
            
            # Check if we have basic analysis
            if "query_analysis" in output_data:
                analysis = output_data["query_analysis"]
                
                # High confidence analysis gets high quality score
                if analysis.confidence > 0.8:
                    quality_score = 0.95
                elif analysis.confidence > 0.6:
                    quality_score = 0.8
                elif analysis.confidence > 0.4:
                    quality_score = 0.6
                else:
                    quality_score = 0.4
                    issues.append("Low confidence in query analysis")
                
                # Check if AI made decisions about symbols
                if analysis.symbols:
                    analyzed_symbols = [s for s in analysis.symbols if s.should_analyze]
                    if analyzed_symbols:
                        quality_score += 0.1
                    else:
                        issues.append("No symbols selected for analysis")
                
                # Check processing route selection
                if analysis.processing_route:
                    quality_score += 0.05
                else:
                    issues.append("No processing route selected")
            
            else:
                quality_score = 0.3
                issues.append("Missing query analysis")
            
            # Cap quality score
            quality_score = min(quality_score, 1.0)
            
            return {
                "quality_score": quality_score,
                "issues": issues,
                "status": "completed" if quality_score > 0.5 else "failed"
            }
        
        return PipelineSection(
            name="query_analysis",
            section_id="query_analysis",
            description="Analyzes user query to determine intent and extract symbols",
            processor=processor,
            quality_checker=quality_checker,
            timeout=config.get_section_config("query_analysis")["timeout"],
            max_retries=config.get_section_config("query_analysis")["max_retries"]
        )
    
    @staticmethod
    def create_data_collection_section() -> PipelineSection:
        """Create the data collection section"""
        
        async def processor(context: Any, results: Dict[str, SectionResult]) -> Dict[str, Any]:
            """Collect data based on AI decisions"""
            start_time = time.time()
            section_name = "data_collection"
            
            # Check circuit breaker
            breaker = AskPipelineSections._create_circuit_breaker(section_name)
            if breaker and not breaker.can_execute():
                metrics_collector.record_section_failure(section_name, "circuit_breaker_open")
                breaker.on_rejection()
                raise PipelineError(section_name, f"Circuit breaker is open (failures={breaker._failure_count})")
            
            try:
                logger.info("Starting data collection section")
                
                # Get analysis results
                analysis_result = results.get("query_analysis")
                if not analysis_result:
                    logger.error("Missing query analysis results")
                    raise PipelineError("query_analysis", "Missing analysis results")
                
                analysis = analysis_result.output_data.get("query_analysis")
                if not analysis:
                    logger.error("Missing analysis object in results")
                    raise PipelineError("query_analysis", "Missing analysis object")
                
                # AI decides what data to collect
                collected_data = {}
                
                # Only collect market data if AI decided it's needed
                if analysis.requires_market_data and analysis.symbols:
                    # Extract the text from SymbolContext objects that should be analyzed
                    symbols_to_analyze = [s.text for s in analysis.symbols if s.should_analyze]
                    
                    if symbols_to_analyze:
                        logger.info(f"AI decided to collect market data for symbols: {symbols_to_analyze}")
                        
                        # Collect data for each symbol the AI wants to analyze
                        for symbol in symbols_to_analyze:
                            try:
                                # Import and use the market data service with fallback
                                try:
                                    from src.api.data.market_data_service import MarketDataService
                                    market_service = MarketDataService()
                                except ImportError:
                                    # Fallback to yfinance if main service not available
                                    market_service = FallbackMarketDataService()
                                
                                # Use get_comprehensive_stock_data for consistent dictionary format
                                stock_data = await market_service.get_comprehensive_stock_data(symbol)
                                
                                if stock_data and stock_data.get('status') == 'success':
                                    # Extract comprehensive data
                                    current_price = stock_data.get('current_price', 0)
                                    change_percent = stock_data.get('change_percent', 0)
                                    change_amount = stock_data.get('change', 0)
                                    volume = stock_data.get('volume', 0)
                                    market_cap = stock_data.get('market_cap', 0)
                                    
                                    # Generate technical indicators from available data
                                    technical_indicators = {}
                                    if current_price and change_percent is not None:
                                        # Simple momentum indicator
                                        if abs(change_percent) > 2:
                                            technical_indicators['momentum'] = 'strong'
                                        elif abs(change_percent) > 0.5:
                                            technical_indicators['momentum'] = 'moderate'
                                        else:
                                            technical_indicators['momentum'] = 'weak'
                                        
                                        # Trend indicator
                                        if change_percent > 0.5:
                                            technical_indicators['trend'] = 'bullish'
                                        elif change_percent < -0.5:
                                            technical_indicators['trend'] = 'bearish'
                                        else:
                                            technical_indicators['trend'] = 'sideways'
                                    
                                    # Generate sentiment analysis
                                    sentiment = {}
                                    if change_percent is not None:
                                        if change_percent > 2:
                                            sentiment['overall_sentiment'] = 0.7
                                            sentiment['sentiment_label'] = 'Bullish'
                                        elif change_percent > 0.5:
                                            sentiment['overall_sentiment'] = 0.3
                                            sentiment['sentiment_label'] = 'Slightly Bullish'
                                        elif change_percent < -2:
                                            sentiment['overall_sentiment'] = -0.7
                                            sentiment['sentiment_label'] = 'Bearish'
                                        elif change_percent < -0.5:
                                            sentiment['overall_sentiment'] = -0.3
                                            sentiment['sentiment_label'] = 'Slightly Bearish'
                                        else:
                                            sentiment['overall_sentiment'] = 0.0
                                            sentiment['sentiment_label'] = 'Neutral'
                                    
                                    # Generate trading recommendation
                                    recommendation = "HOLD"
                                    if change_percent > 2 and abs(change_percent) < 10:  # Strong but not extreme
                                        recommendation = "BUY - Strong upward momentum"
                                    elif change_percent < -2 and abs(change_percent) < 10:
                                        recommendation = "SELL - Strong downward pressure"
                                    elif change_percent > 0.5:
                                        recommendation = "BUY - Positive trend"
                                    elif change_percent < -0.5:
                                        recommendation = "SELL - Negative trend"
                                    else:
                                        recommendation = "HOLD - Sideways movement"
                                    
                                    collected_data[symbol] = {
                                        "symbol": symbol,
                                        "status": "collected",
                                        "timestamp": datetime.now().isoformat(),
                                        "data_quality": "high" if analysis.confidence > 0.7 else "medium",
                                        "current_price": current_price,
                                        "price": current_price,
                                        "change": change_percent,
                                        "change_percent": change_percent,
                                        "volume": volume,
                                        "market_cap": market_cap,
                                        "technical_indicators": technical_indicators,
                                        "sentiment": sentiment,
                                        "recommendation": recommendation
                                    }
                                else:
                                    # Fallback to basic data if full data unavailable
                                    collected_data[symbol] = {
                                        "symbol": symbol,
                                        "status": "collected",
                                        "timestamp": datetime.now().isoformat(),
                                        "data_quality": "medium",
                                        "note": "Limited data available"
                                    }
                                    
                            except Exception as e:
                                logger.error(f"Error collecting data for {symbol}: {e}")
                                collected_data[symbol] = {
                                    "symbol": symbol,
                                    "status": "error",
                                    "error": str(e)
                                }
                    else:
                        logger.info("AI decided no symbols need market data collection")
                else:
                    logger.info("AI decided market data collection not needed")
                
                # Always include analysis context for AI reasoning
                collected_data["analysis_context"] = {
                    "intent": analysis.intent.value,
                    "processing_route": analysis.processing_route.value,
                    "data_requirements": analysis.data_requirements,
                    "context_clues": analysis.context_clues,
                    "response_style": analysis.response_style,
                    "urgency": analysis.urgency,
                    "complexity": analysis.complexity
                }
                
                logger.info(f"Data collection completed for {len([k for k in collected_data.keys() if k != 'analysis_context'])} symbols")
                
                # Record success metrics
                execution_time = time.time() - start_time
                metrics_collector.record_section_success(section_name)
                metrics_collector.record_section_latency(section_name, execution_time)
                
                # Update circuit breaker on success
                if breaker:
                    breaker.on_success()
                    metrics_collector.set_circuit_breaker_state(section_name, breaker.state.value)
                
                return collected_data
                
            except Exception as e:
                # Record failure metrics
                execution_time = time.time() - start_time
                error_type = type(e).__name__
                metrics_collector.record_section_failure(section_name, error_type)
                metrics_collector.record_section_latency(section_name, execution_time)
                
                # Update circuit breaker on failure
                if breaker:
                    breaker.on_failure(str(e))
                    metrics_collector.set_circuit_breaker_state(section_name, breaker.state.value)
                
                logger.error(f"Data collection section failed: {e}")
                raise PipelineError(section_name, f"Processing failed: {str(e)}")
        
        async def quality_checker(output_data: Dict[str, Any], context: Any) -> Dict[str, Any]:
            """Check quality of collected data"""
            logger.debug("Running quality check for data collection section")
            quality_score = 0.0
            issues = []
            
            # Check if we have any data
            if "analysis_context" in output_data:
                quality_score += 0.3
            
            # Check symbol data quality
            symbol_data = {k: v for k, v in output_data.items() if k != "analysis_context"}
            if symbol_data:
                successful_collections = sum(1 for data in symbol_data.values() if data.get("status") == "collected")
                total_symbols = len(symbol_data)
                
                if total_symbols > 0:
                    success_rate = successful_collections / total_symbols
                    quality_score += success_rate * 0.7
                    
                    if success_rate < 0.5:
                        issues.append(f"Low data collection success rate: {success_rate:.1%}")
                else:
                    quality_score += 0.7  # No symbols to collect, but context is good
            
            # Cap quality score
            quality_score = min(quality_score, 1.0)
            
            return {
                "quality_score": quality_score,
                "issues": issues,
                "status": "completed" if quality_score > 0.3 else "failed"
            }
        
        return PipelineSection(
            name="data_collection",
            section_id="data_collection",
            description="Collects market data based on AI analysis",
            processor=processor,
            quality_checker=quality_checker,
            timeout=config.get_section_config("data_collection")["timeout"],
            max_retries=config.get_section_config("data_collection")["max_retries"],
            dependencies=["query_analysis"]
        )
    
    @staticmethod
    def create_response_generation_section() -> PipelineSection:
        """Create the response generation section"""
        
        async def processor(context: Any, results: Dict[str, SectionResult]) -> Dict[str, Any]:
            """Generate AI response based on collected data and context"""
            logger.info("Starting response generation section")
            
            # Get collected data
            data_result = results.get("data_collection")
            if not data_result:
                logger.error("No data collection results available")
                return {"error": "Missing data collection results"}
            
            collected_data = data_result.output_data
            
            # Get analysis context
            analysis_context = collected_data.get("analysis_context", {})
            
            # AI decides how to generate response based on context
            if analysis_context.get("processing_route") == "knowledge_based":
                # Knowledge-based route - focus on AI reasoning
                response = await _generate_knowledge_response(analysis_context, collected_data)
            elif analysis_context.get("processing_route") == "data_driven":
                # Data-driven route - focus on data analysis
                response = await _generate_data_response(analysis_context, collected_data)
            else:
                # Hybrid route - combine both approaches
                response = await _generate_hybrid_response(analysis_context, collected_data)
            
            logger.info(f"Response generation completed with type: {analysis_context.get('processing_route', 'hybrid')}")
            return {
                "response": response,
                "response_type": analysis_context.get("processing_route", "hybrid"),
                "generated_at": datetime.now().isoformat()
            }
        
        async def quality_checker(output_data: Dict[str, Any], context: Any):
            """Check quality of generated response"""
            logger.debug("Running quality check for response generation section")
            quality_score = 0.0
            issues = []
            
            if "response" in output_data:
                response = output_data["response"]
                if response and len(response) > 10:
                    quality_score = 0.8
                    if len(response) > 100:
                        quality_score = 0.9
                else:
                    quality_score = 0.3
                    issues.append("Response too short or empty")
            else:
                quality_score = 0.2
                issues.append("Missing AI response")
            
            return {
                "quality_score": quality_score,
                "issues": issues,
                "status": "completed" if quality_score > 0.5 else "failed"
            }
        
        return PipelineSection(
            name="response_generation",
            section_id="response_generation",
            description="Generates AI response based on collected data",
            processor=processor,
            quality_checker=quality_checker,
            timeout=config.get_section_config("response_generation")["timeout"],
            max_retries=config.get_section_config("response_generation")["max_retries"],
            dependencies=["data_collection"]
        )
    
    @staticmethod
    def create_response_formatting_section() -> PipelineSection:
        """Create the response formatting section"""
        
        async def processor(context: Any, results: Dict[str, SectionResult]) -> Dict[str, Any]:
            """Format the AI response for Discord"""
            logger.info("Starting response formatting section")
            
            # Get AI response
            response_result = results.get("response_generation")
            if not response_result:
                logger.error("No response generation results available")
                return {"error": "Missing response generation results"}
            
            ai_response = response_result.output_data.get("response", "")
            response_type = response_result.output_data.get("response_type", "hybrid")
            
            # Get analysis context for formatting decisions
            data_result = results.get("data_collection")
            analysis_context = {}
            if data_result:
                analysis_context = data_result.output_data.get("analysis_context", {})
            
            # AI decides formatting based on context
            formatted_response = await _format_response_for_discord(
                ai_response, 
                response_type, 
                analysis_context
            )
            
            logger.info(f"Response formatting completed with type: {response_type}")
            return {
                "formatted_response": formatted_response,
                "format_type": response_type,
                "formatted_at": datetime.now().isoformat()
            }
        
        async def quality_checker(output_data: Dict[str, Any], context: Any) -> Dict[str, Any]:
            """Check quality of formatted response"""
            logger.debug("Running quality check for response formatting section")
            quality_score = 0.0
            issues = []
            
            if "formatted_response" in output_data:
                response = output_data["formatted_response"]
                if response and len(response) > 10:
                    quality_score = 0.9
                    if len(response) < 2000:  # Discord limit
                        quality_score = 1.0
                    else:
                        quality_score = 0.8
                        issues.append("Response may exceed Discord limits")
                else:
                    quality_score = 0.3
                    issues.append("Formatted response too short or empty")
            else:
                quality_score = 0.2
                issues.append("Missing formatted response")
            
            return {
                "quality_score": quality_score,
                "issues": issues,
                "status": "completed" if quality_score > 0.5 else "failed"
            }
        
        return PipelineSection(
            name="response_formatting",
            section_id="response_formatting",
            description="Formats AI response for Discord display",
            processor=processor,
            quality_checker=quality_checker,
            timeout=config.get_section_config("response_formatting")["timeout"],
            max_retries=config.get_section_config("response_formatting")["max_retries"],
            dependencies=["response_generation"]
        )

# Helper functions for response generation
async def _generate_knowledge_response(analysis_context: Dict[str, Any], collected_data: Dict[str, Any]) -> str:
    """Generate knowledge-based response using AI reasoning"""
    
    intent = analysis_context.get("intent", "general_question")
    context_clues = analysis_context.get("context_clues", [])
    
    # AI decides response content based on context
    if intent == "portfolio_advice":
        return "Based on your question, here's my portfolio advice: Consider diversifying across different sectors and asset classes. Always assess your risk tolerance and investment timeline before making decisions."
    elif intent == "risk_assessment":
        return "For risk assessment, consider these factors: Market volatility, company fundamentals, sector trends, and your personal risk tolerance. Diversification is key to managing risk."
    else:
        return "Here's what I can tell you about that: I've analyzed your query and can provide insights based on available market data and analysis. What specific aspect would you like me to focus on?"

async def _generate_data_response(analysis_context: Dict[str, Any], collected_data: Dict[str, Any]) -> str:
    """Generate data-driven response using collected market data"""
    
    # Extract symbol data
    symbol_data = {k: v for k, v in collected_data.items() if k != "analysis_context"}
    
    if not symbol_data:
        return "I couldn't collect market data for analysis. Please try again later or check if the symbol is valid."
    
    # AI analyzes the collected data
    response_parts = []
    for symbol, data in symbol_data.items():
        if data.get("status") == "collected":
            # Generate actual analysis based on available data
            analysis = _analyze_stock_data(symbol, data)
            response_parts.append(analysis)
        else:
            response_parts.append(f"❌ **{symbol}**: {data.get('error', 'Data collection failed')}")
    
    return "\n\n".join(response_parts)

def _analyze_stock_data(symbol: str, data: Dict[str, Any]) -> str:
    """Generate actual stock analysis from collected data"""
    
    # Extract key metrics
    price = data.get("current_price", data.get("price"))
    change = data.get("change", data.get("change_percent"))
    volume = data.get("volume")
    market_cap = data.get("market_cap")
    
    # Build analysis with ASCII text instead of emojis
    analysis_parts = [f"**{symbol} Analysis**"]
    
    if price:
        analysis_parts.append(f"**Current Price:** ${price:.2f}")
    
    if change is not None:
        change_sign = "+" if change >= 0 else ""
        analysis_parts.append(f"**Change:** {change_sign}{change:.2f}%")
    
    if volume:
        analysis_parts.append(f"**Volume:** {volume:,.0f}")
    
    if market_cap:
        analysis_parts.append(f"**Market Cap:** ${market_cap:,.0f}")
    
    # Enhanced technical analysis
    technical_indicators = data.get("technical_indicators", {})
    if technical_indicators and isinstance(technical_indicators, dict):
        tech_parts = []
        for indicator, value in technical_indicators.items():
            if value is not None and value != 0:
                if isinstance(value, float):
                    tech_parts.append(f"{indicator}: {value:.2f}")
                else:
                    tech_parts.append(f"{indicator}: {value}")
        
        if tech_parts:
            analysis_parts.append(f"**Technical Indicators:** {', '.join(tech_parts)}")
        else:
            analysis_parts.append("**Technical Indicators:** Basic data available")
    else:
        # Generate basic technical insights from price data
        if price and change is not None:
            if change > 2:
                analysis_parts.append("**Technical:** Strong upward momentum")
            elif change > 0.5:
                analysis_parts.append("**Technical:** Slight upward trend")
            elif change < -2:
                analysis_parts.append("**Technical:** Strong downward pressure")
            elif change < -0.5:
                analysis_parts.append("**Technical:** Slight downward trend")
            else:
                analysis_parts.append("**Technical:** Sideways consolidation")
        else:
            analysis_parts.append("**Technical:** Basic price data available")
    
    # Enhanced sentiment analysis
    sentiment = data.get("sentiment")
    if sentiment and isinstance(sentiment, dict):
        sentiment_label = sentiment.get("sentiment_label", sentiment.get("overall_sentiment"))
        if sentiment_label:
            analysis_parts.append(f"**Sentiment:** {sentiment_label}")
        else:
            # Generate sentiment from price movement
            if change is not None:
                if change > 1:
                    analysis_parts.append("**Sentiment:** Bullish momentum")
                elif change < -1:
                    analysis_parts.append("**Sentiment:** Bearish pressure")
                else:
                    analysis_parts.append("**Sentiment:** Neutral")
            else:
                analysis_parts.append("**Sentiment:** Market data available")
    else:
        # Generate sentiment from price movement
        if change is not None:
            if change > 1:
                analysis_parts.append("**Sentiment:** Bullish momentum")
            elif change < -1:
                analysis_parts.append("**Sentiment:** Bearish pressure")
            else:
                analysis_parts.append("**Sentiment:** Neutral")
        else:
            analysis_parts.append("**Sentiment:** Market data available")
    
    # Enhanced recommendation generation
    recommendation = data.get("recommendation")
    if recommendation:
        analysis_parts.append(f"**Recommendation:** {recommendation}")
    else:
        # Generate recommendation based on available data
        if change is not None and price:
            if change > 2:
                analysis_parts.append("**Recommendation:** BUY - Strong upward momentum")
            elif change > 0.5:
                analysis_parts.append("**Recommendation:** BUY - Positive trend")
            elif change < -2:
                analysis_parts.append("**Recommendation:** SELL - Strong downward pressure")
            elif change < -0.5:
                analysis_parts.append("**Recommendation:** SELL - Negative trend")
            else:
                analysis_parts.append("**Recommendation:** HOLD - Sideways movement")
        else:
            analysis_parts.append("**Recommendation:** HOLD - Insufficient data")
    
    # Add data quality note
    data_quality = data.get("data_quality", "unknown")
    analysis_parts.append(f"*Data Quality: {data_quality}*")
    
    # Add timestamp if available
    timestamp = data.get("timestamp")
    if timestamp:
        try:
            from datetime import datetime
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            formatted_time = dt.strftime("%H:%M:%S")
            analysis_parts.append(f"*Updated: {formatted_time}*")
        except:
            pass
    
    return "\n".join(analysis_parts)

async def _generate_hybrid_response(analysis_context: Dict[str, Any], collected_data: Dict[str, Any]) -> str:
    """Generate hybrid response combining data and knowledge"""
    
    # AI decides how to balance data and knowledge
    intent = analysis_context.get("intent", "general_question")
    
    if intent == "comparison":
        return "Comparing the data I collected: I'll analyze the performance metrics, technical indicators, and fundamental factors to provide you with a comprehensive comparison."
    elif intent == "market_overview":
        return "Market overview combining data and insights: I'll give you a complete picture including current market conditions, trends, and actionable insights."
    else:
        return "Here's my analysis combining available data and knowledge: I've gathered market data and will provide you with both technical analysis and strategic insights to help inform your investment decisions."

async def _format_response_for_discord(ai_response: str, response_type: str, analysis_context: Dict[str, Any]) -> str:
    """Format response for Discord with both structured data and natural chat"""
    
    # Generate natural chat response with formatting
    chat_response = (
        "**Chat Response**\n\n"
        f"{ai_response}\n\n"
        "━━━━━━━━━━━━━━━━━━━━\n"
    )
    
    # Generate detailed structured data section
    structured_data = "**Analysis Context**\n"
    if analysis_context:
        structured_data += f"• **Intent**: {analysis_context.get('intent', 'N/A')}\n"
        structured_data += f"• **Confidence**: {analysis_context.get('confidence', 0)*100:.1f}%\n"
        if 'symbols' in analysis_context:
            symbols = ', '.join([
                f"{s.text} ({s.confidence*100:.0f}%)" 
                for s in analysis_context['symbols']
            ])
            structured_data += f"• **Symbols**: {symbols or 'None'}\n"
        if 'processing_route' in analysis_context:
            structured_data += f"• **Processing**: {analysis_context['processing_route']}\n"
    
    return f"{chat_response}{structured_data}"