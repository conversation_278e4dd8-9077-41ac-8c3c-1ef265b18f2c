"""
Advanced Query Classification Module

This module provides advanced query classification capabilities including:
- Deep semantic analysis
- Domain-specific classification
- Query complexity scoring
- Intent disambiguation
"""

import logging
import re
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class QueryDomain(Enum):
    """Domain classification for queries"""
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    MARKET_OUTLOOK = "market_outlook"
    TRADING_STRATEGY = "trading_strategy"
    RISK_MANAGEMENT = "risk_management"
    EDUCATION = "education"
    PRICE_QUERY = "price_query"
    COMPARISON = "comparison"
    PORTFOLIO_MANAGEMENT = "portfolio_management"
    OPTIONS_TRADING = "options_trading"
    CRYPTO_ANALYSIS = "crypto_analysis"
    GENERAL = "general"

class QueryComplexity(Enum):
    """Complexity levels for queries"""
    SIMPLE = 1
    MODERATE = 2
    COMPLEX = 3
    ADVANCED = 4
    EXPERT = 5

@dataclass
class QueryClassification:
    """Advanced query classification result"""
    primary_domain: QueryDomain
    secondary_domains: List[QueryDomain]
    confidence: float
    subcategories: List[str]
    complexity: QueryComplexity
    sentiment: Dict[str, Any]
    requires_context: bool
    processing_recommendations: List[str]

class AdvancedQueryClassifier:
    """Advanced query classification system"""
    
    def __init__(self):
        self._initialize_classification_patterns()
        self._initialize_complexity_scoring()
    
    def _initialize_classification_patterns(self):
        """Initialize domain classification patterns"""
        self.domain_patterns = {
            QueryDomain.TECHNICAL_ANALYSIS: {
                "keywords": [
                    "rsi", "macd", "moving average", "bollinger", "stochastic", "atr", 
                    "ema", "sma", "fibonacci", "support", "resistance", "trendline",
                    "candlestick", "volume", "momentum", "oscillator", "chart pattern"
                ],
                "phrases": [
                    "technical indicator", "chart analysis", "price pattern", 
                    "momentum analysis", "volume analysis"
                ]
            },
            QueryDomain.FUNDAMENTAL_ANALYSIS: {
                "keywords": [
                    "earnings", "revenue", "profit", "pe ratio", "dividend", "eps",
                    "book value", "debt", "cash flow", "balance sheet", "income statement",
                    "fundamental", "valuation", "growth rate", "margin"
                ],
                "phrases": [
                    "financial analysis", "company fundamentals", "valuation analysis"
                ]
            },
            QueryDomain.MARKET_OUTLOOK: {
                "keywords": [
                    "market", "outlook", "sentiment", "trend", "forecast", "prediction",
                    "bull market", "bear market", "sector", "economy", "fed", "interest rate"
                ],
                "phrases": [
                    "market analysis", "economic outlook", "sector rotation"
                ]
            },
            QueryDomain.TRADING_STRATEGY: {
                "keywords": [
                    "strategy", "play", "entry", "exit", "position", "setup", "pattern",
                    "breakout", "pullback", "reversal", "momentum play"
                ],
                "phrases": [
                    "trading strategy", "entry strategy", "exit strategy"
                ]
            },
            QueryDomain.RISK_MANAGEMENT: {
                "keywords": [
                    "risk", "stop loss", "position size", "volatility", "drawdown",
                    "risk reward", "portfolio risk", "hedging", "diversification"
                ],
                "phrases": [
                    "risk management", "position sizing", "stop loss strategy"
                ]
            },
            QueryDomain.EDUCATION: {
                "keywords": [
                    "explain", "what is", "how to", "learn", "tutorial", "definition",
                    "meaning", "concept", "principle", "strategy"
                ],
                "phrases": [
                    "explain how", "what does", "how does", "learning about"
                ]
            },
            QueryDomain.PRICE_QUERY: {
                "keywords": [
                    "price", "current", "quote", "how much", "cost", "worth", "value"
                ],
                "phrases": [
                    "current price", "what is the price", "how much is"
                ]
            },
            QueryDomain.COMPARISON: {
                "keywords": [
                    "vs", "compare", "versus", "better", "difference", "preferred"
                ],
                "phrases": [
                    "compare with", "versus", "which is better"
                ]
            },
            QueryDomain.PORTFOLIO_MANAGEMENT: {
                "keywords": [
                    "portfolio", "allocation", "diversification", "weighting", "rebalance",
                    "asset allocation", "portfolio performance"
                ],
                "phrases": [
                    "portfolio management", "asset allocation", "portfolio rebalancing"
                ]
            },
            QueryDomain.OPTIONS_TRADING: {
                "keywords": [
                    "option", "call", "put", "strike", "premium", "expiration", "iv",
                    "greeks", "delta", "gamma", "theta", "vega", "options chain"
                ],
                "phrases": [
                    "options strategy", "option chain", "greeks analysis"
                ]
            },
            QueryDomain.CRYPTO_ANALYSIS: {
                "keywords": [
                    "bitcoin", "ethereum", "btc", "eth", "crypto", "blockchain",
                    "coin", "token", "defi", "nft", "web3"
                ],
                "phrases": [
                    "crypto analysis", "blockchain technology", "defi protocol"
                ]
            }
        }
    
    def _initialize_complexity_scoring(self):
        """Initialize complexity scoring rules"""
        self.complexity_factors = {
            "length_penalty": 0.1,  # Points per 10 words
            "technical_terms": 0.5,  # Points per technical term
            "multiple_symbols": 1.0,  # Points for multiple symbols
            "multiple_questions": 1.5,  # Points per question mark after first
            "conditional_logic": 2.0,  # Points for if/when/what if
            "time_constraints": 1.0,  # Points for time references
            "nested_concepts": 2.0,  # Points for complex combinations
        }
    
    async def classify_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> QueryClassification:
        """
        Classify a query with advanced analysis
        
        Args:
            query: The user query to classify
            context: Optional context information
            
        Returns:
            QueryClassification object with detailed classification
        """
        # Domain classification
        domain_scores = self._score_domains(query)
        primary_domain, secondary_domains = self._determine_domains(domain_scores)
        
        # Subcategory extraction
        subcategories = self._extract_subcategories(query, primary_domain)
        
        # Complexity assessment
        complexity = self._assess_complexity(query)
        
        # Sentiment analysis
        sentiment = self._analyze_sentiment(query)
        
        # Context requirements
        requires_context = self._requires_context(query)
        
        # Processing recommendations
        recommendations = self._generate_recommendations(
            primary_domain, complexity, context
        )
        
        # Calculate confidence
        confidence = self._calculate_confidence(domain_scores, primary_domain)
        
        return QueryClassification(
            primary_domain=primary_domain,
            secondary_domains=secondary_domains,
            confidence=confidence,
            subcategories=subcategories,
            complexity=complexity,
            sentiment=sentiment,
            requires_context=requires_context,
            processing_recommendations=recommendations
        )
    
    def _score_domains(self, query: str) -> Dict[QueryDomain, float]:
        """Score query against all domains"""
        scores = {}
        query_lower = query.lower()
        
        for domain, patterns in self.domain_patterns.items():
            score = 0.0
            
            # Score keywords
            for keyword in patterns["keywords"]:
                if keyword in query_lower:
                    score += 1.0
            
            # Score phrases
            for phrase in patterns["phrases"]:
                if phrase in query_lower:
                    score += 2.0  # Phrases are worth more than individual keywords
            
            scores[domain] = score
        
        return scores
    
    def _determine_domains(self, scores: Dict[QueryDomain, float]) -> Tuple[QueryDomain, List[QueryDomain]]:
        """Determine primary and secondary domains"""
        # Sort domains by score
        sorted_domains = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # Filter out zero scores
        non_zero_domains = [domain for domain, score in sorted_domains if score > 0]
        
        if not non_zero_domains:
            return QueryDomain.GENERAL, []
        
        primary_domain = non_zero_domains[0]
        secondary_domains = non_zero_domains[1:3]  # Top 2 secondary domains
        
        return primary_domain, secondary_domains
    
    def _extract_subcategories(self, query: str, primary_domain: QueryDomain) -> List[str]:
        """Extract specific subcategories based on domain"""
        subcategories = []
        query_lower = query.lower()
        
        # Domain-specific subcategory extraction
        if primary_domain == QueryDomain.TECHNICAL_ANALYSIS:
            indicators = ["rsi", "macd", "ema", "sma", "bollinger", "stochastic", "atr"]
            subcategories = [ind for ind in indicators if ind in query_lower]
        
        elif primary_domain == QueryDomain.OPTIONS_TRADING:
            option_types = ["call", "put"]
            subcategories = [opt for opt in option_types if opt in query_lower]
        
        elif primary_domain == QueryDomain.CRYPTO_ANALYSIS:
            cryptos = ["bitcoin", "btc", "ethereum", "eth"]
            subcategories = [crypto for crypto in cryptos if crypto in query_lower]
        
        return subcategories
    
    def _assess_complexity(self, query: str) -> QueryComplexity:
        """Assess the complexity of a query"""
        score = 0.0
        query_lower = query.lower()
        
        # Length factor
        word_count = len(query.split())
        score += (word_count // 10) * self.complexity_factors["length_penalty"]
        
        # Technical terms factor
        technical_terms = []
        for patterns in self.domain_patterns.values():
            technical_terms.extend(patterns["keywords"])
        
        tech_count = sum(1 for term in technical_terms if term in query_lower)
        score += tech_count * self.complexity_factors["technical_terms"]
        
        # Multiple symbols factor
        symbols = re.findall(r'\$([A-Z]{1,5})', query)
        if len(symbols) > 1:
            score += self.complexity_factors["multiple_symbols"]
        
        # Multiple questions factor
        question_marks = query.count('?')
        if question_marks > 1:
            score += (question_marks - 1) * self.complexity_factors["multiple_questions"]
        
        # Conditional logic factor
        conditionals = ["if", "when", "what if", "assuming"]
        if any(cond in query_lower for cond in conditionals):
            score += self.complexity_factors["conditional_logic"]
        
        # Time constraints factor
        time_refs = ["today", "tomorrow", "next week", "this month", "year"]
        if any(time_ref in query_lower for time_ref in time_refs):
            score += self.complexity_factors["time_constraints"]
        
        # Map score to complexity level
        if score <= 1.0:
            return QueryComplexity.SIMPLE
        elif score <= 2.5:
            return QueryComplexity.MODERATE
        elif score <= 4.0:
            return QueryComplexity.COMPLEX
        elif score <= 6.0:
            return QueryComplexity.ADVANCED
        else:
            return QueryComplexity.EXPERT
    
    def _analyze_sentiment(self, query: str) -> Dict[str, Any]:
        """Analyze sentiment of a query"""
        query_lower = query.lower()
        
        # Sentiment keywords
        positive_words = [
            "good", "great", "excellent", "amazing", "awesome", "bullish", "positive",
            "strong", "up", "rise", "gain", "profit"
        ]
        negative_words = [
            "bad", "terrible", "awful", "horrible", "bearish", "negative",
            "weak", "down", "fall", "loss", "risk"
        ]
        neutral_words = [
            "analyze", "explain", "show", "tell", "what", "how", "why"
        ]
        
        # Count sentiment words
        positive_count = sum(1 for word in positive_words if word in query_lower)
        negative_count = sum(1 for word in negative_words if word in query_lower)
        neutral_count = sum(1 for word in neutral_words if word in query_lower)
        
        # Calculate sentiment score
        total_sentiment_words = positive_count + negative_count
        if total_sentiment_words > 0:
            sentiment_score = (positive_count - negative_count) / total_sentiment_words
        else:
            sentiment_score = 0.0
        
        # Determine sentiment label
        if sentiment_score > 0.3:
            sentiment_label = "positive"
        elif sentiment_score < -0.3:
            sentiment_label = "negative"
        else:
            sentiment_label = "neutral"
        
        return {
            "score": sentiment_score,
            "label": sentiment_label,
            "confidence": min(1.0, total_sentiment_words / 5.0),
            "positive_words": positive_count,
            "negative_words": negative_count,
            "neutral_words": neutral_count
        }
    
    def _requires_context(self, query: str) -> bool:
        """Determine if query requires contextual understanding"""
        query_lower = query.lower()
        
        # Queries that typically require context
        context_indicators = [
            "my portfolio", "my position", "i have", "i bought", "i own",
            "my strategy", "we discussed", "as we talked", "like before",
            "previous conversation", "earlier"
        ]
        
        return any(indicator in query_lower for indicator in context_indicators)
    
    def _generate_recommendations(
        self, 
        primary_domain: QueryDomain, 
        complexity: QueryComplexity,
        context: Optional[Dict[str, Any]]
    ) -> List[str]:
        """Generate processing recommendations"""
        recommendations = []
        
        # Domain-based recommendations
        if primary_domain == QueryDomain.TECHNICAL_ANALYSIS:
            recommendations.append("fetch_technical_indicators")
            recommendations.append("analyze_chart_patterns")
        elif primary_domain == QueryDomain.FUNDAMENTAL_ANALYSIS:
            recommendations.append("fetch_financial_data")
            recommendations.append("calculate_valuation_metrics")
        elif primary_domain == QueryDomain.OPTIONS_TRADING:
            recommendations.append("fetch_options_chain")
            recommendations.append("calculate_greeks")
        
        # Complexity-based recommendations
        if complexity in [QueryComplexity.COMPLEX, QueryComplexity.ADVANCED, QueryComplexity.EXPERT]:
            recommendations.append("use_advanced_ai_model")
            recommendations.append("break_down_response")
        elif complexity == QueryComplexity.SIMPLE:
            recommendations.append("use_quick_response_mode")
        
        # Context-based recommendations
        if context and context.get("requires_context", False):
            recommendations.append("load_conversation_history")
            recommendations.append("personalize_response")
        
        return recommendations
    
    def _calculate_confidence(self, scores: Dict[QueryDomain, float], primary_domain: QueryDomain) -> float:
        """Calculate classification confidence"""
        primary_score = scores.get(primary_domain, 0.0)
        total_score = sum(scores.values())
        
        if total_score == 0:
            return 0.5  # Default confidence when no clear signals
        
        # Confidence is higher when primary domain score dominates
        confidence = primary_score / total_score
        
        # Boost confidence for clear winners
        if confidence > 0.7:
            confidence = min(0.95, confidence + 0.1)
        
        return confidence
    
    def get_domain_description(self, domain: QueryDomain) -> str:
        """Get human-readable description of a domain"""
        descriptions = {
            QueryDomain.TECHNICAL_ANALYSIS: "Technical analysis and chart pattern recognition",
            QueryDomain.FUNDAMENTAL_ANALYSIS: "Company financials and valuation analysis",
            QueryDomain.MARKET_OUTLOOK: "Market trends and economic predictions",
            QueryDomain.TRADING_STRATEGY: "Trading setups and execution strategies",
            QueryDomain.RISK_MANAGEMENT: "Risk control and position sizing",
            QueryDomain.EDUCATION: "Educational content and concept explanations",
            QueryDomain.PRICE_QUERY: "Current price and quote requests",
            QueryDomain.COMPARISON: "Symbol and strategy comparisons",
            QueryDomain.PORTFOLIO_MANAGEMENT: "Portfolio construction and management",
            QueryDomain.OPTIONS_TRADING: "Options strategies and analysis",
            QueryDomain.CRYPTO_ANALYSIS: "Cryptocurrency and blockchain analysis",
            QueryDomain.GENERAL: "General trading and market questions"
        }
        return descriptions.get(domain, "Unknown domain")

# Global instance
advanced_classifier = AdvancedQueryClassifier()