"""
ML-Based Sentiment Analyzer

This module provides enhanced sentiment analysis using machine learning models
for more accurate sentiment detection in financial queries.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
import re
import json
import os
from datetime import datetime

import numpy as np

logger = logging.getLogger(__name__)

# Path to sentiment model data
SENTIMENT_MODEL_PATH = os.path.join('data', 'models', 'sentiment')

class MLSentimentAnalyzer:
    """ML-based sentiment analyzer for financial queries"""
    
    def __init__(self):
        """Initialize the sentiment analyzer"""
        self.model_loaded = False
        self.word_embeddings = {}
        self.sentiment_weights = {}
        self.financial_terms = {}
        self.load_model()
    
    def load_model(self):
        """Load sentiment model data"""
        try:
            # Create model directory if it doesn't exist
            os.makedirs(SENTIMENT_MODEL_PATH, exist_ok=True)
            
            # Path to model files
            embeddings_path = os.path.join(SENTIMENT_MODEL_PATH, 'word_embeddings.json')
            weights_path = os.path.join(SENTIMENT_MODEL_PATH, 'sentiment_weights.json')
            terms_path = os.path.join(SENTIMENT_MODEL_PATH, 'financial_terms.json')
            
            # Check if model files exist
            if not all(os.path.exists(path) for path in [embeddings_path, weights_path, terms_path]):
                logger.warning("Sentiment model files not found. Using fallback model.")
                self._initialize_fallback_model()
                return
            
            # Load model files
            with open(embeddings_path, 'r') as f:
                self.word_embeddings = json.load(f)
            
            with open(weights_path, 'r') as f:
                self.sentiment_weights = json.load(f)
            
            with open(terms_path, 'r') as f:
                self.financial_terms = json.load(f)
            
            self.model_loaded = True
            logger.info("Sentiment model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading sentiment model: {e}")
            self._initialize_fallback_model()
    
    def _initialize_fallback_model(self):
        """Initialize fallback model with basic sentiment data"""
        # Basic word embeddings (simplified 2D vectors)
        self.word_embeddings = {
            # Positive words
            "bullish": [0.8, 0.6],
            "positive": [0.9, 0.7],
            "good": [0.7, 0.5],
            "great": [0.9, 0.8],
            "excellent": [1.0, 0.9],
            "strong": [0.8, 0.7],
            "up": [0.6, 0.4],
            "gain": [0.7, 0.6],
            "profit": [0.8, 0.7],
            "buy": [0.6, 0.5],
            "opportunity": [0.7, 0.6],
            "growth": [0.7, 0.5],
            
            # Negative words
            "bearish": [-0.8, -0.6],
            "negative": [-0.9, -0.7],
            "bad": [-0.7, -0.5],
            "poor": [-0.8, -0.6],
            "weak": [-0.7, -0.5],
            "down": [-0.6, -0.4],
            "loss": [-0.8, -0.7],
            "sell": [-0.6, -0.5],
            "risk": [-0.7, -0.5],
            "decline": [-0.7, -0.6],
            "drop": [-0.7, -0.5],
            "fall": [-0.6, -0.5],
            
            # Neutral words
            "market": [0.0, 0.1],
            "stock": [0.0, 0.0],
            "price": [0.0, 0.1],
            "trade": [0.1, 0.0],
            "analysis": [0.0, 0.2],
            "indicator": [0.1, 0.1],
            "level": [0.0, 0.0],
            "volume": [0.0, 0.1]
        }
        
        # Basic sentiment weights
        self.sentiment_weights = {
            "positive_threshold": 0.3,
            "negative_threshold": -0.3,
            "dimension_weights": [0.7, 0.3]  # Weights for the 2D vectors
        }
        
        # Financial terms with sentiment modifiers
        self.financial_terms = {
            "bull market": 0.8,
            "bear market": -0.8,
            "uptrend": 0.7,
            "downtrend": -0.7,
            "breakout": 0.6,
            "breakdown": -0.6,
            "support": 0.4,
            "resistance": -0.2,
            "oversold": 0.5,
            "overbought": -0.5,
            "rally": 0.7,
            "correction": -0.6,
            "crash": -0.9,
            "boom": 0.9
        }
        
        self.model_loaded = True
        logger.info("Fallback sentiment model initialized")
    
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment of text using ML-based approach
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        # Ensure model is loaded
        if not self.model_loaded:
            self.load_model()
        
        # Preprocess text
        processed_text = self._preprocess_text(text)
        
        # Extract features
        features = self._extract_features(processed_text)
        
        # Calculate sentiment score
        sentiment_score, confidence = self._calculate_sentiment(features, processed_text)
        
        # Determine sentiment label
        if sentiment_score > self.sentiment_weights.get("positive_threshold", 0.3):
            sentiment_label = "positive"
        elif sentiment_score < self.sentiment_weights.get("negative_threshold", -0.3):
            sentiment_label = "negative"
        else:
            sentiment_label = "neutral"
        
        # Extract sentiment words
        positive_words, negative_words, neutral_words = self._extract_sentiment_words(processed_text)
        
        # Extract financial phrases
        financial_phrases = self._extract_financial_phrases(text)
        
        return {
            "score": sentiment_score,
            "label": sentiment_label,
            "confidence": confidence,
            "positive_words": positive_words,
            "negative_words": negative_words,
            "neutral_words": neutral_words,
            "financial_phrases": financial_phrases,
            "analysis_method": "ml_enhanced" if self.model_loaded else "fallback",
            "timestamp": datetime.now().isoformat()
        }
    
    def _preprocess_text(self, text: str) -> List[str]:
        """Preprocess text for sentiment analysis"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # Tokenize
        tokens = text.split()
        
        return tokens
    
    def _extract_features(self, tokens: List[str]) -> np.ndarray:
        """Extract features from tokens"""
        # Get embeddings for tokens
        embeddings = []
        for token in tokens:
            if token in self.word_embeddings:
                embeddings.append(self.word_embeddings[token])
        
        # If no embeddings found, return zero vector
        if not embeddings:
            return np.zeros(len(self.sentiment_weights.get("dimension_weights", [0.7, 0.3])))
        
        # Average embeddings
        return np.mean(embeddings, axis=0)
    
    def _calculate_sentiment(self, features: np.ndarray, tokens: List[str]) -> Tuple[float, float]:
        """Calculate sentiment score and confidence"""
        # Apply weights to features
        weights = self.sentiment_weights.get("dimension_weights", [0.7, 0.3])
        weighted_features = features * weights
        
        # Calculate base sentiment score
        base_score = np.sum(weighted_features)
        
        # Apply financial phrase modifiers
        phrase_modifier = 0.0
        phrase_count = 0
        
        # Check for financial phrases
        text = " ".join(tokens)
        for phrase, modifier in self.financial_terms.items():
            if phrase in text:
                phrase_modifier += modifier
                phrase_count += 1
        
        # Apply phrase modifier
        if phrase_count > 0:
            phrase_modifier /= phrase_count
            sentiment_score = (base_score * 0.7) + (phrase_modifier * 0.3)
        else:
            sentiment_score = base_score
        
        # Calculate confidence
        # Confidence is higher when:
        # 1. More sentiment words are found
        # 2. Sentiment score is further from neutral
        sentiment_word_count = sum(1 for token in tokens if token in self.word_embeddings)
        score_confidence = min(1.0, abs(sentiment_score) * 2)  # Higher for extreme scores
        word_confidence = min(1.0, sentiment_word_count / 5.0)  # Higher with more sentiment words
        
        confidence = (score_confidence * 0.6) + (word_confidence * 0.4)
        
        return sentiment_score, confidence
    
    def _extract_sentiment_words(self, tokens: List[str]) -> Tuple[List[str], List[str], List[str]]:
        """Extract positive, negative, and neutral words from tokens"""
        positive_words = []
        negative_words = []
        neutral_words = []
        
        for token in tokens:
            if token in self.word_embeddings:
                embedding = self.word_embeddings[token]
                weighted_embedding = np.array(embedding) * self.sentiment_weights.get("dimension_weights", [0.7, 0.3])
                score = np.sum(weighted_embedding)
                
                if score > self.sentiment_weights.get("positive_threshold", 0.3):
                    positive_words.append(token)
                elif score < self.sentiment_weights.get("negative_threshold", -0.3):
                    negative_words.append(token)
                else:
                    neutral_words.append(token)
        
        return positive_words, negative_words, neutral_words
    
    def _extract_financial_phrases(self, text: str) -> List[Dict[str, Any]]:
        """Extract financial phrases with sentiment modifiers"""
        text_lower = text.lower()
        phrases = []
        
        for phrase, modifier in self.financial_terms.items():
            if phrase in text_lower:
                phrases.append({
                    "phrase": phrase,
                    "sentiment_modifier": modifier,
                    "sentiment": "positive" if modifier > 0 else "negative" if modifier < 0 else "neutral"
                })
        
        return phrases
    
    async def save_model(self):
        """Save sentiment model data"""
        try:
            # Create model directory if it doesn't exist
            os.makedirs(SENTIMENT_MODEL_PATH, exist_ok=True)
            
            # Save model files
            with open(os.path.join(SENTIMENT_MODEL_PATH, 'word_embeddings.json'), 'w') as f:
                json.dump(self.word_embeddings, f, indent=2)
            
            with open(os.path.join(SENTIMENT_MODEL_PATH, 'sentiment_weights.json'), 'w') as f:
                json.dump(self.sentiment_weights, f, indent=2)
            
            with open(os.path.join(SENTIMENT_MODEL_PATH, 'financial_terms.json'), 'w') as f:
                json.dump(self.financial_terms, f, indent=2)
            
            logger.info("Sentiment model saved successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error saving sentiment model: {e}")
            return False
    
    async def update_model(self, feedback: Dict[str, Any]):
        """Update model based on feedback"""
        try:
            # Extract feedback data
            text = feedback.get("text", "")
            expected_sentiment = feedback.get("expected_sentiment", "")
            
            if not text or not expected_sentiment:
                logger.warning("Invalid feedback data")
                return False
            
            # Preprocess text
            tokens = self._preprocess_text(text)
            
            # Update word embeddings based on feedback
            for token in tokens:
                if token in self.word_embeddings:
                    embedding = self.word_embeddings[token]
                    
                    # Adjust embedding based on expected sentiment
                    if expected_sentiment == "positive" and embedding[0] < 0.9:
                        embedding[0] += 0.1
                    elif expected_sentiment == "negative" and embedding[0] > -0.9:
                        embedding[0] -= 0.1
                    
                    self.word_embeddings[token] = embedding
            
            # Save updated model
            await self.save_model()
            return True
            
        except Exception as e:
            logger.error(f"Error updating sentiment model: {e}")
            return False

# Global instance
ml_sentiment_analyzer = MLSentimentAnalyzer()

# Convenience function
async def analyze_sentiment_ml(text: str) -> Dict[str, Any]:
    """
    Analyze sentiment using ML-based approach
    
    Args:
        text: Text to analyze
        
    Returns:
        Dictionary with sentiment analysis results
    """
    return await ml_sentiment_analyzer.analyze_sentiment(text)
