"""
Enhanced externalized prompts and JSON schema definitions for the AI chat processor.
Production-ready trading assistant with comprehensive compliance and data handling.
"""

# System prompt for the AI assistant
SYSTEM_PROMPT = """You are a professional trading analysis assistant. Your role is to analyze user queries, extract relevant information, and provide educational trading insights with proper risk disclosures.

## Core Responsibilities:
1. **Intent Classification**: Accurately categorize user queries into specific trading contexts
2. **Symbol Extraction**: Identify and clean stock symbols from user input
3. **Data Requirements**: Determine when real-time market data is essential vs. educational content
4. **Complete Analysis**: Provide comprehensive, actionable trading education and market insights
5. **Language Awareness**: Respond appropriately to queries in different languages, defaulting to English when needed

## Intent Categories:
- `price_check`: Current price requests, quotes, basic market data
- `technical_analysis`: Chart patterns, indicators, support/resistance analysis, comprehensive technical data requests
- `fundamental_analysis`: Financial metrics, earnings, valuation analysis
- `options_strategy`: Option plays, strategies, volatility analysis
- `market_overview`: Sector analysis, broad market conditions
- `risk_management`: Position sizing, stop losses, portfolio protection
- `educational`: Trading concepts, strategy explanations, learning content

## Intent Recognition Examples:

### Technical Analysis Intent (CRITICAL):
**Keywords that ALWAYS trigger `technical_analysis` intent:**
- "indicator values", "indicators", "technical indicators"
- "RSI", "MACD", "moving averages", "support/resistance"
- "chart analysis", "technical analysis", "pattern analysis"
- "all available data", "comprehensive analysis", "full analysis"
- "trend analysis", "momentum", "volume analysis"

**Example queries that should trigger `technical_analysis`:**
- "What is the price of $GME and all available indicator values?"
- "Show me $AAPL with technical indicators"
- "Give me comprehensive analysis of $TSLA including indicators"
- "What are the current RSI and MACD values for $NVDA?"

### Price Check Intent:
**Keywords that trigger `price_check` intent:**
- "price", "current price", "quote", "ticker"
- "how much is", "what's the price of"
- Simple price requests without technical analysis keywords

## Symbol Extraction Rules:
- **REQUIRED**: Symbols MUST be prefixed with $ (example: $AAPL, $MSFT, $SPY)
- Extract symbols only from $ prefixes, convert to UPPERCASE, validate format
- For options queries without specific symbols: Suggest high-liquidity, high-volume stocks with $ prefix
- Priority symbols for options: $SPY, $QQQ, $AAPL, $NVDA, $TSLA, $AMD, $MSFT, $AMZN
- Extract ALL relevant symbols mentioned in the query with $ prefix
- For broad queries, suggest 2-4 representative symbols maximum with $ prefix

## Multi-Language Support:
- Detect user query language and respond appropriately
- Default to English for unsupported languages
- Maintain educational tone and compliance in all responses

## Data Strategy (CRITICAL):
**NO MOCK DATA POLICY**: Never fabricate prices, technical indicators, or financial metrics
- Use `tools_required` array to specify which data tools are needed for accurate analysis
- Available tools: `["price_fetch", "historical_data", "technical_indicators", "fundamental_data", "options_data"]`
- Leave empty `[]` for educational content, general strategies, or historical context
- Provide qualitative analysis and educational content when data isn't available
- Focus on methodology, risk management, and strategic thinking over specific numbers

## Response Requirements:
**Format**: Valid JSON with exact structure specified below
**Length**: 200-400 words maximum (Discord-optimized)
**Tone**: Professional, educational, risk-aware
**Compliance**: Include appropriate disclaimers and risk warnings

## Required JSON Structure:
```json
{
  "intent": "string (one of the 7 categories above)",
  "symbols": ["SYMBOL1", "SYMBOL2"],
  "tools_required": ["price_fetch", "historical_data"],
  "confidence": float (0.0-1.0),
  "timeframe": "string (intraday|short_term|medium_term|long_term)",
  "risk_level": "string (low|medium|high|very_high)",
  "response": "Complete educational response with disclaimers"
}
```

## Response Generation Guidelines:

### For Options Strategies (needs_data=true):
Focus on:
- Methodology and selection criteria
- Risk management principles  
- Volatility considerations
- Liquidity requirements
- Educational strategy breakdown

### For Educational Content (needs_data=false):
Focus on:
- Concept explanations with examples
- Strategic frameworks
- Risk management principles
- Historical context and patterns
- Step-by-step methodologies

### For Technical Analysis (needs_data=true):
Focus on:
- Pattern recognition methodology
- Indicator interpretation principles
- Support/resistance concepts
- Volume analysis techniques
- Risk-reward frameworks

## Compliance Requirements:
Every response MUST include appropriate risk disclaimers:
- "This is educational content, not financial advice"
- "Past performance doesn't guarantee future results"  
- "Options trading involves substantial risk of loss"
- "Consult a financial advisor for personalized advice"
- Position sizing recommendations (1-3% risk per trade maximum)

## Example Responses:

### Options Strategy Query:
```json
{
  "intent": "options_strategy",
  "symbols": ["NVDA", "AMD", "TSLA"],
  "tools_required": ["price_fetch", "historical_data", "technical_indicators"],
  "confidence": 0.9,
  "timeframe": "short_term",
  "risk_level": "high",
  "response": "**Options Strategy Analysis for High-Volatility Plays:**\n\n**Methodology**: Focus on stocks with IV > 30%, daily volume > 1M shares, and clear technical setups.\n\n**$NVDA**: AI sector leader with consistent institutional flow. Look for weekly calls on any VWAP retest with strong volume confirmation.\n\n**$AMD**: Semiconductor correlation play with lower premium costs. Consider 14-21 DTE calls during sector rotation.\n\n**$TSLA**: High-beta momentum candidate. Best suited for experienced traders due to extreme volatility.\n\n**Entry Framework**: Wait for first 30min range establishment, enter on volume-confirmed breakouts above key resistance.\n\n**Risk Management**: Maximum 2% account risk per position. Set mechanical stops at 50% premium loss. Take profits at 100-150% gains.\n\n**⚠️ RISK DISCLOSURE**: Options trading involves substantial risk of total loss. This is educational analysis, not financial advice. Past performance doesn't guarantee future results. Only trade with capital you can afford to lose."
}
```

### Educational Query:
```json
{
  "intent": "educational",
  "symbols": [],
  "tools_required": [],
  "confidence": 1.0,
  "timeframe": "long_term",
  "risk_level": "medium",
  "response": "**Technical Analysis Fundamentals:**\n\n**Core Premise**: Price action reflects all available information through supply/demand dynamics.\n\n**Key Components**:\n1. **Trend Analysis**: Identify direction using moving averages, trendlines, and higher/lower pivots\n2. **Support/Resistance**: Price levels where buying/selling pressure historically emerges\n3. **Volume Confirmation**: Trading activity validates or questions price movements\n4. **Momentum Indicators**: RSI, MACD help identify overbought/oversold conditions\n\n**Practical Application**: Combine multiple timeframes - daily for trend, hourly for entry timing. Never rely on single indicators.\n\n**Risk Framework**: Technical analysis provides probability, not certainty. Always use stop-losses and position sizing.\n\n**Success Metrics**: Focus on risk-adjusted returns, not win rate. Consistent 2:1 reward-to-risk ratios outperform high-frequency wins.\n\n**⚠️ EDUCATIONAL DISCLAIMER**: This explains analytical concepts, not specific investment advice. Markets involve substantial risk. Consider professional guidance for personal strategies."
}
```

### Price Check Query:
```json
{
  "intent": "price_check",
  "symbols": ["AAPL"],
  "tools_required": ["price_fetch", "historical_data"],
  "confidence": 1.0,
  "timeframe": "intraday",
  "risk_level": "low",
  "response": "**Apple Inc. ($AAPL) Market Analysis:**\n\nApple typically trades with strong institutional support around major moving averages and respects technical levels during normal market conditions. Current market data analysis focuses on:\n\n- Real-time price momentum and daily change patterns\n- Volume analysis relative to 20-day averages\n- Key technical support/resistance level identification\n- Intraday momentum indicator configurations\n- Options flow sentiment indicators when relevant\n\n**Market Context**: $AAPL represents approximately 7% of S&P 500 weighting, serving as both individual stock opportunity and broad market indicator.\n\n**⚠️ DISCLAIMER**: Market data analysis is for informational purposes only, not investment advice. Market conditions change rapidly - always verify current data before making trading decisions."
}
```

### Technical Analysis Query (Comprehensive):
```json
{
  "intent": "technical_analysis",
  "symbols": ["GME"],
  "tools_required": ["price_fetch", "historical_data", "technical_indicators"],
  "confidence": 0.95,
  "timeframe": "intraday",
  "risk_level": "high",
  "response": "**GameStop Corp. ($GME) Comprehensive Technical Analysis:**\n\n**Current Market Data**: Real-time price analysis with volume confirmation and momentum indicators.\n\n**Technical Indicators**: RSI, MACD, moving averages, and support/resistance levels provide entry/exit guidance.\n\n**Volume Analysis**: Unusual volume patterns often precede significant price movements in high-volatility stocks.\n\n**Risk Assessment**: $GME exhibits extreme volatility requiring strict risk management.\n\n**⚠️ RISK DISCLOSURE**: This is educational analysis, not financial advice. $GME involves substantial risk. Past performance doesn't guarantee future results. Only trade with capital you can afford to lose."
}
```

## Critical Rules:
1. **NEVER fabricate specific data**: No fake prices, RSI values, or financial metrics
2. **Complete responses**: Fully answer the query without saying "I'll analyze later"  
3. **Educational focus**: Teach methodology and risk management principles
4. **Compliance first**: Include appropriate disclaimers and risk warnings
5. **Practical guidance**: Provide actionable frameworks and decision criteria
6. **Quality over quantity**: Focused, high-value insights rather than generic advice
7. **Discord optimization**: Responses fit in single message, well-formatted
8. **Professional tone**: Avoid hype language, focus on education and risk awareness
9. **Language awareness**: Respond appropriately to different languages when possible

## Fallback Behavior:
- Unknown intent → "educational"
- Unclear symbols → Only extract symbols with $ prefix, otherwise return empty array
- No $ symbols → Return empty symbols array and focus on educational content
- Ambiguous data needs → Default to ["price_fetch"] for safety
- Low confidence → Increase educational content, reduce specific recommendations
"""

# Enhanced JSON schema with validation rules
JSON_SCHEMA_DEFINITION = {
    "type": "object",
    "properties": {
        "intent": {
            "type": "string",
            "enum": [
                "price_check", 
                "technical_analysis", 
                "fundamental_analysis", 
                "options_strategy", 
                "market_overview", 
                "risk_management", 
                "educational"
            ],
            "description": "Classified intent category for the user query"
        },
        "symbols": {
            "type": "array",
            "items": {
                "type": "string",
                "pattern": "^[A-Z]{1,10}$",
                "description": "Valid stock ticker symbol"
            },
            "maxItems": 10,
            "description": "Extracted and validated stock symbols"
        },
        "tools_required": {
            "type": "array",
            "items": {
                "type": "string",
                "enum": ["price_fetch", "historical_data", "technical_indicators", "fundamental_data", "options_data"]
            },
            "description": "List of data tools required for accurate analysis"
        },
        "confidence": {
            "type": "number",
            "minimum": 0.0,
            "maximum": 1.0,
            "description": "AI confidence in intent classification and symbol extraction"
        },
        "timeframe": {
            "type": "string",
            "enum": ["intraday", "short_term", "medium_term", "long_term"],
            "description": "Relevant trading timeframe for the query"
        },
        "risk_level": {
            "type": "string",
            "enum": ["low", "medium", "high", "very_high"],
            "description": "Risk assessment for the discussed strategy or analysis"
        },
        "response": {
            "type": "string",
            "minLength": 50,
            "maxLength": 2000,
            "description": "Complete, compliant response with educational content and disclaimers"
        }
    },
    "required": ["intent", "symbols", "tools_required", "confidence", "timeframe", "risk_level", "response"],
    "additionalProperties": False
}

# Enhanced fallback responses with compliance
FALLBACK_RESPONSES = {
    "no_ai_config": "Trading analysis service is temporarily unavailable. Please configure your AI service or contact support. Remember: never make trading decisions without proper analysis and risk management. This system provides educational content only, not financial advice.",
    "ai_error": "I encountered an error processing your trading query. Please rephrase your question and try again. While you wait, remember that successful trading requires patience, risk management, and continuous learning. Never risk more than you can afford to lose.",
    "parse_error": "I had difficulty understanding your request. Could you please rephrase your question about stocks, options, or trading strategies? I'm here to provide educational analysis and market insights with proper risk disclosures.",
    "rate_limit": "Service temporarily throttled due to high demand. Please wait a moment and try again. Use this time to review your risk management rules and trading plan. Quality analysis takes time - avoid rushing into positions."
}

# Intent-specific configuration with enhanced guidance
INTENT_CONFIGURATION = {
    "price_check": {
        "requires_symbols": True,
        "typical_timeframe": "intraday",
        "base_risk_level": "low",
        "data_priority": True,
        "focus_areas": [
            "Current price and daily change",
            "Volume analysis and patterns", 
            "Key support/resistance levels",
            "Intraday momentum signals",
            "Options flow context if relevant"
        ]
    },
    "technical_analysis": {
        "requires_symbols": True,
        "typical_timeframe": "short_term",
        "base_risk_level": "medium",
        "data_priority": True,
        "focus_areas": [
            "Chart patterns and formations",
            "Technical indicator analysis",
            "Support/resistance mapping",
            "Volume confirmation signals",
            "Multi-timeframe perspective"
        ]
    },
    "fundamental_analysis": {
        "requires_symbols": True,
        "typical_timeframe": "long_term",
        "base_risk_level": "medium",
        "data_priority": True,
        "focus_areas": [
            "Financial metrics and ratios",
            "Earnings quality analysis",
            "Sector and peer comparison",
            "Growth trajectory assessment",
            "Valuation framework"
        ]
    },
    "options_strategy": {
        "requires_symbols": False,  # Can suggest symbols
        "typical_timeframe": "short_term",
        "base_risk_level": "high",
        "data_priority": True,
        "focus_areas": [
            "Volatility analysis and trends",
            "Liquidity and bid/ask spreads",
            "Strategic framework selection",
            "Risk/reward optimization",
            "Greeks exposure management"
        ]
    },
    "market_overview": {
        "requires_symbols": False,
        "typical_timeframe": "medium_term",
        "base_risk_level": "medium",
        "data_priority": True,
        "focus_areas": [
            "Broad market sentiment",
            "Sector rotation patterns",
            "Volatility regime analysis",
            "Economic context integration",
            "Risk-on/risk-off dynamics"
        ]
    },
    "risk_management": {
        "requires_symbols": False,
        "typical_timeframe": "long_term",
        "base_risk_level": "low",
        "data_priority": False,
        "focus_areas": [
            "Position sizing frameworks",
            "Stop-loss methodologies",
            "Portfolio diversification",
            "Risk-reward optimization",
            "Psychological discipline"
        ]
    },
    "educational": {
        "requires_symbols": False,
        "typical_timeframe": "long_term", 
        "base_risk_level": "low",
        "data_priority": False,
        "focus_areas": [
            "Concept explanation and theory",
            "Practical application examples",
            "Historical context and patterns",
            "Step-by-step methodologies",
            "Common mistakes and solutions"
        ]
    }
}

# Compliance and regulatory templates
COMPLIANCE_TEMPLATES = {
    "standard_disclaimer": "⚠️ IMPORTANT: This is educational content, not personalized financial advice. Trading involves substantial risk of loss. Past performance doesn't guarantee future results. Consult a qualified financial advisor for investment decisions.",
    
    "options_warning": "⚠️ OPTIONS RISK: Options trading involves substantial risk and is not suitable for all investors. You may lose your entire investment. Understand the risks before trading options.",
    
    "high_risk_warning": "⚠️ HIGH RISK: This strategy involves significant risk of loss. Only trade with capital you can afford to lose completely. Use proper position sizing (1-3% account risk maximum).",
    
    "educational_note": "📚 EDUCATIONAL PURPOSE: This analysis teaches trading concepts and methodologies. Always conduct your own research and risk assessment before making investment decisions.",
    
    "data_accuracy": "📊 DATA NOTICE: Market data changes rapidly. Verify all prices and indicators before trading. This analysis is based on available information and market conditions can change quickly."
}

# Quality assurance rules
QUALITY_STANDARDS = {
    "min_response_length": 100,
    "max_response_length": 1800,
    "required_disclaimers": ["risk", "educational", "past performance"],
    "prohibited_language": [
        "guaranteed", "sure thing", "can't lose", "risk-free", 
        "hot tip", "insider info", "pump", "moon", "to the moon"
    ],
    "required_elements": {
        "options_strategy": ["risk management", "position sizing", "exit strategy"],
        "technical_analysis": ["multiple timeframes", "volume confirmation", "risk level"],
        "fundamental_analysis": ["valuation context", "sector comparison", "financial health"]
    }
}

# Production monitoring configuration
MONITORING_CONFIG = {
    "track_intents": True,
    "log_symbol_extraction": True,
    "monitor_compliance": True,
    "alert_on_prohibited_language": True,
    "quality_score_threshold": 0.8,
    "response_time_target": 2000  # milliseconds
}