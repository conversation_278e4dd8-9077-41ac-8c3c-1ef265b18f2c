"""
Symbol Validator - Dollar Sign Required

Only extracts ticker symbols that are prefixed with $ sign.
Clean, simple implementation with proper error handling.
"""

import logging
import os
from typing import List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SymbolSuggestion:
    """Symbol extracted from query with confidence metrics"""
    text: str
    confidence: float
    context: str
    is_likely_ticker: bool
    suggestions: List[str]

class SymbolValidator:
    """
    Extracts stock symbols only when prefixed with $ sign.
    Simple, reliable, and focused on reducing false positives.
    """
    
    def __init__(self):
        self.valid_tickers = set()
        self._load_ticker_data()
    
    def _load_ticker_data(self):
        """Load ticker database for validation"""
        try:
            ticker_file = "data/tickers/all_tickers.txt"
            if os.path.exists(ticker_file):
                with open(ticker_file, 'r') as f:
                    self.valid_tickers = {line.strip().upper() for line in f if line.strip()}
                logger.info(f"Loaded {len(self.valid_tickers)} valid tickers")
            else:
                logger.warning(f"Ticker database not found: {ticker_file}")
                # Fallback to common tickers
                self.valid_tickers = {
                    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'SPY', 'QQQ'
                }
        except Exception as e:
            logger.error(f"Failed to load ticker data: {e}")
            self.valid_tickers = set()
    
    def suggest_symbols(self, query: str) -> List[SymbolSuggestion]:
        """
        Extract symbols only when prefixed with $ sign.
        
        Args:
            query: User input query
            
        Returns:
            List of symbol suggestions with high confidence
        """
        if not query or '$' not in query:
            return []
        
        logger.info(f"Extracting symbols from query with $ prefix")
        
        suggestions = []
        parts = query.split('$')
        
        # Process each part after a $ sign
        for part in parts[1:]:  # Skip first part (before any $)
            symbol = self._extract_symbol_from_text(part)
            if symbol:
                confidence = self._calculate_confidence(symbol)
                suggestions.append(SymbolSuggestion(
                    text=symbol,
                    confidence=confidence,
                    context="dollar_prefix",
                    is_likely_ticker=confidence > 0.8,
                    suggestions=[symbol]
                ))
        
        # Remove duplicates while preserving order
        unique_suggestions = []
        seen = set()
        for suggestion in suggestions:
            if suggestion.text not in seen:
                unique_suggestions.append(suggestion)
                seen.add(suggestion.text)
        
        logger.info(f"Found {len(unique_suggestions)} unique symbols: {[s.text for s in unique_suggestions]}")
        return unique_suggestions
    
    def _extract_symbol_from_text(self, text: str) -> Optional[str]:
        """Extract ticker symbol from text following $ sign"""
        if not text:
            return None
        
        # Extract consecutive alphanumeric characters from start
        symbol = ""
        for char in text:
            if char.isalnum():
                symbol += char.upper()
            else:
                break
        
        # Basic validation
        if len(symbol) < 1 or len(symbol) > 10:
            return None
        
        # Filter out obvious non-tickers
        non_tickers = {'USD', 'CAD', 'EUR', 'GBP', 'JPY', 'THE', 'AND', 'FOR', 'YOU', 'ARE'}
        if symbol in non_tickers:
            return None
        
        return symbol
    
    def _calculate_confidence(self, symbol: str) -> float:
        """Calculate confidence score for extracted symbol"""
        if not symbol:
            return 0.0
        
        # High confidence if in ticker database
        if symbol in self.valid_tickers:
            return 0.95
        
        # Medium confidence for reasonable looking symbols
        if 1 <= len(symbol) <= 6 and symbol.isalpha():
            return 0.7
        
        # Lower confidence for longer or mixed symbols
        if len(symbol) <= 10:
            return 0.5
        
        return 0.2
    
    def should_trigger_analysis(self, symbols: List[SymbolSuggestion]) -> bool:
        """
        Determine if symbols should trigger market data analysis.
        
        Args:
            symbols: List of extracted symbol suggestions
            
        Returns:
            True if analysis should be triggered
        """
        if not symbols:
            return False
        
        # Trigger analysis if we have at least one high-confidence symbol
        return any(s.confidence >= 0.7 for s in symbols)
    
    def validate_symbol_format(self, symbol: str) -> bool:
        """Validate if symbol follows expected ticker format"""
        if not symbol:
            return False
        
        # Basic format validation
        return (symbol.isalpha() and 
                1 <= len(symbol) <= 10 and 
                symbol.isupper())
    
    def get_error_message_for_query(self, query: str) -> Optional[str]:
        """Generate helpful error message if no symbols found"""
        if not query:
            return "Please provide a query with stock symbols."
        
        if '$' not in query:
            return "Please prefix ticker symbols with $ (example: $AAPL, $MSFT)"
        
        # Has $ but no valid symbols extracted
        symbols = self.suggest_symbols(query)
        if not symbols:
            return "No valid ticker symbols found after $. Please use format like $AAPL or $MSFT"
        
        return None

    # ===== ENHANCEMENT METHODS (NEW) =====
    # These methods add preprocessing capabilities while preserving existing functionality

    def enhanced_validation(self, query: str, enable_preprocessing: bool = True) -> dict:
        """
        Enhanced validation with optional preprocessing features.
        
        Args:
            query: User input query
            enable_preprocessing: Whether to enable enhanced preprocessing
            
        Returns:
            Dictionary with validation results and enhancements
        """
        # Use existing validation logic
        base_suggestions = self.suggest_symbols(query)
        
        result = {
            "success": True,
            "suggestions": base_suggestions,
            "enhancements": [],
            "metadata": {}
        }
        
        # Add preprocessing if enabled
        if enable_preprocessing:
            # Query sanitization
            sanitized_query = self._sanitize_query(query)
            if sanitized_query != query:
                result["enhancements"].append("query_sanitization")
                result["sanitized_query"] = sanitized_query
            
            # Basic context analysis
            context = self._analyze_query_context(query)
            result["enhancements"].append("context_analysis")
            result["context"] = context
            
            # Enhanced metadata
            result["metadata"] = {
                "query_length": len(query),
                "symbol_count": len(base_suggestions),
                "processing_mode": "enhanced"
            }
        else:
            result["metadata"] = {
                "processing_mode": "legacy"
            }
        
        return result

    def _sanitize_query(self, query: str) -> str:
        """Simple query sanitization for security."""
        import re
        
        # Remove basic dangerous patterns
        dangerous = [
            r'<script.*?>', r'javascript:', r'<iframe.*?>', r'<object.*?>',
            r'<embed.*?>', r'<form.*?>', r'<input.*?>', r'<textarea.*?>'
        ]
        
        sanitized = query
        for pattern in dangerous:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        
        # Basic length limit
        if len(sanitized) > 2000:
            sanitized = sanitized[:2000]
        
        return sanitized.strip()

    def _analyze_query_context(self, query: str) -> dict:
        """Simple context analysis of the query."""
        return {
            "contains_symbols": '$' in query,
            "contains_numbers": any(c.isdigit() for c in query),
            "estimated_complexity": "simple" if len(query) < 100 else "moderate" if len(query) < 300 else "complex"
        }

    def is_enhanced_mode(self) -> bool:
        """Check if enhanced features are available."""
        return hasattr(self, 'enhanced_validation')