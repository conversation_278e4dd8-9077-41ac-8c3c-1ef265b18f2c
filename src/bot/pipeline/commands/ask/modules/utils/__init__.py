"""
Utilities Package
=================

Common utilities for the trading bot pipeline.

This package provides essential utility functions and classes for:
- Configuration management
- Input validation
- Error handling
- Logging
- Caching
- Retry mechanisms
"""

from .config import (
    get_config,
    set_config,
    get_int,
    get_float,
    get_bool,
    get_list,
    validate_configuration,
    ConfigManager,
    ConfigValue
)

from .exceptions import (
    BotUtilsError,
    CacheError,
    ValidationError,
    RetryExhaustedError,
    RateLimitError,
    ConfigurationError,
    DataSourceError,
    APIError,
    InsufficientDataError,
    ProcessingError,
    AuthenticationError,
    ServiceUnavailableError,
    TimeoutError,
    NetworkError,
    DatabaseError,
    SerializationError,
    InvalidStateError,
    DependencyError,
    ResourceError,
    SecurityError,
    BusinessLogicError,
    MarketDataError,
    IndicatorCalculationError,
    PipelineError,
    StageError,
    ContextualError,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    get_user_friendly_error_message
)

from .validation import (
    validate_symbol,
    validate_date_range,
    validate_price,
    validate_url,
    validate_json,
    validate_choice,
    sanitize_string
)

__all__ = [
    # Configuration
    'get_config',
    'set_config',
    'get_int',
    'get_float',
    'get_bool',
    'get_list',
    'validate_configuration',
    'ConfigManager',
    'ConfigValue',
    
    # Exceptions
    'BotUtilsError',
    'CacheError',
    'ValidationError',
    'RetryExhaustedError',
    'RateLimitError',
    'ConfigurationError',
    'DataSourceError',
    'APIError',
    'InsufficientDataError',
    'ProcessingError',
    'AuthenticationError',
    'ServiceUnavailableError',
    'TimeoutError',
    'NetworkError',
    'DatabaseError',
    'SerializationError',
    'InvalidStateError',
    'DependencyError',
    'ResourceError',
    'SecurityError',
    'BusinessLogicError',
    'MarketDataError',
    'IndicatorCalculationError',
    'PipelineError',
    'StageError',
    'ContextualError',
    'ErrorCollector',
    'ErrorHandler',
    'get_user_friendly_error_message',
    
    # Validation
    'validate_symbol',
    'validate_date_range',
    'validate_price',
    'validate_url',
    'validate_json',
    'validate_choice',
    'sanitize_string'
]

__version__ = "1.0.0"
__author__ = "Trading Bot Team"