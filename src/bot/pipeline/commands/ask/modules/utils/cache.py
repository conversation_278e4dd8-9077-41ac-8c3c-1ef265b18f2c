
"""
Caching Utilities
================

Memory, Redis, and hybrid caching with TTL support and automatic cleanup.

This module provides comprehensive caching solutions with multiple backends,
TTL support, automatic cleanup, and cache warming capabilities.
"""

import time
import json
import hashlib
import pickle
import threading
from typing import Any, Dict, List, Optional, Callable, Union, Tuple
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
import logging
from pathlib import Path
import redis
from .exceptions import CacheError, ValidationError

logger = logging.getLogger(__name__)


class CacheBackend(ABC):
    """Abstract base class for cache backends."""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """Clear all cache entries."""
        pass
    
    @abstractmethod
    def size(self) -> int:
        """Get cache size."""
        pass


class MemoryCacheBackend(CacheBackend):
    """In-memory cache backend with TTL support."""
    
    def __init__(self, max_size: Optional[int] = None):
        self._cache: Dict[str, Tuple[Any, Optional[float]]] = {}
        self._max_size = max_size
        self._lock = threading.RLock()
        self._cleanup_thread = None
        self._cleanup_interval = 60  # seconds
        self._start_cleanup()
    
    def _start_cleanup(self):
        """Start background cleanup thread."""
        if self._cleanup_thread is None:
            self._cleanup_thread = threading.Thread(
                target=self._cleanup_expired,
                daemon=True
            )
            self._cleanup_thread.start()
    
    def _cleanup_expired(self):
        """Clean up expired entries."""
        while True:
            time.sleep(self._cleanup_interval)
            with self._lock:
                now = time.time()
                expired_keys = [
                    key for key, (_, expiry) in self._cache.items()
                    if expiry is not None and expiry <= now
                ]
                for key in expired_keys:
                    del self._cache[key]
    
    def _is_expired(self, expiry: Optional[float]) -> bool:
        """Check if entry is expired."""
        if expiry is None:
            return False
        return time.time() > expiry
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self._lock:
            if key not in self._cache:
                return None
            
            value, expiry = self._cache[key]
            if self._is_expired(expiry):
                del self._cache[key]
                return None
            
            return value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        with self._lock:
            # Handle max size limit
            if self._max_size and len(self._cache) >= self._max_size:
                # Simple LRU: remove oldest entry
                if self._cache:
                    oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k][1] or float('inf'))
                    del self._cache[oldest_key]
            
            expiry = time.time() + ttl if ttl is not None else None
            self._cache[key] = (value, expiry)
            return True
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        return self.get(key) is not None
    
    def clear(self) -> bool:
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
            return True
    
    def size(self) -> int:
        """Get cache size."""
        with self._lock:
            # Clean up expired entries first
            now = time.time()
            self._cache = {
                key: value for key, value in self._cache.items()
                if not self._is_expired(value[1])
            }
            return len(self._cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            now = time.time()
            total_entries = len(self._cache)
            expired_entries = sum(
                1 for _, expiry in self._cache.values()
                if expiry is not None and expiry <= now
            )
            
            return {
                'total_entries': total_entries,
                'active_entries': total_entries - expired_entries,
                'expired_entries': expired_entries,
                'max_size': self._max_size
            }


class RedisCacheBackend(CacheBackend):
    """Redis cache backend."""
    
    def __init__(self, host: str = None, port: int = None, 
                 db: int = 0, password: Optional[str] = None,
                 key_prefix: str = 'trading_bot:'):
        # Use environment variables for defaults
        import os
        self.host = host or os.getenv('REDIS_HOST', 'localhost')
        self.port = port or int(os.getenv('REDIS_PORT', '6379'))
        self.db = db
        self.password = password
        self.key_prefix = key_prefix
        self._client = None
        self._connect()
    
    def _connect(self):
        """Connect to Redis."""
        try:
            self._client = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                decode_responses=False
            )
            self._client.ping()
        except redis.ConnectionError as e:
            raise CacheError(f"Failed to connect to Redis: {e}")
    
    def _get_key(self, key: str) -> str:
        """Get prefixed key."""
        return f"{self.key_prefix}{key}"
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            value = self._client.get(self._get_key(key))
            if value is None:
                return None
            return pickle.loads(value)
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            serialized = pickle.dumps(value)
            redis_key = self._get_key(key)
            
            if ttl is not None:
                return bool(self._client.setex(redis_key, ttl, serialized))
            else:
                return bool(self._client.set(redis_key, serialized))
        except Exception as e:
            logger.error(f"Redis set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            return bool(self._client.delete(self._get_key(key)))
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            return bool(self._client.exists(self._get_key(key)))
        except Exception as e:
            logger.error(f"Redis exists error: {e}")
            return False
    
    def clear(self) -> bool:
        """Clear all cache entries."""
        try:
            keys = self._client.keys(f"{self.key_prefix}*")
            if keys:
                return bool(self._client.delete(*keys))
            return True
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
            return False
    
    def size(self) -> int:
        """Get cache size."""
        try:
            keys = self._client.keys(f"{self.key_prefix}*")
            return len(keys) if keys else 0
        except Exception as e:
            logger.error(f"Redis size error: {e}")
            return 0


class CacheManager:
    """Central cache manager supporting multiple backends."""
    
    def __init__(self, backend: Optional[CacheBackend] = None):
        self.backend = backend or MemoryCacheBackend()
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
           