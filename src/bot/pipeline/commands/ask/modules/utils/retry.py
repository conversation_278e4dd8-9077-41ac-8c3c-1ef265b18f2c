"""
Retry Utility
=============

Retry utilities with exponential backoff and circuit breaker patterns.
"""

import time
import functools
import logging
from typing import Callable, Any, Optional, Tuple, Type
from .exceptions import RetryExhaustedError
from .config import get_config


logger = logging.getLogger(__name__)


class RetryConfig:
    """Configuration for retry behavior."""
    
    def __init__(self, max_retries: int = 3, initial_delay: float = 1.0,
                 max_delay: float = 60.0, backoff_factor: float = 2.0,
                 jitter: bool = True, exceptions: Tuple[Type[Exception], ...] = None):
        """
        Initialize retry configuration.
        
        Args:
            max_retries: Maximum number of retry attempts
            initial_delay: Initial delay between retries in seconds
            max_delay: Maximum delay between retries in seconds
            backoff_factor: Multiplier for delay after each retry
            jitter: Add random jitter to delays
            exceptions: Tuple of exception types to retry on
        """
        self.max_retries = max_retries
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        self.exceptions = exceptions or (Exception,)


class CircuitBreaker:
    """Circuit breaker pattern implementation."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0,
                 expected_exception: Type[Exception] = Exception):
        """
        Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time to wait before attempting recovery
            expected_exception: Exception type to track
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self._failure_count = 0
        self._last_failure_time = None
        self._state = "closed"  # closed, open, half-open
    
    @property
    def state(self) -> str:
        """Get current circuit breaker state."""
        if self._state == "open":
            if self._last_failure_time and time.time() - self._last_failure_time >= self.recovery_timeout:
                self._state = "half-open"
        return self._state
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        if self.state == "open":
            raise Exception("Circuit breaker is open")
        
        try:
            result = func(*args, **kwargs)
            if self._state == "half-open":
                self._state = "closed"
                self._failure_count = 0
            return result
        
        except self.expected_exception as e:
            self._failure_count += 1
            self._last_failure_time = time.time()
            
            if self._failure_count >= self.failure_threshold:
                self._state = "open"
            
            raise e


def retry_with_backoff(
    max_retries: Optional[int] = None,
    initial_delay: Optional[float] = None,
    max_delay: Optional[float] = None,
    backoff_factor: Optional[float] = None,
    exceptions: Optional[Tuple[Type[Exception], ...]] = None,
    logger: Optional[logging.Logger] = None
):
    """
    Decorator for retry with exponential backoff.
    
    Args:
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Multiplier for delay after each retry
        exceptions: Tuple of exception types to retry on
        logger: Logger instance for retry messages
        
    Returns:
        Decorated function
    """
    config = get_config()
    
    if max_retries is None:
        max_retries = config.ai.max_retries
    
    if initial_delay is None:
        initial_delay = 1.0
    
    if max_delay is None:
        max_delay = 60.0
    
    if backoff_factor is None:
        backoff_factor = config.pipeline.backoff_factor
    
    if exceptions is None:
        exceptions = (Exception,)
    
    if logger is None:
        logger = logging.getLogger(__name__)
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(
                            f"Function {func.__name__} failed after {max_retries} retries. "
                            f"Last error: {str(e)}"
                        )
                        raise RetryExhaustedError(
                            f"Failed after {max_retries} retries. Last error: {str(e)}"
                        ) from e
                    
                    delay = min(
                        initial_delay * (backoff_factor ** attempt),
                        max_delay
                    )
                    
                    # Add jitter to prevent thundering herd
                    import random
                    delay *= (0.5 + random.random() * 0.5)
                    
                    logger.warning(
                        f"Function {func.__name__} failed on attempt {attempt + 1}. "
                        f"Retrying in {delay:.2f} seconds. Error: {str(e)}"
                    )
                    
                    time.sleep(delay)
            
            # This should never be reached
            raise RetryExhaustedError("Retry logic failed unexpectedly")
        
        return wrapper
    return decorator


def retry_async(
    max_retries: Optional[int] = None,
    initial_delay: Optional[float] = None,
    max_delay: Optional[float] = None,
    backoff_factor: Optional[float] = None,
    exceptions: Optional[Tuple[Type[Exception], ...]] = None,
    logger: Optional[logging.Logger] = None
):
    """
    Async decorator for retry with exponential backoff.
    
    Args:
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Multiplier for delay after each retry
        exceptions: Tuple of exception types to retry on
        logger: Logger instance for retry messages
        
    Returns:
        Decorated async function
    """
    import asyncio
    
    config = get_config()
    
    if max_retries is None:
        max_retries = config.ai.max_retries
    
    if initial_delay is None:
        initial_delay = 1.0
    
    if max_delay is None:
        max_delay = 60.0
    
    if backoff_factor is None:
        backoff_factor = config.pipeline.backoff_factor
    
    if exceptions is None:
        exceptions = (Exception,)
    
    if logger is None:
        logger = logging.getLogger(__name__)
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(
                            f"Async function {func.__name__} failed after {max_retries} retries. "
                            f"Last error: {str(e)}"
                        )
                        raise RetryExhaustedError(
                            f"Failed after {max_retries} retries. Last error: {str(e)}"
                        ) from e
                    
                    delay = min(
                        initial_delay * (backoff_factor ** attempt),
                        max_delay
                    )
                    
                    # Add jitter
                    import random
                    delay *= (0.5 + random.random() * 0.5)
                    
                    logger.warning(
                        f"Async function {func.__name__} failed on attempt {attempt + 1}. "
                        f"Retrying in {delay:.2f} seconds. Error: {str(e)}"
                    )
                    
                    await asyncio.sleep(delay)
            
            raise RetryExhaustedError("Retry logic failed unexpectedly")
        
        return wrapper
    return decorator


class RetryContext:
    """Context manager for retry operations."""
    
    def __init__(self, **kwargs):
        self.config = RetryConfig(**kwargs)
        self.attempt = 0
        self.last_exception = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            return True
        
        if not issubclass(exc_type, self.config.exceptions):
            return False
        
        self.last_exception = exc_val
        self.attempt += 1
        
        if self.attempt > self.config.max_retries:
            logger.error(
                f"Operation failed after {self.config.max_retries} retries. "
                f"Last error: {str(exc_val)}"
            )
            return False
        
        delay = min(
            self.config.initial_delay * (self.config.backoff_factor ** (self.attempt - 1)),
            self.config.max_delay
        )
        
        if self.config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        logger.warning(
            f"Operation failed on attempt {self.attempt}. "
            f"Retrying in {delay:.2f} seconds. Error: {str(exc_val)}"
        )
        
        time.sleep(delay)
        return True  # Retry


# Example usage functions
def with_retry(func: Callable, *args, max_retries: int = 3, **kwargs) -> Any:
    """
    Execute function with retry logic.
    
    Args:
        func: Function to execute
        *args: Function arguments
        max_retries: Maximum retry attempts
        **kwargs: Function keyword arguments
        
    Returns:
        Function result
    """
    return retry_with_backoff(max_retries=max_retries)(func)(*args, **kwargs)