
"""
Validation Utilities
====================

Input validation and data sanitization utilities.
"""

import re
from datetime import datetime, timezone
from typing import Optional, Tuple, List
from .exceptions import ValidationError


def validate_symbol(symbol: str) -> str:
    """
    Validate and normalize a stock symbol.
    
    Args:
        symbol: Stock symbol to validate
        
    Returns:
        Normalized symbol
        
    Raises:
        ValidationError: If symbol is invalid
    """
    if not symbol:
        raise ValidationError("Symbol cannot be empty")
    
    # Remove whitespace and convert to uppercase
    symbol = symbol.strip().upper()
    
    # Check length limits
    if len(symbol) < 1 or len(symbol) > 10:
        raise ValidationError("Symbol length must be between 1 and 10 characters")
    
    # Validate character set (alphanumeric and common symbols)
    if not re.match(r'^[A-Z0-9\-\.]+$', symbol):
        raise ValidationError(
            "Symbol contains invalid characters. Only letters, numbers, hyphens, and dots are allowed"
        )
    
    # Check against known invalid patterns
    invalid_patterns = [
        r'^\.+$',  # Only dots
        r'^-+',    # Starts with hyphen
        r'-+$',    # Ends with hyphen
        r'\.{2,}', # Multiple consecutive dots
        r'-{2,}',  # Multiple consecutive hyphens
    ]
    
    for pattern in invalid_patterns:
        if re.search(pattern, symbol):
            raise ValidationError(f"Symbol has invalid format: {symbol}")
    
    return symbol


def validate_date_range(
    start_date: str,
    end_date: str,
    max_days: Optional[int] = None,
    min_days: Optional[int] = None,
    allow_future: bool = False
) -> Tuple[datetime, datetime]:
    """
    Validate and parse date range.
    
    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        max_days: Maximum allowed days in range
        min_days: Minimum required days in range
        allow_future: Whether to allow future dates
        
    Returns:
        Tuple of (start_datetime, end_datetime)
        
    Raises:
        ValidationError: If date range is invalid
    """
    try:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    except ValueError as e:
        raise ValidationError(f"Invalid date format. Use YYYY-MM-DD: {e}")
    
    # Ensure dates are timezone-aware
    start_dt = start_dt.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=timezone.utc)
    end_dt = end_dt.replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.utc)
    
    # Validate order
    if start_dt > end_dt:
        raise ValidationError("Start date must be before end date")
    
    # Validate future dates
    if not allow_future:
        now = datetime.now(timezone.utc)
        if start_dt > now:
            raise ValidationError("Start date cannot be in the future")
        if end_dt > now:
            raise ValidationError("End date cannot be in the future")
    
    # Validate range duration
    days_diff = (end_dt - start_dt).days + 1
    
    if min_days is not None and days_diff < min_days:
        raise ValidationError(f"Date range must be at least {min_days} days")
    
    if max_days is not None and days_diff > max_days:
        raise ValidationError(f"Date range cannot exceed {max_days} days")
    
    return start_dt, end_dt


def validate_ticker_list(tickers: List[str]) -> List[str]:
    """
    Validate and normalize a list of tickers.
    
    Args:
        tickers: List of ticker symbols
        
    Returns:
        List of normalized valid symbols
        
    Raises:
        ValidationError: If any ticker is invalid
    """
    if not isinstance(tickers, list):
        raise ValidationError("Tickers must be provided as a list")
    
    if not tickers:
        raise ValidationError("Ticker list cannot be empty")
    
    validated_tickers = []
    seen = set()
    
    for ticker in tickers:
        if not isinstance(ticker, str):
            raise ValidationError(f"Ticker must be a string, got {type(ticker).__name__}")
        
        normalized = validate_symbol(ticker)
        
        if normalized in seen:
            raise ValidationError(f"Duplicate ticker: {normalized}")
        
        seen.add(normalized)
        validated_tickers.append(normalized)
    
    return validated_tickers


def validate_positive_integer(value: int, name: str, allow_zero: bool = False) -> int:
    """
    Validate positive integer.
    
    Args:
        value: Value to validate
        name: Name for error messages
        allow_zero: Whether to allow zero
        
    Returns:
        Validated integer
        
    Raises:
        ValidationError: If value is invalid
    """
    if not isinstance(value, int):
        raise ValidationError(f"{name} must be an integer")
    
    if allow_zero:
        if value < 0:
            raise ValidationError(f"{name} must be non-negative")
    else:
        if value <= 0:
            raise ValidationError(f"{name} must be positive")
    
    return value


def validate_float_range(
    value: float,
    name: str,
    min_val: Optional[float] = None,
    max_val: Optional[float] = None
) -> float:
    """
    Validate float within range.
    
    Args:
        value: Value to validate
        name: Name for error messages
        min_val: Minimum allowed value
        max_val: Maximum allowed value
        
    Returns:
        Validated float
        
    Raises:
        ValidationError: If value is invalid
    """
    if not isinstance(value, (int, float)):
        raise ValidationError(f"{name} must be a number")
    
    value = float(value)
    
    if min_val is not None and value < min_val:
        raise ValidationError(f"{name} must be >= {min_val}")
    
    if max_val is not None and value > max_val:
        raise ValidationError(f"{name} must be <= {max_val}")
    
    return value


def validate_email(email: str) -> str:
    """
    Validate email address.
    
    Args:
        email: Email address to validate
        
    Returns:
        Normalized email address
        
    Raises:
        ValidationError: If email is invalid
    """
    if not email:
        raise ValidationError("Email cannot be empty")
    
    email = email.strip().lower()
    
    # Basic email pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    if not re.match(pattern, email):
        raise ValidationError("Invalid email format")
    
    return email


def validate_url(url: str, allowed_schemes: Optional[List[str]] = None) -> str:
    """
    Validate URL.
    
    Args:
        url: URL to validate
        allowed_schemes: List of allowed URL schemes
        
    Returns:
        Validated URL
        
    Raises:
        ValidationError: If URL is invalid
    """
    if not url:
        raise ValidationError("URL cannot be empty")
    
    url = url.strip()
    
    # Basic URL pattern
    pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    
    if not re.match(pattern, url):
        raise ValidationError("Invalid URL format")
    
    if allowed_schemes:
        scheme = url.split('://')[0].lower()
        if scheme not in [s.lower() for s in allowed_schemes]:
            raise ValidationError(f"URL scheme must be one of: {allowed_schemes}")
    
    return url


def sanitize_string(value: str, max_length: Optional[int] = None) -> str:
    """
    Sanitize string input.
    
    Args:
        value: String to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized string
        
    Raises:
        ValidationError: If string is invalid
    """
    if not isinstance(value, str):
        raise ValidationError("Input must be a string")
    
    # Remove leading/trailing whitespace
    value = value.strip()
    
    # Remove control characters
    value = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', value)
    
    # Limit length
    if max_length is not None and len(value) > max_length:
        value = value[:max_length].rstrip()
    
    return value


def validate_json(data: str) -> dict:
    """
    Validate and parse JSON string.
    
    Args:
        data: JSON string to validate
        
    Returns:
        Parsed JSON data
        
    Raises:
        ValidationError: If JSON is invalid
    """
    import json
    
    if not isinstance(data, str):
        raise ValidationError("JSON data must be a string")
    
    try:
        return json.loads(data)
    except json.JSONDecodeError as e:
        raise ValidationError(f"Invalid JSON: {e}")


def validate_choice(value: str, choices: List[str], case_sensitive: bool = True) -> str:
    """
    Validate choice from predefined list.
    
    Args:
        value: Value to validate
        choices: List of valid choices
        case_sensitive: Whether comparison is case-sensitive
        
    Returns:
        Validated choice
        
    Raises:
        ValidationError: If choice is invalid
    """
    if not choices:
        raise ValidationError("Choices list cannot be empty")
    
    if not isinstance(value, str):
        raise ValidationError("Choice must be a string")
    
