"""
Data Models for AI Chat Processor
================================

Comprehensive Pydantic models for data validation and serialization
across the AI chat processor system.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
import pandas as pd


class PriceData(BaseModel):
    """Individual price data point."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: Optional[int] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ChartData(BaseModel):
    """Chart data for visualization."""
    symbol: str
    timeframe: str
    data: List[PriceData]
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    
    @validator('timeframe')
    def validate_timeframe(cls, v):
        valid_timeframes = ['1d', '1wk', '1mo', '5m', '15m', '1h']
        if v not in valid_timeframes:
            raise ValueError(f"Invalid timeframe: {v}")
        return v


class IndicatorValue(BaseModel):
    """Individual technical indicator value."""
    name: str
    value: float
    signal: Optional[str] = None
    description: Optional[str] = None


class SupportResistanceLevels(BaseModel):
    """Support and resistance levels for a symbol."""
    support_levels: List[float] = Field(default_factory=list)
    resistance_levels: List[float] = Field(default_factory=list)
    pivot_points: Dict[str, float] = Field(default_factory=dict)
    
    def add_support(self, level: float) -> None:
        """Add a support level if it's valid."""
        if level > 0 and level not in self.support_levels:
            self.support_levels.append(level)
            self.support_levels.sort()
    
    def add_resistance(self, level: float) -> None:
        """Add a resistance level if it's valid."""
        if level > 0 and level not in self.resistance_levels:
            self.resistance_levels.append(level)
            self.resistance_levels.sort()


class TechnicalIndicators(BaseModel):
    """Complete technical analysis results."""
    symbol: str
    rsi: Optional[IndicatorValue] = None
    macd: Optional[IndicatorValue] = None
    sma_20: Optional[IndicatorValue] = None
    sma_50: Optional[IndicatorValue] = None
    ema_12: Optional[IndicatorValue] = None
    ema_26: Optional[IndicatorValue] = None
    bollinger_bands: Optional[Dict[str, float]] = None
    volume_analysis: Optional[IndicatorValue] = None
    levels: SupportResistanceLevels = Field(default_factory=SupportResistanceLevels)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all indicators."""
        return {
            "symbol": self.symbol,
            "rsi": self.rsi.dict() if self.rsi else None,
            "macd": self.macd.dict() if self.macd else None,
            "signals": [ind.signal for ind in [self.rsi, self.macd, self.volume_analysis] 
                       if ind and ind.signal],
            "support_levels": self.levels.support_levels,
            "resistance_levels": self.levels.resistance_levels
        }


class SymbolInfo(BaseModel):
    """Basic symbol information."""
    symbol: str
    name: str
    exchange: Optional[str] = None
    sector: Optional[str] = None
    industry: Optional[str] = None
    market_cap: Optional[float] = None
    beta: Optional[float] = None
    dividend_yield: Optional[float] = None


class MarketDataResponse(BaseModel):
    """Complete market data response."""
    symbol: str
    current_price: Optional[float] = None
    price_change: Optional[float] = None
    price_change_percent: Optional[float] = None
    volume: Optional[int] = None
    market_cap: Optional[float] = None
    day_high: Optional[float] = None
    day_low: Optional[float] = None
    fifty_two_week_high: Optional[float] = None
    fifty_two_week_low: Optional[float] = None
    pe_ratio: Optional[float] = None
    eps: Optional[float] = None
    symbol_info: Optional[SymbolInfo] = None
    chart_data: Optional[ChartData] = None
    technical_indicators: Optional[TechnicalIndicators] = None
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    source: str = Field(default="unknown")
    
    def get_price_summary(self) -> Dict[str, Any]:
        """Get price-related summary."""
        return {
            "symbol": self.symbol,
            "current_price": self.current_price,
            "price_change": self.price_change,
            "price_change_percent": self.price_change_percent,
            "volume": self.volume,
            "day_range": {
                "high": self.day_high,
                "low": self.day_low
            } if self.day_high and self.day_low else None
        }


class AIChatRequest(BaseModel):
    """Request structure for AI chat processing."""
    query: str = Field(..., min_length=1, max_length=1000)
    symbols: List[str] = Field(default_factory=list)
    context: Optional[Dict[str, Any]] = None
    include_technical: bool = Field(default=True)
    include_chart: bool = Field(default=False)
    timeframe: str = Field(default="1d")
    
    @validator('symbols')
    def validate_symbols(cls, v):
        """Validate symbols list."""
        if v:
            for symbol in v:
                if not symbol or not symbol.strip():
                    raise ValueError("Empty symbol in symbols list")
                if not symbol.strip().isalpha():
                    raise ValueError(f"Invalid symbol format: {symbol}")
        return [s.upper().strip() for s in v]
    
    @validator('timeframe')
    def validate_timeframe(cls, v):
        """Validate timeframe."""
        valid_timeframes = ['1d', '1wk', '1mo', '5m', '15m', '1h', '4h']
        if v not in valid_timeframes:
            raise ValueError(f"Invalid timeframe: {v}")
        return v


class AIChatResponse(BaseModel):
    """Response structure for AI chat processing."""
    query: str
    response: str
    symbols: List[str]
    market_data: Dict[str, MarketDataResponse] = Field(default_factory=dict)
    technical_analysis: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    recommendations: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    errors: List[str] = Field(default_factory=list)
    processing_time: float = Field(default=0.0)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    def add_market_data(self, symbol: str, data: MarketDataResponse) -> None:
        """Add market data for a symbol."""
        self.market_data[symbol] = data
    
    def add_technical_analysis(self, symbol: str, analysis: Dict[str, Any]) -> None:
        """Add technical analysis for a symbol."""
        self.technical_analysis[symbol] = analysis
    
    def add_recommendation(self, recommendation: str) -> None:
        """Add a recommendation."""
        if recommendation and recommendation not in self.recommendations:
            self.recommendations.append(recommendation)
    
    def add_warning(self, warning: str) -> None:
        """Add a warning."""
        if warning and warning not in self.warnings:
            self.warnings.append(warning)
    
    def add_error(self, error: str) -> None:
        """Add an error."""
        if error and error not in self.errors:
            self.errors.append(error)


class ErrorResponse(BaseModel):
    """Error response structure."""
    error: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CacheKey(BaseModel):
    """Cache key structure for data caching."""
    symbol: str
    timeframe: str
    indicators: List[str] = Field(default_factory=list)
    include_chart: bool = False
    
    def to_string(self) -> str:
        """Convert to cache key string."""
        indicators_str = ",".join(sorted(self.indicators)) if self.indicators else "all"
        return f"{self.symbol}:{self.timeframe}:{indicators_str}:{self.include_chart}"
    
    class Config:
        frozen = True


class ServiceConfig(BaseModel):
    """Configuration for services."""
    timeout_seconds: int = Field(default=30, ge=1, le=300)
    max_retries: int = Field(default=3, ge=0, le=10)
    retry_delay: float = Field(default=1.0, ge=0.1, le=10.0)
    cache_ttl: int = Field(default=300, ge=60, le=3600)
    enable_caching: bool = Field(default=True)
    log_level: str = Field(default="INFO")
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}")
        return v.upper()