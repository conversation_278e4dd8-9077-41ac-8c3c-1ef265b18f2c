# Ask Command Pipeline Configuration
# Integrated with TradingView Webhooks and Supabase Database

pipeline:
  name: "ask"
  version: "2.0.0"
  description: "Multi-stage pipeline for AI-powered trading insights with TradingView integration"
  max_execution_time: ${ASK_PIPELINE_TIMEOUT:-30.0}
  parallel_execution: ${PIPELINE_PARALLEL_EXECUTION:-false}

stages:
  query_classifier:
    timeout: 2.0
    retry_attempts: 2
    critical: false
    quality_threshold: 0.7
    
  data_collection:
    timeout: 15.0
    retry_attempts: 3
    critical: true
    fallback_sources: ["yahoo_finance"]
    quality_threshold: 0.7
    use_tradingview_webhooks: true
    
  data_validation:
    timeout: 3.0
    retry_attempts: 2
    critical: true
    quality_threshold: 0.7
    
  ai_processing:
    timeout: 10.0
    retry_attempts: 2
    critical: false
    quality_threshold: 0.7
    
  response_formatting:
    timeout: 5.0
    retry_attempts: 1
    critical: false
    quality_threshold: 0.5

data_sources:
  priority_order:
    - "tradingview_webhooks"
    - "polygon"
    - "finnhub"
    - "yahoo_finance"
  
  # Use environment variables for rate limits
  rate_limits:
    polygon: ${POLYGON_RATE_LIMIT:-10}
    finnhub: ${FINNHUB_RATE_LIMIT:-50}
    alpha_vantage: ${ALPHA_VANTAGE_RATE_LIMIT:-10}
    yahoo_finance: ${YAHOO_FINANCE_RATE_LIMIT:-20}
  
  # Use environment variables for timeouts
  timeouts:
    polygon: ${POLYGON_TIMEOUT:-10.0}
    finnhub: ${FINNHUB_TIMEOUT:-5.0}
    alpha_vantage: ${ALPHA_VANTAGE_TIMEOUT:-10.0}
    yahoo_finance: ${YAHOO_FINANCE_TIMEOUT:-5.0}

  # API keys from environment variables
  api_keys:
    polygon: ${POLYGON_API_KEY}
    finnhub: ${FINNHUB_API_KEY}
    alpha_vantage: ${ALPHA_VANTAGE_API_KEY}

  # Enable/disable providers from environment variables
  enabled_providers:
    polygon: ${POLYGON_ENABLED:-true}
    finnhub: ${FINNHUB_ENABLED:-true}
    alpha_vantage: ${ALPHA_VANTAGE_ENABLED:-false}
    yahoo_finance: ${YAHOO_FINANCE_ENABLED:-true}

quality_thresholds:
  excellent: 0.9
  good: 0.7
  fair: 0.5
  poor: 0.3
  unreliable: 0.0

# Use OpenRouter configuration from environment variables
ai_models:
  primary: ${OPENROUTER_GENERAL_MODEL:-"openrouter/sonoma-sky-alpha"}
  fallback: ${MODEL_GLOBAL_FALLBACK:-"moonshotai/kimi-k2:free"}
  max_tokens: ${OPENROUTER_GENERAL_MODEL_MAX_TOKENS:-2000}
  temperature: ${OPENROUTER_GENERAL_MODEL_TEMPERATURE:-0.7}
  api_key: ${OPENROUTER_API_KEY}
  base_url: ${OPENROUTER_BASE_URL:-"https://openrouter.ai/api/v1"}

# Database configuration
database:
  supabase_url: ${SUPABASE_URL}
  supabase_key: ${SUPABASE_KEY}
  supabase_anon_key: ${SUPABASE_ANON_KEY}
  database_url: ${DATABASE_URL}
  pool_size: ${DB_POOL_SIZE:-5}

# Redis configuration
redis:
  enabled: ${REDIS_ENABLED:-true}
  host: ${REDIS_HOST:-"redis"}
  port: ${REDIS_PORT:-6379}
  password: ${REDIS_PASSWORD}
  db: ${REDIS_DB:-0}

monitoring:
  enable_audit_trail: ${PIPELINE_ENABLE_AUDIT_TRAIL:-true}
  log_performance_metrics: ${PIPELINE_ENABLE_METRICS:-true}
  track_data_quality: true
  alert_on_failures: true
  log_level: ${LOG_LEVEL:-"INFO"}