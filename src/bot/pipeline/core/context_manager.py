"""
Pipeline Context Manager

Manages data flow and state between pipeline stages for Discord bot commands.
Each command gets its own context instance that flows through the pipeline.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum
import uuid


class PipelineStatus(Enum):
    """Pipeline execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DataQuality(Enum):
    """Data quality levels"""
    EXCELLENT = "excellent"      # 90-100%
    GOOD = "good"               # 70-89%
    FAIR = "fair"               # 50-69%
    POOR = "poor"               # 30-49%
    UNRELIABLE = "unreliable"   # 0-29%


@dataclass
class QualityScore:
    """Data quality scoring"""
    overall_score: float = 0.0
    freshness_score: float = 0.0
    consistency_score: float = 0.0
    completeness_score: float = 0.0
    source_reliability: float = 0.0
    
    @property
    def quality_level(self) -> DataQuality:
        """Convert score to quality level"""
        if self.overall_score >= 90:
            return DataQuality.EXCELLENT
        elif self.overall_score >= 70:
            return DataQuality.GOOD
        elif self.overall_score >= 50:
            return DataQuality.FAIR
        elif self.overall_score >= 30:
            return DataQuality.POOR
        else:
            return DataQuality.UNRELIABLE


@dataclass
class AuditEntry:
    """Audit trail entry for pipeline execution"""
    timestamp: datetime
    stage: str
    action: str
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    execution_time: float
    success: bool
    error_details: Optional[str] = None
    resource_usage: Dict[str, Any] = field(default_factory=dict)
    confidence_score: float = 0.0
    circuit_breaker_metrics: Optional[Dict[str, Any]] = None


@dataclass
class PipelineContext:
    """
    Main pipeline context that flows through all stages
    
    This is the central data container that gets passed between
    pipeline stages, accumulating data and results as it goes.
    """
    
    # Core identification
    pipeline_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    correlation_id: Optional[str] = None  # Correlation ID for request tracing
    command_name: str = ""  # e.g., "ask", "analyze", "watchlist"
    user_id: Optional[str] = None
    guild_id: Optional[str] = None
    ticker: Optional[str] = None
    
    # Circuit breaker metrics
    circuit_breaker_metrics: Optional[Dict[str, Any]] = None
    
    # Query information
    original_query: str = ""
    query_metadata: Dict[str, Any] = field(default_factory=dict)
    query_complexity: int = 1  # 1-10 scale
    query_type: str = ""
    strict_mode: bool = False
    
    # Data collection
    collected_data: Dict[str, Any] = field(default_factory=dict)
    data_sources: List[str] = field(default_factory=list)
    data_timestamps: Dict[str, datetime] = field(default_factory=dict)
    
    # Validation results
    validated_data: Dict[str, Any] = field(default_factory=dict)
    quality_scores: Dict[str, QualityScore] = field(default_factory=dict)
    validation_errors: List[str] = field(default_factory=list)
    
    # Processing results
    processing_results: Dict[str, Any] = field(default_factory=dict)
    analysis_results: Dict[str, Any] = field(default_factory=dict)
    
    # AI responses
    ai_responses: Dict[str, Any] = field(default_factory=dict)
    ai_confidence: float = 0.0
    
    # Pipeline state
    current_stage: str = ""
    stage_history: List[str] = field(default_factory=list)
    status: PipelineStatus = PipelineStatus.PENDING
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    # Performance metrics
    stage_timings: Dict[str, float] = field(default_factory=dict)
    total_execution_time: float = 0.0
    resource_usage: Dict[str, Any] = field(default_factory=dict)
    
    # Audit and monitoring
    audit_trail: List[AuditEntry] = field(default_factory=list)
    error_log: List[Dict[str, Any]] = field(default_factory=list)
    
    # Configuration
    config: Dict[str, Any] = field(default_factory=dict)
    max_execution_time: float = 30.0  # seconds
    should_continue: bool = True
    
    def __post_init__(self):
        """Initialize default values"""
        if not self.start_time:
            self.start_time = datetime.now()
    
    def add_audit_entry(self, stage: str, action: str, input_data: Dict, 
                       output_data: Dict, execution_time: float, success: bool,
                       error_details: Optional[str] = None, confidence: float = 0.0,
                       circuit_breaker_metrics: Optional[Dict[str, Any]] = None):
        """Add an audit trail entry"""
        entry = AuditEntry(
            timestamp=datetime.now(),
            stage=stage,
            action=action,
            input_data=input_data,
            output_data=output_data,
            execution_time=execution_time,
            success=success,
            error_details=error_details,
            confidence_score=confidence,
            circuit_breaker_metrics=circuit_breaker_metrics
        )
        self.audit_trail.append(entry)
    
    def add_error(self, stage: str, error: Exception, details: str = ""):
        """Add an error to the error log"""
        error_entry = {
            "timestamp": datetime.now(),
            "stage": stage,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "details": details
        }
        self.error_log.append(error_entry)
    
    def update_stage(self, stage_name: str, execution_time: float):
        """Update current stage and timing"""
        self.current_stage = stage_name
        self.stage_history.append(stage_name)
        self.stage_timings[stage_name] = execution_time
    
    def set_quality_score(self, data_type: str, score: QualityScore):
        """Set quality score for a specific data type"""
        self.quality_scores[data_type] = score

    def get(self, key: str, default: Any = None) -> Any:
        """Dict-like accessor for backward compatibility.

        Looks up `key` first as an attribute, then in common internal
        dict containers (processing_results, collected_data, validated_data,
        ai_responses, query_metadata, analysis_results, config). If not found,
        searches nested dicts inside those containers before returning the
        provided default.
        """
        # Direct attribute access
        if hasattr(self, key):
            try:
                return getattr(self, key)
            except Exception:
                return default

        # Common top-level dict containers to check
        containers = (
            self.processing_results,
            self.collected_data,
            self.validated_data,
            self.ai_responses,
            self.query_metadata,
            self.analysis_results,
            self.config,
        )

        for container in containers:
            if isinstance(container, dict) and key in container:
                return container.get(key, default)

        # Fallback: search nested dicts inside common containers
        for container in (self.processing_results, self.collected_data, self.validated_data):
            if isinstance(container, dict):
                for value in container.values():
                    if isinstance(value, dict) and key in value:
                        return value.get(key, default)

        return default
    
    def get_overall_quality(self) -> QualityScore:
        """Calculate overall quality score across all data types"""
        if not self.quality_scores:
            return QualityScore()
        
        scores = list(self.quality_scores.values())
        overall = QualityScore(
            overall_score=sum(s.overall_score for s in scores) / len(scores),
            freshness_score=sum(s.freshness_score for s in scores) / len(scores),
            consistency_score=sum(s.consistency_score for s in scores) / len(scores),
            completeness_score=sum(s.completeness_score for s in scores) / len(scores),
            source_reliability=sum(s.source_reliability for s in scores) / len(scores)
        )
        return overall
    
    def can_continue(self) -> bool:
        """Check if pipeline should continue execution"""
        if not self.should_continue:
            return False
        
        # Check execution time limit
        if self.total_execution_time > self.max_execution_time:
            self.add_error("pipeline", Exception("Execution time limit exceeded"), 
                          f"Exceeded {self.max_execution_time}s limit")
            return False
        
        # Check for critical errors
        critical_errors = [e for e in self.error_log if "critical" in e.get("details", "")]
        if critical_errors:
            return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary for serialization"""
        result = {
            "pipeline_id": self.pipeline_id,
            "command_name": self.command_name,
            "original_query": self.original_query,
            "status": self.status.value,
            "current_stage": self.current_stage,
            "quality_scores": {k: v.__dict__ for k, v in self.quality_scores.items()},
            "total_execution_time": self.total_execution_time,
            "error_count": len(self.error_log),
            "audit_entries": len(self.audit_trail)
        }
        
        # Add circuit breaker metrics if available
        if self.circuit_breaker_metrics:
            result["circuit_breaker_metrics"] = self.circuit_breaker_metrics
            
        return result
    
    def finalize(self):
        """Finalize the pipeline execution"""
        self.end_time = datetime.now()
        if self.start_time:
            self.total_execution_time = (self.end_time - self.start_time).total_seconds()
        
        if self.error_log:
            self.status = PipelineStatus.FAILED
        else:
            self.status = PipelineStatus.COMPLETED 