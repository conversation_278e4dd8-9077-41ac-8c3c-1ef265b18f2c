"""
Parallel Pipeline Engine

Enhanced pipeline execution engine that supports advanced parallel execution
of stages with dependency tracking for optimal performance.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Set, Tuple
import logging
from dataclasses import dataclass, field

from .pipeline_engine import BasePipelineStage, StageResult, PipelineEngine
from .context_manager import PipelineContext, PipelineStatus

logger = logging.getLogger(__name__)


@dataclass
class StageDependency:
    """Represents a stage's dependencies and dependents"""
    name: str
    stage: BasePipelineStage
    depends_on: Set[str] = field(default_factory=set)
    dependents: Set[str] = field(default_factory=set)
    completed: bool = False
    result: Optional[StageResult] = None


class ParallelPipelineEngine(PipelineEngine):
    """
    Enhanced pipeline execution engine with advanced parallel execution
    
    Features:
    - Automatic dependency resolution based on inputs/outputs
    - Parallel execution of independent stages
    - Dynamic execution plan based on stage dependencies
    - Optimized resource utilization
    """
    
    def __init__(self, name: str, stages: List[BasePipelineStage] = None):
        """Initialize the parallel pipeline engine"""
        super().__init__(name, stages)
        self.parallel_execution = True
        self.dependency_graph: Dict[str, StageDependency] = {}
        self.execution_plan: List[List[str]] = []
        self.semaphore = asyncio.Semaphore(5)  # Limit concurrent stages
        
    def add_stage(self, stage: BasePipelineStage):
        """Add a stage to the pipeline with dependency tracking"""
        super().add_stage(stage)
        self._update_dependency_graph()
        
    def add_stages(self, stages: List[BasePipelineStage]):
        """Add multiple stages to the pipeline with dependency tracking"""
        super().add_stages(stages)
        self._update_dependency_graph()
    
    def _update_dependency_graph(self):
        """Update the dependency graph based on current stages"""
        # Reset the graph
        self.dependency_graph = {}
        
        # Create nodes for all stages
        for stage in self.stages:
            self.dependency_graph[stage.name] = StageDependency(
                name=stage.name,
                stage=stage,
                depends_on=set(),
                dependents=set()
            )
        
        # Build dependencies based on inputs/outputs
        output_providers = {}  # Maps output key to stage name
        
        # First pass: collect all outputs
        for stage in self.stages:
            for output in stage.outputs:
                output_providers[output] = stage.name
        
        # Second pass: establish dependencies
        for stage in self.stages:
            for input_key in stage.required_inputs:
                if input_key in output_providers and output_providers[input_key] != stage.name:
                    provider = output_providers[input_key]
                    # This stage depends on the provider
                    self.dependency_graph[stage.name].depends_on.add(provider)
                    # The provider has this stage as a dependent
                    self.dependency_graph[provider].dependents.add(stage.name)
        
        # Generate execution plan
        self._generate_execution_plan()
        
    def _generate_execution_plan(self):
        """Generate an optimized execution plan based on dependencies"""
        # Reset the plan
        self.execution_plan = []
        
        # Copy dependency graph for processing
        remaining_stages = {name: dep.depends_on.copy() for name, dep in self.dependency_graph.items()}
        
        # Process until all stages are scheduled
        while remaining_stages:
            # Find stages with no dependencies
            ready_stages = [name for name, deps in remaining_stages.items() if not deps]
            
            if not ready_stages:
                # Circular dependency detected
                logger.error(f"Circular dependency detected in pipeline {self.name}")
                # Break the cycle by selecting the first remaining stage
                ready_stages = [next(iter(remaining_stages.keys()))]
            
            # Add this batch to the execution plan
            self.execution_plan.append(ready_stages)
            
            # Remove scheduled stages from remaining
            for stage_name in ready_stages:
                # Remove this stage from remaining
                del remaining_stages[stage_name]
                
                # Remove this stage from dependencies of other stages
                for deps in remaining_stages.values():
                    deps.discard(stage_name)
        
        logger.info(f"Generated execution plan for pipeline {self.name}: {self.execution_plan}")
    
    async def execute(self, context: PipelineContext) -> PipelineContext:
        """Execute the complete pipeline with optimized parallelization"""
        if not self.stages:
            logger.warning(f"Pipeline {self.name}: No stages configured")
            return context
        
        context.command_name = self.name
        context.status = PipelineStatus.RUNNING
        context.max_execution_time = self.max_execution_time
        
        logger.info(f"Starting parallel pipeline {self.name} with {len(self.stages)} stages")
        
        # Reset stage completion status
        for dep in self.dependency_graph.values():
            dep.completed = False
            dep.result = None
        
        # Execute the pipeline with optimized parallelization
        try:
            await self._execute_optimized(context)
            context.status = PipelineStatus.COMPLETED
        except Exception as e:
            logger.error(f"Pipeline {self.name} failed: {str(e)}", exc_info=True)
            context.add_error("pipeline", e, "Pipeline execution failed")
            context.status = PipelineStatus.FAILED
        
        # Finalize pipeline
        context.finalize()
        
        logger.info(f"Pipeline {self.name} completed with status: {context.status.value}")
        return context
    
    async def _execute_optimized(self, context: PipelineContext):
        """Execute stages according to the optimized execution plan"""
        # Execute each batch in the plan
        for batch_index, batch in enumerate(self.execution_plan):
            logger.info(f"Executing batch {batch_index + 1}/{len(self.execution_plan)}: {batch}")
            
            # Create tasks for all stages in this batch
            tasks = []
            for stage_name in batch:
                stage = self.dependency_graph[stage_name].stage
                task = asyncio.create_task(self._execute_stage_with_semaphore(stage, context))
                tasks.append((stage_name, task))
            
            # Wait for all tasks in this batch to complete
            for stage_name, task in tasks:
                try:
                    result = await task
                    self.dependency_graph[stage_name].completed = True
                    self.dependency_graph[stage_name].result = result
                    
                    # Merge stage outputs into context for next stages to use
                    if result.success and result.output_data:
                        context.processing_results.update(result.output_data)
                        logger.debug(f"Stage {stage_name} outputs merged: {list(result.output_data.keys())}")
                except Exception as e:
                    logger.error(f"Stage {stage_name} failed: {str(e)}", exc_info=True)
                    context.add_error(stage_name, e, "Stage execution failed")
                    # Mark as completed but failed
                    self.dependency_graph[stage_name].completed = True
                    self.dependency_graph[stage_name].result = StageResult(
                        success=False,
                        output_data={},
                        execution_time=0.0,
                        error_message=str(e)
                    )
    
    async def _execute_stage_with_semaphore(self, stage: BasePipelineStage, context: PipelineContext) -> StageResult:
        """Execute a stage with semaphore to limit concurrency"""
        async with self.semaphore:
            return await stage.execute(context)


class FlexiblePipelineBuilder:
    """Builder pattern for creating flexible pipelines with dependency tracking"""
    
    def __init__(self, name: str):
        self.pipeline = ParallelPipelineEngine(name)
    
    def add_stage(self, stage: BasePipelineStage) -> 'FlexiblePipelineBuilder':
        """Add a stage to the pipeline"""
        self.pipeline.add_stage(stage)
        return self
    
    def add_stages(self, stages: List[BasePipelineStage]) -> 'FlexiblePipelineBuilder':
        """Add multiple stages to the pipeline"""
        self.pipeline.add_stages(stages)
        return self
    
    def set_config(self, config: Dict[str, Any]) -> 'FlexiblePipelineBuilder':
        """Set pipeline configuration"""
        self.pipeline.config = config
        return self
    
    def set_max_execution_time(self, max_time: float) -> 'FlexiblePipelineBuilder':
        """Set maximum execution time"""
        self.pipeline.max_execution_time = max_time
        self.pipeline.circuit_breaker_config.timeout = max_time
        return self
    
    def set_concurrency_limit(self, limit: int) -> 'FlexiblePipelineBuilder':
        """Set the maximum number of concurrent stages"""
        self.pipeline.semaphore = asyncio.Semaphore(limit)
        return self
    
    def configure_circuit_breaker(self, 
                                enabled: bool = True,
                                failure_threshold: int = 5,
                                recovery_timeout: float = 120.0) -> 'FlexiblePipelineBuilder':
        """Configure circuit breaker for the pipeline"""
        self.pipeline.use_circuit_breaker = enabled
        self.pipeline.circuit_breaker_config.failure_threshold = failure_threshold
        self.pipeline.circuit_breaker_config.recovery_timeout = recovery_timeout
        return self
    
    def build(self) -> ParallelPipelineEngine:
        """Build and return the configured pipeline"""
        return self.pipeline
