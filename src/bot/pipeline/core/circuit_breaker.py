"""
Circuit Breaker Pattern Implementation for Pipeline Data Flows

This module implements the circuit breaker pattern to prevent cascading failures
in the pipeline by detecting failures and preventing operation when the system
is not functioning correctly.
"""

import time
import logging
import asyncio
from enum import Enum
from typing import Callable, Dict, Any, Optional, TypeVar, Awaitable, Generic, Union

logger = logging.getLogger(__name__)

# Type variables for function signatures
T = TypeVar('T')
R = TypeVar('R')

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation, requests pass through
    OPEN = "open"          # Failing state, requests are blocked
    HALF_OPEN = "half_open"  # Testing state, limited requests allowed


class CircuitBreakerConfig:
    """Configuration for circuit breaker behavior"""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 30.0,
        half_open_max_calls: int = 1,
        timeout: float = 10.0,
        exclude_exceptions: list = None
    ):
        """
        Initialize circuit breaker configuration.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time in seconds before attempting recovery
            half_open_max_calls: Max calls allowed in half-open state
            timeout: Timeout for function calls in seconds
            exclude_exceptions: List of exception types to not count as failures
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_max_calls = half_open_max_calls
        self.timeout = timeout
        self.exclude_exceptions = exclude_exceptions or []


class CircuitBreaker(Generic[T, R]):
    """
    Circuit breaker implementation for protecting services and data flows.
    
    Implements the circuit breaker pattern to prevent cascading failures
    by failing fast when a service is having issues.
    """
    
    def __init__(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ):
        """
        Initialize circuit breaker.
        
        Args:
            name: Name of the protected resource/service
            config: Circuit breaker configuration
        """
        self.name = name
        self.config = config or CircuitBreakerConfig()
        
        # State management
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_failure_time = 0
        self._half_open_calls = 0
        
        # Metrics
        self._success_count = 0
        self._total_calls = 0
        self._last_success_time = 0
        self._last_call_duration = 0
        
        # Concurrency control
        self._state_lock = asyncio.Lock()
    
    @property
    def state(self) -> CircuitState:
        """Get current circuit state"""
        return self._state
    
    @property
    def is_closed(self) -> bool:
        """Check if circuit is closed (normal operation)"""
        return self._state == CircuitState.CLOSED
    
    @property
    def failure_rate(self) -> float:
        """Calculate failure rate as percentage"""
        if self._total_calls == 0:
            return 0.0
        return (self._failure_count / self._total_calls) * 100
    
    async def execute(self, func: Callable[..., Awaitable[R]], *args, **kwargs) -> R:
        """
        Execute a function with circuit breaker protection.
        
        Args:
            func: Async function to execute
            *args: Positional arguments for func
            **kwargs: Keyword arguments for func
            
        Returns:
            Result of the function call
            
        Raises:
            CircuitBreakerOpenError: If circuit is open
            asyncio.TimeoutError: If call times out
            Any exception raised by the function
        """
        self._total_calls += 1
        
        # Check circuit state
        await self._check_state_transition()
        
        if self._state == CircuitState.OPEN:
            logger.warning(f"Circuit {self.name} is OPEN - fast failing")
            self._record_failure(CircuitBreakerOpenError(f"Circuit {self.name} is open"))
            raise CircuitBreakerOpenError(f"Circuit {self.name} is open")
        
        # Half-open state rate limiting
        if self._state == CircuitState.HALF_OPEN:
            async with self._state_lock:
                if self._half_open_calls >= self.config.half_open_max_calls:
                    logger.info(f"Circuit {self.name} is HALF_OPEN with max calls reached - fast failing")
                    raise CircuitBreakerOpenError(f"Circuit {self.name} is half-open with max calls reached")
                self._half_open_calls += 1
        
        # Execute with timeout
        start_time = time.time()
        try:
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=self.config.timeout
            )
            
            # Record success
            self._record_success(time.time() - start_time)
            return result
            
        except asyncio.TimeoutError:
            duration = time.time() - start_time
            logger.warning(f"Circuit {self.name} call timed out after {duration:.2f}s")
            self._record_failure(asyncio.TimeoutError(f"Call timed out after {duration:.2f}s"))
            raise
            
        except Exception as e:
            duration = time.time() - start_time
            
            # Check if this exception should be excluded from failure count
            if any(isinstance(e, exc_type) for exc_type in self.config.exclude_exceptions):
                logger.info(f"Circuit {self.name} excluded exception: {type(e).__name__}")
                self._last_call_duration = duration
                raise
            
            logger.warning(f"Circuit {self.name} call failed with {type(e).__name__}: {str(e)}")
            self._record_failure(e)
            raise
    
    def _record_success(self, duration: float):
        """Record a successful call"""
        self._last_call_duration = duration
        self._last_success_time = time.time()
        self._success_count += 1
        
        # Reset failure count on success in half-open state
        if self._state == CircuitState.HALF_OPEN:
            logger.info(f"Circuit {self.name} recovery successful, closing circuit")
            self._state = CircuitState.CLOSED
            self._failure_count = 0
            self._half_open_calls = 0
    
    def _record_failure(self, exception: Exception):
        """Record a failed call"""
        self._last_failure_time = time.time()
        self._failure_count += 1
        
        # Check if we need to open the circuit
        if self._state == CircuitState.CLOSED and self._failure_count >= self.config.failure_threshold:
            logger.warning(f"Circuit {self.name} reached failure threshold ({self._failure_count}), opening circuit")
            self._state = CircuitState.OPEN
        
        # In half-open state, go back to open on failure
        elif self._state == CircuitState.HALF_OPEN:
            logger.warning(f"Circuit {self.name} failed in half-open state, reopening circuit")
            self._state = CircuitState.OPEN
            self._half_open_calls = 0
    
    async def _check_state_transition(self):
        """Check and update circuit state based on timing"""
        if self._state == CircuitState.OPEN:
            # Check if recovery timeout has elapsed
            if time.time() - self._last_failure_time >= self.config.recovery_timeout:
                async with self._state_lock:
                    if self._state == CircuitState.OPEN:  # Double-check inside lock
                        logger.info(f"Circuit {self.name} recovery timeout elapsed, transitioning to HALF_OPEN")
                        self._state = CircuitState.HALF_OPEN
                        self._half_open_calls = 0
    
    def reset(self):
        """Reset the circuit breaker to closed state"""
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._half_open_calls = 0
        logger.info(f"Circuit {self.name} manually reset to CLOSED state")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get circuit breaker metrics"""
        return {
            "name": self.name,
            "state": self._state.value,
            "failure_count": self._failure_count,
            "success_count": self._success_count,
            "total_calls": self._total_calls,
            "failure_rate": self.failure_rate,
            "last_failure_time": self._last_failure_time,
            "last_success_time": self._last_success_time,
            "last_call_duration": self._last_call_duration
        }


class CircuitBreakerOpenError(Exception):
    """Exception raised when a circuit is open"""
    pass


class CircuitBreakerRegistry:
    """Registry for managing multiple circuit breakers"""
    
    _instance = None
    _breakers: Dict[str, CircuitBreaker] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(CircuitBreakerRegistry, cls).__new__(cls)
        return cls._instance
    
    def get_breaker(self, name: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        """
        Get or create a circuit breaker by name.
        
        Args:
            name: Circuit breaker name
            config: Optional configuration for new breakers
            
        Returns:
            CircuitBreaker instance
        """
        if name not in self._breakers:
            self._breakers[name] = CircuitBreaker(name, config)
        return self._breakers[name]
    
    def get_all_breakers(self) -> Dict[str, CircuitBreaker]:
        """Get all registered circuit breakers"""
        return self._breakers.copy()
    
    def reset_all(self):
        """Reset all circuit breakers"""
        for breaker in self._breakers.values():
            breaker.reset()
    
    def get_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get metrics for all circuit breakers"""
        return {name: breaker.get_metrics() for name, breaker in self._breakers.items()}


# Global registry instance
circuit_breaker_registry = CircuitBreakerRegistry()


def get_circuit_breaker(name: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
    """
    Get a circuit breaker by name from the registry.
    
    Args:
        name: Circuit breaker name
        config: Optional configuration for new breakers
        
    Returns:
        CircuitBreaker instance
    """
    return circuit_breaker_registry.get_breaker(name, config)


async def with_circuit_breaker(
    name: str,
    func: Callable[..., Awaitable[R]],
    *args,
    fallback_result: Optional[R] = None,
    config: Optional[CircuitBreakerConfig] = None,
    **kwargs
) -> R:
    """
    Execute a function with circuit breaker protection and optional fallback.
    
    Args:
        name: Circuit breaker name
        func: Async function to execute
        *args: Positional arguments for func
        fallback_result: Result to return if circuit is open or call fails
        config: Optional circuit breaker configuration
        **kwargs: Keyword arguments for func
        
    Returns:
        Result of the function call or fallback result
    """
    breaker = get_circuit_breaker(name, config)
    
    try:
        return await breaker.execute(func, *args, **kwargs)
    except (CircuitBreakerOpenError, asyncio.TimeoutError, Exception) as e:
        if fallback_result is not None:
            logger.info(f"Circuit {name} using fallback result due to {type(e).__name__}")
            return fallback_result
        raise
