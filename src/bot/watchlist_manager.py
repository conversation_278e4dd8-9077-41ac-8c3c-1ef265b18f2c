"""
Watchlist Manager for TradingView Automation
Manages user watchlists, symbols, and alerts

This module provides backward compatibility by re-exporting the shared watchlist implementation.
"""

from typing import Optional
from src.shared.watchlist.base_manager import BaseWatchlistManager
from src.shared.watchlist.models import WatchlistSymbol, UserWatchlist

# Re-export for backward compatibility
__all__ = ['WatchlistManager', 'WatchlistSymbol', 'UserWatchlist']

class WatchlistManager(BaseWatchlistManager):
    """Legacy watchlist manager - use shared implementation instead"""
    
    def __init__(self, db_pool=None):
        """Initialize with database pool"""
        super().__init__(db_pool)
        
    def log_info(self, message: str, **kwargs):
        """Log information"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info(message, **kwargs)
        
    def log_error(self, message: str, **kwargs):
        """Log error"""
        import logging
        logger = logging.getLogger(__name__)
        logger.error(message, **kwargs)
        
    def record_metric(self, operation: str, status: str, duration: Optional[float] = None):
        """Record metrics - placeholder implementation"""
        pass
