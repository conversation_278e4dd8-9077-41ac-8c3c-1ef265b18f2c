"""
Discord Bot Permission System
Simple permission levels for Discord bot commands
"""

from enum import Enum
from typing import Optional, Dict, Any, List, Set, Tuple
import discord
from discord.ext import commands
import os
import time
import logging
import json
from datetime import datetime, timedelta

from .rate_limiter import EnhancedRateLimiter, RateLimitTier, rate_limiter
from .token_validator import TokenValidator, token_validator

logger = logging.getLogger(__name__)

class PermissionLevel(Enum):
    """Simple permission levels for Discord bot commands"""
    PUBLIC = "public"      # Everyone can use
    PAID = "paid"          # Paid tier users
    ADMIN = "admin"        # Server admins only
    OWNER = "owner"        # Bot owner only

class DiscordPermissionChecker:
    """Enhanced Discord permission checker with token validation and rate limiting"""
    
    def __init__(self, bot_owner_id: Optional[int] = None):
        self.bot_owner_id = bot_owner_id
        
        # Use role IDs instead of names for better security
        # These should be configured from environment variables in production
        self.paid_role_ids = [
            int(id_str) for id_str in os.getenv('PAID_ROLE_IDS', '').split(',') if id_str
        ] or [1408844110642544836]  # Default test role ID
        
        self.admin_role_ids = [
            int(id_str) for id_str in os.getenv('ADMIN_ROLE_IDS', '').split(',') if id_str
        ] or [1304548446090301448]  # Default test role ID
        
        # Fallback to role names only if no IDs are configured
        self.paid_role_names = ["paid", "premium", "vip", "pro"]  
        self.admin_role_names = ["admin", "moderator", "mod"]
        
        # Track permission checks for security auditing
        self.permission_checks = {}
        self.last_audit_time = time.time()
        
        # Token validation cache (user_id -> token_valid)
        self.token_validation_cache = {}
        
        # Token validation expiry (user_id -> expiry_time)
        self.token_validation_expiry = {}
        
        # Cache expiry time (1 hour)
        self.cache_expiry = 3600
        
        # Initialize rate limiter tiers
        self._initialize_rate_limiter_tiers()
    
    def _initialize_rate_limiter_tiers(self):
        """Initialize rate limiter tiers based on environment variables"""
        # This would typically be loaded from a configuration file or database
        # For now, we'll use the default tiers in the EnhancedRateLimiter class
        pass
    
    def has_permission(self, member: discord.User | discord.Member, required_level: PermissionLevel, 
                      token: Optional[str] = None, guild_id: Optional[str] = None) -> Tuple[bool, str]:
        """
        Check if a Discord user or member has the required permission level
        
        Args:
            member: Discord user or member object
            required_level: Required permission level
            token: Optional access token for validation
            guild_id: Optional guild ID for rate limiting
            
        Returns:
            Tuple of (has_permission, reason)
        """
        user_id = str(member.id)
        
        # Public access is always granted but still subject to rate limiting
        if required_level == PermissionLevel.PUBLIC:
            # Check rate limits
            can_request, reason = self._check_rate_limits(user_id, guild_id, RateLimitTier.FREE)
            if not can_request:
                self.log_permission_check(user_id, required_level, False, reason)
                return False, reason
                
            self.log_permission_check(user_id, required_level, True, "Public access granted")
            return True, "Public access granted"
            
        # Owner check - most restrictive
        if required_level == PermissionLevel.OWNER:
            granted = member.id == self.bot_owner_id
            reason = "Owner access granted" if granted else "Not bot owner"
            self.log_permission_check(user_id, required_level, granted, reason)
            return granted, reason
            
        # Admin check
        if required_level == PermissionLevel.ADMIN:
            granted = self._is_admin(member)
            
            # If not admin by role, check token
            if not granted and token:
                granted, reason, payload = token_validator.validate_token(user_id, token)
                if granted and payload and payload.get('tier') == 'admin':
                    granted = True
                    reason = "Admin access granted via token"
                else:
                    granted = False
                    reason = "Invalid admin token"
            else:
                reason = "Admin access granted" if granted else "Not admin"
            
            # Check rate limits if granted
            if granted:
                can_request, rate_reason = self._check_rate_limits(user_id, guild_id, RateLimitTier.ADMIN)
                if not can_request:
                    self.log_permission_check(user_id, required_level, False, rate_reason)
                    return False, rate_reason
            
            self.log_permission_check(user_id, required_level, granted, reason)
            return granted, reason
            
        # Paid tier check
        if required_level == PermissionLevel.PAID:
            # Check if admin first (admins have paid access)
            if self._is_admin(member):
                can_request, rate_reason = self._check_rate_limits(user_id, guild_id, RateLimitTier.ADMIN)
                if not can_request:
                    self.log_permission_check(user_id, required_level, False, rate_reason)
                    return False, rate_reason
                    
                self.log_permission_check(user_id, required_level, True, "Admin has paid access")
                return True, "Admin has paid access"
            
            # Check if paid by role
            granted = self._is_paid(member)
            reason = "Paid access granted by role" if granted else "Not paid tier"
            
            # If not paid by role, check token
            if not granted and token:
                is_valid, token_reason, payload = self._validate_token_with_cache(user_id, token)
                if is_valid and payload and payload.get('tier') in ['paid', 'premium', 'admin']:
                    granted = True
                    reason = "Paid access granted via token"
                else:
                    reason = token_reason
            
            # Check rate limits if granted
            if granted:
                can_request, rate_reason = self._check_rate_limits(user_id, guild_id, RateLimitTier.PAID)
                if not can_request:
                    self.log_permission_check(user_id, required_level, False, rate_reason)
                    return False, rate_reason
            
            self.log_permission_check(user_id, required_level, granted, reason)
            return granted, reason
            
        # Unknown permission level
        logger.warning(f"Unknown permission level requested: {required_level}")
        self.log_permission_check(user_id, required_level, False, "Unknown permission level")
        return False, "Unknown permission level"
    
    def _is_admin(self, member: discord.User | discord.Member) -> bool:
        """Check if member has admin permissions with secure role ID checks"""
        # If it's just a User (not a Member), we can't check roles/permissions
        if not isinstance(member, discord.Member):
            return False
            
        # Check Discord permissions first (most secure)
        if member.guild_permissions.administrator:
            return True
        
        # Check role IDs (secure)
        member_role_ids = {role.id for role in member.roles}
        if any(role_id in member_role_ids for role_id in self.admin_role_ids):
            return True
            
        # Fallback to role names only if no role IDs match (less secure)
        if not self.admin_role_ids:
            member_role_names = {role.name.lower() for role in member.roles}
            return any(admin_role in member_role_names for admin_role in self.admin_role_names)
            
        return False
    
    def _is_paid(self, member: discord.User | discord.Member) -> bool:
        """Check if member has paid tier access with secure role ID checks"""
        # If it's just a User (not a Member), we can't check roles
        if not isinstance(member, discord.Member):
            return False
            
        # Primary check: role IDs (secure)
        member_role_ids = {role.id for role in member.roles}
        if any(role_id in member_role_ids for role_id in self.paid_role_ids):
            return True
            
        # Fallback to role names only if no role IDs match (less secure)
        if not self.paid_role_ids:
            member_role_names = {role.name.lower() for role in member.roles}
            return any(paid_role in member_role_names for paid_role in self.paid_role_names)
            
        return False
        
    def _validate_token_with_cache(self, user_id: str, token: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """Validate token with caching for performance"""
        # Check cache first
        current_time = time.time()
        
        if user_id in self.token_validation_cache and current_time < self.token_validation_expiry.get(user_id, 0):
            return self.token_validation_cache[user_id]
        
        # Validate token
        result = token_validator.validate_token(user_id, token)
        
        # Cache result
        self.token_validation_cache[user_id] = result
        self.token_validation_expiry[user_id] = current_time + self.cache_expiry
        
        return result
    
    def _check_rate_limits(self, user_id: str, guild_id: Optional[str], tier: RateLimitTier) -> Tuple[bool, str]:
        """Check rate limits for user and guild"""
        # Set user tier in rate limiter
        rate_limiter.set_user_tier(user_id, tier)
        
        # Set guild tier if applicable
        if guild_id:
            rate_limiter.set_guild_tier(guild_id, tier)
        
        # Check if request can be made
        return rate_limiter.can_make_request(user_id, guild_id)
    
    def log_permission_check(self, user_id: str, required_level: PermissionLevel, granted: bool, reason: str) -> None:
        """Log permission check for security auditing"""
        now = time.time()
        
        # Initialize user entry if not exists
        if user_id not in self.permission_checks:
            self.permission_checks[user_id] = []
            
        # Add check to log
        self.permission_checks[user_id].append({
            'timestamp': now,
            'required_level': required_level.value,
            'granted': granted,
            'reason': reason
        })
        
        # Log suspicious activity (many failed attempts)
        failed_checks = [c for c in self.permission_checks[user_id] 
                        if not c['granted'] and now - c['timestamp'] < 3600]
        
        if len(failed_checks) >= 5:
            logger.warning(f"Suspicious permission activity detected for user {user_id}: "
                         f"{len(failed_checks)} failed permission checks in the last hour")
            
            # Record suspicious activity in rate limiter
            if hasattr(rate_limiter, '_check_suspicious_activity'):
                rate_limiter._check_suspicious_activity(user_id, datetime.now())
    
    def get_user_tier(self, member: discord.User | discord.Member) -> str:
        """Get user's current tier level"""
        if self._is_admin(member):
            return "admin"
        elif self._is_paid(member):
            return "paid"
        else:
            return "public"

def require_permission(permission_level: PermissionLevel):
    """
    Decorator to require specific permission level for bot commands
    
    Usage:
        @require_permission(PermissionLevel.PAID)
        async def premium_command(ctx):
            # Only paid users can access this
    """
    def decorator(func):
        async def wrapper(self, ctx, *args, **kwargs):
            # Get permission checker from bot instance
            permission_checker = getattr(self, 'permission_checker', None)
            if not permission_checker:
                await ctx.send("❌ Permission system not configured")
                return
            
            # Extract token from kwargs if present
            token = kwargs.pop('token', None)
            
            # Get guild ID if available
            guild_id = str(ctx.guild.id) if ctx.guild else None
            
            # Check permission
            has_permission, reason = permission_checker.has_permission(
                ctx.author, permission_level, token, guild_id
            )
            
            if not has_permission:
                tier_names = {
                    PermissionLevel.PAID: "paid tier",
                    PermissionLevel.ADMIN: "admin",
                    PermissionLevel.OWNER: "bot owner"
                }
                required_tier = tier_names.get(permission_level, "higher permission")
                await ctx.send(f"❌ This command requires {required_tier} access: {reason}")
                return
            
            # Record the request in rate limiter
            if hasattr(rate_limiter, 'record_request'):
                rate_limiter.record_request(str(ctx.author.id), guild_id)
                
            return await func(self, ctx, *args, **kwargs)
        return wrapper
    return decorator


def validate_token(token_required: bool = False):
    """
    Decorator to validate access tokens for premium features
    
    Usage:
        @validate_token()
        async def premium_command(ctx, token=None):
            # Token is validated if provided
            
        @validate_token(token_required=True)
        async def premium_command(ctx, token):
            # Token is required and validated
    """
    def decorator(func):
        async def wrapper(self, ctx, *args, **kwargs):
            # Check if token is provided
            token = kwargs.get('token')
            
            # If token is required but not provided
            if token_required and not token:
                await ctx.send("❌ Access token is required for this command")
                return
            
            # If token is provided, validate it
            if token:
                user_id = str(ctx.author.id)
                is_valid, reason, payload = token_validator.validate_token(user_id, token)
                
                if not is_valid:
                    await ctx.send(f"❌ Invalid access token: {reason}")
                    return
                
                # Add validated payload to kwargs
                kwargs['token_payload'] = payload
                
            return await func(self, ctx, *args, **kwargs)
        return wrapper
    return decorator