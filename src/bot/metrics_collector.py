"""
Metrics Collector Module

Collects and stores metrics and feedback for bot commands
to improve response quality and user experience.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import os
import aiofiles

from src.core.logger import get_logger

logger = get_logger(__name__)

# Path to store feedback data
FEEDBACK_FILE = os.path.join('data', 'response_metrics.json')

async def record_feedback(
    user_id: str,
    command: str,
    rating: str,
    comment: str = "",
    timestamp: Optional[datetime] = None
) -> bool:
    """
    Record user feedback for a command
    
    Args:
        user_id: Discord user ID
        command: Command name
        rating: Feedback rating (positive, negative, neutral)
        comment: Additional comments
        timestamp: Feedback timestamp
        
    Returns:
        True if feedback was recorded successfully, False otherwise
    """
    try:
        # Create timestamp if not provided
        if timestamp is None:
            timestamp = datetime.now()
        
        # Create feedback entry
        feedback_entry = {
            "user_id": user_id,
            "command": command,
            "rating": rating,
            "comment": comment,
            "timestamp": timestamp.isoformat()
        }
        
        # Load existing feedback data
        feedback_data = await load_feedback_data()
        
        # Add new feedback entry
        if "feedback" not in feedback_data:
            feedback_data["feedback"] = []
        
        feedback_data["feedback"].append(feedback_entry)
        
        # Update metrics
        if "metrics" not in feedback_data:
            feedback_data["metrics"] = {}
        
        if command not in feedback_data["metrics"]:
            feedback_data["metrics"][command] = {
                "total": 0,
                "positive": 0,
                "negative": 0,
                "neutral": 0
            }
        
        # Increment metrics
        feedback_data["metrics"][command]["total"] += 1
        feedback_data["metrics"][command][rating] += 1
        
        # Save feedback data
        await save_feedback_data(feedback_data)
        
        logger.info(f"Recorded feedback for command {command} from user {user_id}: {rating}")
        return True
        
    except Exception as e:
        logger.error(f"Error recording feedback: {e}")
        return False

async def load_feedback_data() -> Dict[str, Any]:
    """
    Load feedback data from file
    
    Returns:
        Dictionary with feedback data
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(FEEDBACK_FILE), exist_ok=True)
        
        # Check if file exists
        if not os.path.exists(FEEDBACK_FILE):
            return {"feedback": [], "metrics": {}}
        
        # Load data from file
        async with aiofiles.open(FEEDBACK_FILE, 'r') as f:
            data = await f.read()
            return json.loads(data)
            
    except Exception as e:
        logger.error(f"Error loading feedback data: {e}")
        return {"feedback": [], "metrics": {}}

async def save_feedback_data(data: Dict[str, Any]) -> bool:
    """
    Save feedback data to file
    
    Args:
        data: Feedback data to save
        
    Returns:
        True if data was saved successfully, False otherwise
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(FEEDBACK_FILE), exist_ok=True)
        
        # Save data to file
        async with aiofiles.open(FEEDBACK_FILE, 'w') as f:
            await f.write(json.dumps(data, indent=2))
        
        return True
        
    except Exception as e:
        logger.error(f"Error saving feedback data: {e}")
        return False

async def get_command_metrics(command: Optional[str] = None) -> Dict[str, Any]:
    """
    Get metrics for a specific command or all commands
    
    Args:
        command: Command name or None for all commands
        
    Returns:
        Dictionary with command metrics
    """
    try:
        # Load feedback data
        feedback_data = await load_feedback_data()
        
        # Get metrics
        metrics = feedback_data.get("metrics", {})
        
        if command:
            # Return metrics for specific command
            return {command: metrics.get(command, {
                "total": 0,
                "positive": 0,
                "negative": 0,
                "neutral": 0
            })}
        else:
            # Return metrics for all commands
            return metrics
            
    except Exception as e:
        logger.error(f"Error getting command metrics: {e}")
        return {}

async def get_recent_feedback(
    limit: int = 10,
    command: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Get recent feedback entries
    
    Args:
        limit: Maximum number of entries to return
        command: Filter by command name
        
    Returns:
        List of feedback entries
    """
    try:
        # Load feedback data
        feedback_data = await load_feedback_data()
        
        # Get feedback entries
        feedback = feedback_data.get("feedback", [])
        
        # Filter by command if specified
        if command:
            feedback = [entry for entry in feedback if entry.get("command") == command]
        
        # Sort by timestamp (newest first)
        feedback.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        
        # Limit number of entries
        return feedback[:limit]
            
    except Exception as e:
        logger.error(f"Error getting recent feedback: {e}")
        return []

async def generate_feedback_report() -> Dict[str, Any]:
    """
    Generate a comprehensive feedback report
    
    Returns:
        Dictionary with feedback report data
    """
    try:
        # Load feedback data
        feedback_data = await load_feedback_data()
        
        # Get metrics
        metrics = feedback_data.get("metrics", {})
        
        # Calculate overall metrics
        total_feedback = 0
        total_positive = 0
        total_negative = 0
        total_neutral = 0
        
        for command, command_metrics in metrics.items():
            total_feedback += command_metrics.get("total", 0)
            total_positive += command_metrics.get("positive", 0)
            total_negative += command_metrics.get("negative", 0)
            total_neutral += command_metrics.get("neutral", 0)
        
        # Calculate satisfaction rate
        satisfaction_rate = (total_positive / total_feedback * 100) if total_feedback > 0 else 0
        
        # Get top commands by feedback volume
        top_commands = sorted(
            metrics.items(),
            key=lambda x: x[1].get("total", 0),
            reverse=True
        )[:5]
        
        # Get recent feedback
        recent_feedback = await get_recent_feedback(limit=10)
        
        # Create report
        report = {
            "generated_at": datetime.now().isoformat(),
            "overall": {
                "total_feedback": total_feedback,
                "positive": total_positive,
                "negative": total_negative,
                "neutral": total_neutral,
                "satisfaction_rate": satisfaction_rate
            },
            "top_commands": [
                {
                    "command": command,
                    "total": metrics.get("total", 0),
                    "satisfaction_rate": (metrics.get("positive", 0) / metrics.get("total", 0) * 100) if metrics.get("total", 0) > 0 else 0
                }
                for command, metrics in top_commands
            ],
            "recent_feedback": recent_feedback
        }
        
        return report
            
    except Exception as e:
        logger.error(f"Error generating feedback report: {e}")
        return {
            "error": str(e),
            "generated_at": datetime.now().isoformat()
        }
