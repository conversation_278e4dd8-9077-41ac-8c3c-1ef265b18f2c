"""
Component Initialization Checker
Provides utilities for checking if required components are initialized
"""

import logging
from typing import List, Dict, Any, Optional
import discord

logger = logging.getLogger(__name__)

class ComponentChecker:
    """Utility for checking component initialization status"""
    
    @staticmethod
    async def check_components(
        obj: Any, 
        interaction: discord.Interaction,
        required_components: List[str]
    ) -> bool:
        """
        Check if required components are initialized
        
        Args:
            obj: Object containing the components
            interaction: Discord interaction for response
            required_components: List of component attribute names to check
            
        Returns:
            bool: True if all components are initialized, False otherwise
        """
        missing_components = []
        
        for component_name in required_components:
            component = getattr(obj, component_name, None)
            if component is None:
                missing_components.append(component_name)
                logger.error(f"Required component '{component_name}' is not initialized")
        
        if missing_components:
            # Format component names for display
            formatted_components = ", ".join([f"`{c}`" for c in missing_components])
            
            # Send error message
            try:
                if interaction.response.is_done():
                    await interaction.followup.send(
                        f"❌ System initialization error: Required components not ready: {formatted_components}. "
                        f"Please try again later or contact support."
                    )
                else:
                    await interaction.response.send_message(
                        f"❌ System initialization error: Required components not ready: {formatted_components}. "
                        f"Please try again later or contact support."
                    )
            except Exception as e:
                logger.error(f"Failed to send component initialization error message: {e}")
            
            return False
        
        return True
