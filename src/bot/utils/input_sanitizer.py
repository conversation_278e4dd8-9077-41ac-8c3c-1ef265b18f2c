"""
Input Sanitizer Module

Provides comprehensive input validation and sanitization for Discord bot commands
to prevent injection attacks and ensure data quality.
"""

import re
import logging
import html
from typing import Optional, Dict, Any, List, Tuple, Union

logger = logging.getLogger(__name__)

class InputSanitizer:
    """Input sanitization and validation for Discord bot commands"""
    
    # Regex patterns for validation
    SYMBOL_PATTERN = re.compile(r'^[A-Z0-9\.\-]{1,10}$')
    QUERY_PATTERN = re.compile(r'^[\w\s\.\,\?\!\$\(\)\[\]\{\}\:\;\@\#\%\&\*\+\-\/\\\'\"\=\<\>\~\^\_\`]{1,500}$')
    USERNAME_PATTERN = re.compile(r'^[\w\s\.\-]{1,32}$')
    WATCHLIST_NAME_PATTERN = re.compile(r'^[\w\s\.\-]{1,50}$')
    
    # Blacklisted patterns (potential injection attempts)
    SQL_INJECTION_PATTERN = re.compile(r'(\b(select|insert|update|delete|drop|alter|create|exec|union|where)\b.*\b(from|into|table|database|values)\b)', re.IGNORECASE)
    PROMPT_INJECTION_PATTERN = re.compile(r'(ignore previous instructions|ignore all instructions|system prompt|you are a|you\'re a|act as if|pretend to be)', re.IGNORECASE)
    COMMAND_INJECTION_PATTERN = re.compile(r'(`|\$\(|\|\||&&|;|\n)', re.IGNORECASE)
    
    # Patterns for detecting sensitive information
    API_KEY_PATTERN = re.compile(r'\b[A-Za-z0-9]{20,}\b')
    PASSWORD_PATTERN = re.compile(r'\b(pass|pwd|password|secret|token|key|api[_-]?key|auth)[=:][^\s\n]+\b', re.IGNORECASE)
    EMAIL_PATTERN = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
    CREDIT_CARD_PATTERN = re.compile(r'\b(?:\d[ -]*?){13,16}\b')
    SSN_PATTERN = re.compile(r'\b\d{3}[-.]?\d{2}[-.]?\d{4}\b')
    PERSONAL_INFO_PATTERNS = [
        (r'\b(api[_-]?key|secret[_-]?key|private[_-]?key)\b', 'API key'),
        (r'\b(aws[_-]?access[_-]?key[_-]?id|aws[_-]?secret[_-]?access[_-]?key)\b', 'AWS credentials'),
        (r'\b(bearer\s+[a-z0-9\-\._~\+/]+=*)\b', 'Bearer token'),
        (r'\b([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})\b', 'UUID'),
    ]
    
    @classmethod
    def contains_sensitive_info(cls, text: str) -> bool:
        """
        Check if the input contains sensitive information like API keys, passwords, etc.
        
        Args:
            text: The text to check for sensitive information
            
        Returns:
            bool: True if sensitive information is detected, False otherwise
        """
        if not text:
            return False
            
        # Check for common sensitive patterns
        patterns_to_check = [
            (cls.API_KEY_PATTERN, 'API key'),
            (cls.PASSWORD_PATTERN, 'password/credential'),
            (cls.EMAIL_PATTERN, 'email address'),
            (cls.CREDIT_CARD_PATTERN, 'credit card number'),
            (cls.SSN_PATTERN, 'social security number'),
        ]
        
        # Add dynamic patterns from PERSONAL_INFO_PATTERNS
        for pattern, _ in cls.PERSONAL_INFO_PATTERNS:
            patterns_to_check.append((re.compile(pattern, re.IGNORECASE), _))
        
        # Check each pattern
        for pattern, pattern_name in patterns_to_check:
            if pattern.search(text):
                logger.warning(f'Potential sensitive information detected in input: {pattern_name}')
                return True
                
        return False
    
    @classmethod
    def sanitize_symbol(cls, symbol: str) -> Tuple[str, bool, str]:
        """
        Sanitize and validate a stock symbol
        
        Args:
            symbol: The stock symbol to sanitize
            
        Returns:
            Tuple of (sanitized_symbol, is_valid, error_message)
        """
        if not symbol:
            return "", False, "Symbol cannot be empty"
        
        # Basic sanitization
        sanitized = symbol.upper().strip()
        
        # Remove any $ prefix if present
        if sanitized.startswith('$'):
            sanitized = sanitized[1:]
        
        # Validate against pattern
        if not cls.SYMBOL_PATTERN.match(sanitized):
            return sanitized, False, "Invalid symbol format"
        
        # Check length
        if len(sanitized) > 10:
            return sanitized, False, "Symbol too long (max 10 characters)"
            
        return sanitized, True, ""
    
    @classmethod
    def sanitize_query(cls, query: str) -> Tuple[str, bool, str]:
        """
        Sanitize and validate a user query
        
        Args:
            query: The user query to sanitize
            
        Returns:
            Tuple of (sanitized_query, is_valid, error_message)
        """
        if not query:
            return "", False, "Query cannot be empty"
        
        # Basic sanitization
        sanitized = query.strip()
        
        # HTML escape to prevent XSS
        sanitized = html.escape(sanitized)
        
        # Check for potential injection attempts
        if cls.SQL_INJECTION_PATTERN.search(sanitized):
            logger.warning(f"Potential SQL injection attempt detected: {query}")
            return sanitized, False, "Invalid query format detected"
            
        if cls.COMMAND_INJECTION_PATTERN.search(sanitized):
            logger.warning(f"Potential command injection attempt detected: {query}")
            return sanitized, False, "Invalid characters in query"
            
        if cls.PROMPT_INJECTION_PATTERN.search(sanitized):
            logger.warning(f"Potential prompt injection attempt detected: {query}")
            # Don't reject prompt injections, but log them
            # We'll handle them differently in the AI processing stage
        
        # Check length
        if len(sanitized) > 500:
            return sanitized[:500], False, "Query too long (max 500 characters)"
            
        return sanitized, True, ""
    
    @classmethod
    def sanitize_watchlist_action(cls, action: str) -> Tuple[str, bool, str]:
        """
        Sanitize and validate a watchlist action
        
        Args:
            action: The watchlist action to sanitize
            
        Returns:
            Tuple of (sanitized_action, is_valid, error_message)
        """
        if not action:
            return "show", True, ""  # Default to show
        
        # Basic sanitization
        sanitized = action.lower().strip()
        
        # Validate against allowed actions
        allowed_actions = ["show", "add", "remove", "create"]
        if sanitized not in allowed_actions:
            return "show", False, f"Invalid action. Allowed: {', '.join(allowed_actions)}"
            
        return sanitized, True, ""
    
    @classmethod
    def sanitize_watchlist_name(cls, name: str) -> Tuple[str, bool, str]:
        """
        Sanitize and validate a watchlist name
        
        Args:
            name: The watchlist name to sanitize
            
        Returns:
            Tuple of (sanitized_name, is_valid, error_message)
        """
        if not name:
            return "Default", True, ""  # Default name
        
        # Basic sanitization
        sanitized = name.strip()
        
        # HTML escape
        sanitized = html.escape(sanitized)
        
        # Validate against pattern
        if not cls.WATCHLIST_NAME_PATTERN.match(sanitized):
            return sanitized, False, "Invalid watchlist name format"
        
        # Check length
        if len(sanitized) > 50:
            return sanitized[:50], False, "Watchlist name too long (max 50 characters)"
            
        return sanitized, True, ""
    
    @classmethod
    def detect_sensitive_content(cls, text: str) -> bool:
        """
        Detect potentially sensitive content in user input
        
        Args:
            text: The text to check
            
        Returns:
            True if sensitive content detected, False otherwise
        """
        # Check for potential sensitive patterns
        sensitive_patterns = [
            re.compile(r'password', re.IGNORECASE),
            re.compile(r'api[_\-\s]?key', re.IGNORECASE),
            re.compile(r'secret', re.IGNORECASE),
            re.compile(r'token', re.IGNORECASE),
            re.compile(r'credential', re.IGNORECASE),
            re.compile(r'account', re.IGNORECASE),
            re.compile(r'ssn', re.IGNORECASE),
            re.compile(r'\d{3}[\-\s]?\d{2}[\-\s]?\d{4}'),  # SSN pattern
            re.compile(r'\d{16}'),  # Credit card pattern (simplified)
        ]
        
        for pattern in sensitive_patterns:
            if pattern.search(text):
                logger.warning(f"Potential sensitive information detected in user input")
                return True
                
        return False
    
    @classmethod
    def sanitize_all_inputs(cls, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize all inputs in a dictionary
        
        Args:
            inputs: Dictionary of input parameters
            
        Returns:
            Dictionary with sanitized inputs and validation results
        """
        results = {
            "sanitized": {},
            "is_valid": True,
            "errors": {}
        }
        
        for key, value in inputs.items():
            if key == "query" and isinstance(value, str):
                sanitized, is_valid, error = cls.sanitize_query(value)
                results["sanitized"][key] = sanitized
                if not is_valid:
                    results["is_valid"] = False
                    results["errors"][key] = error
                    
            elif key == "symbol" and isinstance(value, str):
                sanitized, is_valid, error = cls.sanitize_symbol(value)
                results["sanitized"][key] = sanitized
                if not is_valid:
                    results["is_valid"] = False
                    results["errors"][key] = error
                    
            elif key == "action" and isinstance(value, str):
                sanitized, is_valid, error = cls.sanitize_watchlist_action(value)
                results["sanitized"][key] = sanitized
                if not is_valid:
                    results["is_valid"] = False
                    results["errors"][key] = error
                    
            elif key == "name" and isinstance(value, str):
                sanitized, is_valid, error = cls.sanitize_watchlist_name(value)
                results["sanitized"][key] = sanitized
                if not is_valid:
                    results["is_valid"] = False
                    results["errors"][key] = error
                    
            else:
                # For other types, just pass through
                results["sanitized"][key] = value
                
        return results

# Convenience functions
def sanitize_symbol(symbol: str) -> str:
    """Sanitize a stock symbol (convenience function)"""
    sanitized, _, _ = InputSanitizer.sanitize_symbol(symbol)
    return sanitized

def sanitize_query(query: str) -> str:
    """Sanitize a user query (convenience function)"""
    sanitized, _, _ = InputSanitizer.sanitize_query(query)
    return sanitized

def is_valid_symbol(symbol: str) -> bool:
    """Check if a symbol is valid (convenience function)"""
    _, is_valid, _ = InputSanitizer.sanitize_symbol(symbol)
    return is_valid

def is_valid_query(query: str) -> bool:
    """Check if a query is valid (convenience function)"""
    _, is_valid, _ = InputSanitizer.sanitize_query(query)
    return is_valid
