"""
Standardized Error Handler for Trading Bot
Provides consistent error handling and logging across components
"""

import logging
import traceback
import sys
import time
from typing import Dict, Any, Optional, Callable, Coroutine, TypeVar, Union
import discord
from functools import wraps
import asyncio

logger = logging.getLogger(__name__)

# Type variables for generic function handling
T = TypeVar('T')
R = TypeVar('R')

class ErrorCategory:
    """Error categories for better organization and reporting"""
    DATABASE = "database"
    NETWORK = "network"
    AUTHENTICATION = "authentication"
    PERMISSION = "permission"
    VALIDATION = "validation"
    TIMEOUT = "timeout"
    EXTERNAL_API = "external_api"
    INTERNAL = "internal"
    UNKNOWN = "unknown"

class ErrorHandler:
    """Centralized error handling with standardized logging and reporting"""
    
    @staticmethod
    def log_error(
        error: Exception, 
        category: str = ErrorCategory.UNKNOWN,
        context: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Log an error with standardized format and context
        
        Args:
            error: The exception that occurred
            category: Error category for classification
            context: Additional context about where/why the error occurred
            user_id: ID of the user who triggered the action (if applicable)
            correlation_id: Request correlation ID for tracing
            
        Returns:
            Dict with error details for reference
        """
        error_id = int(time.time() * 1000)  # Timestamp-based error ID
        
        # Build error context
        error_context = {
            "error_id": error_id,
            "error_type": error.__class__.__name__,
            "error_message": str(error),
            "category": category,
            "timestamp": time.time(),
            "traceback": traceback.format_exc(),
            "correlation_id": correlation_id
        }
        
        # Add user context if available
        if user_id:
            error_context["user_id"] = user_id
            
        # Add additional context if provided
        if context:
            error_context["context"] = context
            
        # Log with appropriate level based on category
        if category in [ErrorCategory.INTERNAL, ErrorCategory.DATABASE, ErrorCategory.AUTHENTICATION]:
            logger.error(f"Error {error_id} [{category}]: {error}", extra=error_context)
        else:
            logger.warning(f"Error {error_id} [{category}]: {error}", extra=error_context)
            
        return error_context
    
    @staticmethod
    def get_user_friendly_message(
        error: Exception,
        category: str = ErrorCategory.UNKNOWN
    ) -> str:
        """
        Get a user-friendly error message based on error type and category
        
        Args:
            error: The exception that occurred
            category: Error category for classification
            
        Returns:
            User-friendly error message
        """
        # Default messages by category
        category_messages = {
            ErrorCategory.DATABASE: "A database error occurred. Our team has been notified.",
            ErrorCategory.NETWORK: "A network connection error occurred. Please check your connection and try again.",
            ErrorCategory.AUTHENTICATION: "Authentication failed. Please check your credentials and try again.",
            ErrorCategory.PERMISSION: "You don't have permission to perform this action.",
            ErrorCategory.VALIDATION: "The provided data is invalid. Please check your input and try again.",
            ErrorCategory.TIMEOUT: "The operation timed out. Please try again later.",
            ErrorCategory.EXTERNAL_API: "An external service is currently unavailable. Please try again later.",
            ErrorCategory.INTERNAL: "An internal error occurred. Our team has been notified.",
            ErrorCategory.UNKNOWN: "An unexpected error occurred. Please try again later."
        }
        
        # Special handling for common exceptions
        if isinstance(error, asyncio.TimeoutError):
            return "The operation took too long to complete. Please try again with a simpler request."
        elif isinstance(error, ValueError):
            return f"Invalid value provided: {str(error)}"
        elif isinstance(error, KeyError):
            return "A required value was not found. Please check your input and try again."
        elif isinstance(error, PermissionError):
            return "You don't have permission to perform this action."
        
        # Default to category message
        return category_messages.get(category, category_messages[ErrorCategory.UNKNOWN])
    
    @staticmethod
    def async_error_handler(category: str = ErrorCategory.UNKNOWN):
        """
        Decorator for handling errors in async functions
        
        Args:
            category: Default error category for this function
            
        Returns:
            Decorated function with error handling
        """
        def decorator(func: Callable[..., Coroutine[Any, Any, R]]) -> Callable[..., Coroutine[Any, Any, Union[R, None]]]:
            @wraps(func)
            async def wrapper(*args, **kwargs) -> Union[R, None]:
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    # Extract context from kwargs if available
                    context = kwargs.get('context', {})
                    user_id = kwargs.get('user_id', None)
                    correlation_id = kwargs.get('correlation_id', None)
                    
                    # Log the error
                    ErrorHandler.log_error(
                        error=e,
                        category=category,
                        context={
                            "function": func.__name__,
                            "args": str(args),
                            "kwargs": str(kwargs),
                            **context
                        },
                        user_id=user_id,
                        correlation_id=correlation_id
                    )
                    
                    # Re-raise the exception for the caller to handle
                    raise
            return wrapper
        return decorator
    
    @staticmethod
    async def handle_interaction_error(
        interaction: discord.Interaction,
        error: Exception,
        category: str = ErrorCategory.UNKNOWN,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Handle errors in Discord interactions with standardized responses
        
        Args:
            interaction: Discord interaction object
            error: The exception that occurred
            category: Error category for classification
            context: Additional context about where/why the error occurred
        """
        # Log the error
        error_details = ErrorHandler.log_error(
            error=error,
            category=category,
            context=context,
            user_id=str(interaction.user.id) if interaction.user else None
        )
        
        # Get user-friendly message
        user_message = ErrorHandler.get_user_friendly_message(error, category)
        
        # Create error reference for support
        error_reference = f"Error ID: {error_details['error_id']}"
        
        # Send response to user
        try:
            if interaction.response.is_done():
                await interaction.followup.send(
                    f"❌ {user_message}\n\n*{error_reference}*"
                )
            else:
                await interaction.response.send_message(
                    f"❌ {user_message}\n\n*{error_reference}*"
                )
        except Exception as e:
            logger.error(f"Failed to send error message to user: {e}")
