import logging
from typing import Optional
from datetime import datetime, timedelta

from src.data.models.stock_data import AnalysisResult
from src.data.providers.manager import DataProviderManager
from src.analysis.technical.indicators import TechnicalIndicatorsCalculator
from src.analysis.fundamental.metrics import FundamentalMetricsCalculator
from src.analysis.ai.recommendation_engine import AIRecommendationEngine
from src.analysis.risk.assessment import RiskAssessmentCalculator

logger = logging.getLogger(__name__)

class AnalysisOrchestrator:
    """Main orchestrator for complete stock analysis"""
    
    def __init__(self, data_provider_manager: DataProviderManager):
        self.data_provider = data_provider_manager
        self.technical_calc = TechnicalIndicatorsCalculator()
        self.fundamental_calc = FundamentalMetricsCalculator()
        self.ai_engine = AIRecommendationEngine()
        self.risk_calc = RiskAssessmentCalculator()
    
    async def analyze_stock(self, symbol: str) -> Optional[AnalysisResult]:
        """Perform complete stock analysis"""
        try:
            logger.info(f"Starting analysis for {symbol}")
            
            # Initialize analysis result with all required fields
            analysis = AnalysisResult(
                symbol=symbol,
                timestamp=datetime.now(),
                quote=None,
                historical=None,
                technical=None,
                fundamental=None,
                risk=None,
                market_context=None,
                recommendation="HOLD",
                confidence=50,
                time_horizon="MEDIUM",
                key_insights=[],
                summary="",
                data_sources=[],
                analysis_quality="HIGH",
                confidence_factors={}
            )
            
            # 1. Get current quote
            try:
                market_data = await self.data_provider.get_market_data(symbol, data_types=["price", "volume"])
                if market_data and hasattr(market_data, 'price'):
                    # Create a StockQuote from the market data
                    from src.data.models.stock_data import StockQuote
                    quote = StockQuote(
                        symbol=symbol,
                        price=market_data.price,
                        volume=market_data.volume,
                        timestamp=datetime.now(),
                        change=market_data.change if hasattr(market_data, 'change') else None,
                        change_percent=market_data.change_percent if hasattr(market_data, 'change_percent') else None,
                        open=market_data.open if hasattr(market_data, 'open') else None,
                        high=market_data.high if hasattr(market_data, 'high') else None,
                        low=market_data.low if hasattr(market_data, 'low') else None,
                        close=market_data.price,
                        market_cap=None,
                        pe_ratio=None,
                        eps=None
                    )
                    analysis.quote = quote
                    analysis.data_sources.append("quote")
                    logger.info(f"Retrieved quote for {symbol}: ${quote.price}")
            except Exception as e:
                logger.warning(f"Failed to get quote for {symbol}: {e}")
            
            # 2. Get historical data (last 3 months)
            try:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=90)
                
                # Use the data source manager for historical data
                from src.api.data.providers.data_source_manager import DataSourceManager
                data_manager = DataSourceManager()
                historical_data = await data_manager.fetch_historical_data(symbol, days=90)
                
                if historical_data and len(historical_data) >= 50:  # Need at least 50 days
                    # Create HistoricalData from the raw data
                    from src.data.models.stock_data import HistoricalData
                    dates = [datetime.fromisoformat(item['date']) for item in historical_data if 'date' in item]
                    prices = [float(item['close']) for item in historical_data if 'close' in item]
                    volumes = [int(item['volume']) if item.get('volume') else None for item in historical_data]
                    highs = [float(item['high']) if item.get('high') else None for item in historical_data]
                    lows = [float(item['low']) if item.get('low') else None for item in historical_data]
                    opens = [float(item['open']) if item.get('open') else None for item in historical_data]
                    closes = [float(item['close']) for item in historical_data if 'close' in item]
                    
                    historical = HistoricalData(
                        symbol=symbol,
                        dates=dates,
                        prices=prices,
                        volumes=volumes,
                        highs=highs,
                        lows=lows,
                        opens=opens,
                        closes=closes
                    )
                    
                    analysis.historical = historical
                    analysis.data_sources.append("historical")
                    
                    # Calculate technical indicators
                    technical = self.technical_calc.calculate_all_indicators(historical)
                    analysis.technical = technical
                    analysis.data_sources.append("technical")
                    
                    logger.info(f"Calculated technical indicators for {symbol}")
            except Exception as e:
                logger.warning(f"Failed to get historical data for {symbol}: {e}")
            
            # 3. Get fundamental data
            try:
                # For now, skip fundamental data as it's not implemented
                logger.info(f"Skipping fundamental data for {symbol} - not implemented yet")
            except Exception as e:
                logger.warning(f"Failed to get fundamental data for {symbol}: {e}")
            
            # 4. Calculate risk assessment
            if analysis.historical and analysis.quote:
                risk = self.risk_calc.assess_risk(analysis.historical, analysis.quote)
                analysis.risk = risk
                analysis.data_sources.append("risk")
                logger.info(f"Completed risk assessment for {symbol}")
            
            # 5. Generate AI recommendation
            recommendation = self.ai_engine.generate_recommendation(analysis)
            analysis.recommendation = recommendation.action
            analysis.confidence = recommendation.confidence
            analysis.time_horizon = recommendation.time_horizon
            
            # 6. Generate key insights
            analysis.key_insights = self._generate_key_insights(analysis)
            analysis.summary = self._generate_summary(analysis, recommendation.reasoning)
            
            # 7. Set confidence factors
            analysis.confidence_factors = {
                'technical': self._calculate_technical_confidence(analysis),
                'fundamental': self._calculate_fundamental_confidence(analysis),
                'risk': self._calculate_risk_confidence(analysis),
                'data_quality': self._assess_data_quality(analysis)
            }
            
            # 8. Assess overall analysis quality
            analysis.analysis_quality = self._assess_overall_quality(analysis)
            
            logger.info(f"Completed analysis for {symbol}: {analysis.recommendation} ({analysis.confidence}%)")
            return analysis
            
        except Exception as e:
            logger.error(f"Analysis orchestration error for {symbol}: {e}")
            return None
    
    def _generate_key_insights(self, analysis: AnalysisResult) -> list:
        """Generate key insights from analysis"""
        insights = []
        
        # Technical insights
        if analysis.technical:
            tech = analysis.technical
            if tech.rsi is not None:
                if tech.rsi < 30:
                    insights.append("Oversold conditions detected")
                elif tech.rsi > 70:
                    insights.append("Overbought conditions detected")
            
            if tech.sma_50 is not None and tech.sma_200 is not None:
                if tech.sma_50 > tech.sma_200:
                    insights.append("50-day MA above 200-day MA (bullish)")
                else:
                    insights.append("50-day MA below 200-day MA (bearish)")
        
        # Fundamental insights
        if analysis.fundamental:
            fund = analysis.fundamental
            if fund.pe_ratio is not None:
                if fund.pe_ratio < 15:
                    insights.append(f"Potentially undervalued at P/E {fund.pe_ratio:.1f}")
                elif fund.pe_ratio > 25:
                    insights.append(f"Potentially overvalued at P/E {fund.pe_ratio:.1f}")
            
            if fund.revenue_growth is not None and fund.revenue_growth > 0.15:
                insights.append(f"Strong growth: {fund.revenue_growth*100:.1f}% revenue growth")
        
        # Risk insights
        if analysis.risk:
            if analysis.risk.risk_level == "HIGH":
                insights.append("High volatility - consider risk management")
            elif analysis.risk.risk_level == "LOW":
                insights.append("Low volatility - suitable for conservative investors")
        
        return insights[:5]  # Limit to top 5 insights
    
    def _generate_summary(self, analysis: AnalysisResult, reasoning: list) -> str:
        """Generate comprehensive analysis summary"""
        summary_parts = []
        
        # Current price and change
        if analysis.quote:
            change_direction = "up" if analysis.quote.change and analysis.quote.change > 0 else "down"
            summary_parts.append(
                f"Stock analysis for {analysis.symbol}: Currently trading at ${analysis.quote.price:.2f}, "
                f"{change_direction} {abs(analysis.quote.change_percent or 0):.1f}% today."
            )
        
        # Recommendation and confidence
        summary_parts.append(
            f"AI Recommendation: {analysis.recommendation} with {analysis.confidence}% confidence "
            f"for {analysis.time_horizon.lower()} term."
        )
        
        # Key technical signals
        if analysis.technical:
            signals = []
            if analysis.technical.rsi:
                if analysis.technical.rsi < 30:
                    signals.append("oversold")
                elif analysis.technical.rsi > 70:
                    signals.append("overbought")
            if signals:
                summary_parts.append(f"Technical signals: {', '.join(signals)}")
        
        # Add reasoning
        if reasoning:
            summary_parts.extend(reasoning[:2])  # Add top 2 reasons
        
        return " ".join(summary_parts)
    
    def _calculate_technical_confidence(self, analysis: AnalysisResult) -> int:
        """Calculate confidence in technical analysis"""
        confidence = 0
        factors = 0
        
        if analysis.technical:
            tech = analysis.technical
            if tech.rsi is not None:
                confidence += 80
                factors += 1
            if tech.macd is not None:
                confidence += 80
                factors += 1
            if tech.sma_50 is not None and tech.sma_200 is not None:
                confidence += 70
                factors += 1
            if tech.support_level is not None and tech.resistance_level is not None:
                confidence += 60
                factors += 1
        
        return confidence // max(factors, 1) if factors > 0 else 0
    
    def _calculate_fundamental_confidence(self, analysis: AnalysisResult) -> int:
        """Calculate confidence in fundamental analysis"""
        confidence = 0
        factors = 0
        
        if analysis.fundamental:
            fund = analysis.fundamental
            if fund.pe_ratio is not None:
                confidence += 90
                factors += 1
            if fund.eps is not None:
                confidence += 85
                factors += 1
            if fund.revenue_growth is not None:
                confidence += 80
                factors += 1
            if fund.profit_margin is not None:
                confidence += 75
                factors += 1
            if fund.debt_to_equity is not None:
                confidence += 70
                factors += 1
        
        return confidence // max(factors, 1) if factors > 0 else 0
    
    def _calculate_risk_confidence(self, analysis: AnalysisResult) -> int:
        """Calculate confidence in risk assessment"""
        confidence = 0
        factors = 0
        
        if analysis.risk:
            risk = analysis.risk
            if risk.volatility is not None:
                confidence += 85
                factors += 1
            if risk.beta is not None:
                confidence += 80
                factors += 1
            if risk.risk_warnings:
                confidence += 90
                factors += 1
        
        return confidence // max(factors, 1) if factors > 0 else 0
    
    def _assess_data_quality(self, analysis: AnalysisResult) -> int:
        """Assess overall data quality (0-100)"""
        quality = 100
        missing_data = 0
        
        # Check for missing critical data
        if not analysis.quote:
            missing_data += 25
        if not analysis.historical:
            missing_data += 20
        if not analysis.technical:
            missing_data += 15
        if not analysis.fundamental:
            missing_data += 15
        if not analysis.risk:
            missing_data += 10
        
        quality -= missing_data
        
        # Boost quality for comprehensive data
        if len(analysis.data_sources) >= 4:
            quality += 10
        
        return max(0, min(100, quality))
    
    def _assess_overall_quality(self, analysis: AnalysisResult) -> str:
        """Assess overall analysis quality"""
        data_quality = self._assess_data_quality(analysis)
        technical_conf = self._calculate_technical_confidence(analysis)
        fundamental_conf = self._calculate_fundamental_confidence(analysis)
        
        avg_confidence = (technical_conf + fundamental_conf + data_quality) / 3
        
        if avg_confidence >= 80:
            return "HIGH"
        elif avg_confidence >= 60:
            return "MEDIUM"
        else:
            return "LOW"
