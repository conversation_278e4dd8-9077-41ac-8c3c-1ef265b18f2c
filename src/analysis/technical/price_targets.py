"""
Price Target Analysis Engine

Provides comprehensive price target calculations including:
- Fibonacci retracement/extensions
- Volume profile analysis
- Trend strength measurement
- Historical pattern recognition
- Volatility-adjusted targets
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, NamedTuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class TrendDirection(Enum):
    """Trend direction enumeration"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    UNKNOWN = "unknown"

class TargetType(Enum):
    """Price target type enumeration"""
    CONSERVATIVE = "conservative"  # 70% probability
    MODERATE = "moderate"         # 50% probability
    AGGRESSIVE = "aggressive"     # 30% probability
    STOP_LOSS = "stop_loss"       # Risk management

@dataclass
class PriceTargets:
    """Container for price target analysis results"""
    symbol: str
    timeframe: str
    current_price: float
    conservative_target: float
    moderate_target: float
    aggressive_target: float
    stop_loss: float
    confidence_factors: List[str]
    trend_direction: TrendDirection
    trend_strength: float  # 0.0 to 1.0
    volatility: float
    support_levels: List[float]
    resistance_levels: List[float]
    volume_profile: Dict[str, float]
    fibonacci_levels: Dict[str, float]
    timestamp: str

class FibonacciCalculator:
    """Calculates Fibonacci retracement and extension levels"""
    
    FIBONACCI_RATIOS = {
        'retracement': [0.236, 0.382, 0.500, 0.618, 0.786],
        'extension': [1.272, 1.618, 2.000, 2.618, 3.000]
    }
    
    def calculate_levels(self, swing_high: float, swing_low: float, 
                         trend: TrendDirection) -> Dict[str, float]:
        """
        Calculate Fibonacci retracement and extension levels
        
        Args:
            swing_high: Highest price in the swing
            swing_low: Lowest price in the swing
            trend: Current trend direction
            
        Returns:
            Dictionary of Fibonacci levels
        """
        try:
            price_range = swing_high - swing_low
            
            if trend == TrendDirection.BULLISH:
                # Bullish trend: price moving up from swing low
                levels = {}
                for ratio in self.FIBONACCI_RATIOS['retracement']:
                    levels[f'retracement_{ratio}'] = swing_high - (price_range * ratio)
                for ratio in self.FIBONACCI_RATIOS['extension']:
                    levels[f'extension_{ratio}'] = swing_high + (price_range * ratio)
                    
            elif trend == TrendDirection.BEARISH:
                # Bearish trend: price moving down from swing high
                levels = {}
                for ratio in self.FIBONACCI_RATIOS['retracement']:
                    levels[f'retracement_{ratio}'] = swing_low + (price_range * ratio)
                for ratio in self.FIBONACCI_RATIOS['extension']:
                    levels[f'extension_{ratio}'] = swing_low - (price_range * ratio)
                    
            else:
                # Sideways trend: use both directions
                levels = {}
                for ratio in self.FIBONACCI_RATIOS['retracement']:
                    levels[f'retracement_{ratio}_up'] = swing_high - (price_range * ratio)
                    levels[f'retracement_{ratio}_down'] = swing_low + (price_range * ratio)
                    
            return levels
            
        except Exception as e:
            logger.error(f"Error calculating Fibonacci levels: {e}")
            return {}

class VolumeProfileAnalyzer:
    """Analyzes volume profile to identify key price levels"""
    
    def analyze_volume_profile(self, price_data: pd.DataFrame, 
                              volume_data: pd.DataFrame) -> Dict[str, float]:
        """
        Analyze volume profile to identify key price levels
        
        Args:
            price_data: DataFrame with OHLCV data
            volume_data: DataFrame with volume data
            
        Returns:
            Dictionary of volume-weighted price levels
        """
        try:
            if price_data.empty or volume_data.empty:
                return {}
                
            # Combine price and volume data
            combined = pd.concat([price_data, volume_data], axis=1)
            combined = combined.dropna()
            
            if combined.empty:
                return {}
            
            # Calculate volume-weighted average price (VWAP)
            vwap = (combined['close'] * combined['volume']).sum() / combined['volume'].sum()
            
            # Find high volume price levels
            volume_threshold = combined['volume'].quantile(0.8)  # Top 20% volume
            high_volume_levels = combined[combined['volume'] >= volume_threshold]
            
            # Group by price ranges and sum volumes
            price_bins = pd.cut(combined['close'], bins=20)
            volume_by_price = combined.groupby(price_bins)['volume'].sum()
            
            # Find peaks in volume distribution
            volume_peaks = volume_by_price.nlargest(5)
            
            result = {
                'vwap': vwap,
                'high_volume_threshold': volume_threshold,
                'volume_peaks': volume_peaks.to_dict()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing volume profile: {e}")
            return {}

class TrendStrengthAnalyzer:
    """Measures trend strength using multiple indicators"""
    
    def calculate_trend_strength(self, price_data: pd.DataFrame, 
                                timeframe: str) -> Tuple[TrendDirection, float]:
        """
        Calculate trend strength and direction
        
        Args:
            price_data: DataFrame with OHLCV data
            timeframe: Analysis timeframe
            
        Returns:
            Tuple of (trend_direction, strength_score)
        """
        try:
            if price_data.empty:
                return TrendDirection.UNKNOWN, 0.0
            
            # Calculate moving averages
            sma_20 = price_data['close'].rolling(window=20).mean()
            sma_50 = price_data['close'].rolling(window=50).mean()
            
            # Calculate ADX for trend strength (if enough data)
            if len(price_data) >= 14:
                adx = self._calculate_adx(price_data)
                trend_strength = min(adx / 100.0, 1.0)  # Normalize to 0-1
            else:
                trend_strength = 0.5  # Default moderate strength
            
            # Determine trend direction
            current_price = price_data['close'].iloc[-1]
            current_sma_20 = sma_20.iloc[-1]
            current_sma_50 = sma_50.iloc[-1]
            
            if current_price > current_sma_20 > current_sma_50:
                direction = TrendDirection.BULLISH
            elif current_price < current_sma_20 < current_sma_50:
                direction = TrendDirection.BEARISH
            else:
                direction = TrendDirection.SIDEWAYS
                
            return direction, trend_strength
            
        except Exception as e:
            logger.error(f"Error calculating trend strength: {e}")
            return TrendDirection.UNKNOWN, 0.0
    
    def _calculate_adx(self, price_data: pd.DataFrame) -> float:
        """Calculate Average Directional Index (ADX)"""
        try:
            # Simplified ADX calculation
            high = price_data['high']
            low = price_data['low']
            close = price_data['close']
            
            # Calculate True Range
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # Calculate Directional Movement
            dm_plus = high - high.shift(1)
            dm_minus = low.shift(1) - low
            
            dm_plus = dm_plus.where((dm_plus > dm_minus) & (dm_plus > 0), 0)
            dm_minus = dm_minus.where((dm_minus > dm_plus) & (dm_minus > 0), 0)
            
            # Calculate smoothed values
            tr_smooth = tr.rolling(window=14).mean()
            dm_plus_smooth = dm_plus.rolling(window=14).mean()
            dm_minus_smooth = dm_minus.rolling(window=14).mean()
            
            # Calculate DI+ and DI-
            di_plus = 100 * (dm_plus_smooth / tr_smooth)
            di_minus = 100 * (dm_minus_smooth / tr_smooth)
            
            # Calculate ADX
            dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
            adx = dx.rolling(window=14).mean()
            
            return adx.iloc[-1] if not pd.isna(adx.iloc[-1]) else 25.0
            
        except Exception as e:
            logger.error(f"Error calculating ADX: {e}")
            return 25.0  # Default neutral value

class HistoricalPatternRecognizer:
    """Recognizes historical price patterns for target validation"""
    
    def find_similar_patterns(self, price_data: pd.DataFrame, 
                             current_pattern: Dict) -> List[Dict]:
        """
        Find similar historical patterns
        
        Args:
            price_data: Historical price data
            current_pattern: Current pattern characteristics
            
        Returns:
            List of similar historical patterns
        """
        try:
            # This is a simplified pattern recognition
            # In production, you'd want more sophisticated pattern matching
            
            patterns = []
            
            # Look for similar price movements
            if len(price_data) >= 20:
                # Find periods with similar volatility
                current_volatility = price_data['close'].pct_change().std()
                
                for i in range(20, len(price_data) - 20):
                    window = price_data.iloc[i-20:i+20]
                    window_volatility = window['close'].pct_change().std()
                    
                    # Check if volatility is similar
                    if abs(window_volatility - current_volatility) < 0.1:
                        patterns.append({
                            'start_date': window.index[0],
                            'end_date': window.index[-1],
                            'volatility': window_volatility,
                            'price_change': (window['close'].iloc[-1] / window['close'].iloc[0]) - 1
                        })
            
            return patterns[:5]  # Return top 5 patterns
            
        except Exception as e:
            logger.error(f"Error finding similar patterns: {e}")
            return []

class PriceTargetEngine:
    """Main engine for calculating comprehensive price targets"""
    
    def __init__(self):
        self.fibonacci_calc = FibonacciCalculator()
        self.volume_analyzer = VolumeProfileAnalyzer()
        self.trend_analyzer = TrendStrengthAnalyzer()
        self.pattern_recognizer = HistoricalPatternRecognizer()
        
    def calculate_targets(self, symbol: str, timeframe: str, 
                         price_data: pd.DataFrame, volume_data: pd.DataFrame) -> PriceTargets:
        """
        Calculate comprehensive price targets
        
        Args:
            symbol: Stock symbol
            timeframe: Analysis timeframe
            price_data: OHLCV price data
            volume_data: Volume data
            
        Returns:
            PriceTargets object with all analysis results
        """
        try:
            if price_data.empty:
                raise ValueError("Price data is empty")
            
            current_price = price_data['close'].iloc[-1]
            
            # Calculate trend strength and direction
            trend_direction, trend_strength = self.trend_analyzer.calculate_trend_strength(
                price_data, timeframe
            )
            
            # Calculate volatility
            volatility = price_data['close'].pct_change().std()
            
            # Find swing points for Fibonacci calculations
            swing_high = price_data['high'].max()
            swing_low = price_data['low'].min()
            
            # Calculate Fibonacci levels
            fibonacci_levels = self.fibonacci_calc.calculate_levels(
                swing_high, swing_low, trend_direction
            )
            
            # Analyze volume profile
            volume_profile = self.volume_analyzer.analyze_volume_profile(
                price_data, volume_data
            )
            
            # Calculate price targets based on trend and Fibonacci levels
            targets = self._calculate_price_targets(
                current_price, trend_direction, trend_strength, 
                fibonacci_levels, volume_profile, volatility
            )
            
            # Find support and resistance levels
            support_levels, resistance_levels = self._find_support_resistance(
                price_data, volume_profile
            )
            
            # Generate confidence factors
            confidence_factors = self._generate_confidence_factors(
                trend_strength, volatility, volume_profile, targets
            )
            
            return PriceTargets(
                symbol=symbol,
                timeframe=timeframe,
                current_price=current_price,
                conservative_target=targets['conservative'],
                moderate_target=targets['moderate'],
                aggressive_target=targets['aggressive'],
                stop_loss=targets['stop_loss'],
                confidence_factors=confidence_factors,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                volatility=volatility,
                support_levels=support_levels,
                resistance_levels=resistance_levels,
                volume_profile=volume_profile,
                fibonacci_levels=fibonacci_levels,
                timestamp=pd.Timestamp.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"Error calculating price targets for {symbol}: {e}")
            raise
    
    def _calculate_price_targets(self, current_price: float, trend_direction: TrendDirection,
                                trend_strength: float, fibonacci_levels: Dict[str, float],
                                volume_profile: Dict[str, float], volatility: float) -> Dict[str, float]:
        """Calculate specific price targets"""
        
        # Base multipliers based on trend strength
        base_multiplier = 1.0 + (trend_strength * 0.5)  # 1.0 to 1.5
        
        if trend_direction == TrendDirection.BULLISH:
            conservative_target = current_price * (1.0 + (0.05 * base_multiplier))
            moderate_target = current_price * (1.0 + (0.10 * base_multiplier))
            aggressive_target = current_price * (1.0 + (0.20 * base_multiplier))
            stop_loss = current_price * (1.0 - (0.03 * base_multiplier))
            
        elif trend_direction == TrendDirection.BEARISH:
            conservative_target = current_price * (1.0 - (0.05 * base_multiplier))
            moderate_target = current_price * (1.0 - (0.10 * base_multiplier))
            aggressive_target = current_price * (1.0 - (0.20 * base_multiplier))
            stop_loss = current_price * (1.0 + (0.03 * base_multiplier))
            
        else:  # Sideways
            conservative_target = current_price * (1.0 + (0.02 * base_multiplier))
            moderate_target = current_price * (1.0 + (0.05 * base_multiplier))
            aggressive_target = current_price * (1.0 + (0.10 * base_multiplier))
            stop_loss = current_price * (1.0 - (0.02 * base_multiplier))
        
        # Adjust targets based on Fibonacci levels if available
        if fibonacci_levels:
            # Use nearest Fibonacci level as a guide
            fib_levels = [v for v in fibonacci_levels.values() if isinstance(v, (int, float))]
            if fib_levels:
                nearest_fib = min(fib_levels, key=lambda x: abs(x - current_price))
                if trend_direction == TrendDirection.BULLISH and nearest_fib > current_price:
                    moderate_target = nearest_fib
                elif trend_direction == TrendDirection.BEARISH and nearest_fib < current_price:
                    moderate_target = nearest_fib
        
        return {
            'conservative': round(conservative_target, 2),
            'moderate': round(moderate_target, 2),
            'aggressive': round(aggressive_target, 2),
            'stop_loss': round(stop_loss, 2)
        }
    
    def _find_support_resistance(self, price_data: pd.DataFrame, 
                                volume_profile: Dict[str, float]) -> Tuple[List[float], List[float]]:
        """Find support and resistance levels"""
        
        support_levels = []
        resistance_levels = []
        
        try:
            # Use VWAP as a key level
            if 'vwap' in volume_profile:
                vwap = volume_profile['vwap']
                if vwap < price_data['close'].iloc[-1]:
                    support_levels.append(round(vwap, 2))
                else:
                    resistance_levels.append(round(vwap, 2))
            
            # Find recent highs and lows
            recent_highs = price_data['high'].nlargest(3)
            recent_lows = price_data['low'].nsmallest(3)
            
            resistance_levels.extend([round(level, 2) for level in recent_highs.values])
            support_levels.extend([round(level, 2) for level in recent_lows.values])
            
            # Remove duplicates and sort
            support_levels = sorted(list(set(support_levels)))
            resistance_levels = sorted(list(set(resistance_levels)))
            
        except Exception as e:
            logger.error(f"Error finding support/resistance: {e}")
        
        return support_levels, resistance_levels
    
    def _generate_confidence_factors(self, trend_strength: float, volatility: float,
                                   volume_profile: Dict[str, float], 
                                   targets: Dict[str, float]) -> List[str]:
        """Generate confidence factors for the analysis"""
        
        factors = []
        
        # Trend strength factor
        if trend_strength > 0.7:
            factors.append("Strong trend confirmation")
        elif trend_strength > 0.4:
            factors.append("Moderate trend strength")
        else:
            factors.append("Weak trend - exercise caution")
        
        # Volatility factor
        if volatility < 0.02:
            factors.append("Low volatility - stable price action")
        elif volatility < 0.05:
            factors.append("Moderate volatility - normal market conditions")
        else:
            factors.append("High volatility - increased risk")
        
        # Volume factor
        if volume_profile and 'vwap' in volume_profile:
            factors.append("Volume profile analysis available")
        
        # Target spacing factor
        target_spacing = abs(targets['moderate'] - targets['conservative']) / targets['conservative']
        if target_spacing < 0.05:
            factors.append("Tight target spacing - high confidence")
        elif target_spacing < 0.10:
            factors.append("Moderate target spacing - medium confidence")
        else:
            factors.append("Wide target spacing - lower confidence")
        
        return factors 