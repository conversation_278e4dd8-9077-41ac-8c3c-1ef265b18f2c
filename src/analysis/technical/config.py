"""
Central Technical Analysis Configuration

Provides a unified configuration system for all technical analysis parameters
with environment variable overrides and sensible defaults.
"""

import os
from typing import List, Dict, Any
from dataclasses import dataclass, field

@dataclass
class TechnicalConfig:
    """Central configuration for technical analysis parameters"""
    
    # RSI Configuration
    rsi_period: int = field(default=14)
    rsi_overbought: float = field(default=70.0)
    rsi_oversold: float = field(default=30.0)
    
    # MACD Configuration
    macd_fast_period: int = field(default=12)
    macd_slow_period: int = field(default=26)
    macd_signal_period: int = field(default=9)
    
    # Moving Average Configuration
    sma_periods: List[int] = field(default_factory=lambda: [20, 50, 200])
    ema_periods: List[int] = field(default_factory=lambda: [12, 26])
    
    # Bollinger Bands Configuration
    bb_period: int = field(default=20)
    bb_std_dev: float = field(default=2.0)
    
    # Volume Analysis Configuration
    volume_sma_period: int = field(default=20)
    
    # Volatility Configuration
    volatility_period: int = field(default=20)
    atr_period: int = field(default=14)
    
    # Support/Resistance Configuration
    sr_lookback_period: int = field(default=20)
    
    # Trend Analysis Configuration
    trend_analysis_periods: List[int] = field(default_factory=lambda: [20, 50, 200])
    
    def __post_init__(self):
        """Initialize configuration with environment variable overrides"""
        # RSI Configuration
        self.rsi_period = int(os.getenv('TECH_RSI_PERIOD', self.rsi_period))
        self.rsi_overbought = float(os.getenv('TECH_RSI_OVERBOUGHT', self.rsi_overbought))
        self.rsi_oversold = float(os.getenv('TECH_RSI_OVERSOLD', self.rsi_oversold))
        
        # MACD Configuration
        self.macd_fast_period = int(os.getenv('TECH_MACD_FAST_PERIOD', self.macd_fast_period))
        self.macd_slow_period = int(os.getenv('TECH_MACD_SLOW_PERIOD', self.macd_slow_period))
        self.macd_signal_period = int(os.getenv('TECH_MACD_SIGNAL_PERIOD', self.macd_signal_period))
        
        # Bollinger Bands Configuration
        self.bb_period = int(os.getenv('TECH_BB_PERIOD', self.bb_period))
        self.bb_std_dev = float(os.getenv('TECH_BB_STD_DEV', self.bb_std_dev))
        
        # Volume Analysis Configuration
        self.volume_sma_period = int(os.getenv('TECH_VOLUME_SMA_PERIOD', self.volume_sma_period))
        
        # Volatility Configuration
        self.volatility_period = int(os.getenv('TECH_VOLATILITY_PERIOD', self.volatility_period))
        self.atr_period = int(os.getenv('TECH_ATR_PERIOD', self.atr_period))
        
        # Support/Resistance Configuration
        self.sr_lookback_period = int(os.getenv('TECH_SR_LOOKBACK_PERIOD', self.sr_lookback_period))

# Global technical configuration instance
tech_config = TechnicalConfig()

def get_tech_config() -> TechnicalConfig:
    """Get the global technical configuration instance"""
    return tech_config

# Factory functions for calculators
def create_rsi_calculator():
    """Create RSI calculator with configured parameters"""
    from src.analysis.technical.calculators.rsi_calculator import create_rsi_calculator as factory
    config = get_tech_config()
    return factory(
        period=config.rsi_period,
        overbought=config.rsi_overbought,
        oversold=config.rsi_oversold
    )

def create_macd_calculator():
    """Create MACD calculator with configured parameters"""
    from src.analysis.technical.calculators.macd_calculator import create_macd_calculator as factory
    config = get_tech_config()
    return factory(
        fast_period=config.macd_fast_period,
        slow_period=config.macd_slow_period,
        signal_period=config.macd_signal_period
    )

def create_volatility_calculator():
    """Create Volatility calculator with configured parameters"""
    from src.analysis.risk.calculators.volatility_calculator import create_volatility_calculator as factory
    return factory()

def create_beta_calculator():
    """Create Beta calculator with configured parameters"""
    from src.analysis.risk.calculators.beta_calculator import create_beta_calculator as factory
    return factory()