from typing import List, Optional, Dict, Tuple
import logging
import numpy as np
from dataclasses import dataclass

# Import central configuration
from src.analysis.technical.config import get_tech_config

logger = logging.getLogger(__name__)

@dataclass
class MACDAnalysis:
    """Data class for MACD analysis results"""
    macd_line: Optional[float]
    signal_line: Optional[float]
    histogram: Optional[float]
    signal: str
    strength: str
    confidence: float

class MACDCalculator:
    """Calculator for Moving Average Convergence Divergence (MACD) technical indicator"""
    
    def __init__(self, fast_period: int = None, slow_period: int = None, signal_period: int = None):
        """
        Initialize MACD calculator with optional parameters (uses central config if not provided)
        
        Args:
            fast_period: Fast EMA period (default from central config)
            slow_period: Slow EMA period (default from central config)
            signal_period: Signal line EMA period (default from central config)
        """
        # Get central configuration
        config = get_tech_config()
        
        # Use provided values or fall back to central config
        self.fast_period = fast_period if fast_period is not None else config.macd_fast_period
        self.slow_period = slow_period if slow_period is not None else config.macd_slow_period
        self.signal_period = signal_period if signal_period is not None else config.macd_signal_period
        self.min_data_points = self.slow_period + self.signal_period
    
    def calculate_macd(self, prices: List[float]) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """
        Calculate MACD line, signal line, and histogram
        
        Args:
            prices: List of closing prices in chronological order
            
        Returns:
            Tuple of (macd_line, signal_line, histogram) or (None, None, None) if insufficient data
        """
        if len(prices) < self.min_data_points:
            return None, None, None
            
        try:
            # Calculate EMAs
            fast_ema = self._calculate_ema(prices, self.fast_period)
            slow_ema = self._calculate_ema(prices, self.slow_period)
            
            if fast_ema is None or slow_ema is None:
                return None, None, None
                
            # MACD line = Fast EMA - Slow EMA
            macd_line = fast_ema - slow_ema
            
            # Calculate signal line (EMA of MACD line)
            # For signal line, we need to calculate MACD values for the signal period
            macd_values = []
            for i in range(len(prices)):
                if i >= self.slow_period - 1:
                    window_prices = prices[i-self.slow_period+1:i+1]
                    fast_ema_val = self._calculate_ema(window_prices, self.fast_period)
                    slow_ema_val = self._calculate_ema(window_prices, self.slow_period)
                    if fast_ema_val is not None and slow_ema_val is not None:
                        macd_val = fast_ema_val - slow_ema_val
                        macd_values.append(macd_val)
            
            if len(macd_values) < self.signal_period:
                return None, None, None
                
            signal_line = self._calculate_ema(macd_values, self.signal_period)
            
            if signal_line is None:
                return None, None, None
                
            # Histogram = MACD line - Signal line
            histogram = macd_line - signal_line
            
            return macd_line, signal_line, histogram
            
        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return None, None, None
    
    def analyze_macd(self, prices: List[float]) -> MACDAnalysis:
        """
        Comprehensive MACD analysis
        
        Args:
            prices: List of closing prices
            
        Returns:
            MACDAnalysis object with detailed analysis
        """
        macd_line, signal_line, histogram = self.calculate_macd(prices)
        
        if macd_line is None or signal_line is None or histogram is None:
            return MACDAnalysis(
                macd_line=None,
                signal_line=None,
                histogram=None,
                signal="INSUFFICIENT_DATA",
                strength="NONE",
                confidence=0.0
            )
        
        # Determine signal and strength
        signal, strength = self._determine_signal(macd_line, signal_line, histogram)
        
        # Calculate confidence
        confidence = self._calculate_confidence(prices)
        
        return MACDAnalysis(
            macd_line=macd_line,
            signal_line=signal_line,
            histogram=histogram,
            signal=signal,
            strength=strength,
            confidence=confidence
        )
    
    def calculate_rolling_macd(self, prices: List[float]) -> List[Tuple[Optional[float], Optional[float], Optional[float]]]:
        """
        Calculate rolling MACD values
        
        Args:
            prices: List of closing prices
            
        Returns:
            List of (macd_line, signal_line, histogram) tuples
        """
        macd_results = []
        for i in range(len(prices)):
            if i < self.min_data_points:
                macd_results.append((None, None, None))
            else:
                window_prices = prices[i-self.min_data_points+1:i+1]
                macd_line, signal_line, histogram = self.calculate_macd(window_prices)
                macd_results.append((macd_line, signal_line, histogram))
                
        return macd_results
    
    def _calculate_ema(self, prices: List[float], period: int) -> Optional[float]:
        """
        Calculate Exponential Moving Average (EMA)
        
        Args:
            prices: List of prices
            period: EMA period
            
        Returns:
            EMA value or None if insufficient data
        """
        if len(prices) < period:
            return None
            
        try:
            # Calculate smoothing factor
            alpha = 2 / (period + 1)
            
            # Start with SMA for the first value
            ema_values = [sum(prices[:period]) / period]
            
            # Calculate EMA for subsequent values
            for i in range(period, len(prices)):
                ema = alpha * prices[i] + (1 - alpha) * ema_values[-1]
                ema_values.append(ema)
                
            return ema_values[-1]
            
        except Exception as e:
            logger.error(f"Error calculating EMA: {e}")
            return None
    
    def _determine_signal(self, macd_line: float, signal_line: float, histogram: float) -> tuple:
        """
        Determine trading signal based on MACD values
        
        Args:
            macd_line: MACD line value
            signal_line: Signal line value
            histogram: Histogram value
            
        Returns:
            Tuple of (signal, strength)
        """
        # Bullish signals
        if macd_line > signal_line and histogram > 0:
            if histogram > abs(macd_line) * 0.1:  # Strong bullish momentum
                return "BULLISH", "STRONG"
            else:
                return "BULLISH", "MODERATE"
        # Bearish signals
        elif macd_line < signal_line and histogram < 0:
            if abs(histogram) > abs(macd_line) * 0.1:  # Strong bearish momentum
                return "BEARISH", "STRONG"
            else:
                return "BEARISH", "MODERATE"
        # Neutral or crossover signals
        else:
            # Recent crossover detection
            if abs(histogram) < abs(macd_line) * 0.05:  # Very close to crossover
                if macd_line > signal_line:
                    return "BULLISH_CROSSOVER", "WEAK"
                else:
                    return "BEARISH_CROSSOVER", "WEAK"
            else:
                return "NEUTRAL", "NONE"
    
    def _calculate_confidence(self, prices: List[float]) -> float:
        """
        Calculate analysis confidence
        
        Args:
            prices: Price data
            
        Returns:
            Confidence score (0-1)
        """
        if len(prices) < self.min_data_points * 1.5:
            return 0.4
        elif len(prices) < self.min_data_points * 2:
            return 0.6
        elif len(prices) < self.min_data_points * 3:
            return 0.8
        else:
            return 0.95

# Factory function
def create_macd_calculator(fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> MACDCalculator:
    """Create and return a MACDCalculator instance"""
    return MACDCalculator(fast_period, slow_period, signal_period)