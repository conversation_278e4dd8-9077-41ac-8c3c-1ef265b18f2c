"""
Multi-Timeframe Confirmation Analyzer

Provides comprehensive timeframe analysis including:
- Timeframe agreement scoring
- Signal conflict resolution
- Weighted probability calculation
- Confidence level assessment
- Cross-timeframe validation
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class TimeframeBias(Enum):
    """Timeframe bias enumeration"""
    STRONG_BULLISH = "strong_bullish"
    BULLISH = "bullish"
    WEAK_BULLISH = "weak_bullish"
    NEUTRAL = "neutral"
    WEAK_BEARISH = "weak_bearish"
    BEARISH = "bearish"
    STRONG_BEARISH = "strong_bearish"

class SignalStrength(Enum):
    """Signal strength enumeration"""
    VERY_WEAK = "very_weak"      # 0-20%
    WEAK = "weak"                # 20-40%
    MODERATE = "moderate"        # 40-60%
    STRONG = "strong"            # 60-80%
    VERY_STRONG = "very_strong"  # 80-100%

@dataclass
class TimeframeAnalysis:
    """Container for timeframe analysis results"""
    symbol: str
    timeframe: str
    bias: TimeframeBias
    signal_strength: SignalStrength
    confidence: float
    key_levels: List[float]
    trend_direction: str
    momentum: float
    volume_profile: Dict[str, float]
    timestamp: str

@dataclass
class TimeframeConfirmation:
    """Container for multi-timeframe confirmation results"""
    symbol: str
    short_term_bias: TimeframeBias
    medium_term_bias: TimeframeBias
    long_term_bias: TimeframeBias
    agreement_score: float
    conflicting_signals: List[str]
    weighted_probability: Dict[str, float]
    overall_confidence: float
    timeframe_analysis: Dict[str, TimeframeAnalysis]
    recommendation: str
    timestamp: str

class TimeframeAnalyzer:
    """Analyzes individual timeframes for bias and signals"""
    
    TIMEFRAME_WEIGHTS = {
        '1m': 0.05,   # Very short term
        '5m': 0.10,   # Short term
        '15m': 0.15,  # Short term
        '1h': 0.20,   # Medium term
        '4h': 0.25,   # Medium term
        '1d': 0.15,   # Long term
        '1w': 0.10    # Very long term
    }
    
    def analyze_timeframe(self, symbol: str, timeframe: str, 
                         price_data: pd.DataFrame, 
                         volume_data: pd.DataFrame) -> TimeframeAnalysis:
        """
        Analyze a single timeframe
        
        Args:
            symbol: Stock symbol
            timeframe: Timeframe to analyze
            price_data: OHLCV price data
            volume_data: Volume data
            
        Returns:
            TimeframeAnalysis object
        """
        try:
            if price_data.empty:
                raise ValueError(f"Price data is empty for {timeframe}")
            
            # Calculate bias
            bias = self._calculate_bias(price_data, timeframe)
            
            # Calculate signal strength
            signal_strength = self._calculate_signal_strength(price_data, volume_data)
            
            # Calculate confidence
            confidence = self._calculate_confidence(price_data, volume_data, timeframe)
            
            # Find key levels
            key_levels = self._find_key_levels(price_data, volume_data)
            
            # Determine trend direction
            trend_direction = self._determine_trend_direction(price_data)
            
            # Calculate momentum
            momentum = self._calculate_momentum(price_data)
            
            # Analyze volume profile
            volume_profile = self._analyze_volume_profile(price_data, volume_data)
            
            return TimeframeAnalysis(
                symbol=symbol,
                timeframe=timeframe,
                bias=bias,
                signal_strength=signal_strength,
                confidence=confidence,
                key_levels=key_levels,
                trend_direction=trend_direction,
                momentum=momentum,
                volume_profile=volume_profile,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"Error analyzing timeframe {timeframe} for {symbol}: {e}")
            raise
    
    def _calculate_bias(self, price_data: pd.DataFrame, timeframe: str) -> TimeframeBias:
        """Calculate timeframe bias"""
        
        try:
            close_prices = price_data['close']
            
            if len(close_prices) < 10:
                return TimeframeBias.NEUTRAL
            
            # Calculate moving averages
            if len(close_prices) >= 20:
                sma_20 = close_prices.rolling(window=20).mean()
                current_price = close_prices.iloc[-1]
                current_sma = sma_20.iloc[-1]
                
                # Calculate price position relative to SMA
                price_position = (current_price - current_sma) / current_sma
                
                # Determine bias based on position and trend
                if price_position > 0.05:  # 5% above SMA
                    return TimeframeBias.STRONG_BULLISH
                elif price_position > 0.02:  # 2% above SMA
                    return TimeframeBias.BULLISH
                elif price_position > -0.02:  # Within 2% of SMA
                    return TimeframeBias.NEUTRAL
                elif price_position > -0.05:  # 2% below SMA
                    return TimeframeBias.BEARISH
                else:  # More than 5% below SMA
                    return TimeframeBias.STRONG_BEARISH
            
            # Fallback for insufficient data
            recent_change = (close_prices.iloc[-1] / close_prices.iloc[0]) - 1
            
            if recent_change > 0.02:
                return TimeframeBias.BULLISH
            elif recent_change < -0.02:
                return TimeframeBias.BEARISH
            else:
                return TimeframeBias.NEUTRAL
                
        except Exception as e:
            logger.error(f"Error calculating bias: {e}")
            return TimeframeBias.NEUTRAL
    
    def _calculate_signal_strength(self, price_data: pd.DataFrame, 
                                  volume_data: pd.DataFrame) -> SignalStrength:
        """Calculate signal strength"""
        
        try:
            close_prices = price_data['close']
            
            if len(close_prices) < 10:
                return SignalStrength.MODERATE
            
            # Calculate volatility
            volatility = close_prices.pct_change().std()
            
            # Calculate volume strength
            volume_strength = 0.5
            if not volume_data.empty:
                avg_volume = volume_data['volume'].mean()
                if avg_volume > 1000000:  # High volume
                    volume_strength = 0.8
                elif avg_volume > 100000:  # Medium volume
                    volume_strength = 0.6
                else:  # Low volume
                    volume_strength = 0.3
            
            # Calculate trend consistency
            trend_consistency = 0.5
            if len(close_prices) >= 20:
                sma_20 = close_prices.rolling(window=20).mean()
                above_sma = sum(1 for i in range(10) if close_prices.iloc[-(i+1)] > sma_20.iloc[-(i+1)])
                trend_consistency = above_sma / 10
            
            # Combine factors
            strength_score = (volume_strength * 0.4 + 
                            (1 - volatility * 10) * 0.3 + 
                            trend_consistency * 0.3)
            
            # Convert to enum
            if strength_score > 0.8:
                return SignalStrength.VERY_STRONG
            elif strength_score > 0.6:
                return SignalStrength.STRONG
            elif strength_score > 0.4:
                return SignalStrength.MODERATE
            elif strength_score > 0.2:
                return SignalStrength.WEAK
            else:
                return SignalStrength.VERY_WEAK
                
        except Exception as e:
            logger.error(f"Error calculating signal strength: {e}")
            return SignalStrength.MODERATE
    
    def _calculate_confidence(self, price_data: pd.DataFrame, 
                             volume_data: pd.DataFrame, timeframe: str) -> float:
        """Calculate confidence level for the timeframe"""
        
        try:
            # Data quality confidence
            data_confidence = min(1.0, len(price_data) / 100)
            
            # Volume confidence
            volume_confidence = 0.5
            if not volume_data.empty:
                avg_volume = volume_data['volume'].mean()
                if avg_volume > 1000000:
                    volume_confidence = 0.9
                elif avg_volume > 100000:
                    volume_confidence = 0.7
                else:
                    volume_confidence = 0.4
            
            # Timeframe-specific confidence
            timeframe_confidence = self.TIMEFRAME_WEIGHTS.get(timeframe, 0.15)
            
            # Combine confidence factors
            confidence = (data_confidence * 0.4 + 
                         volume_confidence * 0.4 + 
                         timeframe_confidence * 0.2)
            
            return max(0.1, min(0.9, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5
    
    def _find_key_levels(self, price_data: pd.DataFrame, 
                         volume_data: pd.DataFrame) -> List[float]:
        """Find key price levels"""
        
        try:
            levels = []
            
            # Add recent highs and lows
            if len(price_data) >= 20:
                recent_highs = price_data['high'].nlargest(3)
                recent_lows = price_data['low'].nsmallest(3)
                
                levels.extend([round(level, 2) for level in recent_highs.values])
                levels.extend([round(level, 2) for level in recent_lows.values])
            
            # Add VWAP if available
            if not volume_data.empty and len(price_data) >= 10:
                vwap = (price_data['close'] * volume_data['volume']).sum() / volume_data['volume'].sum()
                levels.append(round(vwap, 2))
            
            # Remove duplicates and sort
            levels = sorted(list(set(levels)))
            
            return levels[:5]  # Return top 5 levels
            
        except Exception as e:
            logger.error(f"Error finding key levels: {e}")
            return []
    
    def _determine_trend_direction(self, price_data: pd.DataFrame) -> str:
        """Determine trend direction"""
        
        try:
            close_prices = price_data['close']
            
            if len(close_prices) < 10:
                return "unknown"
            
            # Calculate trend using linear regression
            x = np.arange(len(close_prices))
            y = close_prices.values
            
            if len(x) > 1:
                slope = np.polyfit(x, y, 1)[0]
                
                if slope > 0.01:
                    return "uptrend"
                elif slope < -0.01:
                    return "downtrend"
                else:
                    return "sideways"
            
            return "unknown"
            
        except Exception as e:
            logger.error(f"Error determining trend direction: {e}")
            return "unknown"
    
    def _calculate_momentum(self, price_data: pd.DataFrame) -> float:
        """Calculate momentum indicator"""
        
        try:
            close_prices = price_data['close']
            
            if len(close_prices) < 10:
                return 0.0
            
            # Calculate rate of change
            momentum = (close_prices.iloc[-1] / close_prices.iloc[-10]) - 1
            
            return round(momentum, 4)
            
        except Exception as e:
            logger.error(f"Error calculating momentum: {e}")
            return 0.0
    
    def _analyze_volume_profile(self, price_data: pd.DataFrame, 
                               volume_data: pd.DataFrame) -> Dict[str, float]:
        """Analyze volume profile"""
        
        try:
            profile = {}
            
            if volume_data.empty:
                return profile
            
            # Calculate volume metrics
            profile['avg_volume'] = volume_data['volume'].mean()
            profile['volume_trend'] = volume_data['volume'].pct_change().mean()
            profile['volume_volatility'] = volume_data['volume'].std()
            
            return profile
            
        except Exception as e:
            logger.error(f"Error analyzing volume profile: {e}")
            return {}

class TimeframeConfirmationAnalyzer:
    """Analyzes multi-timeframe confirmation and agreement"""
    
    def __init__(self):
        self.timeframe_analyzer = TimeframeAnalyzer()
    
    def get_timeframe_agreement(self, symbol: str, 
                               timeframe_data: Dict[str, pd.DataFrame],
                               volume_data: Dict[str, pd.DataFrame]) -> TimeframeConfirmation:
        """
        Analyze multi-timeframe agreement
        
        Args:
            symbol: Stock symbol
            timeframe_data: Dictionary of timeframe -> price data
            volume_data: Dictionary of timeframe -> volume data
            
        Returns:
            TimeframeConfirmation object
        """
        try:
            # Analyze each timeframe
            timeframe_analysis = {}
            for timeframe, price_data in timeframe_data.items():
                if timeframe in volume_data:
                    analysis = self.timeframe_analyzer.analyze_timeframe(
                        symbol, timeframe, price_data, volume_data[timeframe]
                    )
                    timeframe_analysis[timeframe] = analysis
            
            # Categorize timeframes
            short_term = self._get_short_term_bias(timeframe_analysis)
            medium_term = self._get_medium_term_bias(timeframe_analysis)
            long_term = self._get_long_term_bias(timeframe_analysis)
            
            # Calculate agreement score
            agreement_score = self._calculate_agreement_score(timeframe_analysis)
            
            # Find conflicting signals
            conflicting_signals = self._find_conflicting_signals(timeframe_analysis)
            
            # Calculate weighted probability
            weighted_probability = self._calculate_weighted_probability(timeframe_analysis)
            
            # Calculate overall confidence
            overall_confidence = self._calculate_overall_confidence(timeframe_analysis)
            
            # Generate recommendation
            recommendation = self._generate_recommendation(
                short_term, medium_term, long_term, agreement_score
            )
            
            return TimeframeConfirmation(
                symbol=symbol,
                short_term_bias=short_term,
                medium_term_bias=medium_term,
                long_term_bias=long_term,
                agreement_score=agreement_score,
                conflicting_signals=conflicting_signals,
                weighted_probability=weighted_probability,
                overall_confidence=overall_confidence,
                timeframe_analysis=timeframe_analysis,
                recommendation=recommendation,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"Error analyzing timeframe agreement for {symbol}: {e}")
            raise
    
    def _get_short_term_bias(self, timeframe_analysis: Dict[str, TimeframeAnalysis]) -> TimeframeBias:
        """Get short-term bias from 1m, 5m, 15m timeframes"""
        
        short_term_timeframes = ['1m', '5m', '15m']
        biases = []
        
        for tf in short_term_timeframes:
            if tf in timeframe_analysis:
                biases.append(timeframe_analysis[tf].bias)
        
        if not biases:
            return TimeframeBias.NEUTRAL
        
        return self._aggregate_biases(biases)
    
    def _get_medium_term_bias(self, timeframe_analysis: Dict[str, TimeframeAnalysis]) -> TimeframeBias:
        """Get medium-term bias from 1h, 4h timeframes"""
        
        medium_term_timeframes = ['1h', '4h']
        biases = []
        
        for tf in medium_term_timeframes:
            if tf in timeframe_analysis:
                biases.append(timeframe_analysis[tf].bias)
        
        if not biases:
            return TimeframeBias.NEUTRAL
        
        return self._aggregate_biases(biases)
    
    def _get_long_term_bias(self, timeframe_analysis: Dict[str, TimeframeAnalysis]) -> TimeframeBias:
        """Get long-term bias from 1d, 1w timeframes"""
        
        long_term_timeframes = ['1d', '1w']
        biases = []
        
        for tf in long_term_timeframes:
            if tf in timeframe_analysis:
                biases.append(timeframe_analysis[tf].bias)
        
        if not biases:
            return TimeframeBias.NEUTRAL
        
        return self._aggregate_biases(biases)
    
    def _aggregate_biases(self, biases: List[TimeframeBias]) -> TimeframeBias:
        """Aggregate multiple biases into a single bias"""
        
        if not biases:
            return TimeframeBias.NEUTRAL
        
        # Convert biases to numerical values
        bias_values = []
        for bias in biases:
            if bias == TimeframeBias.STRONG_BULLISH:
                bias_values.append(3)
            elif bias == TimeframeBias.BULLISH:
                bias_values.append(2)
            elif bias == TimeframeBias.WEAK_BULLISH:
                bias_values.append(1)
            elif bias == TimeframeBias.NEUTRAL:
                bias_values.append(0)
            elif bias == TimeframeBias.WEAK_BEARISH:
                bias_values.append(-1)
            elif bias == TimeframeBias.BEARISH:
                bias_values.append(-2)
            elif bias == TimeframeBias.STRONG_BEARISH:
                bias_values.append(-3)
        
        # Calculate average bias
        avg_bias = sum(bias_values) / len(bias_values)
        
        # Convert back to enum
        if avg_bias > 2:
            return TimeframeBias.STRONG_BULLISH
        elif avg_bias > 1:
            return TimeframeBias.BULLISH
        elif avg_bias > 0:
            return TimeframeBias.WEAK_BULLISH
        elif avg_bias > -1:
            return TimeframeBias.NEUTRAL
        elif avg_bias > -2:
            return TimeframeBias.WEAK_BEARISH
        elif avg_bias > -3:
            return TimeframeBias.BEARISH
        else:
            return TimeframeBias.STRONG_BEARISH
    
    def _calculate_agreement_score(self, timeframe_analysis: Dict[str, TimeframeAnalysis]) -> float:
        """Calculate agreement score across timeframes"""
        
        try:
            if len(timeframe_analysis) < 2:
                return 0.5
            
            # Calculate bias agreement
            biases = [analysis.bias for analysis in timeframe_analysis.values()]
            unique_biases = set(biases)
            
            if len(unique_biases) == 1:
                return 1.0  # Perfect agreement
            elif len(unique_biases) == 2:
                return 0.7  # Good agreement
            elif len(unique_biases) == 3:
                return 0.4  # Moderate agreement
            else:
                return 0.2  # Poor agreement
                
        except Exception as e:
            logger.error(f"Error calculating agreement score: {e}")
            return 0.5
    
    def _find_conflicting_signals(self, timeframe_analysis: Dict[str, TimeframeAnalysis]) -> List[str]:
        """Find conflicting signals across timeframes"""
        
        conflicts = []
        
        try:
            # Check for trend conflicts
            trends = {}
            for tf, analysis in timeframe_analysis.items():
                trends[tf] = analysis.trend_direction
            
            # Find conflicting trends
            unique_trends = set(trends.values())
            if len(unique_trends) > 1:
                conflicts.append(f"Conflicting trends: {', '.join(unique_trends)}")
            
            # Check for bias conflicts
            biases = {}
            for tf, analysis in timeframe_analysis.items():
                biases[tf] = analysis.bias
            
            # Find extreme bias conflicts
            bullish_biases = [b for b in biases.values() if 'bullish' in b.value]
            bearish_biases = [b for b in biases.values() if 'bearish' in b.value]
            
            if bullish_biases and bearish_biases:
                conflicts.append("Mixed bullish/bearish signals across timeframes")
            
            # Check for momentum conflicts
            momentums = {}
            for tf, analysis in timeframe_analysis.items():
                momentums[tf] = analysis.momentum
            
            # Find momentum conflicts
            positive_momentum = [m for m in momentums.values() if m > 0]
            negative_momentum = [m for m in momentums.values() if m < 0]
            
            if positive_momentum and negative_momentum:
                conflicts.append("Mixed momentum signals across timeframes")
            
        except Exception as e:
            logger.error(f"Error finding conflicting signals: {e}")
            conflicts.append("Error analyzing signal conflicts")
        
        return conflicts
    
    def _calculate_weighted_probability(self, timeframe_analysis: Dict[str, TimeframeAnalysis]) -> Dict[str, float]:
        """Calculate weighted probability based on timeframe analysis"""
        
        try:
            probabilities = {'bullish': 0.0, 'bearish': 0.0, 'sideways': 0.0}
            total_weight = 0.0
            
            for timeframe, analysis in timeframe_analysis.items():
                weight = self.timeframe_analyzer.TIMEFRAME_WEIGHTS.get(timeframe, 0.15)
                
                # Convert bias to probability
                if 'bullish' in analysis.bias.value:
                    probabilities['bullish'] += weight * analysis.confidence
                elif 'bearish' in analysis.bias.value:
                    probabilities['bearish'] += weight * analysis.confidence
                else:
                    probabilities['sideways'] += weight * analysis.confidence
                
                total_weight += weight
            
            # Normalize probabilities
            if total_weight > 0:
                for key in probabilities:
                    probabilities[key] = probabilities[key] / total_weight
            
            return probabilities
            
        except Exception as e:
            logger.error(f"Error calculating weighted probability: {e}")
            return {'bullish': 0.33, 'bearish': 0.33, 'sideways': 0.34}
    
    def _calculate_overall_confidence(self, timeframe_analysis: Dict[str, TimeframeAnalysis]) -> float:
        """Calculate overall confidence across timeframes"""
        
        try:
            if not timeframe_analysis:
                return 0.5
            
            # Weighted average of confidence levels
            total_weight = 0.0
            weighted_confidence = 0.0
            
            for timeframe, analysis in timeframe_analysis.items():
                weight = self.timeframe_analyzer.TIMEFRAME_WEIGHTS.get(timeframe, 0.15)
                weighted_confidence += analysis.confidence * weight
                total_weight += weight
            
            if total_weight > 0:
                return weighted_confidence / total_weight
            else:
                return 0.5
                
        except Exception as e:
            logger.error(f"Error calculating overall confidence: {e}")
            return 0.5
    
    def _generate_recommendation(self, short_term: TimeframeBias, 
                                medium_term: TimeframeBias, 
                                long_term: TimeframeBias, 
                                agreement_score: float) -> str:
        """Generate trading recommendation based on timeframe analysis"""
        
        try:
            # Check for strong agreement
            if agreement_score > 0.8:
                if short_term == medium_term == long_term:
                    if 'bullish' in short_term.value:
                        return "Strong buy - all timeframes aligned bullish"
                    elif 'bearish' in short_term.value:
                        return "Strong sell - all timeframes aligned bearish"
                    else:
                        return "Hold - all timeframes neutral"
            
            # Check for trend alignment
            if short_term == medium_term and agreement_score > 0.6:
                if 'bullish' in short_term.value:
                    return "Buy - short and medium term bullish"
                elif 'bearish' in short_term.value:
                    return "Sell - short and medium term bearish"
            
            # Check for short-term opportunities
            if short_term in [TimeframeBias.STRONG_BULLISH, TimeframeBias.STRONG_BEARISH]:
                if 'bullish' in short_term.value:
                    return "Short-term buy - strong short-term bullish signal"
                else:
                    return "Short-term sell - strong short-term bearish signal"
            
            # Default recommendation
            return "Hold - mixed signals across timeframes"
            
        except Exception as e:
            logger.error(f"Error generating recommendation: {e}")
            return "Hold - insufficient data for recommendation" 