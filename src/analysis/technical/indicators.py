import numpy as np
import pandas as pd
from typing import List, Optional, Dict, Any
import logging

# Import central configuration
from src.analysis.technical.config import get_tech_config

from src.data.models.stock_data import HistoricalData, TechnicalIndicators

logger = logging.getLogger(__name__)

class TechnicalIndicatorsCalculator:
    """Calculate technical analysis indicators"""
    
    def __init__(self):
        """Initialize with central configuration"""
        self.config = get_tech_config()
    
    def calculate_rsi(self, prices: List[float], period: int = None) -> Optional[float]:
        """Calculate Relative Strength Index (RSI)"""
        try:
            # Use provided period or fall back to central config
            rsi_period = period if period is not None else self.config.rsi_period
            
            if len(prices) < rsi_period + 1:
                return None
                
            # Convert to pandas series for easier calculation
            series = pd.Series(prices)
            
            # Calculate price changes
            delta = series.diff()
            
            # Separate gains and losses
            gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
            
            # Calculate RS
            rs = gain / loss
            
            # Calculate RSI
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.iloc[-1]
            
        except Exception as e:
            logger.error(f"RSI calculation error: {e}")
            return None
    
    def calculate_macd(
        self, 
        prices: List[float], 
        fast_period: int = None, 
        slow_period: int = None, 
        signal_period: int = None
    ) -> Optional[Dict[str, float]]:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        try:
            # Use provided periods or fall back to central config
            macd_fast = fast_period if fast_period is not None else self.config.macd_fast_period
            macd_slow = slow_period if slow_period is not None else self.config.macd_slow_period
            macd_signal = signal_period if signal_period is not None else self.config.macd_signal_period
            
            if len(prices) < macd_slow:
                return None
                
            series = pd.Series(prices)
            
            # Calculate EMAs
            fast_ema = series.ewm(span=macd_fast, adjust=False).mean()
            slow_ema = series.ewm(span=macd_slow, adjust=False).mean()
            
            # Calculate MACD line
            macd_line = fast_ema - slow_ema
            
            # Calculate signal line
            signal_line = macd_line.ewm(span=macd_signal, adjust=False).mean()
            
            # Calculate histogram
            histogram = macd_line - signal_line
            
            return {
                'macd_line': macd_line.iloc[-1],
                'signal_line': signal_line.iloc[-1],
                'histogram': histogram.iloc[-1]
            }
            
        except Exception as e:
            logger.error(f"MACD calculation error: {e}")
            return None
    
    def calculate_moving_averages(
        self, 
        prices: List[float], 
        periods: List[int] = None
    ) -> Dict[str, float]:
        """Calculate Simple Moving Averages (SMA)"""
        try:
            # Use provided periods or fall back to central config
            sma_periods = periods if periods is not None else self.config.sma_periods
            
            result = {}
            series = pd.Series(prices)
            
            for period in sma_periods:
                if len(prices) >= period:
                    sma = series.rolling(window=period).mean()
                    result[f'sma_{period}'] = sma.iloc[-1]
                else:
                    result[f'sma_{period}'] = None
                    
            return result
            
        except Exception as e:
            logger.error(f"Moving averages calculation error: {e}")
            return {}
    
    def calculate_bollinger_bands(
        self, 
        prices: List[float], 
        period: int = None, 
        std_dev: float = None
    ) -> Optional[Dict[str, float]]:
        """Calculate Bollinger Bands"""
        try:
            # Use provided parameters or fall back to central config
            bb_period = period if period is not None else self.config.bb_period
            bb_std = std_dev if std_dev is not None else self.config.bb_std_dev
            
            if len(prices) < bb_period:
                return None
                
            series = pd.Series(prices)
            
            # Calculate SMA
            sma = series.rolling(window=bb_period).mean()
            
            # Calculate standard deviation
            std = series.rolling(window=bb_period).std()
            
            # Calculate bands
            upper_band = sma + (std * bb_std)
            lower_band = sma - (std * bb_std)
            
            return {
                'sma': sma.iloc[-1],
                'upper_band': upper_band.iloc[-1],
                'lower_band': lower_band.iloc[-1]
            }
            
        except Exception as e:
            logger.error(f"Bollinger Bands calculation error: {e}")
            return None
    
    def find_support_resistance(
        self, 
        highs: List[float], 
        lows: List[float], 
        prices: List[float]
    ) -> Dict[str, float]:
        """Find support and resistance levels using price action"""
        try:
            # Use central config for lookback period
            lookback_period = self.config.sr_lookback_period
            
            # Simple implementation - find recent swing highs/lows
            recent_prices = prices[-lookback_period:] if len(prices) >= lookback_period else prices
            recent_highs = highs[-lookback_period:] if len(highs) >= lookback_period else highs
            recent_lows = lows[-lookback_period:] if len(lows) >= lookback_period else lows
            
            # Support level - recent low
            support = min(recent_lows) if recent_lows else None
            
            # Resistance level - recent high
            resistance = max(recent_highs) if recent_highs else None
            
            return {
                'support': support,
                'resistance': resistance
            }
            
        except Exception as e:
            logger.error(f"Support/Resistance calculation error: {e}")
            return {'support': None, 'resistance': None}
    
    def calculate_volume_sma(self, volumes: List[Optional[float]], period: int = None) -> Optional[float]:
        """Calculate Volume Simple Moving Average"""
        try:
            # Use provided period or fall back to central config
            vol_period = period if period is not None else self.config.volume_sma_period
            
            # Filter out None values
            valid_volumes = [v for v in volumes if v is not None]
            
            if len(valid_volumes) < vol_period:
                return None
                
            series = pd.Series(valid_volumes)
            volume_sma = series.rolling(window=vol_period).mean()
            
            return volume_sma.iloc[-1]
            
        except Exception as e:
            logger.error(f"Volume SMA calculation error: {e}")
            return None
    
    def calculate_all_indicators(self, historical_data: HistoricalData) -> TechnicalIndicators:
        """Calculate all technical indicators"""
        try:
            # Extract price data
            prices = historical_data.prices
            highs = historical_data.highs
            lows = historical_data.lows
            volumes = historical_data.volumes
            
            # Calculate indicators
            rsi = self.calculate_rsi(prices)
            macd = self.calculate_macd(prices)
            moving_averages = self.calculate_moving_averages(prices)
            bollinger_bands = self.calculate_bollinger_bands(prices)
            support_resistance = self.find_support_resistance(highs, lows, prices)
            volume_sma = self.calculate_volume_sma(volumes)
            
            return TechnicalIndicators(
                rsi=rsi,
                macd=macd,
                sma_50=moving_averages.get('sma_50'),
                sma_200=moving_averages.get('sma_200'),
                ema_12=None,  # Not implemented yet
                ema_26=None,  # Not implemented yet
                bollinger_bands=bollinger_bands,
                support_level=support_resistance['support'],
                resistance_level=support_resistance['resistance'],
                volume_sma=volume_sma
            )
            
        except Exception as e:
            logger.error(f"Technical indicators calculation error: {e}")
            return TechnicalIndicators()
