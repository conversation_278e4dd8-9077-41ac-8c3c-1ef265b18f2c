"""
<PERSON><PERSON><PERSON> to clean up mock objects that might be affecting production code.

This script ensures that the execute_ask_pipeline function is restored to its
original implementation and not replaced with mock objects.
"""

import sys
import os
import importlib
import unittest.mock

def cleanup_mocks():
    """
    Clean up any mock objects that might be affecting the production code.
    """
    try:
        # Import the modules
        import src.bot.pipeline.commands.ask.pipeline as pipeline_module
        import src.bot.client as client_module
        
        # Check if execute_ask_pipeline in pipeline module is a mock
        if isinstance(pipeline_module.execute_ask_pipeline, (unittest.mock.Mock, unittest.mock.MagicMock)):
            print("WARNING: execute_ask_pipeline in pipeline module is a mock object")
            print("Reloading module to restore original function...")
            importlib.reload(pipeline_module)
            
            # Verify it's now the original function
            if not isinstance(pipeline_module.execute_ask_pipeline, (unittest.mock.Mock, unittest.mock.MagicMock)):
                print("SUCCESS: execute_ask_pipeline in pipeline module restored")
            else:
                print("ERROR: Failed to restore execute_ask_pipeline in pipeline module")
                return False
        else:
            print("INFO: execute_ask_pipeline in pipeline module is already the original function")
        
        # Check if execute_ask_pipeline in client module is a mock
        # Note: The client module imports the function, so we need to check the original
        if hasattr(client_module, 'execute_ask_pipeline'):
            if isinstance(client_module.execute_ask_pipeline, (unittest.mock.Mock, unittest.mock.MagicMock)):
                print("WARNING: execute_ask_pipeline in client module is a mock object")
                print("Reloading module to restore original function...")
                importlib.reload(client_module)
                
                # Verify it's now the original function
                if not isinstance(client_module.execute_ask_pipeline, (unittest.mock.Mock, unittest.mock.MagicMock)):
                    print("SUCCESS: execute_ask_pipeline in client module restored")
                else:
                    print("ERROR: Failed to restore execute_ask_pipeline in client module")
                    return False
            else:
                print("INFO: execute_ask_pipeline in client module is already the original function")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to clean up mocks: {e}")
        return False

def verify_function():
    """
    Verify that the execute_ask_pipeline function is working correctly.
    """
    try:
        # Import the function
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        
        # Check if it's a mock
        if isinstance(execute_ask_pipeline, (unittest.mock.Mock, unittest.mock.MagicMock)):
            print("ERROR: execute_ask_pipeline is still a mock object")
            return False
        
        # Check if it's callable
        if not callable(execute_ask_pipeline):
            print("ERROR: execute_ask_pipeline is not callable")
            return False
            
        print("INFO: execute_ask_pipeline appears to be working correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to verify execute_ask_pipeline: {e}")
        return False

def main():
    """
    Main function to run the cleanup.
    """
    print("Starting mock cleanup...")
    
    # Clean up mocks
    if not cleanup_mocks():
        print("Failed to clean up mocks")
        return 1
    
    # Verify the function is working correctly
    if not verify_function():
        print("Failed to verify function")
        return 1
    
    print("Mock cleanup completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())