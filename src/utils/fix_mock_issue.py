"""
Fix for the mock object issue with execute_ask_pipeline function.

This module provides a function to ensure that the execute_ask_pipeline function
is properly restored to its original implementation if it has been replaced with a mock.
"""

import unittest.mock
import importlib
import sys
from typing import Any, Callable

def is_mock_object(obj: Any) -> bool:
    """
    Check if an object is a mock object.
    
    Args:
        obj: The object to check
        
    Returns:
        bool: True if the object is a mock, False otherwise
    """
    return isinstance(obj, (unittest.mock.Mock, unittest.mock.MagicMock))

def restore_original_function(module_path: str, function_name: str) -> bool:
    """
    Restore the original implementation of a function by reloading its module.
    
    Args:
        module_path: The path to the module containing the function
        function_name: The name of the function to restore
        
    Returns:
        bool: True if restoration was successful, False otherwise
    """
    try:
        # Import the module
        module = importlib.import_module(module_path)
        
        # Get the current function
        current_function = getattr(module, function_name, None)
        
        # Check if it's a mock
        if current_function and is_mock_object(current_function):
            print(f"WARNING: {function_name} in {module_path} is currently a mock object")
            
            # Reload the module to restore the original function
            importlib.reload(module)
            
            # Check if the function is now the original
            restored_function = getattr(module, function_name, None)
            if restored_function and not is_mock_object(restored_function):
                print(f"SUCCESS: {function_name} has been restored to its original implementation")
                return True
            else:
                print(f"ERROR: Failed to restore {function_name} to its original implementation")
                return False
        else:
            print(f"INFO: {function_name} is already the original function, not a mock")
            return True
            
    except Exception as e:
        print(f"ERROR: Failed to restore {function_name}: {e}")
        return False

def fix_execute_ask_pipeline_mock() -> bool:
    """
    Fix the execute_ask_pipeline mock issue by restoring the original function.
    
    Returns:
        bool: True if the fix was successful, False otherwise
    """
    print("Checking and fixing execute_ask_pipeline mock issue...")
    
    # Try to restore the function in the pipeline module
    success1 = restore_original_function(
        'src.bot.pipeline.commands.ask.pipeline', 
        'execute_ask_pipeline'
    )
    
    # Also try to restore in the client module if it's imported there
    success2 = restore_original_function(
        'src.bot.client', 
        'execute_ask_pipeline'
    )
    
    return success1 and success2

def verify_function_integrity() -> bool:
    """
    Verify that the execute_ask_pipeline function is working correctly.
    
    Returns:
        bool: True if the function is working correctly, False otherwise
    """
    try:
        # Import the function
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        
        # Check if it's a mock
        if is_mock_object(execute_ask_pipeline):
            print("ERROR: execute_ask_pipeline is still a mock object")
            return False
        
        # Check if it's callable
        if not callable(execute_ask_pipeline):
            print("ERROR: execute_ask_pipeline is not callable")
            return False
            
        print("INFO: execute_ask_pipeline appears to be working correctly")
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to verify execute_ask_pipeline integrity: {e}")
        return False

def main() -> int:
    """
    Main function to run the mock fix.
    
    Returns:
        int: 0 if successful, 1 if failed
    """
    print("Starting execute_ask_pipeline mock fix...")
    
    # Try to fix the mock issue
    if not fix_execute_ask_pipeline_mock():
        print("Failed to fix execute_ask_pipeline mock issue")
        return 1
    
    # Verify the function is working correctly
    if not verify_function_integrity():
        print("Failed to verify execute_ask_pipeline integrity")
        return 1
    
    print("execute_ask_pipeline mock fix completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())