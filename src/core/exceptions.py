"""
Unified Exception Hierarchy for Trading Automation System

Consolidates all exception classes into a single, comprehensive hierarchy
that provides consistent error handling across all components.
"""

from enum import Enum
from typing import Optional, Dict, Any, List
import traceback
import sys

class ErrorSeverity(Enum):
    """Error severity levels for logging and handling"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Error categories for classification and handling"""
    CONFIGURATION = "configuration"
    VALIDATION = "validation"
    PIPELINE = "pipeline"
    DATA_PROVIDER = "data_provider"
    AI_SERVICE = "ai_service"
    DATABASE = "database"
    SECURITY = "security"
    NETWORK = "network"
    BUSINESS_LOGIC = "business_logic"
    MARKET_DATA = "market_data"
    TECHNICAL_ANALYSIS = "technical_analysis"
    CACHE = "cache"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    RATE_LIMIT = "rate_limit"
    TIMEOUT = "timeout"
    SERIALIZATION = "serialization"
    RESOURCE = "resource"
    DEPENDENCY = "dependency"

class TradingBotBaseException(Exception):
    """
    Base exception class for all trading bot errors.
    
    Provides consistent error handling with:
    - Error categorization
    - Severity levels
    - Context information
    - Structured error details
    """
    
    def __init__(
        self, 
        message: str, 
        category: ErrorCategory = ErrorCategory.BUSINESS_LOGIC,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.original_error = original_error
        self.context = context or {}
        self.timestamp = self._get_timestamp()
        self.traceback = self._get_traceback()
    
    def _get_timestamp(self) -> str:
        """Get current timestamp for error tracking"""
        from datetime import datetime
        return datetime.utcnow().isoformat()
    
    def _get_traceback(self) -> str:
        """Get current traceback for debugging"""
        return ''.join(traceback.format_tb(sys.exc_info()[2])) if sys.exc_info()[2] else ''
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for serialization"""
        return {
            'message': self.message,
            'category': self.category.value,
            'severity': self.severity.value,
            'details': self.details,
            'original_error': str(self.original_error) if self.original_error else None,
            'context': self.context,
            'timestamp': self.timestamp,
            'traceback': self.traceback,
            'exception_type': self.__class__.__name__
        }
    
    def __str__(self) -> str:
        """String representation with context"""
        base = f"{self.__class__.__name__}: {self.message}"
        if self.context:
            context_str = ', '.join(f"{k}={v}" for k, v in self.context.items())
            base += f" (Context: {context_str})"
        return base

# Configuration and Validation Errors
class ConfigurationError(TradingBotBaseException):
    """Configuration-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.CONFIGURATION, **kwargs)

class ValidationError(TradingBotBaseException):
    """Data validation errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.VALIDATION, **kwargs)

class ConfigValidationError(ValidationError):
    """Configuration validation errors"""
    pass

# Pipeline Errors
class PipelineError(TradingBotBaseException):
    """Base class for pipeline-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.PIPELINE, **kwargs)

class StageExecutionError(PipelineError):
    """Errors during pipeline stage execution"""
    def __init__(self, stage_name: str, message: str, **kwargs):
        super().__init__(message, context={'stage_name': stage_name}, **kwargs)

class PipelineTimeoutError(PipelineError):
    """Pipeline execution timeout"""
    def __init__(self, timeout_seconds: int, **kwargs):
        super().__init__(
            f"Pipeline execution timed out after {timeout_seconds} seconds",
            severity=ErrorSeverity.HIGH,
            context={'timeout_seconds': timeout_seconds},
            **kwargs
        )

# Data Provider Errors
class DataProviderError(TradingBotBaseException):
    """Base class for data provider errors"""
    def __init__(self, message: str, provider_name: str = None, **kwargs):
        context = kwargs.get('context', {})
        if provider_name:
            context['provider_name'] = provider_name
        super().__init__(message, ErrorCategory.DATA_PROVIDER, context=context, **kwargs)

class ProviderError(DataProviderError):
    """Generic provider error (alias for DataProviderError)"""
    pass

class DataProviderTimeoutError(DataProviderError):
    """Data provider timeout errors"""
    def __init__(self, provider_name: str, timeout_seconds: float, **kwargs):
        super().__init__(
            f"Data provider {provider_name} timed out after {timeout_seconds} seconds",
            provider_name=provider_name,
            severity=ErrorSeverity.MEDIUM,
            context={'timeout_seconds': timeout_seconds},
            **kwargs
        )

class ProviderTimeoutError(DataProviderTimeoutError):
    """Provider timeout error (alias for DataProviderTimeoutError)"""
    pass

class DataProviderRateLimitError(DataProviderError):
    """Data provider rate limit errors"""
    def __init__(self, provider_name: str, retry_after: int = None, **kwargs):
        context = {'retry_after': retry_after} if retry_after else {}
        super().__init__(
            f"Data provider {provider_name} rate limit exceeded",
            provider_name=provider_name,
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs
        )

class ProviderRateLimitError(DataProviderRateLimitError):
    """Provider rate limit error (alias for DataProviderRateLimitError)"""
    pass

class DataQualityError(DataProviderError):
    """Data quality issues from providers"""
    def __init__(self, provider_name: str, quality_issues: List[str], **kwargs):
        super().__init__(
            f"Data quality issues from {provider_name}: {', '.join(quality_issues)}",
            provider_name=provider_name,
            severity=ErrorSeverity.MEDIUM,
            context={'quality_issues': quality_issues},
            **kwargs
        )

class MarketDataError(DataProviderError):
    """Market data specific errors"""
    def __init__(self, message: str, symbol: str = None, **kwargs):
        context = kwargs.get('context', {})
        if symbol:
            context['symbol'] = symbol
        super().__init__(message, ErrorCategory.MARKET_DATA, context=context, **kwargs)

class ProviderUnavailableError(DataProviderError):
    """Provider service unavailable errors"""
    def __init__(self, provider_name: str, **kwargs):
        super().__init__(
            f"Data provider {provider_name} is currently unavailable",
            provider_name=provider_name,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )

# AI Service Errors
class AIServiceError(TradingBotBaseException):
    """Base class for AI service errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.AI_SERVICE, **kwargs)

class AIServiceTimeoutError(AIServiceError):
    """AI service timeout errors"""
    def __init__(self, service_name: str, timeout_seconds: float, **kwargs):
        super().__init__(
            f"AI service {service_name} timed out after {timeout_seconds} seconds",
            severity=ErrorSeverity.MEDIUM,
            context={'service_name': service_name, 'timeout_seconds': timeout_seconds},
            **kwargs
        )

class AIServiceQuotaError(AIServiceError):
    """AI service quota exceeded errors"""
    def __init__(self, service_name: str, quota_type: str, **kwargs):
        super().__init__(
            f"AI service {service_name} quota exceeded for {quota_type}",
            severity=ErrorSeverity.MEDIUM,
            context={'service_name': service_name, 'quota_type': quota_type},
            **kwargs
        )

class AIResponseValidationError(AIServiceError):
    """AI response validation errors"""
    def __init__(self, message: str, response_data: Any = None, **kwargs):
        super().__init__(
            message,
            severity=ErrorSeverity.MEDIUM,
            context={'response_data': str(response_data) if response_data else None},
            **kwargs
        )

# Database Errors
class DatabaseError(TradingBotBaseException):
    """Base class for database errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.DATABASE, **kwargs)

class DatabaseConnectionError(DatabaseError):
    """Database connection errors"""
    def __init__(self, database_url: str, **kwargs):
        super().__init__(
            f"Failed to connect to database: {database_url}",
            severity=ErrorSeverity.HIGH,
            context={'database_url': database_url},
            **kwargs
        )

# Security Errors
class SecurityError(TradingBotBaseException):
    """Base class for security-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.SECURITY, **kwargs)

class AuthenticationError(SecurityError):
    """Authentication errors"""
    def __init__(self, message: str, user_id: str = None, **kwargs):
        context = kwargs.get('context', {})
        if user_id:
            context['user_id'] = user_id
        super().__init__(message, ErrorCategory.AUTHENTICATION, context=context, **kwargs)

class AuthorizationError(SecurityError):
    """Authorization errors"""
    def __init__(self, message: str, user_id: str = None, required_permission: str = None, **kwargs):
        context = kwargs.get('context', {})
        if user_id:
            context['user_id'] = user_id
        if required_permission:
            context['required_permission'] = required_permission
        super().__init__(message, ErrorCategory.AUTHORIZATION, context=context, **kwargs)

class RateLimitError(SecurityError):
    """Rate limiting errors"""
    def __init__(self, message: str, retry_after: int = None, **kwargs):
        context = kwargs.get('context', {})
        if retry_after:
            context['retry_after'] = retry_after
        super().__init__(message, ErrorCategory.RATE_LIMIT, context=context, **kwargs)

# Network Errors
class NetworkError(TradingBotBaseException):
    """Base class for network errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.NETWORK, **kwargs)

class ExternalServiceError(NetworkError):
    """External service communication errors"""
    def __init__(self, service_name: str, endpoint: str = None, **kwargs):
        context = kwargs.get('context', {})
        if endpoint:
            context['endpoint'] = endpoint
        super().__init__(
            f"External service {service_name} communication failed",
            context=context,
            **kwargs
        )

# Cache Errors
class CacheError(TradingBotBaseException):
    """Cache-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.CACHE, **kwargs)

# Serialization Errors
class SerializationError(TradingBotBaseException):
    """Data serialization/deserialization errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.SERIALIZATION, **kwargs)

# Resource Errors
class ResourceError(TradingBotBaseException):
    """Resource management errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.RESOURCE, **kwargs)

class DependencyError(TradingBotBaseException):
    """Dependency-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.DEPENDENCY, **kwargs)

class BusinessLogicError(TradingBotBaseException):
    """Business logic errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.BUSINESS_LOGIC, **kwargs)

class TechnicalAnalysisError(TradingBotBaseException):
    """Technical analysis calculation errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.TECHNICAL_ANALYSIS, **kwargs)

class IndicatorCalculationError(TechnicalAnalysisError):
    """Technical indicator calculation errors"""
    pass

class InsufficientDataError(TradingBotBaseException):
    """Insufficient data for analysis"""
    def __init__(self, message: str, required_count: int = None, available_count: int = None, **kwargs):
        context = kwargs.get('context', {})
        if required_count is not None:
            context['required_count'] = required_count
        if available_count is not None:
            context['available_count'] = available_count
        super().__init__(message, context=context, **kwargs)

class ProcessingError(TradingBotBaseException):
    """Data processing errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, **kwargs)

class ServiceUnavailableError(TradingBotBaseException):
    """Service unavailable errors"""
    def __init__(self, service_name: str, **kwargs):
        super().__init__(
            f"Service {service_name} is currently unavailable",
            severity=ErrorSeverity.HIGH,
            context={'service_name': service_name},
            **kwargs
        )

class TimeoutError(TradingBotBaseException):
    """Generic timeout errors"""
    def __init__(self, message: str, timeout_seconds: float = None, **kwargs):
        context = kwargs.get('context', {})
        if timeout_seconds is not None:
            context['timeout_seconds'] = timeout_seconds
        super().__init__(message, ErrorCategory.TIMEOUT, context=context, **kwargs)

class InvalidStateError(TradingBotBaseException):
    """Invalid state errors"""
    def __init__(self, message: str, current_state: str = None, expected_state: str = None, **kwargs):
        context = kwargs.get('context', {})
        if current_state:
            context['current_state'] = current_state
        if expected_state:
            context['expected_state'] = expected_state
        super().__init__(message, context=context, **kwargs)

class RetryExhaustedError(TradingBotBaseException):
    """Retry attempts exhausted errors"""
    def __init__(self, message: str, max_retries: int, **kwargs):
        super().__init__(
            message,
            severity=ErrorSeverity.HIGH,
            context={'max_retries': max_retries},
            **kwargs
        )

class APIError(TradingBotBaseException):
    """API-related errors"""
    def __init__(self, message: str, status_code: int = None, **kwargs):
        context = kwargs.get('context', {})
        if status_code is not None:
            context['status_code'] = status_code
        super().__init__(message, context=context, **kwargs)

class DataSourceError(TradingBotBaseException):
    """Data source errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, **kwargs)

class StageError(PipelineError):
    """Pipeline stage errors"""
    pass

class ContextualError(TradingBotBaseException):
    """Context-related errors"""
    pass

# Error handling utilities
def create_error_from_exception(
    exc: Exception, 
    category: ErrorCategory = None,
    severity: ErrorSeverity = None,
    context: Dict[str, Any] = None
) -> TradingBotBaseException:
    """
    Create a TradingBotBaseException from an existing exception.
    
    Args:
        exc: Original exception
        category: Error category (auto-detected if not provided)
        severity: Error severity (auto-detected if not provided)
        context: Additional context information
    
    Returns:
        TradingBotBaseException instance
    """
    # Auto-detect category and severity if not provided
    if not category:
        if isinstance(exc, (ValueError, TypeError)):
            category = ErrorCategory.VALIDATION
        elif isinstance(exc, (ConnectionError, TimeoutError)):
            category = ErrorCategory.NETWORK
        elif isinstance(exc, (KeyError, IndexError)):
            category = ErrorCategory.DATA_PROVIDER
        else:
            category = ErrorCategory.BUSINESS_LOGIC
    
    if not severity:
        if isinstance(exc, (ConnectionError, TimeoutError)):
            severity = ErrorSeverity.HIGH
        else:
            severity = ErrorSeverity.MEDIUM
    
    # Create appropriate exception type
    if category == ErrorCategory.VALIDATION:
        return ValidationError(str(exc), severity=severity, context=context, original_error=exc)
    elif category == ErrorCategory.NETWORK:
        return NetworkError(str(exc), severity=severity, context=context, original_error=exc)
    elif category == ErrorCategory.DATA_PROVIDER:
        return DataProviderError(str(exc), severity=severity, context=context, original_error=exc)
    else:
        return TradingBotBaseException(str(exc), category=category, severity=severity, context=context, original_error=exc)

def is_retryable_error(exc: Exception) -> bool:
    """
    Determine if an error is retryable.
    
    Args:
        exc: Exception to check
    
    Returns:
        True if the error is retryable, False otherwise
    """
    retryable_types = (
        TimeoutError,
        ConnectionError,
        DataProviderTimeoutError,
        DataProviderRateLimitError,
        RateLimitError,
        NetworkError,
        ExternalServiceError
    )
    
    return isinstance(exc, retryable_types)

def get_error_summary(exc: Exception) -> Dict[str, Any]:
    """
    Get a summary of an exception for logging and monitoring.
    
    Args:
        exc: Exception to summarize
    
    Returns:
        Dictionary with error summary
    """
    if isinstance(exc, TradingBotBaseException):
        return exc.to_dict()
    else:
        return {
            'message': str(exc),
            'category': 'unknown',
            'severity': 'medium',
            'exception_type': type(exc).__name__,
            'traceback': ''.join(traceback.format_tb(sys.exc_info()[2])) if sys.exc_info()[2] else ''
        }