"""
Core Initialization Module

Centralizes core system imports and configuration.
"""

from typing import Dict, Any

# Import the new unified configuration system
from .config_manager import (
    config,
    get_config,
    ConfigurationError
)

# Import core components
from .pipeline_engine import Pipeline as PipelineEngine
from .pipeline_engine import <PERSON><PERSON><PERSON>, PipelineStage, PipelineBuilder
from .monitoring_pkg import PerformanceTracker
from .monitoring import SystemMonitor
from .exceptions import TradingBotBaseException

# Expose key configuration and core system components
__all__ = [
    'config',
    'get_config',
    'ConfigurationError',
    'PipelineEngine',
    'Pipeline',
    'PipelineStage',
    'PipelineBuilder',
    'PerformanceTracker',
    'SystemMonitor',
    'TradingBotBaseException'
]

# Optional: Add a global configuration summary function
def get_config_summary() -> Dict[str, Any]:
    """
    Retrieve a summary of the current system configuration.
    
    Returns:
        Dictionary containing key configuration details
    """
    return config.as_dict()