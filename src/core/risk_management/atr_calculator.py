"""
ATR-based Stop-Loss Calculator

Advanced risk management using Average True Range (ATR) for dynamic stop-loss
and position sizing calculations.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from src.core.logger import get_logger

logger = get_logger(__name__)


class RiskLevel(Enum):
    """Risk level classification"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


class TradeDirection(Enum):
    """Trade direction"""
    LONG = "long"
    SHORT = "short"


@dataclass
class StopLossConfig:
    """Configuration for stop-loss calculations"""
    atr_multiplier: float = 2.0  # Standard ATR multiplier
    risk_percentage: float = 2.0  # Risk per trade (% of portfolio)
    min_distance_pct: float = 1.0  # Minimum distance from entry (%)
    max_risk_pct: float = 5.0  # Maximum risk per trade (%)
    volatility_adjustment: bool = True  # Adjust for volatility
    trend_adjustment: bool = True  # Adjust for trend strength
    volume_filter: bool = True  # Filter based on volume
    volume_threshold_pct: float = 0.5  # Volume threshold (% of average)


@dataclass
class StopLossResult:
    """Stop-loss calculation result"""
    symbol: str
    direction: TradeDirection
    entry_price: float
    stop_loss_price: float
    atr_value: float
    risk_percentage: float
    risk_amount: float
    position_size: int
    confidence_score: float
    risk_level: RiskLevel
    rationale: str
    timestamp: str


class ATRStopLossCalculator:
    """Advanced ATR-based stop-loss calculator with comprehensive risk management."""
    
    def __init__(self, config: Optional[StopLossConfig] = None):
        """Initialize ATR stop-loss calculator."""
        self.config = config or StopLossConfig()
        self.atr_period = 14  # Standard ATR period
        
    def calculate_atr_stop_loss(self, data: pd.DataFrame, symbol: str, 
                             direction: TradeDirection, entry_price: float,
                             current_position: Optional[int] = None) -> StopLossResult:
        """
        Calculate ATR-based stop-loss with comprehensive risk analysis.
        
        Args:
            data: DataFrame with OHLCV data
            symbol: Stock symbol
            direction: Trade direction (LONG/SHORT)
            entry_price: Entry price for the trade
            current_position: Current position size (for existing positions)
            
        Returns:
            StopLossResult with comprehensive stop-loss information
        """
        try:
            if data is None or data.empty or len(data) < self.atr_period:
                raise ValueError("Insufficient data for ATR calculation")
            
            # Calculate ATR
            atr = self._calculate_atr(data)
            
            # Calculate stop-loss price
            stop_loss_price = self._calculate_stop_loss_price(
                data, direction, entry_price, atr
            )
            
            # Calculate risk metrics
            risk_percentage, risk_amount = self._calculate_risk_metrics(
                entry_price, stop_loss_price, current_position or 1000
            )
            
            # Calculate position size
            position_size = self._calculate_position_size(
                entry_price, stop_loss_price, risk_amount
            )
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                data, atr, entry_price, stop_loss_price
            )
            
            # Determine risk level
            risk_level = self._determine_risk_level(
                risk_percentage, atr, data
            )
            
            # Generate rationale
            rationale = self._generate_rationale(
                data, direction, entry_price, stop_loss_price, 
                atr, risk_percentage, risk_level
            )
            
            return StopLossResult(
                symbol=symbol,
                direction=direction,
                entry_price=entry_price,
                stop_loss_price=stop_loss_price,
                atr_value=atr,
                risk_percentage=risk_percentage,
                risk_amount=risk_amount,
                position_size=position_size,
                confidence_score=confidence_score,
                risk_level=risk_level,
                rationale=rationale,
                timestamp=pd.Timestamp.now().isoformat()
            )
            
        except ValueError as e:
            logger.error(f"Value error calculating ATR stop-loss for {symbol}: {e}")
            raise
        except KeyError as e:
            logger.error(f"Missing key in data for {symbol}: {e}")
            raise KeyError(f"Missing required data column for {symbol}: {e}")
        except pd.errors.EmptyDataError as e:
            logger.error(f"Empty data error for {symbol}: {e}")
            raise
        except TypeError as e:
            logger.error(f"Type error in ATR calculation for {symbol}: {e}")
            raise
    
    def _calculate_atr(self, data: pd.DataFrame) -> float:
        """Calculate Average True Range."""
        try:
            high = data['high']
            low = data['low']
            close = data['close']
            
            # Calculate True Range components
            high_low = high - low
            high_close = np.abs(high - close.shift())
            low_close = np.abs(low - close.shift())
            
            # True Range is the maximum of the three components
            true_range = np.maximum(high_low, high_close, low_close)
            
            # ATR is the moving average of True Range
            atr = true_range.rolling(window=self.atr_period).mean().iloc[-1]
            
            return float(atr) if not pd.isna(atr) else 0.0
            
        except KeyError as e:
            logger.error(f"Missing key in ATR calculation: {e}")
            return 0.0
        except pd.errors.EmptyDataError:
            logger.error("Empty DataFrame in ATR calculation")
            return 0.0
        except (ValueError, TypeError) as e:
            logger.error(f"Value or type error in ATR calculation: {e}")
            return 0.0
        except np.core._exceptions._ArrayMemoryError:
            logger.error("Memory error in numpy array during ATR calculation")
            return 0.0
    
    def _calculate_stop_loss_price(self, data: pd.DataFrame, direction: TradeDirection,
                                 entry_price: float, atr: float) -> float:
        """Calculate stop-loss price based on ATR and direction."""
        try:
            # Base ATR multiplier
            multiplier = self.config.atr_multiplier
            
            # Apply volatility adjustment
            if self.config.volatility_adjustment:
                volatility_ratio = self._calculate_volatility_ratio(data)
                multiplier *= volatility_ratio
            
            # Apply trend adjustment
            if self.config.trend_adjustment:
                trend_multiplier = self._calculate_trend_adjustment(data, direction)
                multiplier *= trend_multiplier
            
            # Calculate stop distance
            stop_distance = atr * multiplier
            
            # Apply minimum distance constraint
            min_distance = entry_price * (self.config.min_distance_pct / 100)
            stop_distance = max(stop_distance, min_distance)
            
            # Calculate stop price based on direction
            if direction == TradeDirection.LONG:
                stop_loss_price = entry_price - stop_distance
            else:  # SHORT
                stop_loss_price = entry_price + stop_distance
            
            # Apply volume filter
            if self.config.volume_filter:
                stop_loss_price = self._apply_volume_filter(
                    data, stop_loss_price, direction
                )
            
            return round(stop_loss_price, 2)
            
        except KeyError as e:
            logger.error(f"Missing key in stop-loss calculation: {e}")
            # Fallback: use simple ATR calculation
        except (ValueError, TypeError) as e:
            logger.error(f"Value or type error in stop-loss calculation: {e}")
            # Fallback: use simple ATR calculation
        except ZeroDivisionError as e:
            logger.error(f"Division by zero in stop-loss calculation: {e}")
            # Fallback: use simple ATR calculation
            if direction == TradeDirection.LONG:
                return round(entry_price - (atr * self.config.atr_multiplier), 2)
            else:
                return round(entry_price + (atr * self.config.atr_multiplier), 2)
    
    def _calculate_volatility_ratio(self, data: pd.DataFrame) -> float:
        """Calculate volatility adjustment ratio."""
        try:
            # Calculate current ATR vs historical ATR
            current_atr = self._calculate_atr(data.tail(self.atr_period))
            historical_atrs = []
            
            # Calculate ATR for different periods
            for period in [20, 50, 100]:
                if len(data) >= period:
                    atr_slice = data.tail(period)
                    historical_atr = self._calculate_atr(atr_slice)
                    historical_atrs.append(historical_atr)
            
            if not historical_atrs:
                return 1.0
            
            avg_historical_atr = np.mean(historical_atrs)
            
            if avg_historical_atr > 0:
                volatility_ratio = current_atr / avg_historical_atr
                
                # Adjust multiplier based on volatility
                if volatility_ratio > 1.5:  # High volatility
                    return 1.2  # Increase multiplier
                elif volatility_ratio < 0.7:  # Low volatility
                    return 0.8  # Decrease multiplier
                else:
                    return 1.0
            
            return 1.0
            
        except Exception:
            return 1.0
    
    def _calculate_trend_adjustment(self, data: pd.DataFrame, direction: TradeDirection) -> float:
        """Calculate trend-based adjustment for stop-loss."""
        try:
            # Calculate trend strength using moving averages
            if len(data) < 50:
                return 1.0
            
            # Calculate short and long term moving averages
            short_ma = data['close'].rolling(window=20).mean().iloc[-1]
            long_ma = data['close'].rolling(window=50).mean().iloc[-1]
            current_price = data['close'].iloc[-1]
            
            # Determine trend direction and strength
            if direction == TradeDirection.LONG:
                if current_price > short_ma > long_ma:  # Strong uptrend
                    return 0.8  # Tighter stop-loss in strong trend
                elif current_price < short_ma < long_ma:  # Downtrend
                    return 1.3  # Wider stop-loss against trend
            else:  # SHORT
                if current_price < short_ma < long_ma:  # Strong downtrend
                    return 0.8  # Tighter stop-loss in strong trend
                elif current_price > short_ma > long_ma:  # Uptrend
                    return 1.3  # Wider stop-loss against trend
            
            return 1.0  # Neutral trend
            
        except Exception:
            return 1.0
    
    def _apply_volume_filter(self, data: pd.DataFrame, stop_loss_price: float,
                           direction: TradeDirection) -> float:
        """Apply volume-based filter to stop-loss price."""
        try:
            if len(data) < 20:
                return stop_loss_price
            
            # Calculate recent volume average
            recent_volume = data['volume'].tail(10).mean()
            avg_volume = data['volume'].tail(20).mean()
            
            if recent_volume > avg_volume * (1 + self.config.volume_threshold_pct):
                # High volume, adjust stop-loss slightly
                if direction == TradeDirection.LONG:
                    return stop_loss_price * 0.995  # Slightly tighter
                else:
                    return stop_loss_price * 1.005  # Slightly wider
            
            return stop_loss_price
            
        except (KeyError, ValueError, TypeError) as e:
            logger.error(f"Error in volume filter: {e}")
            return stop_loss_price
        except IndexError as e:
            logger.error(f"Index error in volume filter: {e}")
            return stop_loss_price
    
    def _calculate_risk_metrics(self, entry_price: float, stop_loss_price: float,
                              position_size: int) -> Tuple[float, float]:
        """Calculate risk percentage and amount."""
        try:
            # Calculate risk per share
            if stop_loss_price < entry_price:  # LONG
                risk_per_share = entry_price - stop_loss_price
            else:  # SHORT
                risk_per_share = stop_loss_price - entry_price
            
            # Calculate total risk amount
            risk_amount = risk_per_share * position_size
            
            # Calculate risk percentage
            portfolio_value = entry_price * position_size
            risk_percentage = (risk_amount / portfolio_value) * 100
            
            # Apply maximum risk constraint
            risk_percentage = min(risk_percentage, self.config.max_risk_pct)
            
            return round(risk_percentage, 2), round(risk_amount, 2)
            
        except ZeroDivisionError as e:
            logger.error(f"Division by zero in risk metrics calculation: {e}")
            return 2.0, 100.0  # Default values
        except (ValueError, TypeError) as e:
            logger.error(f"Value or type error in risk metrics: {e}")
            return 2.0, 100.0  # Default values
    
    def _calculate_position_size(self, entry_price: float, stop_loss_price: float,
                              risk_amount: float) -> int:
        """Calculate optimal position size based on risk management."""
        try:
            # Calculate risk per share
            if stop_loss_price < entry_price:  # LONG
                risk_per_share = entry_price - stop_loss_price
            else:  # SHORT
                risk_per_share = stop_loss_price - entry_price
            
            if risk_per_share <= 0:
                return 100  # Default position size
            
            # Calculate position size
            position_size = int(risk_amount / risk_per_share)
            
            # Apply position size constraints
            min_position = 10
            max_position = 10000
            
            position_size = max(min_position, min(position_size, max_position))
            
            return position_size
            
        except ZeroDivisionError as e:
            logger.error(f"Division by zero in position size calculation: {e}")
            return 100  # Default position size
        except (ValueError, TypeError) as e:
            logger.error(f"Value or type error in position size calculation: {e}")
            return 100  # Default position size
    
    def _calculate_confidence_score(self, data: pd.DataFrame, atr: float,
                                 entry_price: float, stop_loss_price: float) -> float:
        """Calculate confidence score for the stop-loss recommendation."""
        try:
            confidence = 50.0  # Base confidence
            
            # Data quality factor
            if len(data) >= 100:
                confidence += 15.0
            elif len(data) >= 50:
                confidence += 10.0
            
            # ATR reliability factor
            if atr > 0:
                confidence += 15.0
            
            # Price action consistency
            recent_prices = data['close'].tail(10)
            price_range = recent_prices.max() - recent_prices.min()
            avg_price = recent_prices.mean()
            
            if price_range > 0 and avg_price > 0:
                volatility = price_range / avg_price
                if volatility < 0.05:  # Low volatility
                    confidence += 10.0
                elif volatility < 0.10:  # Medium volatility
                    confidence += 5.0
            
            # Stop-loss distance factor
            stop_distance = abs(entry_price - stop_loss_price) / entry_price
            if stop_distance > 0.01 and stop_distance < 0.10:  # Reasonable distance
                confidence += 10.0
            
            return min(100.0, confidence)
            
        except (KeyError, ValueError, TypeError) as e:
            logger.error(f"Error in confidence score calculation: {e}")
            return 50.0  # Default confidence
        except IndexError as e:
            logger.error(f"Index error in confidence calculation: {e}")
            return 50.0  # Default confidence
    
    def _determine_risk_level(self, risk_percentage: float, atr: float,
                           data: pd.DataFrame) -> RiskLevel:
        """Determine risk level based on multiple factors."""
        try:
            # Base risk level from percentage
            if risk_percentage >= 5.0:
                base_risk = RiskLevel.EXTREME
            elif risk_percentage >= 3.0:
                base_risk = RiskLevel.HIGH
            elif risk_percentage >= 1.5:
                base_risk = RiskLevel.MEDIUM
            else:
                base_risk = RiskLevel.LOW
            
            # Adjust based on volatility
            volatility = self._calculate_current_volatility(data)
            if volatility > 0.15:  # High volatility
                if base_risk == RiskLevel.LOW:
                    base_risk = RiskLevel.MEDIUM
                elif base_risk == RiskLevel.MEDIUM:
                    base_risk = RiskLevel.HIGH
            elif volatility < 0.05:  # Low volatility
                if base_risk == RiskLevel.HIGH:
                    base_risk = RiskLevel.MEDIUM
                elif base_risk == RiskLevel.EXTREME:
                    base_risk = RiskLevel.HIGH
            
            return base_risk
            
        except (KeyError, ValueError, TypeError) as e:
            logger.error(f"Error in risk level determination: {e}")
            return RiskLevel.MEDIUM  # Default risk level
        except AttributeError as e:
            logger.error(f"Attribute error in risk level calculation: {e}")
            return RiskLevel.MEDIUM  # Default risk level
    
    def _calculate_current_volatility(self, data: pd.DataFrame) -> float:
        """Calculate current volatility as a percentage."""
        try:
            if len(data) < 20:
                return 0.1  # Default volatility
            
            # Calculate standard deviation of returns
            returns = data['close'].pct_change().tail(20)
            volatility = returns.std()
            
            # Annualize volatility (assuming daily data)
            annualized_volatility = volatility * np.sqrt(252)
            
            return float(annualized_volatility) if not pd.isna(annualized_volatility) else 0.1
            
        except (KeyError, ValueError) as e:
            logger.error(f"Key or value error in volatility calculation: {e}")
            return 0.1  # Default volatility
        except (pd.errors.EmptyDataError, IndexError) as e:
            logger.error(f"Data error in volatility calculation: {e}")
            return 0.1  # Default volatility
    
    def _generate_rationale(self, data: pd.DataFrame, direction: TradeDirection,
                          entry_price: float, stop_loss_price: float,
                          atr: float, risk_percentage: float,
                          risk_level: RiskLevel) -> str:
        """Generate rationale for the stop-loss recommendation."""
        try:
            rationale = []
            
            # Add direction-specific rationale
            if direction == TradeDirection.LONG:
                rationale.append(f"Long position stop-loss at ${stop_loss_loss_price:.2f}")
                distance_pct = ((entry_price - stop_loss_price) / entry_price) * 100
                rationale.append(f"Stop distance: {distance_pct:.1f}% below entry")
            else:
                rationale.append(f"Short position stop-loss at ${stop_loss_price:.2f}")
                distance_pct = ((stop_loss_price - entry_price) / entry_price) * 100
                rationale.append(f"Stop distance: {distance_pct:.1f}% above entry")
            
            # Add ATR information
            rationale.append(f"ATR (14-period): ${atr:.2f}")
            
            # Add risk information
            rationale.append(f"Risk per trade: {risk_percentage:.1f}% of portfolio")
            rationale.append(f"Risk level: {risk_level.value.upper()}")
            
            # Add market context
            if len(data) >= 20:
                current_price = data['close'].iloc[-1]
                ma_20 = data['close'].rolling(window=20).mean().iloc[-1]
                ma_50 = data['close'].rolling(window=50).mean().iloc[-1]
                
                rationale.append(f"Price vs 20-day MA: ${current_price:.2f} vs ${ma_20:.2f}")
                rationale.append(f"Price vs 50-day MA: ${current_price:.2f} vs ${ma_50:.2f}")
            
            return " | ".join(rationale)
            
        except (KeyError, ValueError, TypeError) as e:
            logger.error(f"Error generating rationale: {e}")
            return "Stop-loss calculated using ATR methodology"
        except IndexError as e:
            logger.error(f"Index error in rationale generation: {e}")
            return "Stop-loss calculated using ATR methodology"
        except AttributeError as e:
            logger.error(f"Attribute error in rationale generation: {e}")
            return "Stop-loss calculated using ATR methodology"
    
    def batch_calculate_stop_losses(self, data_dict: Dict[str, pd.DataFrame],
                                  entries: List[Dict[str, Any]]) -> List[StopLossResult]:
        """Calculate stop-losses for multiple symbols and positions."""
        results = []
        
        for entry in entries:
            try:
                symbol = entry['symbol']
                direction = TradeDirection(entry['direction'])
                entry_price = entry['entry_price']
                position_size = entry.get('position_size', 100)
                
                if symbol in data_dict and data_dict[symbol] is not None:
                    data = data_dict[symbol]
                    
                    result = self.calculate_atr_stop_loss(
                        data, symbol, direction, entry_price, position_size
                    )
                    results.append(result)
                else:
                    logger.warning(f"No data available for {symbol}")
                    
            except KeyError as e:
                logger.error(f"Missing key for {entry.get('symbol', 'unknown')}: {e}")
                continue
            except ValueError as e:
                logger.error(f"Value error for {entry.get('symbol', 'unknown')}: {e}")
                continue
            except TypeError as e:
                logger.error(f"Type error for {entry.get('symbol', 'unknown')}: {e}")
                continue
            except pd.errors.EmptyDataError as e:
                logger.error(f"Empty data for {entry.get('symbol', 'unknown')}: {e}")
                continue
        
        return results
    
    def get_risk_recommendations(self, stop_loss_results: List[StopLossResult]) -> Dict[str, Any]:
        """Generate overall risk management recommendations."""
        if not stop_loss_results:
            return {"recommendation": "No positions to analyze"}
        
        try:
            # Aggregate statistics
            total_risk = sum(result.risk_percentage for result in stop_loss_results)
            avg_risk = total_risk / len(stop_loss_results)
            high_risk_count = len([r for r in stop_loss_results if r.risk_level in [RiskLevel.HIGH, RiskLevel.EXTREME]])
            
            # Generate recommendations
            recommendations = []
            
            if total_risk > 10.0:
                recommendations.append("⚠️ High total portfolio risk - consider reducing position sizes")
            elif total_risk > 5.0:
                recommendations.append("🟡 Moderate portfolio risk - monitor closely")
            else:
                recommendations.append("🟢 Good risk management - portfolio risk is well-controlled")
            
            if high_risk_count > len(stop_loss_results) * 0.3:
                recommendations.append("🔴 Many positions have high risk - review individual stop-loss levels")
            
            if avg_risk > 3.0:
                recommendations.append("📊 Average risk per trade is high - consider reducing ATR multipliers")
            elif avg_risk < 1.0:
                recommendations.append("💪 Conservative risk approach - could consider slightly wider stops")
            
            return {
                "total_portfolio_risk": round(total_risk, 2),
                "average_risk_per_trade": round(avg_risk, 2),
                "high_risk_positions": high_risk_count,
                "recommendations": recommendations,
                "timestamp": pd.Timestamp.now().isoformat()
            }
            
        except (KeyError, AttributeError) as e:
            logger.error(f"Key or attribute error in risk recommendations: {e}")
            return {"recommendation": "Error generating recommendations"}
        except (ValueError, TypeError) as e:
            logger.error(f"Value or type error in risk recommendations: {e}")
            return {"recommendation": "Error generating recommendations"}
        except ZeroDivisionError as e:
            logger.error(f"Division by zero in risk recommendations: {e}")
            return {"recommendation": "Error generating recommendations"}


# Global ATR stop-loss calculator instance
atr_stop_loss_calculator = ATRStopLossCalculator()