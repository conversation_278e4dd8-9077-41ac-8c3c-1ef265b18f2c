"""
Configuration compatibility layer for src.core.config

This module provides backward compatibility for existing imports
while using the new config_manager system.
"""

import os
from typing import Optional
from .config_manager import get_config, config
from .exceptions import ConfigurationError

# Create a settings object that mimics the expected interface
class Settings:
    """Compatibility settings object that provides the expected interface"""
    
    def __init__(self):
        self._config = get_config()
    
    @property
    def DEBUG(self) -> bool:
        """Get debug setting"""
        return self._config.get('app', 'debug', False)
    
    @property
    def MAX_CPU_THRESHOLD(self) -> float:
        """Get max CPU threshold for health checks"""
        return self._config.get('app', 'max_cpu_threshold', 90.0)
    
    @property
    def MAX_MEMORY_THRESHOLD(self) -> float:
        """Get max memory threshold for health checks"""
        return self._config.get('app', 'max_memory_threshold', 90.0)
    
    @property
    def MAX_DISK_THRESHOLD(self) -> float:
        """Get max disk threshold for health checks"""
        return self._config.get('app', 'max_disk_threshold', 90.0)
    
    @property
    def DATABASE_URL(self) -> str:
        """
        Get database URL from configuration
        
        Returns:
            str: The database connection URL
            
        Raises:
            ConfigurationError: If database URL is not configured
        """
        db_url = self._config.get('database', 'url', '')
        if not db_url:
            raise ConfigurationError(
                "Database URL is not configured. "
                "Please set DATABASE_URL in your environment."
            )
        return db_url
    
    @property
    def SUPABASE_URL(self) -> str:
        """
        Get Supabase project URL
        
        Returns:
            str: The Supabase project URL
            
        Raises:
            ConfigurationError: If Supabase URL is not configured
        """
        supabase_url = self._config.get('database', 'supabase_url', '')
        if not supabase_url:
            raise ConfigurationError(
                "Supabase URL is not configured. "
                "Please set SUPABASE_URL in your environment."
            )
        return supabase_url
    
    @property
    def SUPABASE_KEY(self) -> str:
        """
        Get Supabase API key
        
        Returns:
            str: The Supabase API key
            
        Raises:
            ConfigurationError: If Supabase key is not configured
        """
        supabase_key = self._config.get('database', 'supabase_key', '')
        if not supabase_key:
            raise ConfigurationError(
                "Supabase API key is not configured. "
                "Please set SUPABASE_KEY in your environment."
            )
        return supabase_key
    
    @property
    def USE_SUPABASE(self) -> bool:
        """
        Check if Supabase is being used
        
        Returns:
            bool: Always returns True as we only support Supabase
        """
        return True
    
    @property
    def REDIS_URL(self) -> str:
        """
        Get Redis URL
        
        Returns:
            str: The Redis connection URL
            
        Raises:
            ConfigurationError: If Redis URL is not configured
        """
        redis_url = self._config.get('redis', 'url', '')
        if not redis_url:
            raise ConfigurationError(
                "Redis URL is not configured. "
                "Please set REDIS_URL in your environment."
            )
        return redis_url
    
    @property
    def ENVIRONMENT(self) -> str:
        """Get environment setting"""
        return self._config.get('app', 'environment', 'development')
    
    def get_database_config(self) -> dict:
        """
        Get database configuration
        
        Returns:
            dict: Database configuration
        """
        return {
            'url': self.DATABASE_URL,
            'supabase_url': self.SUPABASE_URL,
            'supabase_key': self.SUPABASE_KEY,
            'pool_size': self._config.get('database', 'pool_size', 5),
            'max_overflow': self._config.get('database', 'max_overflow', 10),
            'connect_timeout': self._config.get('database', 'connect_timeout', 10),
            'statement_timeout': self._config.get('database', 'statement_timeout', 30000),
            'ssl_required': self._config.get('database', 'ssl_required', True)
        }

# Create global settings instance
settings = Settings()

# Also provide the get_settings function for backward compatibility
def get_settings() -> Settings:
    """Get settings instance for backward compatibility"""
    return settings

# Export the main config for direct access
__all__ = ['settings', 'get_settings', 'config']