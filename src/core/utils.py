import uuid
import re
from typing import Any, Dict, Optional
import pytz
from datetime import datetime, time, timedelta

def generate_unique_id() -> str:
    """Generate a unique identifier."""
    return str(uuid.uuid4())

def sanitize_symbol(symbol: str) -> str:
    """
    Sanitize stock symbol input.
    
    Removes any non-alphanumeric characters and converts to uppercase.
    
    Args:
        symbol (str): Input stock symbol
    
    Returns:
        str: Cleaned and validated stock symbol
    """
    if not symbol:
        return ""
    
    # Remove any non-alphanumeric characters and convert to uppercase
    cleaned_symbol = re.sub(r'[^A-Za-z0-9]', '', symbol).upper()
    
    return cleaned_symbol

def safe_get(
    data: Dict[str, Any], 
    key: str, 
    default: Optional[Any] = None
) -> Optional[Any]:
    """
    Safely retrieve a value from a dictionary.
    
    Args:
        data (dict): Dictionary to search
        key (str): Key to retrieve
        default (Any, optional): Default value if key not found
    
    Returns:
        Value associated with key or default
    """
    return data.get(key, default)

def truncate_text(
    text: str, 
    max_length: int = 1000, 
    ellipsis: str = "..."
) -> str:
    """
    Truncate text to a specified maximum length.
    
    Args:
        text (str): Input text
        max_length (int): Maximum length of text
        ellipsis (str): Suffix to add if text is truncated
    
    Returns:
        str: Truncated text
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(ellipsis)] + ellipsis 

def is_market_open() -> Dict[str, Any]:
    """
    Check if US stock markets are currently open
    
    Returns:
        Dict containing market status and timing information
    """
    # Eastern Time (ET) - US markets operate on ET
    et_tz = pytz.timezone('US/Eastern')
    now_et = datetime.now(et_tz)
    
    # Market hours: 9:30 AM - 4:00 PM ET, Monday-Friday
    market_open = time(9, 30)
    market_close = time(16, 0)
    
    # Check if it's a weekday
    is_weekday = now_et.weekday() < 5  # Monday = 0, Friday = 4
    
    # Check if current time is within market hours
    current_time = now_et.time()
    is_within_hours = market_open <= current_time <= market_close
    
    # Check if markets are open
    markets_open = is_weekday and is_within_hours
    
    # Calculate time until next market open/close
    if markets_open:
        # Markets are open, calculate time until close
        if current_time < market_close:
            close_time = datetime.combine(now_et.date(), market_close, tzinfo=et_tz)
            time_until_close = (close_time - now_et).total_seconds() / 3600  # hours
        else:
            time_until_close = 0
        time_until_open = None
    else:
        # Markets are closed, calculate time until next open
        if is_weekday and current_time < market_open:
            # Today, before market open
            next_open = datetime.combine(now_et.date(), market_open, tzinfo=et_tz)
        elif is_weekday and current_time > market_close:
            # Today, after market close
            next_open = datetime.combine(now_et.date() + timedelta(days=1), market_open, tzinfo=et_tz)
        else:
            # Weekend or holiday
            days_until_monday = (7 - now_et.weekday()) % 7
            if days_until_monday == 0:  # Today is Monday
                days_until_monday = 7
            next_open = datetime.combine(now_et.date() + timedelta(days=days_until_monday), market_open, tzinfo=et_tz)
        
        time_until_open = (next_open - now_et).total_seconds() / 3600  # hours
        time_until_close = None
    
    return {
        'markets_open': markets_open,
        'current_time_et': now_et.strftime('%I:%M %p ET'),
        'current_date': now_et.strftime('%A, %B %d, %Y'),
        'is_weekday': is_weekday,
        'time_until_open': round(time_until_open, 1) if time_until_open else None,
        'time_until_close': round(time_until_close, 1) if time_until_close else None,
        'market_hours': '9:30 AM - 4:00 PM ET',
        'timezone': 'US/Eastern'
    }

def get_market_context() -> str:
    """
    Get a human-readable market context message
    
    Returns:
        String describing current market status
    """
    market_status = is_market_open()
    
    if market_status['markets_open']:
        if market_status['time_until_close']:
            return f"🟢 **Markets OPEN** ({market_status['current_time_et']})\n⏰ Closes in {market_status['time_until_close']} hours"
        else:
            return f"🟢 **Markets OPEN** ({market_status['current_time_et']})\n⏰ Closing soon"
    else:
        if market_status['time_until_open']:
            if market_status['time_until_open'] < 24:
                return f"🔴 **Markets CLOSED** ({market_status['current_time_et']})\n⏰ Opens in {market_status['time_until_open']} hours"
            else:
                days = int(market_status['time_until_open'] // 24)
                hours = int(market_status['time_until_open'] % 24)
                if days > 0:
                    return f"🔴 **Markets CLOSED** ({market_status['current_time_et']})\n⏰ Opens in {days} days, {hours} hours"
                else:
                    return f"🔴 **Markets CLOSED** ({market_status['current_time_et']})\n⏰ Opens in {hours} hours"
        else:
            return f"🔴 **Markets CLOSED** ({market_status['current_time_et']})"

def add_market_context_to_response(response: str, include_timing: bool = True) -> str:
    """
    Add market context to a response
    
    Args:
        response: Original response text
        include_timing: Whether to include timing information
    
    Returns:
        Response with market context added
    """
    market_status = is_market_open()
    
    if include_timing:
        context = get_market_context()
        return f"{response}\n\n{context}"
    else:
        # Just add basic open/closed status
        status = "🟢 **Markets OPEN**" if market_status['markets_open'] else "🔴 **Markets CLOSED**"
        return f"{response}\n\n{status}"

def validate_market_hours_for_query(query: str) -> Dict[str, Any]:
    """
    Validate if a query is appropriate for current market hours
    
    Args:
        query: User query text
    
    Returns:
        Dict with validation results and recommendations
    """
    market_status = is_market_open()
    query_lower = query.lower()
    
    # Queries that absolutely require real-time data (only block these)
    absolute_real_time_keywords = [
        'live trading', 'real-time signals', 'current market orders', 'live order book',
        'instant execution', 'live portfolio', 'real-time alerts', 'execute trade now',
        'place order', 'market order', 'limit order', 'stop loss now'
    ]
    
    # Queries that can work with delayed/after-hours data (allow these)
    delayed_data_keywords = [
        'current price', 'what is', 'how much is', 'show me the price', 'current value',
        'price of', 'value of', 'stock price', 'share price'
    ]
    
    # Queries that are educational or don't need real-time data (always allow)
    educational_keywords = [
        'explain', 'how to', 'strategy', 'analysis', 'indicators',
        'support', 'resistance', 'trend', 'pattern', 'risk', 'education',
        'what does', 'how does', 'why', 'when', 'where'
    ]
    
    # Check query types
    needs_absolute_real_time = any(keyword in query_lower for keyword in absolute_real_time_keywords)
    can_use_delayed_data = any(keyword in query_lower for keyword in delayed_data_keywords)
    is_educational = any(keyword in query_lower for keyword in educational_keywords)
    
    # Determine if query is appropriate
    if needs_absolute_real_time and not market_status['markets_open']:
        appropriate = False
        recommendation = "This query requires live market execution that's only available during trading hours."
    elif can_use_delayed_data and not market_status['markets_open']:
        # Allow these queries but note data may be delayed
        appropriate = True
        recommendation = "Query can be answered with available data, though it may be delayed from market close."
    elif is_educational:
        appropriate = True
        recommendation = "Educational queries are always appropriate regardless of market hours."
    else:
        # Default to allowing the query
        appropriate = True
        recommendation = "Query is appropriate for current market status."
    
    return {
        'appropriate': appropriate,
        'requires_real_time': needs_absolute_real_time,
        'can_use_delayed_data': can_use_delayed_data,
        'is_educational': is_educational,
        'markets_open': market_status['markets_open'],
        'recommendation': recommendation,
        'market_context': get_market_context()
    } 