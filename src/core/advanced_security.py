from enum import Enum, auto
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio
import pyotp
import jwt
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from pydantic import BaseModel
import logging

from src.core.secure_cache import get_secure_cache

class UserRole(Enum):
    """Enumeration of user roles with hierarchical access"""
    ADMIN = "admin"
    TRADER = "trader"
    VIEWER = "viewer"

class RoleBasedAccessControl:
    """Comprehensive Role-Based Access Control System"""
    
    ROLE_HIERARCHY = {
        UserRole.ADMIN: 3,
        UserRole.TRADER: 2,
        UserRole.VIEWER: 1
    }
    
    @classmethod
    def has_permission(cls, current_role: UserRole, required_role: UserRole) -> bool:
        """
        Check if current role has sufficient permissions
        
        Args:
            current_role (UserRole): Current user's role
            required_role (UserRole): Required role for the action
        
        Returns:
            bool: Whether access is permitted
        """
        return cls.ROLE_HIERARCHY.get(current_role, 0) >= cls.ROLE_HIERARCHY.get(required_role, 0)

class MultiFactorAuthenticator:
    """Advanced Multi-Factor Authentication Manager"""
    
    def __init__(self, user):
        """
        Initialize MFA for a specific user
        
        Args:
            user: User object with MFA-related attributes
        """
        self.user = user
        self.mfa_methods = {
            'totp': self._validate_totp,
            'email': self._send_email_code,
            'sms': self._send_sms_code
        }
        # Use secure Redis cache instead of in-memory storage
        self._verification_cache = get_secure_cache('verification_codes')
    
    async def generate_mfa_challenge(self, method: str = 'totp') -> Dict[str, str]:
        """
        Generate a multi-factor authentication challenge
        
        Args:
            method (str): MFA method (default: TOTP)
        
        Returns:
            Dict[str, str]: Challenge details
        """
        if method not in self.mfa_methods:
            raise ValueError(f"Unsupported MFA method: {method}")
        
        # Call the appropriate method with await if it's async
        if method in ['email', 'sms']:
            return await self.mfa_methods[method]()
        else:
            return self.mfa_methods[method]()
    
    def _validate_totp(self) -> Dict[str, str]:
        """
        Generate TOTP (Time-based One-Time Password) challenge
        
        Returns:
            Dict[str, str]: TOTP challenge details
        """
        # Generate a secret for the user if not exists
        if not hasattr(self.user, 'mfa_secret') or not self.user.mfa_secret:
            self.user.mfa_secret = pyotp.random_base32()
        
        totp = pyotp.TOTP(self.user.mfa_secret)
        return {
            'challenge_type': 'totp',
            'qr_code': totp.provisioning_uri(
                name=self.user.email, 
                issuer_name='TradingBot'
            )
        }
    
    async def _send_email_code(self) -> Dict[str, str]:
        """
        Generate and send email verification code
        
        Returns:
            Dict[str, str]: Email verification challenge
        """
        try:
            # Generate a secure verification code
            verification_code = pyotp.random_base32()[:6]
            
            # Store the code securely with expiration
            await self._store_verification_code('email', verification_code)
            
            # TODO: Implement actual email sending service integration
            # For now, log the code for development/testing
            logging.info(f"Email verification code generated for user {self.user.email}: {verification_code}")
            
            return {
                'challenge_type': 'email',
                'message': 'Verification code sent to email',
                'status': 'success'
            }
        except Exception as e:
            logging.error(f"Failed to send email verification code: {e}")
            return {
                'challenge_type': 'email',
                'message': 'Failed to send verification code',
                'status': 'error',
                'error': str(e)
            }
    
    async def _send_sms_code(self) -> Dict[str, str]:
        """
        Generate and send SMS verification code
        
        Returns:
            Dict[str, str]: SMS verification challenge
        """
        try:
            # Generate a secure verification code
            verification_code = pyotp.random_base32()[:6]
            
            # Store the code securely with expiration
            await self._store_verification_code('sms', verification_code)
            
            # TODO: Implement actual SMS service integration (Twilio, etc.)
            # For now, log the code for development/testing
            logging.info(f"SMS verification code generated for user {self.user.phone}: {verification_code}")
            
            return {
                'challenge_type': 'sms',
                'message': 'Verification code sent via SMS',
                'status': 'success'
            }
        except Exception as e:
            logging.error(f"Failed to send SMS verification code: {e}")
            return {
                'challenge_type': 'sms',
                'message': 'Failed to send verification code',
                'status': 'error',
                'error': str(e)
            }
    
    async def _store_verification_code(self, method: str, code: str) -> None:
        """
        Store verification code securely with expiration
        
        Args:
            method (str): Verification method (email/sms)
            code (str): Verification code to store
        """
        try:
            # Store in secure Redis cache with 10-minute expiration
            cache_key = f"verification_{method}_{self.user.id}"
            
            # Store code with metadata
            await self._verification_cache.set(
                key=cache_key,
                value={
                    'code': code,
                    'created_at': datetime.now().isoformat(),
                    'attempts': 0
                },
                ttl_seconds=600  # 10 minutes expiration
            )
            
            logging.info(f"Verification code stored securely for user {self.user.id}")
            
        except Exception as e:
            logging.error(f"Failed to store verification code: {e}")
            raise
    
    async def verify_mfa_token(self, method: str, token: str) -> bool:
        """
        Verify multi-factor authentication token
        
        Args:
            method (str): MFA method
            token (str): Token to verify
        
        Returns:
            bool: Whether the token is valid
        """
        if method == 'totp':
            totp = pyotp.TOTP(self.user.mfa_secret)
            return totp.verify(token)
        
        # For email and SMS verification
        if method in ['email', 'sms']:
            cache_key = f"verification_{method}_{self.user.id}"
            
            # Get stored verification data
            stored_data = await self._verification_cache.get(cache_key)
            
            if not stored_data:
                logging.warning(f"No verification code found for {method} and user {self.user.id}")
                return False
            
            # Check if code matches
            if stored_data['code'] == token:
                # Delete the used code
                await self._verification_cache.delete(cache_key)
                return True
            else:
                # Increment failed attempts
                stored_data['attempts'] += 1
                
                # Update stored data with incremented attempts
                await self._verification_cache.set(
                    key=cache_key,
                    value=stored_data,
                    ttl_seconds=600  # Keep the same TTL
                )
                
                # If too many attempts, invalidate the code
                if stored_data['attempts'] >= 3:
                    await self._verification_cache.delete(cache_key)
                    logging.warning(f"Too many failed attempts for {method} verification for user {self.user.id}")
                
                return False
        
        # Unsupported method
        return False

class AdaptiveRateLimiter:
    """
    Intelligent, adaptive rate limiting system
    
    Dynamically adjusts rate limits based on user behavior
    """
    
    def __init__(self, base_limit: int = 100, learning_rate: float = 0.1):
        """
        Initialize Adaptive Rate Limiter
        
        Args:
            base_limit (int): Initial request limit
            learning_rate (float): Rate of limit adjustment
        """
        self.request_history: Dict[str, List[Dict]] = {}
        self.user_limits: Dict[str, float] = {}  # Track per-user limits
        self.base_limit = base_limit
        self.learning_rate = learning_rate
        self.last_recovery_check: Dict[str, datetime] = {}  # Track last recovery time
    
    def is_allowed(self, user_id: str, request: Request) -> bool:
        """
        Determine if a request is allowed based on adaptive rate limiting
        
        Args:
            user_id (str): Unique user identifier
            request (Request): Incoming request object
        
        Returns:
            bool: Whether the request is allowed
        """
        current_time = datetime.now()
        user_requests = self.request_history.get(user_id, [])
        
        # Remove expired requests (last minute)
        user_requests = [
            req for req in user_requests 
            if current_time - req['timestamp'] < timedelta(minutes=1)
        ]
        
        # Calculate dynamic limit based on recent behavior
        dynamic_limit = self._calculate_dynamic_limit(user_requests, user_id)
        
        # If requests exceed dynamic limit, block
        if len(user_requests) >= dynamic_limit:
            return False
        
        # Record this request
        user_requests.append({
            'timestamp': current_time,
            'endpoint': str(request.url.path)
        })
        self.request_history[user_id] = user_requests
        
        return True
    
    def _calculate_dynamic_limit(self, requests: List[Dict], user_id: str) -> int:
        """
        Calculate dynamic rate limit based on request patterns
        
        Args:
            requests (List[Dict]): Recent request history
            user_id (str): Unique user identifier
        
        Returns:
            int: Dynamically adjusted rate limit
        """
        current_time = datetime.now()
        
        # Get current limit for this user (or base limit if not set)
        current_limit = self.user_limits.get(user_id, self.base_limit)
        
        # Check if we should recover the rate limit
        last_recovery = self.last_recovery_check.get(user_id, datetime.min)
        recovery_due = (current_time - last_recovery).total_seconds() > 60  # Check every minute
        
        if recovery_due:
            self.last_recovery_check[user_id] = current_time
            # If current limit is below base limit, gradually increase it
            if current_limit < self.base_limit:
                # Recover by 25% of the difference up to base limit
                recovery_amount = (self.base_limit - current_limit) * 0.25
                new_limit = min(current_limit + recovery_amount, self.base_limit)
                self.user_limits[user_id] = new_limit
                logging.info(f"Rate limit for user {user_id} recovered from {current_limit:.1f} to {new_limit:.1f}")
                current_limit = new_limit
        
        # If requests exceed current limit, reduce the limit
        if len(requests) >= current_limit:
            # Calculate new reduced limit (reduce by 25%)
            reduced_limit = max(current_limit * 0.75, 5)  # Never go below 5 requests
            self.user_limits[user_id] = reduced_limit
            
            # Reduce the number of stored requests to match new limit
            reduced_requests = requests[:int(reduced_limit)]
            self.request_history[user_id] = reduced_requests
            
            logging.warning(f"Rate limit for user {user_id} reduced to {reduced_limit:.1f} due to high traffic")
            return int(reduced_limit)
        
        return int(current_limit)

def role_required(required_role: Optional[UserRole] = None):
    """
    Flexible security decorator that allows configurable access control
    
    Args:
        required_role (Optional[UserRole]): Minimum role required to access the endpoint
    
    Returns:
        Callable: Decorator with intelligent access control
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract request object from args or kwargs
            request = None
            for arg in args:
                if hasattr(arg, 'headers') and hasattr(arg, 'cookies'):
                    request = arg
                    break
            
            if request is None and 'request' in kwargs:
                request = kwargs['request']
            
            # Log the access attempt
            logging.info(f"Accessing {func.__name__} with role requirement: {required_role}")
            
            # If no specific role is required, allow access
            if required_role is None:
                return await func(*args, **kwargs)
            
            # Retrieve current user's role from request
            current_user_role = await get_current_user_role(request)
            
            # Intelligent access control
            if RoleBasedAccessControl.has_permission(current_user_role, required_role):
                return await func(*args, **kwargs)
            else:
                # Log security event
                logging.warning(f"Access denied to {func.__name__}. Required: {required_role}, Current: {current_user_role}")
                
                # Provide a more informative error
                raise HTTPException(
                    status_code=403, 
                    detail=f"Insufficient permissions. Required role: {required_role}"
                )
        
        return wrapper
    return decorator


async def get_current_user_role(request: Optional[Any]) -> UserRole:
    """
    Extract the current user's role from the request
    
    Args:
        request: FastAPI request object
        
    Returns:
        UserRole: The user's role
    """
    try:
        if request is None:
            return UserRole.VIEWER  # Default role if no request
        
        # Try to get from authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.replace('Bearer ', '')
            # Decode JWT token
            try:
                from src.core.config_manager import get_config
                config = get_config()
                secret_key = config.get('security', 'jwt_secret', '')
                
                if not secret_key:
                    logging.warning("JWT secret key not configured")
                    return UserRole.VIEWER
                
                payload = jwt.decode(token, secret_key, algorithms=['HS256'])
                role_str = payload.get('role', 'viewer')
                
                # Convert string to UserRole enum
                try:
                    return UserRole(role_str)
                except ValueError:
                    logging.warning(f"Invalid role in token: {role_str}")
                    return UserRole.VIEWER
                    
            except jwt.PyJWTError as e:
                logging.warning(f"Invalid JWT token: {e}")
                return UserRole.VIEWER
        
        # Try to get from session
        if hasattr(request, 'session') and 'user_role' in request.session:
            role_str = request.session['user_role']
            try:
                return UserRole(role_str)
            except ValueError:
                pass
        
        # Default to viewer role
        return UserRole.VIEWER
        
    except Exception as e:
        logging.error(f"Error determining user role: {e}")
        return UserRole.VIEWER  # Default to lowest privilege on error 