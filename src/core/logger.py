"""
Unified Logging System for Trading Automation

Consolidates all logging functionality into a single, comprehensive system
that provides consistent logging across all components with advanced features
like correlation tracking, structured logging, and specialized loggers.
"""

import logging
import sys
import os
import time
import functools
import uuid
import traceback
from typing import Dict, Any, Optional, Callable, TypeVar, ParamSpec
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from datetime import datetime
import json
from pathlib import Path
from contextlib import contextmanager
import contextvars

# Import the new unified configuration
from .config_manager import config

# Type variables for decorators
T = TypeVar('T')
P = ParamSpec('P')

# Context variable to hold correlation id for the current context
correlation_context: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar('correlation_id', default=None)

def generate_correlation_id(prefix: str = "cmd") -> str:
    """Generates traceable IDs like `cmd-1a2b3c4d-123456789`."""
    return f"{prefix}-{uuid.uuid4().hex[:8]}-{int(time.time())}"

class StructuredFormatter(logging.Formatter):
    """
    Custom formatter that outputs structured JSON logs for better parsing
    """
    
    def format(self, record):
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "correlation_id": getattr(record, 'correlation_id', None)
        }
        
        # Add extra fields if present
        extra_fields = [
            'pipeline_id', 'user_id', 'event_type', 'tool_name', 
            'symbol', 'result_size', 'success', 'error', 
            'response_length', 'model_used', 'execution_time',
            'section', 'quality_score', 'tool_calls_made'
        ]
        
        for field in extra_fields:
            if hasattr(record, field):
                log_entry[field] = getattr(record, field)
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry)

class BaseLogger:
    """Base logger class with common functionality"""
    
    def __init__(self, name: str):
        self.name = name
        self.correlation_id: Optional[str] = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Set up the logger with proper handlers and formatters"""
        self.logger = logging.getLogger(self.name)
        
        # Only add handlers if none exist
        if not self.logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(StructuredFormatter())
            self.logger.addHandler(console_handler)
            
            # File handler for persistent logs (only if not console-only mode)
            if not os.getenv('LOG_TO_CONSOLE_ONLY', '').lower() == 'true':
                try:
                    log_dir = Path("logs")
                    log_dir.mkdir(exist_ok=True)

                    file_handler = RotatingFileHandler(
                        log_dir / f"{self.name}.log",
                        maxBytes=10*1024*1024,  # 10MB
                        backupCount=5
                    )
                    file_handler.setFormatter(StructuredFormatter())
                    self.logger.addHandler(file_handler)
                except (PermissionError, OSError) as e:
                    # If file logging fails, continue with console only
                    print(f"Warning: Could not set up file logging: {e}")
                    pass
            
            # Set log level
            self.logger.setLevel(logging.INFO)
    
    def set_correlation_id(self, correlation_id: Optional[str] = None):
        """Set or generate a correlation ID"""
        self.correlation_id = correlation_id or generate_correlation_id()
        # Populate the context var so non-converted loggers can still pick it up
        try:
            correlation_context.set(self.correlation_id)
        except Exception:
            pass
        return self
    
    def _log_with_context(self, level: int, message: str, **kwargs):
        """Log with additional context and correlation"""
        # Extract special logging parameters
        exc_info = kwargs.pop('exc_info', None)
        stack_info = kwargs.pop('stack_info', None)
        
        # Create extra dict with remaining kwargs
        extra = kwargs.copy()
        extra['correlation_id'] = self.correlation_id
        
        # Ensure the record has the correlation ID
        self.logger.log(level, message, extra=extra, exc_info=exc_info, stack_info=stack_info)
    
    def debug(self, message: str, **kwargs):
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self._log_with_context(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        self._log_with_context(logging.CRITICAL, message, **kwargs)

class EnhancedLogger(BaseLogger):
    """
    Enhanced logger with advanced tracking and correlation capabilities
    """
    
    def log_with_context(
        self, 
        level: int, 
        message: str, 
        **kwargs
    ):
        """Log with additional context and correlation"""
        extra = kwargs.copy()
        extra['correlation_id'] = self.correlation_id
        
        # Ensure the record has the correlation ID
        self.logger.log(level, message, extra=extra)

class AIInteractionLogger(BaseLogger):
    """Specialized logger for AI interactions"""
    
    def __init__(self):
        super().__init__("ai_interactions")
    
    def log_tool_call_start(
        self, 
        tool_name: str, 
        params: Dict[str, Any], 
        symbol: Optional[str] = None
    ):
        """Log the start of a tool call"""
        self.info(
            f"Tool call start: {tool_name}",
            tool_name=tool_name,
            params=params,
            symbol=symbol,
            event_type="tool_call_start",
            timestamp=time.time()
        )
    
    def log_tool_call_end(
        self, 
        tool_name: str, 
        result_size: int, 
        success: bool, 
        symbol: Optional[str] = None
    ):
        """Log the completion of a tool call"""
        self.info(
            f"Tool call end: {tool_name}",
            tool_name=tool_name,
            result_size=result_size,
            success=success,
            symbol=symbol,
            event_type="tool_call_end",
            timestamp=time.time()
        )
    
    def log_tool_call_error(
        self, 
        tool_name: str, 
        error: str, 
        symbol: Optional[str] = None
    ):
        """Log tool call errors"""
        self.error(
            f"Tool call error: {tool_name}",
            tool_name=tool_name,
            error=error,
            symbol=symbol,
            event_type="tool_call_error",
            timestamp=time.time()
        )
    
    def log_ai_response(
        self, 
        response: str, 
        tool_calls_made: list, 
        model_used: str
    ):
        """Log AI response with tool call validation"""
        self.info(
            "AI response generated",
            response_length=len(response),
            tool_calls_made=tool_calls_made,
            model_used=model_used,
            event_type="ai_response",
            timestamp=time.time()
        )

class PipelineLogger(BaseLogger):
    """Enhanced logger with pipeline-specific methods"""
    
    def __init__(self, name: str):
        super().__init__(f"pipeline.{name}")
    
    def log_section_start(self, section_name: str, **kwargs):
        """Log the start of a pipeline section"""
        self.info(
            f"Pipeline section started: {section_name}",
            section=section_name,
            event_type="section_start",
            timestamp=time.time(),
            **kwargs
        )
    
    def log_section_complete(self, section_name: str, quality_score: float, **kwargs):
        """Log the completion of a pipeline section"""
        self.info(
            f"Pipeline section completed: {section_name}",
            section=section_name,
            quality_score=quality_score,
            event_type="section_complete",
            timestamp=time.time(),
            **kwargs
        )
    
    def log_section_error(self, section_name: str, error: str, **kwargs):
        """Log pipeline section errors"""
        self.error(
            f"Pipeline section error: {section_name}",
            section=section_name,
            error=error,
            event_type="section_error",
            timestamp=time.time(),
            **kwargs
        )

class TradingBotLogger(BaseLogger):
    """
    Enhanced logger with specialized methods for different trading bot components
    """
    
    def __init__(self, name: str):
        super().__init__(f"trading_bot.{name}")
    
    def log_trade_signal(
        self, 
        symbol: str, 
        signal_type: str, 
        confidence: float, 
        **kwargs
    ):
        """Log trading signals"""
        self.info(
            f"Trade signal: {signal_type} for {symbol}",
            symbol=symbol,
            signal_type=signal_type,
            confidence=confidence,
            event_type="trade_signal",
            timestamp=time.time(),
            **kwargs
        )
    
    def log_market_data(
        self, 
        symbol: str, 
        data_type: str, 
        data_size: int, 
        **kwargs
    ):
        """Log market data operations"""
        self.info(
            f"Market data: {data_type} for {symbol}",
            symbol=symbol,
            data_type=data_type,
            data_size=data_size,
            event_type="market_data",
            timestamp=time.time(),
            **kwargs
        )

    def log_interaction(
        self,
        *,
        user_input: str,
        raw_ai_output: str,
        final_response: str,
        template_used: Optional[str] = None,
        command_type: Optional[str] = None,
        user_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        **kwargs
    ):
        """Log a complete user interaction handled by the trading bot"""
        self.info(
            "User interaction logged",
            user_input=user_input,
            raw_ai_output=(raw_ai_output if raw_ai_output is not None else ""),
            final_response=(final_response if final_response is not None else ""),
            template_used=template_used,
            command_type=command_type,
            user_id=user_id,
            correlation_id=correlation_id,
            event_type="interaction",
            timestamp=time.time(),
            **kwargs
        )

# Factory functions for creating loggers
def get_logger(name: str) -> BaseLogger:
    """Get a base logger instance"""
    return BaseLogger(name)

def get_ai_logger() -> AIInteractionLogger:
    """Get an AI interaction logger instance"""
    return AIInteractionLogger()

def get_pipeline_logger(name: str) -> PipelineLogger:
    """Get a pipeline logger instance"""
    return PipelineLogger(name)

def get_trading_logger(name: str) -> TradingBotLogger:
    """Get a trading bot logger instance"""
    return TradingBotLogger(name)

# Decorators for logging function calls
def log_function_call(logger: Optional[BaseLogger] = None):
    """
    Decorator to log function calls with timing and correlation
    """
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @functools.wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            # Get logger from context if not provided
            func_logger = logger or get_logger(func.__module__)
            
            start_time = time.time()
            correlation_id = generate_correlation_id("func")
            func_logger.set_correlation_id(correlation_id)
            
            try:
                func_logger.info(
                    f"Function call started: {func.__name__}",
                    function=func.__name__,
                    event_type="function_start",
                    timestamp=start_time
                )
                
                result = func(*args, **kwargs)
                
                execution_time = time.time() - start_time
                func_logger.info(
                    f"Function call completed: {func.__name__}",
                    function=func.__name__,
                    event_type="function_complete",
                    execution_time=execution_time,
                    timestamp=time.time()
                )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                func_logger.error(
                    f"Function call failed: {func.__name__}",
                    function=func.__name__,
                    event_type="function_error",
                    error=str(e),
                    execution_time=execution_time,
                    timestamp=time.time()
                )
                raise
        
        return wrapper
    return decorator

# Context manager for logging operations
@contextmanager
def log_operation(logger: BaseLogger, operation_name: str, **context):
    """
    Context manager for logging operations with start/end and error handling
    """
    start_time = time.time()
    correlation_id = generate_correlation_id("op")
    logger.set_correlation_id(correlation_id)
    
    try:
        logger.info(
            f"Operation started: {operation_name}",
            operation=operation_name,
            event_type="operation_start",
            timestamp=start_time,
            **context
        )
        
        yield logger
        
        execution_time = time.time() - start_time
        logger.info(
            f"Operation completed: {operation_name}",
            operation=operation_name,
            event_type="operation_complete",
            execution_time=execution_time,
            timestamp=time.time(),
            **context
        )
        
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(
            f"Operation failed: {operation_name}",
            operation=operation_name,
            event_type="operation_error",
            error=str(e),
            execution_time=execution_time,
            timestamp=time.time(),
            **context
        )
        raise

# Global logger instance for backward compatibility
default_logger = get_logger("default")

def log(level: int, message: str, **kwargs):
    """Global logging function for backward compatibility"""
    default_logger._log_with_context(level, message, **kwargs)

def debug(message: str, **kwargs):
    """Global debug logging for backward compatibility"""
    default_logger.debug(message, **kwargs)

def info(message: str, **kwargs):
    """Global info logging for backward compatibility"""
    default_logger.info(message, **kwargs)

def warning(message: str, **kwargs):
    """Global warning logging for backward compatibility"""
    default_logger.warning(message, **kwargs)

def error(message: str, **kwargs):
    """Global error logging for backward compatibility"""
    default_logger.error(message, **kwargs)

def critical(message: str, **kwargs):
    """Global critical logging for backward compatibility"""
    default_logger.critical(message, **kwargs) 