"""
Core prompt management module for trading analysis system.
Located at: src/core/prompts/

This module centralizes all prompt-related functionality including:
- Intent classification
- Symbol extraction  
- Tool requirement determination
- Response generation
- Compliance validation
"""

from .models import (
    IntentType, ToolType, IntentClassification, AnalysisPrompt, 
    ComplianceTemplate, PromptResult
)

from .prompt_manager import PromptManager

__all__ = [
    'IntentType',
    'ToolType', 
    'IntentClassification',
    'AnalysisPrompt',
    'ComplianceTemplate',
    'PromptResult',
    'PromptManager'
]

# Initialize global prompt manager instance
prompt_manager = PromptManager()