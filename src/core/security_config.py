"""
Centralized security configuration for the trading automation system.
"""

import os
import re
import secrets
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field

@dataclass
class SecurityConfig:
    """Security configuration settings."""
    
    # JWT Configuration
    JWT_SECRET: str = os.getenv('JWT_SECRET', '')
    JWT_ALGORITHM: str = 'HS256'
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 15
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = int(os.getenv('RATE_LIMIT_REQUESTS', '100'))
    RATE_LIMIT_WINDOW: int = int(os.getenv('RATE_LIMIT_WINDOW', '60'))
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = field(default_factory=lambda: [])
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_MAX_AGE: int = 600
    
    # Security Headers
    SECURITY_HEADERS: Dict[str, str] = field(default_factory=lambda: {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
    })
    
    # Password Policy
    MIN_PASSWORD_LENGTH: int = 12
    REQUIRE_UPPERCASE: bool = True
    REQUIRE_LOWERCASE: bool = True
    REQUIRE_DIGITS: bool = True
    REQUIRE_SPECIAL_CHARS: bool = True
    
    # Session Management
    SESSION_TIMEOUT_MINUTES: int = 30
    MAX_CONCURRENT_SESSIONS: int = 3
    
    # API Security
    API_KEY_HEADER: str = 'X-API-Key'
    API_KEY_LENGTH: int = 32
    
    # Audit Logging
    AUDIT_LOG_ENABLED: bool = True
    AUDIT_LOG_LEVEL: str = 'INFO'
    AUDIT_LOG_RETENTION_DAYS: int = 90
    
    @staticmethod
    def sanitize_input(input_str: Optional[str], max_length: int = 255) -> str:
        """
        Sanitize input string by removing potentially harmful characters.
        
        Args:
            input_str (str): Input string to sanitize
            max_length (int): Maximum allowed length
        
        Returns:
            str: Sanitized input string
        """
        if input_str is None:
            return ""
        
        # Remove HTML and script tags completely, including their content
        sanitized = re.sub(r'<script.*?</script>', '', input_str, flags=re.DOTALL)
        
        # Remove any remaining HTML tags
        sanitized = re.sub(r'<[^>]+>', '', sanitized)
        
        # Remove special characters and parentheses, keep alphanumeric and some punctuation
        sanitized = re.sub(r'[^\w\s.,!?-]', '', sanitized)
        
        # Truncate to max length
        return sanitized[:max_length].strip()
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """
        Generate a cryptographically secure random token.
        
        Args:
            length (int): Length of the token
        
        Returns:
            str: Secure random token
        """
        return secrets.token_hex(length // 2)
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """
        Validate email format.
        
        Args:
            email (str): Email address to validate
        
        Returns:
            bool: True if email is valid, False otherwise
        """
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(email_regex, email) is not None
    
    def validate(self) -> List[str]:
        """Validate security configuration."""
        errors = []
        
        if not self.JWT_SECRET:
            errors.append("JWT_SECRET is required")
        
        if len(self.JWT_SECRET) < 32:
            errors.append("JWT_SECRET must be at least 32 characters")
        
        if self.RATE_LIMIT_REQUESTS < 1:
            errors.append("RATE_LIMIT_REQUESTS must be positive")
        
        if self.RATE_LIMIT_WINDOW < 1:
            errors.append("RATE_LIMIT_WINDOW must be positive")
        
        return errors
    
    def get_cors_origins(self, is_production: bool = False) -> List[str]:
        """Get CORS origins based on environment."""
        if self.CORS_ORIGINS:
            return self.CORS_ORIGINS
        
        if is_production:
            return []  # No CORS in production by default
        else:
            return ["http://${FRONTEND_URL}", "http://${FRONTEND_URL}"]
    
    def get_security_headers(self, is_https: bool = False) -> Dict[str, str]:
        """Get security headers based on environment."""
        headers = self.SECURITY_HEADERS.copy()
        
        if is_https:
            headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return headers

# Global security configuration instance
security_config = SecurityConfig()

def get_security_config() -> SecurityConfig:
    """Get the global security configuration."""
    return security_config

def validate_security_config() -> None:
    """Validate security configuration and raise errors if invalid."""
    errors = security_config.validate()
    if errors:
        raise ValueError(f"Security configuration errors: {', '.join(errors)}") 