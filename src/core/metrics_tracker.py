import json
import os
from datetime import datetime, date
from typing import Dict, Any, List, Optional
from enum import Enum
import uuid
from pathlib import Path

class MetricType(Enum):
    """Types of metrics that can be tracked"""
    RESPONSE_GENERATION = "response_generation"
    RESPONSE_QUALITY = "response_quality"
    USER_FEEDBACK = "user_feedback"
    PERFORMANCE = "performance"
    ERROR = "error"

class MetricsTracker:
    """
    Centralized metrics tracking system for monitoring response generation
    and system performance with persistent storage
    """
    
    def __init__(self, metrics_file: str = 'response_metrics.json'):
        self.metrics_file = metrics_file
        self.metrics_data = self._load_metrics()
    
    def _load_metrics(self) -> Dict[str, Any]:
        """Load existing metrics data"""
        if os.path.exists(self.metrics_file):
            try:
                with open(self.metrics_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        return {
            'total_responses_generated': 0,
            'responses_by_type': {},
            'daily_metrics': {},
            'performance_metrics': {
                'average_response_time': 0,
                'total_response_time': 0,
                'response_count': 0
            },
            'quality_metrics': {
                'average_confidence': 0,
                'total_confidence': 0,
                'confidence_count': 0
            },
            'error_metrics': {
                'total_errors': 0,
                'errors_by_type': {}
            }
        }
    
    def _save_metrics(self):
        """Save metrics data to file"""
        # Ensure directory exists
        Path(self.metrics_file).parent.mkdir(parents=True, exist_ok=True)
        
        with open(self.metrics_file, 'w') as f:
            json.dump(self.metrics_data, f, indent=2)
    
    def track_response_generation(
        self,
        response_type: str,
        confidence: float = 0.0,
        processing_time: float = 0.0,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Track a response generation event with comprehensive metrics
        
        Args:
            response_type (str): Type of response generated
            confidence (float): Confidence score of the response (0.0-1.0)
            processing_time (float): Time taken to generate response in seconds
            context (dict, optional): Additional context information
            
        Returns:
            str: Unique metric ID for this event
        """
        metric_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        # Update overall metrics
        self.metrics_data['total_responses_generated'] += 1
        
        # Update response type metrics
        if response_type not in self.metrics_data['responses_by_type']:
            self.metrics_data['responses_by_type'][response_type] = {
                'count': 0,
                'total_confidence': 0,
                'total_processing_time': 0
            }
        
        type_metrics = self.metrics_data['responses_by_type'][response_type]
        type_metrics['count'] += 1
        type_metrics['total_confidence'] += confidence
        type_metrics['total_processing_time'] += processing_time
        
        # Update performance metrics
        perf_metrics = self.metrics_data['performance_metrics']
        perf_metrics['total_response_time'] += processing_time
        perf_metrics['response_count'] += 1
        perf_metrics['average_response_time'] = (
            perf_metrics['total_response_time'] / perf_metrics['response_count']
        )
        
        # Update quality metrics
        quality_metrics = self.metrics_data['quality_metrics']
        quality_metrics['total_confidence'] += confidence
        quality_metrics['confidence_count'] += 1
        quality_metrics['average_confidence'] = (
            quality_metrics['total_confidence'] / quality_metrics['confidence_count']
        )
        
        # Update daily metrics
        today = date.today().isoformat()
        if today not in self.metrics_data['daily_metrics']:
            self.metrics_data['daily_metrics'][today] = {
                'total_responses': 0,
                'responses_by_type': {},
                'total_processing_time': 0,
                'average_confidence': 0
            }
        
        daily_metrics = self.metrics_data['daily_metrics'][today]
        daily_metrics['total_responses'] += 1
        daily_metrics['total_processing_time'] += processing_time
        
        if response_type not in daily_metrics['responses_by_type']:
            daily_metrics['responses_by_type'][response_type] = 0
        daily_metrics['responses_by_type'][response_type] += 1
        
        # Save metrics
        self._save_metrics()
        
        return metric_id
    
    def track_error(
        self,
        error_type: str,
        error_message: str,
        response_type: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Track an error event
        
        Args:
            error_type (str): Type of error
            error_message (str): Error message
            response_type (str, optional): Response type if applicable
            context (dict, optional): Additional context
            
        Returns:
            str: Unique error ID
        """
        error_id = str(uuid.uuid4())
        
        # Update error metrics
        self.metrics_data['error_metrics']['total_errors'] += 1
        
        if error_type not in self.metrics_data['error_metrics']['errors_by_type']:
            self.metrics_data['error_metrics']['errors_by_type'][error_type] = 0
        self.metrics_data['error_metrics']['errors_by_type'][error_type] += 1
        
        # Save metrics
        self._save_metrics()
        
        return error_id
    
    def get_metrics_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive metrics report
        
        Returns:
            dict: Detailed metrics report
        """
        return {
            'overall_metrics': {
                'total_responses_generated': self.metrics_data['total_responses_generated'],
                'responses_by_type': self.metrics_data['responses_by_type'],
                'performance_metrics': self.metrics_data['performance_metrics'],
                'quality_metrics': self.metrics_data['quality_metrics'],
                'error_metrics': self.metrics_data['error_metrics']
            },
            'daily_metrics': self.metrics_data['daily_metrics'],
            'timestamp': datetime.now().isoformat()
        }
    
    def get_daily_report(self, date_str: Optional[str] = None) -> Dict[str, Any]:
        """
        Get metrics report for a specific day
        
        Args:
            date_str (str, optional): Date in ISO format (YYYY-MM-DD). 
                                    If None, uses current date.
        
        Returns:
            dict: Daily metrics report
        """
        if date_str is None:
            date_str = date.today().isoformat()
        
        daily_metrics = self.metrics_data['daily_metrics'].get(date_str, {})
        
        return {
            'date': date_str,
            'metrics': daily_metrics,
            'exists': date_str in self.metrics_data['daily_metrics']
        }
    
    def reset_metrics(self):
        """Reset all metrics data"""
        self.metrics_data = {
            'total_responses_generated': 0,
            'responses_by_type': {},
            'daily_metrics': {},
            'performance_metrics': {
                'average_response_time': 0,
                'total_response_time': 0,
                'response_count': 0
            },
            'quality_metrics': {
                'average_confidence': 0,
                'total_confidence': 0,
                'confidence_count': 0
            },
            'error_metrics': {
                'total_errors': 0,
                'errors_by_type': {}
            }
        }
        self._save_metrics()

# Global metrics tracker instance
metrics_tracker = MetricsTracker()