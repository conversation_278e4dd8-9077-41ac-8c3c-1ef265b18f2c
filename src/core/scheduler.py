import asyncio
import signal
import sys
import logging
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from src.api.data.cache import market_data_cache, warm_top_symbols_cache
from src.core.logger import get_logger

# Configure logging
logger = get_logger(__name__)

async def run_scheduler():
    """Run the data refresher scheduler."""
    scheduler = AsyncIOScheduler()
    
    # Add job to warm cache every 5 minutes
    scheduler.add_job(
        warm_top_symbols_cache,
        trigger=IntervalTrigger(minutes=5),
        args=(market_data_cache,),
        id='warm_cache_job',
        replace_existing=True
    )

    # Add job to scan trade opportunities every 15 minutes
    from src.core.trade_scanner import update_trade_opportunities
    scheduler.add_job(
        update_trade_opportunities,
        trigger=IntervalTrigger(minutes=15),
        id='scan_opportunities_job',
        replace_existing=True
    )
    
    # Initialize watchlist alerts if database is available
    try:
        from src.bot.database_manager import db_manager
        from src.bot.watchlist_manager import WatchlistManager
        from src.bot.watchlist_alerts import initialize_watchlist_alerts, register_with_scheduler
        
        # Initialize database connection
        db_success = await db_manager.initialize()
        
        if db_success:
            logger.info("Database connection established for watchlist alerts")
            
            # Initialize watchlist manager
            watchlist_manager = WatchlistManager(db_manager.get_pool())
            
            # Initialize watchlist alerts
            await initialize_watchlist_alerts(watchlist_manager)
            
            # Register watchlist alerts with scheduler
            register_with_scheduler(scheduler)
            
            logger.info("Watchlist alerts registered with scheduler")
        else:
            logger.error("Failed to initialize database connection for watchlist alerts")
    except Exception as e:
        logger.error(f"Error initializing watchlist alerts: {e}")
    
    scheduler.start()
    logger.info("Data refresher scheduler started. Warming cache every 5 minutes.")
    
    # Keep the scheduler running
    try:
        while True:
            await asyncio.sleep(1)
    except (KeyboardInterrupt, SystemExit):
        logger.info("Shutting down scheduler...")
        scheduler.shutdown()
        sys.exit(0)

if __name__ == "__main__":
    asyncio.run(run_scheduler())