"""
Bot Health Monitoring Configuration
Handles the integration between bot and monitoring system
"""

from typing import Optional
from src.bot.client import TradingBot
from src.api.routes.bot_health import bot_health_monitor

class BotMonitorConfig:
    """Configuration for bot health monitoring"""
    
    @staticmethod
    def initialize_bot_monitoring(bot_instance: TradingBot):
        """
        Initialize bot health monitoring
        
        Args:
            bot_instance: The trading bot instance to monitor
        """
        # Set the bot instance for health monitoring
        bot_health_monitor.set_bot_instance(bot_instance)
        
        # Log initialization
        bot_instance.logger.info("Bot health monitoring initialized")
    
    @staticmethod
    async def get_health_summary():
        """
        Get a quick health summary for the bot
        
        Returns:
            Dict with essential health information
        """
        from src.api.routes.bot_health import detailed_health_check
        
        try:
            health_data = await detailed_health_check()
            return {
                "status": health_data["overall_status"],
                "bot_connected": health_data["details"]["bot"]["bot"]["connected"],
                "uptime": health_data["details"]["bot"]["uptime"]["process"],
                "timestamp": health_data["timestamp"]
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": str(datetime.utcnow())
            }

# Global monitoring instance
bot_monitor = BotMonitorConfig()