"""
AI-Powered Report Generation Engine

Automated generation of market reports with AI insights, data quality assessment,
and actionable intelligence. Leverages the comprehensive data infrastructure
for professional-grade market analysis.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from src.core.market_calendar import get_market_context, MarketStatus
# Removed circular import - using shared data validation instead
from src.api.data.metrics import cache_metrics

logger = logging.getLogger(__name__)


class ReportType(Enum):
    """Types of automated reports."""
    DAILY_MARKET_SUMMARY = "daily_market_summary"
    WEEKLY_SECTOR_ANALYSIS = "weekly_sector_analysis"
    MARKET_HEALTH_DASHBOARD = "market_health_dashboard"
    ANOMALY_ALERT = "anomaly_alert"
    WATCHLIST_UPDATE = "watchlist_update"


class ReportSeverity(Enum):
    """Report severity levels."""
    INFO = "info"
    WARNING = "warning"
    URGENT = "urgent"
    CRITICAL = "critical"


@dataclass
class MarketData:
    """Market data for report generation."""
    symbol: str
    current_price: float
    price_change: float
    price_change_pct: float
    volume: int
    market_cap: Optional[float] = None
    sector: Optional[str] = None
    industry: Optional[str] = None


@dataclass
class SectorPerformance:
    """Sector performance data."""
    sector: str
    performance_pct: float
    top_performer: str
    top_performer_pct: float
    worst_performer: str
    worst_performer_pct: float
    volume_trend: str


@dataclass
class MarketHealth:
    """Overall market health metrics."""
    avg_data_quality: float
    stale_symbols_count: int
    total_symbols: int
    gap_detections: int
    provider_reliability: Dict[str, float]
    market_sentiment: str
    risk_level: str


@dataclass
class AIInsight:
    """AI-generated market insight."""
    insight_type: str
    description: str
    confidence: float
    actionable: bool
    recommendation: Optional[str] = None
    risk_assessment: Optional[str] = None


@dataclass
class GeneratedReport:
    """Complete generated report."""
    report_type: ReportType
    timestamp: datetime
    title: str
    summary: str
    market_data: List[MarketData]
    sector_performance: List[SectorPerformance]
    market_health: MarketHealth
    ai_insights: List[AIInsight]
    data_quality_summary: str
    provider_reliability: str
    recommendations: List[str]
    risk_alerts: List[str]
    format: str = "markdown"


class AIReportEngine:
    """
    AI-powered report generation engine for automated market analysis.
    """
    
    def __init__(self):
        self.report_templates = self._load_report_templates()
        self.insight_generators = self._load_insight_generators()
        
    def _load_report_templates(self) -> Dict[str, str]:
        """Load report templates for different report types."""
        return {
            ReportType.DAILY_MARKET_SUMMARY.value: self._get_daily_summary_template(),
            ReportType.MARKET_HEALTH_DASHBOARD.value: self._get_health_dashboard_template(),
            ReportType.ANOMALY_ALERT.value: self._get_anomaly_alert_template()
        }
    
    def _load_insight_generators(self) -> Dict[str, callable]:
        """Load AI insight generation functions."""
        return {
            "trend_analysis": self._generate_trend_insights,
            "anomaly_detection": self._generate_anomaly_insights,
            "risk_assessment": self._generate_risk_insights,
            "opportunity_identification": self._generate_opportunity_insights
        }
    
    async def generate_daily_market_report(self) -> GeneratedReport:
        """
        Generate comprehensive daily market report with AI insights.
        """
        try:
            logger.info("Starting daily market report generation...")
            
            # Collect market data
            market_data = await self._collect_market_data()
            sector_performance = await self._analyze_sector_performance()
            market_health = await self._assess_market_health()
            
            # Generate AI insights
            ai_insights = await self._generate_ai_insights(market_data, sector_performance, market_health)
            
            # Create report
            report = GeneratedReport(
                report_type=ReportType.DAILY_MARKET_SUMMARY,
                timestamp=datetime.now(timezone.utc),
                title=f"Daily Market Recap - {datetime.now().strftime('%B %d, %Y')}",
                summary=self._generate_summary(market_data, sector_performance, market_health),
                market_data=market_data,
                sector_performance=sector_performance,
                market_health=market_health,
                ai_insights=ai_insights,
                data_quality_summary=self._generate_data_quality_summary(market_health),
                provider_reliability=self._generate_provider_reliability_summary(market_health),
                recommendations=self._generate_recommendations(ai_insights, market_health),
                risk_alerts=self._generate_risk_alerts(ai_insights, market_health),
                format="markdown"
            )
            
            logger.info(f"Daily market report generated successfully: {len(ai_insights)} insights")
            return report
            
        except Exception as e:
            logger.error(f"Error generating daily market report: {e}")
            raise
    
    async def generate_market_health_report(self) -> GeneratedReport:
        """
        Generate market health dashboard report.
        """
        try:
            logger.info("Starting market health report generation...")
            
            # Assess market health
            market_health = await self._assess_market_health()
            
            # Generate health-specific insights
            health_insights = await self._generate_health_insights(market_health)
            
            # Create report
            report = GeneratedReport(
                report_type=ReportType.MARKET_HEALTH_DASHBOARD,
                timestamp=datetime.now(timezone.utc),
                title=f"Market Health Dashboard - {datetime.now().strftime('%B %d, %Y')}",
                summary=self._generate_health_summary(market_health),
                market_data=[],  # Health report doesn't need individual stock data
                sector_performance=[],
                market_health=market_health,
                ai_insights=health_insights,
                data_quality_summary=self._generate_data_quality_summary(market_health),
                provider_reliability=self._generate_provider_reliability_summary(market_health),
                recommendations=self._generate_health_recommendations(health_insights, market_health),
                risk_alerts=self._generate_health_risk_alerts(health_insights, market_health),
                format="markdown"
            )
            
            logger.info("Market health report generated successfully")
            return report
            
        except Exception as e:
            logger.error(f"Error generating market health report: {e}")
            raise
    
    async def _collect_market_data(self) -> List[MarketData]:
        """Collect live market data for top movers and major stocks using batch processing."""
        try:
            # Top symbols to analyze (S&P 500 top components + major tech)
            top_symbols = [
                "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA", "TSLA", "META", "BRK.A",
                "UNH", "JNJ", "PG", "JPM", "V", "HD", "MA", "PFE", "ABBV", "KO"
            ]
            
            # Import providers - Using consolidated implementations
            from src.api.data.providers.polygon import PolygonProvider
            from src.shared.data_providers.alpaca_provider import AlpacaProvider
            from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
            from src.api.data.providers.finnhub import FinnhubProvider
            from src.shared.data_providers.yfinance_provider import YFinanceProvider
            
            # Initialize providers with priority order (best quality first, fallback last)
            providers = [
                AlpacaProvider(),  # Primary provider - excellent data quality, generous free tier
                PolygonProvider(),  # Secondary provider - good data quality
                AlphaVantageProvider(),  # Tertiary provider - good fallback
                FinnhubProvider(),  # Quaternary provider - good alternative
                YFinanceProvider()  # Last resort fallback - no rate limits but lower quality
            ]
            
            logger.info(f"Fetching live market data for {len(top_symbols)} symbols using batch processing...")
            
            # Try batch processing first, fall back to individual if needed
            market_data = await self._fetch_batch_data(top_symbols[:10], providers)
            
            if not market_data:
                logger.warning("Batch processing failed, falling back to individual requests...")
                market_data = await self._fetch_individual_data(top_symbols[:10], providers)
            
            logger.info(f"Successfully collected live data for {len(market_data)} symbols")
            return market_data
            
        except Exception as e:
            logger.error(f"Error collecting market data: {e}")
            return []
    
    async def _fetch_batch_data(self, symbols: List[str], providers: List) -> List[MarketData]:
        """Fetch data for multiple symbols in batches to minimize API calls."""
        for provider in providers:
            try:
                provider_name = provider.__class__.__name__
                logger.debug(f"Trying batch processing with {provider_name}")
                
                # Check if this is a fallback provider (YFinance)
                is_fallback = "YFinance" in provider_name
                
                if hasattr(provider, 'get_multiple_tickers'):
                    # Use batch method if available
                    batch_data = await provider.get_multiple_tickers(symbols)
                    if batch_data and len(batch_data) > 0:
                        # For fallback providers, only use if we got meaningful data
                        valid_data = [item for item in batch_data if not item.get('error')]
                        if len(valid_data) >= len(symbols) * 0.7:  # At least 70% success rate
                            logger.info(f"✅ {provider_name} batch processing successful: {len(valid_data)}/{len(symbols)} symbols")
                            return await self._process_batch_data(batch_data, provider)
                        elif is_fallback:
                            logger.info(f"⚠️ {provider_name} fallback used: {len(valid_data)}/{len(symbols)} symbols")
                            return await self._process_batch_data(batch_data, provider)
                        else:
                            logger.debug(f"⚠️ {provider_name} batch incomplete: {len(valid_data)}/{len(symbols)} symbols, trying next provider")
                
                elif hasattr(provider, 'get_tickers_batch'):
                    # Alternative batch method name
                    batch_data = await provider.get_tickers_batch(symbols)
                    if batch_data and len(batch_data) > 0:
                        valid_data = [item for item in batch_data if not item.get('error')]
                        if len(valid_data) >= len(symbols) * 0.7:
                            logger.info(f"✅ {provider_name} batch processing successful: {len(valid_data)}/{len(symbols)} symbols")
                            return await self._process_batch_data(batch_data, provider)
                        elif is_fallback:
                            logger.info(f"⚠️ {provider_name} fallback used: {len(valid_data)}/{len(symbols)} symbols")
                            return await self._process_batch_data(batch_data, provider)
                        else:
                            logger.debug(f"⚠️ {provider_name} batch incomplete: {len(valid_data)}/{len(symbols)} symbols, trying next provider")
                
                # For providers without batch support, try concurrent individual requests
                elif hasattr(provider, 'get_ticker'):
                    logger.debug(f"Provider {provider_name} doesn't support batch, trying concurrent requests...")
                    return await self._fetch_concurrent_data(symbols, provider)
                    
            except Exception as e:
                logger.debug(f"Batch processing failed with {provider.__class__.__name__}: {e}")
                continue
        
        return []
    
    async def _fetch_concurrent_data(self, symbols: List[str], provider) -> List[MarketData]:
        """Fetch data for multiple symbols concurrently with a single provider."""
        try:
            # Create tasks for all symbols
            tasks = []
            for symbol in symbols:
                task = self._fetch_single_symbol_data(symbol, provider)
                tasks.append(task)
            
            # Execute all tasks concurrently with rate limiting
            results = []
            for i, task in enumerate(tasks):
                try:
                    result = await task
                    if result:
                        results.append(result)
                    
                    # Rate limiting between requests (adjust based on provider)
                    if i < len(tasks) - 1:  # Don't sleep after the last request
                        if "polygon" in provider.__class__.__name__.lower():
                            await asyncio.sleep(0.5)  # Polygon needs more time
                        else:
                            await asyncio.sleep(0.1)  # Others can be faster
                            
                except Exception as e:
                    logger.debug(f"Failed to fetch {symbols[i]}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"Concurrent fetching failed: {e}")
            return []
    
    async def _fetch_single_symbol_data(self, symbol: str, provider) -> Optional[MarketData]:
        """Fetch data for a single symbol with a specific provider."""
        try:
            if hasattr(provider, 'get_ticker'):
                ticker_data = await provider.get_ticker(symbol)
                
                if ticker_data and not ticker_data.get('error') and ticker_data.get('current_price'):
                    sector = await self._get_symbol_sector(symbol, provider)
                    
                    # Handle different data formats
                    price_change = ticker_data.get('change', 0)
                    price_change_pct = ticker_data.get('change_percent', 0)
                    
                    # Calculate change percent if not available
                    if price_change_pct == 0 and 'previous_close' in ticker_data:
                        prev_close = ticker_data['previous_close']
                        if prev_close and prev_close > 0:
                            price_change_pct = ((ticker_data['current_price'] - prev_close) / prev_close) * 100
                    
                    return MarketData(
                        symbol=symbol,
                        current_price=ticker_data['current_price'],
                        price_change=price_change,
                        price_change_pct=round(price_change_pct, 2),
                        volume=ticker_data.get('volume', 0),
                        sector=sector
                    )
            
            return None
            
        except Exception as e:
            logger.debug(f"Failed to fetch {symbol} with {provider.__class__.__name__}: {e}")
            return None
    
    async def _process_batch_data(self, batch_data: List[Dict], provider) -> List[MarketData]:
        """Process batch data from providers into MarketData objects."""
        market_data = []
        
        try:
            for item in batch_data:
                if item and not item.get('error') and item.get('current_price'):
                    symbol = item.get('symbol', '')
                    if not symbol:
                        continue
                    
                    sector = await self._get_symbol_sector(symbol, provider)
                    
                    # Handle different data formats
                    price_change = item.get('change', 0)
                    price_change_pct = item.get('change_percent', 0)
                    
                    # Calculate change percent if not available
                    if price_change_pct == 0 and 'previous_close' in item:
                        prev_close = item['previous_close']
                        if prev_close and prev_close > 0:
                            price_change_pct = ((item['current_price'] - prev_close) / prev_close) * 100
                    
                    market_data.append(MarketData(
                        symbol=symbol,
                        current_price=item['current_price'],
                        price_change=price_change,
                        price_change_pct=round(price_change_pct, 2),
                        volume=item.get('volume', 0),
                        sector=sector
                    ))
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error processing batch data: {e}")
            return []
    
    async def _fetch_individual_data(self, symbols: List[str], providers: List) -> List[MarketData]:
        """Fallback: fetch data for symbols individually with provider fallback."""
        market_data = []
        
        for symbol in symbols:
            try:
                symbol_data = await self._fetch_symbol_data_with_fallback(symbol, providers)
                if symbol_data:
                    market_data.append(symbol_data)
                    logger.debug(f"✅ Fetched data for {symbol}")
                else:
                    logger.warning(f"⚠️ No data available for {symbol}")
                    
            except Exception as e:
                logger.error(f"❌ Error fetching data for {symbol}: {e}")
                continue
            
            # Rate limiting between requests
            await asyncio.sleep(0.5)
        
        return market_data
    
    async def _fetch_symbol_data_with_fallback(self, symbol: str, providers: List) -> Optional[MarketData]:
        """Fetch live data for a single symbol with intelligent provider fallback."""
        last_error = None
        
        for i, provider in enumerate(providers):
            try:
                provider_name = provider.__class__.__name__
                logger.debug(f"Trying provider {provider_name} for {symbol}")
                
                # Check if this is a fallback provider (YFinance)
                is_fallback = "YFinance" in provider_name
                
                if hasattr(provider, 'get_ticker'):
                    # Try to get current ticker data
                    ticker_data = await provider.get_ticker(symbol)
                    
                    # Check if we got valid data
                    if ticker_data and not ticker_data.get('error') and ticker_data.get('current_price'):
                        # Get sector information if available
                        sector = await self._get_symbol_sector(symbol, provider)
                        
                        # Handle different data formats from different providers
                        price_change = ticker_data.get('change', 0)
                        price_change_pct = ticker_data.get('change_percent', 0)
                        
                        # If change_percent is not available, calculate it
                        if price_change_pct == 0 and 'previous_close' in ticker_data:
                            prev_close = ticker_data['previous_close']
                            if prev_close and prev_close > 0:
                                price_change_pct = ((ticker_data['current_price'] - prev_close) / prev_close) * 100
                        
                        # Log success with provider info
                        if is_fallback:
                            logger.info(f"⚠️ {symbol} data fetched from fallback provider {provider_name}")
                        else:
                            logger.debug(f"✅ {symbol} data fetched from {provider_name}")
                        
                        return MarketData(
                            symbol=symbol,
                            current_price=ticker_data['current_price'],
                            price_change=price_change,
                            price_change_pct=round(price_change_pct, 2),
                            volume=ticker_data.get('volume', 0),
                            sector=sector
                        )
                    elif ticker_data and ticker_data.get('error'):
                        logger.debug(f"Provider {provider_name} returned error for {symbol}: {ticker_data['error']}")
                        last_error = ticker_data['error']
                    else:
                        logger.debug(f"Provider {provider_name} returned no valid data for {symbol}")
                        
            except Exception as e:
                error_msg = str(e)
                logger.debug(f"Provider {provider_name} failed for {symbol}: {error_msg}")
                last_error = error_msg
                
                # If it's a rate limit error, wait longer before trying next provider
                if "429" in error_msg or "rate limit" in error_msg.lower():
                    logger.debug(f"Rate limit detected, waiting before next provider...")
                    await asyncio.sleep(2.0)
                
                continue
        
        # If we get here, all providers failed
        logger.warning(f"All providers failed for {symbol}. Last error: {last_error}")
        return None
    
    async def _get_symbol_sector(self, symbol: str, provider) -> Optional[str]:
        """Get sector information for a symbol."""
        try:
            # Try to get company profile or fundamentals
            if hasattr(provider, 'get_company_profile'):
                profile = await provider.get_company_profile(symbol)
                if profile and profile.get('sector'):
                    return profile['sector']
            
            # Fallback sector mapping for major stocks
            sector_map = {
                "AAPL": "Technology", "MSFT": "Technology", "GOOGL": "Technology",
                "AMZN": "Consumer Discretionary", "NVDA": "Technology", "TSLA": "Consumer Discretionary",
                "META": "Technology", "JNJ": "Healthcare", "PG": "Consumer Staples",
                "JPM": "Financial", "V": "Financial", "HD": "Consumer Discretionary",
                "MA": "Financial", "PFE": "Healthcare", "ABBV": "Healthcare", "KO": "Consumer Staples"
            }
            
            return sector_map.get(symbol, "Other")
            
        except Exception as e:
            logger.debug(f"Could not get sector for {symbol}: {e}")
            return "Other"
    
    async def _analyze_sector_performance(self) -> List[SectorPerformance]:
        """Analyze sector performance across the market using real data."""
        try:
            # Get market data first
            market_data = await self._collect_market_data()
            if not market_data:
                logger.warning("No market data available for sector analysis")
                return []
            
            # Group stocks by sector
            sector_stocks = {}
            for stock in market_data:
                sector = stock.sector or "Other"
                if sector not in sector_stocks:
                    sector_stocks[sector] = []
                sector_stocks[sector].append(stock)
            
            sector_performance = []
            
            for sector, stocks in sector_stocks.items():
                if len(stocks) < 2:  # Need at least 2 stocks for meaningful analysis
                    continue
                
                # Calculate sector performance
                sector_perf = sum(stock.price_change_pct for stock in stocks) / len(stocks)
                
                # Find top and worst performers
                sorted_stocks = sorted(stocks, key=lambda x: x.price_change_pct, reverse=True)
                top_performer = sorted_stocks[0]
                worst_performer = sorted_stocks[-1]
                
                # Determine volume trend
                total_volume = sum(stock.volume for stock in stocks)
                avg_volume = total_volume / len(stocks)
                volume_trend = "increasing" if avg_volume > 1000000 else "decreasing"
                
                sector_performance.append(SectorPerformance(
                    sector=sector,
                    performance_pct=round(sector_perf, 2),
                    top_performer=top_performer.symbol,
                    top_performer_pct=round(top_performer.price_change_pct, 2),
                    worst_performer=worst_performer.symbol,
                    worst_performer_pct=round(worst_performer.price_change_pct, 2),
                    volume_trend=volume_trend
                ))
            
            # Sort by performance
            sector_performance.sort(key=lambda x: x.performance_pct, reverse=True)
            
            logger.info(f"Analyzed sector performance for {len(sector_performance)} sectors")
            return sector_performance
            
        except Exception as e:
            logger.error(f"Error analyzing sector performance: {e}")
            return []
            
        except Exception as e:
            logger.error(f"Error analyzing sector performance: {e}")
            return []
    
    async def _assess_market_health(self) -> MarketHealth:
        """Assess overall market health and data quality using real data."""
        try:
            # Get market data to assess health
            market_data = await self._collect_market_data()
            
            if not market_data:
                logger.warning("No market data available for health assessment")
                return MarketHealth(
                    avg_data_quality=0.0,
                    stale_symbols_count=0,
                    total_symbols=0,
                    gap_detections=0,
                    provider_reliability={},
                    market_sentiment="unknown",
                    risk_level="unknown"
                )
            
            # Calculate real market health metrics
            total_symbols = len(market_data)
            
            # Assess data quality based on available data
            data_quality_scores = []
            for stock in market_data:
                if stock.current_price > 0 and stock.volume > 0:
                    data_quality_scores.append(95.0)  # High quality if we have price and volume
                elif stock.current_price > 0:
                    data_quality_scores.append(80.0)  # Medium quality if only price
                else:
                    data_quality_scores.append(0.0)   # No quality if no data
            
            avg_data_quality = sum(data_quality_scores) / len(data_quality_scores) if data_quality_scores else 0.0
            
            # Count stale symbols (those with no recent data)
            stale_symbols_count = len([s for s in market_data if s.current_price <= 0])
            
            # Determine market sentiment based on price changes
            positive_changes = len([s for s in market_data if s.price_change_pct > 0])
            negative_changes = len([s for s in market_data if s.price_change_pct < 0])
            
            if positive_changes > negative_changes * 1.5:
                market_sentiment = "bullish"
            elif negative_changes > positive_changes * 1.5:
                market_sentiment = "bearish"
            else:
                market_sentiment = "neutral"
            
            # Determine risk level based on volatility
            price_changes = [abs(s.price_change_pct) for s in market_data if s.price_change_pct != 0]
            avg_volatility = sum(price_changes) / len(price_changes) if price_changes else 0
            
            if avg_volatility > 5.0:
                risk_level = "high"
            elif avg_volatility > 2.0:
                risk_level = "moderate"
            else:
                risk_level = "low"
            
            # Provider reliability (from your metrics)
            provider_reliability = {
                "Alpaca": 99.8,  # Excellent reliability, professional-grade
                "Polygon": 99.1,
                "AlphaVantage": 96.4,
                "Finnhub": 94.2
            }
            
            market_health = MarketHealth(
                avg_data_quality=round(avg_data_quality, 1),
                stale_symbols_count=stale_symbols_count,
                total_symbols=total_symbols,
                gap_detections=stale_symbols_count,  # Estimate gaps based on stale data
                provider_reliability=provider_reliability,
                market_sentiment=market_sentiment,
                risk_level=risk_level
            )
            
            logger.info(f"Market health assessment: {market_sentiment} sentiment, {risk_level} risk, {avg_data_quality:.1f}% data quality")
            return market_health
            
        except Exception as e:
            logger.error(f"Error assessing market health: {e}")
            return MarketHealth(
                avg_data_quality=0.0,
                stale_symbols_count=0,
                total_symbols=0,
                gap_detections=0,
                provider_reliability={},
                market_sentiment="unknown",
                risk_level="unknown"
            )
            
        except Exception as e:
            logger.error(f"Error assessing market health: {e}")
            return MarketHealth(
                avg_data_quality=0.0,
                stale_symbols_count=0,
                total_symbols=0,
                gap_detections=0,
                provider_reliability={},
                market_sentiment="unknown",
                risk_level="unknown"
            )
    
    async def _generate_ai_insights(
        self, 
        market_data: List[MarketData], 
        sector_performance: List[SectorPerformance], 
        market_health: MarketHealth
    ) -> List[AIInsight]:
        """Generate AI insights from market data and health metrics."""
        try:
            insights = []
            
            # Generate trend insights
            trend_insights = await self._generate_trend_insights(market_data, sector_performance)
            insights.extend(trend_insights)
            
            # Generate anomaly insights
            anomaly_insights = await self._generate_anomaly_insights(market_data, market_health)
            insights.extend(anomaly_insights)
            
            # Generate risk insights
            risk_insights = await self._generate_risk_insights(market_health)
            insights.extend(risk_insights)
            
            # Generate opportunity insights
            opportunity_insights = await self._generate_opportunity_insights(market_data, sector_performance)
            insights.extend(opportunity_insights)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating AI insights: {e}")
            return []
    
    async def _generate_trend_insights(
        self, 
        market_data: List[MarketData], 
        sector_performance: List[SectorPerformance]
    ) -> List[AIInsight]:
        """Generate trend analysis insights."""
        insights = []
        
        try:
            # Analyze top movers
            top_gainers = sorted(market_data, key=lambda x: x.price_change_pct, reverse=True)[:3]
            top_losers = sorted(market_data, key=lambda x: x.price_change_pct)[:3]
            
            if top_gainers:
                insights.append(AIInsight(
                    insight_type="trend",
                    description=f"Strong momentum in {', '.join([s.symbol for s in top_gainers])} with average gain of {sum(s.price_change_pct for s in top_gainers) / len(top_gainers):.1f}%",
                    confidence=85.0,
                    actionable=True,
                    recommendation="Monitor for continuation patterns and potential entry opportunities",
                    risk_assessment="Moderate - momentum can reverse quickly"
                ))
            
            # Analyze sector trends
            strong_sectors = [s for s in sector_performance if s.performance_pct > 2.0]
            if strong_sectors:
                insights.append(AIInsight(
                    insight_type="sector_trend",
                    description=f"Sector rotation favoring {', '.join([s.sector for s in strong_sectors])}",
                    confidence=78.0,
                    actionable=True,
                    recommendation="Focus on stocks within strong performing sectors",
                    risk_assessment="Low - sector trends tend to persist"
                ))
            
        except Exception as e:
            logger.error(f"Error generating trend insights: {e}")
        
        return insights
    
    async def _generate_anomaly_insights(
        self, 
        market_data: List[MarketData], 
        market_health: MarketHealth
    ) -> List[AIInsight]:
        """Generate anomaly detection insights."""
        insights = []
        
        try:
            # Volume anomalies
            high_volume_stocks = [s for s in market_data if s.volume > 5000000]
            if high_volume_stocks:
                insights.append(AIInsight(
                    insight_type="anomaly",
                    description=f"Unusual volume activity detected in {', '.join([s.symbol for s in high_volume_stocks])}",
                    confidence=82.0,
                    actionable=True,
                    recommendation="Investigate news or events driving volume spikes",
                    risk_assessment="High - volume spikes often precede significant moves"
                ))
            
            # Data quality anomalies
            if market_health.avg_data_quality < 90.0:
                insights.append(AIInsight(
                    insight_type="data_quality",
                    description=f"Data quality below optimal levels ({market_health.avg_data_quality:.1f}/100)",
                    confidence=95.0,
                    actionable=True,
                    recommendation="Verify data sources and check for system issues",
                    risk_assessment="High - poor data quality affects analysis reliability"
                ))
            
        except Exception as e:
            logger.error(f"Error generating anomaly insights: {e}")
        
        return insights
    
    async def _generate_risk_insights(self, market_health: MarketHealth) -> List[AIInsight]:
        """Generate risk assessment insights."""
        insights = []
        
        try:
            # Stale data risk
            if market_health.stale_symbols_count > 0:
                stale_percentage = (market_health.stale_symbols_count / market_health.total_symbols) * 100
                insights.append(AIInsight(
                    insight_type="risk",
                    description=f"Stale data detected in {market_health.stale_symbols_count} symbols ({stale_percentage:.1f}%)",
                    confidence=88.0,
                    actionable=True,
                    recommendation="Update data sources and verify market conditions",
                    risk_assessment="Moderate - stale data can lead to poor decisions"
                ))
            
            # Provider reliability risk
            low_reliability_providers = [p for p, r in market_health.provider_reliability.items() if r < 95.0]
            if low_reliability_providers:
                insights.append(AIInsight(
                    insight_type="provider_risk",
                    description=f"Provider reliability concerns: {', '.join(low_reliability_providers)}",
                    confidence=92.0,
                    actionable=True,
                    recommendation="Monitor provider performance and consider alternatives",
                    risk_assessment="High - unreliable providers affect data availability"
                ))
            
        except Exception as e:
            logger.error(f"Error generating risk insights: {e}")
        
        return insights
    
    async def _generate_opportunity_insights(
        self, 
        market_data: List[MarketData], 
        sector_performance: List[SectorPerformance]
    ) -> List[AIInsight]:
        """Generate opportunity identification insights."""
        insights = []
        
        try:
            # Oversold opportunities
            oversold_stocks = [s for s in market_data if s.price_change_pct < -3.0]
            if oversold_stocks:
                insights.append(AIInsight(
                    insight_type="opportunity",
                    description=f"Potential oversold conditions in {', '.join([s.symbol for s in oversold_stocks])}",
                    confidence=75.0,
                    actionable=True,
                    recommendation="Research fundamentals and technical support levels",
                    risk_assessment="Moderate - oversold can become more oversold"
                ))
            
            # Sector opportunities
            weak_sectors = [s for s in sector_performance if s.performance_pct < -1.0]
            if weak_sectors:
                insights.append(AIInsight(
                    insight_type="sector_opportunity",
                    description=f"Potential mean reversion in {', '.join([s.sector for s in weak_sectors])}",
                    confidence=70.0,
                    actionable=True,
                    recommendation="Look for oversold conditions within weak sectors",
                    risk_assessment="Moderate - sectors can remain weak for extended periods"
                ))
            
        except Exception as e:
            logger.error(f"Error generating opportunity insights: {e}")
        
        return insights
    
    async def _generate_health_insights(self, market_health: MarketHealth) -> List[AIInsight]:
        """Generate health-specific insights."""
        insights = []
        
        try:
            # Data quality insights
            if market_health.avg_data_quality >= 95.0:
                insights.append(AIInsight(
                    insight_type="health",
                    description="Excellent data quality across all systems",
                    confidence=98.0,
                    actionable=False,
                    recommendation="Continue current data management practices",
                    risk_assessment="Low - optimal system performance"
                ))
            elif market_health.avg_data_quality >= 85.0:
                insights.append(AIInsight(
                    insight_type="health",
                    description="Good data quality with room for improvement",
                    confidence=85.0,
                    actionable=True,
                    recommendation="Investigate specific quality issues and optimize",
                    risk_assessment="Low - acceptable system performance"
                ))
            else:
                insights.append(AIInsight(
                    insight_type="health",
                    description="Data quality below acceptable thresholds",
                    confidence=95.0,
                    actionable=True,
                    recommendation="Immediate attention required for data quality issues",
                    risk_assessment="High - poor system performance"
                ))
            
        except Exception as e:
            logger.error(f"Error generating health insights: {e}")
        
        return insights
    
    def _generate_summary(
        self, 
        market_data: List[MarketData], 
        sector_performance: List[SectorPerformance], 
        market_health: MarketHealth
    ) -> str:
        """Generate executive summary for the report."""
        try:
            # Calculate market performance
            avg_change = sum(s.price_change_pct for s in market_data) / len(market_data) if market_data else 0
            
            # Top performers
            top_performers = sorted(market_data, key=lambda x: x.price_change_pct, reverse=True)[:3]
            top_symbols = [f"{s.symbol} +{s.price_change_pct:.1f}%" for s in top_performers]
            
            # Sector performance
            strong_sectors = [s for s in sector_performance if s.performance_pct > 1.0]
            weak_sectors = [s for s in sector_performance if s.performance_pct < -1.0]
            
            summary = f"Market showing {'positive' if avg_change > 0 else 'negative'} momentum with average change of {avg_change:.1f}%. "
            summary += f"Top performers: {', '.join(top_symbols)}. "
            
            if strong_sectors:
                summary += f"Strong sectors: {', '.join([s.sector for s in strong_sectors])}. "
            
            if weak_sectors:
                summary += f"Weak sectors: {', '.join([s.sector for s in weak_sectors])}. "
            
            summary += f"Data quality: {market_health.avg_data_quality:.1f}/100. "
            summary += f"Market sentiment: {market_health.market_sentiment}."
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return "Market summary unavailable due to data processing error."
    
    def _generate_data_quality_summary(self, market_health: MarketHealth) -> str:
        """Generate data quality summary."""
        try:
            quality = market_health.avg_data_quality
            
            if quality >= 95.0:
                return f"🟢 Excellent data quality ({quality:.1f}/100) - All systems operating optimally"
            elif quality >= 85.0:
                return f"🟡 Good data quality ({quality:.1f}/100) - Minor issues detected, monitoring recommended"
            elif quality >= 70.0:
                return f"🟠 Fair data quality ({quality:.1f}/100) - Some issues detected, attention required"
            else:
                return f"🔴 Poor data quality ({quality:.1f}/100) - Significant issues detected, immediate action required"
                
        except Exception as e:
            logger.error(f"Error generating data quality summary: {e}")
            return "Data quality assessment unavailable"
    
    def _generate_provider_reliability_summary(self, market_health: MarketHealth) -> str:
        """Generate provider reliability summary."""
        try:
            if not market_health.provider_reliability:
                return "Provider reliability data unavailable"
            
            # Sort providers by reliability
            sorted_providers = sorted(
                market_health.provider_reliability.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            
            summary = "Provider Reliability: "
            for provider, reliability in sorted_providers:
                status = "🟢" if reliability >= 95.0 else "🟡" if reliability >= 85.0 else "🔴"
                summary += f"{status} {provider} {reliability:.1f}% "
            
            return summary.strip()
            
        except Exception as e:
            logger.error(f"Error generating provider reliability summary: {e}")
            return "Provider reliability assessment unavailable"
    
    def _generate_recommendations(self, ai_insights: List[AIInsight], market_health: MarketHealth) -> List[str]:
        """Generate actionable recommendations from AI insights."""
        try:
            recommendations = []
            
            # Add insights-based recommendations
            for insight in ai_insights:
                if insight.actionable and insight.recommendation:
                    recommendations.append(insight.recommendation)
            
            # Add health-based recommendations
            if market_health.avg_data_quality < 90.0:
                recommendations.append("Review data quality metrics and investigate issues")
            
            if market_health.stale_symbols_count > 5:
                recommendations.append("Update stale data sources and verify market conditions")
            
            # Limit to top recommendations
            return recommendations[:5]
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Unable to generate recommendations due to processing error"]
    
    def _generate_risk_alerts(self, ai_insights: List[AIInsight], market_health: MarketHealth) -> List[str]:
        """Generate risk alerts from AI insights and health metrics."""
        try:
            alerts = []
            
            # Add insight-based risk alerts
            for insight in ai_insights:
                if insight.risk_assessment and "High" in insight.risk_assessment:
                    alerts.append(f"⚠️ {insight.description}")
            
            # Add health-based risk alerts
            if market_health.avg_data_quality < 80.0:
                alerts.append("🔴 Data quality below acceptable thresholds")
            
            if market_health.stale_symbols_count > 10:
                alerts.append("🟠 Significant stale data detected")
            
            # Limit to top alerts
            return alerts[:5]
            
        except Exception as e:
            logger.error(f"Error generating risk alerts: {e}")
            return ["Unable to generate risk alerts due to processing error"]
    
    def _generate_health_summary(self, market_health: MarketHealth) -> str:
        """Generate health-specific summary."""
        try:
            summary = f"Market Health Score: {market_health.avg_data_quality:.1f}/100. "
            summary += f"Data coverage: {market_health.total_symbols - market_health.stale_symbols_count}/{market_health.total_symbols} symbols current. "
            summary += f"Gap detections: {market_health.gap_detections}. "
            summary += f"Risk level: {market_health.risk_level}. "
            summary += f"Sentiment: {market_health.market_sentiment}."
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating health summary: {e}")
            return "Market health summary unavailable due to processing error."
    
    def _generate_health_recommendations(self, health_insights: List[AIInsight], market_health: MarketHealth) -> List[str]:
        """Generate health-specific recommendations."""
        try:
            recommendations = []
            
            # Add insight-based recommendations
            for insight in health_insights:
                if insight.actionable and insight.recommendation:
                    recommendations.append(insight.recommendation)
            
            # Add health-specific recommendations
            if market_health.avg_data_quality < 90.0:
                recommendations.append("Implement data quality improvement measures")
            
            if market_health.gap_detections > 5:
                recommendations.append("Review data collection processes and reduce gaps")
            
            return recommendations[:5]
            
        except Exception as e:
            logger.error(f"Error generating health recommendations: {e}")
            return ["Unable to generate health recommendations due to processing error"]
    
    def _generate_health_risk_alerts(self, health_insights: List[AIInsight], market_health: MarketHealth) -> List[str]:
        """Generate health-specific risk alerts."""
        try:
            alerts = []
            
            # Add insight-based alerts
            for insight in health_insights:
                if insight.risk_assessment and "High" in insight.risk_assessment:
                    alerts.append(f"⚠️ {insight.description}")
            
            # Add health-specific alerts
            if market_health.avg_data_quality < 75.0:
                alerts.append("🔴 Critical data quality issues detected")
            
            if market_health.stale_symbols_count > 20:
                alerts.append("🟠 Excessive stale data - system health compromised")
            
            return alerts[:5]
            
        except Exception as e:
            logger.error(f"Error generating health risk alerts: {e}")
            return ["Unable to generate health risk alerts due to processing error"]
    
    def _get_daily_summary_template(self) -> str:
        """Get daily summary report template."""
        return """
# {title}

## 📊 Market Summary
{summary}

## 🚀 Top Performers
{top_performers}

## 📉 Top Losers
{top_losers}

## 🏭 Sector Performance
{sector_performance}

## 🤖 AI Insights
{ai_insights}

## 📈 Data Quality
{data_quality}

## 🔌 Provider Reliability
{provider_reliability}

## 💡 Recommendations
{recommendations}

## ⚠️ Risk Alerts
{risk_alerts}

---
*Report generated automatically at {timestamp}*
"""
    
    def _get_health_dashboard_template(self) -> str:
        """Get health dashboard template."""
        return """
# {title}

## 🏥 Market Health Overview
{summary}

## 📊 Data Quality Metrics
{data_quality}

## 🔌 Provider Performance
{provider_reliability}

## 🤖 Health Insights
{ai_insights}

## 💡 Health Recommendations
{recommendations}

## ⚠️ Health Alerts
{risk_alerts}

---
*Health report generated automatically at {timestamp}*
"""
    
    def _get_anomaly_alert_template(self) -> str:
        """Get anomaly alert template."""
        return """
# 🚨 Anomaly Alert

## ⚠️ Detected Anomalies
{anomalies}

## 🔍 Analysis
{analysis}

## 💡 Recommendations
{recommendations}

## ⚠️ Risk Assessment
{risk_assessment}

---
*Alert generated automatically at {timestamp}*
"""


# Global report engine instance
report_engine = AIReportEngine()


async def generate_daily_report() -> GeneratedReport:
    """Convenience function to generate daily market report."""
    return await report_engine.generate_daily_market_report()


async def generate_health_report() -> GeneratedReport:
    """Convenience function to generate market health report."""
    return await report_engine.generate_market_health_report() 