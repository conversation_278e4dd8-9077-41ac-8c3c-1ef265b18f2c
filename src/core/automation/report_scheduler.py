"""
AI Report Automation Scheduler

Automated scheduling and execution of AI-powered market reports using APScheduler.
Integrates with the report engine and formatter for seamless automation.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.jobstores.memory import MemoryJobStore

from .report_engine import AIReportEngine, GeneratedReport, ReportType
from .report_formatter import ReportFormatter, OutputFormat, FormattedReport
from .discord_handler import DiscordWebhookHandler, initialize_discord_handler
from src.core.market_calendar import get_market_context, MarketStatus

logger = logging.getLogger(__name__)


@dataclass
class ScheduledJob:
    """Scheduled job configuration."""
    job_id: str
    report_type: ReportType
    schedule: str
    output_formats: List[OutputFormat]
    enabled: bool = True
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    run_count: int = 0
    success_count: int = 0
    error_count: int = 0


class AIReportScheduler:
    """
    Automated scheduler for AI-powered market reports.
    """
    
    def __init__(self, discord_webhook_url: Optional[str] = None):
        self.scheduler = AsyncIOScheduler(
            jobstores={'default': MemoryJobStore()},
            timezone='UTC'
        )
        self.report_engine = AIReportEngine()
        self.report_formatter = ReportFormatter()
        self.scheduled_jobs: Dict[str, ScheduledJob] = {}
        self.output_handlers: Dict[OutputFormat, callable] = {}
        
        # Initialize Discord handler if webhook URL provided
        if discord_webhook_url:
            self.discord_handler = initialize_discord_handler(discord_webhook_url)
            # Register Discord output handler
            self.register_output_handler(OutputFormat.DISCORD, self._discord_output_handler)
            logger.info("Discord webhook handler initialized and registered")
        else:
            self.discord_handler = None
        
        # Initialize default jobs
        self._initialize_default_jobs()
        
    def _initialize_default_jobs(self):
        """Initialize default scheduled jobs."""
        default_jobs = [
            {
                "job_id": "daily_market_summary",
                "report_type": ReportType.DAILY_MARKET_SUMMARY,
                "schedule": "0 21 * * 1-5",  # 9:00 PM UTC (4:00 PM ET) Mon-Fri
                "output_formats": [OutputFormat.DISCORD, OutputFormat.MARKDOWN],
                "description": "Daily market summary after market close"
            },
            {
                "job_id": "market_health_dashboard",
                "report_type": ReportType.MARKET_HEALTH_DASHBOARD,
                "schedule": "0 14 * * 1-5",  # 2:00 PM UTC (9:00 AM ET) Mon-Fri
                "output_formats": [OutputFormat.DISCORD, OutputFormat.MARKDOWN],
                "description": "Market health dashboard at market open"
            },
            {
                "job_id": "pre_market_analysis",
                "report_type": ReportType.DAILY_MARKET_SUMMARY,
                "schedule": "0 12 * * 1-5",  # 12:00 PM UTC (7:00 AM ET) Mon-Fri
                "output_formats": [OutputFormat.DISCORD],
                "description": "Pre-market analysis before market open"
            },
            {
                "job_id": "weekend_summary",
                "report_type": ReportType.DAILY_MARKET_SUMMARY,
                "schedule": "0 18 * * 6",  # 6:00 PM UTC (1:00 PM ET) Saturday
                "output_formats": [OutputFormat.DISCORD, OutputFormat.MARKDOWN],
                "description": "Weekend market summary"
            }
        ]
        
        for job_config in default_jobs:
            self.add_scheduled_job(
                job_id=job_config["job_id"],
                report_type=job_config["report_type"],
                schedule=job_config["schedule"],
                output_formats=job_config["output_formats"],
                description=job_config["description"]
            )
    
    def add_scheduled_job(
        self,
        job_id: str,
        report_type: ReportType,
        schedule: str,
        output_formats: List[OutputFormat],
        description: str = "",
        enabled: bool = True
    ) -> bool:
        """
        Add a new scheduled job.
        
        Args:
            job_id: Unique identifier for the job
            report_type: Type of report to generate
            schedule: Cron schedule string (e.g., "0 21 * * 1-5")
            output_formats: List of output formats
            description: Job description
            enabled: Whether the job is enabled
            
        Returns:
            True if job added successfully, False otherwise
        """
        try:
            if job_id in self.scheduled_jobs:
                logger.warning(f"Job {job_id} already exists, updating configuration")
                self.remove_scheduled_job(job_id)
            
            # Create scheduled job
            scheduled_job = ScheduledJob(
                job_id=job_id,
                report_type=report_type,
                schedule=schedule,
                output_formats=output_formats,
                enabled=enabled
            )
            
            self.scheduled_jobs[job_id] = scheduled_job
            
            # Add to scheduler if enabled
            if enabled:
                self._add_job_to_scheduler(scheduled_job)
            
            logger.info(f"Added scheduled job: {job_id} ({description})")
            return True
            
        except Exception as e:
            logger.error(f"Error adding scheduled job {job_id}: {e}")
            return False
    
    def remove_scheduled_job(self, job_id: str) -> bool:
        """Remove a scheduled job."""
        try:
            if job_id in self.scheduled_jobs:
                # Remove from scheduler
                if self.scheduler.get_job(job_id):
                    self.scheduler.remove_job(job_id)
                
                # Remove from tracking
                del self.scheduled_jobs[job_id]
                
                logger.info(f"Removed scheduled job: {job_id}")
                return True
            else:
                logger.warning(f"Job {job_id} not found")
                return False
                
        except Exception as e:
            logger.error(f"Error removing scheduled job {job_id}: {e}")
            return False
    
    def enable_job(self, job_id: str) -> bool:
        """Enable a scheduled job."""
        try:
            if job_id in self.scheduled_jobs:
                job = self.scheduled_jobs[job_id]
                job.enabled = True
                
                # Add to scheduler if not already there
                if not self.scheduler.get_job(job_id):
                    self._add_job_to_scheduler(job)
                
                logger.info(f"Enabled scheduled job: {job_id}")
                return True
            else:
                logger.warning(f"Job {job_id} not found")
                return False
                
        except Exception as e:
            logger.error(f"Error enabling scheduled job {job_id}: {e}")
            return False
    
    def disable_job(self, job_id: str) -> bool:
        """Disable a scheduled job."""
        try:
            if job_id in self.scheduled_jobs:
                job = self.scheduled_jobs[job_id]
                job.enabled = False
                
                # Remove from scheduler
                if self.scheduler.get_job(job_id):
                    self.scheduler.remove_job(job_id)
                
                logger.info(f"Disabled scheduled job: {job_id}")
                return True
            else:
                logger.warning(f"Job {job_id} not found")
                return False
                
        except Exception as e:
            logger.error(f"Error disabling scheduled job {job_id}: {e}")
            return False
    
    def _add_job_to_scheduler(self, scheduled_job: ScheduledJob):
        """Add a job to the APScheduler."""
        try:
            # Parse cron schedule
            cron_parts = scheduled_job.schedule.split()
            if len(cron_parts) != 5:
                raise ValueError(f"Invalid cron schedule: {scheduled_job.schedule}")
            
            minute, hour, day, month, day_of_week = cron_parts
            
            # Create cron trigger
            trigger = CronTrigger(
                minute=minute,
                hour=hour,
                day=day,
                month=month,
                day_of_week=day_of_week,
                timezone='UTC'
            )
            
            # Add job to scheduler
            self.scheduler.add_job(
                func=self._execute_scheduled_job,
                trigger=trigger,
                args=[scheduled_job.job_id],
                id=scheduled_job.job_id,
                name=f"AI Report: {scheduled_job.report_type.value}",
                replace_existing=True
            )
            
            # Update next run time
            next_run = self.scheduler.get_job(scheduled_job.job_id).next_run_time
            scheduled_job.next_run = next_run
            
            logger.info(f"Added job {scheduled_job.job_id} to scheduler, next run: {next_run}")
            
        except Exception as e:
            logger.error(f"Error adding job {scheduled_job.job_id} to scheduler: {e}")
            raise
    
    async def _execute_scheduled_job(self, job_id: str):
        """Execute a scheduled job."""
        try:
            if job_id not in self.scheduled_jobs:
                logger.error(f"Job {job_id} not found in scheduled jobs")
                return
            
            job = self.scheduled_jobs[job_id]
            job.last_run = datetime.now(timezone.utc)
            job.run_count += 1
            
            logger.info(f"Executing scheduled job: {job_id} ({job.report_type.value})")
            
            # Check market conditions
            if not self._should_run_job(job):
                logger.info(f"Job {job_id} skipped due to market conditions")
                return
            
            # Generate report
            report = await self._generate_report(job.report_type)
            if not report:
                logger.error(f"Failed to generate report for job {job_id}")
                job.error_count += 1
                return
            
            # Format and output report
            success = await self._output_report(report, job.output_formats)
            
            if success:
                job.success_count += 1
                logger.info(f"Job {job_id} completed successfully")
            else:
                job.error_count += 1
                logger.error(f"Job {job_id} failed during output")
            
            # Update next run time
            next_run = self.scheduler.get_job(job_id).next_run_time
            job.next_run = next_run
            
        except Exception as e:
            logger.error(f"Error executing scheduled job {job_id}: {e}")
            if job_id in self.scheduled_jobs:
                self.scheduled_jobs[job_id].error_count += 1
    
    def _should_run_job(self, job: ScheduledJob) -> bool:
        """Determine if a job should run based on market conditions."""
        try:
            # Get current market context
            market_context = get_market_context(datetime.now(timezone.utc))
            market_status = market_context.get('status', 'unknown')
            
            # Skip jobs during certain market conditions
            if job.job_id == "daily_market_summary":
                # Only run during market hours or after market close
                if market_status in ['holiday', 'weekend']:
                    return False
            
            elif job.job_id == "pre_market_analysis":
                # Only run before market open
                if market_status == 'open':
                    return False
            
            elif job.job_id == "market_health_dashboard":
                # Run regardless of market status
                return True
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking if job should run: {e}")
            return True  # Default to running if check fails
    
    async def _generate_report(self, report_type: ReportType) -> Optional[GeneratedReport]:
        """Generate a report of the specified type."""
        try:
            if report_type == ReportType.DAILY_MARKET_SUMMARY:
                return await self.report_engine.generate_daily_market_report()
            elif report_type == ReportType.MARKET_HEALTH_DASHBOARD:
                return await self.report_engine.generate_market_health_report()
            elif report_type == ReportType.ANOMALY_ALERT:
                # TODO: Implement anomaly alert generation
                logger.warning("Anomaly alert generation not yet implemented")
                return None
            else:
                logger.warning(f"Unknown report type: {report_type}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating report {report_type}: {e}")
            return None
    
    async def _output_report(self, report: GeneratedReport, output_formats: List[OutputFormat]) -> bool:
        """Output report in specified formats."""
        try:
            success = True
            
            for output_format in output_formats:
                try:
                    # Format report
                    formatted_report = self._format_report(report, output_format)
                    if not formatted_report:
                        logger.error(f"Failed to format report for {output_format}")
                        success = False
                        continue
                    
                    # Output report
                    output_success = await self._send_formatted_report(formatted_report)
                    if not output_success:
                        logger.error(f"Failed to output report in {output_format}")
                        success = False
                    
                except Exception as e:
                    logger.error(f"Error processing output format {output_format}: {e}")
                    success = False
            
            return success
            
        except Exception as e:
            logger.error(f"Error outputting report: {e}")
            return False
    
    def _format_report(self, report: GeneratedReport, output_format: OutputFormat) -> Optional[FormattedReport]:
        """Format report for specific output format."""
        try:
            if output_format == OutputFormat.DISCORD:
                return self.report_formatter.format_for_discord(report)
            elif output_format == OutputFormat.MARKDOWN:
                return self.report_formatter.format_for_markdown(report)
            elif output_format == OutputFormat.EMAIL:
                return self.report_formatter.format_for_email(report)
            elif output_format == OutputFormat.JSON:
                return self.report_formatter.format_for_json(report)
            else:
                logger.warning(f"Unknown output format: {output_format}")
                return None
                
        except Exception as e:
            logger.error(f"Error formatting report for {output_format}: {e}")
            return None
    
    async def _send_formatted_report(self, formatted_report: FormattedReport) -> bool:
        """Send formatted report to appropriate output handler."""
        try:
            output_format = formatted_report.format
            
            if output_format in self.output_handlers:
                handler = self.output_handlers[output_format]
                result = await handler(formatted_report)
                return result
            else:
                logger.warning(f"No output handler registered for {output_format}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending formatted report: {e}")
            return False
    
    def register_output_handler(self, output_format: OutputFormat, handler: callable):
        """Register an output handler for a specific format."""
        try:
            self.output_handlers[output_format] = handler
            logger.info(f"Registered output handler for {output_format}")
            
        except Exception as e:
            logger.error(f"Error registering output handler for {output_format}: {e}")
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get status information for a specific job."""
        try:
            if job_id not in self.scheduled_jobs:
                return None
            
            job = self.scheduled_jobs[job_id]
            scheduler_job = self.scheduler.get_job(job_id)
            
            return {
                "job_id": job.job_id,
                "report_type": job.report_type.value,
                "schedule": job.schedule,
                "enabled": job.enabled,
                "last_run": job.last_run.isoformat() if job.last_run else None,
                "next_run": job.next_run.isoformat() if job.next_run else None,
                "run_count": job.run_count,
                "success_count": job.success_count,
                "error_count": job.error_count,
                "scheduler_status": "scheduled" if scheduler_job else "not_scheduled"
            }
            
        except Exception as e:
            logger.error(f"Error getting job status for {job_id}: {e}")
            return None
    
    def get_all_jobs_status(self) -> List[Dict[str, Any]]:
        """Get status information for all jobs."""
        try:
            status_list = []
            
            for job_id in self.scheduled_jobs:
                status = self.get_job_status(job_id)
                if status:
                    status_list.append(status)
            
            return status_list
            
        except Exception as e:
            logger.error(f"Error getting all jobs status: {e}")
            return []
    
    def start(self):
        """Start the scheduler."""
        try:
            if not self.scheduler.running:
                self.scheduler.start()
                logger.info("AI Report Scheduler started")
            else:
                logger.info("AI Report Scheduler already running")
                
        except Exception as e:
            logger.error(f"Error starting AI Report Scheduler: {e}")
            raise
    
    def stop(self):
        """Stop the scheduler."""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown()
                logger.info("AI Report Scheduler stopped")
            else:
                logger.info("AI Report Scheduler not running")
                
        except Exception as e:
            logger.error(f"Error stopping AI Report Scheduler: {e}")
    
    def is_running(self) -> bool:
        """Check if scheduler is running."""
        return self.scheduler.running
    
    async def run_job_now(self, job_id: str) -> bool:
        """Run a job immediately."""
        try:
            if job_id not in self.scheduled_jobs:
                logger.error(f"Job {job_id} not found")
                return False
            
            logger.info(f"Running job {job_id} immediately")
            await self._execute_scheduled_job(job_id)
            return True
            
        except Exception as e:
            logger.error(f"Error running job {job_id} immediately: {e}")
            return False
    
    async def test_job(self, job_id: str) -> bool:
        """Test a job without affecting statistics."""
        try:
            if job_id not in self.scheduled_jobs:
                logger.error(f"Job {job_id} not found")
                return False
            
            logger.info(f"Testing job {job_id}")
            
            # Generate report
            job = self.scheduled_jobs[job_id]
            report = await self._generate_report(job.report_type)
            if not report:
                logger.error(f"Failed to generate report for test job {job_id}")
                return False
            
            # Format report (test first output format)
            if job.output_formats:
                formatted_report = self._format_report(report, job.output_formats[0])
                if formatted_report:
                    logger.info(f"Test job {job_id} completed successfully")
                    return True
                else:
                    logger.error(f"Failed to format report for test job {job_id}")
                    return False
            else:
                logger.error(f"No output formats configured for test job {job_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing job {job_id}: {e}")
            return False
    
    async def _discord_output_handler(self, formatted_report: FormattedReport) -> bool:
        """Handle Discord output for formatted reports."""
        try:
            if self.discord_handler:
                return await self.discord_handler.send_report(formatted_report)
            else:
                logger.warning("Discord handler not available")
                return False
        except Exception as e:
            logger.error(f"Error in Discord output handler: {e}")
            return False


# Global scheduler instance (will be initialized with Discord webhook)
ai_report_scheduler: Optional[AIReportScheduler] = None


def initialize_ai_report_scheduler(discord_webhook_url: Optional[str] = None) -> AIReportScheduler:
    """Initialize the global AI report scheduler with optional Discord integration."""
    global ai_report_scheduler
    ai_report_scheduler = AIReportScheduler(discord_webhook_url)
    return ai_report_scheduler


def start_ai_report_scheduler():
    """Start the global AI report scheduler."""
    if ai_report_scheduler:
        ai_report_scheduler.start()
    else:
        logger.error("AI report scheduler not initialized")


def stop_ai_report_scheduler():
    """Stop the global AI report scheduler."""
    if ai_report_scheduler:
        ai_report_scheduler.stop()
    else:
        logger.error("AI report scheduler not initialized")


def get_ai_report_scheduler() -> Optional[AIReportScheduler]:
    """Get the global AI report scheduler instance."""
    return ai_report_scheduler


async def generate_and_send_report(
    report_type: ReportType,
    output_formats: List[OutputFormat]
) -> bool:
    """
    Convenience function to generate and send a report immediately.
    
    Args:
        report_type: Type of report to generate
        output_formats: List of output formats
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Generate report
        report = await ai_report_scheduler._generate_report(report_type)
        if not report:
            return False
        
        # Output report
        success = await ai_report_scheduler._output_report(report, output_formats)
        return success
        
    except Exception as e:
        logger.error(f"Error in generate_and_send_report: {e}")
        return False 