import re
import os
from fastapi.security import <PERSON>Auth2Pass<PERSON><PERSON><PERSON><PERSON>
from fastapi import Depends, HTTPException
import secrets
import hashlib
from typing import Any, Dict, Optional, Union
from pydantic import BaseModel, Field, EmailStr, model_validator
from passlib.hash import bcrypt
from datetime import datetime, timedelta
import jwt
from passlib.context import CryptContext
from .security_config import SecurityConfig

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash a password for storing."""
    return pwd_context.hash(password)

def hash_password(password: str) -> str:
    """Hash a password for storing."""
    return get_password_hash(password)

def validate_password(password: str) -> bool:
    """
    Validate password strength.
    Basic validation - at least 8 characters, has uppercase, lowercase, and number.
    """
    if len(password) < 8:
        return False
    if not any(c.isupper() for c in password):
        return False
    if not any(c.islower() for c in password):
        return False
    if not any(c.isdigit() for c in password):
        return False
    return True

def generate_password_salt() -> str:
    """Generate a random salt for password hashing."""
    return secrets.token_hex(16)  # 32 character hex string

class SecureModel(BaseModel):
    """
    Base model with additional security validations.
    """
    
    @model_validator(mode='before')
    @classmethod
    def sanitize_fields(cls, value):
        """
        Sanitize all string fields before validation.
        """
        if isinstance(value, dict):
            for key, val in value.items():
                if isinstance(val, str):
                    value[key] = SecurityConfig.sanitize_input(val)
        return value

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    Generate a secure JWT access token
    
    Args:
        data (dict): Payload data to encode in the token
        expires_delta (Optional[timedelta]): Token expiration time
    
    Returns:
        str: Encoded JWT token
    """
    to_encode = data.copy()
    
    # Set default expiration if not provided
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        # Default to 15 minutes
        expire = datetime.utcnow() + timedelta(minutes=15)
    
    to_encode.update({"exp": expire})
    
    # Get secret from environment variable
    secret_key = os.getenv('JWT_SECRET')
    if not secret_key:
        raise ValueError("JWT_SECRET environment variable is required")
    
    # Use secure algorithm and secret
    encoded_jwt = jwt.encode(
        to_encode, 
        secret_key, 
        algorithm="HS256"
    )
    return encoded_jwt

def decode_token(token: str):
    """
    Decode and validate a JWT token
    
    Args:
        token (str): JWT token to decode
    
    Returns:
        dict: Decoded token payload
    """
    try:
        secret_key = os.getenv('JWT_SECRET')
        if not secret_key:
            raise ValueError("JWT_SECRET environment variable is required")
        
        payload = jwt.decode(
            token, 
            secret_key, 
            algorithms=["HS256"]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

def generate_temporary_user_token(username: str = "default_user"):
    """
    Generate a temporary user token for testing/development
    
    Args:
        username (str): Username to encode in token
    
    Returns:
        str: Generated JWT token
    """
    token_data = {
        "sub": username,
        "exp": datetime.utcnow() + timedelta(hours=1)
    }
    return create_access_token(token_data)
