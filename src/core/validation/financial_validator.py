from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
import numpy as np
from datetime import datetime, timedelta
from enum import Enum

from ..logger import get_logger

logger = get_logger(__name__)


class DataQualityLevel(Enum):
    """Data quality levels"""
    EXCELLENT = "excellent"      # 90-100%
    GOOD = "good"               # 70-89%
    FAIR = "fair"               # 50-69%
    POOR = "poor"               # 30-49%
    UNRELIABLE = "unreliable"   # 0-29%


@dataclass
class ValidationResult:
    """
    Enhanced validation result with quality scoring
    """
    is_valid: bool
    quality_score: float = 0.0
    quality_level: DataQualityLevel = DataQualityLevel.UNRELIABLE
    errors: Optional[Dict[str, str]] = None
    warnings: Optional[Dict[str, str]] = None
    consensus_data: Optional[Dict[str, Any]] = None
    provider_scores: Dict[str, float] = field(default_factory=dict)
    
    def __post_init__(self):
        """Calculate quality level from score"""
        if self.quality_score >= 90:
            self.quality_level = DataQualityLevel.EXCELLENT
        elif self.quality_score >= 70:
            self.quality_level = DataQualityLevel.GOOD
        elif self.quality_score >= 50:
            self.quality_level = DataQualityLevel.FAIR
        elif self.quality_score >= 30:
            self.quality_level = DataQualityLevel.POOR
        else:
            self.quality_level = DataQualityLevel.UNRELIABLE


@dataclass
class ConsensusResult:
    """Result of consensus analysis across multiple data sources"""
    consensus_value: Any
    confidence: float
    provider_values: Dict[str, Any]
    outliers: List[str]
    agreement_percentage: float

class FinancialDataValidator:
    """
    Comprehensive validator for financial data with multiple validation strategies
    """
    
    @staticmethod
    def validate_stock_data(data: Dict[str, Any]) -> ValidationResult:
        """
        Validate comprehensive stock data
        
        Args:
            data (Dict[str, Any]): Stock data dictionary to validate
        
        Returns:
            ValidationResult: Validation outcome
        """
        errors = {}
        warnings = {}
        
        # Validate basic structure
        required_keys = [
            'symbol', 'current_price', 'price_history'
        ]
        for key in required_keys:
            if key not in data:
                errors[key] = f"Missing required key: {key}"
        
        # Optional keys that can be None
        optional_keys = ['market_cap', 'financials', 'earnings']
        for key in optional_keys:
            if key not in data:
                warnings[key] = f"Missing optional key: {key}"
        
        # Price validation
        try:
            current_price = float(data.get('current_price', 0))
            if current_price <= 0:
                errors['current_price'] = "Price must be positive"
            
            # Reasonable price range check (0.01 to 100,000)
            if not (0.01 <= current_price <= 100000):
                warnings['current_price'] = "Price seems unusually high or low"
        except (TypeError, ValueError):
            errors['current_price'] = "Invalid price format"
        
        # Market cap validation (optional)
        market_cap = data.get('market_cap')
        if market_cap is not None:
            try:
                market_cap = float(market_cap)
                if market_cap < 0:
                    errors['market_cap'] = "Market cap cannot be negative"
                
                # Reasonable market cap range (million to trillion)
                if not (1_000_000 <= market_cap <= 10_000_000_000_000):
                    warnings['market_cap'] = "Market cap seems unusually high or low"
            except (TypeError, ValueError):
                errors['market_cap'] = "Invalid market cap format"
        
        # Price history validation
        price_history = data.get('price_history', {})
        if not price_history or 'close' not in price_history:
            errors['price_history'] = "Invalid or missing price history"
        else:
            try:
                prices = np.array(price_history['close'])
                
                # Check for extreme price variations
                price_variation = np.std(prices) / np.mean(prices)
                if price_variation > 0.5:
                    warnings['price_history'] = "Unusually high price volatility"
            except Exception:
                errors['price_history'] = "Unable to analyze price history"
        
        # Financials validation (optional)
        financials = data.get('financials')
        if financials is not None:
            try:
                revenue = float(financials.get('total_revenue', 0)) if financials.get('total_revenue') is not None else 0
                net_income = float(financials.get('net_income', 0)) if financials.get('net_income') is not None else 0
                
                # Basic financial sanity checks
                if revenue < 0:
                    errors['revenue'] = "Revenue cannot be negative"
                
                if net_income < -revenue:  # Net loss cannot exceed total revenue
                    warnings['net_income'] = "Unusually large net loss"
            except (TypeError, ValueError):
                warnings['financials'] = "Invalid financial data format"
        
        # Earnings validation (optional)
        earnings = data.get('earnings')
        if earnings is not None and earnings.get('quarterly_history'):
            if not earnings['quarterly_history']:
                warnings['earnings'] = "Limited or missing earnings data"
        
        # Timestamp validation
        try:
            timestamp = datetime.fromisoformat(data.get('data_timestamp', ''))
            if timestamp > datetime.now() + timedelta(days=1):
                warnings['data_timestamp'] = "Timestamp appears to be in the future"
            
            if timestamp < datetime.now() - timedelta(days=365):
                warnings['data_timestamp'] = "Data appears to be over a year old"
        except (TypeError, ValueError):
            errors['data_timestamp'] = "Invalid timestamp format"
        
        # Determine overall validation status
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors if errors else None,
            warnings=warnings if warnings else None
        )
    
    @staticmethod
    def sanitize_stock_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize stock data by removing or correcting problematic values
        
        Args:
            data (Dict[str, Any]): Stock data to sanitize
        
        Returns:
            Dict[str, Any]: Sanitized stock data
        """
        # Create a deep copy to avoid modifying original data
        sanitized_data = data.copy()
        
        # Sanitize numeric fields
        numeric_fields = [
            'current_price', 'market_cap', 
            'financials.total_revenue', 
            'financials.net_income'
        ]
        
        for field in numeric_fields:
            try:
                # Navigate nested dictionaries
                keys = field.split('.')
                current = sanitized_data
                for key in keys[:-1]:
                    current = current.get(key, {})
                
                # Sanitize the final key
                value = current.get(keys[-1], 0)
                
                # Convert to float and handle edge cases
                sanitized_value = float(max(0, value))
                
                # Update the nested structure
                current[keys[-1]] = sanitized_value
            except (TypeError, ValueError):
                # If conversion fails, set to 0 or remove
                current[keys[-1]] = 0
        
        # Sanitize price history
        if 'price_history' in sanitized_data:
            try:
                prices = np.array(sanitized_data['price_history'].get('close', []))
                
                # Remove extreme outliers (beyond 3 standard deviations)
                mean_price = np.mean(prices)
                std_price = np.std(prices)
                
                sanitized_prices = [
                    price for price in prices 
                    if mean_price - 3*std_price <= price <= mean_price + 3*std_price
                ]
                
                sanitized_data['price_history']['close'] = sanitized_prices
            except Exception:
                # If sanitization fails, clear price history
                sanitized_data['price_history'] = {'close': []}
        
        return sanitized_data
    
    @staticmethod
    def calculate_quality_score(data: Dict[str, Any], provider: str = "unknown") -> float:
        """
        Calculate data quality score based on completeness, freshness, and consistency
        
        Args:
            data: Stock data to score
            provider: Data provider name
            
        Returns:
            Quality score from 0-100
        """
        score = 100.0
        
        # Completeness scoring (40% of total score)
        required_fields = ['symbol', 'current_price', 'volume', 'timestamp']
        optional_fields = ['market_cap', 'pe_ratio', 'change_percent', 'previous_close']
        
        missing_required = sum(1 for field in required_fields if not data.get(field))
        missing_optional = sum(1 for field in optional_fields if not data.get(field))
        
        completeness_score = max(0, 40 - (missing_required * 15) - (missing_optional * 2))
        score = min(score, completeness_score + 60)  # Cap at 60 if missing required fields
        
        # Freshness scoring (30% of total score)
        try:
            timestamp = data.get('timestamp')
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            elif not isinstance(timestamp, datetime):
                timestamp = datetime.now()
            
            age_minutes = (datetime.now() - timestamp).total_seconds() / 60
            
            if age_minutes <= 5:
                freshness_score = 30
            elif age_minutes <= 15:
                freshness_score = 25
            elif age_minutes <= 60:
                freshness_score = 20
            elif age_minutes <= 1440:  # 24 hours
                freshness_score = 10
            else:
                freshness_score = 0
            
            score = min(score, (score - 30) + freshness_score)
            
        except Exception:
            score -= 30  # Penalize invalid timestamps
        
        # Consistency scoring (20% of total score)
        consistency_score = 20
        
        # Check for reasonable price values
        current_price = data.get('current_price', 0)
        if isinstance(current_price, (int, float)) and current_price > 0:
            if current_price < 0.01 or current_price > 100000:
                consistency_score -= 10
        else:
            consistency_score -= 15
        
        # Check volume reasonableness
        volume = data.get('volume', 0)
        if isinstance(volume, (int, float)) and volume >= 0:
            if volume == 0:
                consistency_score -= 5  # Zero volume is suspicious but not invalid
        else:
            consistency_score -= 10
        
        score = min(score, (score - 20) + consistency_score)
        
        # Provider reliability bonus/penalty (10% of total score)
        provider_scores = {
            'yahoo_finance': 10,
            'alpha_vantage': 8,
            'polygon': 9,
            'finnhub': 7,
            'unknown': 5
        }
        
        provider_bonus = provider_scores.get(provider.lower(), 5)
        score = min(100, score + (provider_bonus - 5))
        
        return max(0, min(100, score))
    
    @staticmethod
    def create_consensus(provider_data: Dict[str, Dict[str, Any]]) -> ConsensusResult:
        """
        Create consensus data from multiple providers with conflict resolution
        
        Args:
            provider_data: Dict mapping provider names to their data
            
        Returns:
            ConsensusResult with consensus values and confidence scores
        """
        if not provider_data:
            return ConsensusResult(
                consensus_value={},
                confidence=0.0,
                provider_values={},
                outliers=[],
                agreement_percentage=0.0
            )
        
        consensus_data = {}
        provider_values = {}
        outliers = []
        
        # Fields to create consensus for
        numeric_fields = ['current_price', 'volume', 'market_cap', 'pe_ratio']
        string_fields = ['symbol', 'exchange', 'currency']
        
        # Process numeric fields with statistical consensus
        for field in numeric_fields:
            values = []
            field_providers = {}
            
            for provider, data in provider_data.items():
                value = data.get(field)
                if value is not None and isinstance(value, (int, float)) and value > 0:
                    values.append(value)
                    field_providers[provider] = value
            
            if values:
                provider_values[field] = field_providers
                
                if len(values) == 1:
                    consensus_data[field] = values[0]
                else:
                    # Use median for consensus (more robust than mean)
                    consensus_value = np.median(values)
                    consensus_data[field] = consensus_value
                    
                    # Identify outliers (values more than 20% away from consensus)
                    threshold = consensus_value * 0.2
                    for provider, value in field_providers.items():
                        if abs(value - consensus_value) > threshold:
                            outliers.append(f"{provider}:{field}")
        
        # Process string fields with majority vote
        for field in string_fields:
            values = []
            field_providers = {}
            
            for provider, data in provider_data.items():
                value = data.get(field)
                if value and isinstance(value, str):
                    values.append(value.upper())  # Normalize case
                    field_providers[provider] = value
            
            if values:
                provider_values[field] = field_providers
                
                # Use most common value
                from collections import Counter
                most_common = Counter(values).most_common(1)
                if most_common:
                    consensus_data[field] = most_common[0][0]
        
        # Calculate overall agreement percentage
        total_comparisons = 0
        agreements = 0
        
        for field, field_providers in provider_values.items():
            if len(field_providers) > 1:
                consensus_value = consensus_data.get(field)
                for provider, value in field_providers.items():
                    total_comparisons += 1
                    if field in numeric_fields:
                        # For numeric fields, consider within 5% as agreement
                        if consensus_value and abs(value - consensus_value) / consensus_value <= 0.05:
                            agreements += 1
                    else:
                        # For string fields, exact match
                        if str(value).upper() == str(consensus_value).upper():
                            agreements += 1
        
        agreement_percentage = (agreements / total_comparisons * 100) if total_comparisons > 0 else 100
        
        # Calculate confidence based on number of providers and agreement
        num_providers = len(provider_data)
        confidence = min(100, (num_providers * 20) + (agreement_percentage * 0.8))
        
        return ConsensusResult(
            consensus_value=consensus_data,
            confidence=confidence,
            provider_values=provider_values,
            outliers=outliers,
            agreement_percentage=agreement_percentage
        )
    
    @staticmethod
    def validate_with_consensus(provider_data: Dict[str, Dict[str, Any]]) -> ValidationResult:
        """
        Validate data using consensus from multiple providers
        
        Args:
            provider_data: Dict mapping provider names to their data
            
        Returns:
            Enhanced ValidationResult with consensus data and quality scoring
        """
        if not provider_data:
            return ValidationResult(
                is_valid=False,
                quality_score=0.0,
                errors={"providers": "No provider data available"}
            )
        
        # Create consensus
        consensus_result = FinancialDataValidator.create_consensus(provider_data)
        
        # Validate consensus data
        validation_result = FinancialDataValidator.validate_stock_data(consensus_result.consensus_value)
        
        # Calculate provider-specific quality scores
        provider_scores = {}
        for provider, data in provider_data.items():
            provider_scores[provider] = FinancialDataValidator.calculate_quality_score(data, provider)
        
        # Calculate overall quality score weighted by provider reliability
        if provider_scores:
            weighted_score = sum(provider_scores.values()) / len(provider_scores)
            # Boost score based on consensus confidence
            consensus_boost = consensus_result.confidence * 0.1
            overall_quality = min(100, weighted_score + consensus_boost)
        else:
            overall_quality = 0.0
        
        # Add consensus-specific warnings
        warnings = validation_result.warnings or {}
        if consensus_result.outliers:
            warnings['consensus'] = f"Data conflicts detected: {', '.join(consensus_result.outliers)}"
        
        if consensus_result.agreement_percentage < 80:
            warnings['agreement'] = f"Low provider agreement: {consensus_result.agreement_percentage:.1f}%"
        
        return ValidationResult(
            is_valid=validation_result.is_valid and consensus_result.confidence > 50,
            quality_score=overall_quality,
            errors=validation_result.errors,
            warnings=warnings if warnings else None,
            consensus_data=consensus_result.consensus_value,
            provider_scores=provider_scores
        ) 