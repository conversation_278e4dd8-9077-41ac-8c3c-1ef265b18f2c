"""
Configuration validation for critical trading parameters.
Prevents invalid fallback values from being used in production.
"""

import os
import logging
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from dataclasses import fields

logger = logging.getLogger(__name__)

class ConfigValidator:
    """Validates configuration values to prevent dangerous fallbacks"""
    
    @staticmethod
    def validate_trading_strategy(config: Any) -> Tuple[bool, Dict[str, str]]:
        """
        Validate trading strategy configuration values.
        
        Args:
            config: TradingStrategyConfig instance
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = {}
        
        # Get field metadata for validation rules
        for field in fields(config):
            field_name = field.name
            field_value = getattr(config, field_name)
            metadata = field.metadata
            
            if 'validation' in metadata:
                validation_rule = metadata['validation']
                if not ConfigValidator._validate_field(field_name, field_value, validation_rule):
                    errors[field_name] = f"Value {field_value} failed validation: {validation_rule}"
                    logger.error(f"Configuration validation failed for {field_name}: {field_value}")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def _validate_field(field_name: str, value: Any, validation_rule: str) -> bool:
        """Validate a single field against its validation rule"""
        try:
            # Parse validation rules like "0.001 <= value <= 0.1"
            if '<=' in validation_rule and 'value' in validation_rule:
                parts = validation_rule.split('<=')
                if len(parts) == 3:
                    min_val = float(parts[0].strip())
                    max_val = float(parts[2].strip())
                    return min_val <= value <= max_val
            
            # Parse validation rules like "1 <= value <= 20"
            elif '<=' in validation_rule and 'value' in validation_rule:
                parts = validation_rule.split('<=')
                if len(parts) == 3:
                    min_val = int(parts[0].strip())
                    max_val = int(parts[2].strip())
                    return min_val <= value <= max_val
            
            return True  # Default to valid if rule not recognized
            
        except (ValueError, TypeError) as e:
            logger.error(f"Validation rule parsing error for {field_name}: {e}")
            return False
    
    @staticmethod
    def validate_environment_variables() -> Tuple[bool, Dict[str, str]]:
        """
        Validate that critical environment variables are set.
        
        Returns:
            Tuple of (all_set, missing_vars)
        """
        critical_vars = [
            'RISK_PER_TRADE',
            'MAX_POSITION_SIZE', 
            'STOP_LOSS_MULTIPLIER',
            'TAKE_PROFIT_MULTIPLIER',
            'MAX_OPEN_POSITIONS',
            'MINIMUM_VOLUME_THRESHOLD',
            'PRICE_CHANGE_THRESHOLD'
        ]
        
        missing_vars = []
        for var in critical_vars:
            if not os.getenv(var):
                missing_vars.append(var)
                logger.warning(f"Critical environment variable {var} not set, using fallback")
        
        return len(missing_vars) == 0, missing_vars
    
    @staticmethod
    def log_fallback_usage(param_name: str, fallback_value: Any, env_var: str):
        """Log when fallback values are used for monitoring"""
        logger.warning(
            f"Fallback value used for {param_name}: {fallback_value} "
            f"(env var {env_var} not set)",
            extra={
                "fallback_used": True,
                "parameter": param_name,
                "fallback_value": fallback_value,
                "missing_env_var": env_var
            }
        )

def validate_config_on_startup():
    """Validate configuration on system startup"""
    logger.info("Starting configuration validation...")
    
    # Check environment variables
    env_valid, missing_env = ConfigValidator.validate_environment_variables()
    if not env_valid:
        logger.warning(f"Missing environment variables: {missing_env}")
    
    # Import and validate config after environment check
    try:
        from .config_manager import TradingStrategyConfig, TradingBotConfig
        
        # Create config instance
        config = TradingBotConfig()
        trading_config = config.get('trading_strategy')
        
        # Validate trading strategy
        config_valid, config_errors = ConfigValidator.validate_trading_strategy(trading_config)
        if not config_valid:
            logger.error(f"Configuration validation errors: {config_errors}")
            return False
        
        logger.info("Configuration validation completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        return False 