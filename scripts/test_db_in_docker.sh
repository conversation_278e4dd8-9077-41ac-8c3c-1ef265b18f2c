#!/bin/bash

# Test database connection inside Docker
set -e

echo "Testing database connection from within Docker..."

# Check if we're running in a container
if [ -f /.dockerenv ]; then
    echo "Running inside Docker container"
else
    echo "This script should be run inside a Docker container"
    exit 1
fi

# Check environment variables
echo "Environment variables:"
echo "USE_SUPABASE: ${USE_SUPABASE}"
echo "DATABASE_URL: ${DATABASE_URL}"
echo "SUPABASE_URL: ${SUPABASE_URL}"

# Install required packages if needed
if ! command -v psql &> /dev/null; then
    echo "Installing PostgreSQL client..."
    apt-get update && apt-get install -y postgresql-client
fi

# Clean the DATABASE_URL by removing the +asyncpg part if it exists
CLEAN_DB_URL=$(echo $DATABASE_URL | sed 's|postgresql+asyncpg://|postgresql://|')

# Extract database connection details using a more robust method
eval $(echo $CLEAN_DB_URL | sed -e 's|^.*//\([^@]*\)@\(.*\)$|DB_USER_PASS=\1 DB_HOST_PORT_DBNAME=\2|')
DB_USER=$(echo $DB_USER_PASS | cut -d: -f1)
DB_PASS=$(echo $DB_USER_PASS | cut -d: -f2)
DB_HOST=$(echo $DB_HOST_PORT_DBNAME | cut -d: -f1)
DB_PORT=$(echo $DB_HOST_PORT_DBNAME | cut -d/ -f1 | cut -d: -f2)
DB_NAME=$(echo $DB_HOST_PORT_DBNAME | cut -d/ -f2 | cut -d? -f1)

echo "Extracted database details:"
echo "User: $DB_USER"
echo "Host: $DB_HOST"
echo "Port: $DB_PORT"
echo "Database: $DB_NAME"

# Test connection with psql
echo "Testing connection with psql..."
PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1 as test_connection;"

echo "Database connection test completed successfully!"
