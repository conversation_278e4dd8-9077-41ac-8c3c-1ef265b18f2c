#!/usr/bin/env python3
import os
import sys
import argparse
import subprocess

def run_command(command):
    """
    Run a shell command and handle potential errors.
    
    Args:
        command (list): Command to run
    """
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        print(f"Stderr: {e.stderr}")
        sys.exit(1)

def create_migration(message):
    """
    Create a new database migration.
    
    Args:
        message (str): Migration message/description
    """
    command = [
        'alembic', 
        'revision', 
        '--autogenerate', 
        '-m', 
        message
    ]
    run_command(command)

def upgrade_database(revision='head'):
    """
    Upgrade database to specified revision.
    
    Args:
        revision (str, optional): Migration revision. Defaults to 'head'.
    """
    command = ['alembic', 'upgrade', revision]
    run_command(command)

def downgrade_database(revision='-1'):
    """
    Downgrade database to previous revision.
    
    Args:
        revision (str, optional): Number of revisions to downgrade. Defaults to previous.
    """
    command = ['alembic', 'downgrade', revision]
    run_command(command)

def show_history():
    """
    Show migration history.
    """
    command = ['alembic', 'history']
    run_command(command)

def main():
    """
    Main script entry point.
    """
    parser = argparse.ArgumentParser(description='Database Migration Management')
    subparsers = parser.add_subparsers(dest='command', help='Migration commands')
    
    # Create migration subcommand
    create_parser = subparsers.add_parser('create', help='Create a new migration')
    create_parser.add_argument('message', help='Migration description')
    
    # Upgrade subcommand
    upgrade_parser = subparsers.add_parser('upgrade', help='Upgrade database')
    upgrade_parser.add_argument('-r', '--revision', default='head', help='Revision to upgrade to')
    
    # Downgrade subcommand
    downgrade_parser = subparsers.add_parser('downgrade', help='Downgrade database')
    downgrade_parser.add_argument('-r', '--revision', default='-1', help='Revision to downgrade to')
    
    # History subcommand
    subparsers.add_parser('history', help='Show migration history')
    
    args = parser.parse_args()
    
    # Change to project root directory
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    os.chdir(project_root)
    
    if args.command == 'create':
        create_migration(args.message)
    elif args.command == 'upgrade':
        upgrade_database(args.revision)
    elif args.command == 'downgrade':
        downgrade_database(args.revision)
    elif args.command == 'history':
        show_history()
    else:
        parser.print_help()

if __name__ == '__main__':
    main() 