#!/usr/bin/env python3
"""
Environment Variables Management Utility

Helps generate, rotate, and manage environment variables for the trading automation project.
"""

import os
import secrets
import argparse
import json
from cryptography.fernet import Fernet

class EnvManager:
    def __init__(self, env_file='.env'):
        self.env_file = env_file
        self.env_vars = {}
        self.load_env()

    def load_env(self):
        """Load existing .env file if it exists"""
        if os.path.exists(self.env_file):
            with open(self.env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        self.env_vars[key] = value

    def generate_secret(self, secret_type: str) -> str:
        """Generate a realistic placeholder secret for the given type"""
        if secret_type == 'discord_bot_token':
            # Discord bot tokens are base64 encoded with format: xxxxxx.xxxxxx.xxxxxxxxxxxxxxxxxxxxx
            return "MTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIzNDU2Nzg5MDEyMzQ1Njc4OQ.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWxYz"
        elif secret_type == 'supabase_key':
            # Supabase keys are JWT tokens
            return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRlc3QtcmVmIiwiYXVkIjoiYW5vbnltb3VzIiwiaWF0IjoxNjM5NTY4MDAwLCJleHAiOjE5NTUxNDQwMDB9.test_signature_placeholder"
        elif secret_type == 'openrouter_api_key':
            # OpenRouter keys start with sk-or-v1-
            return "sk-or-v1-[REPLACE_WITH_ACTUAL_KEY]"
        elif secret_type == 'polygon_api_key':
            # Polygon keys are alphanumeric
            return "[REPLACE_WITH_ACTUAL_POLYGON_API_KEY]"
        elif secret_type == 'finnhub_api_key':
            # Finnhub keys are alphanumeric
            return "[REPLACE_WITH_ACTUAL_FINNHUB_API_KEY]"
        elif secret_type == 'alpaca_api_key':
            # Alpaca keys are alphanumeric
            return "[REPLACE_WITH_ACTUAL_ALPACA_API_KEY]"
        elif secret_type == 'jwt_secret':
            # JWT secrets are alphanumeric
            return "[REPLACE_WITH_ACTUAL_JWT_SECRET]"
        elif secret_type == 'redis_password':
            # Redis passwords can be any string
            return "[REPLACE_WITH_ACTUAL_REDIS_PASSWORD]"
        else:
            # Fallback to random hex for unknown types
            return secrets.token_hex(32)

    def generate_jwt_secret(self):
        """Generate a secure JWT secret."""
        return Fernet.generate_key().decode()

    def save_env_var(self, name, value):
        """Save an environment variable to .env file"""
        self.env_vars[name] = value
        self.save_env_file()

    def save_env_file(self):
        """Save all environment variables to .env file"""
        with open(self.env_file, 'w') as f:
            f.write("# Trading Automation - Environment Configuration\n")
            f.write("# Generated by manage_secrets.py\n\n")
            
            for key, value in self.env_vars.items():
                f.write(f"{key}={value}\n")
        
        print(f"Environment variables saved to {self.env_file}")

    def rotate_secret(self, name):
        """Rotate a specific secret."""
        if name == 'jwt_secret':
            secret = self.generate_jwt_secret()
        else:
            secret = self.generate_secret(name)
        
        self.save_env_var(name, secret)
        print(f"Secret '{name}' rotated and saved to {self.env_file}")

    def generate_all_secrets(self):
        """Generate all required environment variables"""
        secrets_to_generate = [
            'SUPABASE_KEY',
            'DISCORD_BOT_TOKEN', 
            'OPENROUTER_API_KEY',
            'POLYGON_API_KEY',
            'FINNHUB_API_KEY',
            'ALPACA_API_KEY',
            'JWT_SECRET',
            'REDIS_PASSWORD'
        ]
        
        for secret_name in secrets_to_generate:
            try:
                secret = self.generate_secret(secret_name.lower())
                self.save_env_var(secret_name, secret)
                print(f"Secret '{secret_name}' generated and saved to {self.env_file}")
            except Exception as e:
                print(f"Failed to generate secret '{secret_name}': {e}")
        
        print("All environment variables generated successfully!")

def main():
    parser = argparse.ArgumentParser(description='Trading Bot Environment Variables Management')
    parser.add_argument('action', choices=['generate', 'rotate'], 
                        help='Action to perform on environment variables')
    parser.add_argument('--secret', help='Specific secret to rotate')
    
    args = parser.parse_args()

    manager = EnvManager()

    if args.action == 'generate':
        if args.secret:
            secret = manager.generate_secret(args.secret.lower())
            manager.save_env_var(args.secret.upper(), secret)
        else:
            manager.generate_all_secrets()
    elif args.action == 'rotate':
        if args.secret:
            manager.rotate_secret(args.secret.lower())
        else:
            print("Please specify a secret to rotate with --secret")

if __name__ == "__main__":
    main() 