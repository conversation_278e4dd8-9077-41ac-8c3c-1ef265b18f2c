#!/usr/bin/env python3
"""
Security Testing Script for TradingView Automation
"""
import subprocess
import sys
import os
from pathlib import Path
import logging
from typing import Dict, List, Tuple

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecurityTester:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.security_issues = []
    
    def run_bandit(self) -> bool:
        """Run Bandit security linting"""
        logger.info("🔍 Running Bandit security analysis...")
        try:
            result = subprocess.run([
                'bandit', '-r', 'src/',
                '-c', '.bandit',
                '-f', 'json',
                '-o', 'security_reports/bandit_report.json'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                logger.info("✅ Bandit: No critical security issues found")
                return True
            else:
                logger.warning("⚠️ Bandit: Security issues detected")
                self.security_issues.append(("Bandit", result.stdout))
                return False
        except FileNotFoundError:
            logger.error("❌ Bandit not installed. Run: pip install bandit")
            return False
    
    def run_safety(self) -> bool:
        """Run Safety dependency vulnerability check"""
        logger.info("🔍 Running Safety dependency vulnerability scan...")
        try:
            result = subprocess.run([
                'safety', 'check',
                '--output', 'security_reports/safety_report.json'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                logger.info("✅ Safety: No known vulnerabilities in dependencies")
                return True
            else:
                logger.warning("⚠️ Safety: Vulnerabilities found in dependencies")
                self.security_issues.append(("Safety", result.stdout))
                return False
        except FileNotFoundError:
            logger.error("❌ Safety not installed. Run: pip install safety")
            return False
    
    def run_semgrep(self) -> bool:
        """Run Semgrep security pattern matching"""
        logger.info("🔍 Running Semgrep security pattern analysis...")
        try:
            result = subprocess.run([
                'semgrep', '--config', 'auto',
                '--output', 'security_reports/semgrep_report.json',
                '--json',
                'src/'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                logger.info("✅ Semgrep: No security pattern violations found")
                return True
            else:
                logger.warning("⚠️ Semgrep: Security pattern violations detected")
                self.security_issues.append(("Semgrep", result.stdout))
                return False
        except FileNotFoundError:
            logger.error("❌ Semgrep not installed. Run: pip install semgrep")
            return False
    
    def run_pip_audit(self) -> bool:
        """Run pip-audit for dependency vulnerabilities"""
        logger.info("🔍 Running pip-audit dependency analysis...")
        try:
            result = subprocess.run([
                'pip-audit',
                '--format', 'json',
                '--output', 'security_reports/pip_audit_report.json'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                logger.info("✅ pip-audit: No dependency vulnerabilities found")
                return True
            else:
                logger.warning("⚠️ pip-audit: Dependency vulnerabilities detected")
                self.security_issues.append(("pip-audit", result.stdout))
                return False
        except FileNotFoundError:
            logger.error("❌ pip-audit not installed. Run: pip install pip-audit")
            return False
    
    def check_sensitive_files(self) -> bool:
        """Check for sensitive files that shouldn't be committed"""
        logger.info("🔍 Checking for sensitive files...")
        
        sensitive_patterns = [
            '.env',
            'secrets.json',
            'config.ini',
            '*.pem',
            '*.key',
            '*.p12',
            'password*',
            '*secret*'
        ]
        
        issues = []
        for pattern in sensitive_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    issues.append(f"Found potentially sensitive file: {file_path}")
        
        if issues:
            logger.warning("⚠️ Sensitive files check: Issues found")
            self.security_issues.append(("Sensitive Files", "\n".join(issues)))
            return False
        else:
            logger.info("✅ Sensitive files check: No issues found")
            return True
    
    def check_dependencies(self) -> bool:
        """Check for outdated or vulnerable dependencies"""
        logger.info("🔍 Checking dependencies...")
        try:
            result = subprocess.run([
                'pip', 'list', '--outdated', '--format=json'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                logger.info("✅ Dependencies check: Completed")
                return True
            else:
                logger.warning("⚠️ Dependencies check: Issues found")
                return False
        except Exception as e:
            logger.error(f"❌ Dependencies check failed: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all security tests"""
        # Create security reports directory
        reports_dir = self.project_root / "security_reports"
        reports_dir.mkdir(exist_ok=True)
        
        logger.info("🚀 Starting comprehensive security testing...")
        
        tests = {
            "bandit": self.run_bandit,
            "safety": self.run_safety,
            "semgrep": self.run_semgrep,
            "pip_audit": self.run_pip_audit,
            "sensitive_files": self.check_sensitive_files,
            "dependencies": self.check_dependencies
        }
        
        results = {}
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests.items():
            logger.info(f"\n{'='*50}")
            logger.info(f"Running {test_name.upper()} security test")
            logger.info(f"{'='*50}")
            
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
                results[test_name] = False
        
        # Summary
        logger.info(f"\n{'='*50}")
        logger.info("SECURITY TESTING SUMMARY")
        logger.info(f"{'='*50}")
        logger.info(f"Tests Passed: {passed}/{total}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if self.security_issues:
            logger.warning(f"\n⚠️ SECURITY ISSUES FOUND:")
            for tool, issues in self.security_issues:
                logger.warning(f"\n{tool} Issues:")
                logger.warning(issues[:1000] + "..." if len(issues) > 1000 else issues)
        
        return results
    
    def generate_report(self) -> None:
        """Generate security testing report"""
        report_path = self.project_root / "security_reports" / "security_summary.md"
        
        with open(report_path, 'w') as f:
            f.write("# Security Testing Report\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            if self.security_issues:
                f.write("## Issues Found\n\n")
                for tool, issues in self.security_issues:
                    f.write(f"### {tool}\n")
                    f.write(f"```\n{issues[:2000]}\n```\n\n")
            else:
                f.write("## ✅ No Security Issues Found\n\n")
                f.write("All security tests passed successfully!\n\n")
            
            f.write("## Recommendations\n\n")
            f.write("1. Run security tests regularly (weekly/monthly)\n")
            f.write("2. Keep dependencies updated\n")
            f.write("3. Review security reports in security_reports/ directory\n")
            f.write("4. Address any high-severity issues immediately\n")
            f.write("5. Use tools like Dependabot for automated dependency updates\n")

def main():
    tester = SecurityTester()
    results = tester.run_all_tests()
    tester.generate_report()
    
    # Exit with appropriate code
    if all(results.values()):
        logger.info("🎉 All security tests passed!")
        sys.exit(0)
    else:
        logger.error("❌ Security issues found. Check security_reports/ directory")
        sys.exit(1)

if __name__ == "__main__":
    main()
