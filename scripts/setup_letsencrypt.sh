#!/bin/bash
# Setup Let's Encrypt certificates for tradingview-automation
# This script should be run on the host machine, not inside a container

# Exit on error
set -e

# Configuration
DOMAIN=${1:-"example.com"}
EMAIL=${2:-"<EMAIL>"}
STAGING=${3:-"1"}  # Use 1 for testing, 0 for production

# Check if domain is provided
if [ "$DOMAIN" == "example.com" ]; then
    echo "ERROR: Please provide a valid domain name as the first argument"
    echo "Usage: $0 yourdomain.com <EMAIL> [0|1]"
    echo "  - First argument: Domain name"
    echo "  - Second argument: Email address for Let's Encrypt notifications"
    echo "  - Third argument: Use staging environment (1 for testing, 0 for production)"
    exit 1
fi

# Check if certbot is installed
if ! command -v certbot &> /dev/null; then
    echo "Installing certbot..."
    apt-get update
    apt-get install -y certbot
fi

# Create directory for certificates if it doesn't exist
CERT_DIR="/home/<USER>/Desktop/tradingview-automatio/nginx/ssl"
mkdir -p "$CERT_DIR"

# Backup existing certificates
if [ -f "$CERT_DIR/cert.pem" ]; then
    echo "Backing up existing certificates..."
    BACKUP_DIR="$CERT_DIR/backup_$(date +%Y%m%d%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    cp "$CERT_DIR/cert.pem" "$BACKUP_DIR/"
    cp "$CERT_DIR/key.pem" "$BACKUP_DIR/"
fi

# Generate certificates using standalone mode
echo "Generating Let's Encrypt certificates for $DOMAIN..."

# Determine if we should use staging environment
if [ "$STAGING" == "1" ]; then
    echo "Using Let's Encrypt staging environment for testing"
    STAGING_ARG="--test-cert"
else
    echo "Using Let's Encrypt production environment"
    STAGING_ARG=""
fi

# Stop nginx if it's running to free up port 80
if docker ps | grep -q tradingview-nginx; then
    echo "Stopping nginx container to free up port 80..."
    docker stop tradingview-nginx || true
fi

# Generate certificates
certbot certonly \
    --standalone \
    --non-interactive \
    --agree-tos \
    --email "$EMAIL" \
    --domains "$DOMAIN" \
    $STAGING_ARG \
    --cert-name tradingview-cert

# Copy certificates to nginx ssl directory
echo "Copying certificates to nginx ssl directory..."
cp "/etc/letsencrypt/live/tradingview-cert/fullchain.pem" "$CERT_DIR/cert.pem"
cp "/etc/letsencrypt/live/tradingview-cert/privkey.pem" "$CERT_DIR/key.pem"

# Set proper permissions
chmod 644 "$CERT_DIR/cert.pem"
chmod 600 "$CERT_DIR/key.pem"

# Update nginx configuration
echo "Updating nginx configuration..."
sed -i 's/# SSL configuration (self-signed for development)/# SSL configuration (Let'\''s Encrypt)/' /home/<USER>/Desktop/tradingview-automatio/nginx/nginx.conf

# Create renewal hook
RENEWAL_HOOK_DIR="/etc/letsencrypt/renewal-hooks/deploy"
mkdir -p "$RENEWAL_HOOK_DIR"

cat > "$RENEWAL_HOOK_DIR/tradingview-cert-copy.sh" << EOF
#!/bin/bash
# Copy renewed certificates to nginx ssl directory
cp "/etc/letsencrypt/live/tradingview-cert/fullchain.pem" "$CERT_DIR/cert.pem"
cp "/etc/letsencrypt/live/tradingview-cert/privkey.pem" "$CERT_DIR/key.pem"
chmod 644 "$CERT_DIR/cert.pem"
chmod 600 "$CERT_DIR/key.pem"
# Restart nginx container if it's running
if docker ps -a | grep -q tradingview-nginx; then
    docker restart tradingview-nginx
fi
EOF

chmod +x "$RENEWAL_HOOK_DIR/tradingview-cert-copy.sh"

# Create a cron job for certificate renewal
echo "Setting up automatic renewal..."
(crontab -l 2>/dev/null || echo "") | grep -v "certbot renew" | { cat; echo "0 3 * * * certbot renew --quiet"; } | crontab -

echo "Let's Encrypt certificates have been set up successfully!"
echo "Certificates will be automatically renewed before they expire."
echo "You can now restart your nginx container:"
echo "  docker start tradingview-nginx"
