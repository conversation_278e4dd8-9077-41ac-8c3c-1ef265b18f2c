#!/usr/bin/env python3
"""
Security Cleanup Script for TradingView Automation
Identifies and fixes hardcoded values, duplicate files, and security issues
"""

import os
import re
import shutil
from pathlib import Path
from typing import List, Dict, Set, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SecurityCleanup:
    """Security cleanup and hardcoded value replacement"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.src_dir = self.project_root / "src"
        self.scripts_dir = self.project_root / "scripts"
        
        # Patterns to find hardcoded values
        self.hardcoded_patterns = {
            'localhost': [
                r'localhost:\d+',
                r'127\.0\.0\.1:\d+',
                r'localhost',
                r'127\.0\.0\.1'
            ],
            'ports': [
                r':8000',
                r':8001', 
                r':6379',
                r':5432',
                r':3000'
            ],
            'sqlite': [
                r'sqlite:///\./',
                r'sqlite:///',
                r'\.db'
            ],
            'redis_localhost': [
                r'redis://localhost',
                r'redis://127\.0\.0\.1'
            ]
        }
        
        # Environment variable replacements
        self.env_replacements = {
            'localhost:3000': '${FRONTEND_URL}',
            '127.0.0.1:3000': '${FRONTEND_URL}',
            'localhost:8001': '${WEBHOOK_ORIGIN}',
            'localhost:6379': '${REDIS_HOST}:${REDIS_PORT}',
            'localhost:5432': '${POSTGRES_HOST}:${POSTGRES_PORT}',
            'sqlite:///./local_dev.db': '${DATABASE_URL}',
            'redis://localhost:6379/0': '${REDIS_URL}',
            'redis://localhost': '${REDIS_URL}'
        }
    
    def scan_hardcoded_values(self) -> Dict[str, List[Tuple[str, int, str]]]:
        """Scan for hardcoded values in Python files"""
        logger.info("🔍 Scanning for hardcoded values...")
        
        hardcoded_found = {}
        
        for pattern_name, patterns in self.hardcoded_patterns.items():
            hardcoded_found[pattern_name] = []
            
            for py_file in self.src_dir.rglob("*.py"):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        
                        for line_num, line in enumerate(lines, 1):
                            for pattern in patterns:
                                if re.search(pattern, line):
                                    hardcoded_found[pattern_name].append((
                                        str(py_file.relative_to(self.project_root)),
                                        line_num,
                                        line.strip()
                                    ))
                                    break
                except Exception as e:
                    logger.warning(f"Error reading {py_file}: {e}")
        
        return hardcoded_found
    
    def find_duplicate_files(self) -> Dict[str, List[str]]:
        """Find duplicate file names across the codebase"""
        logger.info("🔍 Scanning for duplicate files...")
        
        file_counts = {}
        duplicates = {}
        
        for py_file in self.src_dir.rglob("*.py"):
            filename = py_file.name
            if filename not in file_counts:
                file_counts[filename] = []
            file_counts[filename].append(str(py_file.relative_to(self.project_root)))
        
        for filename, paths in file_counts.items():
            if len(paths) > 1:
                duplicates[filename] = paths
        
        return duplicates
    
    def generate_cleanup_report(self) -> str:
        """Generate comprehensive cleanup report"""
        logger.info("📊 Generating cleanup report...")
        
        hardcoded = self.scan_hardcoded_values()
        duplicates = self.find_duplicate_files()
        
        report = []
        report.append("🔒 SECURITY CLEANUP REPORT")
        report.append("=" * 60)
        report.append("")
        
        # Hardcoded values summary
        total_hardcoded = sum(len(items) for items in hardcoded.values())
        report.append(f"🚨 HARDCODED VALUES FOUND: {total_hardcoded}")
        report.append("-" * 40)
        
        for pattern_name, items in hardcoded.items():
            if items:
                report.append(f"\n{pattern_name.upper()}: {len(items)} occurrences")
                for file_path, line_num, line in items[:5]:  # Show first 5
                    report.append(f"  {file_path}:{line_num} -> {line}")
                if len(items) > 5:
                    report.append(f"  ... and {len(items) - 5} more")
        
        # Duplicate files summary
        total_duplicates = sum(len(paths) - 1 for paths in duplicates.values())
        report.append(f"\n📁 DUPLICATE FILES: {total_duplicates} duplicates")
        report.append("-" * 40)
        
        for filename, paths in duplicates.items():
            report.append(f"\n{filename}: {len(paths)} copies")
            for path in paths:
                report.append(f"  - {path}")
        
        # Recommendations
        report.append(f"\n🎯 CLEANUP PRIORITIES:")
        report.append("-" * 40)
        report.append(f"1. Replace {total_hardcoded} hardcoded values with environment variables")
        report.append(f"2. Consolidate {len(duplicates)} sets of duplicate files")
        report.append("3. Update docker-compose.yml to use .env.secure")
        report.append("4. Test all services after cleanup")
        
        return "\n".join(report)
    
    def create_cleanup_script(self) -> str:
        """Create a script to automatically fix common issues"""
        script_content = """#!/bin/bash
# Auto-generated cleanup script for TradingView Automation
# Run this script to fix common security issues

echo "🔒 Starting security cleanup..."

# 1. Replace hardcoded localhost values
echo "📝 Replacing hardcoded localhost values..."
find src/ -name "*.py" -exec sed -i 's/localhost:3000/\${FRONTEND_URL}/g' {} \\;
find src/ -name "*.py" -exec sed -i 's/localhost:8001/\${WEBHOOK_ORIGIN}/g' {} \\;
find src/ -name "*.py" -exec sed -i 's/localhost:6379/\${REDIS_HOST}:\${REDIS_PORT}/g' {} \\;
find src/ -name "*.py" -exec sed -i 's/localhost:5432/\${POSTGRES_HOST}:\${POSTGRES_PORT}/g' {} \\;

# 2. Replace hardcoded database URLs
echo "🗄️ Replacing hardcoded database URLs..."
find src/ -name "*.py" -exec sed -i 's|sqlite:///./local_dev.db|\${DATABASE_URL}|g' {} \\;
find src/ -name "*.py" -exec sed -i 's|redis://localhost:6379/0|\${REDIS_URL}|g' {} \\;

# 3. Update environment file usage
echo "🔧 Updating environment configuration..."
if [ ! -f .env.secure ]; then
    echo "❌ .env.secure not found. Please create it first."
    exit 1
fi

# 4. Restart services with new configuration
echo "🚀 Restarting services with secure configuration..."
docker-compose down
docker-compose up -d

echo "✅ Security cleanup complete!"
echo "🔍 Please review the changes and test all functionality."
"""
        
        script_path = self.scripts_dir / "auto_cleanup.sh"
        script_path.parent.mkdir(exist_ok=True)
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(script_path, 0o755)
        
        return str(script_path)
    
    def run_cleanup(self) -> None:
        """Run the complete cleanup process"""
        logger.info("🚀 Starting security cleanup process...")
        
        # Generate report
        report = self.generate_cleanup_report()
        print(report)
        
        # Save report to file
        report_path = self.project_root / "SECURITY_CLEANUP_REPORT.md"
        with open(report_path, 'w') as f:
            f.write(report)
        
        logger.info(f"📄 Report saved to: {report_path}")
        
        # Create cleanup script
        script_path = self.create_cleanup_script()
        logger.info(f"🔧 Cleanup script created: {script_path}")
        
        logger.info("✅ Cleanup process complete!")

def main():
    """Main execution function"""
    cleanup = SecurityCleanup()
    cleanup.run_cleanup()

if __name__ == "__main__":
    main() 