#!/usr/bin/env python3
"""
Test runner for Critical Fallback Value Remediation
Run this script to validate that all dangerous fallback values have been properly addressed.
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

# Load environment variables from .env.secure
def load_env_file(env_file_path):
    """Load environment variables from .env file"""
    if os.path.exists(env_file_path):
        with open(env_file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

# Load .env.secure file
env_file = Path(__file__).parent.parent / '.env.secure'
load_env_file(env_file)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_environment_variables():
    """Test that critical environment variables are set"""
    logger.info("Testing environment variables...")
    
    critical_vars = [
        'RISK_PER_TRADE',
        'MAX_POSITION_SIZE',
        'STOP_LOSS_MULTIPLIER',
        'TAKE_PROFIT_MULTIPLIER',
        'MAX_OPEN_POSITIONS',
        'MINIMUM_VOLUME_THRESHOLD',
        'PRICE_CHANGE_THRESHOLD',
        'SUPPORT_RESISTANCE_PERCENTAGE',
        'CONFIDENCE_MIN_THRESHOLD',
        'CONFIDENCE_MAX_THRESHOLD',
        'DATA_QUALITY_MIN_THRESHOLD',
        'ACTION_THRESHOLD_BASE'
    ]
    
    missing_vars = []
    for var in critical_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"✓ {var} = {value}")
        else:
            logger.warning(f"✗ {var} not set")
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"Missing {len(missing_vars)} critical environment variables")
        return False
    
    logger.info("✓ All critical environment variables are set")
    return True

def test_config_manager():
    """Test that config manager uses environment variables"""
    logger.info("Testing configuration manager...")
    
    try:
        from core.config_manager import TradingStrategyConfig
        
        # Test with environment variables
        config = TradingStrategyConfig()
        
        # Check that values are loaded from environment
        risk_per_trade = config.risk_per_trade
        max_position_size = config.max_position_size
        
        logger.info(f"✓ Risk per trade: {risk_per_trade}")
        logger.info(f"✓ Max position size: {max_position_size}")
        
        # Validate ranges
        if 0.001 <= risk_per_trade <= 0.1:
            logger.info("✓ Risk per trade within valid range")
        else:
            logger.error(f"✗ Risk per trade {risk_per_trade} outside valid range [0.001, 0.1]")
            return False
        
        if 0.01 <= max_position_size <= 0.5:
            logger.info("✓ Max position size within valid range")
        else:
            logger.error(f"✗ Max position size {max_position_size} outside valid range [0.01, 0.5]")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Configuration manager test failed: {e}")
        return False

def test_data_quality_validator():
    """Test data quality validation system"""
    logger.info("Testing data quality validator...")
    
    try:
        from core.data_quality_validator import DataQualityValidator, ActionValidator
        
        # Test confidence calculation
        confidence = DataQualityValidator.calculate_confidence(
            data_quality=80,
            completeness=0.9,
            technical_signals={'rsi': {'confirmed': True}}
        )
        
        if confidence > 0:
            logger.info(f"✓ Confidence calculation working: {confidence}")
        else:
            logger.error("✗ Confidence calculation failed")
            return False
        
        # Test action determination
        action = ActionValidator.determine_action(
            change=5.0,
            technical_signals={'rsi': {'confirmed': True}},
            data_quality=80,
            volatility=0.02
        )
        
        if action in ['BUY', 'SELL', 'HOLD']:
            logger.info(f"✓ Action determination working: {action}")
        else:
            logger.error(f"✗ Action determination failed: {action}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Data quality validator test failed: {e}")
        return False

def test_config_validator():
    """Test configuration validation system"""
    logger.info("Testing configuration validator...")
    
    try:
        from core.config_validator import ConfigValidator
        
        # Test environment variable validation
        all_set, missing = ConfigValidator.validate_environment_variables()
        
        if all_set:
            logger.info("✓ Environment variable validation working")
        else:
            logger.warning(f"⚠ Some environment variables missing: {missing}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Configuration validator test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("=" * 60)
    logger.info("CRITICAL FALLBACK VALUE REMEDIATION VALIDATION")
    logger.info("=" * 60)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Configuration Manager", test_config_manager),
        ("Data Quality Validator", test_data_quality_validator),
        ("Configuration Validator", test_config_validator)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\nRunning {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED - Fallback remediation successful!")
        return 0
    else:
        logger.error("❌ SOME TESTS FAILED - Review fallback remediation")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 