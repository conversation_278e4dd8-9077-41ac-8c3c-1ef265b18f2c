import asyncio
import json
import logging
import pandas as pd
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.technical_analysis.zones import supply_demand_detector
from src.shared.market_analysis.unified_signal_analyzer import unified_signal_analyzer

logging.basicConfig(level=logging.INFO)

async def main():
    agg = DataProviderAggregator()
    print('Fetching history for AAPL...')
    res = await agg.get_history('AAPL', period='3mo', interval='1d')

    # Use the unified signal analyzer to normalize the data
    df = unified_signal_analyzer._normalize_provider_data_to_df(res)
    
    if df.empty:
        print('No historical data returned for AAPL')
        return

    if len(df) < 20:
        print(f'Insufficient data points after normalization: {len(df)}')
        return

    zones = supply_demand_detector.detect_zones(df, symbol='AAPL')
    print(json.dumps(zones, default=str, indent=2))

asyncio.run(main())
