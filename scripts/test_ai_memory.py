import asyncio
import os
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import openai
import logging
from dotenv import load_dotenv
import hashlib
import time
from pathlib import Path

# Load environment variables
load_dotenv()

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"

class ModelProvider(Enum):
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"

@dataclass
class Message:
    role: str
    content: str
    timestamp: datetime
    token_count: Optional[int] = None
    model_used: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat(),
            'token_count': self.token_count,
            'model_used': self.model_used
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        return cls(
            role=data['role'],
            content=data['content'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            token_count=data.get('token_count'),
            model_used=data.get('model_used')
        )

@dataclass
class ConversationConfig:
    max_history_length: int = 20
    max_context_tokens: int = 4000
    temperature: float = 0.7
    max_tokens: int = 1000
    model: str = 'deepseek/deepseek-v3.1-base'
    provider: ModelProvider = ModelProvider.OPENROUTER
    system_prompt: str = '''You are an advanced AI trading assistant with deep knowledge of financial markets, trading strategies, and risk management. 
    
Key capabilities:
- Provide contextual, nuanced financial advice
- Maintain conversation continuity and build upon previous exchanges  
- Offer both beginner-friendly and advanced trading insights
- Focus on risk management and responsible trading practices
- Stay current with market trends and analysis techniques
    
Always prioritize risk management and remind users that trading involves significant risks.'''

class ConversationManager:
    def __init__(self, config: ConversationConfig):
        self.config = config
        self.messages: List[Message] = []
        self.session_id = self._generate_session_id()
        self.total_tokens_used = 0
        self.conversation_start = datetime.now()
        
    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        timestamp = str(int(time.time()))
        return hashlib.md5(timestamp.encode()).hexdigest()[:8]
    
    def add_message(self, role: str, content: str, token_count: Optional[int] = None, model: Optional[str] = None):
        """Add message to conversation history"""
        message = Message(
            role=role,
            content=content,
            timestamp=datetime.now(),
            token_count=token_count,
            model_used=model
        )
        self.messages.append(message)
        
        if token_count:
            self.total_tokens_used += token_count
        
        # Trim history if needed
        self._trim_history()
    
    def _trim_history(self):
        """Trim conversation history based on length and token limits"""
        if len(self.messages) > self.config.max_history_length:
            # Keep system message and trim from the middle, preserving recent context
            system_messages = [msg for msg in self.messages if msg.role == 'system']
            user_assistant_messages = [msg for msg in self.messages if msg.role != 'system']
            
            if len(user_assistant_messages) > self.config.max_history_length - len(system_messages):
                # Keep the most recent messages
                keep_count = self.config.max_history_length - len(system_messages)
                user_assistant_messages = user_assistant_messages[-keep_count:]
            
            self.messages = system_messages + user_assistant_messages
    
    def get_context_messages(self) -> List[Dict[str, str]]:
        """Get messages formatted for API calls"""
        messages = []
        
        # Add system message
        messages.append({
            'role': 'system',
            'content': self.config.system_prompt
        })
        
        # Add conversation history
        for msg in self.messages[-self.config.max_history_length:]:
            if msg.role != 'system':  # Avoid duplicate system messages
                messages.append({
                    'role': msg.role,
                    'content': msg.content
                })
        
        return messages
    
    def reset(self):
        """Reset conversation history"""
        self.messages.clear()
        self.total_tokens_used = 0
        self.conversation_start = datetime.now()
        self.session_id = self._generate_session_id()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get conversation statistics"""
        return {
            'session_id': self.session_id,
            'message_count': len(self.messages),
            'total_tokens': self.total_tokens_used,
            'conversation_duration': str(datetime.now() - self.conversation_start),
            'last_activity': self.messages[-1].timestamp.isoformat() if self.messages else None
        }

class AIAssistant:
    def __init__(self, config: Optional[ConversationConfig] = None):
        self.config = config or ConversationConfig()
        self.conversation_manager = ConversationManager(self.config)
        self.logger = self._setup_logger()
        self.client = self._setup_client()
        self.rate_limit_tracker = {}
        
    def _setup_logger(self) -> logging.Logger:
        """Configure enhanced logging"""
        log_level = os.getenv('LOG_LEVEL', 'INFO')
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # Create a custom formatter that can handle session_id
        class SessionFormatter(logging.Formatter):
            def format(self, record):
                if hasattr(record, 'session_id'):
                    record.session_id = record.session_id
                else:
                    record.session_id = 'unknown'
                return super().format(record)
        
        # Configure logging
        logging.basicConfig(
            level=numeric_level,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('ai_assistant.log')
            ]
        )
        
        logger = logging.getLogger(__name__)
        
        # Set custom formatter
        formatter = SessionFormatter('%(asctime)s - %(name)s - %(levelname)s - [Session: %(session_id)s] - %(message)s')
        for handler in logger.handlers:
            handler.setFormatter(formatter)
        
        # Create logger adapter with session_id
        logger = logging.LoggerAdapter(logger, {'session_id': self.conversation_manager.session_id})
        return logger
    
    def _setup_client(self) -> openai.OpenAI:
        """Setup API client based on provider"""
        api_key = os.getenv('OPENROUTER_API_KEY', '')
        
        if not api_key or api_key == 'your_openrouter_api_key_here':
            raise ValueError('API key is not configured. Please set OPENROUTER_API_KEY in your .env file')
        
        if self.config.provider == ModelProvider.OPENROUTER:
            return openai.OpenAI(
                base_url='https://openrouter.ai/api/v1',
                api_key=api_key
            )
        else:
            # Can be extended for other providers
            return openai.OpenAI(api_key=api_key)
    
    def _check_rate_limit(self) -> bool:
        """Simple rate limiting check"""
        now = time.time()
        minute_ago = now - 60
        
        # Clean old requests
        self.rate_limit_tracker = {
            timestamp: count for timestamp, count in self.rate_limit_tracker.items()
            if timestamp > minute_ago
        }
        
        # Count requests in the last minute
        current_requests = sum(self.rate_limit_tracker.values())
        max_requests_per_minute = int(os.getenv('MAX_REQUESTS_PER_MINUTE', '20'))
        
        if current_requests >= max_requests_per_minute:
            return False
        
        # Track this request
        current_minute = int(now // 60) * 60
        self.rate_limit_tracker[current_minute] = self.rate_limit_tracker.get(current_minute, 0) + 1
        return True
    
    async def generate_response(self, question: str, **kwargs) -> str:
        """Generate AI response with enhanced error handling and features"""
        try:
            # Rate limiting check
            if not self._check_rate_limit():
                return "⏱️ Rate limit exceeded. Please wait a moment before asking another question."
            
            # Override config with kwargs
            temperature = kwargs.get('temperature', self.config.temperature)
            max_tokens = kwargs.get('max_tokens', self.config.max_tokens)
            model = kwargs.get('model', self.config.model)
            
            self.logger.info(f"Generating response for query: {question[:100]}...")
            
            # Prepare messages
            messages = self.conversation_manager.get_context_messages()
            messages.append({'role': 'user', 'content': question})
            
            # Make API call with retry logic
            response = await self._make_api_call_with_retry(
                messages=messages,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # Extract response
            ai_response = response.choices[0].message.content.strip()
            
            # Track token usage
            total_tokens = getattr(response, 'usage', None)
            if total_tokens and hasattr(total_tokens, 'total_tokens'):
                total_tokens = total_tokens.total_tokens
            else:
                total_tokens = 0
            
            # Update conversation history
            self.conversation_manager.add_message('user', question, model=model)
            self.conversation_manager.add_message('assistant', ai_response, total_tokens, model)
            
            self.logger.info(f"Response generated successfully. Tokens used: {total_tokens}")
            return ai_response
            
        except Exception as e:
            error_msg = f"AI Error: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # Return user-friendly error based on error type
            if "rate limit" in str(e).lower():
                return "⏱️ Rate limit exceeded. Please wait a few moments before trying again."
            elif "invalid api key" in str(e).lower():
                return "🔑 API key issue. Please check your configuration."
            elif "model not found" in str(e).lower():
                return f"🤖 Model '{model}' not available. Please try a different model."
            else:
                return f"❌ {error_msg}"
    
    async def _make_api_call_with_retry(self, messages: List[Dict], model: str, temperature: float, max_tokens: int, max_retries: int = 3):
        """Make API call with exponential backoff retry"""
        for attempt in range(max_retries):
            try:
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.client.chat.completions.create(
                        model=model,
                        messages=messages,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        timeout=30
                    )
                )
                return response
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                
                wait_time = (2 ** attempt) + (time.time() % 1)  # Exponential backoff with jitter
                self.logger.warning(f"API call failed (attempt {attempt + 1}), retrying in {wait_time:.2f}s: {e}")
                await asyncio.sleep(wait_time)
    
    def reset_conversation(self):
        """Reset conversation history"""
        self.conversation_manager.reset()
        self.logger.info("Conversation history reset")
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get conversation statistics"""
        return self.conversation_manager.get_stats()
    
    def export_conversation(self) -> str:
        """Export the conversation history to a JSON file."""
        conversation_data = {
            "messages": self.messages,
            "stats": {
                "total_messages": len(self.messages),
                "total_tokens": sum(msg.get("token_count", 0) for msg in self.messages)
            }
        }

        # Ensure ModelProvider objects are not included
        if "model_provider" in conversation_data:
            conversation_data.pop("model_provider")  # Remove if present

        # Convert any unserializable objects to strings
        for key, value in conversation_data.items():
            if isinstance(value, object) and not isinstance(value, (str, int, float, bool, list, dict, tuple)):
                conversation_data[key] = str(value)

        export_path = os.path.join("conversations", f"conversation_{self.conversation_manager.session_id}.json")  
        os.makedirs("conversations", exist_ok=True)

        with open(export_path, "w", encoding="utf-8") as f:
            json.dump(conversation_data, f, indent=2, ensure_ascii=False)

        return export_path
    
    def load_conversation(self, filepath: str):
        """Load conversation from JSON file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Load messages
            self.conversation_manager.messages = [
                Message.from_dict(msg_data) for msg_data in data['messages']
            ]
            
            self.logger.info(f"Conversation loaded from {filepath}")
        except Exception as e:
            self.logger.error(f"Failed to load conversation: {e}")
            raise

async def test_enhanced_conversation():
    """Test the enhanced AI assistant"""
    # Initialize with custom config
    config = ConversationConfig(
        max_history_length=15,
        temperature=0.8,
        max_tokens=800,
        system_prompt="""You are a professional trading mentor with 20+ years of experience. 
        Provide practical, actionable advice while emphasizing risk management."""
    )
    
    assistant = AIAssistant(config)
    
    # Test conversation
    queries = [
        "I'm a complete beginner interested in stock trading. Where should I start?",
        "What's the most important thing about risk management you mentioned?",
        "Can you give me a specific example of how to apply that risk management principle?",
        "What tools or platforms would you recommend for a beginner?",
        "How much money should I start with?"
    ]
    
    print("🚀 Enhanced AI Trading Assistant Test\n" + "="*60)
    
    for i, query in enumerate(queries, 1):
        print(f"\n📝 Query {i}: {query}")
        print("-" * 50)
        
        response = await assistant.generate_response(query)
        print(f"🤖 AI Response:\n{response}")
        
        # Show stats every few queries
        if i % 2 == 0:
            stats = assistant.get_conversation_stats()
            print(f"\n📊 Stats: {stats['message_count']} messages, {stats['total_tokens']} tokens")
    
    # Export conversation
    export_path = assistant.export_conversation()
    print(f"\n💾 Conversation exported to: {export_path}")
    
    # Show final stats
    final_stats = assistant.get_conversation_stats()
    print(f"\n📈 Final Statistics:")
    for key, value in final_stats.items():
        print(f"  {key}: {value}")

if __name__ == '__main__':
    asyncio.run(test_enhanced_conversation())