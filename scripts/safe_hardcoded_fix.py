#!/usr/bin/env python3
"""
SAFE Hardcoded Value Fix Script
ONLY fixes hardcoded localhost/ports - does NOT touch legitimate duplicate file architecture
"""

import os
import re
from pathlib import Path
import logging
from typing import Dict, List, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SafeHardcodedFix:
    """Safely fix only hardcoded values without touching legitimate architecture"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.src_dir = self.project_root / "src"
        
        # ONLY fix these specific hardcoded patterns - don't touch legitimate duplicates
        self.safe_replacements = {
            # Frontend URLs
            r'localhost:3000': '${FRONTEND_URL}',
            r'127\.0\.0\.1:3000': '${FRONTEND_URL}',
            
            # Webhook URLs  
            r'localhost:8001': '${WEBHOOK_ORIGIN}',
            
            # Database URLs
            r'localhost:5432': '${POSTGRES_HOST}:${POSTGRES_PORT}',
            r'localhost:6379': '${REDIS_HOST}:${REDIS_PORT}',
            
            # Redis URLs
            r'redis://localhost:6379/0': '${REDIS_URL}',
            r'redis://localhost:6379': '${REDIS_URL}',
            r'redis://localhost': '${REDIS_URL}',
            
            # SQLite fallbacks (only if they're hardcoded)
            r'sqlite:///\./local_dev\.db': '${DATABASE_URL}',
            r'sqlite:///\./': '${DATABASE_URL}',
        }
        
        # Files to SKIP (legitimate duplicates that should NOT be touched)
        self.skip_files = {
            'src/core/config.py',           # Core config - legitimate
            'src/api/config.py',            # API config - legitimate  
            'src/database/config.py',       # Database config - legitimate
            'src/data/providers/config.py', # Provider config - legitimate
            'src/bot/pipeline/commands/ask/config.py', # Pipeline config - legitimate
        }
    
    def is_safe_to_modify(self, file_path: Path) -> bool:
        """Check if file is safe to modify (not a legitimate duplicate)"""
        relative_path = str(file_path.relative_to(self.project_root))
        return relative_path not in self.skip_files
    
    def fix_hardcoded_values(self) -> Dict[str, List[Tuple[str, int, str, str]]]:
        """Safely fix only hardcoded values in safe files"""
        logger.info("🔒 Starting SAFE hardcoded value fix...")
        logger.info("⚠️  Will NOT touch legitimate duplicate files")
        
        changes_made = {}
        total_fixes = 0
        
        for py_file in self.src_dir.rglob("*.py"):
            if not self.is_safe_to_modify(py_file):
                logger.info(f"⏭️  Skipping legitimate file: {py_file.relative_to(self.project_root)}")
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    original_content = content
                
                file_changes = []
                file_modified = False
                
                # Apply safe replacements
                for pattern, replacement in self.safe_replacements.items():
                    if re.search(pattern, content):
                        # Count occurrences
                        matches = len(re.findall(pattern, content))
                        if matches > 0:
                            # Replace the pattern
                            new_content = re.sub(pattern, replacement, content)
                            if new_content != content:
                                content = new_content
                                file_modified = True
                                file_changes.append((pattern, replacement, matches))
                
                # Save file if modified
                if file_modified:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    relative_path = str(py_file.relative_to(self.project_root))
                    changes_made[relative_path] = file_changes
                    total_fixes += sum(matches for _, _, matches in file_changes)
                    
                    logger.info(f"✅ Fixed {len(file_changes)} patterns in {relative_path}")
                
            except Exception as e:
                logger.warning(f"Error processing {py_file}: {e}")
        
        logger.info(f"🎯 Total safe fixes applied: {total_fixes}")
        return changes_made
    
    def generate_safe_report(self) -> str:
        """Generate report of safe changes made"""
        changes = self.fix_hardcoded_values()
        
        report = []
        report.append("🔒 SAFE HARDCODED VALUE FIX REPORT")
        report.append("=" * 60)
        report.append("")
        report.append("✅ ONLY hardcoded values were fixed")
        report.append("✅ NO legitimate duplicate files were touched")
        report.append("✅ Architecture integrity maintained")
        report.append("")
        
        if changes:
            report.append("📝 CHANGES MADE:")
            report.append("-" * 40)
            
            for file_path, file_changes in changes.items():
                report.append(f"\n{file_path}:")
                for pattern, replacement, count in file_changes:
                    report.append(f"  - {pattern} → {replacement} ({count} occurrences)")
        else:
            report.append("📝 No hardcoded values found to fix")
        
        report.append("")
        report.append("🛡️ FILES PROTECTED (legitimate duplicates):")
        report.append("-" * 40)
        for protected_file in self.skip_files:
            report.append(f"  ✅ {protected_file}")
        
        report.append("")
        report.append("🎯 NEXT STEPS:")
        report.append("-" * 40)
        report.append("1. Test the application to ensure it still works")
        report.append("2. Verify environment variables are set correctly")
        report.append("3. Check that no legitimate functionality was broken")
        
        return "\n".join(report)
    
    def run_safe_fix(self) -> None:
        """Run the safe hardcoded value fix"""
        logger.info("🚀 Starting SAFE hardcoded value fix process...")
        
        # Generate and display report
        report = self.generate_safe_report()
        print(report)
        
        # Save report to file
        report_path = self.project_root / "SAFE_HARDCODED_FIX_REPORT.md"
        with open(report_path, 'w') as f:
            f.write(report)
        
        logger.info(f"📄 Safe fix report saved to: {report_path}")
        logger.info("✅ Safe fix process complete!")

def main():
    """Main execution function"""
    fixer = SafeHardcodedFix()
    fixer.run_safe_fix()

if __name__ == "__main__":
    main() 