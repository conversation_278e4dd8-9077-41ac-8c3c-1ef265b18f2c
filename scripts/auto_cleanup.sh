#!/bin/bash
# Auto-generated cleanup script for TradingView Automation
# Run this script to fix common security issues

echo "🔒 Starting security cleanup..."

# 1. Replace hardcoded localhost values
echo "📝 Replacing hardcoded localhost values..."
find src/ -name "*.py" -exec sed -i 's/localhost:3000/\${FRONTEND_URL}/g' {} \;
find src/ -name "*.py" -exec sed -i 's/localhost:8001/\${WEBHOOK_ORIGIN}/g' {} \;
find src/ -name "*.py" -exec sed -i 's/localhost:6379/\${REDIS_HOST}:\${REDIS_PORT}/g' {} \;
find src/ -name "*.py" -exec sed -i 's/localhost:5432/\${POSTGRES_HOST}:\${POSTGRES_PORT}/g' {} \;

# 2. Replace hardcoded database URLs
echo "🗄️ Replacing hardcoded database URLs..."
find src/ -name "*.py" -exec sed -i 's|sqlite:///./local_dev.db|\${DATABASE_URL}|g' {} \;
find src/ -name "*.py" -exec sed -i 's|redis://localhost:6379/0|\${REDIS_URL}|g' {} \;

# 3. Update environment file usage
echo "🔧 Updating environment configuration..."
if [ ! -f .env.secure ]; then
    echo "❌ .env.secure not found. Please create it first."
    exit 1
fi

# 4. Restart services with new configuration
echo "🚀 Restarting services with secure configuration..."
docker-compose down
docker-compose up -d

echo "✅ Security cleanup complete!"
echo "🔍 Please review the changes and test all functionality."
