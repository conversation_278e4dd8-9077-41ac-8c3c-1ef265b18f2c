#!/usr/bin/env python3
import secrets
import base64
import argparse

def generate_secret_key(length=48):
    """
    Generate a cryptographically secure random secret key.
    
    Args:
        length (int): Length of the secret key in bytes
    
    Returns:
        str: Base64 encoded secret key
    """
    return base64.urlsafe_b64encode(secrets.token_bytes(length)).decode('utf-8')

def main():
    parser = argparse.ArgumentParser(description='Generate secure secret keys')
    parser.add_argument('--type', choices=['secret', 'jwt', 'webhook'], default='secret', 
                        help='Type of secret key to generate')
    parser.add_argument('--length', type=int, default=48, 
                        help='Length of the secret key in bytes')
    
    args = parser.parse_args()
    
    key = generate_secret_key(args.length)
    
    print(f"Generated {args.type.upper()} Secret Key:")
    print(key)
    print("\nSuggested .env entry:")
    if args.type == 'secret':
        print(f"SECRET_KEY={key}")
    elif args.type == 'jwt':
        print(f"JWT_SECRET={key}")
    elif args.type == 'webhook':
        print(f"WEBHOOK_SECRET={key}")

if __name__ == '__main__':
    main()
