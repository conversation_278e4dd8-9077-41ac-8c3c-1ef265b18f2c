#!/usr/bin/env python3
"""
Generate Deprecation Report

This script generates a report on the usage of deprecated modules and functions,
helping to track migration progress toward canonical implementations.
"""

import os
import sys
import json
import argparse
from datetime import datetime
from typing import Dict, List, Optional
import matplotlib.pyplot as plt
import pandas as pd

# Add project root to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.core.deprecation_monitor import DeprecationMonitor
from src.core.logger import get_logger

logger = get_logger(__name__)


def generate_text_report(usage_data: Dict, migration_data: Dict) -> str:
    """
    Generate a text-based deprecation report.
    
    Args:
        usage_data: Usage statistics from DeprecationMonitor
        migration_data: Migration progress data from DeprecationMonitor
        
    Returns:
        str: Formatted text report
    """
    report_lines = []
    
    # Add report header
    report_lines.append("=" * 80)
    report_lines.append(f"DEPRECATION USAGE REPORT - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("=" * 80)
    
    # Add migration progress summary
    report_lines.append("\nMIGRATION PROGRESS SUMMARY")
    report_lines.append("-" * 30)
    report_lines.append(f"Total Deprecated Modules: {migration_data['total_modules']}")
    report_lines.append(f"Modules Not Used in 30+ Days: {migration_data['migrated_modules_count']}")
    report_lines.append(f"Modules Still in Active Use: {migration_data['active_modules_count']}")
    report_lines.append(f"Migration Progress: {migration_data['progress_percentage']}%")
    
    # Add usage statistics
    report_lines.append("\nDEPRECATED MODULE USAGE")
    report_lines.append("-" * 30)
    report_lines.append(f"Total Usage Count: {usage_data['total_usage_count']}")
    
    # Add table header for module details
    report_lines.append("\nMODULE DETAILS")
    report_lines.append("-" * 100)
    report_lines.append(f"{'Module Name':<50} {'Usage Count':<15} {'Days Since Last Use':<20} {'Unique Callers':<15}")
    report_lines.append("-" * 100)
    
    # Add module details
    for module in usage_data['modules']:
        report_lines.append(
            f"{module['name']:<50} {module['usage_count']:<15} "
            f"{module['days_since_last_usage']:<20.1f} {module['unique_callers']:<15}"
        )
    
    # Add most active callers
    report_lines.append("\nTOP CALLERS BY MODULE")
    report_lines.append("-" * 100)
    
    for module in usage_data['modules']:
        if module['callers']:
            report_lines.append(f"\nModule: {module['name']}")
            report_lines.append("-" * 80)
            for i, caller in enumerate(sorted(module['callers']), 1):
                if i <= 5:  # Show top 5 callers
                    report_lines.append(f"  {i}. {caller}")
            if len(module['callers']) > 5:
                report_lines.append(f"  ... and {len(module['callers']) - 5} more callers")
    
    return "\n".join(report_lines)


def generate_html_report(usage_data: Dict, migration_data: Dict) -> str:
    """
    Generate an HTML deprecation report.
    
    Args:
        usage_data: Usage statistics from DeprecationMonitor
        migration_data: Migration progress data from DeprecationMonitor
        
    Returns:
        str: HTML report
    """
    # Generate progress chart
    progress_chart_path = "deprecation_progress.png"
    generate_progress_chart(migration_data, progress_chart_path)
    
    # Generate usage chart
    usage_chart_path = "deprecation_usage.png"
    generate_usage_chart(usage_data, usage_chart_path)
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Deprecation Usage Report - {datetime.now().strftime('%Y-%m-%d')}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3 {{ color: #333; }}
            .progress-bar {{
                background-color: #f0f0f0;
                border-radius: 5px;
                height: 25px;
                margin: 10px 0;
            }}
            .progress-bar-fill {{
                background-color: #4CAF50;
                height: 100%;
                border-radius: 5px;
                text-align: center;
                line-height: 25px;
                color: white;
            }}
            table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .chart {{ margin: 20px 0; }}
            .caller-list {{ margin-left: 20px; }}
        </style>
    </head>
    <body>
        <h1>Deprecation Usage Report</h1>
        <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        
        <h2>Migration Progress Summary</h2>
        <div class="progress-bar">
            <div class="progress-bar-fill" style="width: {migration_data['progress_percentage']}%">
                {migration_data['progress_percentage']}%
            </div>
        </div>
        <p>
            <strong>Total Deprecated Modules:</strong> {migration_data['total_modules']}<br>
            <strong>Modules Not Used in 30+ Days:</strong> {migration_data['migrated_modules_count']}<br>
            <strong>Modules Still in Active Use:</strong> {migration_data['active_modules_count']}
        </p>
        
        <div class="chart">
            <h3>Migration Progress</h3>
            <img src="{progress_chart_path}" alt="Migration Progress Chart" width="600">
        </div>
        
        <div class="chart">
            <h3>Top 10 Most Used Deprecated Modules</h3>
            <img src="{usage_chart_path}" alt="Module Usage Chart" width="600">
        </div>
        
        <h2>Deprecated Module Usage</h2>
        <p><strong>Total Usage Count:</strong> {usage_data['total_usage_count']}</p>
        
        <h2>Module Details</h2>
        <table>
            <tr>
                <th>Module Name</th>
                <th>Usage Count</th>
                <th>Days Since Last Use</th>
                <th>Unique Callers</th>
            </tr>
    """
    
    # Add module rows
    for module in usage_data['modules']:
        html += f"""
            <tr>
                <td>{module['name']}</td>
                <td>{module['usage_count']}</td>
                <td>{module['days_since_last_usage']:.1f}</td>
                <td>{module['unique_callers']}</td>
            </tr>
        """
    
    html += """
        </table>
        
        <h2>Top Callers By Module</h2>
    """
    
    # Add caller details
    for module in usage_data['modules']:
        if module['callers']:
            html += f"""
            <h3>{module['name']}</h3>
            <ul class="caller-list">
            """
            
            for i, caller in enumerate(sorted(module['callers']), 1):
                if i <= 5:  # Show top 5 callers
                    html += f"<li>{caller}</li>"
            
            if len(module['callers']) > 5:
                html += f"<li>... and {len(module['callers']) - 5} more callers</li>"
            
            html += "</ul>"
    
    html += """
    </body>
    </html>
    """
    
    return html


def generate_progress_chart(migration_data: Dict, output_path: str):
    """
    Generate a chart showing migration progress.
    
    Args:
        migration_data: Migration progress data
        output_path: Path to save the chart
    """
    try:
        # Create data for pie chart
        labels = ['Migrated', 'Active']
        sizes = [
            migration_data['migrated_modules_count'],
            migration_data['active_modules_count']
        ]
        colors = ['#4CAF50', '#FFA500']
        explode = (0.1, 0)  # explode the 1st slice (Migrated)
        
        plt.figure(figsize=(8, 6))
        plt.pie(
            sizes, explode=explode, labels=labels, colors=colors,
            autopct='%1.1f%%', shadow=True, startangle=140
        )
        plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle
        plt.title('Migration Progress')
        plt.savefig(output_path)
        plt.close()
    except Exception as e:
        logger.error(f"Failed to generate progress chart: {e}")


def generate_usage_chart(usage_data: Dict, output_path: str):
    """
    Generate a chart showing module usage.
    
    Args:
        usage_data: Usage statistics
        output_path: Path to save the chart
    """
    try:
        # Get top 10 most used modules
        top_modules = usage_data['modules'][:10]
        
        # Create data for bar chart
        module_names = [m['name'].split('.')[-1] for m in top_modules]  # Use just the last part of the name
        usage_counts = [m['usage_count'] for m in top_modules]
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(range(len(module_names)), usage_counts, color='#3498db')
        plt.xticks(range(len(module_names)), module_names, rotation=45, ha='right')
        plt.xlabel('Module')
        plt.ylabel('Usage Count')
        plt.title('Top 10 Most Used Deprecated Modules')
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    except Exception as e:
        logger.error(f"Failed to generate usage chart: {e}")


def main():
    """Main function to generate deprecation report."""
    parser = argparse.ArgumentParser(description='Generate deprecation usage report')
    parser.add_argument(
        '--format', choices=['text', 'html', 'json'], default='text',
        help='Output format (text, html, or json)'
    )
    parser.add_argument(
        '--output', '-o', type=str, default=None,
        help='Output file path (default: stdout)'
    )
    args = parser.parse_args()
    
    try:
        # Get deprecation monitor instance
        monitor = DeprecationMonitor.get_instance()
        
        # Get usage and migration data
        usage_data = monitor.get_usage_report()
        migration_data = monitor.get_migration_progress()
        
        # Generate report in requested format
        if args.format == 'text':
            report = generate_text_report(usage_data, migration_data)
        elif args.format == 'html':
            report = generate_html_report(usage_data, migration_data)
        else:  # json
            report = json.dumps({
                'usage_data': usage_data,
                'migration_data': migration_data,
                'generated_at': datetime.now().isoformat()
            }, indent=2)
        
        # Output report
        if args.output:
            with open(args.output, 'w') as f:
                f.write(report)
            print(f"Report saved to {args.output}")
        else:
            print(report)
        
    except Exception as e:
        logger.error(f"Error generating deprecation report: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
