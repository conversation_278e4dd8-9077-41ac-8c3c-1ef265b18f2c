import subprocess
import sys

def run_security_tests():
    """
    Run security-specific test suite
    
    Returns:
        bool: True if tests pass, False otherwise
    """
    try:
        # Run pytest with specific security test file
        result = subprocess.run([
            'pytest', 
            '-v',  # Verbose output
            'tests/test_advanced_security.py',
            '--disable-warnings'  # Suppress warnings
        ], capture_output=True, text=True)
        
        # Print test output
        print(result.stdout)
        print(result.stderr)
        
        # Check test result
        if result.returncode == 0:
            print("✅ Security Tests Passed Successfully!")
            return True
        else:
            print("❌ Security Tests Failed!")
            return False
    
    except Exception as e:
        print(f"Error running security tests: {e}")
        return False

if __name__ == '__main__':
    success = run_security_tests()
    sys.exit(0 if success else 1) 