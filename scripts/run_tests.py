#!/usr/bin/env python3
"""
Comprehensive test runner for TradingView Automation.

This script runs all tests and generates a report of the results.
"""

import os
import sys
import subprocess
import datetime
import json
from pathlib import Path

# Define test directories and files
TEST_DIRS = [
    "tests",
    "tests/unit",
    "tests/integration"
]

# Define specific test files to run
SPECIFIC_TESTS = [
    "tests/test_ai_chat_processor.py",
    "tests/test_backward_compatibility.py"
]

def run_test(test_path, verbose=True):
    """Run a specific test and return the result."""
    cmd = ["python", "-m", "pytest", test_path]
    if verbose:
        cmd.append("-v")
    
    print(f"Running test: {test_path}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    return {
        "path": test_path,
        "success": result.returncode == 0,
        "output": result.stdout,
        "error": result.stderr
    }

def generate_report(results):
    """Generate a report of the test results."""
    total = len(results)
    successful = sum(1 for r in results if r["success"])
    failed = total - successful
    
    report = {
        "timestamp": datetime.datetime.now().isoformat(),
        "total_tests": total,
        "successful_tests": successful,
        "failed_tests": failed,
        "success_rate": f"{(successful / total) * 100:.2f}%" if total > 0 else "N/A",
        "results": results
    }
    
    # Save report to file
    report_path = Path("test_results/test_report.json")
    report_path.parent.mkdir(exist_ok=True)
    with open(report_path, "w") as f:
        json.dump(report, f, indent=2)
    
    # Generate summary
    summary = f"""
TEST SUMMARY
============
Timestamp: {report['timestamp']}
Total Tests: {report['total_tests']}
Successful: {report['successful_tests']}
Failed: {report['failed_tests']}
Success Rate: {report['success_rate']}

Failed Tests:
{chr(10).join(f"- {r['path']}" for r in results if not r['success'])}
"""
    
    # Save summary to file
    summary_path = Path("test_results/test_summary.txt")
    with open(summary_path, "w") as f:
        f.write(summary)
    
    return report, summary

def main():
    """Main function to run tests and generate report."""
    results = []
    
    # Run specific tests
    for test in SPECIFIC_TESTS:
        if os.path.exists(test):
            results.append(run_test(test))
    
    # Run tests in directories
    for test_dir in TEST_DIRS:
        if os.path.exists(test_dir) and os.path.isdir(test_dir):
            results.append(run_test(test_dir, verbose=False))
    
    # Generate and print report
    report, summary = generate_report(results)
    print(summary)
    
    # Return non-zero exit code if any tests failed
    return 0 if report["failed_tests"] == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
