#!/usr/bin/env python3
"""
Monitor usage of deprecated modules and generate reports.

This script monitors the usage of deprecated modules and generates reports
to help identify which parts of the codebase are still using deprecated modules.
"""

import os
import sys
import json
import datetime
import argparse
from pathlib import Path
from collections import Counter, defaultdict

# Define the path to the deprecation log file
DEPRECATION_LOG_PATH = Path("logs/deprecation_log.json")

def load_deprecation_log():
    """Load the deprecation log file."""
    if not DEPRECATION_LOG_PATH.exists():
        print(f"Deprecation log file not found at {DEPRECATION_LOG_PATH}")
        return []
    
    try:
        with open(DEPRECATION_LOG_PATH, "r") as f:
            return json.load(f)
    except json.JSONDecodeError:
        print(f"Error decoding JSON from {DEPRECATION_LOG_PATH}")
        return []

def generate_usage_report(log_entries):
    """Generate a report of module usage."""
    if not log_entries:
        return {
            "timestamp": datetime.datetime.now().isoformat(),
            "total_usages": 0,
            "modules": {},
            "callers": {},
            "daily_usage": {}
        }
    
    # Count module usage
    module_counter = Counter()
    caller_counter = Counter()
    daily_usage = defaultdict(Counter)
    
    for entry in log_entries:
        module = entry.get("module", "unknown")
        caller = entry.get("caller", "unknown")
        timestamp = entry.get("timestamp", "")
        
        module_counter[module] += 1
        caller_counter[caller] += 1
        
        # Extract date from timestamp
        try:
            date = timestamp.split("T")[0]
            daily_usage[date][module] += 1
        except (IndexError, AttributeError):
            pass
    
    # Convert daily usage to regular dict for JSON serialization
    daily_usage_dict = {date: dict(modules) for date, modules in daily_usage.items()}
    
    # Sort dates for the report
    sorted_dates = sorted(daily_usage_dict.keys())
    
    report = {
        "timestamp": datetime.datetime.now().isoformat(),
        "total_usages": sum(module_counter.values()),
        "modules": dict(module_counter),
        "callers": dict(caller_counter),
        "daily_usage": daily_usage_dict,
        "date_range": {
            "start": sorted_dates[0] if sorted_dates else None,
            "end": sorted_dates[-1] if sorted_dates else None
        }
    }
    
    return report

def save_report(report, output_path=None):
    """Save the report to a file."""
    if output_path is None:
        output_dir = Path("logs/deprecation_reports")
        output_dir.mkdir(exist_ok=True, parents=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = output_dir / f"deprecation_report_{timestamp}.json"
    
    with open(output_path, "w") as f:
        json.dump(report, f, indent=2)
    
    return output_path

def print_report_summary(report):
    """Print a summary of the report."""
    print("\nDEPRECATION USAGE REPORT")
    print("=======================")
    print(f"Generated: {report['timestamp']}")
    print(f"Total usages: {report['total_usages']}")
    
    if report['date_range']['start']:
        print(f"Date range: {report['date_range']['start']} to {report['date_range']['end']}")
    
    print("\nTop 5 deprecated modules by usage:")
    for module, count in sorted(report['modules'].items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  - {module}: {count} usages")
    
    print("\nTop 5 callers of deprecated modules:")
    for caller, count in sorted(report['callers'].items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  - {caller}: {count} usages")
    
    # Print daily usage trend
    if report['daily_usage']:
        print("\nDaily usage trend:")
        dates = sorted(report['daily_usage'].keys())
        for date in dates[-7:]:  # Show last 7 days
            total = sum(report['daily_usage'][date].values())
            print(f"  - {date}: {total} usages")

def main():
    """Main function to generate and display deprecation reports."""
    parser = argparse.ArgumentParser(description="Monitor usage of deprecated modules")
    parser.add_argument("--output", "-o", help="Output file path for the report")
    parser.add_argument("--quiet", "-q", action="store_true", help="Don't print the report summary")
    args = parser.parse_args()
    
    # Load deprecation log
    log_entries = load_deprecation_log()
    
    # Generate report
    report = generate_usage_report(log_entries)
    
    # Save report
    output_path = save_report(report, args.output)
    print(f"Report saved to {output_path}")
    
    # Print summary
    if not args.quiet:
        print_report_summary(report)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
