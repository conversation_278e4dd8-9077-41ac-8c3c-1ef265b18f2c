#!/usr/bin/env python3
"""
Test Script for Enhanced Technical Indicators

Tests the new advanced technical indicators:
- Fibonacci retracements
- Ichimoku Cloud
- Stochastic Oscillator
- Williams %R
- CCI, ATR, VWAP
- Elliott Wave analysis
"""

import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_fibonacci_retracements():
    """Test Fibonacci retracement calculations."""
    print("🔍 Testing Fibonacci Retracements...")
    
    try:
        from shared.technical_analysis.enhanced_indicators import EnhancedTechnicalIndicators
        
        calculator = EnhancedTechnicalIndicators()
        
        # Test data: price range from 100 to 150
        highs = [150, 150, 150, 150, 150]
        lows = [100, 100, 100, 100, 100]
        
        fib_levels = calculator.calculate_fibonacci_retracements(highs, lows)
        
        if fib_levels:
            print("✅ Fibonacci levels calculated successfully")
            print(f"   Swing High: ${fib_levels.swing_high:.2f}")
            print(f"   Swing Low: ${fib_levels.swing_low:.2f}")
            print(f"   Swing Range: ${fib_levels.swing_range:.2f}")
            
            # Check key retracement levels
            key_levels = ["0.236", "0.382", "0.500", "0.618", "0.786"]
            for level in key_levels:
                if level in fib_levels.retracements:
                    price = fib_levels.retracements[level]
                    print(f"   {level} retracement: ${price:.2f}")
            
            return True
        else:
            print("❌ Fibonacci levels calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Fibonacci retracements: {e}")
        return False

def test_ichimoku_cloud():
    """Test Ichimoku Cloud calculations."""
    print("\n🔍 Testing Ichimoku Cloud...")
    
    try:
        from shared.technical_analysis.enhanced_indicators import EnhancedTechnicalIndicators
        
        calculator = EnhancedTechnicalIndicators()
        
        # Create sample data (need at least 52 periods)
        prices = list(range(100, 152))  # 52 periods
        highs = [p + 2 for p in prices]
        lows = [p - 2 for p in prices]
        closes = prices
        
        ichimoku = calculator.calculate_ichimoku_cloud(highs, lows, closes)
        
        if ichimoku:
            print("✅ Ichimoku Cloud calculated successfully")
            print(f"   Tenkan-sen: ${ichimoku.tenkan_sen:.2f}")
            print(f"   Kijun-sen: ${ichimoku.kijun_sen:.2f}")
            print(f"   Cloud Color: {ichimoku.cloud_color}")
            print(f"   Cloud Top: ${ichimoku.cloud_top:.2f}")
            print(f"   Cloud Bottom: ${ichimoku.cloud_bottom:.2f}")
            
            return True
        else:
            print("❌ Ichimoku Cloud calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Ichimoku Cloud: {e}")
        return False

def test_stochastic_oscillator():
    """Test Stochastic Oscillator calculations."""
    print("\n🔍 Testing Stochastic Oscillator...")
    
    try:
        from shared.technical_analysis.enhanced_indicators import EnhancedTechnicalIndicators
        
        calculator = EnhancedTechnicalIndicators()
        
        # Create sample data
        prices = [100, 101, 102, 101, 100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90]
        highs = [p + 1 for p in prices]
        lows = [p - 1 for p in prices]
        closes = prices
        
        stoch = calculator.calculate_stochastic_oscillator(highs, lows, closes)
        
        if stoch:
            print("✅ Stochastic Oscillator calculated successfully")
            print(f"   %K: {stoch['k_percent']:.2f}")
            print(f"   %D: {stoch['d_percent']:.2f}")
            print(f"   Overbought: {stoch['overbought']}")
            print(f"   Oversold: {stoch['oversold']}")
            
            # Validate ranges
            if 0 <= stoch['k_percent'] <= 100 and 0 <= stoch['d_percent'] <= 100:
                print("✅ Stochastic values in valid range (0-100)")
                return True
            else:
                print("❌ Stochastic values outside valid range")
                return False
        else:
            print("❌ Stochastic Oscillator calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Stochastic Oscillator: {e}")
        return False

def test_williams_r():
    """Test Williams %R calculations."""
    print("\n🔍 Testing Williams %R...")
    
    try:
        from shared.technical_analysis.enhanced_indicators import EnhancedTechnicalIndicators
        
        calculator = EnhancedTechnicalIndicators()
        
        # Create sample data
        prices = [100, 101, 102, 101, 100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90]
        highs = [p + 1 for p in prices]
        lows = [p - 1 for p in prices]
        closes = prices
        
        williams_r = calculator.calculate_williams_r(highs, lows, closes)
        
        if williams_r:
            print("✅ Williams %R calculated successfully")
            print(f"   Williams %R: {williams_r['williams_r']:.2f}")
            print(f"   Overbought: {williams_r['overbought']}")
            print(f"   Oversold: {williams_r['oversold']}")
            
            # Validate range (-100 to 0)
            if -100 <= williams_r['williams_r'] <= 0:
                print("✅ Williams %R in valid range (-100 to 0)")
                return True
            else:
                print("❌ Williams %R outside valid range")
                return False
        else:
            print("❌ Williams %R calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Williams %R: {e}")
        return False

def test_cci():
    """Test Commodity Channel Index calculations."""
    print("\n🔍 Testing CCI...")
    
    try:
        from shared.technical_analysis.enhanced_indicators import EnhancedTechnicalIndicators
        
        calculator = EnhancedTechnicalIndicators()
        
        # Create sample data
        prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119]
        highs = [p + 1 for p in prices]
        lows = [p - 1 for p in prices]
        closes = prices
        
        cci = calculator.calculate_cci(highs, lows, closes)
        
        if cci:
            print("✅ CCI calculated successfully")
            print(f"   CCI: {cci['cci']:.2f}")
            print(f"   Overbought: {cci['overbought']}")
            print(f"   Oversold: {cci['oversold']}")
            print(f"   Neutral: {cci['neutral']}")
            
            return True
        else:
            print("❌ CCI calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing CCI: {e}")
        return False

def test_atr():
    """Test Average True Range calculations."""
    print("\n🔍 Testing ATR...")
    
    try:
        from shared.technical_analysis.enhanced_indicators import EnhancedTechnicalIndicators
        
        calculator = EnhancedTechnicalIndicators()
        
        # Create sample data
        prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119]
        highs = [p + 2 for p in prices]
        lows = [p - 2 for p in prices]
        closes = prices
        
        atr = calculator.calculate_atr(highs, lows, closes)
        
        if atr:
            print("✅ ATR calculated successfully")
            print(f"   ATR: {atr['atr']:.4f}")
            print(f"   ATR %: {atr['atr_percentage']:.2f}%")
            print(f"   Volatility Level: {atr['volatility_level']}")
            
            return True
        else:
            print("❌ ATR calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing ATR: {e}")
        return False

def test_vwap():
    """Test Volume Weighted Average Price calculations."""
    print("\n🔍 Testing VWAP...")
    
    try:
        from shared.technical_analysis.enhanced_indicators import EnhancedTechnicalIndicators
        
        calculator = EnhancedTechnicalIndicators()
        
        # Create sample data
        prices = [100, 101, 102, 103, 104, 105]
        volumes = [1000000, 1100000, 1200000, 1300000, 1400000, 1500000]
        
        vwap = calculator.calculate_vwap(prices, volumes)
        
        if vwap:
            print("✅ VWAP calculated successfully")
            print(f"   VWAP: ${vwap['vwap']:.2f}")
            print(f"   Upper Band: ${vwap['upper_band']:.2f}")
            print(f"   Lower Band: ${vwap['lower_band']:.2f}")
            print(f"   VWAP Position: {vwap['vwap_position']:.2f}%")
            print(f"   Above VWAP: {vwap['above_vwap']}")
            
            return True
        else:
            print("❌ VWAP calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing VWAP: {e}")
        return False

def test_momentum_indicators():
    """Test momentum-based indicators."""
    print("\n🔍 Testing Momentum Indicators...")
    
    try:
        from shared.technical_analysis.enhanced_indicators import EnhancedTechnicalIndicators
        
        calculator = EnhancedTechnicalIndicators()
        
        # Create sample data
        prices = [100, 101, 102, 101, 100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90]
        volumes = [1000000] * len(prices)
        
        momentum = calculator.calculate_momentum_indicators(prices, volumes)
        
        if momentum:
            print("✅ Momentum indicators calculated successfully")
            
            if "roc" in momentum:
                print(f"   ROC: {momentum['roc']:.2f}%")
            
            if "mfi" in momentum:
                print(f"   MFI: {momentum['mfi']:.2f}")
                print(f"   MFI Overbought: {momentum['mfi_overbought']}")
                print(f"   MFI Oversold: {momentum['mfi_oversold']}")
            
            if "obv" in momentum:
                print(f"   OBV: {momentum['obv']:,.0f}")
            
            if "ad_line" in momentum:
                print(f"   AD Line: {momentum['ad_line']:,.0f}")
            
            return True
        else:
            print("❌ Momentum indicators calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing momentum indicators: {e}")
        return False

def run_all_tests():
    """Run all enhanced indicators tests."""
    print("🚀 Running Enhanced Technical Indicators Tests...")
    print("=" * 70)
    
    tests = [
        ("Fibonacci Retracements", test_fibonacci_retracements),
        ("Ichimoku Cloud", test_ichimoku_cloud),
        ("Stochastic Oscillator", test_stochastic_oscillator),
        ("Williams %R", test_williams_r),
        ("CCI", test_cci),
        ("ATR", test_atr),
        ("VWAP", test_vwap),
        ("Momentum Indicators", test_momentum_indicators)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Enhanced technical indicators are working properly!")
        print("🚀 Ready to integrate with multi-timeframe analysis")
    else:
        print("⚠️  Some enhanced indicators need attention")
        print("🔧 Please fix the failing tests before continuing")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 