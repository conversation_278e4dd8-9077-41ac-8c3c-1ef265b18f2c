#!/usr/bin/env python3
"""
Test the report engine with mock provider fallback.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_report_engine():
    """Test the report engine with mock provider fallback."""
    try:
        print("🚀 Testing Report Engine with Mock Provider")
        print("=" * 50)
        
        from src.core.automation.report_engine import AIReportEngine
        
        engine = AIReportEngine()
        print("✅ Report engine created")
        
        # Test market data collection
        print("\n📊 Collecting market data...")
        market_data = await engine._collect_market_data()
        
        if market_data:
            print(f"✅ Successfully collected data for {len(market_data)} symbols")
            print("\n📈 Sample Market Data:")
            for stock in market_data[:5]:  # Show first 5
                print(f"  {stock.symbol}: ${stock.current_price:.2f} ({stock.price_change_pct:+.2f}%) - {stock.sector}")
        else:
            print("❌ No market data collected")
            return
        
        # Test sector analysis
        print("\n🏭 Testing Sector Performance Analysis...")
        sector_performance = await engine._analyze_sector_performance()
        
        if sector_performance:
            print(f"✅ Analyzed {len(sector_performance)} sectors")
            for sector in sector_performance[:3]:  # Show top 3 sectors
                print(f"  {sector.sector}: {sector.performance_pct:+.2f}% (Top: {sector.top_performer} +{sector.top_performer_pct:.2f}%)")
        
        # Test market health assessment
        print("\n🏥 Testing Market Health Assessment...")
        market_health = await engine._assess_market_health()
        
        if market_health:
            print(f"✅ Market Health: {market_health.avg_data_quality:.1f}/100")
            print(f"  Sentiment: {market_health.market_sentiment}")
            print(f"  Risk Level: {market_health.risk_level}")
            print(f"  Data Coverage: {market_health.total_symbols - market_health.stale_symbols_count}/{market_health.total_symbols}")
        
        # Test full report generation
        print("\n📋 Testing Full Report Generation...")
        try:
            report = await engine.generate_daily_market_report()
            print(f"✅ Daily report generated successfully!")
            print(f"  Title: {report.title}")
            print(f"  Summary: {report.summary[:100]}...")
            print(f"  AI Insights: {len(report.ai_insights)}")
            print(f"  Recommendations: {len(report.recommendations)}")
            print(f"  Risk Alerts: {len(report.risk_alerts)}")
        except Exception as e:
            print(f"❌ Report generation failed: {e}")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    await test_report_engine()
    print("\n✅ Report engine test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 