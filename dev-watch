#!/bin/bash

echo "👀 Dev Watch - Auto-copy files on change"
echo "========================================"

# Install inotify-tools if not available
if ! command -v inotifywait &> /dev/null; then
    echo "📦 Installing inotify-tools for file watching..."
    sudo dnf install -y inotify-tools || sudo apt install -y inotify-tools
fi

echo "🔍 Watching for changes in src/bot/pipeline/..."
echo "💡 Press Ctrl+C to stop watching"

# Watch for changes and auto-copy
inotifywait -m -r -e modify,create,move src/bot/pipeline/ --format '%w%f' | while read file; do
    if [[ "$file" == *.py ]]; then
        echo "📝 File changed: $file"
        
        # Get the relative path inside the container
        container_path="/app/$file"
        
        echo "📁 Copying to container: $container_path"
        docker cp "$file" "tradingview-automation-bot-1:$container_path" 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo "✅ Updated successfully!"
        else
            echo "❌ Copy failed - container might not be running"
        fi
        
        echo "---"
    fi
done 