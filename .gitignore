# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
 
# Database
*.db
*.sqlite3

# Docker
.dockerignore

# Keep .cursor directory (contains project rules)
 

# Keep important docs
!CONTRIBUTING.md
!README.md 

# Secrets and Environment Files
 
 
*.pem
*.key

# Sensitive Logs
*.log
logs/
*.log.*

# Sensitive Caches
.cache/
__pycache__/

# Development and IDE specific
.vscode/
.idea/
*.swp
*.swo

 
 
