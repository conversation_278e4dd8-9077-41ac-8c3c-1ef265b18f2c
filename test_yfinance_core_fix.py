#!/usr/bin/env python3
"""
Core yfinance integration test focusing on the main fixes.

This test validates the core yfinance functionality and error handling
without complex dependencies.
"""

import sys
import os
import traceback
import asyncio

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_yfinance_basic_functionality():
    """Test basic yfinance functionality"""
    print("🧪 Testing yfinance Basic Functionality...")
    
    try:
        import yfinance as yf
        
        # Test 1: Import successful
        print("  ✅ Test 1: yfinance import")
        print(f"     yfinance version: {getattr(yf, '__version__', 'unknown')}")
        
        # Test 2: Basic ticker creation
        print("  ✅ Test 2: Ticker creation")
        ticker = yf.Ticker("AAPL")
        print("     AAPL ticker created successfully")
        
        # Test 3: Historical data
        print("  ✅ Test 3: Historical data")
        hist = ticker.history(period="1d")
        if not hist.empty:
            latest_price = hist['Close'].iloc[-1]
            print(f"     AAPL latest price: ${latest_price:.2f}")
        else:
            print("     No historical data available")
        
        # Test 4: Multiple symbols
        print("  ✅ Test 4: Multiple symbols")
        symbols = ["AAPL", "MSFT", "GOOGL"]
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="1d")
                if not hist.empty:
                    price = hist['Close'].iloc[-1]
                    print(f"     {symbol}: ${price:.2f}")
                else:
                    print(f"     {symbol}: No data")
            except Exception as e:
                print(f"     {symbol}: Error - {e}")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ ImportError: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def test_error_handling_improvements():
    """Test improved error handling for yfinance"""
    print("🧪 Testing Error Handling Improvements...")
    
    try:
        # Test 1: Import error simulation
        print("  ✅ Test 1: Import error handling")
        
        # Simulate the improved error handling logic
        def simulate_yfinance_import_error():
            try:
                # This would be the actual import in real code
                import yfinance as yf
                return True, "yfinance imported successfully"
            except ImportError as e:
                # Improved error handling
                import sys
                if 'yfinance' in sys.modules:
                    return False, f"yfinance import error (dependency issue): {str(e)}"
                else:
                    return False, "yfinance package not available - install with: pip install yfinance"
            except Exception as e:
                return False, f"yfinance import failed: {str(e)}"
        
        success, message = simulate_yfinance_import_error()
        print(f"     Result: {message}")
        
        # Test 2: Graceful degradation
        print("  ✅ Test 2: Graceful degradation")
        
        def get_stock_data_with_fallback(symbol):
            """Simulate improved stock data fetching with fallback"""
            try:
                import yfinance as yf
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="1d")
                
                if hist.empty:
                    raise ValueError("No historical data available")
                
                price = hist['Close'].iloc[-1]
                return {
                    'symbol': symbol,
                    'current_price': float(price),
                    'status': 'success',
                    'provider': 'yfinance',
                    'error': None
                }
                
            except ImportError as e:
                return {
                    'symbol': symbol,
                    'current_price': None,
                    'status': 'error',
                    'provider': 'fallback',
                    'error': f"yfinance import failed: {str(e)}"
                }
            except Exception as e:
                return {
                    'symbol': symbol,
                    'current_price': None,
                    'status': 'error',
                    'provider': 'fallback',
                    'error': str(e)
                }
        
        # Test with valid symbol
        result = get_stock_data_with_fallback("AAPL")
        print(f"     AAPL result: {result['status']} - ${result.get('current_price', 'N/A')}")
        
        # Test with invalid symbol
        result = get_stock_data_with_fallback("INVALID_SYMBOL")
        print(f"     INVALID result: {result['status']} - {result.get('error', 'No error')}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def test_requirements_version_fix():
    """Test that requirements.txt version is updated"""
    print("🧪 Testing Requirements Version Fix...")
    
    try:
        # Test 1: Check requirements.txt content
        print("  ✅ Test 1: Requirements.txt version")
        with open('requirements.txt', 'r') as f:
            content = f.read()
        
        if 'yfinance==0.2.65' in content:
            print("     ✅ yfinance version updated to 0.2.65")
            version_correct = True
        elif 'yfinance==0.2.18' in content:
            print("     ⚠️ yfinance still at old version 0.2.18")
            version_correct = False
        else:
            print("     ❓ yfinance version not found in requirements.txt")
            version_correct = False
        
        # Test 2: Check installed version matches
        print("  ✅ Test 2: Installed version check")
        try:
            import yfinance
            installed_version = getattr(yfinance, '__version__', 'unknown')
            print(f"     Installed version: {installed_version}")
            
            if installed_version == '0.2.65':
                print("     ✅ Installed version matches requirements")
            else:
                print(f"     ⚠️ Version mismatch - installed: {installed_version}, required: 0.2.65")
        except ImportError:
            print("     ❌ yfinance not installed")
        
        return version_correct
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def test_fallback_data_generation():
    """Test fallback data generation without complex dependencies"""
    print("🧪 Testing Fallback Data Generation...")
    
    try:
        # Test 1: Simple fallback data structure
        print("  ✅ Test 1: Fallback data structure")
        
        def generate_fallback_data(symbol):
            """Generate simple fallback data"""
            import random
            from datetime import datetime
            
            # Sample data for common symbols
            sample_prices = {
                "AAPL": 175.00,
                "MSFT": 380.00,
                "GOOGL": 140.00,
                "TSLA": 250.00,
                "NVDA": 450.00
            }
            
            base_price = sample_prices.get(symbol, 100.00)
            variation = random.uniform(-0.05, 0.05)  # ±5%
            current_price = base_price * (1 + variation)
            change = base_price * variation
            change_percent = variation * 100
            
            return {
                "symbol": symbol,
                "current_price": round(current_price, 2),
                "change": round(change, 2),
                "change_percent": round(change_percent, 2),
                "volume": random.randint(1000000, 50000000),
                "timestamp": datetime.now().isoformat(),
                "source": "fallback",
                "data_suspicious": True,
                "suspicious_reasons": ["Using fallback data - primary provider unavailable"]
            }
        
        # Test fallback data for multiple symbols
        symbols = ["AAPL", "MSFT", "UNKNOWN_SYMBOL"]
        for symbol in symbols:
            data = generate_fallback_data(symbol)
            price = data["current_price"]
            change = data["change_percent"]
            print(f"     {symbol}: ${price} ({change:+.2f}%) [fallback]")
        
        # Test 2: Fallback data validation
        print("  ✅ Test 2: Fallback data validation")
        test_data = generate_fallback_data("TEST")
        
        required_fields = ["symbol", "current_price", "change", "change_percent", "volume", "timestamp"]
        missing_fields = [field for field in required_fields if field not in test_data]
        
        if not missing_fields:
            print("     ✅ All required fields present")
        else:
            print(f"     ❌ Missing fields: {missing_fields}")
        
        # Check data types
        if isinstance(test_data["current_price"], (int, float)) and test_data["current_price"] > 0:
            print("     ✅ Price data valid")
        else:
            print("     ❌ Invalid price data")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def test_configuration_improvements():
    """Test configuration improvements"""
    print("🧪 Testing Configuration Improvements...")
    
    try:
        # Test 1: Provider configuration structure
        print("  ✅ Test 1: Provider configuration")
        
        def get_provider_config():
            """Simulate improved provider configuration"""
            return {
                "yfinance": {
                    "enabled": True,
                    "priority": 3,  # Lower priority (fallback)
                    "rate_limit": 5,
                    "timeout": 30.0,
                    "max_retries": 3,
                    "graceful_degradation": True
                },
                "alpha_vantage": {
                    "enabled": True,
                    "priority": 2,
                    "rate_limit": 5,
                    "timeout": 10.0,
                    "api_key": ""
                },
                "polygon": {
                    "enabled": True,
                    "priority": 1,  # Highest priority
                    "rate_limit": 5,
                    "timeout": 8.0,
                    "api_key": ""
                }
            }
        
        config = get_provider_config()
        print(f"     Configured providers: {list(config.keys())}")
        
        # Test priority ordering
        providers_by_priority = sorted(config.items(), key=lambda x: x[1].get("priority", 999))
        priority_order = [name for name, _ in providers_by_priority]
        print(f"     Priority order: {' → '.join(priority_order)}")
        
        # Test 2: Fallback chain
        print("  ✅ Test 2: Fallback chain")
        
        def get_fallback_chain(config):
            """Get fallback chain based on configuration"""
            available_providers = []
            for name, provider_config in config.items():
                if provider_config.get("enabled", False):
                    # Simulate availability check
                    if name == "yfinance":
                        available_providers.append(name)  # yfinance is available
                    elif provider_config.get("api_key"):
                        available_providers.append(name)  # Has API key
                    # else: not available (no API key)
            
            return sorted(available_providers, key=lambda x: config[x].get("priority", 999))
        
        fallback_chain = get_fallback_chain(config)
        print(f"     Fallback chain: {' → '.join(fallback_chain) if fallback_chain else 'None available'}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all core tests"""
    print("🔧 Testing Core yfinance Integration Fixes\n")
    
    tests = [
        test_yfinance_basic_functionality,
        test_error_handling_improvements,
        test_requirements_version_fix,
        test_fallback_data_generation,
        test_configuration_improvements
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ PASSED\n")
            else:
                print("❌ FAILED\n")
        except Exception as e:
            print(f"❌ FAILED with exception: {e}\n")
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All core tests passed! yfinance integration fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
