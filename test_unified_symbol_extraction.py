#!/usr/bin/env python3
"""
Test script for unified symbol extraction functionality.

This script tests the new unified symbol extraction to ensure it works
correctly across both /ask and /analyze commands.
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_unified_symbol_extraction():
    """Test the unified symbol extraction functionality"""
    print("Testing unified symbol extraction...")
    
    try:
        from src.shared.utils.symbol_extraction import (
            extract_symbols_from_query,
            extract_symbols_with_metadata,
            validate_symbol_format,
            unified_symbol_extractor
        )
        
        # Test cases for symbol extraction
        test_cases = [
            ("What is the price of $AAPL?", ["AAPL"]),
            ("Tell me about AAPL and MSFT", []),  # No $ prefix
            ("I want $TSLA and $NVDA info", ["TSLA", "NVDA"]),
            ("What are the top TECH stocks?", []),  # No $ prefix
            ("Check $MSFT performance", ["MSFT"]),
            ("The PRICE of STOCK is high", []),  # Common words
            ("$GOOGL vs $AMZN comparison", ["GOOGL", "AMZN"]),
            ("Buy $SPY and hold $QQQ", ["SPY", "QQQ"]),
        ]
        
        print("\n1. Testing extract_symbols_from_query():")
        all_passed = True
        for query, expected in test_cases:
            result = extract_symbols_from_query(query)
            if result == expected:
                print(f"  ✅ '{query}' -> {result}")
            else:
                print(f"  ❌ '{query}' -> {result} (expected {expected})")
                all_passed = False
        
        print("\n2. Testing extract_symbols_with_metadata():")
        metadata_test_cases = [
            ("What is $AAPL price?", 1),  # Should find 1 symbol
            ("Compare $TSLA vs $NVDA", 2),  # Should find 2 symbols
            ("No symbols here", 0),  # Should find 0 symbols
        ]
        
        for query, expected_count in metadata_test_cases:
            result = extract_symbols_with_metadata(query)
            if len(result) == expected_count:
                print(f"  ✅ '{query}' -> {len(result)} symbols")
                for symbol in result:
                    print(f"      {symbol.symbol} (confidence: {symbol.confidence}, context: {symbol.context})")
            else:
                print(f"  ❌ '{query}' -> {len(result)} symbols (expected {expected_count})")
                all_passed = False
        
        print("\n3. Testing validate_symbol_format():")
        validation_test_cases = [
            ("AAPL", True),
            ("$TSLA", True),  # Should handle $ prefix
            ("GOOGL", True),
            ("INVALID123", False),  # Numbers not allowed
            ("", False),  # Empty string
            ("TOOLONGNAME", False),  # Too long
            ("THE", False),  # Common word
        ]
        
        for symbol, expected_valid in validation_test_cases:
            sanitized, is_valid, error = validate_symbol_format(symbol)
            if is_valid == expected_valid:
                print(f"  ✅ '{symbol}' -> valid: {is_valid}, sanitized: '{sanitized}'")
            else:
                print(f"  ❌ '{symbol}' -> valid: {is_valid} (expected {expected_valid}), error: {error}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing unified symbol extraction: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_discord_helpers():
    """Test the Discord helper utilities"""
    print("\nTesting Discord helper utilities...")
    
    try:
        from src.shared.utils.discord_helpers import DiscordMessageHelper
        
        # Test message length enforcement
        print("\n1. Testing message length enforcement:")
        
        # Short message (should pass through unchanged)
        short_msg = "This is a short message"
        result = DiscordMessageHelper.enforce_message_limit(short_msg)
        if result == short_msg:
            print(f"  ✅ Short message preserved: '{result[:50]}...'")
        else:
            print(f"  ❌ Short message changed unexpectedly")
            return False
        
        # Long message (should be truncated)
        long_msg = "A" * 2500  # Longer than Discord's 2000 char limit
        result = DiscordMessageHelper.enforce_message_limit(long_msg)
        if len(result) <= 2000 and "truncated" in result:
            print(f"  ✅ Long message truncated: {len(result)} chars")
        else:
            print(f"  ❌ Long message not properly truncated: {len(result)} chars")
            return False
        
        # Test code block formatting
        print("\n2. Testing code block formatting:")
        code_content = "print('Hello, world!')"
        code_block = DiscordMessageHelper.format_code_block(code_content, "python")
        if code_block.startswith("```python") and code_block.endswith("```"):
            print(f"  ✅ Code block formatted correctly")
        else:
            print(f"  ❌ Code block not formatted correctly")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Discord helpers: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("UNIFIED SYMBOL EXTRACTION & DISCORD HELPERS TEST")
    print("=" * 60)
    
    # Test unified symbol extraction
    symbol_test_passed = test_unified_symbol_extraction()
    
    # Test Discord helpers
    discord_test_passed = test_discord_helpers()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"Symbol Extraction: {'✅ PASSED' if symbol_test_passed else '❌ FAILED'}")
    print(f"Discord Helpers: {'✅ PASSED' if discord_test_passed else '❌ FAILED'}")
    
    if symbol_test_passed and discord_test_passed:
        print("\n🎉 All tests passed! The unified utilities are working correctly.")
        return True
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
