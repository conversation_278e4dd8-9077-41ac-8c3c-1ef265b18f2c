"""
Simple Pipeline Debug Test

This script demonstrates how to use the pipeline debugging features
without requiring the Discord library.
"""

import asyncio
import time
import json
from datetime import datetime
from enum import Enum
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("pipeline-debug")

class StageStatus(Enum):
    """Status of a pipeline stage"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class SimplePipelineDebugger:
    """
    Simple pipeline debugger that logs execution flow
    without requiring Discord or other external dependencies
    """
    
    def __init__(self, command_name: str, correlation_id: str = None):
        self.command_name = command_name
        self.correlation_id = correlation_id or f"debug-{int(time.time())}"
        self.stages = {}
        self.start_time = time.time()
        self.data_snapshots = {}
        self.errors = []
        
    async def start(self):
        """Start debugging session"""
        logger.info(f"🚀 Starting pipeline debug for {self.command_name} (ID: {self.correlation_id})")
        
    async def register_stage(self, stage_name: str, description: str = ""):
        """Register a pipeline stage"""
        self.stages[stage_name] = {
            "name": stage_name,
            "description": description,
            "status": StageStatus.PENDING,
            "start_time": None,
            "end_time": None,
            "duration": None,
            "order": len(self.stages) + 1
        }
        logger.info(f"📋 Registered stage: {stage_name} - {description}")
        
    async def start_stage(self, stage_name: str, input_data=None):
        """Mark a stage as started"""
        if stage_name not in self.stages:
            await self.register_stage(stage_name)
            
        self.stages[stage_name]["status"] = StageStatus.RUNNING
        self.stages[stage_name]["start_time"] = time.time()
        
        # Log input data
        if input_data:
            self.data_snapshots[f"{stage_name}_input"] = self._create_data_snapshot(input_data)
            
        logger.info(f"▶️ Started stage: {stage_name}")
        
    async def complete_stage(self, stage_name: str, output_data=None):
        """Mark a stage as completed"""
        if stage_name not in self.stages:
            logger.warning(f"Completing unregistered stage: {stage_name}")
            await self.register_stage(stage_name)
            await self.start_stage(stage_name)
            
        self.stages[stage_name]["status"] = StageStatus.COMPLETED
        self.stages[stage_name]["end_time"] = time.time()
        
        if self.stages[stage_name]["start_time"]:
            duration = self.stages[stage_name]["end_time"] - self.stages[stage_name]["start_time"]
            self.stages[stage_name]["duration"] = duration
            
        # Log output data
        if output_data:
            self.data_snapshots[f"{stage_name}_output"] = self._create_data_snapshot(output_data)
            
        logger.info(f"✅ Completed stage: {stage_name} in {self.stages[stage_name].get('duration', 0):.2f}s")
        
    async def fail_stage(self, stage_name: str, error):
        """Mark a stage as failed"""
        if stage_name not in self.stages:
            logger.warning(f"Failing unregistered stage: {stage_name}")
            await self.register_stage(stage_name)
            await self.start_stage(stage_name)
            
        self.stages[stage_name]["status"] = StageStatus.FAILED
        self.stages[stage_name]["end_time"] = time.time()
        
        if self.stages[stage_name]["start_time"]:
            duration = self.stages[stage_name]["end_time"] - self.stages[stage_name]["start_time"]
            self.stages[stage_name]["duration"] = duration
            
        # Record error
        error_info = {
            "stage": stage_name,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": time.time()
        }
        self.errors.append(error_info)
        
        logger.error(f"❌ Stage failed: {stage_name} - {type(error).__name__}: {error}")
        
    async def log_data_flow(self, key: str, data):
        """Log data flow between stages"""
        self.data_snapshots[key] = self._create_data_snapshot(data)
        logger.debug(f"📊 Data flow: {key}")
        
    async def stop(self):
        """Stop debugging session"""
        total_time = time.time() - self.start_time
        
        # Count completed and failed stages
        completed = sum(1 for s in self.stages.values() if s["status"] == StageStatus.COMPLETED)
        failed = sum(1 for s in self.stages.values() if s["status"] == StageStatus.FAILED)
        
        logger.info(f"🏁 Pipeline debug completed in {total_time:.2f}s")
        logger.info(f"📊 Summary: {completed} stages completed, {failed} failed")
        
        if self.errors:
            logger.error(f"❌ {len(self.errors)} errors occurred during pipeline execution")
            
        # Print final report
        self._print_debug_report()
            
    def _create_data_snapshot(self, data):
        """Create a snapshot of data for debugging"""
        try:
            snapshot = {
                "timestamp": time.time(),
                "type": type(data).__name__
            }
            
            # Handle different data types
            if isinstance(data, dict):
                # Limit size for visualization
                if len(data) > 10:
                    keys = list(data.keys())[:10]
                    snapshot["data"] = {k: data[k] for k in keys}
                    snapshot["truncated"] = True
                    snapshot["total_keys"] = len(data)
                else:
                    snapshot["data"] = data
                    snapshot["truncated"] = False
            elif isinstance(data, list):
                # Limit size for visualization
                if len(data) > 10:
                    snapshot["data"] = data[:10]
                    snapshot["truncated"] = True
                    snapshot["total_items"] = len(data)
                else:
                    snapshot["data"] = data
                    snapshot["truncated"] = False
            elif isinstance(data, str):
                # Limit string length
                if len(data) > 500:
                    snapshot["data"] = data[:500] + "..."
                    snapshot["truncated"] = True
                    snapshot["total_length"] = len(data)
                else:
                    snapshot["data"] = data
                    snapshot["truncated"] = False
            else:
                # For other types, use string representation
                snapshot["data"] = str(data)
                
            return snapshot
        except Exception as e:
            logger.warning(f"Failed to create data snapshot: {e}")
            return {
                "timestamp": time.time(),
                "type": type(data).__name__,
                "error": f"Failed to capture: {e}"
            }
            
    def _print_debug_report(self):
        """Print a detailed debug report"""
        print("\n" + "="*80)
        print(f"PIPELINE DEBUG REPORT: {self.command_name}")
        print(f"ID: {self.correlation_id}")
        print(f"Total time: {time.time() - self.start_time:.2f}s")
        print("="*80)
        
        # Print stages
        print("\nSTAGES:")
        for stage in sorted(self.stages.values(), key=lambda s: s["order"]):
            status_emoji = {
                StageStatus.PENDING: "⏳",
                StageStatus.RUNNING: "▶️",
                StageStatus.COMPLETED: "✅",
                StageStatus.FAILED: "❌",
                StageStatus.SKIPPED: "⏭️"
            }.get(stage["status"], "⏺️")
            
            duration = ""
            if stage["duration"] is not None:
                duration = f" ({stage['duration']:.2f}s)"
                
            print(f"{status_emoji} {stage['name']}{duration}")
            
            # Print data flow
            input_key = f"{stage['name']}_input"
            output_key = f"{stage['name']}_output"
            
            if input_key in self.data_snapshots:
                input_data = self.data_snapshots[input_key]
                print(f"  ↳ Input: {input_data.get('type', 'unknown')}")
                
            if output_key in self.data_snapshots:
                output_data = self.data_snapshots[output_key]
                print(f"  ↳ Output: {output_data.get('type', 'unknown')}")
                
        # Print errors
        if self.errors:
            print("\nERRORS:")
            for i, error in enumerate(self.errors):
                print(f"{i+1}. {error['stage']}: {error['error_type']} - {error['error_message']}")
                
        print("\n" + "="*80 + "\n")

async def simulate_ask_pipeline():
    """Simulate the /ask pipeline execution with debugging"""
    print("\n=== Starting Ask Pipeline Simulation ===\n")
    
    # Create debugger
    debugger = SimplePipelineDebugger(command_name="ask")
    await debugger.start()
    
    try:
        # Register pipeline stages
        await debugger.register_stage("initialization", "Pipeline initialization and context setup")
        await debugger.register_stage("query_processing", "Processing user query")
        await debugger.register_stage("ai_processing", "AI model processing")
        await debugger.register_stage("response_formatting", "Formatting and validating response")
        await debugger.register_stage("completion", "Pipeline completion and cleanup")
        
        # Simulate initialization stage
        await debugger.start_stage("initialization", {"query": "What is the price of AAPL?"})
        await asyncio.sleep(0.5)  # Simulate processing time
        await debugger.complete_stage("initialization", {"context": {"user_id": "123", "strict_mode": True}})
        
        # Simulate query processing stage
        await debugger.start_stage("query_processing", {"query": "What is the price of AAPL?"})
        await asyncio.sleep(1.0)  # Simulate processing time
        await debugger.complete_stage("query_processing", {"processed_query": "Get current price for AAPL"})
        
        # Simulate AI processing stage
        await debugger.start_stage("ai_processing", {"query": "Get current price for AAPL"})
        await asyncio.sleep(2.0)  # Simulate processing time
        await debugger.complete_stage("ai_processing", {"response": "The current price of AAPL is $150.25"})
        
        # Simulate response formatting stage
        await debugger.start_stage("response_formatting", {"raw_response": "The current price of AAPL is $150.25"})
        await asyncio.sleep(0.8)  # Simulate processing time
        await debugger.complete_stage("response_formatting", {"formatted_response": "The current price of AAPL is $150.25"})
        
        # Simulate completion stage
        await debugger.start_stage("completion", {"status": "completed"})
        await asyncio.sleep(0.3)  # Simulate processing time
        await debugger.complete_stage("completion", {"total_time": 4.6})
        
    finally:
        # Stop debugging
        await debugger.stop()
    
    print("\n=== Ask Pipeline Simulation Completed ===\n")

async def simulate_analyze_pipeline(should_fail=False):
    """Simulate the /analyze pipeline execution with debugging"""
    print("\n=== Starting Analyze Pipeline Simulation ===\n")
    
    # Create debugger
    debugger = SimplePipelineDebugger(command_name="analyze")
    await debugger.start()
    
    try:
        # Register pipeline stages
        await debugger.register_stage("initialization", "Pipeline initialization and context setup")
        await debugger.register_stage("symbol_validation", "Validating stock symbol")
        await debugger.register_stage("market_data_retrieval", "Retrieving market data")
        await debugger.register_stage("technical_analysis", "Performing technical analysis")
        await debugger.register_stage("sentiment_analysis", "Analyzing market sentiment")
        await debugger.register_stage("response_generation", "Generating analysis response")
        await debugger.register_stage("completion", "Pipeline completion and cleanup")
        
        # Simulate initialization stage
        await debugger.start_stage("initialization", {"symbol": "TSLA", "indicators": "RSI,MACD"})
        await asyncio.sleep(0.5)  # Simulate processing time
        await debugger.complete_stage("initialization", {"context": {"user_id": "123", "strict_mode": True}})
        
        # Simulate symbol validation stage
        await debugger.start_stage("symbol_validation", {"symbol": "TSLA"})
        await asyncio.sleep(0.7)  # Simulate processing time
        await debugger.complete_stage("symbol_validation", {"valid": True, "exchange": "NASDAQ"})
        
        # Simulate market data retrieval stage
        await debugger.start_stage("market_data_retrieval", {"symbol": "TSLA", "timeframe": "1d"})
        await asyncio.sleep(1.5)  # Simulate processing time
        await debugger.complete_stage("market_data_retrieval", {
            "price": 242.50,
            "volume": 25000000,
            "high": 245.30,
            "low": 238.75
        })
        
        # Simulate technical analysis stage
        await debugger.start_stage("technical_analysis", {"symbol": "TSLA", "indicators": "RSI,MACD"})
        await asyncio.sleep(2.0)  # Simulate processing time
        await debugger.complete_stage("technical_analysis", {
            "rsi": 65.4,
            "macd": 2.3,
            "macd_signal": 1.8,
            "trend": "bullish"
        })
        
        # Simulate sentiment analysis stage
        await debugger.start_stage("sentiment_analysis", {"symbol": "TSLA"})
        await asyncio.sleep(1.2)  # Simulate processing time
        
        if should_fail:
            # Simulate failure
            await debugger.fail_stage("sentiment_analysis", Exception("Failed to retrieve sentiment data"))
            return
        else:
            await debugger.complete_stage("sentiment_analysis", {
                "sentiment": "bullish",
                "sentiment_score": 0.75,
                "news_count": 12
            })
        
        # Simulate response generation stage
        await debugger.start_stage("response_generation", {"symbol": "TSLA", "analysis": "Technical and sentiment"})
        await asyncio.sleep(0.8)  # Simulate processing time
        await debugger.complete_stage("response_generation", {
            "response": "TSLA analysis shows bullish trend with RSI at 65.4 and positive MACD."
        })
        
        # Simulate completion stage
        await debugger.start_stage("completion", {"status": "completed"})
        await asyncio.sleep(0.3)  # Simulate processing time
        await debugger.complete_stage("completion", {"total_time": 7.0})
        
    finally:
        # Stop debugging
        await debugger.stop()
    
    print("\n=== Analyze Pipeline Simulation Completed ===\n")

async def main():
    """Run the pipeline debug tests"""
    print("Starting Pipeline Debug Tests")
    
    # Test Ask Pipeline
    await simulate_ask_pipeline()
    
    # Test Analyze Pipeline (success case)
    await simulate_analyze_pipeline(should_fail=False)
    
    # Test Analyze Pipeline (failure case)
    await simulate_analyze_pipeline(should_fail=True)
    
    print("All Pipeline Debug Tests Completed")

if __name__ == "__main__":
    asyncio.run(main())
