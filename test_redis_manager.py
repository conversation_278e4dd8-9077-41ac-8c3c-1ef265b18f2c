#!/usr/bin/env python3
"""
Test script for the centralized Redis connection manager
"""

import asyncio
import sys
import os
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Redis manager
from src.shared.redis import redis_manager, get_redis_client

async def test_redis_connection():
    """Test the Redis connection manager"""
    logger.info("Testing Redis connection manager...")
    
    # Initialize Redis manager
    await redis_manager.initialize()
    
    # Get Redis client
    redis_client = await get_redis_client()
    
    if redis_client is None:
        logger.error("Failed to get Redis client")
        return False
    
    # Test basic operations
    try:
        # Set a test key
        test_key = "test:redis:manager"
        test_value = "Connection test successful"
        
        logger.info(f"Setting test key: {test_key}")
        await redis_client.set(test_key, test_value)
        
        # Get the test key
        result = await redis_client.get(test_key)
        logger.info(f"Retrieved test key: {result}")
        
        # Verify the result
        assert result == test_value, f"Expected '{test_value}', got '{result}'"
        
        # Delete the test key
        await redis_client.delete(test_key)
        logger.info("Test key deleted")
        
        logger.info("Redis connection test successful!")
        return True
        
    except Exception as e:
        logger.error(f"Redis test failed: {e}")
        return False
    finally:
        # Close the connection
        await redis_manager.close()
        logger.info("Redis connection closed")

if __name__ == "__main__":
    asyncio.run(test_redis_connection())
