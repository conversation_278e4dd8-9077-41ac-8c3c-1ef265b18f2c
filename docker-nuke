#!/bin/bash

echo "🚀 DOCKER NUKE - Complete Cleanup & Rebuild"
echo "=============================================="

echo ""
echo "💥 Step 1: Stopping and removing all containers..."
docker compose down --volumes --remove-orphans

echo ""
echo "🧹 Step 2: Removing all Docker images and build cache..."
docker system prune -a --volumes -f

echo ""
echo "🔨 Step 3: Rebuilding all containers from scratch..."
docker compose build --no-cache

echo ""
echo "🚀 Step 4: Starting fresh containers..."
docker compose up -d

echo ""
echo "✅ DOCKER NUKE COMPLETE!"
echo "All containers have been rebuilt with fresh code."
echo ""
echo "To check logs: docker compose logs -f"
echo "To stop: docker compose down" 