-- Initialize TradingView Automation Database
-- This script creates the necessary users, databases, and basic structure

-- Create the tradingview_user if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'tradingview_user') THEN
        CREATE ROLE tradingview_user WITH LOGIN PASSWORD 'tradingview_pass';
    END IF;
END
$$;

-- Grant necessary privileges
ALTER ROLE tradingview_user CREATEDB;
ALTER ROLE tradingview_user WITH SUPERUSER;

-- Create the database if it doesn't exist
SELECT 'CREATE DATABASE tradingview_data'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'tradingview_data')\gexec

-- Connect to the tradingview_data database
\c tradingview_data;

-- Grant privileges on the database
GRANT ALL PRIVILEGES ON DATABASE tradingview_data TO tradingview_user;
GRANT ALL ON SCHEMA public TO tradingview_user;

-- Create basic tables for webhook processing
CREATE TABLE IF NOT EXISTS webhook_alerts (
    id SERIAL PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL,
    signal VARCHAR(50) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    timestamp BIGINT NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    entry_price DECIMAL(10,4),
    tp1_price DECIMAL(10,4),
    tp2_price DECIMAL(10,4),
    tp3_price DECIMAL(10,4),
    sl_price DECIMAL(10,4),
    raw_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trading signals table for signal storage
CREATE TABLE IF NOT EXISTS trading_signals (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    signal_type VARCHAR(50) NOT NULL,
    confidence DECIMAL(3,2),
    price DECIMAL(10,4),
    timestamp TIMESTAMP NOT NULL,
    indicators_used TEXT,
    reasoning TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_webhook_alerts_symbol ON webhook_alerts(symbol);
CREATE INDEX IF NOT EXISTS idx_webhook_alerts_timestamp ON webhook_alerts(timestamp);
CREATE INDEX IF NOT EXISTS idx_webhook_alerts_alert_type ON webhook_alerts(alert_type);

CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol ON trading_signals(symbol);
CREATE INDEX IF NOT EXISTS idx_trading_signals_timestamp ON trading_signals(timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_signals_signal_type ON trading_signals(signal_type);

-- Grant table privileges
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tradingview_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tradingview_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO tradingview_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO tradingview_user; 