<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Network Architecture</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
            color: white;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .network-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .network-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .network-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 212, 255, 0.2);
        }
        
        .network-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .network-internal .network-title { color: #ff6b6b; }
        .network-webhook .network-title { color: #4ecdc4; }
        .network-external .network-title { color: #45b7d1; }
        
        .service {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
            cursor: pointer;
            border-left: 4px solid transparent;
        }
        
        .service:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateX(5px);
        }
        
        .service.healthy { border-left-color: #00ff88; }
        .service.starting { border-left-color: #ffaa00; }
        .service.failing { border-left-color: #ff4757; }
        
        .service-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .service-name {
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .service-port {
            font-size: 0.9em;
            opacity: 0.7;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-healthy {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
        }
        
        .status-starting {
            background: rgba(255, 170, 0, 0.2);
            color: #ffaa00;
        }
        
        .status-failing {
            background: rgba(255, 71, 87, 0.2);
            color: #ff4757;
        }
        
        .flow-diagram {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .flow-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
            color: #00d4ff;
        }
        
        .flow-path {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .flow-node {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 0.9em;
            transition: all 0.2s ease;
        }
        
        .flow-node:hover {
            background: rgba(0, 212, 255, 0.2);
            transform: scale(1.05);
        }
        
        .flow-arrow {
            font-size: 1.2em;
            color: #00d4ff;
            margin: 0 5px;
        }
        
        .port-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .port-table th,
        .port-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .port-table th {
            background: rgba(0, 212, 255, 0.2);
            font-weight: bold;
            color: #00d4ff;
        }
        
        .port-table tr:hover {
            background: rgba(255, 255, 255, 0.08);
        }
        
        .issue-panel {
            background: linear-gradient(135deg, rgba(255, 71, 87, 0.1), rgba(255, 71, 87, 0.05));
            border: 1px solid rgba(255, 71, 87, 0.3);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .issue-title {
            color: #ff4757;
            font-size: 1.5em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .solution {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .solution-title {
            color: #00ff88;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            border-left: 4px solid #00d4ff;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            gap: 5px;
        }
        
        .tab {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 10px 10px 0 0;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .tab.active {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .network-grid {
                grid-template-columns: 1fr;
            }
            
            .flow-path {
                flex-direction: column;
                align-items: stretch;
            }
            
            .flow-node {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 TradingView Network Architecture</h1>
            <p>Interactive visualization of container networks, ports, and communication flows</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('overview')">Network Overview</button>
            <button class="tab" onclick="showTab('flows')">Data Flows</button>
            <button class="tab" onclick="showTab('ports')">Port Mapping</button>
            <button class="tab" onclick="showTab('issues')">Issues & Solutions</button>
            <button class="tab" onclick="showTab('debug')">Debug Console</button>
        </div>
        
        <div id="overview" class="tab-content active">
            <div class="network-grid">
                <div class="network-section network-internal">
                    <div class="network-title">
                        🔒 Internal Network
                        <small>(**********/16)</small>
                    </div>
                    <div class="service healthy" onclick="showServiceDetails('postgres')">
                        <div class="service-info">
                            <div class="service-name">PostgreSQL</div>
                            <div class="service-port">Port: 5432</div>
                        </div>
                        <div class="status-badge status-healthy">✅ Healthy</div>
                    </div>
                    <div class="service healthy" onclick="showServiceDetails('redis')">
                        <div class="service-info">
                            <div class="service-name">Redis</div>
                            <div class="service-port">Port: 6379</div>
                        </div>
                        <div class="status-badge status-healthy">✅ Healthy</div>
                    </div>
                    <div class="service healthy" onclick="showServiceDetails('api')">
                        <div class="service-info">
                            <div class="service-name">Trading API</div>
                            <div class="service-port">Port: 8000</div>
                        </div>
                        <div class="status-badge status-healthy">✅ Healthy</div>
                    </div>
                </div>
                
                <div class="network-section network-webhook">
                    <div class="network-title">
                        🌐 Webhook Network
                        <small>(Bridge)</small>
                    </div>
                    <div class="service healthy" onclick="showServiceDetails('webhook-ingest')">
                        <div class="service-info">
                            <div class="service-name">Webhook Ingest</div>
                            <div class="service-port">Port: 8001</div>
                        </div>
                        <div class="status-badge status-healthy">✅ Healthy</div>
                    </div>
                    <div class="service healthy" onclick="showServiceDetails('webhook-proxy')">
                        <div class="service-info">
                            <div class="service-name">Webhook Proxy</div>
                            <div class="service-port">Port: 8001</div>
                        </div>
                        <div class="status-badge status-healthy">✅ Healthy</div>
                    </div>
                    <div class="service starting" onclick="showServiceDetails('ngrok')">
                        <div class="service-info">
                            <div class="service-name">Ngrok Tunnel</div>
                            <div class="service-port">Port: 4040</div>
                        </div>
                        <div class="status-badge status-starting">🔄 Starting</div>
                    </div>
                </div>
                
                <div class="network-section network-external" style="grid-column: 1 / -1;">
                    <div class="network-title">
                        🌍 External Network
                        <small>(Public Access)</small>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                        <div class="service starting" onclick="showServiceDetails('nginx')">
                            <div class="service-info">
                                <div class="service-name">Nginx</div>
                                <div class="service-port">Ports: 80/443</div>
                            </div>
                            <div class="status-badge status-starting">🔄 Starting</div>
                        </div>
                        <div class="service healthy" onclick="showServiceDetails('webhook-proxy-ext')">
                            <div class="service-info">
                                <div class="service-name">Webhook Proxy</div>
                                <div class="service-port">Port: 8001</div>
                            </div>
                            <div class="status-badge status-healthy">✅ Healthy</div>
                        </div>
                        <div class="service failing" onclick="showServiceDetails('discord-bot')">
                            <div class="service-info">
                                <div class="service-name">Discord Bot</div>
                                <div class="service-port">External API</div>
                            </div>
                            <div class="status-badge status-failing">❌ DNS Fail</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="flows" class="tab-content">
            <div class="flow-diagram">
                <div class="flow-title">📡 Webhook Processing Flow</div>
                <div class="flow-path">
                    <div class="flow-node">TradingView</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">nginx:443</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">webhook-proxy:8001</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">webhook-ingest:8001</div>
                </div>
                <div class="flow-path">
                    <div class="flow-node">Redis Queue</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">Discord Bot</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">Database</div>
                </div>
            </div>
            
            <div class="flow-diagram">
                <div class="flow-title">🔧 API Access Flow</div>
                <div class="flow-path">
                    <div class="flow-node">External Request</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">nginx:443</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">api:8000</div>
                </div>
            </div>
            
            <div class="flow-diagram">
                <div class="flow-title">🤖 Discord Bot Communication</div>
                <div class="flow-path">
                    <div class="flow-node">discord-bot</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">postgres:5432</div>
                </div>
                <div class="flow-path">
                    <div class="flow-node">discord-bot</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">redis:6379</div>
                </div>
                <div class="flow-path">
                    <div class="flow-node">discord-bot</div>
                    <span class="flow-arrow">❌</span>
                    <div class="flow-node">discord.com:443</div>
                </div>
            </div>
        </div>
        
        <div id="ports" class="tab-content">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🌐 Localhost Exposed Ports</h3>
            <table class="port-table">
                <thead>
                    <tr>
                        <th>Port</th>
                        <th>Service</th>
                        <th>Container</th>
                        <th>Access Level</th>
                        <th>Purpose</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>80</td>
                        <td>HTTP</td>
                        <td>nginx</td>
                        <td>Public</td>
                        <td>HTTP → HTTPS redirect</td>
                    </tr>
                    <tr>
                        <td>443</td>
                        <td>HTTPS</td>
                        <td>nginx</td>
                        <td>Public</td>
                        <td>Main application access</td>
                    </tr>
                    <tr>
                        <td>8001</td>
                        <td>Webhook</td>
                        <td>webhook-proxy</td>
                        <td>Public</td>
                        <td>TradingView webhook endpoint</td>
                    </tr>
                    <tr>
                        <td>4040</td>
                        <td>Ngrok</td>
                        <td>ngrok</td>
                        <td>Public</td>
                        <td>Ngrok tunnel management</td>
                    </tr>
                </tbody>
            </table>
            
            <h3 style="color: #ff6b6b; margin: 30px 0 15px;">🔒 Internal Only Ports</h3>
            <table class="port-table">
                <thead>
                    <tr>
                        <th>Port</th>
                        <th>Service</th>
                        <th>Container</th>
                        <th>Network</th>
                        <th>Purpose</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>5432</td>
                        <td>PostgreSQL</td>
                        <td>postgres</td>
                        <td>internal</td>
                        <td>Database</td>
                    </tr>
                    <tr>
                        <td>6379</td>
                        <td>Redis</td>
                        <td>redis</td>
                        <td>internal</td>
                        <td>Cache/Queue</td>
                    </tr>
                    <tr>
                        <td>8000</td>
                        <td>API</td>
                        <td>api</td>
                        <td>internal</td>
                        <td>Main trading API</td>
                    </tr>
                    <tr>
                        <td>8001</td>
                        <td>Webhook</td>
                        <td>webhook-ingest</td>
                        <td>webhook+internal</td>
                        <td>Webhook processing</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="issues" class="tab-content">
            <div class="issue-panel">
                <div class="issue-title">
                    ⚠️ Current Network Issues
                </div>
                
                <h4 style="color: #ff4757; margin: 20px 0 10px;">1. Discord Bot DNS Resolution</h4>
                <p><strong>Status:</strong> ❌ FAILING</p>
                <p><strong>Error:</strong> Cannot connect to host discord.com:443 ssl:default [Temporary failure in name resolution]</p>
                <p><strong>Root Cause:</strong> Network isolation preventing external DNS</p>
                
                <div class="solution">
                    <div class="solution-title">💡 Recommended Solution</div>
                    <p>Update your docker-compose.yml to give the Discord bot access to both external and internal networks:</p>
                    
                    <div class="code-block">discord-bot:
  networks:
    - external-network  # For Discord API access
    - internal-network  # For database/cache access
  dns:
    - *******
    - *******</div>
                    
                    <p>And ensure your networks are configured correctly:</p>
                    
                    <div class="code-block">networks:
  external-network:
    driver: bridge
    # Allow both external and internal communication
  
  internal-network:
    driver: bridge
    internal: true  # Keep core services isolated</div>
                </div>
                
                <h4 style="color: #ffaa00; margin: 30px 0 10px;">2. Service Connectivity Status</h4>
                <p>✅ postgres → redis (healthy)</p>
                <p>✅ webhook-ingest → postgres + redis (healthy)</p>
                <p>✅ webhook-proxy → webhook-ingest (healthy)</p>
                <p>🔄 nginx → api + webhook-proxy (starting)</p>
                <p>🔄 ngrok → webhook-proxy (starting)</p>
                <p>❌ discord-bot → external DNS (failing)</p>
                
                <div class="solution">
                    <div class="solution-title">🔧 Next Steps</div>
                    <ol style="margin-left: 20px; line-height: 1.6;">
                        <li>Fix Discord Bot Network: Allow external DNS while maintaining internal access</li>
                        <li>Verify Ngrok Tunnel: Ensure webhook endpoint is publicly accessible</li>
                        <li>Test Complete Flow: Webhook → Processing → Discord Bot → Database</li>
                        <li>Monitor Health: All services should show healthy status</li>
                        <li>Security Validation: Ensure only intended endpoints are exposed</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Debug Console Tab -->
        <div id="debug" class="tab-content">
            <div class="flow-diagram">
                <div class="flow-title">🛠️ Debug Console</div>
                <div style="display:flex; gap:20px; flex-wrap:wrap;">
                    <div style="flex:1 1 480px;">
                        <div class="issue-panel">
                            <div class="solution-title">Command Log</div>
                            <div id="command-log" class="code-block" style="height:260px; overflow:auto; white-space:pre-wrap;"></div>
                            <div style="margin-top:10px; display:flex; gap:10px;">
                                <button class="tab" onclick="simulateCommand('/analyze GME')">Simulate /analyze</button>
                                <button class="tab" onclick="simulateCommand('/zones GME')">Simulate /zones</button>
                                <button class="tab" onclick="simulateCommand('/ask What is the price of AAPL?')">Simulate /ask</button>
                                <button class="tab" onclick="clearLog()">Clear Log</button>
                            </div>
                        </div>
                    </div>
                    <div style="flex:1 1 360px;">
                        <div class="issue-panel">
                            <div class="solution-title">Pipeline</div>
                            <ul id="pipeline-steps" style="list-style:none; padding-left:0;">
                                <!-- dynamic -->
                            </ul>
                            <div style="margin-top:12px;">
                                <button class="tab" onclick="resetPipeline()">Reset</button>
                                <button class="tab" onclick="advancePipeline()">Advance</button>
                            </div>
                            <hr/>
                            <div class="solution-title">Tools Used</div>
                            <ul id="tools-used" style="list-style:none; padding-left:0;">
                                <!-- dynamic -->
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Debug Console helpers
        const commandLogEl = () => document.getElementById('command-log');
        const pipelineStepsEl = () => document.getElementById('pipeline-steps');
        const toolsUsedEl = () => document.getElementById('tools-used');

        const pipelineState = [
            'receive_request',
            'fetch_market_data',
            'validate_data',
            'technical_analysis',
            'enhanced_zones',
            'ai_summarization',
            'format_response',
            'send_response'
        ];
        let pipelineIndex = 0;
        let usedTools = new Set(['PolygonProvider', 'FinnhubProvider', 'YFinance', 'OpenRouter']);

        function logCommand(msg) {
            const el = commandLogEl();
            if (!el) return;
            const time = new Date().toISOString();
            el.textContent = `${time}  ${msg}\n` + el.textContent;
        }

        function simulateCommand(cmd) {
            logCommand(`SIMULATED: ${cmd}`);
            // Reset pipeline and show
            pipelineIndex = 0;
            renderPipeline();
            renderTools();
        }

        function clearLog() {
            const el = commandLogEl(); if (el) el.textContent = '';
        }

        function renderPipeline() {
            const ul = pipelineStepsEl();
            if (!ul) return;
            ul.innerHTML = '';
            for (let i = 0; i < pipelineState.length; i++) {
                const li = document.createElement('li');
                li.style.padding = '8px 0';
                li.style.display = 'flex';
                li.style.justifyContent = 'space-between';
                li.innerHTML = `<span>${pipelineState[i]}</span><span>${i < pipelineIndex ? '✅' : i === pipelineIndex ? '⏳' : '⏸️'}</span>`;
                ul.appendChild(li);
            }
        }

        function advancePipeline() {
            if (pipelineIndex < pipelineState.length) pipelineIndex++;
            renderPipeline();
        }

        function resetPipeline() {
            pipelineIndex = 0; renderPipeline();
        }

        function renderTools() {
            const ul = toolsUsedEl(); if (!ul) return;
            ul.innerHTML = '';
            Array.from(usedTools).forEach(t => {
                const li = document.createElement('li');
                li.textContent = `• ${t}`;
                ul.appendChild(li);
            });
        }

        // Initialize debug ui
        document.addEventListener('DOMContentLoaded', function() {
            renderPipeline(); renderTools();
        });

        function showTab(tabName) {
            // Hide all tab contents
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        function showServiceDetails(serviceName) {
            const serviceInfo = {
                'postgres': {
                    name: 'PostgreSQL Database',
                    network: 'Internal Network (**********/16)',
                    port: '5432',
                    status: 'Healthy ✅',
                    access: 'Internal only - no external access',
                    purpose: 'Primary database for storing trading data, user configurations, and system logs'
                },
                'redis': {
                    name: 'Redis Cache/Queue',
                    network: 'Internal Network (**********/16)',
                    port: '6379',
                    status: 'Healthy ✅',
                    access: 'Internal only - no external access',
                    purpose: 'Caching layer and message queue for webhook processing'
                },
                'api': {
                    name: 'Trading API',
                    network: 'Internal Network (**********/16)',
                    port: '8000',
                    status: 'Healthy ✅',
                    access: 'Internal only - accessed via nginx proxy',
                    purpose: 'Main trading logic API, handles orders and strategy execution'
                },
                'discord-bot': {
                    name: 'Discord Bot',
                    network: 'External Network (needs both external and internal)',
                    port: 'N/A (Discord API client)',
                    status: 'DNS Resolution Failing ❌',
                    access: 'Needs external DNS access for Discord API',
                    purpose: 'Sends notifications and alerts to Discord channels'
                }
            };
            
            const service = serviceInfo[serviceName];
            if (service) {
                alert(`🔍 Service Details\n\nName: ${service.name}\nNetwork: ${service.network}\nPort: ${service.port}\nStatus: ${service.status}\nAccess: ${service.access}\nPurpose: ${service.purpose}`);
            }
        }
        
        // Add some dynamic effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects for network sections
            const sections = document.querySelectorAll('.network-section');
            sections.forEach(section => {
                section.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });
                
                section.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // Simulate status updates
            setInterval(() => {
                const services = document.querySelectorAll('.service.starting');
                services.forEach(service => {
                    if (Math.random() > 0.7) {
                        const badge = service.querySelector('.status-badge');
                        if (badge.textContent.includes('Starting')) {
                            badge.textContent = '✅ Healthy';
                            badge.className = 'status-badge status-healthy';
                            service.className = 'service healthy';
                        }
                    }
                });
            }, 5000);

            // SSE connection to backend debug stream
            try {
                const sseUrl = '/api/debug/stream';
                const evtSource = new EventSource(sseUrl);
                evtSource.onmessage = (e) => {
                    try {
                        const payload = JSON.parse(e.data);
                        handleDebugEvent(payload);
                    } catch (err) {
                        console.error('Invalid debug event', err);
                    }
                };
                evtSource.onerror = (err) => {
                    console.warn('SSE error', err);
                };
            } catch (err) {
                console.warn('SSE not available', err);
            }
        });

        // Post helper to backend debug endpoints
        async function postDebug(path, body) {
            try {
                await fetch('/api/debug' + path, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(body)
                });
            } catch (err) {
                console.error('Failed to post debug event', err);
            }
        }

        // Update simulateCommand to POST to server as well as local log
        function simulateCommand(cmd) {
            logCommand(`SIMULATED: ${cmd}`);
            postDebug('/command', { command: cmd });
            pipelineIndex = 0;
            renderPipeline();
            renderTools();
        }

        // Handle incoming debug events (SSE)
        function handleDebugEvent(payload) {
            if (!payload || !payload.type) return;
            if (payload.type === 'command') {
                logCommand(`REMOTE CMD: ${payload.command}`);
            } else if (payload.type === 'log') {
                logCommand(`${payload.level.toUpperCase()}: ${payload.message}`);
            } else if (payload.type === 'tool') {
                usedTools.add(payload.tool);
                renderTools();
                logCommand(`TOOL: ${payload.tool} ${JSON.stringify(payload.details || {})}`);
            } else {
                logCommand(`EVENT: ${JSON.stringify(payload)}`);
            }
        }
    </script>
</body>
</html>