#!/usr/bin/env python3
"""
Test script to verify the improved market hours validation and data fetching logic.
"""

import asyncio
import logging
from datetime import datetime
from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_technical_analysis_formatter():
    """Test the new TechnicalAnalysisFormatter class."""
    
    print("\n🧪 Testing TechnicalAnalysisFormatter")
    print("=" * 50)
    
    try:
        from src.core.formatting.technical_analysis import TechnicalAnalysisFormatter
        
        # Test data availability checks
        test_symbol_data = {
            'AAPL': {
                'current_price': 150.0,
                'rsi': 65.5,
                'macd': 0.5,
                'sma_20': 148.0,
                'historical': [{'date': '2024-01-01', 'close': 150.0}]
            },
            'TSLA': {
                'current_price': 250.0,
                'technical_indicators_available': True
            },
            'NVDA': {
                'current_price': None,
                'rsi': None
            }
        }
        
        # Test individual symbol checks
        for symbol, data in test_symbol_data.items():
            print(f"\n📊 Testing {symbol}:")
            print(f"   - Has price data: {TechnicalAnalysisFormatter.has_price_data(data)}")
            print(f"   - Has technical data: {TechnicalAnalysisFormatter.has_technical_data(data)}")
            print(f"   - Has historical data: {TechnicalAnalysisFormatter.has_historical_data(data)}")
        
        # Test data availability summary
        availability = TechnicalAnalysisFormatter.get_data_availability_summary(test_symbol_data)
        print(f"\n📈 Overall Data Availability:")
        print(f"   - Price data: {availability['has_price_data']}")
        print(f"   - Technical data: {availability['has_technical_data']}")
        print(f"   - Historical data: {availability['has_historical_data']}")
        
        # Test indicator formatting
        print(f"\n🔧 Indicator Formatting for AAPL:")
        formatted = TechnicalAnalysisFormatter.format_indicators_summary(test_symbol_data['AAPL'])
        print(formatted)
        
        # Test market status notice
        print(f"\n📊 Market Status Notice (Closed):")
        notice = TechnicalAnalysisFormatter.format_market_status_notice(
            "🔴 **Markets CLOSED** (5:00 PM ET)",
            test_symbol_data
        )
        print(notice[:200] + "..." if len(notice) > 200 else notice)
        
    except Exception as e:
        print(f"❌ Error testing TechnicalAnalysisFormatter: {e}")
        import traceback
        traceback.print_exc()

async def test_market_hours_validation():
    """Test the improved market hours validation logic."""
    
    # Test queries that should work even when markets are closed
    test_queries = [
        "What is the current price of AAPL?",
        "Show me the RSI for TSLA",
        "What are the support and resistance levels for NVDA?",
        "Explain how MACD works",
        "What is the current value of MSFT stock?",
        "Show me the price of AMD",
        "What are the technical indicators for GOOGL?",
        "How to calculate moving averages",
        "What is the current stock price of META?"
    ]
    
    print("🧪 Testing Market Hours Validation Logic")
    print("=" * 50)
    
    for query in test_queries:
        print(f"\n📝 Query: {query}")
        
        # Test the validation logic
        try:
            from src.core.utils import validate_market_hours_for_query
            validation = validate_market_hours_for_query(query)
            
            print(f"   ✅ Appropriate: {validation['appropriate']}")
            print(f"   📊 Requires real-time: {validation['requires_real_time']}")
            print(f"   📈 Can use delayed data: {validation['can_use_delayed_data']}")
            print(f"   🎓 Educational: {validation['is_educational']}")
            print(f"   🕐 Markets open: {validation['markets_open']}")
            print(f"   💡 Recommendation: {validation['recommendation']}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)

async def test_data_fetching():
    """Test the improved data fetching logic."""
    
    print("\n🧪 Testing Data Fetching Logic")
    print("=" * 50)
    
    # Test with a simple query
    test_query = "What is the current price of AAPL and show me its RSI?"
    
    print(f"📝 Test Query: {test_query}")
    
    try:
        # Create processor
        processor = AIChatProcessor()
        
        # Process the query
        print("🔄 Processing query...")
        result = await processor.process(test_query)
        
        print(f"✅ Response received!")
        print(f"📊 Intent: {result.get('intent')}")
        print(f"🔤 Symbols: {result.get('symbols')}")
        print(f"🛠️ Tools required: {result.get('tools_required')}")
        
        # Check the data
        data = result.get('data', {})
        if data:
            print(f"📈 Data received for {len(data)} symbols:")
            for symbol, symbol_data in data.items():
                print(f"   {symbol}:")
                print(f"     - Price: ${symbol_data.get('current_price', 'N/A')}")
                print(f"     - Change: {symbol_data.get('change_percent', 'N/A')}%")
                print(f"     - Volume: {symbol_data.get('volume', 'N/A')}")
                print(f"     - RSI: {symbol_data.get('rsi', 'N/A')}")
                print(f"     - Data available: {symbol_data.get('data_available', 'N/A')}")
                print(f"     - Provider: {symbol_data.get('provider', 'N/A')}")
        else:
            print("❌ No data received")
        
        # Check the response
        response = result.get('response', '')
        if response:
            print(f"\n📝 Response preview: {response[:200]}...")
        else:
            print("❌ No response received")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    print("🚀 Starting Market Hours Fix Tests")
    print("=" * 60)
    
    # Test the new TechnicalAnalysisFormatter
    await test_technical_analysis_formatter()
    
    # Test market hours validation
    await test_market_hours_validation()
    
    # Test data fetching
    await test_data_fetching()
    
    print("\n🎉 Testing completed!")

if __name__ == "__main__":
    asyncio.run(main()) 