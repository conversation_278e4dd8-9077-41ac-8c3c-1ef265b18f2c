#!/usr/bin/env python3
"""
Test script for message length enforcement functionality.

This script tests the message length enforcement without requiring Discord.
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_message_length_enforcement():
    """Test message length enforcement without Discord dependency"""
    print("Testing message length enforcement...")
    
    try:
        # Import just the enforcement function without Discord
        import re
        
        def enforce_message_limit(
            message: str, 
            limit: int = 2000,
            truncation_suffix: str = "\n\n... (message truncated due to length)"
        ) -> str:
            """Standalone version of message length enforcement"""
            if not message:
                return ""
                
            if len(message) <= limit:
                return message
                
            # Calculate available space for content
            available_space = limit - len(truncation_suffix)
            
            if available_space <= 0:
                # Suffix is too long, just truncate without suffix
                return message[:limit]
                
            # Try to truncate at a natural break point
            truncated = message[:available_space]
            
            # Look for natural break points in reverse order of preference
            break_points = ['\n\n', '\n', '. ', '! ', '? ', ', ', ' ']
            
            for break_point in break_points:
                last_break = truncated.rfind(break_point)
                if last_break > available_space * 0.7:  # Don't truncate too aggressively
                    truncated = truncated[:last_break + len(break_point)]
                    break
                    
            return truncated + truncation_suffix
        
        # Test cases
        print("\n1. Testing message length enforcement:")
        
        # Short message (should pass through unchanged)
        short_msg = "This is a short message"
        result = enforce_message_limit(short_msg)
        if result == short_msg:
            print(f"  ✅ Short message preserved")
        else:
            print(f"  ❌ Short message changed unexpectedly")
            return False
        
        # Long message (should be truncated)
        long_msg = "A" * 2500  # Longer than Discord's 2000 char limit
        result = enforce_message_limit(long_msg)
        if len(result) <= 2000 and "truncated" in result:
            print(f"  ✅ Long message truncated: {len(result)} chars")
        else:
            print(f"  ❌ Long message not properly truncated: {len(result)} chars")
            return False
        
        # Message with natural break points
        natural_msg = "This is sentence one. " * 100  # Creates natural break points
        result = enforce_message_limit(natural_msg)
        if len(result) <= 2000 and result.endswith(". ... (message truncated due to length)"):
            print(f"  ✅ Natural break point truncation: {len(result)} chars")
        else:
            print(f"  ❌ Natural break point truncation failed: {len(result)} chars")
            return False
        
        # Test with custom limit
        custom_result = enforce_message_limit("A" * 150, limit=100)
        if len(custom_result) <= 100:
            print(f"  ✅ Custom limit respected: {len(custom_result)} chars")
        else:
            print(f"  ❌ Custom limit not respected: {len(custom_result)} chars")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing message length enforcement: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_code_block_formatting():
    """Test code block formatting without Discord dependency"""
    print("\nTesting code block formatting...")
    
    try:
        def format_code_block(content: str, language: str = "") -> str:
            """Standalone version of code block formatting"""
            if not content:
                return ""
                
            # Account for code block formatting
            code_block_overhead = len(f"```{language}\n```")
            available_space = 2000 - code_block_overhead
            
            # Truncate content if necessary
            if len(content) > available_space:
                content = content[:available_space - 20] + "\n... (truncated)"
                
            return f"```{language}\n{content}\n```"
        
        # Test cases
        print("\n1. Testing code block formatting:")
        
        # Simple code
        code_content = "print('Hello, world!')"
        code_block = format_code_block(code_content, "python")
        if code_block.startswith("```python") and code_block.endswith("```"):
            print(f"  ✅ Code block formatted correctly")
        else:
            print(f"  ❌ Code block not formatted correctly")
            return False
        
        # Long code (should be truncated)
        long_code = "print('line')\n" * 200
        long_code_block = format_code_block(long_code, "python")
        if len(long_code_block) <= 2000 and "truncated" in long_code_block:
            print(f"  ✅ Long code block truncated: {len(long_code_block)} chars")
        else:
            print(f"  ❌ Long code block not properly truncated: {len(long_code_block)} chars")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing code block formatting: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("MESSAGE LENGTH ENFORCEMENT TEST")
    print("=" * 60)
    
    # Test message length enforcement
    length_test_passed = test_message_length_enforcement()
    
    # Test code block formatting
    code_test_passed = test_code_block_formatting()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"Message Length Enforcement: {'✅ PASSED' if length_test_passed else '❌ FAILED'}")
    print(f"Code Block Formatting: {'✅ PASSED' if code_test_passed else '❌ FAILED'}")
    
    if length_test_passed and code_test_passed:
        print("\n🎉 All tests passed! The message utilities are working correctly.")
        return True
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
