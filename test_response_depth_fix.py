#!/usr/bin/env python3
"""
Test script to verify the ResponseDepth .get() method fix.

This test verifies that the Alpha Vantage provider correctly handles
nested configuration access without causing AttributeError.
"""

import sys
import os
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_alpha_vantage_config_access():
    """Test that Alpha Vantage provider handles config.get() correctly"""
    print("🧪 Testing Alpha Vantage configuration access...")

    try:
        # Test the specific fix: config.get() with proper nested access
        print("  ✅ Test 1: Nested config.get() access pattern")

        # This is the pattern that was causing the error:
        # config.get('data_providers', 'alpha_vantage', {})  # WRONG - 3 arguments

        # This is the correct pattern:
        # config.get('data_providers', {}).get('alpha_vantage', {})  # CORRECT - 2 arguments each

        # Test with None config
        config = None
        provider_config = config.get('data_providers', {}).get('alpha_vantage', {}) if config else {}
        print(f"     None config result: {provider_config}")
        assert provider_config == {}

        # Test with empty config
        config = {}
        provider_config = config.get('data_providers', {}).get('alpha_vantage', {})
        print(f"     Empty config result: {provider_config}")
        assert provider_config == {}

        # Test with config missing data_providers
        config = {"other_section": {"key": "value"}}
        provider_config = config.get('data_providers', {}).get('alpha_vantage', {})
        print(f"     Missing data_providers result: {provider_config}")
        assert provider_config == {}

        # Test with config missing alpha_vantage
        config = {"data_providers": {"other_provider": {"key": "value"}}}
        provider_config = config.get('data_providers', {}).get('alpha_vantage', {})
        print(f"     Missing alpha_vantage result: {provider_config}")
        assert provider_config == {}

        # Test with complete config
        config = {
            "data_providers": {
                "alpha_vantage": {
                    "api_key": "test_key",
                    "cache_ttl": 600
                }
            }
        }
        provider_config = config.get('data_providers', {}).get('alpha_vantage', {})
        print(f"     Complete config result: {provider_config}")
        assert provider_config == {"api_key": "test_key", "cache_ttl": 600}

        print("     All nested config.get() patterns work correctly!")
        return True

    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def test_response_depth_enum():
    """Test that ResponseDepth enum works correctly"""
    print("🧪 Testing ResponseDepth enum...")
    
    try:
        from src.bot.pipeline.commands.ask.stages.response_templates import ResponseDepth
        
        # Test enum values
        print("  ✅ Test 1: Enum values")
        depths = [ResponseDepth.CASUAL, ResponseDepth.GENERAL, ResponseDepth.DETAILED, 
                 ResponseDepth.TECHNICAL, ResponseDepth.ACADEMIC]
        for depth in depths:
            print(f"     {depth.name} = {depth.value}")
        
        # Test that enum doesn't have .get() method (which was the original error)
        print("  ✅ Test 2: Enum doesn't have .get() method")
        try:
            ResponseDepth.CASUAL.get('some_key', 'default')
            print("  ❌ ERROR: Enum should not have .get() method!")
            return False
        except AttributeError:
            print("     Correctly raises AttributeError when calling .get() on enum")
        
        # Test enum comparison
        print("  ✅ Test 3: Enum comparison")
        assert ResponseDepth.CASUAL == ResponseDepth.CASUAL
        assert ResponseDepth.CASUAL != ResponseDepth.DETAILED
        print("     Enum comparison works correctly")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def test_depth_style_analyzer():
    """Test that depth style analyzer works with ResponseDepth enum"""
    print("🧪 Testing QueryAnalyzer with ResponseDepth...")

    try:
        from src.bot.pipeline.commands.ask.stages.depth_style_analyzer import EnhancedQueryAnalyzer
        from src.bot.pipeline.commands.ask.stages.response_templates import ResponseDepth, ResponseStyle

        analyzer = EnhancedQueryAnalyzer()

        # Test query analysis
        print("  ✅ Test 1: Query analysis")
        result = analyzer.analyze_query_comprehensive("What is the price of AAPL?", {})
        print(f"     Query: 'What is the price of AAPL?'")
        print(f"     Depth: {result.depth.name} ({result.depth.value})")
        print(f"     Style: {result.style.name} ({result.style.value})")
        print(f"     Depth confidence: {result.depth_confidence:.2f}")
        print(f"     Style confidence: {result.style_confidence:.2f}")

        # Test that result contains proper enum values
        assert isinstance(result.depth, ResponseDepth)
        assert isinstance(result.style, ResponseStyle)

        # Test complex query
        print("  ✅ Test 2: Complex technical query")
        result = analyzer.analyze_query_comprehensive(
            "Give me a detailed technical analysis of AAPL with RSI, MACD, and support/resistance levels",
            {}
        )
        print(f"     Query: 'Give me a detailed technical analysis...'")
        print(f"     Depth: {result.depth.name} ({result.depth.value})")
        print(f"     Style: {result.style.name} ({result.style.value})")

        return True

    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all tests"""
    print("🔧 Testing ResponseDepth .get() method fix\n")
    
    tests = [
        test_alpha_vantage_config_access,
        test_response_depth_enum,
        test_depth_style_analyzer
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ PASSED\n")
            else:
                print("❌ FAILED\n")
        except Exception as e:
            print(f"❌ FAILED with exception: {e}\n")
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The ResponseDepth .get() method fix is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
