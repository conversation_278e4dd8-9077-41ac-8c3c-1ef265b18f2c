#!/usr/bin/env python3
"""
Test Enhanced Analysis Stage Only

This test isolates the enhanced analysis stage to debug the issue.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_enhanced_stage_only():
    """Test just the enhanced analysis stage"""
    
    print("🧪 TESTING ENHANCED ANALYSIS STAGE ONLY")
    print("=" * 50)
    
    try:
        from src.bot.pipeline.commands.analyze.stages.enhanced_analysis import EnhancedAnalysisStage
        from src.bot.pipeline.commands.analyze.pipeline import PipelineContext
        
        # Create mock pipeline context
        ticker = "TSLA"
        print(f"📊 Testing enhanced analysis stage for: {ticker}")
        
        # Generate realistic mock market data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        base_price = 150.0
        prices = base_price + np.linspace(0, 30, 100) + np.random.normal(0, 3, 100)
        
        mock_market_data = {
            'current_price': prices[-1],
            'price': prices[-1],
            'volume': np.random.randint(50000000, 200000000, 100).mean(),
            'historical_data': {
                'daily': [
                    {
                        'date': date.strftime('%Y-%m-%d'),
                        'open': price * 0.99,
                        'high': price * 1.02,
                        'low': price * 0.98,
                        'close': price,
                        'volume': np.random.randint(50000000, 200000000)
                    }
                    for date, price in zip(dates, prices)
                ]
            }
        }
        
        # Create pipeline context
        context = PipelineContext(
            ticker=ticker.upper(),
            user_id="test_user_123",
            guild_id="test_guild_456",
            correlation_id="test_corr_789",
            strict_mode=True
        )
        
        # Add mock market data
        context.processing_results["market_data"] = mock_market_data
        
        print(f"✅ Created pipeline context for {ticker}")
        print(f"   Current price: ${mock_market_data['current_price']:.2f}")
        print(f"   Historical data points: {len(mock_market_data['historical_data']['daily'])}")
        
        # Test enhanced analysis stage
        print("\n🚀 Testing Enhanced Analysis Stage...")
        try:
            enhanced_stage = EnhancedAnalysisStage()
            print("   ✅ Enhanced analysis stage created")
            
            # Run the stage
            updated_context = await enhanced_stage.run(context)
            print("   ✅ Enhanced analysis stage completed")
            
            # Check what's in the context
            print(f"\n🔍 Context after enhanced analysis stage:")
            print(f"   Keys: {list(updated_context.processing_results.keys())}")
            
            if 'enhanced_analysis' in updated_context.processing_results:
                enhanced_data = updated_context.processing_results['enhanced_analysis']
                print(f"   Enhanced analysis type: {type(enhanced_data)}")
                
                if isinstance(enhanced_data, dict):
                    print(f"   ✅ Enhanced analysis is a dictionary")
                    print(f"   Dictionary keys: {list(enhanced_data.keys())}")
                    
                    # Check if it has the expected structure
                    if 'price_targets' in enhanced_data:
                        print(f"   ✅ Price targets present")
                    if 'probability_assessment' in enhanced_data:
                        print(f"   ✅ Probability assessment present")
                    if 'timeframe_confirmation' in enhanced_data:
                        print(f"   ✅ Timeframe confirmation present")
                else:
                    print(f"   ❌ Enhanced analysis is NOT a dictionary")
                    print(f"   It's a: {type(enhanced_data).__name__}")
            else:
                print(f"   ❌ Enhanced analysis NOT in context!")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Enhanced analysis stage failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Error testing enhanced stage: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🎯 Testing Enhanced Analysis Stage Only")
    print("=" * 60)
    
    success = await test_enhanced_stage_only()
    
    if success:
        print("\n🎉 ENHANCED STAGE TEST COMPLETED!")
        print("Check the output above to see what's happening.")
    else:
        print("\n⚠️ Enhanced stage test failed. Check the error messages above.")
    
    return success

if __name__ == "__main__":
    import asyncio
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 