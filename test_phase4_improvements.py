#!/usr/bin/env python3
"""
Test script to verify Phase 4 improvements:
- Enhanced market context service
- Sector and industry analysis
- Market sentiment analysis
- Market hours detection with timezone handling
"""

import asyncio
import sys
import os
import time

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_phase4_improvements():
    """Test the Phase 4 improvements to the ask pipeline."""
    try:
        print("🔍 Testing Phase 4 improvements...")
        
        # Test importing the enhanced market context service
        from src.bot.pipeline.commands.ask.stages.market_context_service import (
            EnhancedMarketContextService, 
            MarketHours, 
            SectorInfo, 
            MarketSentiment
        )
        print("✅ Enhanced Market Context Service imported successfully")
        
        # Test the enhanced market context service
        print("\n🧪 Testing enhanced market context service...")
        
        # Create service instance
        market_service = EnhancedMarketContextService()
        print("✅ Market context service instance created successfully")
        
        # Test market hours configuration
        print("\n🧪 Testing market hours configuration...")
        
        if 'NYSE' in market_service.market_hours:
            nyse_config = market_service.market_hours['NYSE']
            print(f"✅ NYSE configuration loaded:")
            print(f"   - Timezone: {nyse_config.timezone}")
            print(f"   - Open: {nyse_config.open_time}")
            print(f"   - Close: {nyse_config.close_time}")
            print(f"   - Pre-market: {nyse_config.pre_market_open}")
            print(f"   - After-hours: {nyse_config.after_hours_close}")
            print(f"   - Holidays: {len(nyse_config.holidays)} configured")
        else:
            print("❌ NYSE configuration not found")
        
        # Test market status detection
        print("\n🧪 Testing market status detection...")
        
        market_status = market_service.get_current_market_status('NYSE')
        print(f"✅ Market status retrieved:")
        print(f"   - Exchange: {market_status.get('exchange', 'N/A')}")
        print(f"   - Status: {market_status.get('status', 'N/A')}")
        print(f"   - Markets Open: {market_status.get('markets_open', 'N/A')}")
        print(f"   - Current Time: {market_status.get('current_time', 'N/A')}")
        print(f"   - Message: {market_status.get('message', 'N/A')}")
        
        # Test market context summary
        print("\n🧪 Testing market context summary...")
        
        context_summary = market_service.get_market_context_summary('NYSE')
        print(f"✅ Market context summary generated:")
        print(f"   - Length: {len(context_summary)} characters")
        print(f"   - Preview: {context_summary[:100]}...")
        
        # Test sector analysis (mock data)
        print("\n🧪 Testing sector analysis...")
        
        sector_info = await market_service.get_sector_analysis('AAPL')
        if sector_info:
            print(f"✅ Sector analysis retrieved:")
            print(f"   - Sector: {sector_info.sector}")
            print(f"   - Industry: {sector_info.industry}")
            print(f"   - Sector Rank: #{sector_info.sector_rank}")
            print(f"   - Industry Rank: #{sector_info.industry_rank}")
            print(f"   - Volatility: {sector_info.volatility:.1%}")
            print(f"   - Correlation: {sector_info.correlation:.2f}")
        else:
            print("❌ Sector analysis failed")
        
        # Test market sentiment (mock data)
        print("\n🧪 Testing market sentiment...")
        
        sentiment = await market_service.get_market_sentiment('AAPL')
        if sentiment:
            print(f"✅ Market sentiment retrieved:")
            print(f"   - Overall: {sentiment.overall_sentiment}")
            print(f"   - Score: {sentiment.sentiment_score:.2f}")
            print(f"   - Fear & Greed: {sentiment.fear_greed_index:.0f}/100")
            print(f"   - Volatility Index: {sentiment.volatility_index:.1f}")
            print(f"   - News Sentiment: {sentiment.news_sentiment:+.2f}")
            print(f"   - Social Sentiment: {sentiment.social_sentiment:+.2f}")
        else:
            print("❌ Market sentiment failed")
        
        # Test comprehensive market context
        print("\n🧪 Testing comprehensive market context...")
        
        comprehensive_context = await market_service.get_comprehensive_market_context('AAPL', 'NYSE')
        if comprehensive_context:
            print(f"✅ Comprehensive market context retrieved:")
            print(f"   - Enhanced Context: {comprehensive_context.get('enhanced_context', False)}")
            print(f"   - Exchange: {comprehensive_context.get('exchange', 'N/A')}")
            print(f"   - Symbol: {comprehensive_context.get('symbol', 'N/A')}")
            print(f"   - Has Market Status: {'market_status' in comprehensive_context}")
            print(f"   - Has Sector Analysis: {'sector_analysis' in comprehensive_context}")
            print(f"   - Has Market Sentiment: {'market_sentiment' in comprehensive_context}")
        else:
            print("❌ Comprehensive market context failed")
        
        # Test enhanced AI chat processor integration
        print("\n🧪 Testing enhanced AI chat processor integration...")
        
        from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor
        
        # Create a test config
        test_config = {
            'technical': {
                'rsi_period': 14,
                'sma_short': 20,
                'sma_long': 50,
                'ema_short': 12,
                'ema_long': 26,
                'decimal_places': 2
            }
        }
        
        # Create processor instance
        processor = AIChatProcessor(test_config)
        print("✅ Enhanced AI Chat Processor instance created successfully")
        
        # Test market context service availability
        if hasattr(processor, 'market_context_service') and processor.market_context_service:
            print("✅ Enhanced market context service integrated successfully")
        else:
            print("ℹ️ Enhanced market context service not available (using basic market hours)")
        
        # Test comprehensive market context method
        if hasattr(processor, '_get_comprehensive_market_context'):
            print("✅ Comprehensive market context method exists")
        else:
            print("❌ Comprehensive market context method missing")
        
        # Test enhanced market context formatting
        if hasattr(processor, '_format_enhanced_market_context'):
            print("✅ Enhanced market context formatting method exists")
        else:
            print("❌ Enhanced market context formatting method missing")
        
        print("\n🎉 Phase 4 improvements test completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Phase 4 improvements test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_phase4_improvements())
    sys.exit(0 if success else 1) 