#!/usr/bin/env python3
"""
Test script for AI response generation.
Tests the AI chat processor with various query types.
"""

import asyncio
import logging
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_ai_response_generation():
    """Test AI response generation with various query types"""
    logger.info("🤖 Testing AI Response Generation")
    logger.info("=" * 50)
    
    try:
        from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor
        
        # Create processor instance
        processor = AIChatProcessor()
        
        # Test queries
        test_queries = [
            "What is the current price of AAPL?",
            "Show me technical indicators for MSFT",
            "What's the market trend for GOOGL?",
            "Give me a fundamental analysis of TSLA"
        ]
        
        # Test each query
        for i, query in enumerate(test_queries, 1):
            logger.info(f"\n📝 Test Query {i}: {query}")
            logger.info("-" * 40)
            
            try:
                # Process query
                result = await processor.process(query)
                
                if result:
                    # Extract intent and symbols
                    intent = result.get('intent', 'N/A')
                    symbols = result.get('symbols', [])
                    needs_data = result.get('needs_data', 'N/A')
                    
                    logger.info(f"🎯 Intent: {intent}")
                    logger.info(f"📊 Symbols: {symbols}")
                    logger.info(f"🔍 Needs Data: {needs_data}")
                    
                    # Check response quality
                    response = result.get('response', '')
                    if len(response) > 100:
                        logger.info(f"💬 Response Length: {len(response)} characters")
                        logger.info(f"📤 Response Content: {response[:200]}...")
                        logger.info(f"✅ Response is comprehensive (truncated for display)")
                    else:
                        logger.warning(f"⚠️ Response might be too short")
                        
                else:
                    logger.warning("⚠️ No response generated")
                    
            except Exception as e:
                logger.error(f"❌ Error processing query: {e}")
            
            logger.info()
        
        logger.info("✅ AI response generation test completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(test_ai_response_generation())
        if result:
            logger.info("✅ All tests passed!")
        else:
            logger.error("❌ Some tests failed!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1) 