#!/usr/bin/env python3
"""
Test script for the prompt management system.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from src.core.prompts import PromptManager, IntentType


def run_prompt_tests():
    """Run comprehensive tests for the prompt system."""
    
    print("🧪 Testing Prompt Management System")
    print("=" * 50)
    
    # Initialize prompt manager
    prompt_mgr = PromptManager()
    
    # Test cases with expected results
    test_cases = [
        # Technical analysis queries
        {
            "query": "What is the price of $GME and all available indicator values?",
            "expected_intent": IntentType.TECHNICAL_ANALYSIS,
            "expected_symbols": ["GME"],
            "expected_tools": ["price_fetch", "historical_data", "technical_indicators"]
        },
        {
            "query": "$AAPL current price",
            "expected_intent": IntentType.PRICE_CHECK,
            "expected_symbols": ["AAPL"],
            "expected_tools": ["price_fetch"]
        },
        {
            "query": "What are some good options strategies for high volatility?",
            "expected_intent": IntentType.OPTIONS_STRATEGY,
            "expected_symbols": [],
            "expected_tools": ["price_fetch", "historical_data", "options_data"]
        },
        {
            "query": "How do I calculate position size for risk management?",
            "expected_intent": IntentType.RISK_MANAGEMENT,
            "expected_symbols": [],
            "expected_tools": []
        },
        {
            "query": "Explain RSI indicator",
            "expected_intent": IntentType.EDUCATIONAL,
            "expected_symbols": [],
            "expected_tools": []
        },
        {
            "query": "Technical analysis for $NVDA and $AMD with all indicators",
            "expected_intent": IntentType.TECHNICAL_ANALYSIS,
            "expected_symbols": ["NVDA", "AMD"],
            "expected_tools": ["price_fetch", "historical_data", "technical_indicators"]
        },
        {
            "query": "What's the support and resistance for $TSLA?",
            "expected_intent": IntentType.TECHNICAL_ANALYSIS,
            "expected_symbols": ["TSLA"],
            "expected_tools": ["price_fetch", "historical_data", "technical_indicators"]
        }
    ]
    
    results = []
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test['query']}")
        print("-" * 50)
        
        # Run classification
        classification = prompt_mgr.classify_query(test['query'])
        
        # Check results
        success = True
        checks = []
        
        # Check intent
        if classification.intent == test['expected_intent']:
            checks.append(f"✅ Intent: {classification.intent.value}")
        else:
            checks.append(f"❌ Intent: expected {test['expected_intent'].value}, got {classification.intent.value}")
            success = False
        
        # Check symbols
        if classification.symbols == test['expected_symbols']:
            checks.append(f"✅ Symbols: {classification.symbols}")
        else:
            checks.append(f"❌ Symbols: expected {test['expected_symbols']}, got {classification.symbols}")
            success = False
        
        # Check tools
        actual_tools = [t.value for t in classification.tools_required]
        expected_tools = test['expected_tools']
        if actual_tools == expected_tools:
            checks.append(f"✅ Tools: {actual_tools}")
        else:
            checks.append(f"❌ Tools: expected {expected_tools}, got {actual_tools}")
            success = False
        
        # Check confidence
        if classification.confidence >= 0.5:
            checks.append(f"✅ Confidence: {classification.confidence:.2f}")
        else:
            checks.append(f"⚠️ Confidence: {classification.confidence:.2f} (low)")
        
        # Print results
        for check in checks:
            print(f"  {check}")
        
        results.append({
            'test': test,
            'result': classification,
            'success': success,
            'checks': checks
        })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    # Failed tests
    failed_tests = [r for r in results if not r['success']]
    if failed_tests:
        print(f"\n❌ Failed Tests ({len(failed_tests)}):")
        for test in failed_tests:
            print(f"  - {test['test']['query']}")
    
    # All results
    print(f"\n📈 Full Results:")
    for result in results:
        status = "✅" if result['success'] else "❌"
        query = result['test']['query'][:60] + "..." if len(result['test']['query']) > 60 else result['test']['query']
        print(f"  {status} {query}")
    
    return passed == total


def test_symbol_extraction():
    """Test symbol extraction functionality."""
    print("\n🔍 Testing Symbol Extraction")
    print("=" * 40)
    
    prompt_mgr = PromptManager()
    
    symbol_tests = [
        ("What's the price of $AAPL and $MSFT?", ["AAPL", "MSFT"]),
        ("Technical analysis for $NVDA", ["NVDA"]),
        ("$SPY and $QQQ options strategy", ["SPY", "QQQ"]),
        ("No symbols here", []),
        ("$gme $amc $tsla", ["GME", "AMC", "TSLA"]),
        ("Invalid $123 and $TOOLONGSYMBOL", []),
    ]
    
    all_passed = True
    
    for query, expected in symbol_tests:
        actual = prompt_mgr.extract_symbols(query)
        if actual == expected:
            print(f"✅ '{query}' -> {actual}")
        else:
            print(f"❌ '{query}' -> expected {expected}, got {actual}")
            all_passed = False
    
    return all_passed


if __name__ == "__main__":
    print("Starting Prompt System Tests...\n")
    
    # Run tests
    test1_passed = run_prompt_tests()
    test2_passed = test_symbol_extraction()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS")
    print("=" * 60)
    
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED! The prompt system is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Check the output above for details.")
        sys.exit(1)