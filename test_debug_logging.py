#!/usr/bin/env python3
"""
Simple test script to verify debug logging is working
"""

import asyncio
import logging
import sys
import os

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Configure logging to DEBUG level
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug_test.log')
    ]
)

logger = logging.getLogger(__name__)

async def test_debug_logging():
    """Test that debug logging is working"""
    logger.debug("🔍 This is a DEBUG message")
    logger.info("ℹ️ This is an INFO message")
    logger.warning("⚠️ This is a WARNING message")
    logger.error("❌ This is an ERROR message")
    
    # Test pipeline import
    try:
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        logger.info("✅ Pipeline import successful")
        
        # Test a simple query
        logger.info("🚀 Testing pipeline with simple query...")
        result = await execute_ask_pipeline(
            query="What is the price of $AAPL?",
            user_id="test_user_123",
            guild_id="test_guild_456"
        )
        
        logger.info(f"✅ Pipeline completed with status: {result.status}")
        logger.info(f"🔍 Available keys: {list(result.processing_results.keys())}")
        
        # Check for response
        if 'response' in result.processing_results:
            response = result.processing_results['response']
            logger.info(f"✅ Found response: {len(str(response))} chars")
            logger.debug(f"📝 Response content: {response}")
        else:
            logger.warning(f"⚠️ No response found. Available keys: {list(result.processing_results.keys())}")
            
    except Exception as e:
        logger.error(f"❌ Pipeline test failed: {e}", exc_info=True)

if __name__ == "__main__":
    logger.info("🚀 Starting Debug Logging Test")
    asyncio.run(test_debug_logging())
    logger.info("✅ Debug Logging Test Completed") 