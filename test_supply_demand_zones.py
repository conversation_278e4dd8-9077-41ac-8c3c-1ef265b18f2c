#!/usr/bin/env python3
"""
Test script for supply and demand zone detection functionality.

This script tests the new zone detection capabilities with real market data
and provides validation examples.
"""

import os
import sys
import logging
from datetime import datetime, timedelta
import pandas as pd
import json

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.shared.technical_analysis.zones import SupplyDemandDetector
from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator
from src.api.data.providers.yfinance_provider import YFinanceProvider

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_zone_detection():
    """Test supply/demand zone detection with sample data."""
    print("=" * 60)
    print("Supply/Demand Zone Detection Test")
    print("=" * 60)
    
    # Initialize components
    detector = SupplyDemandDetector()
    calculator = TechnicalAnalysisCalculator()
    provider = YFinanceProvider()
    
    # Test with a liquid stock - AAPL
    symbol = "AAPL"
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)  # 3 months of data
    
    print(f"\nFetching data for {symbol}...")
    
    try:
        # Get historical data
        df = provider.get_historical_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            interval="1d"
        )
        
        if df is None or df.empty:
            print(f"Failed to fetch data for {symbol}")
            return
            
        print(f"Retrieved {len(df)} data points")
        print(f"Date range: {df.index[0]} to {df.index[-1]}")
        
        # Test zone detection
        print("\nTesting zone detection...")
        zones_result = detector.detect_zones(df)
        
        zones = zones_result.get('zones', [])
        metrics = zones_result.get('metrics', {})
        
        print(f"\nZone Detection Results:")
        print(f"Total zones detected: {len(zones)}")
        print(f"Supply zones: {metrics.get('supply_zone_count', 0)}")
        print(f"Demand zones: {metrics.get('demand_zone_count', 0)}")
        print(f"Current price: ${metrics.get('current_price', 0):.2f}")
        
        # Display top zones
        if zones:
            print("\nTop 5 Strongest Zones:")
            sorted_zones = sorted(zones, key=lambda x: x['strength'], reverse=True)[:5]
            
            for i, zone in enumerate(sorted_zones, 1):
                zone_obj = detector._find_zone_by_index(zones, zone['center_price'])
                print(f"\n{i}. {zone['zone_type'].upper()} ZONE")
                print(f"   Range: ${zone['bottom_price']:.2f} - ${zone['top_price']:.2f}")
                print(f"   Center: ${zone['center_price']:.2f}")
                print(f"   Strength: {zone['strength']:.1f}/100")
                print(f"   Height: {zone['percentage_height']:.2f}%")
                print(f"   Touches: {zone['touches']}")
                
                # Check if current price is near zone
                current_price = metrics.get('current_price', 0)
                if zone_obj and zone_obj.contains_price(current_price):
                    print(f"   ⚠️  Current price is WITHIN this zone!")
                elif zone['zone_type'] == 'supply' and current_price < zone['bottom_price']:
                    distance = zone['bottom_price'] - current_price
                    print(f"   Distance to resistance: ${distance:.2f} ({(distance/current_price*100):.2f}%)")
                elif zone['zone_type'] == 'demand' and current_price > zone['top_price']:
                    distance = current_price - zone['top_price']
                    print(f"   Distance to support: ${distance:.2f} ({(distance/current_price*100):.2f}%)")
        
        # Test integration with calculator
        print("\n" + "=" * 40)
        print("Integration Test with TechnicalAnalysisCalculator")
        print("=" * 40)
        
        ta_results = calculator.calculate_all_indicators(df, symbol)
        
        zone_data = ta_results.get('supply_demand_zones', [])
        zone_metrics = ta_results.get('zone_metrics', {})
        
        print(f"Calculator zones: {len(zone_data)}")
        print(f"Calculator zone count: {ta_results.get('zone_count', 0)}")
        
        if zone_data:
            print(f"\nNearest supply zone: {zone_metrics.get('nearest_supply', {}).get('center_price', 'N/A')}")
            print(f"Nearest demand zone: {zone_metrics.get('nearest_demand', {}).get('center_price', 'N/A')}")
        
        # Save detailed results
        results_file = f"test_results_{symbol}_zones.json"
        with open(results_file, 'w') as f:
            json.dump({
                'symbol': symbol,
                'timestamp': pd.Timestamp.now().isoformat(),
                'zones': zones,
                'metrics': metrics,
                'ta_results_summary': {
                    'zone_count': ta_results.get('zone_count', 0),
                    'zone_metrics': zone_metrics
                }
            }, f, indent=2, default=str)
        
        print(f"\nDetailed results saved to: {results_file}")
        
    except Exception as e:
        logger.error(f"Error testing zone detection: {e}")
        import traceback
        traceback.print_exc()


def test_zone_properties():
    """Test zone object properties and methods."""
    print("\n" + "=" * 60)
    print("Zone Object Properties Test")
    print("=" * 60)
    
    from src.shared.technical_analysis.zones import SupplyDemandZone
    
    # Create test zones
    supply_zone = SupplyDemandZone(
        zone_type='supply',
        top_price=150.0,
        bottom_price=148.0,
        strength=85.5,
        volume_profile={'high_volume_area': True, 'rejection_wicks': 3},
        touches=2,
        is_active=True
    )
    
    demand_zone = SupplyDemandZone(
        zone_type='demand',
        top_price=100.0,
        bottom_price=98.0,
        strength=92.3,
        volume_profile={'high_volume_area': True, 'strong_bounce': True},
        touches=1,
        is_active=True
    )
    
    # Test properties
    print(f"Supply Zone:")
    print(f"  Type: {supply_zone.zone_type}")
    print(f"  Range: ${supply_zone.bottom_price:.2f} - ${supply_zone.top_price:.2f}")
    print(f"  Center: ${supply_zone.center_price:.2f}")
    print(f"  Height: ${supply_zone.height:.2f} ({supply_zone.percentage_height:.2f}%)")
    print(f"  Strength: {supply_zone.strength}")
    print(f"  Contains $149: {supply_zone.contains_price(149.0)}")
    print(f"  Contains $147: {supply_zone.contains_price(147.0)}")
    
    print(f"\nDemand Zone:")
    print(f"  Type: {demand_zone.zone_type}")
    print(f"  Range: ${demand_zone.bottom_price:.2f} - ${demand_zone.top_price:.2f}")
    print(f"  Center: ${demand_zone.center_price:.2f}")
    print(f"  Height: ${demand_zone.height:.2f} ({demand_zone.percentage_height:.2f}%)")
    print(f"  Strength: {demand_zone.strength}")
    print(f"  Contains $99: {demand_zone.contains_price(99.0)}")
    print(f"  Contains $101: {demand_zone.contains_price(101.0)}")
    
    # Test serialization
    supply_dict = supply_zone.to_dict()
    print(f"\nSerialized supply zone keys: {list(supply_dict.keys())}")


def test_multiple_symbols():
    """Test zone detection across multiple symbols."""
    print("\n" + "=" * 60)
    print("Multi-Symbol Zone Detection Test")
    print("=" * 60)
    
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY']
    provider = YFinanceProvider()
    detector = SupplyDemandDetector()
    
    results = {}
    
    for symbol in symbols:
        print(f"\nTesting {symbol}...")
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=60)
            
            df = provider.get_historical_data(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                interval="1d"
            )
            
            if df is not None and not df.empty:
                zone_result = detector.detect_zones(df)
                zones = zone_result.get('zones', [])
                metrics = zone_result.get('metrics', {})
                
                results[symbol] = {
                    'data_points': len(df),
                    'total_zones': len(zones),
                    'supply_zones': metrics.get('supply_zone_count', 0),
                    'demand_zones': metrics.get('demand_zone_count', 0),
                    'current_price': metrics.get('current_price', 0),
                    'avg_supply_strength': metrics.get('avg_supply_strength', 0),
                    'avg_demand_strength': metrics.get('avg_demand_strength', 0)
                }
                
                print(f"  ✓ {results[symbol]['total_zones']} zones detected")
            else:
                results[symbol] = {'error': 'Failed to fetch data'}
                print(f"  ✗ Failed to fetch data")
                
        except Exception as e:
            results[symbol] = {'error': str(e)}
            print(f"  ✗ Error: {e}")
    
    # Display summary
    print("\n" + "-" * 40)
    print("Summary Table:")
    print("-" * 40)
    print(f"{'Symbol':<6} {'Zones':<5} {'Supply':<6} {'Demand':<6} {'Price':<8}")
    print("-" * 40)
    
    for symbol, data in results.items():
        if 'error' not in data:
            print(f"{symbol:<6} {data['total_zones']:<5} {data['supply_zones']:<6} "
                  f"{data['demand_zones']:<6} ${data['current_price']:<7.2f}")
        else:
            print(f"{symbol:<6} ERROR: {data['error']}")
    
    return results


def main():
    """Run all tests."""
    print("Supply/Demand Zone Detection System Test Suite")
    print("=" * 60)
    
    try:
        # Test 1: Basic zone detection
        test_zone_detection()
        
        # Test 2: Zone object properties
        test_zone_properties()
        
        # Test 3: Multi-symbol testing
        multi_results = test_multiple_symbols()
        
        # Save summary results
        summary = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'test_suite': 'supply_demand_zones',
            'multi_symbol_results': multi_results
        }
        
        with open('test_zones_summary.json', 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print("\n" + "=" * 60)
        print("✅ All tests completed successfully!")
        print("=" * 60)
        print("Files generated:")
        print("- test_results_AAPL_zones.json (detailed AAPL results)")
        print("- test_zones_summary.json (multi-symbol summary)")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()