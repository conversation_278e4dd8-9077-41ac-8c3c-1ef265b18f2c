#!/usr/bin/env python3
"""
Test script to verify the fixed pipeline architecture
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_pipeline_import():
    """Test that the pipeline can be imported without errors"""
    try:
        print("🔍 Testing pipeline imports...")
        
        # Test importing the pipeline
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline, AskPipeline
        print("✅ Pipeline imported successfully")
        
        # Test importing the processor
        from src.bot.pipeline.commands.ask.stages.ai_chat_processor import processor
        print("✅ Processor imported successfully")
        
        # Test importing the config
        from src.bot.pipeline.commands.ask.config import AskPipelineConfig
        print("✅ Config imported successfully")
        
        # Test creating config instance
        config = AskPipelineConfig()
        print("✅ Config instance created successfully")
        
        # Test getting AI config
        ai_config = config.get_ai_config()
        print(f"✅ AI config retrieved: {ai_config.get('enabled', False)}")
        
        print("\n🎯 All imports successful! Pipeline architecture is fixed.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_pipeline_execution():
    """Test that the pipeline can execute without errors"""
    try:
        print("\n🔍 Testing pipeline execution...")
        
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        
        # Test with a simple query
        query = "What is the price of $AAPL?"
        print(f"📝 Testing query: {query}")
        
        # Execute pipeline
        result = await execute_ask_pipeline(
            query=query,
            user_id="test_user_123",
            guild_id="test_guild_456"
        )
        
        print(f"✅ Pipeline executed successfully")
        print(f"📊 Result type: {type(result)}")
        
        if hasattr(result, 'processing_results'):
            print(f"📋 Processing results keys: {list(result.processing_results.keys())}")
            
            if 'response' in result.processing_results:
                response = result.processing_results['response']
                print(f"💬 Response length: {len(response)} characters")
                print(f"💬 Response preview: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Testing Fixed Pipeline Architecture")
    print("=" * 50)
    
    # Test imports
    import_success = await test_pipeline_import()
    
    if not import_success:
        print("\n❌ Import tests failed. Cannot proceed with execution tests.")
        return False
    
    # Test execution
    execution_success = await test_pipeline_execution()
    
    if execution_success:
        print("\n🎉 All tests passed! Pipeline is working correctly.")
        return True
    else:
        print("\n❌ Execution tests failed. Pipeline needs more fixes.")
        return False

if __name__ == "__main__":
    # Run the tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 