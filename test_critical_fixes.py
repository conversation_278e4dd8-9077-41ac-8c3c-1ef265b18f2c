#!/usr/bin/env python3
"""
Test Script for Critical Fixes

This script tests the critical fixes we implemented:
1. Timeout fixes (30s -> 45s)
2. Quality threshold restoration
3. Fake data fallback removal
4. Watchlist infrastructure
5. Analysis scheduler
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_timeout_fixes():
    """Test that timeout fixes are properly applied."""
    print("🔍 Testing Timeout Fixes...")
    
    try:
        # Check if the client file has the correct timeout values
        with open('src/bot/client.py', 'r') as f:
            content = f.read()
            
        # Check for 45 second timeouts
        if 'timeout=45.0' in content:
            print("✅ Bot timeout fixed: 45 seconds (was 30 seconds)")
        else:
            print("❌ Bot timeout not fixed - still using 30 seconds")
            return False
        
        # Check for multiple timeout instances
        timeout_count = content.count('timeout=45.0')
        if timeout_count >= 3:
            print(f"✅ All timeout instances fixed: {timeout_count} found")
        else:
            print(f"⚠️  Some timeout instances may not be fixed: {timeout_count} found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing timeout fixes: {e}")
        return False

def test_quality_thresholds():
    """Test that quality thresholds are restored to professional standards."""
    print("\n🔍 Testing Quality Threshold Restoration...")
    
    try:
        # Check the pipeline config file
        with open('src/bot/pipeline/commands/ask/config.yaml', 'r') as f:
            content = f.read()
        
        # Check for restored quality thresholds
        if 'quality_threshold: 0.7' in content:
            print("✅ Quality thresholds restored to professional standards (0.7+)")
        else:
            print("❌ Quality thresholds not restored - still using lowered values")
            return False
        
        # Check for proper quality threshold levels
        if 'good: 0.7' in content and 'fair: 0.5' in content:
            print("✅ Quality threshold levels properly configured")
        else:
            print("❌ Quality threshold levels not properly configured")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing quality thresholds: {e}")
        return False

def test_fake_data_removal():
    """Test that fake data fallbacks are removed."""
    print("\n🔍 Testing Fake Data Removal...")
    
    try:
        # Check the technical analysis file
        with open('src/bot/pipeline/commands/analyze/stages/technical_analysis.py', 'r') as f:
            content = f.read()
        
        # Check that fake RSI=50.0 is removed
        if 'rsi: 50.0' in content:
            print("❌ Fake RSI=50.0 still present")
            return False
        else:
            print("✅ Fake RSI=50.0 removed")
        
        # Check that proper error handling is in place
        if 'insufficient_data' in content and 'Need at least 14 data points' in content:
            print("✅ Proper error handling for insufficient data implemented")
        else:
            print("❌ Proper error handling not implemented")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing fake data removal: {e}")
        return False

def test_watchlist_infrastructure():
    """Test that watchlist infrastructure is created."""
    print("🔍 Testing Watchlist Infrastructure...")
    
    try:
        # Check if watchlist manager file exists
        if os.path.exists('src/core/watchlist/watchlist_manager.py'):
            print("✅ Watchlist manager file created")
        else:
            print("❌ Watchlist manager file not found")
            return False
        
        # Try to import the watchlist manager
        from core.watchlist.watchlist_manager import WatchlistManager, WatchlistPriority, AnalysisDepth
        
        # Test basic functionality
        manager = WatchlistManager("test_user_123")
        
        # Test creating a watchlist
        if manager.create_watchlist("test_list"):
            print("✅ Watchlist creation working")
        else:
            print("❌ Watchlist creation failed")
            return False
        
        # Test adding a symbol
        if manager.add_symbol("AAPL", "test_list", notes="Test symbol"):
            print("✅ Symbol addition working")
        else:
            print("❌ Symbol addition failed")
            return False
        
        # Test getting symbols needing analysis
        # Newly added symbols should be scheduled for future analysis, not immediately need analysis
        symbols_needing_analysis = manager.get_symbols_needing_analysis("test_list")
        print(f"   Debug: Found {len(symbols_needing_analysis)} symbols needing analysis")
        
        # Debug: Check the watchlist details
        watchlist = manager.get_watchlist("test_list")
        if watchlist:
            print(f"   Debug: Watchlist has {len(watchlist.symbols)} symbols")
            for i, symbol in enumerate(watchlist.symbols):
                print(f"   Debug: Symbol {i}: {symbol.symbol}, needs_analysis: {symbol.needs_analysis()}, next_analysis: {symbol.next_analysis}")
        
        # Test that symbols are properly scheduled (not immediately needing analysis)
        if len(watchlist.symbols) > 0:
            symbol = watchlist.symbols[0]
            if symbol.next_analysis and symbol.next_analysis > datetime.now():
                print("✅ Symbol analysis scheduling working (properly scheduled for future)")
            else:
                print("❌ Symbol analysis scheduling not working (not scheduled properly)")
                return False
        else:
            print("❌ No symbols in watchlist to test")
            return False
        
        # Test getting watchlist summary
        summary = manager.get_watchlist_summary("test_list")
        if summary and summary.get('total_symbols', 0) > 0:
            print("✅ Watchlist summary working")
        else:
            print("❌ Watchlist summary not working")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing watchlist infrastructure: {e}")
        return False

def test_analysis_scheduler():
    """Test that analysis scheduler is created."""
    print("\n🔍 Testing Analysis Scheduler...")
    
    try:
        # Check if analysis scheduler file exists
        if os.path.exists('src/core/automation/analysis_scheduler.py'):
            print("✅ Analysis scheduler file created")
        else:
            print("❌ Analysis scheduler file not found")
            return False
        
        # Try to import the analysis scheduler
        from core.automation.analysis_scheduler import AnalysisJobScheduler, JobPriority, JobStatus
        
        # Test basic functionality
        scheduler = AnalysisJobScheduler(max_concurrent_jobs=3)
        
        # Test scheduling a job
        job_id = asyncio.run(scheduler.schedule_job(
            symbol="AAPL",
            user_id="test_user",
            priority=JobPriority.HIGH,
            analysis_depth="standard"
        ))
        
        if job_id:
            print("✅ Job scheduling working")
        else:
            print("❌ Job scheduling failed")
            return False
        
        # Test getting scheduler status
        status = asyncio.run(scheduler.get_scheduler_status())
        if status and 'queue_size' in status:
            print("✅ Scheduler status reporting working")
        else:
            print("❌ Scheduler status reporting not working")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing analysis scheduler: {e}")
        return False

def run_all_tests():
    """Run all critical fix tests."""
    print("🚀 Running Critical Fix Tests...")
    print("=" * 50)
    
    tests = [
        ("Timeout Fixes", test_timeout_fixes),
        ("Quality Thresholds", test_quality_thresholds),
        ("Fake Data Removal", test_fake_data_removal),
        ("Watchlist Infrastructure", test_watchlist_infrastructure),
        ("Analysis Scheduler", test_analysis_scheduler)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All critical fixes are working properly!")
        print("🚀 Ready to proceed with Week 2 enhancements")
    else:
        print("⚠️  Some critical fixes need attention before proceeding")
        print("🔧 Please fix the failing tests before continuing")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 