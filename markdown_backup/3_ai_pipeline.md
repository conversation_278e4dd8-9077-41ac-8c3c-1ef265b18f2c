# 3. AI Pipeline (`src/bot/pipeline`)

The AI pipeline is the core of the bot's intelligence. It's responsible for processing user queries, performing technical analysis, and generating insightful responses.

## Workflow

1.  **Pipeline Execution:** The `execute_ask_pipeline` function in `pipeline.py` is the main entry point for the AI pipeline.
2.  **Context Creation:** It creates a `PipelineContext` object to track the request and pass data between stages.
3.  **AI Chat Processor:** The `AIChatProcessor` in `ai_service_wrapper.py` is the main processing stage. It performs the following steps:
    *   Extracts symbols from the user's query.
    *   Fetches comprehensive technical analysis data for the symbols.
    *   Generates trading signals.
    *   Generates a conversational AI response that interprets the data.
4.  **Response Return:** The response is returned to the `TradingBot`, which then sends it to the user on Discord.

## Key Files

*   `src/bot/pipeline/commands/ask/pipeline.py`: Orchestrates the AI pipeline for the `/ask` command.
*   `src/bot/pipeline/commands/ask/stages/ai_service_wrapper.py`: The core of the AI processing logic.
*   `src/bot/pipeline/core/pipeline_engine.py`: The generic pipeline engine that the `/ask` pipeline is built on.
*   `src/shared/technical_analysis/`: This directory contains the modules for calculating technical indicators, detecting supply/demand zones, and other analysis tasks.

## Detailed Breakdown

### `pipeline.py`

*   **Purpose:** To orchestrate the AI pipeline for the `/ask` command.
*   **Key Features:**
    *   **Single-Stage Pipeline:** It currently implements a single-stage pipeline, but it's designed to be easily extensible.
    *   **`PipelineContext`:** It uses a `PipelineContext` to manage the state of the request and pass data between stages.
    *   **Performance Optimization:** It uses a `pipeline_optimizer` to cache results and improve performance.
    *   **Monitoring and Health Checks:** It includes a `PipelineMonitor` for tracking metrics and health.

### `ai_service_wrapper.py`

*   **Purpose:** To provide the core AI processing logic.
*   **Key Features:**
    *   **Comprehensive Analysis:** It performs a wide range of technical analysis, including calculating indicators, detecting zones, and generating signals.
    *   **Data Aggregation:** It fetches data from multiple sources (Polygon, Yahoo Finance, Finnhub) to get a complete picture of the market.
    *   **Conversational AI Response:** It generates a human-like response that explains the analysis in a way that is easy to understand.
    *   **Rate Limiting:** It includes a rate limiter for API calls to external data providers.

### `pipeline_engine.py`

*   **Purpose:** To provide a generic, extensible pipeline engine.
*   **Key Features:**
    *   **Stage-Based Architecture:** It allows for the creation of multi-stage pipelines where each stage is a separate class.
    *   **`PipelineContext`:** It uses a `PipelineContext` to pass data between stages.
    *   **Error Handling:** It includes error handling for pipeline execution.

### `technical_analysis` directory

*   **Purpose:** To provide the tools for performing technical analysis.
*   **Key Modules:**
    *   `calculator.py`: Calculates a wide range of technical indicators.
    *   `zones.py`: Detects supply and demand zones.
    *   `signal_analyzer.py`: Generates trading signals based on the analysis.
    *   `atr_calculator.py`: Calculates ATR-based stop-loss recommendations.
