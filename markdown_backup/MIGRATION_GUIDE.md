# Migration Guide: Code Deduplication and Canonical Imports

This guide provides instructions for migrating from deprecated modules to their canonical implementations as part of our code deduplication effort.

## Table of Contents

1. [Overview](#overview)
2. [Migration Process](#migration-process)
3. [Canonical Directory Structure](#canonical-directory-structure)
4. [Backward Compatibility](#backward-compatibility)
5. [Deprecation Monitoring](#deprecation-monitoring)
6. [Best Practices](#best-practices)
7. [FAQ](#faq)

## Overview

Our codebase has accumulated significant duplication over time, with multiple implementations of similar functionality spread across different modules. This leads to maintenance challenges, inconsistent behavior, and security risks.

The code deduplication initiative aims to:
- Consolidate duplicated code into canonical locations
- Provide a clear path for migration
- Maintain backward compatibility during the transition
- Track migration progress through deprecation monitoring

## Migration Process

### Step 1: Identify Canonical Imports

Before making changes, identify the canonical location for the functionality you need:

| Deprecated Import | Canonical Import |
|-------------------|------------------|
| `src.bot.pipeline.ask.stages.ai_service_wrapper` | `src.shared.ai_services.ai_service_wrapper` |
| `src.bot.pipeline.commands.ask.stages.ai_service_wrapper` | `src.shared.ai_services.ai_service_wrapper` |
| *More will be added as migration progresses* | |

### Step 2: Update Your Imports

Replace deprecated imports with their canonical equivalents:

```python
# Before
from src.bot.pipeline.ask.stages.ai_service_wrapper import AIChatProcessor

# After
from src.shared.ai_services.ai_service_wrapper import AIChatProcessor
```

### Step 3: Test Your Changes

After updating imports, thoroughly test your code to ensure it works as expected with the canonical implementations.

## Canonical Directory Structure

Our canonical code is organized in the following directory structure:

```
src/
└── shared/
    ├── ai_services/        # AI service wrappers and utilities
    ├── market_analysis/    # Market analysis tools and algorithms
    ├── database/           # Database connection and utilities
    ├── technical_analysis/ # Technical indicators and analysis
    └── data_providers/     # Data provider implementations
```

When adding new functionality, place it in the appropriate canonical location rather than creating new implementations.

## Backward Compatibility

To maintain backward compatibility during the migration period, we've implemented backward compatibility modules that:

1. Import from the canonical location
2. Issue deprecation warnings
3. Track usage through the deprecation monitoring system

These modules allow existing code to continue working while providing clear signals about the need to migrate.

### Example Backward Compatibility Module

```python
"""
Module Name - DEPRECATED

This module is deprecated. Import from src.shared.canonical_path instead.
"""

import warnings
import inspect
from typing import Any, Dict, List

# Import from canonical location
from src.shared.canonical_path import (
    CanonicalClass,
    canonical_function
)

# Import deprecation monitoring
from src.core.deprecation_monitor import record_deprecation

# Get caller information
caller_frame = inspect.currentframe()
if caller_frame and caller_frame.f_back:
    caller_info = f"{caller_frame.f_back.f_code.co_filename}:{caller_frame.f_back.f_lineno}"
else:
    caller_info = "unknown"

# Record deprecation usage
record_deprecation("src.deprecated.path", caller_info)

# Show deprecation warning
warnings.warn(
    "This module is deprecated. Import from src.shared.canonical_path instead.",
    DeprecationWarning,
    stacklevel=2
)

# For backward compatibility
class LegacyClass(CanonicalClass):
    """Legacy wrapper for backward compatibility"""
    pass
```

## Deprecation Monitoring

We've implemented a deprecation monitoring system to track the usage of deprecated modules and functions. This helps us:

1. Identify which deprecated modules are still in use
2. Track migration progress
3. Make informed decisions about when to remove deprecated code

### Generating Deprecation Reports

You can generate deprecation reports using the provided script:

```bash
# Generate a text report
./scripts/generate_deprecation_report.py --format text --output deprecation_report.txt

# Generate an HTML report with charts
./scripts/generate_deprecation_report.py --format html --output deprecation_report.html

# Generate a JSON report for further processing
./scripts/generate_deprecation_report.py --format json --output deprecation_report.json
```

### Interpreting Deprecation Reports

The reports include:
- Migration progress percentage
- List of deprecated modules still in use
- Usage counts for each deprecated module
- Days since last usage for each module
- Top callers for each deprecated module

Use this information to prioritize your migration efforts and track progress.

## Best Practices

1. **Always use canonical imports** for new code.
2. **Update imports** when modifying existing code.
3. **Don't create new implementations** of functionality that already exists in canonical locations.
4. **Run deprecation reports** regularly to track migration progress.
5. **Update tests** to use canonical imports.
6. **Document migration** in your pull request descriptions.

## FAQ

### Q: How do I know if a module is deprecated?

A: Deprecated modules will issue a `DeprecationWarning` when imported. You can also check the deprecation reports for a complete list.

### Q: What if the canonical implementation doesn't have all the functionality I need?

A: Extend the canonical implementation rather than maintaining a separate version. Submit a pull request to add the needed functionality to the canonical location.

### Q: When will deprecated modules be removed?

A: Deprecated modules will be maintained for at least 3 months after their canonical replacements are available. After that, they will be removed if they show no usage in the deprecation reports for at least 30 days.

### Q: How can I contribute to the code deduplication effort?

A: You can help by:
- Updating your code to use canonical imports
- Identifying duplicated code that needs consolidation
- Creating canonical implementations for duplicated functionality
- Creating backward compatibility modules for deprecated code

### Q: What if I find a bug in a canonical implementation?

A: Submit a bug report or pull request to fix the issue in the canonical implementation. Do not create a new implementation to work around the bug.
