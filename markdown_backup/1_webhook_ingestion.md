# 1. Webhook Ingestion (`tradingview-ingest`)

The `tradingview-ingest` service is responsible for receiving, validating, and processing webhooks from TradingView. It is a standalone FastAPI application designed to handle a high volume of incoming webhooks efficiently and securely.

## Workflow

1.  **Webhook Reception:** An HTTP POST request is received by `webhook_receiver.py`.
2.  **Security:** The request is validated by the `TradingViewSecurityMiddleware` in `security.py`.
3.  **Signature Validation:** The webhook signature is validated using HMAC-SHA256.
4.  **Queueing:** The validated webhook payload is placed in a Redis queue.
5.  **Supabase (Initial Save):** The raw webhook data is saved to the `webhooks` table in Supabase.
6.  **Processing:** The `webhook_processor.py` dequeues the webhook.
7.  **Parsing:** The webhook data is parsed by `DataParser` or `PineScriptAlertParser`.
8.  **Storage (Local):** The `StorageManager` stores the data in Redis.
9.  **Storage (Supabase):** The `webhook_processor` calls the `SupabaseClient` to update the webhook status and save the parsed ticker data.
10. **Alerting:** The `AlertEngine` sends alerts to Discord and/or a generic webhook.
11. **AI Analysis (Periodic):** The `AITradingAnalyzer` runs periodically, fetches data from the database, and sends analysis reports to Discord via the `DiscordNotifier`.

## Key Files

*   `tradingview-ingest/src/webhook_receiver.py`: The main FastAPI application that receives webhooks.
*   `tradingview-ingest/src/security.py`: The security middleware for validating incoming requests.
*   `tradingview-ingest/src/webhook_processor.py`: The worker that processes webhooks from the Redis queue.
*   `tradingview-ingest/src/data_parser.py`: Parses structured JSON webhook data.
*   `tradingview-ingest/src/text_parser.py`: Parses raw text alerts from Pine Script.
*   `tradingview-ingest/src/storage_manager.py`: Manages the storage of webhook data in Redis and PostgreSQL.
*   `tradingview-ingest/src/alert_engine.py`: Sends alerts to Discord and other webhook endpoints.
*   `tradingview-ingest/src/automated_analyzer.py`: Performs periodic AI analysis of recent data.
*   `tradingview-ingest/src/supabase_client.py`: A client for interacting with the Supabase API.

## Detailed Breakdown

### `webhook_receiver.py`

*   **Purpose:** To provide a high-volume, asynchronous endpoint for receiving TradingView webhooks.
*   **Framework:** FastAPI.
*   **Key Features:**
    *   **Security Middleware:** All incoming requests are first processed by the `TradingViewSecurityMiddleware`.
    *   **HMAC Signature Validation:** Ensures that webhooks are genuinely from TradingView by validating the `X-TradingView-Signature` header.
    *   **Redis Queueing:** Validated webhooks are immediately placed into a Redis queue for asynchronous processing, preventing the endpoint from being blocked by long-running tasks.
    *   **Supabase Integration:** Saves a copy of the raw webhook data to Supabase for auditing and historical analysis.
    *   **Health Checks and Metrics:** Provides `/health` and `/metrics` endpoints for monitoring.

### `security.py`

*   **Purpose:** To provide a robust security layer for the webhook receiver.
*   **Key Features:**
    *   **IP Address Validation:** Blocks requests from known malicious IP addresses.
    *   **Request Method Validation:** Only allows POST requests.
    *   **Content-Type Validation:** Ensures that the request has the correct content type.
    *   **Payload Size Validation:** Prevents resource exhaustion by limiting the size of the webhook payload.
    *   **Rate Limiting:** Prevents abuse by limiting the number of requests from a single IP address.
    *   **User-Agent Validation:** Blocks requests from common bots and scrapers.
    *   **Request Path Validation:** Ensures that requests are only made to the intended webhook endpoint.

### `webhook_processor.py`

*   **Purpose:** To process webhooks from the Redis queue in the background.
*   **Workflow:**
    1.  Dequeues a webhook from the Redis queue.
    2.  Parses the webhook data using the appropriate parser (`DataParser` or `PineScriptAlertParser`).
    3.  Stores the parsed data using the `StorageManager`.
    4.  Sends an alert using the `AlertEngine`.
*   **Key Features:**
    *   **Asynchronous Processing:** Processes webhooks asynchronously, allowing the receiver to handle a high volume of requests.
    *   **Error Handling:** Includes error handling and a retry mechanism with exponential backoff.
    *   **AI Analyzer Integration:** Starts the `AITradingAnalyzer` as a background task.

### `data_parser.py` and `text_parser.py`

*   **Purpose:** To parse different formats of webhook data into a standardized format.
*   **`DataParser`:** Parses structured JSON data into `MarketData`, `TechnicalIndicator`, or `TradingSignal` objects.
*   **`PineScriptAlertParser`:** Parses raw text alerts from Pine Script, supporting both a new pipe-separated format and a legacy emoji-based format.
*   **Key Features:**
    *   **Flexibility:** The ability to handle multiple formats makes the system more robust and adaptable.
    *   **Data Validation:** Includes basic data quality validation.

### `storage_manager.py`

*   **Purpose:** To manage the storage of webhook data.
*   **Key Features:**
    *   **Redis and PostgreSQL:** It's designed to work with both Redis for caching and PostgreSQL for persistent storage (though the PostgreSQL connection is currently disabled).
    *   **Batch Processing:** It uses a batching mechanism to efficiently write data to the database.
    *   **Ticker Management:** It includes methods for managing trading symbols.

### `alert_engine.py`

*   **Purpose:** To send alerts to various channels.
*   **Key Features:**
    *   **Multiple Channels:** Can send alerts to a generic webhook and a Discord webhook.
    *   **Rate Limiting:** Prevents spamming alerts for the same symbol.
    *   **Formatted Alerts:** Creates nicely formatted alerts for Discord.

### `automated_analyzer.py`

*   **Purpose:** To perform periodic AI-powered analysis of recent market data.
*   **Workflow:**
    1.  Fetches recent symbols from the database.
    2.  Performs a two-stage analysis (overview and deep dive).
    3.  If the analysis is significant, it sends a report to Discord.
*   **Key Features:**
    *   **Multi-Stage Analysis:** The two-stage analysis allows for a quick overview of many symbols and a more detailed analysis of promising ones.
    *   **Specialized Analyzers:** It includes placeholders for specialized trader analyzers, which can be implemented in the future.

### `supabase_client.py`

*   **Purpose:** To provide an interface for interacting with Supabase.
*   **Key Features:**
    *   **Asynchronous:** Uses `httpx` for asynchronous requests.
    *   **CRUD Operations:** Provides methods for saving and updating webhook and ticker data.
