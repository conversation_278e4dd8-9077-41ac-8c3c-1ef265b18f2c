# Historical Data Improvement Roadmap (Audited)

Last Updated: 2025-08-26  
Next Review: 2025-09-02

Summary: This checklist reflects a full audit of historical data handling across providers, caching, quality checks, response templates, and pipeline behavior. It prioritizes removing fabrication (no synthetic data), improving transparency, and adding measurable telemetry and tests.

---

## ✅ Completed (audit verified)
- [X] Remove synthetic data generation (confirmed in `src/api/data/providers/data_source_manager.py`)  
- [X] Transparent data limitation messaging implemented in AI responses (`ai_chat_processor.py`, `response_templates.py`)  
- [X] Documented provider fallback order (Polygon → Alpha Vantage → yfinance / other)  
- [X] Added detailed provenance logging for data sources and cache operations
- [X] **Implement cache warming for top N symbols (Top 50)** ✅ COMPLETED
  - **Implementation**: Added `warm_cache_for_symbols()` method to `MarketDataCache` class
  - **Scheduling**: Created `ScheduledTaskManager` that runs cache warming 30min before market open (9:00 AM ET)
  - **Batch Processing**: Processes 10 symbols concurrently with 5-minute timeout per batch
  - **Monitoring**: Added cache statistics and metrics collection
  - **Files Modified**: `src/api/data/cache.py`, `src/api/data/scheduled_tasks.py`
  - **Tests**: Created comprehensive test suite in `tests/test_cache_warming.py`
- [X] **Enhanced Cache Warming Scheduler** ✅ COMPLETED
  - **APScheduler Integration**: Replaced basic scheduling with robust APScheduler for production-grade job management
  - **Market-Aware Scheduling**: Pre-market job at 7:30 AM ET (12:30 UTC) Monday-Friday, weekend refresh at 10 AM ET Sunday
  - **Job Management**: Prevents duplicate jobs, provides job status monitoring, graceful shutdown
  - **Enhanced Monitoring**: Detailed metrics collection, failure alerts, cache statistics logging
  - **Files Added**: `src/api/data/cache_warming_scheduler.py`
  - **Dependencies**: Added APScheduler==3.10.4 and pytz==2023.3 to requirements.txt

---

## 🔧 High Priority — Core (must ship)
- [X] **Add robust data-gap detection and reporting** ✅ COMPLETED
  - **Implementation**: Comprehensive gap detection engine with severity classification (minor/moderate/major/critical)
  - **Gap Detection**: Identifies missing intervals in time series data across multiple granularities (1m, 5m, 15m, 1h, 1d)
  - **Quality Assessment**: Calculates data quality scores (0-100) based on completeness and gap severity
  - **Prometheus Integration**: Metrics for gap counts, durations, quality scores, and completeness percentages
  - **/ask Command Enhancement**: Automatically detects gaps and provides transparent data quality information in responses
  - **Files Added**: `src/shared/data_validation.py`, `tests/test_data_gap_detection.py`
  - **Integration**: Seamlessly integrated with AI chat processor and metrics collection system

- [X] **Provider attribution in user responses** ✅ COMPLETED
  - **Implementation**: Enhanced data providers with comprehensive metadata (provider_name, fetched_at, data_window, fallback info)
  - **Transparency Footer**: Every `/ask` response includes data source, freshness, and attribution information
  - **Provider Metadata**: Tracks response times, cache status, fallback usage, and data coverage windows
  - **Prometheus Metrics**: Provider performance, fallback usage, data freshness, and cache hit rates
  - **Files Modified**: `src/api/data/providers/base.py`, `src/api/data/metrics.py`, `src/bot/pipeline/commands/ask/stages/ai_service_wrapper.py`
  - **Tests**: Comprehensive test suite for provider attribution and metadata handling

---

## ⚠️ Medium Priority — Quality & Scoring
- [X] **Implement data quality scoring API and integration** ✅ COMPLETED
  - **Implementation**: Comprehensive data quality scoring system with weighted factors (completeness 35%, freshness 25%, consistency 20%, provider reliability 20%)
  - **Quality Levels**: EXCELLENT (90-100), GOOD (80-89), FAIR (70-79), POOR (50-69), VERY_POOR (0-49)
  - **API Endpoints**: `/analytics/quality/{symbol}` for individual assessment, `/analytics/quality/batch` for batch processing
  - **Quality Factors**: Completeness scoring, freshness assessment, consistency validation, provider reliability, gap penalty calculation
  - **Integration**: Seamlessly integrated with gap detection, provider attribution, and Prometheus metrics
  - **Files Added**: `src/core/data_quality.py`, `src/api/routes/analytics.py`, `tests/test_data_quality_scoring.py`
  - **Features**: Real-time quality assessment, gap penalty calculation, quality improvement recommendations, batch processing for monitoring

- [X] **Stale data warnings and rules** ✅ COMPLETED
  - **Implementation**: Comprehensive stale data detection system with 5 severity levels (NONE, MILD, MODERATE, SEVERE, CRITICAL)
  - **Automatic Confidence Adjustments**: 5% (mild), 15% (moderate), 30% (severe), 50% (critical) confidence reductions
  - **Smart Thresholds**: <5min (fresh), 5-15min (mild), 15-60min (moderate), 1-4h (severe), >4h (critical)
  - **Market Hours Awareness**: Different recommendations for open vs closed market conditions
  - **Integration**: Seamlessly integrated with AI chat processor for automatic warnings and confidence adjustments
  - **Files Added**: `src/core/stale_data_detector.py`, `tests/test_stale_data_detection.py`
  - **Features**: Real-time staleness detection, contextual recommendations, confidence scoring, comprehensive testing

- [X] **Outlier detection (price / volume)** ✅ COMPLETED
  - **Implementation**: Comprehensive outlier detection system using statistical methods (z-scores, percent changes, volume ratios)
  - **Outlier Types**: Price spikes/drops, volume spikes/drops, price gaps, volatility spikes
  - **Severity Levels**: MINOR (2%), MODERATE (8%), MAJOR (20%), CRITICAL (40%) confidence impacts
  - **Statistical Methods**: Z-score analysis, rolling statistics, threshold-based detection, market-aware validation
  - **Integration**: Seamlessly integrated with AI chat processor for automatic outlier warnings and confidence adjustments
  - **Files Added**: `src/core/outlier_detector.py`, `tests/test_outlier_detection.py`
  - **Features**: Real-time anomaly detection, statistical validation, confidence scoring, comprehensive recommendations

---

## 📈 Low Priority — Optimizations & Monitoring
- [ ] Pre-load sector ETF historicals (optional cache warm)  
  - Owner: @backend  
  - ETA: 2 days  
  - Estimate: 8h

## 🗓️ Phase 7: Holiday/Weekend Calendar Integration [X] ✅ COMPLETED

**Goal**: Eliminate false stale data alarms during closed markets and provide intelligent market context.

### ✅ **Completed Tasks:**

#### **7.1 Market Calendar Service** [X]
- **Created**: `src/core/market_calendar.py`
- **Features**:
  - US Federal Holidays (2024-2025) with NYSE/NASDAQ support
  - Market hours awareness (regular, pre-market, after-hours)
  - Timezone handling (ET/UTC conversion)
  - Holiday detection and weekend awareness
  - Next/previous market open/close calculations
  - Market status enumeration (OPEN, CLOSED, PRE_MARKET, AFTER_HOURS, HOLIDAY, WEEKEND)

#### **7.2 Stale Data Detector Integration** [X]
- **Enhanced**: `src/core/stale_data_detector.py`
- **Features**:
  - Intelligent severity adjustments for closed markets
  - Holiday-aware warning messages
  - Weekend-aware recommendations
  - Market context integration
  - Automatic confidence calibration

#### **7.3 AI Response Enhancement** [X]
- **Enhanced**: `src/bot/pipeline/commands/ask/stages/ai_service_wrapper.py`
- **Features**:
  - Market status context in responses
  - Holiday and weekend awareness
  - Next trading session information
  - Market hours display
  - Intelligent stale data warnings

#### **7.4 Comprehensive Testing** [X]
- **Created**: `tests/test_market_calendar.py`
- **Coverage**: 26 test cases covering all functionality
- **Test Results**: ✅ All tests passing
- **Features Tested**:
  - Market hours validation
  - Holiday detection
  - Weekend handling
  - Pre/after market hours
  - Timezone conversion
  - Edge cases and rollovers

### 🎯 **Key Benefits Achieved:**

1. **Eliminated False Alarms**: No more "critical stale data" warnings on weekends/holidays
2. **Intelligent Context**: Users get context-aware responses like "Markets closed today for Labor Day"
3. **Smart Severity**: Data staleness severity automatically adjusted for market conditions
4. **User Experience**: Reduced noise and confusion around non-trading periods
5. **Professional Grade**: Rivals professional market data platforms in transparency

### 🔧 **Technical Implementation:**

- **Market Calendar Service**: Core calendar logic with holiday detection
- **Intelligent Severity**: Smart adjustments based on market status
- **Context-Aware Warnings**: Market-specific recommendations and explanations
- **Timezone Handling**: Proper ET/UTC conversion for accurate market hours
- **Extensible Design**: Easy to add global exchanges and dynamic holiday sources

### 📊 **Integration Points:**

- **Stale Data Detection**: Automatic severity downgrading for closed markets
- **AI Responses**: Market context and status information
- **User Experience**: Clear explanations of expected vs unexpected data staleness
- **Monitoring**: Track expected vs unexpected stale periods

---

## 🚀 **NEXT PHASE OPTIONS:**

### **Option A: Global Exchange Expansion**
- Add LSE (London), HKEX (Hong Kong), TSE (Tokyo) calendars
- Multi-timezone market hours
- Global holiday awareness

### **Option B: Dynamic Holiday Sources**
- Integrate with TradingHours.com API
- Real-time holiday updates
- Custom holiday management

### **Option C: Advanced Market Intelligence**
- Sector rotation analysis
- Market sentiment integration
- Economic calendar integration

**Recommendation**: The system is now production-ready with comprehensive US market calendar integration. Consider **Option A (Global Exchange Expansion)** next for international trading capabilities.

---

## 🛡️ Security / Compliance
- [ ] Audit & rotate provider API keys (document rotation schedule)  
  - Owner: @security  
  - ETA: 2 days  

- [ ] Add data retention policy & documentation  
  - Owner: @legal/ops  
  - ETA: 3 days

---

## 📊 Monitoring / Alerts (must have)
- [X] **Add metrics to monitor and alert on** ✅ COMPLETED
  - **Implementation**: Comprehensive Prometheus metrics system for cache warming and performance monitoring
  - **Metrics Covered**: Cache hit rate, provider success/failure rates, data age distribution, data quality score distribution
  - **Prometheus Integration**: Custom registry with histograms, gauges, and counters for all cache operations
  - **Grafana Dashboard**: Pre-configured dashboard JSON with panels for cache performance, warming success rates, and memory usage
  - **Files Added**: `src/api/data/metrics.py`, `tests/test_cache_metrics.py`
  - **Dependencies**: Added prometheus-client==0.19.0 to requirements.txt
  - **Integration**: Metrics automatically collected in cache warming scheduler and cache operations

---

## ✅ Verification & Tests (must pass before merge)
- Unit tests for gap detection, outlier detection, cache warming  
- Integration tests that simulate provider failures and validate fallback behavior  
- End-to-end test for `/ask` that verifies provider attribution + data quality messaging

---

## Priority Backlog (nice-to-have)
- Shadow-mode rollouts for any new data-quality enforcement (run alongside production for 2 weeks)  
- Automated cache eviction strategy tuned to symbol popularity  

---

## 🚀 Next Implementation Priority
**Data Gap Detection & Reporting** - This will ensure the system can identify and report when historical data has missing intervals, improving data quality transparency.

**Implementation Plan**:
1. Create `src/shared/data_validation.py` with gap detection algorithms
2. Integrate gap detection into `ai_chat_processor.py` 
3. Add data quality scoring based on gap analysis
4. Create unit tests for gap detection scenarios

---

Notes from audit:
- Synthetic data generator existed and was removed — verify no other synthetic paths remain (`provider='synthetic'` search recommended).  
- Cache uses Redis with separate TTLs for current vs historical data (historical TTL = 24h) — **IMPLEMENTED** ✅
- Response templates already handle stale/unreliable prices; integrate provider attribution + quality score into templates.  

**Current Status**: Cache warming implementation complete with scheduled job, batch processing, and monitoring. Ready to proceed with data gap detection implementation.