# 5. Core Components (`src/core`)

This section details the core components of the `tradingview-automation` project, which provide essential services like configuration management, secrets management, and logging.

## Key Files

*   `src/core/config_manager.py`: Manages the application's configuration.
*   `src/core/secrets.py`: Manages secrets and other sensitive information.
*   `src/core/logger.py`: Provides a centralized logging facility.
*   `src/core/exceptions.py`: Defines custom exception classes for the application.

## Detailed Breakdown

### `config_manager.py`

*   **Purpose:** To provide a centralized way to manage the application's configuration.
*   **Key Features:**
    *   **YAML and Environment Variables:** It loads configuration from a `config.yaml` file and allows for overriding values with environment variables.
    *   **Typed Configuration:** It uses Pydantic models to define the structure of the configuration, which provides type hints and validation.
    *   **Singleton Pattern:** It uses a singleton pattern to ensure that there is only one instance of the configuration manager.

### `secrets.py`

*   **Purpose:** To manage secrets and other sensitive information.
*   **Key Features:**
    *   **Encryption:** It encrypts secrets before storing them.
    *   **Key Derivation:** It uses a key derivation function to derive the encryption key from a master secret.
    *   **Environment Variable Integration:** It can load the master secret from an environment variable.

### `logger.py`

*   **Purpose:** To provide a centralized logging facility.
*   **Key Features:**
    *   **Structured Logging:** It uses the `structlog` library to produce structured logs, which are easy to parse and analyze.
    *   **Correlation ID:** It supports correlation IDs for tracing requests across different services.
    *   **Log Levels:** It supports different log levels (e.g., `DEBUG`, `INFO`, `WARNING`, `ERROR`).

### `exceptions.py`

*   **Purpose:** To define custom exception classes for the application.
*   **Key Features:**
    *   **Custom Exceptions:** It defines custom exception classes for different types of errors, which makes it easier to handle errors in a structured way.
