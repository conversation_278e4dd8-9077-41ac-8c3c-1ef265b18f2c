# Audit System Usage Guide

This document provides detailed instructions on how to use the request visualization and audit system for the TradingView Automation Discord bot.

## Overview

The audit system automatically captures all Discord command requests and sends them to a designated webhook or developer channel for real-time monitoring and auditing. This is particularly useful during testing and debugging phases.

## Setup

### Basic Setup

To enable the audit system in your bot, add the following code to your bot's initialization:

```python
from src.bot.client_audit_integration import add_audit_hooks

# Initialize the bot
bot = discord.Bot(...)

# Set up the audit system with the webhook URL
webhook_url = "https://discord.com/api/webhooks/1414317125371428874/q9zbJZJee_14EBav22k0JJ3z-ANTfpRt3tDCeAScg3EOy22trtVR8yy2iS9AnMu1nX8p"
audit_integration = add_audit_hooks(bot, webhook_url=webhook_url, enabled=True)
```

### Configuration Options

The `add_audit_hooks` function accepts the following parameters:

- `bot`: The Discord bot instance
- `dev_channel_id`: (Optional) The ID of a Discord channel to send audit logs to
- `webhook_url`: (Optional) The webhook URL to send audit logs to
- `enabled`: (Default: True) Whether to enable audit logging

## Toggling the Audit System

### In-Bot Commands

The audit system provides several Discord commands for administrators to control its behavior:

#### Toggle Webhook Logging

```
/toggle_webhook
```

This command toggles webhook logging on and off. When disabled, no logs will be sent to the webhook URL.

#### Configure Audit System

```
/audit_config enabled:true|false use_webhook:true|false channel_id:1234567890
```

Parameters:
- `enabled`: Enable or disable the entire audit system
- `use_webhook`: Enable or disable webhook logging
- `channel_id`: Set a Discord channel ID for audit logs

#### Test Audit System

```
/test_audit
```

This command sends test events to verify that the audit system is working correctly.

### Programmatic Control

You can also control the audit system programmatically:

```python
# Enable/disable the entire audit system
request_visualizer.enabled = True  # or False

# Enable/disable webhook logging
request_visualizer.log_to_webhook = True  # or False

# Enable/disable console logging
request_visualizer.log_to_console = True  # or False
```

## Performance Considerations

The audit system includes rate limiting to prevent excessive resource usage when there are many users. This is particularly important in production environments.

### Rate Limits

The default rate limits are:
- 60 events per minute globally
- 10 events per user per minute
- 30 events per guild per minute

When rate limits are exceeded, the system will automatically sample events to ensure you still get a representative view of what's happening without overwhelming resources.

### Important Notes

1. **Errors are always logged** regardless of rate limits
2. **Warning and critical events are always logged** regardless of rate limits
3. **The first event from each user and guild is always logged** to ensure you see a sample of all users

### Adjusting Rate Limits

You can adjust the rate limits by modifying the `AuditRateLimiter` instance:

```python
from src.bot.audit.rate_limiter import audit_rate_limiter

# Increase global rate limit to 120 events per minute
audit_rate_limiter.max_events_per_minute = 120

# Increase per-user rate limit to 20 events per minute
audit_rate_limiter.max_events_per_user_minute = 20

# Change sampling rate to 20% when rate limited
audit_rate_limiter.sampling_rate = 0.2
```

## Webhook Integration

The audit system sends detailed information to the configured webhook URL. This includes:

1. **Command Requests**: When a user executes a command
2. **Command Responses**: The result of command execution
3. **Pipeline Visualizations**: Detailed view of pipeline execution stages
4. **Audit Events**: System events at various severity levels

### Webhook Format

All webhook messages are sent as Discord embeds with the following information:

- **Title**: The type of event (request, response, pipeline, audit)
- **Description**: A brief summary of the event
- **Fields**: Detailed information about the event
- **Color**: Indicates status (blue for requests, green for success, red for errors)
- **Timestamp**: When the event occurred
- **Footer**: Contains the correlation ID for tracking related events

## Best Practices

1. **Enable in Testing, Disable in Production**: The audit system is designed for debugging and testing. It's recommended to disable it in production to avoid unnecessary resource usage.

2. **Use Correlation IDs**: Each request is assigned a unique correlation ID. Use this ID to track related events across the system.

3. **Monitor Rate Limiting**: If you see messages about rate limiting in your logs, consider adjusting the rate limits or sampling rate.

4. **Create a Dedicated Audit Channel**: Create a dedicated Discord channel for audit logs to keep them separate from normal user interactions.

## Troubleshooting

### No Logs Appearing

1. Check that the audit system is enabled: `request_visualizer.enabled = True`
2. Check that webhook logging is enabled: `request_visualizer.log_to_webhook = True`
3. Verify the webhook URL is correct
4. Check for rate limiting messages in your console logs

### Rate Limiting Issues

If you're seeing too many rate limiting messages:

1. Increase the rate limits as described above
2. Consider disabling audit logging for specific commands that are used frequently

### High Resource Usage

If the audit system is using too many resources:

1. Decrease the rate limits
2. Decrease the sampling rate
3. Disable logging for non-critical commands
4. Use a channel instead of a webhook (slightly more efficient)

## Example: Full Integration

Here's a complete example of how to integrate the audit system with your bot:

```python
import discord
from discord.ext import commands
from src.bot.client_audit_integration import add_audit_hooks

# Initialize the bot
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix="!", intents=intents)

# Set up the audit system
webhook_url = "https://discord.com/api/webhooks/1414317125371428874/q9zbJZJee_14EBav22k0JJ3z-ANTfpRt3tDCeAScg3EOy22trtVR8yy2iS9AnMu1nX8p"
audit_integration = add_audit_hooks(
    bot=bot,
    webhook_url=webhook_url,
    enabled=True  # Set to False in production
)

@bot.event
async def on_ready():
    print(f"Logged in as {bot.user}")
    print("Audit system enabled and ready")

# Run the bot
bot.run("YOUR_TOKEN_HERE")
```

## Conclusion

The audit system provides powerful tools for monitoring and debugging your Discord bot. By sending detailed information about command execution to a webhook or channel, you can gain valuable insights into your bot's operation and quickly identify and fix issues.
