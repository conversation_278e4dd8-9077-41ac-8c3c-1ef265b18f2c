# Container & Network Debug Checklist

## 🚨 Current Container Status
```
NAME                 STATUS     PORTS
tradingview-nginx    unhealthy  0.0.0.0:80->80/tcp, 0.0.0.0:443->443/tcp
tradingview-ngrok    unhealthy  
tradingview-webhook-proxy  unhealthy  0.0.0.0:8001->8001/tcp
tradingview-webhook-ingest unhealthy  
tradingview-postgres  healthy    
tradingview-redis     healthy    
tradingview-automation-api-1 healthy
```

## 🔍 NGROK Container Issues
- [x] **NGROK_AUTHTOKEN** missing from environment
- [ ] DNS resolution still failing for webhook-proxy service
- [ ] Container shows as unhealthy despite token fix

## 🔗 Network Connectivity
- [ ] **webhook-proxy** (port 8001) health check failing
- [ ] **webhook-ingest** (port 8001) service connectivity issues
- [ ] **nginx** (ports 80/443) health check failing

## 📋 Debug Tasks

### 1. Webhook Proxy Container
- [ ] Check nginx configuration syntax in webhook.conf
- [ ] Verify port 8001 is actually bound and listening
- [ ] Test HTTP connectivity to /health endpoint
- [ ] Check nginx error logs inside container

### 2. Webhook Ingest Container  
- [ ] Verify FastAPI app is actually running on port 8001
- [ ] Check database connectivity (PostgreSQL)
- [ ] Check Redis connectivity
- [ ] Verify CORS and security configuration

### 3. Main Nginx Container
- [ ] Check main nginx configuration
- [ ] Verify SSL certificate configuration
- [ ] Test all upstream services connectivity
- [ ] Check nginx error logs

### 4. Ngrok Container
- [ ] Re-check DNS resolution for webhook-proxy
- [ ] Verify ngrok tunnel establishment
- [ ] Check ngrok configuration file

## 🔧 Quick Tests to Run

```bash
# Test webhook-proxy health
curl -v http://localhost:8001/health

# Check container logs
docker logs tradingview-webhook-proxy
docker logs tradingview-webhook-ingest
docker logs tradingview-nginx

# Check container networking
docker inspect tradingview-webhook-proxy | grep -A 10 "NetworkSettings"
docker exec -it tradingview-webhook-proxy nginx -t

# Check port bindings
netstat -tlnp | grep :8001
ss -tlnp | grep :8001
```

## 🎯 Expected Endpoints
- **Webhook Proxy**: http://localhost:8001/webhook/tradingview
- **Webhook Health**: http://localhost:8001/health  
- **Main API**: http://localhost/api/
- **Main HTTPS**: https://localhost/

## 📝 Configuration Files to Check
- [ ] docker-compose.yml
- [ ] nginx/webhook.conf
- [ ] tradingview-ingest/config/tradingview_config.py
- [ ] .env files for environment variables