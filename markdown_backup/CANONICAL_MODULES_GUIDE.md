# Canonical Modules Migration Guide

This guide explains how to migrate from the legacy module structure to the new canonical module structure. The canonical structure improves code organization, reduces duplication, and makes the codebase more maintainable.

## Overview

We've consolidated duplicated code into canonical modules located in the `src/shared` directory. The original modules still exist as backward compatibility wrappers that import from the canonical locations, but they emit deprecation warnings and will eventually be removed.

## Canonical Module Locations

| Legacy Module | Canonical Module |
|---------------|-----------------|
| `src/bot/pipeline/ask/stages/ai_chat_processor.py` | `src/shared/ai_services/ai_service_wrapper.py` |
| `src/bot/pipeline/commands/ask/stages/ai_chat_processor.py` | `src/shared/ai_services/ai_service_wrapper.py` |
| `src/shared/market_analysis/signal_analyzer.py` | `src/shared/market_analysis/unified_signal_analyzer.py` |

## How to Migrate

### AI Chat Processor

**Before:**
```python
from src.bot.pipeline.ask.stages.ai_chat_processor import AIChatProcessor

processor = AIChatProcessor(context)
result = await processor.process("What is the price of AAPL?")
```

**After:**
```python
from src.shared.ai_services.ai_service_wrapper import AIChatProcessor

config = {
    'api_key': 'your-api-key',
    'model': 'your-model',
    'technical': {'enabled': True}
}
processor = AIChatProcessor(config)
result = await processor.process("What is the price of AAPL?")
```

### Signal Analyzer

**Before:**
```python
from src.shared.market_analysis.signal_analyzer import SignalAnalyzer

analyzer = SignalAnalyzer()
signals = await analyzer.analyze_market_data("AAPL", "1d", market_data)
```

**After:**
```python
from src.shared.market_analysis.unified_signal_analyzer import unified_signal_analyzer

signals = await unified_signal_analyzer.analyze_market_data("AAPL", "1d", market_data)
```

Or, for more control:

```python
from src.shared.market_analysis.unified_signal_analyzer import UnifiedSignalAnalyzer

analyzer = UnifiedSignalAnalyzer()
signals = await analyzer.analyze_market_data("AAPL", "1d", market_data)
```

## Deprecation Monitoring

The backward compatibility modules use the deprecation monitoring system to track usage. This helps us identify which parts of the codebase are still using the legacy modules and need to be updated.

To view deprecation usage statistics, run:

```bash
python -m src.core.deprecation_monitor --report
```

## Testing

We've added tests to verify that the backward compatibility modules work correctly with the canonical implementations. If you're updating code to use the canonical modules, make sure to run the tests to ensure everything works as expected:

```bash
pytest tests/test_backward_compatibility.py
```

## Benefits of Using Canonical Modules

1. **Reduced Code Duplication**: Eliminates duplicated code, making the codebase more maintainable.
2. **Improved Consistency**: Ensures consistent behavior across different parts of the application.
3. **Better Testability**: Centralizes logic in one place, making it easier to test.
4. **Easier Maintenance**: Changes only need to be made in one place.
5. **Better Documentation**: Canonical modules have more comprehensive documentation.

## Timeline for Removal of Backward Compatibility Modules

The backward compatibility modules will be maintained for a transition period to allow for gradual migration. After all code has been updated to use the canonical modules, the backward compatibility modules will be removed.

Estimated timeline:
- **Phase 1 (Current)**: Canonical modules available, backward compatibility modules in place
- **Phase 2 (Next 2 months)**: Update all code to use canonical modules
- **Phase 3 (After 3 months)**: Remove backward compatibility modules

## Need Help?

If you have any questions or need help migrating to the canonical modules, please contact the development team.
