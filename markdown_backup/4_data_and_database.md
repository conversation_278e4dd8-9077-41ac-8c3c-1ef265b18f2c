# 4. Data and Database (`src/data` and `src/database`)

This section details the data models, data providers, and database interactions used in the `tradingview-automation` project.

## Data Models

The project uses dataclasses to define the structure of the data that is passed between different components. This is a good practice as it provides type hints and makes the code easier to understand and maintain.

### Key Data Models:

*   **`MarketData`:** Represents market data from TradingView, including the symbol, exchange, price, and volume.
*   **`TechnicalIndicator`:** Represents a technical indicator, including the symbol, indicator name, value, and parameters.
*   **`TradingSignal`:** Represents a trading signal, including the symbol, signal type, confidence, and other relevant information.
*   **`ParsedAlert`:** Represents a parsed alert from a raw text message.

## Data Providers

The project is designed to work with multiple data providers to get a comprehensive view of the market. The data providers are located in the `src/shared/data_providers` directory.

### Key Data Providers:

*   **Polygon:** The primary data provider for historical market data.
*   **Yahoo Finance:** A backup data provider for historical market data.
*   **Finnhub:** Used for getting market context, such as sector and industry information.
*   **Alpha Vantage:** Another provider for market data.

## Database

The project uses a PostgreSQL database for persistent storage and Redis for caching and queuing.

### Key Files:

*   `src/database/connection.py`: Manages the connection to the PostgreSQL database using SQLAlchemy.
*   `src/database/query_wrapper.py`: Provides wrapper functions for executing database queries with correlation ID logging.
*   `src/database/models/`: This directory contains the SQLAlchemy models for the database tables.
*   `init-db.sql`: An SQL script for initializing the database schema.

### Database Schema

The database schema is defined in the SQLAlchemy models in the `src/database/models/` directory. The key tables are:

*   **`webhook_alerts`:** Stores the parsed data from TradingView webhooks.
*   **`tickers`:** Stores information about the trading symbols.
*   **`watchlists`:** Stores user watchlists.
*   **`webhook_events`:** Stores raw webhook data for auditing and debugging.

### Database Interactions

The application interacts with the database using SQLAlchemy, which is a robust and secure way to work with databases. The `query_wrapper.py` module provides a layer of abstraction for executing queries and includes correlation ID logging, which is good for tracing requests.
