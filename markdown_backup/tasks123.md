
#    Timeline Expectations
##      /ask Pipeline: 2-3 hours (with feature flags)
###       Data Providers: 1-2 hours (with compatibility layer)
##      Logger Consolidation: 1 hour
#    Total: 4-6 hours over 1-2 days


# Codebase Audit - Action Plan

## 🔴 **Critical Security Issues**
### 1. **Rate Limiting Disabled** ✅ **COMPLETED**
- **File**: `src/api/middleware/security.py`
- **Issue**: Rate limiting middleware was commented out due to `None` handling bug.
- **Action**: ✅ Fixed `None` handling and re-enabled middleware with proper defaults.

### 2. **Hardcoded Default Passwords** ✅ **COMPLETED**
- **File**: `docker-compose.yml`
- **Issue**: Redis password defaulted to `redis_password_123`.
- **Action**: ✅ Reverted to simple `.env` approach for development mode.

### 3. **API Key Exposure** ✅ **COMPLETED**
- **Files**: `src/core/config_manager.py`, `.env.example`
- **Issue**: No rotation mechanism for exposed API keys.
- **Action**: ✅ Simplified to development mode with `.env` files.

---

## 🟡 **Pipeline & Commands**
### 4. **Unstable `/ask` Pipeline** 🔄 **IN PROGRESS**
- **File**: `src/bot/pipeline/commands/ask/stages/ai_chat_processor.py`
- **Issue**: 
  - Monolithic file (2500+ lines).
  - No clear error fallbacks.
- **Action**: 
  - Split into smaller modules (e.g., `preprocessor.py`, `postprocessor.py`).
  - Add fallback logic.

### **Target Architecture for `/ask` Pipeline:**
```
src/bot/pipeline/commands/ask/stages/
├── ai_chat_processor.py          # Main orchestrator (200-300 lines)
├── preprocessor/
│   ├── __init__.py
│   ├── input_validator.py        # User input validation
│   ├── context_builder.py        # Build conversation context
│   └── prompt_formatter.py       # Format prompts for AI
├── core/
│   ├── __init__.py
│   ├── ai_client.py              # AI service interactions
│   ├── response_parser.py        # Parse AI responses
│   └── error_handler.py          # Centralized error handling
├── postprocessor/
│   ├── __init__.py
│   ├── response_formatter.py     # Format bot responses
│   ├── memory_updater.py         # Update conversation memory
│   └── metrics_collector.py      # Collect usage metrics
└── utils/
    ├── __init__.py
    ├── cache_manager.py          # Response caching
    ├── rate_limiter.py           # User rate limiting
    └── fallback_handler.py       # Graceful degradation
```

### **Step-by-Step Breakdown for `/ask` Pipeline:**

#### **Phase 1: Analysis & Planning**
1. **Audit current file structure** - Map all functions and their dependencies
2. **Identify logical groupings** - Group related functionality
3. **Create dependency map** - Understand function relationships
4. **Plan migration strategy** - Determine order of extraction

#### **Phase 2: Core Infrastructure**
1. **Create new directory structure**
2. **Set up `__init__.py` files** with proper imports
3. **Create base classes/interfaces** for consistency
4. **Set up error handling framework**

#### **Phase 3: Extract Preprocessing**
1. **Create `preprocessor/` module**
2. **Extract input validation logic**
3. **Extract context building logic**
4. **Extract prompt formatting logic**
5. **Update imports in main file**

#### **Phase 4: Extract Core Logic**
1. **Create `core/` module**
2. **Extract AI client interactions**
3. **Extract response parsing logic**
4. **Extract error handling logic**
5. **Update imports in main file**

#### **Phase 5: Extract Postprocessing**
1. **Create `postprocessor/` module**
2. **Extract response formatting logic**
3. **Extract memory update logic**
4. **Extract metrics collection logic**
5. **Update imports in main file**

#### **Phase 6: Extract Utilities**
1. **Create `utils/` module**
2. **Extract caching logic**
3. **Extract rate limiting logic**
4. **Extract fallback handling logic**
5. **Update imports in main file**

#### **Phase 7: Refactor Main File**
1. **Update main orchestrator** to use new modules
2. **Remove extracted code** from original file
3. **Add proper error handling** and logging
4. **Ensure all tests pass**

#### **Phase 8: Testing & Validation**
1. **Run existing tests** to ensure no regressions
2. **Add new tests** for extracted modules
3. **Performance testing** to ensure no degradation
4. **Integration testing** with bot commands

### 5. **Missing Command Tests**
- **Directory**: `src/bot/commands/`
- **Issue**: 
  - No unit/integration tests for commands.
- **Action**: 
  - Add pytest coverage for `analyze.py` and other commands.

---

## 🟡 **Code Quality & Duplication**
### 6. **Duplicate Data Providers** 🔄 **PLANNED**
- **Files**: `src/shared/data_providers/` vs legacy providers.
- **Issue**: 11 duplicate implementations.
- **Action**: 
  - Consolidate into 3-4 authoritative providers.
  - Update imports.

### **Target Architecture for Data Providers:**
```
src/shared/data_providers/
├── __init__.py
├── base.py                      # Abstract base classes
├── alpaca/
│   ├── __init__.py
│   ├── client.py                # Single Alpaca client
│   └── models.py                # Alpaca-specific models
├── polygon/
│   ├── __init__.py
│   ├── client.py                # Single Polygon client
│   └── models.py                # Polygon-specific models
├── finnhub/
│   ├── __init__.py
│   ├── client.py                # Single Finnhub client
│   └── models.py                # Finnhub-specific models
└── aggregator.py                # Unified data aggregator
```

### **Step-by-Step Breakdown for Data Providers:**

#### **Phase 1: Audit & Analysis**
1. **Identify all duplicate providers** across the codebase
2. **Map functionality differences** between duplicates
3. **Determine authoritative versions** to keep
4. **Create migration plan** for each duplicate

#### **Phase 2: Create Base Infrastructure**
1. **Design abstract base classes** for providers
2. **Create unified interfaces** for common operations
3. **Set up error handling** and fallback mechanisms
4. **Create configuration management** system

#### **Phase 3: Consolidate by Provider Type**
1. **Consolidate Alpaca providers** into single implementation
2. **Consolidate Polygon providers** into single implementation
3. **Consolidate Finnhub providers** into single implementation
4. **Update all imports** to use new consolidated providers

#### **Phase 4: Create Unified Aggregator**
1. **Build data aggregator** that can use any provider
2. **Implement fallback logic** between providers
3. **Add caching layer** for performance
4. **Create unified error handling**

#### **Phase 5: Migration & Cleanup**
1. **Update all import statements** across codebase
2. **Remove duplicate files** and directories
3. **Update configuration files** to use new structure
4. **Run tests** to ensure no regressions

### 7. **Logger Fragmentation** 🔄 **PLANNED**
- **Files**: 
  - `src/utils/logger.py`
  - `src/core/logger.py`
- **Issue**: Multiple logger implementations.
- **Action**: 
  - Choose one (e.g., `src/core/logger.py`).
  - Remove duplicates.

### **Target Architecture for Logging:**
```
src/core/
├── logging/
│   ├── __init__.py
│   ├── logger.py                 # Single logger implementation
│   ├── formatters.py             # Log formatting
│   ├── handlers.py               # Log handlers (file, console, etc.)
│   └── config.py                 # Logging configuration
```

### **Step-by-Step Breakdown for Logging:**

#### **Phase 1: Audit Current Loggers**
1. **Find all logger implementations** across codebase
2. **Compare functionality** and features
3. **Choose best implementation** to standardize on
4. **Document differences** and migration needs

#### **Phase 2: Create Unified Logger**
1. **Design unified logger interface**
2. **Implement consistent formatting** across all log levels
3. **Add structured logging** capabilities
4. **Create configuration system** for different environments

#### **Phase 3: Migration**
1. **Update all imports** to use unified logger
2. **Remove duplicate logger files**
3. **Update logging calls** to use new interface
4. **Ensure consistent log levels** across app

---

## 🟢 **Infrastructure & DevOps**
### 8. **Database Connectivity**
- **Issue**: API container fails to connect to Supabase.
- **Action**: 
  - Debug Docker network/firewall.
  - Add local PostgreSQL to `docker-compose.yml` as fallback.

### 9. **Missing Health Checks**
- **File**: `src/api/routes/health.py`
- **Issue**: No resilience for DB failures.
- **Action**: 
  - Add retry logic.
  - Document health check expectations.

---

## 📅 **Prioritized Task List**
1. ✅ **Fix Rate Limiting** (Security) - COMPLETED
2. ✅ **Remove Hardcoded Passwords** (Security) - COMPLETED  
3. ✅ **Implement API Key Rotation** (Security) - COMPLETED
4. 🔄 **Split `/ask` Pipeline** (Code Quality) - IN PROGRESS
5. 🔄 **Consolidate Data Providers** (Code Quality) - PLANNED
6. 🔄 **Fix Logger Fragmentation** (Code Quality) - PLANNED
7. **Add Command Tests** (Commands)
8. **Debug Database Connectivity** (Infra)

---

## ✅ **Success Metrics**
- **Security**: ✅ No hardcoded secrets; 100% middleware coverage.
- **Pipeline**: `/ask` response time <5s; 100% test coverage.
- **Code Quality**: 70% fewer duplicate files.

---

## ⚠️ **Risks & Mitigation**
- **Risk**: Breaking changes during consolidation.
  - **Mitigation**: Test after each change; maintain backups.
- **Risk**: Performance regressions.
  - **Mitigation**: Load-test `/ask` pipeline weekly.

---

**Next Steps**:  
Ready to begin **Task 4: Split `/ask` Pipeline** with Phase 1 (Analysis & Planning).  
This will involve reading through the current 2500-line file and creating a detailed extraction plan.

**Estimated Effort**: 2-3 hours  
**Risk Level**: Medium (refactoring existing functionality)