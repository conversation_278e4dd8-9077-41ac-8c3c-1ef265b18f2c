# Trading Bot Health Monitoring System

This document provides comprehensive information about the new health monitoring system for the Discord trading bot.

## Overview

The health monitoring system provides real-time visibility into bot performance, system resources, and service status through a combination of REST API endpoints and a web dashboard.

## Features

### 🔍 Health Check Endpoints

- **Bot Health**: `/api/v1/health/bot` - Bot-specific health information
- **Detailed Health**: `/api/v1/health/detailed` - Comprehensive system health
- **System Metrics**: `/api/v1/health/metrics` - Real-time resource usage

### 📊 Web Dashboard

- **URL**: `/dashboard`
- **Features**:
  - Real-time bot status
  - System resource monitoring (CPU, Memory, Disk)
  - Service health indicators
  - Network metrics
  - Auto-refresh every 30 seconds
  - Visual alerts for degraded/unhealthy states

### 🔄 Monitoring Capabilities

- **Bot Status**: Discord connection, guild count, command registration
- **System Resources**: CPU, memory, disk usage
- **Network**: Bytes sent/received, connection count
- **Process Info**: Uptime, threads, open files
- **Rate Limiting**: User tracking and limit monitoring

## Quick Start

### Option 1: Full Bot + Monitoring
```bash
python start_with_monitoring.py
```

### Option 2: Degraded Mode (No Discord)
```bash
python start_with_monitoring.py --degraded
```

### Option 3: API Server Only
```bash
python start_with_monitoring.py --api-only
```

### Option 4: Bot Only
```bash
python start_with_monitoring.py --bot-only
```

## API Endpoints

### Bot Health Check
```bash
curl http://localhost:8000/api/v1/health/bot
```

**Response:**
```json
{
  "status": "healthy",
  "bot": {
    "connected": true,
    "guilds": 5,
    "commands_registered": 12,
    "latency": 45.2
  },
  "uptime": 3600.5,
  "timestamp": "2025-08-26T15:11:30.001Z"
}
```

### Detailed Health Check
```bash
curl http://localhost:8000/api/v1/health/detailed
```

**Response includes:**
- Overall system health
- Bot-specific metrics
- Database status
- System resource usage
- Network statistics
- Process information

### System Metrics
```bash
curl http://localhost:8000/api/v1/health/metrics
```

## Integration with Existing Bot

The health monitoring system is designed to work seamlessly with your existing bot:

1. **No Discord Mode**: The system works in degraded mode without Discord connection
2. **Real-time Updates**: Health data is cached for 30 seconds to prevent excessive resource usage
3. **Error Handling**: Graceful handling of missing Discord connection or other service failures
4. **Modular Design**: Can be started independently or integrated with the full bot

## Configuration

### Environment Variables
```bash
# Optional customizations
HEALTH_CHECK_CACHE_TIMEOUT=30  # Cache timeout in seconds
HEALTH_CHECK_LOG_LEVEL=INFO    # Logging level
```

### Custom Health Checks
You can add custom health checks by extending the `BotHealthMonitor` class in [`src/api/routes/bot_health.py`](src/api/routes/bot_health.py).

## Usage Examples

### Monitoring Script
```python
import asyncio
import aiohttp

async def monitor_bot_health():
    async with aiohttp.ClientSession() as session:
        async with session.get('http://localhost:8000/api/v1/health/detailed') as resp:
            data = await resp.json()
            
            if data['overall_status'] == 'healthy':
                print("✅ Bot is healthy")
            else:
                print(f"⚠️ Bot health: {data['overall_status']}")
                print("Issues:", data.get('degraded_services', []))

asyncio.run(monitor_bot_health())
```

### Dashboard Embedding
The dashboard can be embedded into other monitoring systems or accessed directly via web browser.

## Troubleshooting

### Common Issues

1. **API Server Not Starting**: Check port 8000 availability
2. **Bot Connection Failures**: Verify Discord token and intents
3. **Database Connection Issues**: Check database configuration
4. **High Resource Usage**: Monitor via dashboard and adjust cache timeouts

### Debug Mode
```bash
# Enable debug logging
export HEALTH_CHECK_LOG_LEVEL=DEBUG
python start_with_monitoring.py --degraded
```

## Architecture

The health monitoring system consists of:

- **BotHealthMonitor**: Core monitoring class
- **Health Dashboard**: Web-based monitoring interface
- **API Routes**: REST endpoints for external monitoring
- **Integration Layer**: Seamless connection with existing bot

## Security Considerations

- Rate limiting on health endpoints (60 requests per minute per IP)
- No sensitive data exposure in health responses
- Configurable health data granularity

This system provides comprehensive monitoring while maintaining the bot's existing functionality and supporting both Discord-connected and degraded modes.