# Development Rules for TradingView Automation

## 🚫 What NOT to do:
- **NEVER use `pip install` or `pip freeze`**
- **NEVER suggest installing packages locally**
- **NEVER assume Python packages are installed on the host system**

## ✅ What to do instead:
- **All dependencies are managed via Docker + requirements.txt**
- **Code always runs in Docker containers, not locally**
- **Use `docker-compose up --build` to run the application**
- **Dependencies are installed inside the container automatically**

## 🐳 Running the Application

### Start all services:
```bash
docker-compose up --build
```

### Start specific service:
```bash
docker-compose up bot
docker-compose up api
```

### View logs:
```bash
docker-compose logs bot
docker-compose logs api
```

### Restart services:
```bash
docker-compose restart bot
docker-compose restart api
```

## 📦 Adding New Dependencies

1. **Edit requirements.txt** - Add the new package
2. **Rebuild Docker image** - `docker-compose up --build`
3. **Never use pip install locally**

## 🏗️ Project Structure

- **Bot service**: Discord bot with trading automation
- **API service**: FastAPI backend for market data
- **Database**: PostgreSQL for data storage
- **Redis**: Caching and session management

## 🔧 Development Workflow

1. Make code changes
2. Rebuild containers: `docker-compose up --build`
3. Test functionality
4. Commit changes

## 📝 Important Notes

- This is a **containerized application**
- No local Python environment needed
- All dependencies are resolved inside containers
- Use Docker commands for everything 