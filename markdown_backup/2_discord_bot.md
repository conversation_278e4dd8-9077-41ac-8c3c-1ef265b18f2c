# 2. Discord Bot (`src/bot`)

The Discord bot is the primary user interface for interacting with the trading automation system. It allows users to ask questions, manage their watchlists, and receive alerts and analysis reports.

## Workflow

1.  **Command Reception:** A user on Discord interacts with the bot using a slash command, for example, `/ask $AAPL`.
2.  **Bot Client:** The `TradingBot` class in `client.py` receives the command. It performs rate limiting to prevent abuse.
3.  **Pipeline Execution:** The bot calls the `execute_ask_pipeline` function to initiate the AI analysis.
4.  **Response to User:** The generated response is passed back up the call stack to the `TradingBot`.
5.  The bot then sends the formatted response back to the user on Discord.

## Key Files

*   `src/bot/client.py`: The main entry point for the Discord bot. It initializes the bot, sets up event listeners, and registers the slash commands.
*   `src/bot/commands/`: This directory contains the implementation of the different slash commands.
*   `src/bot/permissions.py`: Implements the permission system for the bot, with different access levels for public, paid, and admin users.
*   `src/bot/watchlist_manager.py`: Manages user watchlists.
*   `src/bot/database_manager.py`: Manages the connection to the database.

## Detailed Breakdown

### `client.py`

*   **Purpose:** To initialize the Discord bot, set up event listeners, and register the slash commands.
*   **Framework:** `discord.py`.
*   **Key Features:**
    *   **Slash Commands:** The bot uses slash commands for all its functionality.
    *   **Rate Limiting:** A simple rate limiter is in place to prevent abuse.
    *   **Command Handling:** The `setup_commands` method registers all the slash commands, and the `handle_*_command` methods contain the logic for each command.
    *   **AI Query Analysis:** The bot uses an `AIQueryAnalyzer` to quickly determine if a query is a simple price lookup, which is a good optimization.
    *   **Error Handling:** The bot includes error handling for commands and a degraded mode in case the Discord connection fails.
    *   **Component Checker:** The `_check_required_components` method ensures that all necessary components are initialized before a command is executed.

### Command Handlers

The `handle_*_command` methods in `client.py` are responsible for handling the logic for each slash command. They typically perform the following steps:

1.  Defer the response to let the user know that the command is being processed.
2.  Check for rate limiting.
3.  Call the appropriate service or pipeline to get the requested information.
4.  Format the response as a Discord embed.
5.  Send the response back to the user.

### `permissions.py`

*   **Purpose:** To implement a permission system for the bot.
*   **Key Features:**
    *   **Permission Levels:** Defines different permission levels (e.g., `PUBLIC`, `PAID`, `ADMIN`).
    *   **Permission Checker:** The `DiscordPermissionChecker` class checks if a user has the required permission to execute a command.

### `watchlist_manager.py`

*   **Purpose:** To manage user watchlists.
*   **Key Features:**
    *   **CRUD Operations:** Provides methods for creating, reading, updating, and deleting watchlists and the symbols within them.
    *   **Database Interaction:** Interacts with the database to store and retrieve watchlist data.

### `database_manager.py`

*   **Purpose:** To manage the connection to the database.
*   **Key Features:**
    *   **Connection Pooling:** Uses a connection pool to efficiently manage database connections.
    *   **Asynchronous:** Uses `asyncpg` for asynchronous database operations.
