# Discord Bot Enhancement Tasks

## Overview
This task list is derived exclusively from the findings in `discord_bot_audit.md`. It outlines actionable steps to address weaknesses, implement needs, and follow the recommended roadmap. Tasks are prioritized by urgency and phased for execution. Focus: Expand capabilities, improve reliability, ensure scalability, and integrate with core systems. Track progress with [ ] for pending, [-] for in progress, [x] for completed.

Total Estimated Effort: 11 weeks (per implementation plan). Monitor via bot status commands and tests.

## High Priority Tasks (Immediate Fixes - 2 Weeks)
These address critical issues like reliability, security, and basic integrations to make the bot production-stable.

- [x] Fix pipeline import errors observed in tests (e.g., test_ask_pipeline.py, test_integrated_pipeline) by resolving circular imports and ensuring consistent module loading across ask/analyze/watchlist pipelines.
- [x] Add comprehensive error fallbacks in all pipelines (e.g., degraded mode for AI outages using static data from cache_manager.py; handle invalid symbols without crashes).
- [x] Sanitize all user inputs in commands (/ask, /analyze, etc.) using enhanced _sanitize_query in client.py and input_validator stages; prevent prompt injection in ai_chat_processor.py.
- [x] Enforce financial advice disclaimers in all response_formatter.py outputs for /recommendations, /ask, /analyze (add mandatory footer in embeds).
- [x] Integrate watchlist alerts with src/core/scheduler.py: Add event hooks in watchlist_manager.py to trigger notifications on price changes.
- [x] Implement circuit breakers for external APIs (Polygon/YFinance) in fetch_data stages using existing circuit_breaker.py; test with invalid responses.
- [x] Strengthen permissions in permissions.py: Add token validation for paid tiers and prevent DDoS via enhanced BotRateLimiter (e.g., per-guild limits).

## Medium Priority Tasks (Short-Term Enhancements - 4 Weeks)
Focus on expanding features, optimizing performance, and improving testing based on command/pipeline needs.

- [x] Add new commands: Implement /alerts (real-time notifications via WebSocket integration with src/core/monitoring.py), /portfolio (user-specific position tracking using database_manager.py), /batch_analyze (multi-symbol processing in analyze pipeline).
- [x] Optimize pipelines for async parallel stages (e.g., concurrent data fetching in fetch_data.py, multi-symbol execution in FlexiblePipeline); integrate Redis caching consistently across all cache_manager.py instances.
- [x] Enhance /ask: Add batch queries support in query_analyzer.py, basic voice input parsing via discord.py attachments, multi-language detection in prompt_formatter.py.
- [x] Improve /analyze: Convert synchronous data fetching to async in providers; add user-specific historical reports via conversation_memory_service.py; integrate automated scans from src/core/trade_scanner.py.
- [x] Upgrade /watchlist: Enable real-time updates via scheduler hooks, add export/import functionality (JSON/CSV via utils), AI-driven symbol suggestions using enhanced_analysis.py.
- [x] Expand /zones: Support multi-timeframe analysis with probability scoring; integrate outputs into /recommendations embeds.
- [ ] Enhance /recommendations: Add user risk profile storage in database_manager.py, basic backtesting using technical_analysis/strategy_calculator.py, enforce compliance checks pre-response.
- [ ] Develop full test suite: Expand pytest coverage for e2e user flows (e.g., full /ask execution), add load/stress tests for high-traffic scenarios using test_ask_pipeline.py as base; aim for >90% coverage.
- [ ] Update utility commands: Make /help interactive with tutorials (use discord.ui components); add /feedback command routing to metrics_collector.py for response improvement.

## Low Priority Tasks (Long-Term Improvements - 5 Weeks)
Advanced features for scalability and user experience, post-core fixes.

- [ ] Implement multi-bot sharding in client.py for high Discord traffic; use discord.py's built-in sharding with connection monitoring.
- [ ] Integrate ML for personalized recommendations in ai_routing_service.py (e.g., user history-based models via src/core/response_generator.py).
- [ ] Add UI enhancements: Interactive components (buttons/modals) in all embeds for follow-up queries; voice command support via discord.py voice extensions.
- [ ] Build analytics dashboard: Track usage metrics in monitoring/health_monitor.py; enable A/B testing for pipeline versions (e.g., alternate ai_service_wrapper configs).
- [ ] Address compliance fully: Add GDPR data privacy controls in database_manager.py (e.g., consent flags); audit all responses for regulatory risks.
- [ ] Reduce maintainability issues: Refactor duplicated stages (e.g., consolidate cache_managers into single shared module); update docs (PIPELINE.md, COMMANDS.md) with current architecture.
- [ ] Enhance monitoring: Add alerting for pipeline failures via src/core/monitoring/bot_monitor.py; integrate with external tools like Prometheus.

## Implementation Timeline
Phased rollout based on audit plan:

### Phase 1: Refactor Pipelines for Consistency (Weeks 1-2)
- [ ] Standardize stage interfaces across ask/analyze/watchlist (use base.py patterns).
- [ ] Profile performance bottlenecks (AI calls, data fetching) using metrics.py; optimize top 3.
- [ ] Containerize bot fully: Complete Dockerfile/nginx.conf in pipeline/; test with docker-compose.

### Phase 2: Expand Commands and Tests (Weeks 3-6)
- [ ] Develop and integrate new commands (/alerts, /portfolio, /batch_analyze).
- [ ] Run full test suite; fix all failing integration tests (e.g., test_integrated_pipeline).
- [ ] Implement optimizations (async, caching) and verify with load tests.

### Phase 3: Integrate with Core Modules (Weeks 7-9)
- [ ] Hook bot pipelines to src/core/scheduler.py and trade_scanner.py for proactive features.
- [ ] Add WebSocket real-time support in client.py; integrate with ingest/watchlist managers.
- [ ] Dependency injection for providers (e.g., via config_manager.py).

### Phase 4: Deploy with Monitoring (Weeks 10-11)
- [ ] Deploy updated bot with nginx/Docker; enable sharding if needed.
- [ ] Set up alerting and analytics; conduct final e2e testing.
- [ ] Document all changes; update requirements.txt with any new deps (e.g., openai if missing).

## Success Criteria
- All high-priority tasks completed with no regressions in existing commands.
- 100% test pass rate; performance improved (e.g., <5s response time for /ask).
- Bot integrated seamlessly with core; new features usable in production.
- Updated docs reflect changes; monitoring shows zero unhandled errors.

Review this list weekly; adjust based on testing outcomes.