# Trading Bot Automation Architecture

## Overview
This document outlines the technical architecture for implementing automated trading analysis, watchlist monitoring, and intelligent market scanning capabilities in the trading bot system.

## Current Implementation Status

### ✅ COMPLETED COMPONENTS (Week 1, 2 & 3)

#### Watchlist Management System
- **Location**: `src/core/watchlist/watchlist_manager.py`
- **Features**: 
  - User watchlist creation and management
  - Priority-based analysis scheduling (HIGH: 15min, MEDIUM: 1hr, LOW: 4hr)
  - Symbol tracking with notes, tags, and analysis history
  - Automatic next analysis calculation
- **Status**: Fully implemented and tested

#### Analysis Job Scheduler
- **Location**: `src/core/automation/analysis_scheduler.py`
- **Features**:
  - Priority-based job queuing (CRITICAL, HIGH, MEDIUM, LOW)
  - Async job execution with concurrent job limits
  - Job status tracking and management
  - Graceful shutdown handling
- **Status**: Core structure implemented, needs integration

#### Multi-Timeframe Analysis Engine
- **Location**: `src/shared/technical_analysis/multi_timeframe_analyzer.py`
- **Features**:
  - Timeframe selection (Quick: 1m-1h, Standard: 1m-1d, Deep: 1m-1M)
  - Multi-timeframe data aggregation
  - Trend detection across timeframes
  - Price cluster analysis
- **Status**: Fully implemented and tested

#### Enhanced Technical Indicators
- **Location**: `src/shared/technical_analysis/enhanced_indicators.py`
- **Features**:
  - Fibonacci retracements and extensions
  - Ichimoku Cloud analysis
  - Stochastic Oscillator, Williams %R, CCI
  - ATR, VWAP, momentum indicators (ROC, MFI, OBV, AD Line)
- **Status**: Fully implemented and tested

#### Volume Analysis Engine
- **Location**: `src/shared/technical_analysis/volume_analyzer.py`
- **Features**:
  - Volume profile analysis
  - Volume zone detection
  - Unusual volume detection (1.5x std deviation threshold)
  - Volume divergence analysis
  - Volume-based indicators
- **Status**: Fully implemented and tested

#### AI Analysis Depth Controller
- **Location**: `src/shared/ai/depth_controller.py`
- **Features**:
  - Analysis depth management (Quick: 5min, Standard: 15min, Deep: 30min)
  - Priority-based depth recommendations
  - Time and resource constraint management
  - Quality threshold enforcement
- **Status**: Fully implemented and tested

#### AI Model Fine-tuner
- **Location**: `src/shared/ai/model_fine_tuner.py`
- **Features**:
  - Financial domain prompt templates
  - Context optimization and market context building
  - Model configuration optimization
  - Response quality assessment
- **Status**: Fully implemented and tested

#### Multi-Source Sentiment Analyzer
- **Location**: `src/shared/sentiment/sentiment_analyzer.py`
- **Features**:
  - News sentiment analysis with keyword detection
  - Social media sentiment (Reddit, Twitter)
  - Options flow sentiment analysis
  - Market breadth sentiment indicators
  - Comprehensive sentiment aggregation
- **Status**: Fully implemented and tested

#### Automated Recommendation Engine
- **Location**: `src/shared/ai/recommendation_engine.py`
- **Features**:
  - Intelligent trading recommendation generation
  - Risk assessment integration
  - Confidence scoring and quality metrics
  - Actionable trading signals
- **Status**: Fully implemented and tested

#### Historical Performance Tracker
- **Location**: `src/shared/analytics/performance_tracker.py`
- **Features**:
  - Analysis result tracking and storage
  - Prediction accuracy measurement
  - Performance metrics calculation
  - Data export and cleanup
- **Status**: Fully implemented and tested

## Architecture Components

### 1. Data Layer

#### Redis Cache Schema
```redis
# Analysis Results Cache
analysis:{symbol}:{timeframe}:{timestamp} -> JSON analysis result
analysis:quality:{symbol}:{timestamp} -> quality score (0.0-1.0)

# Watchlist Cache
watchlist:{user_id}:{watchlist_name} -> JSON watchlist data
watchlist:symbols:{user_id}:{watchlist_name} -> Set of symbols

# Rate Limiting
rate_limit:{user_id}:{command} -> remaining requests
rate_limit:{api_provider}:{endpoint} -> remaining API calls

# Job Queue
jobs:queue -> Priority queue of analysis jobs
jobs:running -> Set of currently running job IDs
jobs:completed:{job_id} -> Job completion data
```

#### PostgreSQL Schema Extensions
```sql
-- Analysis History
CREATE TABLE analysis_history (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    analysis_type VARCHAR(50) NOT NULL,
    timeframe VARCHAR(10),
    result_data JSONB NOT NULL,
    quality_score DECIMAL(3,2),
    execution_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    INDEX idx_symbol_time (symbol, created_at),
    INDEX idx_user_time (user_id, created_at)
);

-- Watchlist Analysis Schedule
CREATE TABLE watchlist_analysis_schedule (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    watchlist_name VARCHAR(100) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    last_analysis TIMESTAMP,
    next_analysis TIMESTAMP NOT NULL,
    analysis_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, watchlist_name, symbol)
);

-- Automated Alerts
CREATE TABLE automated_alerts (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    symbol VARCHAR(10),
    condition_data JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_triggered TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 2. Core Automation Engine

#### Analysis Job Scheduler
```python
class AnalysisJobScheduler:
    """Centralized scheduler for automated analysis jobs."""
    
    def __init__(self, max_concurrent_jobs: int = 5):
        self.priority_queue = AsyncPriorityQueue()
        self.rate_limit_manager = RateLimitManager()
        self.job_executor = JobExecutor()
        self.max_concurrent_jobs = max_concurrent_jobs
        self.running_jobs = {}
        self.shutdown_event = asyncio.Event()
        
    async def schedule_job(self, job: AnalysisJob):
        """Schedule a new analysis job."""
        await self.priority_queue.put((job.priority.value, job))
        
    async def process_jobs(self):
        """Main job processing loop."""
        while not self.shutdown_event.is_set():
            if len(self.running_jobs) < self.max_concurrent_jobs:
                job = await self.priority_queue.get()
                if job:
                    await self._execute_job(job)
            await asyncio.sleep(1)
```

#### Watchlist Manager
```python
class WatchlistManager:
    """Manages user watchlists and analysis scheduling."""
    
    def __init__(self):
        self.redis_client = Redis()
        self.db_session = DatabaseSession()
        
    async def add_symbol(self, user_id: int, watchlist_name: str, 
                         symbol: str, priority: WatchlistPriority = WatchlistPriority.MEDIUM):
        """Add symbol to watchlist with priority-based scheduling."""
        entry = WatchlistEntry(
            symbol=symbol,
            priority=priority,
            added_at=datetime.now()
        )
        # Calculate next analysis based on priority
        entry.next_analysis = entry._calculate_next_analysis()
        
    def get_symbols_needing_analysis(self, user_id: int, watchlist_name: str) -> List[str]:
        """Get symbols that need analysis based on schedule."""
        watchlist = self.get_watchlist(user_id, watchlist_name)
        if not watchlist:
            return []
        
        now = datetime.now()
        return [entry.symbol for entry in watchlist.symbols 
                if entry.next_analysis and entry.next_analysis <= now]
```

### 3. Analysis Pipeline Integration

#### Multi-Timeframe Analysis
```python
class MultiTimeframeAnalyzer:
    """Analyzes symbols across multiple timeframes."""
    
    def __init__(self):
        self.data_provider = DataProviderAggregator()
        self.indicator_calculator = TechnicalIndicatorCalculator()
        self.pattern_detector = PatternDetector()
        
    async def analyze_symbol(self, symbol: str, depth: AnalysisDepth) -> TimeframeAnalysis:
        """Analyze symbol based on requested depth."""
        timeframes = self._get_timeframes_for_depth(depth)
        results = {}
        
        for timeframe in timeframes:
            data = await self.data_provider.get_ohlcv(symbol, timeframe)
            if data and len(data.closes) >= 14:  # Minimum data requirement
                indicators = self._calculate_indicators(data)
                patterns = self._detect_patterns(data)
                results[timeframe] = {
                    'indicators': indicators,
                    'patterns': patterns,
                    'trend': self._determine_trend(data),
                    'support_resistance': self._find_support_resistance(data)
                }
        
        return TimeframeAnalysis(
            symbol=symbol,
            timeframes=results,
            overall_trend=self._aggregate_trends(results),
            confidence_score=self._calculate_confidence(results)
        )
```

#### Volume Analysis Integration
```python
class VolumeAnalyzer:
    """Analyzes volume patterns and anomalies."""
    
    def analyze_volume_profile(self, volumes: List[float], prices: List[float]) -> VolumeProfile:
        """Analyze volume distribution across price levels."""
        if len(volumes) < 25:
            return VolumeProfile(error="insufficient_data")
            
        # Find volume zones
        zones = self._find_volume_zones(volumes, prices)
        
        # Detect unusual volume
        anomaly = self._detect_unusual_volume(volumes, prices)
        
        # Calculate volume indicators
        indicators = self._calculate_volume_indicators(volumes, prices)
        
        return VolumeProfile(
            zones=zones,
            anomaly=anomaly,
            indicators=indicators,
            confidence_score=self._calculate_confidence(volumes, prices)
        )
```

### 4. API Endpoints

#### Watchlist Management
```python
@router.post("/watchlist/{watchlist_name}/symbols")
async def add_symbol_to_watchlist(
    watchlist_name: str,
    symbol: str,
    priority: WatchlistPriority = WatchlistPriority.MEDIUM,
    current_user: User = Depends(get_current_user)
):
    """Add symbol to user's watchlist."""
    await watchlist_manager.add_symbol(
        user_id=current_user.id,
        watchlist_name=watchlist_name,
        symbol=symbol.upper(),
        priority=priority
    )
    return {"message": f"Added {symbol} to {watchlist_name}"}

@router.get("/watchlist/{watchlist_name}/analysis")
async def get_watchlist_analysis(
    watchlist_name: str,
    current_user: User = Depends(get_current_user)
):
    """Get analysis for all symbols in watchlist."""
    symbols = await watchlist_manager.get_symbols_needing_analysis(
        user_id=current_user.id,
        watchlist_name=watchlist_name
    )
    
    if not symbols:
        return {"message": "No symbols need analysis"}
    
    # Schedule analysis jobs
    for symbol in symbols:
        job = AnalysisJob(
            symbol=symbol,
            user_id=current_user.id,
            priority=JobPriority.MEDIUM,
            analysis_type="watchlist"
        )
        await scheduler.schedule_job(job)
    
    return {"message": f"Scheduled analysis for {len(symbols)} symbols"}
```

#### Automated Analysis
```router.post("/analyze/automated")
async def trigger_automated_analysis(
    symbols: List[str],
    depth: AnalysisDepth = AnalysisDepth.STANDARD,
    current_user: User = Depends(get_current_user)
):
    """Trigger automated analysis for multiple symbols."""
    jobs = []
    
    for symbol in symbols:
        job = AnalysisJob(
            symbol=symbol,
            user_id=current_user.id,
            priority=JobPriority.MEDIUM,
            analysis_type="automated",
            depth=depth
        )
        jobs.append(job)
        await scheduler.schedule_job(job)
    
    return {"message": f"Scheduled {len(jobs)} analysis jobs"}
```

### 5. Rate Limiting & Performance

#### Rate Limit Management
```python
class RateLimitManager:
    """Manages API rate limits and user request throttling."""
    
    def __init__(self):
        self.redis_client = Redis()
        self.api_limits = {
            'alpaca': {'calls': 200, 'window': 60},  # 200 calls per minute
            'polygon': {'calls': 100, 'window': 60},  # 100 calls per minute
            'finnhub': {'calls': 60, 'window': 60},   # 60 calls per minute
        }
        self.user_limits = {
            'free': {'requests': 10, 'window': 300},   # 10 requests per 5 minutes
            'premium': {'requests': 50, 'window': 300}, # 50 requests per 5 minutes
        }
    
    async def check_api_limit(self, provider: str) -> bool:
        """Check if API provider has available calls."""
        key = f"rate_limit:{provider}:api"
        current = await self.redis_client.get(key)
        
        if current and int(current) >= self.api_limits[provider]['calls']:
            return False
        
        await self.redis_client.incr(key)
        await self.redis_client.expire(key, self.api_limits[provider]['window'])
        return True
    
    async def check_user_limit(self, user_id: int, user_tier: str) -> bool:
        """Check if user has available requests."""
        key = f"rate_limit:{user_id}:requests"
        current = await self.redis_client.get(key)
        
        if current and int(current) >= self.user_limits[user_tier]['requests']:
            return False
        
        await self.redis_client.incr(key)
        await self.redis_client.expire(key, self.user_limits[user_tier]['window'])
        return True
```

### 6. Data Freshness & Quality

#### Data Validation
```python
class DataFreshnessValidator:
    """Validates data freshness and quality."""
    
    def __init__(self):
        self.freshness_thresholds = {
            '1m': 120,    # 2 minutes for 1-minute data
            '5m': 300,    # 5 minutes for 5-minute data
            '15m': 900,   # 15 minutes for 15-minute data
            '1h': 3600,   # 1 hour for hourly data
            '1d': 86400,  # 24 hours for daily data
        }
    
    def is_data_fresh(self, data: Dict, timeframe: str) -> bool:
        """Check if data is fresh enough for analysis."""
        if 'timestamp' not in data:
            return False
            
        age = time.time() - data['timestamp']
        threshold = self.freshness_thresholds.get(timeframe, 3600)
        
        return age <= threshold
    
    def get_data_quality_score(self, data: Dict) -> float:
        """Calculate data quality score based on completeness and consistency."""
        score = 1.0
        
        # Check required fields
        required_fields = ['open', 'high', 'low', 'close', 'volume']
        for field in required_fields:
            if field not in data or not data[field]:
                score -= 0.2
        
        # Check for data gaps
        if 'gaps' in data and data['gaps'] > 0:
            score -= min(0.3, data['gaps'] * 0.1)
        
        # Check for extreme values
        if self._has_extreme_values(data):
            score -= 0.2
        
        return max(0.0, score)
```

## Implementation Status

### ✅ Week 1: Critical Fixes - COMPLETED
- Watchlist management system fully implemented
- Analysis job scheduler core structure complete
- Pipeline timeouts and quality thresholds fixed
- Fake data fallbacks eliminated

### ✅ Week 2: Data Enhancement - COMPLETED
- Multi-timeframe analysis engine implemented
- Enhanced technical indicators complete
- Volume analysis engine fully functional
- All components tested and validated

### ✅ Week 3: AI Enhancement - COMPLETED
- AI model fine-tuning with depth control implemented
- Multi-source sentiment analysis fully functional
- Automated recommendation generation working
- Historical performance tracking complete
- All components tested and validated

### 🎯 Week 4: Advanced Features - IN PROGRESS
- Options data integration
- Advanced risk management
- Real-time market scanning
- Performance optimization

### 📋 Week 5-6: Performance & UX - PLANNED
- Parallel processing and load balancing
- Smart caching and optimization
- Enhanced user interface
- Compliance and security features

## Next Steps

1. **Complete Week 3 AI Enhancement**: Focus on AI model fine-tuning and sentiment analysis
2. **Integration Testing**: Ensure all Week 1-2 components work together seamlessly
3. **Performance Optimization**: Optimize the analysis pipeline for production use
4. **User Interface**: Enhance Discord bot responses with new analysis capabilities

## Technical Debt & Considerations

- **Placeholder Dependencies**: Some components reference placeholder classes that need implementation
- **Error Handling**: Comprehensive error handling needed for production use
- **Monitoring**: Add logging and monitoring for production deployment
- **Testing**: Expand test coverage for all new components 