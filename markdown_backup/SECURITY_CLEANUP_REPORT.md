🔒 SECURITY CLEANUP REPORT
============================================================

🚨 HARDCODED VALUES FOUND: 36
----------------------------------------

LOCALHOST: 11 occurrences
  src/api/main.py:66 -> os.getenv('FRONTEND_URL', 'http://localhost:3000'),
  src/api/main.py:67 -> os.getenv('ALLOWED_ORIGIN', 'http://localhost:3000'),
  src/core/config_manager.py:112 -> os.getenv('FRONTEND_URL', 'http://localhost:3000'),
  src/core/config_manager.py:113 -> os.getenv('ALLOWED_ORIGIN', 'http://localhost:3000')
  src/core/config_manager.py:225 -> 'url': os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
  ... and 6 more

PORTS: 13 occurrences
  src/api/main.py:66 -> os.getenv('FRONTEND_URL', 'http://localhost:3000'),
  src/api/main.py:67 -> os.getenv('ALLOWED_ORIGIN', 'http://localhost:3000'),
  src/api/main.py:68 -> os.getenv('WEBHOOK_ORIGIN', 'http://webhook-proxy:8001')
  src/core/config.py:41 -> return self._config.get('database', 'url', os.getenv('DATABASE_URL', '************************************************************/tradingview_data'))
  src/core/config.py:51 -> return self._config.get('redis', 'url', os.getenv('REDIS_URL', 'redis://:${REDIS_PASSWORD}@redis:6379/0'))
  ... and 8 more

SQLITE: 9 occurrences
  src/core/config_manager.py:216 -> 'url': os.getenv('DATABASE_URL', 'sqlite:///./local_dev.db'),
  src/database/connection.py:24 -> db_url = config.get('database', 'url', 'sqlite:///./local_dev.db')
  src/database/connection.py:30 -> fallback_path = os.getenv('SQLITE_FALLBACK_PATH', './data/local_dev.db')
  src/database/connection.py:32 -> db_url = f"sqlite:///{fallback_path}"
  src/database/connection.py:108 -> if db_url.startswith('sqlite:///'):
  ... and 4 more

REDIS_LOCALHOST: 3 occurrences
  src/core/config_manager.py:225 -> 'url': os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
  src/data/cache/manager.py:207 -> redis_url = 'redis://localhost:6379'
  src/shared/background/celery_app.py:23 -> redis_url = 'redis://localhost:6379/0'

📁 DUPLICATE FILES: 98 duplicates
----------------------------------------

main.py: 2 copies
  - src/main.py
  - src/api/main.py

__init__.py: 64 copies
  - src/__init__.py
  - src/data/__init__.py
  - src/bot/__init__.py
  - src/api/__init__.py
  - src/analysis/__init__.py
  - src/core/__init__.py
  - src/templates/__init__.py
  - src/database/__init__.py
  - src/data/providers/__init__.py
  - src/data/models/__init__.py
  - src/data/cache/__init__.py
  - src/bot/pipeline/__init__.py
  - src/bot/commands/__init__.py
  - src/bot/utils/__init__.py
  - src/bot/events/__init__.py
  - src/bot/pipeline/commands/__init__.py
  - src/bot/pipeline/monitoring/__init__.py
  - src/bot/pipeline/core/__init__.py
  - src/bot/pipeline/shared/__init__.py
  - src/bot/pipeline/commands/watchlist/__init__.py
  - src/bot/pipeline/commands/ask/__init__.py
  - src/bot/pipeline/commands/analyze/__init__.py
  - src/bot/pipeline/commands/watchlist/stages/__init__.py
  - src/bot/pipeline/commands/ask/stages/__init__.py
  - src/bot/pipeline/commands/ask/stages/utils/__init__.py
  - src/bot/pipeline/commands/ask/stages/preprocessor/__init__.py
  - src/bot/pipeline/commands/ask/stages/core/__init__.py
  - src/bot/pipeline/commands/ask/stages/postprocessor/__init__.py
  - src/bot/pipeline/commands/ask/modules/utils/__init__.py
  - src/bot/pipeline/commands/analyze/stages/__init__.py
  - src/bot/pipeline/shared/data_collectors/__init__.py
  - src/bot/pipeline/shared/validators/__init__.py
  - src/bot/pipeline/shared/formatters/__init__.py
  - src/api/data/__init__.py
  - src/api/analytics/__init__.py
  - src/api/routes/__init__.py
  - src/api/webhooks/__init__.py
  - src/api/middleware/__init__.py
  - src/api/data/providers/__init__.py
  - src/analysis/ai/__init__.py
  - src/analysis/technical/__init__.py
  - src/analysis/orchestration/__init__.py
  - src/analysis/utils/__init__.py
  - src/analysis/risk/__init__.py
  - src/analysis/fundamental/__init__.py
  - src/analysis/ai/calculators/__init__.py
  - src/analysis/technical/calculators/__init__.py
  - src/analysis/risk/calculators/__init__.py
  - src/analysis/fundamental/calculators/__init__.py
  - src/core/validation/__init__.py
  - src/core/watchlist/__init__.py
  - src/core/enums/__init__.py
  - src/core/prompts/__init__.py
  - src/core/formatting/__init__.py
  - src/core/risk_management/__init__.py
  - src/shared/background/__init__.py
  - src/shared/configuration/__init__.py
  - src/shared/market_analysis/__init__.py
  - src/shared/data_providers/__init__.py
  - src/shared/error_handling/__init__.py
  - src/shared/technical_analysis/__init__.py
  - src/shared/background/tasks/__init__.py
  - src/database/models/__init__.py
  - src/database/migrations/__init__.py

config.py: 6 copies
  - src/api/config.py
  - src/core/config.py
  - src/database/config.py
  - src/data/providers/config.py
  - src/bot/pipeline/commands/ask/config.py
  - src/bot/pipeline/commands/ask/stages/config.py

security.py: 2 copies
  - src/core/security.py
  - src/api/middleware/security.py

utils.py: 2 copies
  - src/core/utils.py
  - src/shared/market_analysis/utils.py

response_generator.py: 2 copies
  - src/core/response_generator.py
  - src/bot/pipeline/commands/ask/stages/postprocessor/response_generator.py

pipeline_engine.py: 2 copies
  - src/core/pipeline_engine.py
  - src/bot/pipeline/core/pipeline_engine.py

manager.py: 2 copies
  - src/data/providers/manager.py
  - src/data/cache/manager.py

base.py: 3 copies
  - src/data/providers/base.py
  - src/bot/pipeline/commands/ask/stages/core/base.py
  - src/api/data/providers/base.py

indicators.py: 4 copies
  - src/data/models/indicators.py
  - src/analysis/technical/indicators.py
  - src/shared/technical_analysis/indicators.py
  - src/shared/background/tasks/indicators.py

ai_cache.py: 2 copies
  - src/bot/pipeline/commands/ai_cache.py
  - src/bot/pipeline/commands/ask/stages/ai_cache.py

metrics.py: 4 copies
  - src/bot/pipeline/utils/metrics.py
  - src/api/data/metrics.py
  - src/api/routes/metrics.py
  - src/analysis/fundamental/metrics.py

ai_chat_processor.py: 2 copies
  - src/bot/pipeline/ask/stages/ai_chat_processor.py
  - src/bot/pipeline/commands/ask/stages/ai_chat_processor.py

conversation_memory_service.py: 2 copies
  - src/bot/pipeline/ask/stages/conversation_memory_service.py
  - src/bot/pipeline/commands/ask/stages/conversation_memory_service.py

ai_service_wrapper.py: 2 copies
  - src/bot/pipeline/ask/stages/ai_service_wrapper.py
  - src/bot/pipeline/commands/ask/stages/ai_service_wrapper.py

pipeline.py: 2 copies
  - src/bot/pipeline/commands/ask/pipeline.py
  - src/bot/pipeline/commands/analyze/pipeline.py

models.py: 2 copies
  - src/bot/pipeline/commands/ask/stages/models.py
  - src/core/prompts/models.py

response_templates.py: 2 copies
  - src/bot/pipeline/commands/ask/stages/response_templates.py
  - src/core/formatting/response_templates.py

cache_manager.py: 2 copies
  - src/bot/pipeline/commands/ask/stages/utils/cache_manager.py
  - src/bot/pipeline/commands/ask/modules/utils/cache_manager.py

cache.py: 2 copies
  - src/bot/pipeline/commands/ask/modules/utils/cache.py
  - src/api/data/cache.py

retry.py: 2 copies
  - src/bot/pipeline/commands/ask/modules/utils/retry.py
  - src/shared/error_handling/retry.py

validators.py: 2 copies
  - src/bot/pipeline/commands/ask/modules/utils/validators.py
  - src/shared/configuration/validators.py

technical_analysis.py: 2 copies
  - src/bot/pipeline/commands/analyze/stages/technical_analysis.py
  - src/core/formatting/technical_analysis.py

market_data.py: 3 copies
  - src/api/routes/market_data.py
  - src/api/routers/market_data.py
  - src/database/models/market_data.py

enhancement_strategy.py: 2 copies
  - src/analysis/ai/enhancement_strategy.py
  - src/analysis/orchestration/enhancement_strategy.py

recommendation_engine.py: 2 copies
  - src/analysis/ai/recommendation_engine.py
  - src/shared/ai/recommendation_engine.py

🎯 CLEANUP PRIORITIES:
----------------------------------------
1. Replace 36 hardcoded values with environment variables
2. Consolidate 26 sets of duplicate files
3. Update docker-compose.yml to use .env.secure
4. Test all services after cleanup