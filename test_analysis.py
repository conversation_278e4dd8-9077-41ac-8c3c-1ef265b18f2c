#!/usr/bin/env python3
"""
Test script for the full analysis pipeline.
Tests the complete flow from data fetching to analysis generation.
"""

import asyncio
import logging
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_full_analysis():
    """Test the complete analysis pipeline"""
    logger.info("Testing full analysis pipeline...")
    
    try:
        from src.analysis.orchestration.analysis_orchestrator import AnalysisOrchestrator
        
        # Create orchestrator
        orchestrator = AnalysisOrchestrator()
        
        # Test analysis
        analysis = await orchestrator.analyze_stock('AAPL')
        
        if analysis:
            logger.info(f"✅ Analysis successful!")
            logger.info(f"Symbol: {analysis.symbol}")
            logger.info(f"Recommendation: {analysis.recommendation}")
            logger.info(f"Confidence: {analysis.confidence}%")
            logger.info(f"Data sources: {analysis.data_sources}")
            logger.info(f"Analysis quality: {analysis.analysis_quality}")
            
            # Check data availability
            logger.info(f"Quote available: {analysis.quote is not None}")
            logger.info(f"Historical data available: {analysis.historical is not None}")
            logger.info(f"Technical indicators available: {analysis.technical is not None}")
            logger.info(f"Fundamental data available: {analysis.fundamental is not None}")
            logger.info(f"Risk assessment available: {analysis.risk is not None}")
            
            # Display some data
            if analysis.quote:
                logger.info(f"Current price: ${analysis.quote.price}")
            
            if analysis.technical:
                logger.info(f"RSI: {analysis.technical.rsi}")
                logger.info(f"MACD: {analysis.technical.macd}")
            
            if analysis.fundamental:
                logger.info(f"P/E Ratio: {analysis.fundamental.pe_ratio}")
            
            return True
        else:
            logger.error("❌ Analysis failed: No result returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Analysis error: {e}")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(test_full_analysis())
        if result:
            logger.info("✅ All tests passed!")
        else:
            logger.error("❌ Some tests failed!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1)
