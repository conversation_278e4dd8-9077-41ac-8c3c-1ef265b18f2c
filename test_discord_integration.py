#!/usr/bin/env python3
"""
Test Discord Integration

Test the Discord webhook integration with the AI automation system.
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_discord_connection():
    """Test Discord webhook connection."""
    print("🔗 Testing Discord Webhook Connection...")
    
    try:
        from src.core.automation.discord_handler import test_discord_connection
        
        # Your Discord webhook URL
        webhook_url = "https://discord.com/api/webhooks/1409753017137369088/2wUoyZFHFMs5DbAiBfqd6IvzRauFWc4cjC5AJLPD61D71qh6IXrIaVULTu_HvejxFyyZ"
        
        print(f"📡 Testing webhook: {webhook_url[:50]}...")
        
        success = await test_discord_connection(webhook_url)
        
        if success:
            print("✅ Discord webhook connection successful!")
            return True
        else:
            print("❌ Discord webhook connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Discord connection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_discord_report_delivery():
    """Test sending a report to Discord."""
    print("\n📤 Testing Discord Report Delivery...")
    
    try:
        from src.core.automation.report_engine import AIReportEngine, ReportType
        from src.core.automation.report_formatter import ReportFormatter, OutputFormat
        from src.core.automation.discord_handler import DiscordWebhookHandler
        
        # Initialize components
        engine = AIReportEngine()
        formatter = ReportFormatter()
        
        # Your Discord webhook URL
        webhook_url = "https://discord.com/api/webhooks/1409753017137369088/2wUoyZFHFMs5DbAiBfqd6IvzRauFWc4cjC5AJLPD61D71qh6IXrIaVULTu_HvejxFyyZ"
        
        # Generate a test report
        print("📊 Generating test market report...")
        report = await engine.generate_daily_market_report()
        
        if not report:
            print("❌ Failed to generate test report")
            return False
        
        # Format for Discord
        print("📱 Formatting report for Discord...")
        discord_report = formatter.format_for_discord(report)
        
        if not discord_report:
            print("❌ Failed to format report for Discord")
            return False
        
        # Send to Discord
        print("🚀 Sending report to Discord...")
        async with DiscordWebhookHandler(webhook_url) as handler:
            success = await handler.send_report(discord_report)
            
            if success:
                print("✅ Report sent to Discord successfully!")
                print(f"   - Report Type: {discord_report.metadata.get('report_type', 'unknown')}")
                print(f"   - Content Length: {len(discord_report.content)} characters")
                print(f"   - Format: {discord_report.format.value}")
                return True
            else:
                print("❌ Failed to send report to Discord")
                return False
                
    except Exception as e:
        print(f"❌ Discord report delivery test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_automated_discord_scheduler():
    """Test the automated scheduler with Discord integration."""
    print("\n⏰ Testing Automated Discord Scheduler...")
    
    try:
        from src.core.automation.report_scheduler import initialize_ai_report_scheduler, start_ai_report_scheduler, stop_ai_report_scheduler
        
        # Your Discord webhook URL
        webhook_url = "https://discord.com/api/webhooks/1409753017137369088/2wUoyZFHFMs5DbAiBfqd6IvzRauFWc4cjC5AJLPD61D71qh6IXrIaVULTu_HvejxFyyZ"
        
        # Initialize scheduler with Discord
        print("🔧 Initializing scheduler with Discord integration...")
        scheduler = initialize_ai_report_scheduler(webhook_url)
        
        if scheduler:
            print("✅ Scheduler initialized with Discord integration")
            
            # Check Discord handler
            if hasattr(scheduler, 'discord_handler') and scheduler.discord_handler:
                print("✅ Discord handler properly integrated")
            else:
                print("❌ Discord handler not properly integrated")
                return False
            
            # Test immediate job execution
            print("🚀 Testing immediate job execution with Discord...")
            success = await scheduler.run_job_now("daily_market_summary")
            
            if success:
                print("✅ Job executed and sent to Discord successfully!")
                return True
            else:
                print("❌ Job execution failed")
                return False
                
        else:
            print("❌ Failed to initialize scheduler")
            return False
            
    except Exception as e:
        print(f"❌ Automated Discord scheduler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all Discord integration tests."""
    print("🚀 Discord Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Discord Connection", test_discord_connection),
        ("Discord Report Delivery", test_discord_report_delivery),
        ("Automated Discord Scheduler", test_automated_discord_scheduler)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} test passed!")
            else:
                print(f"❌ {test_name} test failed!")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Discord integration tests passed!")
        print("\n🚀 Your AI automation system is now connected to Discord!")
        print("   - Reports will be automatically delivered to your server")
        print("   - Captain Hook will post AI-powered market analysis")
        print("   - Scheduled reports will run automatically")
        return True
    else:
        print("⚠️ Some Discord integration tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 