#!/usr/bin/env python3
"""
Test integrated enhanced analysis with existing pipeline
"""

import sys
import os
import asyncio

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_integrated_pipeline():
    """Test the integrated analyze pipeline with enhanced analysis"""
    
    print("🧪 TESTING INTEGRATED ENHANCED ANALYSIS PIPELINE")
    print("=" * 60)
    
    try:
        from src.bot.pipeline.commands.analyze.pipeline import execute_analyze_pipeline
        
        # Test with a sample ticker
        ticker = "AAPL"
        print(f"📊 Testing pipeline with ticker: {ticker}")
        
        # Execute the pipeline
        print("🚀 Executing analyze pipeline...")
        context = await execute_analyze_pipeline(
            ticker=ticker,
            user_id="test_user_123",
            guild_id="test_guild_456",
            correlation_id="test_corr_789",
            strict_mode=True
        )
        
        # Check results
        print(f"\n✅ Pipeline completed with status: {context.status}")
        
        if context.status.value == "completed":
            print("🎉 Pipeline executed successfully!")
            
            # Show enhanced analysis results
            enhanced_analysis = context.processing_results.get('enhanced_analysis')
            if enhanced_analysis:
                print("\n🚀 ENHANCED ANALYSIS RESULTS:")
                print(f"   Overall Recommendation: {enhanced_analysis.get('overall_recommendation', 'N/A')}")
                print(f"   Confidence Score: {enhanced_analysis.get('confidence_score', 0) * 100:.1f}%")
                print(f"   Risk Level: {enhanced_analysis.get('risk_level', 'N/A')}")
                
                # Show price targets
                price_targets = enhanced_analysis.get('price_targets', {})
                if price_targets:
                    print(f"\n🎯 PRICE TARGETS:")
                    print(f"   Conservative: ${price_targets.get('conservative_target', 'N/A')}")
                    print(f"   Moderate: ${price_targets.get('moderate_target', 'N/A')}")
                    print(f"   Aggressive: ${price_targets.get('aggressive_target', 'N/A')}")
                    print(f"   Stop Loss: ${price_targets.get('stop_loss', 'N/A')}")
                
                # Show probability assessment
                prob_assessment = enhanced_analysis.get('probability_assessment', {})
                if prob_assessment:
                    print(f"\n🎲 PROBABILITY ASSESSMENT:")
                    print(f"   Bullish: {prob_assessment.get('bullish_probability', 0) * 100:.1f}%")
                    print(f"   Bearish: {prob_assessment.get('bearish_probability', 0) * 100:.1f}%")
                    print(f"   Sideways: {prob_assessment.get('sideways_probability', 0) * 100:.1f}%")
                    print(f"   Confidence: {prob_assessment.get('confidence_level', 0) * 100:.1f}%")
                
                # Show timeframe confirmation
                timeframe_conf = enhanced_analysis.get('timeframe_confirmation', {})
                if timeframe_conf:
                    print(f"\n⏰ TIMEFRAME CONFIRMATION:")
                    print(f"   Agreement Score: {timeframe_conf.get('agreement_score', 0) * 100:.1f}%")
                    print(f"   Overall Confidence: {timeframe_conf.get('overall_confidence', 0) * 100:.1f}%")
                    print(f"   Recommendation: {timeframe_conf.get('recommendation', 'N/A')}")
                
                # Show key insights
                key_insights = enhanced_analysis.get('key_insights', [])
                if key_insights:
                    print(f"\n🔍 KEY INSIGHTS:")
                    for insight in key_insights[:5]:
                        print(f"   • {insight}")
            else:
                print("⚠️ No enhanced analysis results found")
            
            # Show final response
            response = context.processing_results.get('response', 'No response generated')
            print(f"\n📝 FINAL REPORT:")
            print("=" * 40)
            print(response)
            print("=" * 40)
            
        else:
            print(f"❌ Pipeline failed with status: {context.status}")
            if context.error_log:
                print("\n📋 Error Log:")
                for error in context.error_log:
                    print(f"   • {error.get('stage', 'Unknown')}: {error.get('error_message', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing integrated pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🎯 Testing Enhanced Analysis Integration")
    print("=" * 60)
    
    success = await test_integrated_pipeline()
    
    if success:
        print("\n🎉 INTEGRATION TEST COMPLETED SUCCESSFULLY!")
        print("The enhanced analysis system is now fully integrated")
        print("with your existing analyze pipeline!")
    else:
        print("\n⚠️ Integration test failed. Check the error messages above.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 