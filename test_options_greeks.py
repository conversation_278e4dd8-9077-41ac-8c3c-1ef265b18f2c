#!/usr/bin/env python3
"""
Test script for Options Greeks Calculator
"""

import sys
sys.path.append('/app')

from src.shared.technical_analysis.options_greeks_calculator import OptionsGreeksCalculator, OptionType, GreeksSnapshot

def test_options_greeks():
    """Test options Greeks calculator with synthetic data"""
    print("🚀 Testing Options Greeks Calculator")
    print("=" * 60)
    
    try:
        calculator = OptionsGreeksCalculator()
        
        # Create synthetic Greeks data for QQQ $573 call option
        current_price = 573.50
        strike_price = 573.0
        days_to_expiry = 7
        risk_free_rate = 0.05
        volatility = 0.25  # 25% annual volatility
        
        # Create a GreeksSnapshot with synthetic data
        # These values are typical for an at-the-money call option
        synthetic_greeks = GreeksSnapshot(
            delta=0.52,           # 52% chance of being in-the-money
            gamma=0.008,          # Rate of change of delta
            theta=-0.45,          # Time decay (negative = loses value over time)
            vega=0.12,            # Sensitivity to volatility changes
            rho=0.03,             # Sensitivity to interest rate changes
            price=12.50,          # Option premium
            underlying_price=current_price,
            strike=strike_price,
            time_to_expiry=days_to_expiry/365,
            implied_volatility=volatility,
            risk_free_rate=risk_free_rate
        )
        
        print(f"📊 QQQ ${strike_price:.0f} Call Option Analysis")
        print(f"📈 Current QQQ Price: ${current_price:.2f}")
        print(f"🎯 Strike Price: ${strike_price:.0f}")
        print(f"⏰ Days to Expiry: {days_to_expiry}")
        print(f"📊 Volatility: {volatility*100:.1f}%")
        print(f"💰 Current Option Price: ${synthetic_greeks.price:.2f}")
        print()
        
        print("🎢 Current Option Greeks:")
        print(f"   Delta: {synthetic_greeks.delta:.4f}")
        print(f"   Gamma: {synthetic_greeks.gamma:.4f}")
        print(f"   Theta: {synthetic_greeks.theta:.4f}")
        print(f"   Vega: {synthetic_greeks.vega:.4f}")
        print(f"   Rho: {synthetic_greeks.rho:.4f}")
        print()
        
        # Test 1: Estimate future Greeks after price movement
        print("🔮 Testing Future Greeks Estimation")
        print("-" * 40)
        
        # Simulate QQQ moving up $5
        price_change = 5.0
        time_elapsed = 1/365  # 1 day
        volatility_change = 0.01  # 1% increase in volatility
        
        future_greeks = calculator.estimate_future_greeks(
            synthetic_greeks,
            price_change,
            time_elapsed,
            volatility_change
        )
        
        print(f"📈 After QQQ moves +${price_change:.2f} in 1 day:")
        print(f"   New Delta: {future_greeks['delta']:.4f}")
        print(f"   New Gamma: {future_greeks['gamma']:.4f}")
        print(f"   New Theta: {future_greeks['theta']:.4f}")
        print(f"   New Vega: {future_greeks['vega']:.4f}")
        print(f"   New Price: ${future_greeks['price']:.2f}")
        print()
        
        # Test 2: Calculate breakeven points
        print("⚖️  Testing Breakeven Analysis")
        print("-" * 40)
        
        breakeven_info = calculator.calculate_breakeven_points(
            synthetic_greeks,
            OptionType.CALL
        )
        
        print(f"🎯 Call Option Breakeven Analysis:")
        print(f"   Breakeven Price: ${breakeven_info['breakeven_price']:.2f}")
        print(f"   Profit Zone: Above ${breakeven_info['profit_zone_start']:.2f}")
        print(f"   Loss Zone: Below ${breakeven_info['loss_zone_end']:.2f}")
        print(f"   Max Loss: ${breakeven_info['max_loss']:.2f}")
        print(f"   Max Profit: Unlimited")
        print()
        
        # Test 3: Estimate probability of profit
        print("🎲 Testing Probability Analysis")
        print("-" * 40)
        
        # Test different target prices
        target_prices = [580.0, 585.0, 590.0, 600.0]
        
        for target in target_prices:
            prob = calculator.estimate_probability_of_profit(
                synthetic_greeks,
                target,
                OptionType.CALL
            )
            print(f"   Probability of profit at ${target:.1f}: {prob*100:.1f}%")
        
        print()
        print("✅ All calculations completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Options Greeks calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_options_greeks() 