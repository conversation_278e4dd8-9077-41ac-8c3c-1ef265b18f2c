"""
Simple Audit Visualization Test

This script tests the automatic request visualization system without requiring the Discord library.
It simulates Discord interactions and verifies that they are properly visualized.
"""

import asyncio
import logging
import time
import json
from datetime import datetime
from enum import Enum
import sys
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("audit-test")

class AuditLevel(Enum):
    """Audit logging levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class SimpleAuditVisualizer:
    """
    Simple audit visualizer for testing
    
    This class simulates the request visualization system without requiring Discord.
    """
    
    def __init__(self, dev_channel_id: Optional[int] = None):
        self.dev_channel_id = dev_channel_id or 123456789
        self.enabled = True
        self.log_to_console = True
        self.log_to_channel = True
        self.include_user_data = True
        self.include_guild_data = True
        self.max_content_length = 1000
        self.visualized_commands = set()
        self.audit_records = []
        
    def register_command(self, command_name: str):
        """Register a command for visualization"""
        self.visualized_commands.add(command_name)
        logger.info(f"Registered command '{command_name}' for visualization")
        
    def register_commands(self, command_names: list):
        """Register multiple commands for visualization"""
        for command_name in command_names:
            self.register_command(command_name)
    
    def should_visualize(self, command_name: str) -> bool:
        """Check if a command should be visualized"""
        # If no specific commands are registered, visualize all
        if not self.visualized_commands:
            return True
        return command_name in self.visualized_commands
    
    async def visualize_request(self, 
                              interaction: Any,
                              command_name: str,
                              args: Dict[str, Any],
                              correlation_id: str):
        """
        Visualize a Discord command request
        
        Args:
            interaction: Mock Discord interaction
            command_name: The name of the command
            args: The command arguments
            correlation_id: Correlation ID for tracking
        """
        if not self.enabled or not self.should_visualize(command_name):
            return
        
        # Create timestamp
        timestamp = datetime.utcnow()
        
        # Create audit record
        audit_record = {
            "type": "request",
            "timestamp": timestamp.isoformat(),
            "correlation_id": correlation_id,
            "command": command_name,
            "args": self._sanitize_args(args),
            "user": {
                "id": getattr(interaction, "user_id", "unknown"),
                "name": getattr(interaction, "user_name", "Unknown User")
            }
        }
        
        # Add to audit records
        self.audit_records.append(audit_record)
        
        # Log to console
        if self.log_to_console:
            logger.info(f"[AUDIT] Request: {command_name} | User: {audit_record['user']['name']} | Args: {self._sanitize_args(args)}")
        
        # Log to channel
        if self.log_to_channel:
            logger.info(f"[CHANNEL] Sent request visualization for {command_name} to developer channel")
    
    async def visualize_response(self,
                               interaction: Any,
                               command_name: str,
                               response_data: Any,
                               execution_time: float,
                               correlation_id: str,
                               success: bool = True,
                               error: Optional[Exception] = None):
        """
        Visualize a Discord command response
        
        Args:
            interaction: Mock Discord interaction
            command_name: The name of the command
            response_data: The response data
            execution_time: The execution time in seconds
            correlation_id: Correlation ID for tracking
            success: Whether the command was successful
            error: The error if the command failed
        """
        if not self.enabled or not self.should_visualize(command_name):
            return
        
        # Create timestamp
        timestamp = datetime.utcnow()
        
        # Create audit record
        audit_record = {
            "type": "response",
            "timestamp": timestamp.isoformat(),
            "correlation_id": correlation_id,
            "command": command_name,
            "execution_time": execution_time,
            "success": success,
            "user": {
                "id": getattr(interaction, "user_id", "unknown"),
                "name": getattr(interaction, "user_name", "Unknown User")
            }
        }
        
        # Add response data
        if response_data:
            audit_record["response"] = self._truncate_content(str(response_data))
        
        # Add error information
        if error:
            audit_record["error"] = {
                "type": type(error).__name__,
                "message": str(error)
            }
        
        # Add to audit records
        self.audit_records.append(audit_record)
        
        # Log to console
        if self.log_to_console:
            status = "SUCCESS" if success else "FAILED"
            logger.info(f"[AUDIT] Response: {command_name} | Status: {status} | Time: {execution_time:.2f}s")
            if error:
                logger.error(f"[AUDIT] Error: {type(error).__name__}: {error}")
        
        # Log to channel
        if self.log_to_channel:
            logger.info(f"[CHANNEL] Sent response visualization for {command_name} to developer channel")
    
    async def visualize_pipeline(self,
                               interaction: Any,
                               command_name: str,
                               pipeline_data: Dict[str, Any],
                               correlation_id: str):
        """
        Visualize a pipeline execution
        
        Args:
            interaction: Mock Discord interaction
            command_name: The name of the command
            pipeline_data: The pipeline execution data
            correlation_id: Correlation ID for tracking
        """
        if not self.enabled or not self.should_visualize(command_name):
            return
        
        # Create timestamp
        timestamp = datetime.utcnow()
        
        # Create audit record
        audit_record = {
            "type": "pipeline",
            "timestamp": timestamp.isoformat(),
            "correlation_id": correlation_id,
            "command": command_name,
            "pipeline_data": pipeline_data,
            "user": {
                "id": getattr(interaction, "user_id", "unknown"),
                "name": getattr(interaction, "user_name", "Unknown User")
            }
        }
        
        # Add to audit records
        self.audit_records.append(audit_record)
        
        # Log to console
        if self.log_to_console:
            status = pipeline_data.get("status", "unknown")
            execution_time = pipeline_data.get("execution_time", 0)
            stages = len(pipeline_data.get("stages", []))
            errors = len(pipeline_data.get("errors", []))
            logger.info(f"[AUDIT] Pipeline: {command_name} | Status: {status} | Time: {execution_time:.2f}s | Stages: {stages} | Errors: {errors}")
        
        # Log to channel
        if self.log_to_channel:
            logger.info(f"[CHANNEL] Sent pipeline visualization for {command_name} to developer channel")
    
    async def log_audit_event(self,
                            level: AuditLevel,
                            message: str,
                            command_name: Optional[str] = None,
                            correlation_id: Optional[str] = None,
                            data: Optional[Dict[str, Any]] = None):
        """
        Log an audit event
        
        Args:
            level: The audit level
            message: The audit message
            command_name: The name of the command (optional)
            correlation_id: Correlation ID for tracking (optional)
            data: Additional data to include (optional)
        """
        if not self.enabled:
            return
        
        # Create timestamp
        timestamp = datetime.utcnow()
        
        # Create audit record
        audit_record = {
            "type": "event",
            "timestamp": timestamp.isoformat(),
            "level": level.value,
            "message": message,
            "correlation_id": correlation_id,
            "command": command_name
        }
        
        # Add additional data
        if data:
            audit_record["data"] = data
        
        # Add to audit records
        self.audit_records.append(audit_record)
        
        # Log to console
        if self.log_to_console:
            log_method = getattr(logger, level.value, logger.info)
            log_method(f"[AUDIT] {message}")
        
        # Log to channel
        if self.log_to_channel:
            logger.info(f"[CHANNEL] Sent audit event to developer channel: {message}")
    
    def _sanitize_args(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize command arguments for logging"""
        sanitized = {}
        for key, value in args.items():
            # Skip sensitive parameters
            if key.lower() in ("password", "token", "secret", "key", "auth", "credentials"):
                sanitized[key] = "***REDACTED***"
            elif isinstance(value, str) and len(value) > self.max_content_length:
                sanitized[key] = value[:self.max_content_length] + "..."
            else:
                sanitized[key] = value
        return sanitized
    
    def _truncate_content(self, content: str) -> str:
        """Truncate content to the maximum length"""
        if len(content) > self.max_content_length:
            return content[:self.max_content_length] + "..."
        return content
    
    def print_audit_report(self):
        """Print a detailed audit report"""
        print("\n" + "="*80)
        print("AUDIT VISUALIZATION REPORT")
        print("="*80)
        
        # Group records by correlation ID
        records_by_correlation = {}
        for record in self.audit_records:
            correlation_id = record.get("correlation_id", "unknown")
            if correlation_id not in records_by_correlation:
                records_by_correlation[correlation_id] = []
            records_by_correlation[correlation_id].append(record)
        
        # Print records by correlation ID
        for correlation_id, records in records_by_correlation.items():
            print(f"\nCorrelation ID: {correlation_id}")
            print("-" * 40)
            
            # Sort records by timestamp
            records.sort(key=lambda r: r.get("timestamp", ""))
            
            for record in records:
                record_type = record.get("type", "unknown")
                timestamp = record.get("timestamp", "")
                command = record.get("command", "unknown")
                
                if record_type == "request":
                    args = record.get("args", {})
                    args_str = ", ".join(f"{k}={v}" for k, v in args.items())
                    print(f"📥 REQUEST: /{command} ({args_str})")
                    print(f"   Time: {timestamp}")
                    print(f"   User: {record.get('user', {}).get('name', 'Unknown')}")
                
                elif record_type == "response":
                    success = record.get("success", True)
                    execution_time = record.get("execution_time", 0)
                    status = "✅ SUCCESS" if success else "❌ FAILED"
                    print(f"📤 RESPONSE: /{command} - {status}")
                    print(f"   Time: {timestamp}")
                    print(f"   Execution Time: {execution_time:.2f}s")
                    
                    if "response" in record:
                        print(f"   Response: {record['response']}")
                    
                    if "error" in record:
                        error = record["error"]
                        print(f"   Error: {error.get('type')}: {error.get('message')}")
                
                elif record_type == "pipeline":
                    pipeline_data = record.get("pipeline_data", {})
                    status = pipeline_data.get("status", "unknown")
                    execution_time = pipeline_data.get("execution_time", 0)
                    stages = pipeline_data.get("stages", [])
                    errors = pipeline_data.get("errors", [])
                    
                    print(f"🔄 PIPELINE: /{command} - Status: {status.upper()}")
                    print(f"   Time: {timestamp}")
                    print(f"   Execution Time: {execution_time:.2f}s")
                    print(f"   Stages: {len(stages)}")
                    
                    # Print stages
                    for i, stage in enumerate(stages):
                        stage_name = stage.get("name", "unknown")
                        stage_status = stage.get("status", "unknown")
                        stage_duration = stage.get("duration")
                        
                        # Add emoji based on status
                        status_emoji = {
                            "pending": "⏳",
                            "running": "▶️",
                            "completed": "✅",
                            "failed": "❌",
                            "skipped": "⏭️",
                            "unknown": "⏺️"
                        }.get(stage_status, "⏺️")
                        
                        # Add duration if available
                        duration_text = f" ({stage_duration:.2f}s)" if stage_duration is not None else ""
                        
                        print(f"     {status_emoji} {stage_name}{duration_text}")
                    
                    # Print errors
                    if errors:
                        print(f"   Errors: {len(errors)}")
                        for i, error in enumerate(errors):
                            error_stage = error.get("stage", "Unknown")
                            error_type = error.get("error_type", "Unknown")
                            error_message = error.get("error_message", "Unknown")
                            print(f"     {i+1}. {error_stage}: {error_type} - {error_message}")
                
                elif record_type == "event":
                    level = record.get("level", "info")
                    message = record.get("message", "")
                    
                    # Determine emoji based on level
                    emoji_map = {
                        "debug": "🔍",
                        "info": "ℹ️",
                        "warning": "⚠️",
                        "error": "❌",
                        "critical": "🚨"
                    }
                    emoji = emoji_map.get(level, "ℹ️")
                    
                    print(f"{emoji} EVENT: {level.upper()}")
                    print(f"   Time: {timestamp}")
                    print(f"   Message: {message}")
                    
                    if "data" in record:
                        data = record["data"]
                        print(f"   Data: {data}")
                
                print()
        
        print("="*80)

class MockInteraction:
    """Mock interaction for testing"""
    def __init__(self, user_id="123456789", user_name="Test User"):
        self.user_id = user_id
        self.user_name = user_name

async def test_request_visualization():
    """Test request visualization"""
    logger.info("=== Testing Request Visualization ===")
    
    # Create visualizer
    visualizer = SimpleAuditVisualizer()
    
    # Register commands
    visualizer.register_commands(["analyze", "ask", "price", "watchlist"])
    
    # Create mock interaction
    interaction = MockInteraction(user_id="123456789", user_name="Test User")
    
    # Test analyze command
    correlation_id = f"test-{int(time.time())}"
    
    # Visualize request
    await visualizer.visualize_request(
        interaction=interaction,
        command_name="analyze",
        args={
            "symbol": "AAPL",
            "indicators": "RSI,MACD",
            "target": "$180",
            "debug": True
        },
        correlation_id=correlation_id
    )
    
    # Simulate processing time
    await asyncio.sleep(1)
    
    # Visualize successful response
    await visualizer.visualize_response(
        interaction=interaction,
        command_name="analyze",
        response_data="AAPL analysis shows bullish trend with RSI at 65.4 and positive MACD.",
        execution_time=3.5,
        correlation_id=correlation_id,
        success=True
    )
    
    # Visualize pipeline
    await visualizer.visualize_pipeline(
        interaction=interaction,
        command_name="analyze",
        pipeline_data={
            "status": "completed",
            "execution_time": 3.5,
            "stages": [
                {
                    "name": "initialization",
                    "status": "completed",
                    "duration": 0.2
                },
                {
                    "name": "symbol_validation",
                    "status": "completed",
                    "duration": 0.5
                },
                {
                    "name": "market_data_retrieval",
                    "status": "completed",
                    "duration": 1.0
                },
                {
                    "name": "technical_analysis",
                    "status": "completed",
                    "duration": 1.5
                },
                {
                    "name": "response_formatting",
                    "status": "completed",
                    "duration": 0.3
                }
            ],
            "errors": []
        },
        correlation_id=correlation_id
    )
    
    # Test ask command with error
    error_correlation_id = f"error-{int(time.time())}"
    
    # Visualize request
    await visualizer.visualize_request(
        interaction=interaction,
        command_name="ask",
        args={"query": "What is the price of INVALID_SYMBOL?"},
        correlation_id=error_correlation_id
    )
    
    # Simulate processing time
    await asyncio.sleep(1)
    
    # Visualize error response
    await visualizer.visualize_response(
        interaction=interaction,
        command_name="ask",
        response_data="Failed to retrieve data for INVALID_SYMBOL",
        execution_time=2.5,
        correlation_id=error_correlation_id,
        success=False,
        error=Exception("Symbol not found: INVALID_SYMBOL")
    )
    
    # Visualize pipeline with error
    await visualizer.visualize_pipeline(
        interaction=interaction,
        command_name="ask",
        pipeline_data={
            "status": "failed",
            "execution_time": 2.5,
            "stages": [
                {
                    "name": "initialization",
                    "status": "completed",
                    "duration": 0.2
                },
                {
                    "name": "symbol_validation",
                    "status": "failed",
                    "duration": 0.5
                },
                {
                    "name": "market_data_retrieval",
                    "status": "skipped",
                    "duration": None
                },
                {
                    "name": "technical_analysis",
                    "status": "skipped",
                    "duration": None
                },
                {
                    "name": "response_formatting",
                    "status": "skipped",
                    "duration": None
                }
            ],
            "errors": [
                {
                    "stage": "symbol_validation",
                    "error_type": "SymbolNotFoundError",
                    "error_message": "Symbol not found: INVALID_SYMBOL"
                }
            ]
        },
        correlation_id=error_correlation_id
    )
    
    # Test audit events
    for level in AuditLevel:
        await visualizer.log_audit_event(
            level=level,
            message=f"Test audit event at {level.value} level",
            command_name="test",
            correlation_id=f"event-{int(time.time())}",
            data={
                "test": True,
                "level": level.value,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    # Print audit report
    visualizer.print_audit_report()
    
    logger.info("=== Request Visualization Test Completed ===")

async def main():
    """Run all tests"""
    logger.info("Starting Simple Audit Visualization Test")
    await test_request_visualization()
    logger.info("All Tests Completed")

if __name__ == "__main__":
    asyncio.run(main())
