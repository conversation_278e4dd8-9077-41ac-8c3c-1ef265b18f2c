#!/bin/bash

echo "🚀 Fast Copy - Update files in running container"
echo "==============================================="

# Check if container is running
if ! docker compose ps --services --filter "status=running" | grep -q "bot"; then
    echo "❌ Bot container is not running. Starting it..."
    docker compose up -d bot
    sleep 5
fi

echo "📁 Copying updated files to container..."

# Copy the main files we've been editing
docker cp src/bot/pipeline/commands/ask/stages/ask_sections.py tradingview-automation-bot-1:/app/src/bot/pipeline/commands/ask/stages/ask_sections.py
docker cp src/bot/pipeline/commands/ask/stages/pipeline_sections.py tradingview-automation-bot-1:/app/src/bot/pipeline/commands/ask/stages/pipeline_sections.py  
docker cp src/bot/pipeline/commands/ask/pipeline.py tradingview-automation-bot-1:/app/src/bot/pipeline/commands/ask/pipeline.py
docker cp src/bot/pipeline/commands/ask/stages/response_templates.py tradingview-automation-bot-1:/app/src/bot/pipeline/commands/ask/stages/response_templates.py

echo "🔄 Restarting bot service to reload code..."
docker compose restart bot

echo "✅ Files updated! Bot restarted."
echo "⏱️  This took ~10 seconds instead of 5+ minutes!" 