#!/usr/bin/env python3
"""
Test script for database connection.
Tests the database connection and basic functionality.
"""

import logging
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_database_connection():
    """Test database connection and basic functionality"""
    try:
        from src.database.connection import get_database_connection
        from sqlalchemy import text
        
        # Get database connection
        db_url = os.getenv('DATABASE_URL', 'sqlite:///./local_dev.db')
        logger.info(f"Attempting to connect to: {db_url.split('@')[1] if db_url else 'No URL found'}")
        
        # Test connection
        with get_database_connection() as connection:
            # Test basic query
            result = connection.execute(text("SELECT 1")).scalar()
            logger.info("✅ Successfully connected to the database!")
            logger.info(f"✅ Test query result: {result}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to connect to the database: {e}")
        return False

if __name__ == "__main__":
    try:
        result = test_database_connection()
        if result:
            logger.info("✅ Database connection test passed!")
        else:
            logger.error("❌ Database connection test failed!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1)
