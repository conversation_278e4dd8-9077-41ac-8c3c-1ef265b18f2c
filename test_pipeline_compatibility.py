#!/usr/bin/env python3
"""
Simplified test to verify the interface compatibility between 
ai_chat_processor.processor and AskPipeline._run_stage without external dependencies.
"""

import asyncio
import sys
from unittest.mock import MagicMock, patch
from typing import Dict, Any

# Mock the external dependencies to avoid import issues
mock_modules = {
    'openai': MagicMock(),
    'src.api.data.market_data_service': MagicMock(),
    'src.bot.pipeline.core.pipeline_engine': MagicMock(),
    'src.bot.pipeline.core.context_manager': MagicMock(),
}

for mod_name, mock_obj in mock_modules.items():
    sys.modules[mod_name] = mock_obj

# Now import the modules we need to test
from src.bot.pipeline.commands.ask.pipeline import AskPipeline, execute_ask_pipeline
from src.bot.pipeline.commands.ask.stages.ai_chat_processor import processor

async def test_processor_signature():
    """Test that processor function accepts context and query parameters and returns dict."""
    print("Testing processor function signature...")
    
    # Mock context object
    mock_context = MagicMock()
    test_query = "test query"
    
    try:
        # Call the processor function with the correct signature
        result = await processor(mock_context, test_query)
        
        # Check that it returns a dictionary
        assert isinstance(result, dict), f"Expected dict, got {type(result)}"
        print("✅ Processor function accepts context and query, returns dict")
        
        # Check for expected keys in the result
        expected_keys = ['response', 'data', 'intent', 'symbols', 'needs_data']
        for key in expected_keys:
            assert key in result, f"Missing expected key: {key}"
        print("✅ Result contains all expected keys")
        
    except Exception as e:
        print(f"❌ Processor function test failed: {e}")
        return False
    
    return True

async def test_pipeline_integration():
    """Test that AskPipeline properly calls the processor with context."""
    print("\nTesting pipeline integration...")
    
    # Create an async mock processor that tracks calls and returns a dict
    async def mock_processor_func(context, query):
        return {
            'response': 'test response',
            'data': {},
            'intent': 'test_intent',
            'symbols': [],
            'needs_data': False
        }
    
    # Create pipeline with mock context
    mock_context = MagicMock()
    pipeline = AskPipeline({}, mock_context)
    
    # Replace the processor in the sections with our mock function
    pipeline.sections["ai_chat_processor"]["processor"] = mock_processor_func
    
    try:
        # Run the pipeline
        result = await pipeline.run("test query")
        
        # Check that the result is a dict from the mock processor
        assert isinstance(result, dict), f"Expected dict, got {type(result)}"
        assert 'ai_chat_processor' in result, "Result should contain ai_chat_processor key"
        
        # The result should be the dict returned by the mock processor
        processor_result = result['ai_chat_processor']
        assert processor_result['response'] == 'test response'
        assert processor_result['intent'] == 'test_intent'
        
        print("✅ Pipeline correctly calls processor and returns expected result")
        print("✅ Integration test passed")
        
    except Exception as e:
        print(f"❌ Pipeline integration test failed: {e}")
        return False
    
    return True

async def main():
    """Run all compatibility tests."""
    print("Running compatibility tests for AskPipeline interface...")
    
    success = True
    
    # Test 1: Processor function signature
    if not await test_processor_signature():
        success = False
    
    # Test 2: Pipeline integration
    if not await test_pipeline_integration():
        success = False
    
    if success:
        print("\n🎉 All compatibility tests passed!")
        print("The interface between ai_chat_processor.processor and AskPipeline._run_stage is now compatible.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())