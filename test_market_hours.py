#!/usr/bin/env python3
"""
Test to check if this is a market hours issue and try different approaches.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_market_hours_issue():
    """Test if this is a market hours issue."""
    try:
        print("🕐 Testing Market Hours Issue")
        print("=" * 50)
        
        # Check current time
        now = datetime.now()
        print(f"🕐 Current time: {now}")
        print(f"📅 Day of week: {now.strftime('%A')}")
        print(f"🌍 Timezone: {now.astimezone().tzinfo}")
        
        # Check if markets are open (simplified check)
        # US markets are typically open 9:30 AM - 4:00 PM ET, Monday-Friday
        hour = now.hour
        weekday = now.weekday()  # Monday = 0, Sunday = 6
        
        if weekday < 5:  # Monday-Friday
            if 9 <= hour <= 16:  # Rough market hours
                print("✅ Markets should be open")
            else:
                print("⚠️ Markets are likely closed")
        else:
            print("⚠️ Weekend - markets are closed")
        
        print("\n🔍 Testing different symbol formats...")
        
        from src.shared.data_providers.yfinance_provider import YFinanceProvider
        
        provider = YFinanceProvider()
        
        # Test different symbol formats
        test_symbols = [
            "AAPL", "AAPL.O", "AAPL.US",  # Different formats
            "SPY", "QQQ", "IWM",          # ETFs (often more reliable)
            "MSFT", "GOOGL", "AMZN"       # Standard symbols
        ]
        
        for symbol in test_symbols:
            try:
                print(f"\n📊 Testing {symbol}...")
                data = await provider.get_ticker(symbol)
                
                if data and not data.get('error'):
                    print(f"✅ {symbol}: ${data['current_price']:.2f}")
                else:
                    print(f"❌ {symbol}: {data.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"❌ {symbol}: Exception - {e}")
        
        print("\n🎯 Testing with a simple ETF...")
        try:
            # SPY is usually very reliable
            spy_data = await provider.get_ticker("SPY")
            if spy_data and not spy_data.get('error'):
                print(f"✅ SPY working: ${spy_data['current_price']:.2f}")
            else:
                print(f"❌ SPY failed: {spy_data.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"❌ SPY exception: {e}")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    await test_market_hours_issue()
    print("\n✅ Market hours test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 