#!/usr/bin/env python3
"""
Test script for Yahoo Finance provider.
Tests the yfinance integration.
"""

import logging
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_yahoo_finance():
    """Test Yahoo Finance provider"""
    try:
        import yfinance as yf
        
        # Test basic functionality
        ticker = yf.Ticker("AAPL")
        info = ticker.info
        
        logger.info("✅ Yahoo Finance provider working")
        logger.info(f"📊 Company: {info.get('longName', 'N/A')}")
        logger.info(f"💰 Market Cap: ${info.get('marketCap', 0):,}")
        
        return True
        
    except ImportError:
        logger.error("❌ yfinance package not installed")
        return False
    except Exception as e:
        logger.error(f"❌ Yahoo Finance test failed: {e}")
        return False

if __name__ == "__main__":
    try:
        result = test_yahoo_finance()
        if result:
            logger.info("✅ All tests passed!")
        else:
            logger.error("❌ Some tests failed!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1)
