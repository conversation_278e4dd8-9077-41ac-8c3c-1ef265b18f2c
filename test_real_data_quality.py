#!/usr/bin/env python3
"""
Test script to check the quality of real market data
"""

import sys
import asyncio
import os
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.append('/app')

async def test_market_data_quality():
    """Test the quality of real market data from various providers"""
    print("🔍 Testing Real Market Data Quality")
    print("=" * 60)
    
    try:
        from src.api.data.market_data_service import MarketDataService
        from src.api.data.providers.data_source_manager import DataSourceManager
        
        # Test 1: Market Data Service
        print("\n📊 Testing Market Data Service")
        print("-" * 40)
        
        market_service = MarketDataService()
        print("✅ Market data service initialized")
        
        # Test 2: Data Source Manager
        print("\n🔗 Testing Data Source Manager")
        print("-" * 40)
        
        data_manager = DataSourceManager()
        print("✅ Data source manager initialized")
        
        # Test 3: Get current price for QQQ
        print("\n💰 Testing QQQ Current Price")
        print("-" * 40)
        
        try:
            qqq_quote = await market_service.get_current_price("QQQ")
            print(f"✅ QQQ Current Price: ${qqq_quote.price:.2f}")
            print(f"   Change: {qqq_quote.change_percent:.2f}%")
            print(f"   Volume: {qqq_quote.volume:,}")
            print(f"   Provider: {qqq_quote.provider}")
        except Exception as e:
            print(f"❌ QQQ price fetch failed: {e}")
        
        # Test 4: Get historical data
        print("\n📈 Testing Historical Data")
        print("-" * 40)
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            historical_data = await market_service.get_historical_data(
                "QQQ", start_date, end_date, 7
            )
            
            if historical_data and len(historical_data) > 0:
                print(f"✅ Got {len(historical_data)} historical data points")
                latest = historical_data[0]
                print(f"   Latest: ${latest.price:.2f} at {latest.timestamp}")
                
                # Calculate price change
                if len(historical_data) > 1:
                    oldest = historical_data[-1]
                    price_change = latest.price - oldest.price
                    price_change_pct = (price_change / oldest.price) * 100
                    print(f"   7-day change: ${price_change:.2f} ({price_change_pct:.2f}%)")
            else:
                print("⚠️  No historical data returned")
                
        except Exception as e:
            print(f"❌ Historical data fetch failed: {e}")
        
        # Test 5: Test multiple symbols
        print("\n🎯 Testing Multiple Symbols")
        print("-" * 40)
        
        symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]
        successful = 0
        
        for symbol in symbols:
            try:
                quote = await market_service.get_current_price(symbol)
                print(f"✅ {symbol}: ${quote.price:.2f} ({quote.change_percent:.2f}%)")
                successful += 1
            except Exception as e:
                print(f"❌ {symbol}: Failed - {e}")
        
        print(f"\n📊 Success Rate: {successful}/{len(symbols)} symbols")
        
        # Test 6: Data Source Manager direct access
        print("\n🔧 Testing Data Source Manager Direct Access")
        print("-" * 40)
        
        try:
            # Test current price through data manager
            qqq_data = await data_manager.fetch_current_price("QQQ")
            if qqq_data and 'current_price' in qqq_data:
                print(f"✅ Direct QQQ price: ${qqq_data['current_price']:.2f}")
                print(f"   Provider: {qqq_data.get('provider', 'Unknown')}")
                print(f"   Volume: {qqq_data.get('volume', 'N/A')}")
            else:
                print("⚠️  Direct data fetch returned incomplete data")
        except Exception as e:
            print(f"❌ Direct data fetch failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Market data quality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_technical_analysis_quality():
    """Test the quality of technical analysis calculations"""
    print("\n🧮 Testing Technical Analysis Quality")
    print("=" * 60)
    
    try:
        from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator
        from src.shared.technical_analysis.indicators import calculate_rsi, calculate_sma, calculate_macd
        
        calculator = TechnicalAnalysisCalculator()
        print("✅ Technical analysis calculator initialized")
        
        # Test with realistic price data (QQQ-like prices)
        print("\n📊 Testing with Realistic Price Data")
        print("-" * 40)
        
        import pandas as pd
        import numpy as np
        
        # Generate realistic price data (QQQ-like volatility)
        np.random.seed(42)  # For reproducible results
        base_price = 573.50
        days = 30
        
        # Generate realistic price movements
        returns = np.random.normal(0.001, 0.02, days)  # 0.1% daily return, 2% daily volatility
        prices = [base_price]
        
        for ret in returns:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        prices = pd.Series(prices)
        volumes = np.random.randint(1000000, 5000000, len(prices))
        
        print(f"✅ Generated {len(prices)} days of realistic price data")
        print(f"   Starting price: ${prices.iloc[0]:.2f}")
        print(f"   Ending price: ${prices.iloc[-1]:.2f}")
        print(f"   Total return: {((prices.iloc[-1] / prices.iloc[0]) - 1) * 100:.2f}%")
        
        # Test individual indicators
        print("\n📈 Testing Individual Indicators")
        print("-" * 40)
        
        # RSI
        rsi = calculate_rsi(prices, period=14)
        if rsi is not None:
            print(f"✅ RSI (14): {rsi:.2f}")
        else:
            print("⚠️  RSI calculation returned None")
        
        # SMA
        sma_20 = calculate_sma(prices, window=20)
        if sma_20 is not None:
            print(f"✅ SMA (20): {sma_20:.2f}")
        else:
            print("⚠️  SMA calculation returned None")
        
        # MACD
        macd_result = calculate_macd(prices, fast=12, slow=26, signal=9)
        if macd_result and all(v is not None for v in macd_result.values()):
            print(f"✅ MACD: {macd_result['macd']:.4f}")
            print(f"   Signal: {macd_result['signal']:.4f}")
            print(f"   Histogram: {macd_result['histogram']:.4f}")
        else:
            print("⚠️  MACD calculation returned None values")
        
        # Test comprehensive analysis
        print("\n🔍 Testing Comprehensive Analysis")
        print("-" * 40)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * 1.01,  # 1% higher
            'low': prices * 0.99,   # 1% lower
            'open': prices * 0.995, # 0.5% lower
            'volume': volumes
        })
        
        all_indicators = calculator.calculate_all_indicators(df, "QQQ")
        print(f"✅ Comprehensive analysis: {len(all_indicators)} indicators calculated")
        
        # Show some key indicators
        if 'rsi' in all_indicators and all_indicators['rsi'] is not None:
            print(f"   RSI: {all_indicators['rsi']:.2f}")
        if 'sma_20' in all_indicators and all_indicators['sma_20'] is not None:
            print(f"   SMA(20): {all_indicators['sma_20']:.2f}")
        if 'macd' in all_indicators and all_indicators['macd'] is not None:
            print(f"   MACD: {all_indicators['macd']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Technical analysis quality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Real Data Quality Assessment")
    print("=" * 70)
    
    results = {}
    
    # Test 1: Market data quality
    results['Market Data Quality'] = await test_market_data_quality()
    
    # Test 2: Technical analysis quality
    results['Technical Analysis Quality'] = await test_technical_analysis_quality()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 Data Quality Assessment Summary")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {test_name:<30}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All quality tests passed! Your system is ready for production.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    asyncio.run(main()) 