#!/usr/bin/env python3
"""
Test script for the unified database connection manager
"""

import asyncio
import sys
import os
import logging
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import database manager
from src.shared.database import (
    db_manager,
    get_supabase_client,
    query_data,
    insert_data,
    update_data
)

async def test_database_connections():
    """Test the unified database connection manager"""
    logger.info("Testing unified database connection manager...")
    
    # Initialize database manager
    success = await db_manager.initialize()
    if not success:
        logger.error("Failed to initialize database connections")
        return False
    
    logger.info("Database connections initialized successfully")
    
    try:
        # Test 1: Get Supabase client
        supabase = await get_supabase_client()
        if supabase:
            logger.info("Supabase client retrieved successfully")
        else:
            logger.warning("Supabase client not available")
        
        # Test 2: Insert data
        test_id = f"test-{int(asyncio.get_event_loop().time())}"
        test_webhook = {
            "webhook_id": test_id,
            "timestamp": asyncio.get_event_loop().time(),
            "client_ip": "127.0.0.1",
            "raw_data": '{"symbol":"TEST","price":100.0,"action":"BUY","test":true}',
            "status": "test"
        }
        
        logger.info(f"Inserting test webhook with ID: {test_id}")
        result = await insert_data("webhooks", test_webhook)
        
        if result:
            logger.info(f"Test webhook inserted successfully: {result}")
        else:
            logger.warning("Failed to insert test webhook")
        
        # Test 3: Query data
        logger.info(f"Querying test webhook with ID: {test_id}")
        webhooks = await query_data("webhooks", {"webhook_id": test_id})
        
        if webhooks and len(webhooks) > 0:
            logger.info(f"Found {len(webhooks)} test webhooks")
        else:
            logger.warning("No test webhooks found")
        
        # Test 4: Update data
        if webhooks and len(webhooks) > 0:
            logger.info(f"Updating test webhook with ID: {test_id}")
            updated = await update_data(
                "webhooks",
                {"webhook_id": test_id},
                {"status": "updated"}
            )
            
            if updated:
                logger.info(f"Test webhook updated successfully: {updated}")
            else:
                logger.warning("Failed to update test webhook")
        
        # Test 5: Query tickers table
        logger.info("Querying tickers table")
        tickers = await query_data("tickers", {"limit": 5})
        
        if tickers:
            logger.info(f"Found {len(tickers)} tickers")
            for ticker in tickers[:3]:  # Show first 3 tickers
                logger.info(f"Ticker: {ticker.get('symbol')}, Price: {ticker.get('last_price')}")
        else:
            logger.warning("No tickers found")
        
        # Test 6: Clean up test data
        if webhooks and len(webhooks) > 0:
            # Note: This is just for demonstration - in a real application,
            # you would implement a delete_data function in the db_manager
            supabase = await get_supabase_client()
            if supabase:
                logger.info(f"Cleaning up test webhook with ID: {test_id}")
                try:
                    # Direct client access for cleanup
                    await supabase.client.table("webhooks").delete().eq("webhook_id", test_id).execute()
                    logger.info("Test data cleaned up successfully")
                except Exception as e:
                    logger.warning(f"Failed to clean up test data: {e}")
        
        logger.info("Database connection tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Database test failed: {e}")
        return False
    finally:
        # Close database connections
        await db_manager.close()
        logger.info("Database connections closed")

if __name__ == "__main__":
    asyncio.run(test_database_connections())
