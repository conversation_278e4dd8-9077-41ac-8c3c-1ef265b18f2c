#!/usr/bin/env python3
"""
Test script for the Ask Pipeline to verify the fixes and improvements.
This script tests the pipeline with various query types to ensure proper functionality.
"""

import asyncio
import logging
import sys
import os

# Add the src directory to the path to import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging to see detailed output
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def test_pipeline():
    """Test the ask pipeline with various query types"""
    try:
        # Import the pipeline function
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        
        test_queries = [
            "What is the current price of NVDA?",
            "Show me technical analysis for TSLA",
            "How is AMD performing today?",
            "Give me a market overview",
            "This is an empty query",  # Should trigger empty query handling
            "What about my portfolio?"  # Should trigger personalization boundary
        ]
        
        logger.info("🚀 Starting Ask Pipeline Tests")
        logger.info("=" * 50)
        
        for i, query in enumerate(test_queries, 1):
            logger.info(f"🧪 Test {i}: '{query}'")
            logger.info("-" * 30)
            
            try:
                # Execute the pipeline
                context = await execute_ask_pipeline(
                    query=query,
                    user_id="test_user_123",
                    guild_id="test_guild_456",
                    correlation_id=f"test_{i}",
                    strict_mode=False
                )
                
                # Check the results
                logger.info(f"✅ Pipeline executed successfully")
                logger.info(f"📊 Status: {context.status}")
                logger.info(f"📝 Response length: {len(context.processing_results.get('response', ''))} characters")
                logger.info(f"🔍 Intent: {context.processing_results.get('intent', 'unknown')}")
                logger.info(f"📈 Symbols: {context.processing_results.get('symbols', [])}")
                
                # Log the response (first 200 chars for brevity)
                response = context.processing_results.get('response', '')
                logger.info(f"💬 Response preview: {response[:200]}...")
                
                # Check for errors
                if context.error_log:
                    logger.warning(f"⚠️ Errors: {context.error_log}")
                
                logger.info("")
                
            except Exception as e:
                logger.error(f"❌ Test {i} failed with error: {e}")
                logger.error(f"❌ Error type: {type(e).__name__}")
                import traceback
                logger.error(f"❌ Traceback: {traceback.format_exc()}")
                logger.info("")
        
        logger.info("🎯 All tests completed")
        
    except ImportError as e:
        logger.error(f"❌ Failed to import pipeline: {e}")
        logger.error("Make sure the src directory is in the Python path")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error in test setup: {e}")
        return False
    
    return True

async def test_cache_functionality():
    """Test the AI cache functionality"""
    try:
        from src.bot.pipeline.commands.ask.stages.ai_cache import get_cached_ai_response, cache_ai_response
        
        logger.info("🧪 Testing Cache Functionality")
        logger.info("-" * 30)
        
        test_query = "What is the price of AAPL?"
        test_data = {
            'response': 'Test response for caching',
            'data': {'AAPL': {'price': 150.0}},
            'intent': 'price_check',
            'symbols': ['AAPL']
        }
        
        # Test cache storage
        cache_result = await cache_ai_response(test_query, test_data)
        logger.info(f"💾 Cache storage result: {cache_result}")
        
        # Test cache retrieval
        cached_response = await get_cached_ai_response(test_query)
        if cached_response:
            logger.info(f"📦 Cache hit: {cached_response.get('response', 'No response')}")
        else:
            logger.info("📦 Cache miss (may be expected if Redis is not available)")
        
        logger.info("")
        
    except Exception as e:
        logger.error(f"❌ Cache test failed: {e}")

async def main():
    """Main test function"""
    logger.info("🤖 Starting Comprehensive Ask Pipeline Audit")
    logger.info("=" * 60)
    
    # Test the main pipeline
    pipeline_success = await test_pipeline()
    
    # Test cache functionality
    await test_cache_functionality()
    
    # Summary
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 30)
    if pipeline_success:
        logger.info("✅ Pipeline tests completed successfully")
    else:
        logger.error("❌ Pipeline tests failed")
    
    logger.info("🔍 Review the logs above for detailed results")

if __name__ == "__main__":
    asyncio.run(main())