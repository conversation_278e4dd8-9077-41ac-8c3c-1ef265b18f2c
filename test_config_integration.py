#!/usr/bin/env python3
"""
Integration test script to validate that all components use the centralized configuration.
Tests Celery, data providers, and aggregator configuration integration.
"""

import os
import sys
from pathlib import Path

# Add src to path to import modules
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_celery_config_integration():
    """Test that Celery uses centralized configuration"""
    print("🧪 Testing Celery configuration integration...")
    
    try:
        from src.shared.background.celery_app import celery_app
        from src.shared.configuration import get_config
        
        config = get_config()
        celery_config = config.get_celery_config()
        
        # Check that Celery uses the configured Redis URL
        assert celery_app.conf.broker_url == celery_config["broker_url"]
        assert celery_app.conf.result_backend == celery_config["result_backend"]
        
        # Check task time limits
        assert celery_app.conf.task_time_limit == celery_config["task_time_limit"]
        assert celery_app.conf.task_soft_time_limit == celery_config["task_soft_time_limit"]
        
        print("✅ Celery configuration integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Celery configuration integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_provider_config_integration():
    """Test that data providers use centralized configuration"""
    print("🧪 Testing data provider configuration integration...")
    
    try:
        from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
        from src.shared.data_providers.yfinance import YFinanceProvider
        from src.shared.configuration import get_config
        
        config = get_config()
        
        # Test Alpha Vantage provider
        alpha_vantage_provider = AlphaVantageProvider()
        alpha_config = config.get_data_provider_config('alpha_vantage')
        
        assert alpha_vantage_provider.api_key == alpha_config['api_key']
        assert alpha_vantage_provider.rate_limit_delay == 60.0 / alpha_config['rate_limit']
        assert alpha_vantage_provider.timeout == alpha_config['timeout']
        
        # Test Yahoo Finance provider
        yfinance_provider = YFinanceProvider()
        yfinance_config = config.get_data_provider_config('yfinance')
        
        assert yfinance_provider.rate_limit_delay == 60.0 / yfinance_config['rate_limit']
        assert yfinance_provider.timeout == yfinance_config['timeout']
        
        print("✅ Data provider configuration integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Data provider configuration integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_aggregator_config_integration():
    """Test that aggregator uses centralized configuration"""
    print("🧪 Testing aggregator configuration integration...")
    
    try:
        from src.shared.data_providers.aggregator import DataProviderAggregator
        from src.shared.configuration import get_config
        
        config = get_config()
        aggregator = DataProviderAggregator()
        
        # Check that aggregator uses the configured provider order
        assert aggregator.provider_order == config.data_provider_order
        
        # Check that providers are initialized according to configuration
        available_providers = aggregator.get_available_providers()
        enabled_providers = config.get_enabled_providers()
        
        # Both should have the same providers (intersection of order and enabled)
        assert set(available_providers) == set(enabled_providers)
        
        print("✅ Aggregator configuration integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Aggregator configuration integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_reload_functionality():
    """Test that configuration can be reloaded at runtime"""
    print("🧪 Testing configuration reload functionality...")
    
    try:
        from src.shared.configuration import get_config, config
        
        # Get initial configuration
        initial_config = get_config()
        initial_redis_url = initial_config.redis_url
        
        # Mock environment change
        import os
        original_redis_url = os.environ.get('REDIS_URL')
        os.environ['REDIS_URL'] = 'redis://localhost:9999/0'
        
        # Reload configuration
        success = config.reload_configuration()
        
        # Check that reload was successful and value changed
        assert success, "Configuration reload failed"
        assert config.redis_url == 'redis://localhost:9999/0'
        
        # Restore original environment
        if original_redis_url is not None:
            os.environ['REDIS_URL'] = original_redis_url
        else:
            del os.environ['REDIS_URL']
            
        # Reload again to restore original state
        config.reload_configuration()
        
        print("✅ Configuration reload functionality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration reload functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting configuration integration tests...")
    print("=" * 60)
    
    tests = [
        test_celery_config_integration,
        test_data_provider_config_integration,
        test_aggregator_config_integration,
        test_config_reload_functionality
    ]
    
    results = []
    for test in tests:
        results.append(test())
        print()
    
    print("=" * 60)
    if all(results):
        print("🎉 All integration tests passed! Configuration system is fully integrated.")
        sys.exit(0)
    else:
        print("💥 Some integration tests failed. Please check the configuration integration.")
        sys.exit(1)