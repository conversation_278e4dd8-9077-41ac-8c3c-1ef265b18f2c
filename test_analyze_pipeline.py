#!/usr/bin/env python3
"""
Test script for the Analyze Pipeline

This script tests the analyze pipeline by running it with sample tickers
to ensure the strict report format is working correctly.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test the pipeline function directly without Discord bot dependencies
async def test_analyze_pipeline():
    """Test the analyze pipeline with various tickers"""
    test_tickers = ["TSLA", "AAPL", "NVDA", "MSFT"]
    
    for ticker in test_tickers:
        print(f"\n{'='*60}")
        print(f"TESTING ANALYZE PIPELINE FOR: {ticker}")
        print(f"{'='*60}")
        
        try:
            # Import and test the pipeline function directly
            from src.bot.pipeline.commands.analyze.pipeline import execute_analyze_pipeline
            
            # Execute the analyze pipeline
            context = await execute_analyze_pipeline(
                ticker=ticker,
                user_id="test_user_123",
                guild_id="test_guild_456",
                strict_mode=True
            )
            
            # Check if pipeline completed successfully
            if context.status.value == "completed":
                print(f"✅ Pipeline completed successfully!")
                print(f"📊 Response length: {len(context.processing_results.get('response', ''))} characters")
                
                # Display the analysis report
                response = context.processing_results.get('response', '')
                if response:
                    print(f"\n📋 ANALYSIS REPORT:")
                    print(response)
                else:
                    print("❌ No response generated")
                    
            else:
                print(f"❌ Pipeline failed with status: {context.status}")
                if context.error_log:
                    print(f"Error: {context.error_log[-1]['error_message']}")
                    
        except ImportError as e:
            print(f"❌ Import error: {e}")
            print("This might be due to missing dependencies or circular imports")
        except Exception as e:
            print(f"❌ Unexpected error during pipeline execution: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n{'='*60}")
        print(f"COMPLETED TEST FOR: {ticker}")
        print(f"{'='*60}\n")


if __name__ == "__main__":
    print("🚀 Starting Analyze Pipeline Tests...")
    print("Note: This test runs the pipeline function directly without Discord bot dependencies")
    asyncio.run(test_analyze_pipeline())
    print("🎯 All tests completed!")