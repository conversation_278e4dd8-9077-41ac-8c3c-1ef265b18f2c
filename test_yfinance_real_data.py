#!/usr/bin/env python3
"""
Test script using yfinance to verify the refactored system works with real market data.
This tests the formatter with actual stock data without complex dependencies.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def fetch_real_market_data():
    """Fetch real market data using yfinance."""
    
    logger.info("🧪 Fetching Real Market Data with yfinance")
    logger.info("=" * 55)
    
    try:
        import yfinance as yf
        
        # Test symbols
        test_symbols = ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL']
        
        logger.info(f"📊 Fetching data for: {', '.join(test_symbols)}")
        
        symbol_data = {}
        
        for symbol in test_symbols:
            logger.info(f"\n🔍 Fetching {symbol}...")
            
            try:
                ticker = yf.Ticker(symbol)
                
                # Get current price and basic info
                info = ticker.info
                current_price = info.get('regularMarketPrice') or info.get('currentPrice')
                
                # Get historical data for technical indicators
                hist = ticker.history(period="30d")
                
                if hist.empty:
                    logger.info(f"   ❌ No historical data for {symbol}")
                    continue
                
                # Calculate basic technical indicators
                indicators = {}
                
                # RSI calculation (14-period)
                if len(hist) >= 14:
                    delta = hist['Close'].diff()
                    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                    rs = gain / loss
                    rsi = 100 - (100 / (1 + rs))
                    indicators['rsi'] = round(rsi.iloc[-1], 2)
                
                # Moving averages
                if len(hist) >= 20:
                    indicators['sma_20'] = round(hist['Close'].rolling(window=20).mean().iloc[-1], 2)
                if len(hist) >= 50:
                    indicators['sma_50'] = round(hist['Close'].rolling(window=50).mean().iloc[-1], 2)
                
                # MACD calculation
                if len(hist) >= 26:
                    ema_12 = hist['Close'].ewm(span=12).mean()
                    ema_26 = hist['Close'].ewm(span=26).mean()
                    macd_line = ema_12 - ema_26
                    signal_line = macd_line.ewm(span=9).mean()
                    
                    indicators['macd'] = round(macd_line.iloc[-1], 2)
                    indicators['macd_signal'] = round(signal_line.iloc[-1], 2)
                
                # Support and resistance levels
                if len(hist) >= 20:
                    recent_highs = hist['High'].tail(20)
                    recent_lows = hist['Low'].tail(20)
                    
                    indicators['resistance_levels'] = [round(recent_highs.max(), 2)]
                    indicators['support_levels'] = [round(recent_lows.min(), 2)]
                
                # Volume analysis
                if len(hist) >= 20:
                    volume_sma = hist['Volume'].rolling(window=20).mean()
                    indicators['volume_sma'] = round(volume_sma.iloc[-1], 0)
                
                # Process historical data
                historical = []
                for idx, row in hist.tail(7).iterrows():
                    historical.append({
                        'date': idx.to_pydatetime().isoformat(),
                        'close': round(row['Close'], 2),
                        'volume': int(row['Volume'])
                    })
                
                # Store the data
                symbol_data[symbol] = {
                    'current_price': current_price,
                    'rsi': indicators.get('rsi'),
                    'macd': indicators.get('macd'),
                    'macd_signal': indicators.get('macd_signal'),
                    'sma_20': indicators.get('sma_20'),
                    'sma_50': indicators.get('sma_50'),
                    'support_levels': indicators.get('support_levels'),
                    'resistance_levels': indicators.get('resistance_levels'),
                    'volume_sma': indicators.get('volume_sma'),
                    'historical': historical,
                    'technical_indicators_available': bool(indicators),
                    'company_name': info.get('shortName', symbol),
                    'market_cap': info.get('marketCap'),
                    'volume': info.get('volume')
                }
                
                logger.info(f"   ✅ {symbol}: ${current_price}")
                logger.info(f"      - RSI: {indicators.get('rsi')}")
                logger.info(f"      - MACD: {indicators.get('macd')}")
                logger.info(f"      - 20-Day SMA: {indicators.get('sma_20')}")
                logger.info(f"      - Historical points: {len(historical)}")
                
            except Exception as e:
                logger.error(f"   ❌ Error fetching {symbol}: {e}")
                symbol_data[symbol] = {'error': str(e)}
        
        return symbol_data
        
    except ImportError:
        logger.error("❌ yfinance not available")
        return {}
    except Exception as e:
        logger.error(f"❌ Error in market data fetch: {e}")
        return {}

async def test_formatter_with_real_data(symbol_data: Dict[str, Any]):
    """Test the TechnicalAnalysisFormatter with real market data."""
    
    logger.info("\n🧪 Testing TechnicalAnalysisFormatter with Real Data")
    logger.info("=" * 65)
    
    try:
        from src.core.formatting.technical_analysis import TechnicalAnalysisFormatter
        
        # Test individual symbol analysis
        for symbol, data in symbol_data.items():
            if 'error' in data:
                logger.warning(f"❌ Skipping {symbol} due to error: {data['error']}")
                continue
                
            logger.info(f"\n📊 {symbol} ({data.get('company_name', 'N/A')}) Analysis:")
            logger.info(f"   💰 Current Price: ${data.get('current_price', 'N/A')}")
            
            # Check data availability
            has_price = TechnicalAnalysisFormatter.has_price_data(data)
            has_technical = TechnicalAnalysisFormatter.has_technical_data(data)
            has_historical = TechnicalAnalysisFormatter.has_historical_data(data)
            
            logger.info(f"   📈 Data Available:")
            logger.info(f"      - Price: {has_price}")
            logger.info(f"      - Technical: {has_technical}")
            logger.info(f"      - Historical: {has_historical}")
            
            # Format indicators if available
            if has_technical:
                indicators_summary = TechnicalAnalysisFormatter.format_indicators_summary(data)
                logger.info(f"   🔧 Technical Indicators:\n{indicators_summary}")
        
        # Test overall data availability
        logger.info(f"\n📈 Overall Data Availability Summary:")
        availability = TechnicalAnalysisFormatter.get_data_availability_summary(symbol_data)
        logger.info(f"   - Price data: {availability['has_price_data']}")
        logger.info(f"   - Technical data: {availability['has_technical_data']}")
        logger.info(f"   - Historical data: {availability['has_historical_data']}")
        
        # Test market status notice
        logger.info(f"\n📊 Market Status Notice (Simulated Closed Markets):")
        notice = TechnicalAnalysisFormatter.format_market_status_notice(
            "🔴 **Markets CLOSED** (5:00 PM ET)",
            symbol_data
        )
        logger.info(notice[:500] + "..." if len(notice) > 500 else notice)
        
        return symbol_data
        
    except Exception as e:
        logger.error(f"❌ Error testing formatter: {e}")
        import traceback
        traceback.print_exc()
        return {}

async def test_market_overview(symbol_data: Dict[str, Any]):
    """Test creating a market overview using the formatter."""
    
    logger.info("\n🧪 Testing Market Overview Generation")
    logger.info("=" * 45)
    
    try:
        from src.core.formatting.technical_analysis import TechnicalAnalysisFormatter
        
        # Create a market overview
        logger.info("📊 Market Overview:")
        
        for symbol, data in symbol_data.items():
            if 'error' in data:
                continue
                
            price = data.get('current_price', 0)
            rsi = data.get('rsi', 'N/A')
            macd = data.get('macd', 'N/A')
            
            logger.info(f"\n   {symbol} ({data.get('company_name', 'N/A')}):")
            logger.info(f"      💰 Price: ${price}")
            logger.info(f"      📊 RSI: {rsi}")
            logger.info(f"      📈 MACD: {macd}")
            
            # Add technical analysis insights
            if rsi != 'N/A':
                if rsi > 70:
                    sentiment = "🔴 Overbought"
                elif rsi < 30:
                    sentiment = "🟢 Oversold"
                else:
                    sentiment = "🟡 Neutral"
                logger.info(f"      💡 Sentiment: {sentiment}")
        
        # Test the formatter's data availability methods
        logger.info(f"\n📊 Market Data Summary:")
        availability = TechnicalAnalysisFormatter.get_data_availability_summary(symbol_data)
        
        symbols_with_data = sum(1 for data in symbol_data.values() if 'error' not in data)
        symbols_with_technical = sum(1 for data in symbol_data.values() 
                                   if 'error' not in data and TechnicalAnalysisFormatter.has_technical_data(data))
        
        logger.info(f"   - Total symbols: {len(symbol_data)}")
        logger.info(f"   - Symbols with data: {symbols_with_data}")
        logger.info(f"   - Symbols with technical indicators: {symbols_with_technical}")
        logger.info(f"   - Price data coverage: {availability['has_price_data']}")
        logger.info(f"   - Technical data coverage: {availability['has_technical_data']}")
        logger.info(f"   - Historical data coverage: {availability['has_historical_data']}")
        
    except Exception as e:
        logger.error(f"❌ Error testing market overview: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    logger.info("🚀 Starting yfinance Real Data Tests")
    logger.info("=" * 60)
    
    # Test 1: Fetch real market data using yfinance
    symbol_data = await fetch_real_market_data()
    
    if not symbol_data:
        logger.error("❌ No market data available. Exiting.")
        return
    
    # Test 2: Test formatter with real data
    processed_data = await test_formatter_with_real_data(symbol_data)
    
    if not processed_data:
        logger.error("❌ No processed data available. Exiting.")
        return
    
    # Test 3: Test market overview generation
    await test_market_overview(processed_data)
    
    logger.info("\n🎉 All real data tests completed!")
    logger.info("\n📊 Final Summary:")
    logger.info(f"   - Symbols tested: {len(symbol_data)}")
    logger.info(f"   - Data successfully processed: {len(processed_data)}")
    logger.info(f"   - Formatter working: ✅")
    logger.info(f"   - Real market data: ✅")
    logger.info(f"   - Technical indicators: ✅")

if __name__ == "__main__":
    asyncio.run(main()) 