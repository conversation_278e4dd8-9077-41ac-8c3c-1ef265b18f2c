#!/usr/bin/env python3
"""
Enhanced Analysis Demo

Demonstrates the new analysis engines:
- Price Target Engine
- Probability Engine  
- Timeframe Confirmation Analyzer
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def generate_sample_data():
    """Generate realistic sample market data for demonstration"""
    
    # Create date ranges for different timeframes
    end_date = datetime.now()
    
    # Daily data (100 days)
    dates_1d = pd.date_range(end=end_date, periods=100, freq='D')
    
    # 4-hour data (200 periods)
    dates_4h = pd.date_range(end=end_date, periods=200, freq='4h')
    
    # 1-hour data (500 periods)
    dates_1h = pd.date_range(end=end_date, periods=500, freq='h')
    
    # Generate realistic price data with trend and volatility
    np.random.seed(42)  # For reproducible results
    
    def generate_price_data(dates, base_price=100, trend_strength=0.1, volatility=0.02):
        """Generate realistic price data"""
        n_periods = len(dates)
        
        # Create trend
        trend = np.linspace(0, n_periods * trend_strength, n_periods)
        
        # Add random walk
        random_walk = np.cumsum(np.random.normal(0, volatility, n_periods))
        
        # Combine trend and random walk
        prices = base_price + trend + random_walk
        
        # Generate OHLC data
        data = []
        for i, price in enumerate(prices):
            # Create realistic OHLC from close price
            daily_range = price * volatility * 2
            high = price + abs(np.random.normal(0, daily_range * 0.3))
            low = price - abs(np.random.normal(0, daily_range * 0.3))
            open_price = price + np.random.normal(0, daily_range * 0.2)
            
            data.append({
                'open': max(open_price, low),
                'high': max(high, open_price, price),
                'low': min(low, open_price, price),
                'close': price,
                'volume': np.random.randint(100000, 2000000)
            })
        
        return pd.DataFrame(data, index=dates)
    
    # Generate data for each timeframe
    price_data = {
        '1d': generate_price_data(dates_1d, base_price=100, trend_strength=0.15, volatility=0.025),
        '4h': generate_price_data(dates_4h, base_price=100, trend_strength=0.12, volatility=0.02),
        '1h': generate_price_data(dates_1h, base_price=100, trend_strength=0.08, volatility=0.015)
    }
    
    # Generate volume data
    volume_data = {}
    for timeframe, data in price_data.items():
        volume_data[timeframe] = pd.DataFrame({
            'volume': data['volume']
        }, index=data.index)
    
    return price_data, volume_data

def demo_price_target_engine():
    """Demonstrate the price target engine"""
    
    print("🎯 PRICE TARGET ENGINE DEMO")
    print("=" * 50)
    
    try:
        from src.analysis.technical.price_targets import PriceTargetEngine
        
        # Generate sample data
        price_data, volume_data = generate_sample_data()
        
        # Create engine
        engine = PriceTargetEngine()
        
        # Calculate price targets for daily timeframe
        print("📊 Calculating price targets for daily timeframe...")
        targets = engine.calculate_targets('AAPL', '1d', price_data['1d'], volume_data['1d'])
        
        print(f"✅ Price targets calculated successfully!")
        print(f"   Symbol: {targets.symbol}")
        print(f"   Timeframe: {targets.timeframe}")
        print(f"   Current Price: ${targets.current_price:.2f}")
        print(f"   Conservative Target: ${targets.conservative_target:.2f}")
        print(f"   Moderate Target: ${targets.moderate_target:.2f}")
        print(f"   Aggressive Target: ${targets.aggressive_target:.2f}")
        print(f"   Stop Loss: ${targets.stop_loss:.2f}")
        print(f"   Trend Direction: {targets.trend_direction.value}")
        print(f"   Trend Strength: {targets.trend_strength:.2%}")
        print(f"   Volatility: {targets.volatility:.2%}")
        
        print(f"\n📈 Key Levels:")
        print(f"   Support Levels: {[f'${level:.2f}' for level in targets.support_levels[:3]]}")
        print(f"   Resistance Levels: {[f'${level:.2f}' for level in targets.resistance_levels[:3]]}")
        
        print(f"\n🔍 Confidence Factors:")
        for factor in targets.confidence_factors[:3]:
            print(f"   • {factor}")
        
        print(f"\n📊 Fibonacci Levels: {len(targets.fibonacci_levels)} levels calculated")
        
    except Exception as e:
        print(f"❌ Error in price target demo: {e}")
        import traceback
        traceback.print_exc()

def demo_probability_engine():
    """Demonstrate the probability engine"""
    
    print("\n🎲 PROBABILITY ENGINE DEMO")
    print("=" * 50)
    
    try:
        from src.analysis.probability.probability_engine import ProbabilityEngine
        
        # Generate sample data
        price_data, volume_data = generate_sample_data()
        
        # Create engine
        engine = ProbabilityEngine()
        
        # Assess probabilities
        print("📊 Assessing probabilities for daily timeframe...")
        assessment = engine.assess_probabilities(
            'AAPL', '1d', price_data['1d'], volume_data['1d'], sentiment_score=0.3
        )
        
        print(f"✅ Probability assessment completed!")
        print(f"   Symbol: {assessment.symbol}")
        print(f"   Timeframe: {assessment.timeframe}")
        print(f"   Bullish Probability: {assessment.bullish_probability:.1%}")
        print(f"   Bearish Probability: {assessment.bearish_probability:.1%}")
        print(f"   Sideways Probability: {assessment.sideways_probability:.1%}")
        print(f"   Confidence Level: {assessment.confidence_level:.1%}")
        print(f"   Market Regime: {assessment.market_regime.value}")
        print(f"   Regime Confidence: {assessment.regime_confidence:.1%}")
        print(f"   Risk-Adjusted Return: {assessment.risk_adjusted_return:.4f}")
        print(f"   Historical Accuracy: {assessment.historical_accuracy:.1%}")
        
        print(f"\n✅ Supporting Factors:")
        for factor in assessment.supporting_factors[:3]:
            print(f"   • {factor}")
        
        print(f"\n⚠️ Risk Factors:")
        for factor in assessment.risk_factors[:3]:
            print(f"   • {factor}")
        
    except Exception as e:
        print(f"❌ Error in probability demo: {e}")
        import traceback
        traceback.print_exc()

def demo_timeframe_confirmation():
    """Demonstrate the timeframe confirmation analyzer"""
    
    print("\n⏰ TIMEFRAME CONFIRMATION DEMO")
    print("=" * 50)
    
    try:
        from src.analysis.technical.timeframe_confirmation import TimeframeConfirmationAnalyzer
        
        # Generate sample data
        price_data, volume_data = generate_sample_data()
        
        # Create analyzer
        analyzer = TimeframeConfirmationAnalyzer()
        
        # Analyze timeframe agreement
        print("📊 Analyzing timeframe agreement across multiple timeframes...")
        confirmation = analyzer.get_timeframe_agreement('AAPL', price_data, volume_data)
        
        print(f"✅ Timeframe confirmation analysis completed!")
        print(f"   Symbol: {confirmation.symbol}")
        print(f"   Short-term Bias: {confirmation.short_term_bias.value}")
        print(f"   Medium-term Bias: {confirmation.medium_term_bias.value}")
        print(f"   Long-term Bias: {confirmation.long_term_bias.value}")
        print(f"   Agreement Score: {confirmation.agreement_score:.1%}")
        print(f"   Overall Confidence: {confirmation.overall_confidence:.1%}")
        print(f"   Recommendation: {confirmation.recommendation}")
        
        print(f"\n📊 Weighted Probabilities:")
        for direction, prob in confirmation.weighted_probability.items():
            print(f"   {direction.title()}: {prob:.1%}")
        
        print(f"\n⚠️ Conflicting Signals:")
        if confirmation.conflicting_signals:
            for signal in confirmation.conflicting_signals[:3]:
                print(f"   • {signal}")
        else:
            print("   • No conflicts detected")
        
        print(f"\n🔍 Timeframe Analysis:")
        for timeframe, analysis in confirmation.timeframe_analysis.items():
            print(f"   {timeframe}: {analysis.bias.value} (confidence: {analysis.confidence:.1%})")
        
    except Exception as e:
        print(f"❌ Error in timeframe confirmation demo: {e}")
        import traceback
        traceback.print_exc()

def demo_integration():
    """Demonstrate integration of all engines"""
    
    print("\n🚀 INTEGRATED ANALYSIS DEMO")
    print("=" * 50)
    
    try:
        from src.bot.pipeline.commands.analyze.stages.enhanced_analysis import EnhancedAnalysisStage
        
        # Generate sample data
        price_data, volume_data = generate_sample_data()
        
        # Create enhanced analysis stage
        stage = EnhancedAnalysisStage()
        
        # Create analysis context
        context = {
            'symbol': 'AAPL',
            'price_data': price_data,
            'volume_data': volume_data,
            'sentiment_score': 0.25
        }
        
        print("📊 Running integrated enhanced analysis...")
        
        # Execute enhanced analysis
        updated_context = stage.execute(context)
        
        if 'enhanced_analysis' in updated_context:
            result = updated_context['enhanced_analysis']
            
            print(f"✅ Integrated analysis completed!")
            print(f"   Overall Recommendation: {result.overall_recommendation}")
            print(f"   Confidence Score: {result.confidence_score:.1%}")
            print(f"   Risk Level: {result.risk_level}")
            
            print(f"\n🔍 Key Insights:")
            for insight in result.key_insights[:5]:
                print(f"   • {insight}")
            
            print(f"\n📊 Analysis Summary:")
            print(f"   Price Targets: ${result.price_targets.moderate_target:.2f}")
            print(f"   Bullish Probability: {result.probability_assessment.bullish_probability:.1%}")
            print(f"   Timeframe Agreement: {result.timeframe_confirmation.agreement_score:.1%}")
            
        else:
            print("⚠️ Enhanced analysis not available in context")
            
    except Exception as e:
        print(f"❌ Error in integration demo: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main demonstration function"""
    
    print("🎉 ENHANCED ANALYSIS ENGINE DEMONSTRATION")
    print("=" * 60)
    print("This demo showcases the new analysis engines:")
    print("• Price Target Engine - Fibonacci, volume profile, trend analysis")
    print("• Probability Engine - Statistical models, risk assessment, regime detection")
    print("• Timeframe Confirmation - Multi-timeframe agreement and signal validation")
    print("=" * 60)
    
    # Run individual demos
    demo_price_target_engine()
    demo_probability_engine()
    demo_timeframe_confirmation()
    
    # Run integration demo
    demo_integration()
    
    print("\n🎯 DEMO COMPLETED!")
    print("=" * 60)
    print("The enhanced analysis engines are now ready for integration")
    print("into your existing trading bot pipeline.")
    print("\nNext steps:")
    print("1. Integrate with your /analyze command")
    print("2. Add to your analysis pipeline stages")
    print("3. Test with real market data")
    print("4. Customize parameters for your trading strategy")

if __name__ == "__main__":
    main() 