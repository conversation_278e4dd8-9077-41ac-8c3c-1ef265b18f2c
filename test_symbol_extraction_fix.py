#!/usr/bin/env python3
"""
Test script to verify the symbol extraction fix.
This tests that the problematic query "What is the price of $AAPL?" 
only extracts AAPL and not 26 invalid symbols.
"""

import re
from typing import List

# Mock the exceptions for testing
class InvalidSymbolFormatError(Exception):
    pass

def validate_symbol(symbol: str) -> str:
    """
    Validate and normalize a stock symbol.
    """
    if not symbol or not isinstance(symbol, str):
        raise InvalidSymbolFormatError(str(symbol))
    
    # Clean and normalize
    symbol = symbol.strip().upper()
    
    # Remove common suffixes and prefixes
    symbol = re.sub(r'\.[A-Z]+$', '', symbol)  # Remove exchange suffix
    symbol = re.sub(r'^[A-Z]+:', '', symbol)   # Remove prefix
    
    # Validate format: only letters and numbers, 1-5 characters
    if not re.match(r'^[A-Z0-9]{1,5}$', symbol):
        raise InvalidSymbolFormatError(symbol)
    
    # Check for common invalid patterns
    invalid_patterns = [
        r'^TEST$',
        r'^DEMO$',
        r'^FAKE$',
        r'^\d+$',  # Only numbers
    ]
    
    for pattern in invalid_patterns:
        if re.match(pattern, symbol, re.IGNORECASE):
            raise InvalidSymbolFormatError(symbol)
    
    return symbol

def extract_symbols_from_text_fixed(text: str) -> List[str]:
    """
    Extract potential stock symbols from text - FIXED VERSION.
    """
    if not text:
        return []
    
    # Common patterns for stock symbols - REMOVED the problematic r'\b[A-Z]{1,5}\b' pattern
    patterns = [
        r'\$[A-Z]{1,5}\b',  # $AAPL format
        r'\b[A-Z]{1,5}\.[A-Z]{1,4}\b',  # AAPL.NASDAQ format
    ]
    
    symbols = []
    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            # Remove $ prefix if present
            symbol = match.lstrip('$')
            try:
                validated = validate_symbol(symbol)
                if validated not in symbols:
                    symbols.append(validated)
            except InvalidSymbolFormatError:
                continue
    
    return symbols

def extract_symbols_from_text_original(text: str) -> List[str]:
    """
    Extract potential stock symbols from text - ORIGINAL PROBLEMATIC VERSION.
    """
    if not text:
        return []
    
    # Common patterns for stock symbols - INCLUDES the problematic pattern
    patterns = [
        r'\$[A-Z]{1,5}\b',  # $AAPL format
        r'\b[A-Z]{1,5}\b',   # AAPL format - THIS IS THE PROBLEM
        r'\b[A-Z]{1,5}\.[A-Z]{1,4}\b',  # AAPL.NASDAQ format
    ]
    
    symbols = []
    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            # Remove $ prefix if present
            symbol = match.lstrip('$')
            try:
                validated = validate_symbol(symbol)
                if validated not in symbols:
                    symbols.append(validated)
            except InvalidSymbolFormatError:
                continue
    
    return symbols

def test_symbol_extraction():
    """Test the symbol extraction fix."""
    test_queries = [
        "What is the price of $AAPL?",
        "What is the price of AAPL?",
        "Tell me about AAPL and MSFT stocks",
        "I want to know about $TSLA and $NVDA performance",
        "What are the top TECH stocks to buy?"
    ]

    print("Testing symbol extraction fix...")
    print()

    all_tests_passed = True

    for i, test_query in enumerate(test_queries, 1):
        print(f"Test {i}: '{test_query}'")

        # Test original (problematic) version
        original_symbols = extract_symbols_from_text_original(test_query)
        print(f"  Original: {len(original_symbols)} symbols: {original_symbols}")

        # Test fixed version
        fixed_symbols = extract_symbols_from_text_fixed(test_query)
        print(f"  Fixed:    {len(fixed_symbols)} symbols: {fixed_symbols}")

        # Check if fixed version has fewer false positives
        if len(fixed_symbols) <= len(original_symbols):
            print(f"  ✅ Fixed version has same or fewer symbols")
        else:
            print(f"  ❌ Fixed version has MORE symbols - unexpected!")
            all_tests_passed = False
        print()

    return all_tests_passed

if __name__ == "__main__":
    success = test_symbol_extraction()
    exit(0 if success else 1)
