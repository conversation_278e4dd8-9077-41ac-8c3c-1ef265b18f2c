#!/usr/bin/env python3
"""
Test script to simulate the actual pipeline execution that the Discord bot uses.
This will test the real /ask command functionality.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_real_pipeline_execution():
    """Test the real pipeline execution that the bot uses"""
    print("🤖 Testing Real Pipeline Execution (Bot Simulation)")
    print("=" * 60)
    
    try:
        # Import the pipeline function that the bot actually uses
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        
        # Test query (same as what a user would type)
        test_query = "What is the current price of AAPL?"
        user_id = "12345"  # Simulated Discord user ID
        guild_id = "67890"  # Simulated Discord guild ID
        
        print(f"📝 Test Query: {test_query}")
        print(f"👤 User ID: {user_id}")
        print(f"🏠 Guild ID: {guild_id}")
        print("-" * 50)
        
        print("🚀 Executing pipeline (same as bot would do)...")
        
        # Execute the pipeline (this is exactly what the bot does)
        result = await execute_ask_pipeline(
            query=test_query,
            user_id=user_id,
            guild_id=guild_id
        )
        
        print(f"\n📊 Pipeline Status: {result.status.value}")
        
        if result.status.value == "completed":
            print("✅ Pipeline completed successfully!")
            
            # Check what the bot would see
            available_keys = list(result.processing_results.keys())
            print(f"📋 Available keys: {available_keys}")
            
            # Check for the response (this is what the bot looks for)
            response = result.processing_results.get("response")
            if response:
                print(f"\n🎯 RESPONSE FOUND (Bot will send this):")
                print(f"📤 Length: {len(response)} characters")
                print(f"💬 Content: {response[:300]}...")
                
                # Simulate what the bot would do
                print(f"\n🤖 Bot would send this response to Discord user {user_id}")
                
            else:
                print("\n❌ No response found - Bot would show error message")
                
        else:
            print(f"❌ Pipeline failed with status: {result.status.value}")
            if hasattr(result, 'error_log') and result.error_log:
                print(f"Error details: {result.error_log}")
        
        print("\n✅ Real pipeline execution test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_real_pipeline_execution()) 