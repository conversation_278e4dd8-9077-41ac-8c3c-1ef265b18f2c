#!/usr/bin/env python3
"""
Test script to validate pipeline optimization improvements.

This test validates:
1. Parallel execution optimization
2. Dependency analysis and batching
3. Performance improvements
4. Pipeline section optimization
5. AI service wrapper optimization
"""

import sys
import os
import asyncio
import time
import traceback
from typing import Dict, Any, List

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_pipeline_optimizer():
    """Test the new pipeline optimizer"""
    print("🧪 Testing Pipeline Optimizer...")
    
    try:
        from src.bot.pipeline.core.pipeline_optimizer import (
            PipelineOptimizer, 
            OptimizationStrategy,
            ExecutionMetrics,
            get_pipeline_optimizer
        )
        
        optimizer = PipelineOptimizer()
        
        # Test 1: Dependency analysis
        print("  ✅ Test 1: Dependency analysis")
        mock_stages = {
            "query_analysis": {"dependencies": []},
            "data_collection": {"dependencies": ["query_analysis"]},
            "response_generation": {"dependencies": ["query_analysis", "data_collection"]},
            "response_formatting": {"dependencies": ["response_generation"]}
        }
        
        dependency_graph = optimizer.analyze_dependencies(mock_stages)
        print(f"     Dependency graph: {dependency_graph}")
        
        # Test 2: Optimization plan generation
        print("  ✅ Test 2: Optimization plan generation")
        plan = optimizer.generate_optimization_plan(mock_stages, OptimizationStrategy.PARALLEL_BATCHES)
        print(f"     Strategy: {plan.strategy.value}")
        print(f"     Execution batches: {plan.execution_batches}")
        print(f"     Estimated time: {plan.estimated_time:.2f}s")
        print(f"     Parallelization factor: {plan.parallelization_factor:.2f}")
        print(f"     Critical path: {plan.critical_path}")
        
        # Test 3: Different optimization strategies
        print("  ✅ Test 3: Different optimization strategies")
        strategies = [OptimizationStrategy.SEQUENTIAL, OptimizationStrategy.PARALLEL_BATCHES, OptimizationStrategy.ADAPTIVE]
        for strategy in strategies:
            plan = optimizer.generate_optimization_plan(mock_stages, strategy)
            batch_count = len(plan.execution_batches)
            max_parallel = max(len(batch) for batch in plan.execution_batches) if plan.execution_batches else 0
            print(f"     {strategy.value}: {batch_count} batches, max {max_parallel} parallel")
        
        # Test 4: Performance metrics recording
        print("  ✅ Test 4: Performance metrics recording")
        metrics = ExecutionMetrics(
            total_time=15.5,
            stage_times={"query_analysis": 2.0, "data_collection": 8.0, "response_generation": 4.5, "response_formatting": 1.0},
            parallel_efficiency=0.85,
            bottleneck_stages=["data_collection"]
        )
        optimizer.record_execution_metrics("test_pipeline", metrics)
        
        summary = optimizer.get_optimization_summary("test_pipeline")
        print(f"     Optimization summary: {summary}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

async def test_pipeline_sections_optimization():
    """Test the optimized pipeline sections manager"""
    print("🧪 Testing Pipeline Sections Optimization...")

    try:
        from src.bot.pipeline.commands.ask.stages.pipeline_sections import (
            PipelineSectionManager,
            PipelineSection,
            SectionStatus
        )
        
        manager = PipelineSectionManager()
        
        # Test 1: Create mock sections with dependencies
        print("  ✅ Test 1: Creating mock sections with dependencies")
        
        async def mock_processor(context, results):
            await asyncio.sleep(0.1)  # Simulate processing time
            return {"processed": True, "timestamp": time.time()}
        
        async def mock_quality_checker(output_data, context):
            return {"quality_score": 0.9, "issues": []}
        
        sections = [
            PipelineSection(
                section_id="query_analysis",
                name="Query Analysis",
                description="Analyze user query",
                dependencies=[],
                processor=mock_processor,
                quality_checker=mock_quality_checker
            ),
            PipelineSection(
                section_id="data_collection",
                name="Data Collection",
                description="Collect market data",
                dependencies=["query_analysis"],
                processor=mock_processor,
                quality_checker=mock_quality_checker
            ),
            PipelineSection(
                section_id="response_generation",
                name="Response Generation",
                description="Generate AI response",
                dependencies=["query_analysis", "data_collection"],
                processor=mock_processor,
                quality_checker=mock_quality_checker
            ),
            PipelineSection(
                section_id="response_formatting",
                name="Response Formatting",
                description="Format final response",
                dependencies=["response_generation"],
                processor=mock_processor,
                quality_checker=mock_quality_checker
            )
        ]
        
        for section in sections:
            manager.add_section(section)
        
        manager.set_execution_order(["query_analysis", "data_collection", "response_generation", "response_formatting"])
        
        # Test 2: Execute optimized pipeline
        print("  ✅ Test 2: Executing optimized pipeline")
        
        start_time = time.time()
        context = {"test_mode": True}
        results = await manager.execute_pipeline(context)
        execution_time = time.time() - start_time
        
        print(f"     Execution time: {execution_time:.2f}s")
        print(f"     Results: {len(results)} sections")
        
        # Check results
        for section_id, result in results.items():
            print(f"     {section_id}: {result.status.value} ({result.execution_time:.3f}s)")
        
        # Test 3: Verify parallel execution benefits
        print("  ✅ Test 3: Verifying parallel execution benefits")
        
        # Count completed sections
        completed = sum(1 for result in results.values() if result.status == SectionStatus.COMPLETED)
        print(f"     Completed sections: {completed}/{len(sections)}")
        
        # Check if execution was faster than sequential
        sequential_estimate = len(sections) * 0.1  # Each section takes ~0.1s
        if execution_time < sequential_estimate * 0.8:  # At least 20% improvement
            print(f"     ✅ Parallel execution benefit: {((sequential_estimate - execution_time) / sequential_estimate * 100):.1f}% faster")
        else:
            print(f"     ⚠️ Limited parallel benefit: {execution_time:.2f}s vs {sequential_estimate:.2f}s estimated")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

async def test_ai_service_optimization():
    """Test the optimized AI service wrapper"""
    print("🧪 Testing AI Service Optimization...")
    
    try:
        from src.shared.ai_services.ai_service_wrapper import AIChatProcessor
        
        # Test 1: Create processor
        print("  ✅ Test 1: Creating AI processor")
        config = {"test_mode": True}
        processor = AIChatProcessor(config)
        
        # Test 2: Process query with parallel optimization
        print("  ✅ Test 2: Processing query with parallel optimization")
        
        start_time = time.time()
        result = await processor.process(
            "What is the price of AAPL?",
            user_id="test_user",
            guild_id="test_guild"
        )
        processing_time = time.time() - start_time
        
        print(f"     Processing time: {processing_time:.2f}s")
        print(f"     Result type: {result.get('response_type', 'unknown')}")
        print(f"     Intent: {result.get('intent', 'unknown')}")
        
        # Check if processing time is recorded
        if 'processing_time' in result:
            print(f"     ✅ Processing time recorded: {result['processing_time']:.3f}s")
        else:
            print("     ⚠️ Processing time not recorded")
        
        # Test 3: Test parallel task execution
        print("  ✅ Test 3: Testing parallel task execution")
        
        # Process multiple queries to test parallel efficiency
        queries = [
            "What is AAPL doing?",
            "How is MSFT performing?",
            "Tell me about GOOGL"
        ]
        
        start_time = time.time()
        tasks = [processor.process(query, "test_user", "test_guild") for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        successful_results = [r for r in results if not isinstance(r, Exception)]
        print(f"     Processed {len(successful_results)}/{len(queries)} queries in {total_time:.2f}s")
        print(f"     Average time per query: {total_time / len(queries):.2f}s")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

async def test_dependency_optimization():
    """Test dependency analysis and optimization"""
    print("🧪 Testing Dependency Optimization...")

    try:
        from src.bot.pipeline.core.pipeline_optimizer import PipelineOptimizer
        
        optimizer = PipelineOptimizer()
        
        # Test 1: Simple linear dependencies
        print("  ✅ Test 1: Linear dependencies")
        linear_stages = {
            "stage1": {"dependencies": []},
            "stage2": {"dependencies": ["stage1"]},
            "stage3": {"dependencies": ["stage2"]},
            "stage4": {"dependencies": ["stage3"]}
        }
        
        plan = optimizer.generate_optimization_plan(linear_stages)
        print(f"     Linear plan: {plan.execution_batches}")
        print(f"     Expected: 4 batches of 1 stage each")
        
        # Test 2: Parallel-friendly dependencies
        print("  ✅ Test 2: Parallel-friendly dependencies")
        parallel_stages = {
            "init": {"dependencies": []},
            "fetch_data": {"dependencies": ["init"]},
            "fetch_news": {"dependencies": ["init"]},
            "fetch_analysis": {"dependencies": ["init"]},
            "combine": {"dependencies": ["fetch_data", "fetch_news", "fetch_analysis"]},
            "format": {"dependencies": ["combine"]}
        }
        
        plan = optimizer.generate_optimization_plan(parallel_stages)
        print(f"     Parallel plan: {plan.execution_batches}")
        print(f"     Expected: ~4 batches with parallel fetch operations")
        
        # Test 3: Complex dependencies
        print("  ✅ Test 3: Complex dependencies")
        complex_stages = {
            "a": {"dependencies": []},
            "b": {"dependencies": ["a"]},
            "c": {"dependencies": ["a"]},
            "d": {"dependencies": ["b", "c"]},
            "e": {"dependencies": ["c"]},
            "f": {"dependencies": ["d", "e"]}
        }
        
        plan = optimizer.generate_optimization_plan(complex_stages)
        print(f"     Complex plan: {plan.execution_batches}")
        print(f"     Critical path: {plan.critical_path}")
        print(f"     Parallelization factor: {plan.parallelization_factor:.2f}")
        
        # Test 4: Circular dependency detection
        print("  ✅ Test 4: Circular dependency handling")
        circular_stages = {
            "a": {"dependencies": ["c"]},
            "b": {"dependencies": ["a"]},
            "c": {"dependencies": ["b"]}
        }
        
        plan = optimizer.generate_optimization_plan(circular_stages)
        print(f"     Circular plan: {plan.execution_batches}")
        print(f"     Notes: {plan.optimization_notes}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        print(f"  ❌ Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Run all optimization tests"""
    print("🔧 Testing Pipeline Optimization and Dependency Management\n")
    
    tests = [
        test_pipeline_optimizer,
        test_pipeline_sections_optimization,
        test_ai_service_optimization,
        test_dependency_optimization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if asyncio.iscoroutinefunction(test):
                result = await test()
            else:
                result = test()
            
            if result:
                passed += 1
                print("✅ PASSED\n")
            else:
                print("❌ FAILED\n")
        except Exception as e:
            print(f"❌ FAILED with exception: {e}\n")
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Pipeline optimization improvements are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
