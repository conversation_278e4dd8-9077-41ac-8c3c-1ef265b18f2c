#!/usr/bin/env python3
"""
Test script for technical indicators.
Tests the technical analysis calculations with real market data.
"""

import asyncio
import logging
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_technical_indicators_with_real_data():
    """Test technical indicators calculation with real market data"""
    try:
        from src.analysis.technical.indicators import TechnicalIndicatorsCalculator
        
        # Create calculator instance
        calculator = TechnicalIndicatorsCalculator()
        
        # Test symbols
        test_symbols = ['AAPL', 'TSLA', 'MSFT', 'GOOGL']
        
        logger.info("🧪 Testing Technical Indicators with Real Market Data")
        logger.info("=" * 60)
        
        for i, symbol in enumerate(test_symbols):
            logger.info(f"\n🔍 Testing {symbol}:")
        
            # Add delay between requests to respect rate limits
            if i > 0:
                logger.info(f"      ⏳ Waiting 2 seconds before next request...")
                await asyncio.sleep(2)
            
            try:
                # Fetch real market data using Polygon API (which works in Docker)
                try:
                    from src.api.data.providers.data_source_manager import DataSourceManager
                    
                    # Initialize data source manager
                    manager = DataSourceManager()
                    
                    # Get current price and basic info
                    current_data = await manager.fetch_current_price(symbol)
                    if current_data and 'price' in current_data:
                        logger.info(f"      💰 Current price: ${current_data['price']}")
                    else:
                        logger.warning(f"      ⚠️ No current price available")
                    
                    # Get historical data
                    hist_data = await manager.fetch_historical_data(symbol, days=30)
                    if hist_data and len(hist_data) > 0:
                        logger.info(f"      📅 Successfully fetched {len(hist_data)} days of historical data")
                        # Convert to pandas-like format for compatibility
                        import pandas as pd
                        hist = pd.DataFrame(hist_data)
                        if 'close' in hist.columns:
                            hist['Close'] = hist['close']
                        elif 'price' in hist.columns:
                            hist['Close'] = hist['price']
                    else:
                        logger.warning(f"      ⚠️ No historical data available for {symbol}")
                        continue
                        
                except Exception as e:
                    logger.error(f"      ❌ Failed to fetch data for {symbol}: {e}")
                    continue
                
                # Extract closing prices
                prices = hist['Close'].tolist()
                logger.info(f"   📊 Fetched {len(prices)} days of price data")
                
                # Test RSI calculation
                rsi = calculator.calculate_rsi(prices, 14)
                if rsi is not None:
                    logger.info(f"   ✅ RSI(14): {rsi:.2f}")
                    
                    # Add RSI interpretation
                    if rsi > 70:
                        interpretation = "🔴 Overbought"
                    elif rsi < 30:
                        interpretation = "🟢 Oversold"
                    else:
                        interpretation = "🟡 Neutral"
                    logger.info(f"      💡 Interpretation: {interpretation}")
                else:
                    logger.warning(f"   ⚠️ RSI calculation failed for {symbol}")
                
                # Test MACD calculation
                macd_result = calculator.calculate_macd(prices)
                if macd_result:
                    macd_line = macd_result['macd_line']
                    signal_line = macd_result['signal_line']
                    histogram = macd_result['histogram']
                    
                    logger.info(f"   ✅ MACD: {macd_line:.4f}")
                    logger.info(f"      📈 Signal: {signal_line:.4f}")
                    logger.info(f"      📊 Histogram: {histogram:.4f}")
                    
                    # Add MACD interpretation
                    if macd_line > signal_line:
                        macd_signal = "🟢 Bullish (MACD above signal)"
                    else:
                        macd_signal = "🔴 Bearish (MACD below signal)"
                    logger.info(f"      💡 Signal: {macd_signal}")
                else:
                    logger.warning(f"   ⚠️ MACD calculation failed for {symbol}")
                
                # Test Moving Averages
                moving_averages = calculator.calculate_moving_averages(prices, [20, 50])
                for period, value in moving_averages.items():
                    if value is not None:
                        current_price = prices[-1]
                        logger.info(f"   ✅ {period.upper()}: ${value:.2f}")
                        
                        # Add moving average interpretation
                        if current_price > value:
                            ma_signal = "🟢 Price above MA (Bullish)"
                        else:
                            ma_signal = "🔴 Price below MA (Bearish)"
                        logger.info(f"      💡 {ma_signal}")
                    else:
                        logger.warning(f"   ⚠️ {period.upper()} calculation failed (insufficient data)")
                
                # Test Bollinger Bands calculation
                try:
                    bollinger_result = calculator.calculate_bollinger_bands(prices, 20, 2)
                    if bollinger_result:
                        upper_band = bollinger_result['upper_band']
                        lower_band = bollinger_result['lower_band']
                        middle_band = bollinger_result['middle_band']
                        
                        logger.info(f"   ✅ Bollinger Bands (20,2):")
                        logger.info(f"      📈 Upper: ${upper_band:.2f}")
                        logger.info(f"      📊 Middle: ${middle_band:.2f}")
                        logger.info(f"      📉 Lower: ${lower_band:.2f}")
                        
                        # Add Bollinger Bands interpretation
                        current_price = prices[-1]
                        if current_price > upper_band:
                            bb_signal = "🔴 Price above upper band (Overbought)"
                        elif current_price < lower_band:
                            bb_signal = "🟢 Price below lower band (Oversold)"
                        else:
                            bb_signal = "🟡 Price within bands (Normal)"
                        logger.info(f"      💡 {bb_signal}")
                    else:
                        logger.warning(f"   ⚠️ Bollinger Bands calculation failed")
                except AttributeError:
                    logger.info(f"   ℹ️ Bollinger Bands method not available in this version")
                
                # Test Stochastic Oscillator
                try:
                    stoch_result = calculator.calculate_stochastic_oscillator(prices, 14, 3)
                    if stoch_result:
                        k_percent = stoch_result['k_percent']
                        d_percent = stoch_result['d_percent']
                        
                        logger.info(f"   ✅ Stochastic Oscillator (14,3):")
                        logger.info(f"      📊 %K: {k_percent:.2f}")
                        logger.info(f"      📈 %D: {d_percent:.2f}")
                        
                        # Add Stochastic interpretation
                        if k_percent > 80 and d_percent > 80:
                            stoch_signal = "🔴 Overbought"
                        elif k_percent < 20 and d_percent < 20:
                            stoch_signal = "🟢 Oversold"
                        else:
                            stoch_signal = "🟡 Neutral"
                        logger.info(f"      💡 Signal: {stoch_signal}")
        else:
                        logger.warning(f"   ⚠️ Stochastic calculation failed")
                except AttributeError:
                    logger.info(f"   ℹ️ Stochastic Oscillator method not available in this version")
                
                logger.info(f"   ✅ {symbol} analysis completed successfully")
                
            except Exception as e:
                logger.error(f"   ❌ Error testing {symbol}: {e}")
                continue
        
        logger.info("\n🎉 All technical indicator tests completed!")
            return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Technical indicators test failed: {e}")
        return False

def test_technical_indicators():
    """Test technical indicators calculation with real data"""
    return asyncio.run(test_technical_indicators_with_real_data())

if __name__ == "__main__":
    try:
        result = test_technical_indicators()
        if result:
            logger.info("✅ All tests passed!")
    else:
            logger.error("❌ Some tests failed!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1) 