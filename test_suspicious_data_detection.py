#!/usr/bin/env python3
"""
Test the updated suspicious data detection system.
This verifies that the bot can detect fallback data sources and prevent false recommendations.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from datetime import datetime, timedelta
from src.bot.pipeline.commands.ask.stages.response_templates import ResponseTemplateEngine, ResponseStyle

def test_suspicious_data_detection():
    """Test that suspicious data patterns are properly detected"""
    print("🔄 Testing Suspicious Data Detection System\n")
    
    engine = ResponseTemplateEngine()
    
    # Test 1: Normal data (should work normally)
    print("📊 Test 1: Normal Data (Real-time)")
    print("=" * 50)
    
    normal_data = {
        'symbol': 'GME',
        'current_price': 22.89,
        'change': 1.06,
        'change_percent': 1.06,
        'volume': 5551442,
        'high': 23.15,
        'low': 22.45,
        'open': 22.50,
        'close': 22.89,
        'timestamp': datetime.now().isoformat(),
        'provider': 'polygon'
    }
    
    validated_normal = engine._validate_data_freshness(normal_data)
    print(f"✅ Data suspicious: {validated_normal.get('data_suspicious', False)}")
    print(f"✅ Data stale: {validated_normal.get('data_stale', False)}")
    print(f"✅ Prices unreliable: {validated_normal.get('prices_unreliable', False)}")
    
    # Test 2: Suspicious data (yfinance fallback pattern)
    print("\n📊 Test 2: Suspicious Data (yfinance Fallback Pattern)")
    print("=" * 50)
    
    suspicious_data = {
        'symbol': 'GME',
        'current_price': 22.89,
        'change': 1.06,
        'change_percent': 1.06,
        'volume': 5551442,
        'high': 0,  # Suspicious: high is 0
        'low': 0,   # Suspicious: low is 0
        'open': 0,  # Suspicious: open is 0
        'close': 22.89,  # Suspicious: close equals current_price
        'timestamp': datetime.now().isoformat(),
        'provider': 'yfinance'  # Suspicious: fallback provider
    }
    
    validated_suspicious = engine._validate_data_freshness(suspicious_data)
    print(f"🚨 Data suspicious: {validated_suspicious.get('data_suspicious', False)}")
    print(f"🚨 Suspicious reasons: {validated_suspicious.get('suspicious_reasons', [])}")
    print(f"🚨 Data stale: {validated_suspicious.get('data_stale', False)}")
    print(f"🚨 Prices unreliable: {validated_suspicious.get('prices_unreliable', False)}")
    
    # Test 3: Generate response with suspicious data
    print("\n📊 Test 3: Response Generation with Suspicious Data")
    print("=" * 50)
    
    try:
        response = engine.generate_response(
            'stock_analysis',
            ResponseStyle.DETAILED,
            validated_suspicious,
            {'query': 'what is GME doing?'}
        )
        
        print("✅ Response generated successfully")
        print(f"📊 Response length: {len(response)} characters")
        print(f"🚨 Contains suspicious warning: {'fallback source' in response.lower() or 'suspicious' in response.lower()}")
        print(f"🚨 Contains HOLD recommendation: {'HOLD' in response.upper()}")
        print(f"🚨 Contains critical warning: {'CRITICAL' in response.upper() or 'DO NOT' in response.upper()}")
        
        # Show the warning section
        if '🚨 **CRITICAL WARNING:**' in response:
            warning_start = response.find('🚨 **CRITICAL WARNING:**')
            warning_end = response.find('\n\n', warning_start)
            if warning_end == -1:
                warning_end = len(response)
            warning_section = response[warning_start:warning_end]
            print(f"\n🚨 Warning Section:\n{warning_section}")
        
    except Exception as e:
        print(f"❌ Response generation failed: {e}")
    
    # Test 4: Test recommendation generation
    print("\n📊 Test 4: Recommendation Generation with Suspicious Data")
    print("=" * 50)
    
    try:
        recommendation = engine._generate_recommendation(validated_suspicious)
        print(f"✅ Action: {recommendation.action}")
        print(f"✅ Confidence: {recommendation.confidence}%")
        print(f"✅ Risk Level: {recommendation.risk_level}")
        print(f"✅ Reasoning: {recommendation.reasoning}")
        
        # Verify it forces HOLD for suspicious data
        if recommendation.action == "HOLD":
            print("✅ SUCCESS: Suspicious data correctly forced HOLD recommendation")
        else:
            print("❌ FAILURE: Suspicious data should have forced HOLD recommendation")
            
    except Exception as e:
        print(f"❌ Recommendation generation failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Suspicious Data Detection Test Results:")
    
    if validated_suspicious.get('data_suspicious', False):
        print("✅ Suspicious data patterns correctly detected")
    else:
        print("❌ Suspicious data patterns not detected")
    
    if validated_suspicious.get('prices_unreliable', False):
        print("✅ Prices correctly marked as unreliable")
    else:
        print("❌ Prices not marked as unreliable")
    
    print("\n🎉 The system now properly detects fallback data sources and prevents false recommendations!")

if __name__ == "__main__":
    test_suspicious_data_detection() 