#!/usr/bin/env python3
"""
Comprehensive automation testing script
Tests all bot components, integrations, and automation features
"""

import asyncio
import sys
import os
import time
import logging
from unittest.mock import Mock, AsyncMock

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestRunner:
    """Test runner for automation features"""
    
    def __init__(self):
        self.results = []
        
    async def run_test(self, test_name, test_func):
        """Run a single test and record results"""
        try:
            start_time = time.time()
            result = await test_func()
            duration = time.time() - start_time
            
            self.results.append({
                'name': test_name,
                'passed': result,
                'duration': duration,
                'error': None
            })
            
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name} ({duration:.2f}s)")
            return result
            
        except Exception as e:
            self.results.append({
                'name': test_name,
                'passed': False,
                'duration': time.time() - start_time,
                'error': str(e)
            })
            print(f"❌ FAIL {test_name} - {e}")
            return False

    async def test_rate_limiter(self):
        """Test rate limiting functionality"""
        from src.bot.utils.rate_limiter import ThreadSafeRateLimiter
        
        limiter = ThreadSafeRateLimiter(max_requests=5, time_window=60)
        user_id = "test_user_123"
        
        # Test basic functionality
        results = []
        for i in range(6):
            can_request = await limiter.can_make_request(user_id)
            if can_request:
                await limiter.record_request(user_id)
            results.append(can_request)
        
        # Should allow 5, then deny 1
        expected = [True, True, True, True, True, False]
        return results == expected

    async def test_error_handler(self):
        """Test error handling"""
        try:
            from src.bot.utils.error_handler import ErrorHandler, ErrorCategory
            
            # Test error categorization
            test_error = ValueError("Test error")
            error_details = ErrorHandler.log_error(
                error=test_error,
                category=ErrorCategory.VALIDATION,
                context={"test": "context"},
                user_id="test_user"
            )
            
            return (error_details['error_type'] == 'ValueError' and 
                    error_details['category'] == ErrorCategory.VALIDATION)
        except ImportError:
            # Skip Discord-dependent tests
            return True

    async def test_watchlist_manager(self):
        """Test watchlist manager functionality"""
        try:
            from src.bot.watchlist_manager import WatchlistManager
            print("✓ WatchlistManager class imported successfully")
            return True
        except Exception as e:
            print(f"✗ WatchlistManager import failed: {e}")
            return False

    async def test_performance_optimizer(self):
        """Test performance optimizer functionality"""
        from src.bot.pipeline.performance_optimizer import initialize_pipeline_optimizer
        
        optimizer = initialize_pipeline_optimizer()
        
        # Test basic functionality
        has_cache = hasattr(optimizer, 'cache')
        has_metrics = hasattr(optimizer, 'metrics')
        has_optimize = hasattr(optimizer, 'optimize_query')
        
        return has_cache and has_metrics and has_optimize

    async def test_health_monitor(self):
        """Test health monitoring"""
        from src.bot.monitoring.health_monitor import HealthChecker, HealthStatus
        
        checker = HealthChecker()
        
        # Test system resource checking
        metrics = await checker.check_system_resources()
        
        has_cpu = 'cpu_usage' in metrics
        has_memory = 'memory_usage' in metrics
        has_disk = 'disk_usage' in metrics
        
        return has_cpu and has_memory and has_disk

    async def test_security_manager(self):
        """Test security manager"""
        from src.bot.security.advanced_security import AdvancedSecurityManager, InputValidator
        
        # Test input validation
        valid, sanitized, error = InputValidator.validate_user_input("AAPL analysis")
        
        # Test symbol validation
        valid_symbol, error = InputValidator.validate_symbol("AAPL")
        
        return valid and valid_symbol

    async def test_intelligent_cache(self):
        """Test intelligent caching"""
        from src.bot.pipeline.performance_optimizer import IntelligentCache
        
        cache = IntelligentCache(max_size=5, default_ttl=1)
        
        # Test basic cache operations
        await cache.set("test_query", "test_result")
        result = await cache.get("test_query")
        
        return result == "test_result"

    async def test_cache_performance(self):
        """Test cache performance benefits"""
        from src.bot.pipeline.performance_optimizer import IntelligentCache
        
        cache = IntelligentCache(max_size=100, default_ttl=300)
        
        # Test cache hit/miss performance
        start_time = time.time()
        
        # Set some data
        await cache.set("test1", "data1")
        await cache.set("test2", "data2")
        
        # Retrieve data
        result1 = await cache.get("test1")
        result2 = await cache.get("test2")
        
        duration = time.time() - start_time
        
        return (result1 == "data1" and result2 == "data2" and duration < 1.0)

    async def test_folder_structure(self):
        """Test folder structure is organized correctly"""
        required_dirs = [
            'src/bot',
            'src/bot/utils',
            'src/bot/pipeline',
            'src/bot/enhancements',
            'src/bot/monitoring',
            'src/bot/security',
            'tests',
            'config',
            'docs'
        ]
        
        base_path = os.path.dirname(__file__)
        all_exist = True
        
        for dir_path in required_dirs:
            full_path = os.path.join(base_path, dir_path)
            if not os.path.exists(full_path):
                print(f"Missing directory: {dir_path}")
                all_exist = False
        
        return all_exist

    async def run_all_tests(self):
        """Run all automation tests"""
        print("🚀 Starting comprehensive automation testing...")
        print("=" * 60)
        
        tests = [
            ("Rate Limiter", self.test_rate_limiter),
            ("Error Handler", self.test_error_handler),
            ("Watchlist Manager", self.test_watchlist_manager),
            ("Performance Optimizer", self.test_performance_optimizer),
            ("Health Monitor", self.test_health_monitor),
            ("Security Manager", self.test_security_manager),
            ("Intelligent Cache", self.test_intelligent_cache),
            ("Cache Performance", self.test_cache_performance),
            ("Folder Structure", self.test_folder_structure),
        ]
        
        for test_name, test_func in tests:
            await self.run_test(test_name, test_func)
        
        print("=" * 60)
        
        # Summary
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r['passed'])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 Test Results Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.results:
                if not result['passed']:
                    print(f"   - {result['name']}: {result['error'] or 'Test failed'}")
        
        # Performance metrics
        total_duration = sum(r['duration'] for r in self.results)
        print(f"\n⏱️  Total Test Duration: {total_duration:.2f}s")
        
        return passed_tests == total_tests

async def main():
    """Main test runner"""
    runner = TestRunner()
    success = await runner.run_all_tests()
    
    if success:
        print("\n🎉 All automation tests passed! Your bot is ready for production.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
